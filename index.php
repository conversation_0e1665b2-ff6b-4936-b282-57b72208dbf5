<?php
// [ 应用入口文件 ]
if($_SERVER['REQUEST_METHOD'] == 'OPTIONS'){
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
    header('Access-Control-Allow-Methods: GET, POST, PUT,DELETE,OPTIONS,PATCH');
    file_put_contents('option.txt',json_encode($_REQUEST));
    exit;
}
header('Access-Control-Allow-Origin:*');
header('Access-Control-Allow-Methods:*');
header('Access-Control-Allow-Headers:content-type,token,id');
header("Access-Control-Request-Headers: Origin, X-Requested-With, content-Type, Accept, Authorization");// 否允许发送Cookie
header('Access-Control-Allow-Credentials:true');
header("Access-Control-Allow-Headers: X-Requested-With, Content-Type, Accept, Apikey");

// 定义应用目录
define('APP_PATH', __DIR__ . '/application/');

//配置文件路径 
define('CONF_PATH', __DIR__ . '/config/');

define('NOW_TIME', time());

// 加载框架引导文件
require __DIR__ . '/thinkphp/start.php';

