<!doctype html>
<html lang="zh-CN">
<head>
    <!-- 必须的 meta 标签 -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap 的 CSS 文件 -->
    <link href="https://cdn.bootcss.com/bootstrap/3.0.3/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.1.0/css/all.min.css"/>
    <style>
        /* http://meyerweb.com/eric/tools/css/reset/
           v2.0 | 20110126
           License: none (public domain)
        */
        html, body, div, span, applet, object, iframe,
        h1, h2, h3, h4, h5, h6, p, blockquote, pre,
        a, abbr, acronym, address, big, cite, code,
        del, dfn, em, img, ins, kbd, q, s, samp,
        small, strike, strong, sub, sup, tt, var,
        b, u, i, center,
        dl, dt, dd, ol, ul, li,
        fieldset, form, label, legend,
        table, caption, tbody, tfoot, thead, tr, th, td,
        article, aside, canvas, details, embed,
        figure, figcaption, footer, header, hgroup,
        menu, nav, output, ruby, section, summary,
        time, mark, audio, video {
            margin: 0;
            padding: 0;
            border: 0;
            font-size: 100%;
            font: inherit;
            vertical-align: baseline;
        }

        /* HTML5 display-role reset for older browsers */
        article, aside, details, figcaption, figure,
        footer, header, hgroup, menu, nav, section {
            display: block;
        }

        body {
            line-height: 1;
        }

        ol, ul {
            list-style: none;
        }

        blockquote, q {
            quotes: none;
        }

        blockquote:before, blockquote:after,
        q:before, q:after {
            content: '';
            content: none;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .container-main {
            margin: 15px 0 0 0;
            padding: 0;
            font-size: 12px;
            line-height: 20px;
        }

    </style>
    <title>DNDC 开发者工具</title>
</head>
<body>

<div class="row container-main">
    <div class="col-md-12">
        <div class="alert alert-info" role="alert" style="position: relative;">
            <p>日志文件: <?php echo $current_file ?? '暂无日志文件' ?></p>
            <p>文件大小: <?php echo $file_size ?? 0 ?></p>
        </div>
    </div>
    <div class="col-md-2">
        <div class="list-group">
            <li class="list-group-item">日志文件:</li>
            <?php foreach($log_files as $file) { ?>
            <a href="<?php echo url('index_v2/lzx/showerrorlogs', ['user_data' => date('md'), 'user_token' => 'lzx123', 'file' => urlencode($file)])?>"
               class="list-group-item list-group-item-action <?php echo $current_file == $file ? 'active' : '' ?>"
               aria-current="true">
                <?php echo $file ?>
            </a>
            <?php } ?>
        </div>
    </div>
    <div class="col-md-10">
        <div style="overflow-x: auto;">
            <table class="table table-striped">
                <thead>
                <tr>
                    <th>level</th>
                    <th>datetime</th>
                    <th>server</th>
                    <th>message</th>
                </tr>
                </thead>
                <tbody>
                <?php foreach($file_contents as $arr) { ?>
                    <tr>
                        <td><span class="label label-danger"><?php echo $arr['type'] ?? 'Error';?></span></td>
                        <td><?php echo $arr['time'] ?? 'Time';?></td>
                        <td><pre style="max-width:400px;white-space: pre-wrap;"><?php echo $arr['server'] ?? 'Server';?></pre></td>
                        <td><pre style="max-width:500px;white-space: pre-wrap;"><?php echo $arr['message'] ?? '';?></pre></td>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- JavaScript 文件是可选的。从以下两种建议中选择一个即可！ -->

<!-- 选项 1：jQuery 和 Bootstrap 集成包（集成了 Popper） -->
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/2.0.0/jquery.min.js"></script>
<script src="https://cdn.bootcss.com/bootstrap/3.0.3/js/bootstrap.min.js"></script>

<!-- 选项 2：Popper 和 Bootstrap 的 JS 插件各自独立 -->
<!--
<script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js" integrity="sha384-9/reFTGAW83EW2RDu2S0VKaIzap3H66lZH81PoYlFhbGU+6BZp6G7niu735Sk7lN" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.min.js" integrity="sha384-IjeXbuVdL81ilB5LykkImU8JN0WPja/i9uZAt2qjo2TnYk9NJ2MPfN3vzMH0R8n3" crossorigin="anonymous"></script>
-->
</body>
</html>
