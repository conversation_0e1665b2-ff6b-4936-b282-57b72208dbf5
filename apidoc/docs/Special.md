## 限时活动组件

```js
{
    name: "限时活动",  //组件名称
        type: "cLimit",   //组件标识
        attribute: {
        goods_data: [    //活动商品
            goods_name: "ROYDX莱德斯时尚水纹直身杯B"  //商品名称
        id: 3619   //商品id
        image_path: "http://wx-dealer.oss-cn-                     shenzhen.aliyuncs.com/commodity/20201030/xxxx.jpg"  //商品展示图片地址
        one: "" //一级分类  
        original_price: "278.00"  //商品原价
        price: "278.00"          //商品现价
        sales_price: "278.00"    //同上
        special: ""    //商品亮点
        tag_name: "新品"   //商品标签
        three: "生活家居" //三级分类
        two: "品质生活"   //二级分类
        path: "", //下面savePath的type=6的时候根据这个跳转  
            path: "", //下面savePath的type=6的时候根据这个跳转  
            savePath: { //跳转路径
            type: "", //跳转类型：1:商品，2:分类，3:app路径，4:h5，5:app页面跳转
                url: "javascript:;",  // type=3的时候根据这个跳转
                id: ""   //type=1,2，根椐id跳指定商品/分类页面
            h5_url:'' // type=4的时候根据这个跳转
            app_url: // type=5的时候根据这个跳转 
                }
    ],
        title: "限时购",    //限时活动标题
            textColor: "#333333",  //标题颜色
            type: "",          //限时活动类型   1: 限时优惠 2: N件N折 3: 满优惠 4: 预售 5: 多人拼团 6: 优惠套装 
            morePath: { url: "", bk_url: "" },   //组件右上角更多 url:跳转路径  bk_url:可用url
        activity: {
            title: "",  //活动标题
                id: "",     //活动id
                start_time: "",  //活动开始时间
                end_time: ""     //活动结束时间
        }
        path: "", //下面savePath的type=6的时候根据这个跳转  
            savePath: { //跳转路径
            type: "", //跳转类型：1:商品，2:分类，3:app路径，4:h5，5:app页面跳转
                url: "javascript:;",  // type=3的时候根据这个跳转
                id: ""   //type=1,2，根椐id跳指定商品/分类页面
            h5_url:'' // type=4的时候根据这个跳转
            app_url: // type=5的时候根据这个跳转 
                }
    }
},
```



## 富文本组件

```js
{
  name: "富文本",      //组件名称
  type: "cRichText",  //组件标识
  attribute: { 
      styleTemplate: "img",   //显示样式  img:图片  text:富文本
      textContent: "",     //富文本内容，styleTemplate为text时，用这个显示
      image: ""           //图片地址， styleTemplate为img时，用这个显示
  }
},
```



## 图片广告组件

```js
{
  name: "图片广告",   //组件名称
  type: "cAdvertising",  //组件标识
  attribute: {
    image: "",  //图片地址
    path: "",
    savePath: { //跳转路径
            type: "", //跳转类型：1:商品，2:分类，3:app路径，4:h5
            url: "javascript:;",  // type=3的时候根据这个跳转
            id: ""   //type=1,2，根椐id跳指定商品/分类页面
            h5_url:'' // type=4的时候根据这个跳转 
          }
  }
},
```



## 商品组件

```js
{
    name: "商品模块",  //组件名称
        type: "cGoods",   //组件标识
        attribute: {
        styleTemplate: "two", //样式 one:一行一个，two:一行两个，three:一行三个
            goods_data: [
                goods_name: "ROYDX莱德斯时尚水纹直身杯B"  //商品名称
                id: 3619   //商品id
                image_path: "http://wx-dealer.oss-cn-shenzhen.aliyuncs.com/commodity/20201030/44fbbb1f215f399ed7e49e2336076dcf.jpg"  //商品展示图片地址
                one: "" //一级分类  
                original_price: "278.00"  //商品原价
                price: "278.00"          //商品现价
                sales_price: "278.00"    //同上
                special: ""    //商品亮点
                tag_name: "新品"   //商品标签
                three: "生活家居" //三级分类
                two: "品质生活"   //二级分类
                pay_style: 1 // 1现金+积分 2现金 3积分
                max_point: 10 // 最大可用积分,0为不限制
        ]
    }
},
```

## 频道栏组件

```js
{
  name: "频道栏",   //组件名称
  type: "cChannel",  //组件标识
  attribute: {
    styleTemplate: "fix",      //样式 fix:固定，不可滑动  switch:可滑动
    color: { 
        textColor: "#333333"   //文字颜色
    },
    imgs: [
      {
        src: "",         //图片链接地址  
        path: "", 
        name: "保养套餐",  //频道入口名称
        savePath: { //跳转路径
            type: "", //跳转类型：1:商品，2:分类，3:app路径，4:h5
            url: "javascript:;",  // type=3的时候根据这个跳转
            id: ""   //type=1,2，根椐id跳指定商品/分类页面
            h5_url:'' // type=4的时候根据这个跳转
          }
      }
    ]
  }
},
```



## 优惠券组件

```js
{
    name: "优惠券",   //组件名称
        type: "cCoupon",  //组件标识
        attribute: {
        title: "购物先领券",  //组件标题
            subhed: "副标",      //组件副标
            styleTemplate: 3,    //样式  1:单张，2:两张，3:三张 , 4:专题页无边距卡券
            cards_data: [
            {
                card_data: {   //优惠券信息
                    card_id: 1101,    //优惠券ID
                    card_type_name: 4,  //优惠券类型
                    card_name: "节气阀清洗20元券*",   //优惠券名称
                    act_code: "402106261101"        //优惠券code
                },
                img: "http://wx-dealer.oss-cn-shenzhen.aliyuncs.com/home_page_small_set/xxxx.jpg", // 未领券
                img_got: "http://wx-dealer.oss-cn-shenzhen.aliyuncs.com/home_page_small_set/xxxx.jpg", // 已领券
                path: "", //忽略，后台显示路径 
                savePath: { //跳转路径
                    type: "", //跳转类型：1:商品，2:分类，3:app路径，4:h5，5:app页面跳转
                    url: "javascript:;",  // type=3的时候根据这个跳转
                    id: ""   //type=1,2，根椐id跳指定商品/分类页面
                    h5_url:'' // type=4的时候根据这个跳转
                    app_url: // type=5的时候根据这个跳转 
                }  //优惠券图片
            }
        ]
    }
}
```





## 轮播广告组件

```js
{
    name: "轮播广告", //组件名称
    type: "cCarousel", //组件标识
    attribute: {  
        imgs: [ //图片数组
            {
                title: "",//·主标题
                titleColor:"#333333",//主标题颜色
                subTitle:"",//副标题
                subTitleColor:"#333333",//·副标题颜色
                src: "", //图片链接地址
                path: "", //忽略，后台显示路径 
                savePath: { //跳转路径
                    type: "", //跳转类型：1:商品，2:分类，3:app路径，4:h5，5:app页面跳转
                    url: "javascript:;",  // type=3的时候根据这个跳转
                    id: "",   //type=1,2，根椐id跳指定商品/分类页面
                    h5_url:'', // type=4的时候根据这个跳转
                    app_url: ""// type=5的时候根据这个跳转 
                },
                userGroups:{}//用户群组
            }
        ]
    }
}
```

## 专题活动专用组件

```js
{
  name: "专题活动专用",   //组件名称
  type: "cAdvertising1", //组件标识
  attribute: {
    styleTemplate: "fix",  //oneActivity：一行一个；doubleActivity：一行两个；threeActivity：一行三个
    imgs: [  //图片数组
      {
          src: "", //图片链接地址
          path: "", //忽略，后台显示路径 
          savePath: { //跳转路径
            type: "", //跳转类型：1:商品，2:分类，3:app路径，4:h5
            url: "javascript:;",  // type=3的时候根据这个跳转
            id: ""   //type=1,2，根椐id跳指定商品/分类页面
            h5_url:'' // type=4的时候根据这个跳转
          }
        }
    ]
  }
},


```

## 抽奖活动组件

```js
{
    name: "抽奖活动",  //组件名称
        type: "cLottery",   //组件标识
        attribute: {
        goods_data: [],
        title: "限时购",    //抽奖活动标题
        rule: "抽奖活动",    //抽奖活动描述
        bannerImg: "",    //抽奖活动展示图
        textColor: "#333333",  //标题颜色
        type: "8",          //活动类型  8抽奖活动
        activity: {
            title: "",  //活动标题
                id: "",     //活动id
                start_time: "",  //活动开始时间
                end_time: "",     //活动结束时间
                draw_num: ""     //抽奖次数
        }
    }
},
```

## 秒杀活动组件

```js
{ // 参考“限时活动组件”
  name: "秒杀活动",  //组件名称
  type: "cSeckill",   //组件标识
  attribute: {
    goods_data: [    //活动商品
         goods_name: "ROYDX莱德斯时尚水纹直身杯B"  //商品名称
         id: 3619   //商品id
         image_path: "http://wx-dealer.oss-cn-shenzhen.aliyuncs.com/commodity/20201030/xxxx.jpg"  //商品展示图片地址
         one: "" //一级分类  
         original_price: "278.00"  //商品原价
         price: "278.00"          //商品现价
         sales_price: "278.00"    //同上
         special: ""    //商品亮点
         tag_name: "新品"   //商品标签
         three: "生活家居" //三级分类
         two: "品质生活"   //二级分类  
         stock: 100 //库存
    ],  
    title: "秒杀",    //限时活动标题
    textColor: "#333333",  //标题颜色
    type: "",          //限时活动类型   1: 限时优惠 2: N件N折 3: 满优惠 4: 预售 5: 多人拼团 6: 优惠套装 7秒杀 
    morePath: { url: "", bk_url: "" },   //组件右上角更多 url:跳转路径  bk_url:可用url
    activity: {
      title: "",  //活动标题
      id: "",     //活动id
      start_time: "",  //活动开始时间
      end_time: ""     //活动结束时间
    }
  }
},
```

## 图片广告(用户群体)
```js
{
    name: "图片广告(用户群体)", //组件名称
    type: "cAdvertising2", //组件标识
    attribute: {  
      imgs: [ //图片数组
        {
          src: "", //图片链接地址
          path: "", //忽略，后台显示路径 
          savePath: { //跳转路径
            type: "", //跳转类型：1:商品，2:分类，3:app路径，4:h5，5:app页面跳转
            url: "javascript:;",  // type=3的时候根据这个跳转
            id: ""   //type=1,2，根椐id跳指定商品/分类页面
            h5_url:'' // type=4的时候根据这个跳转
            app_url: // type=5的时候根据这个跳转 
          }, 
          userGroups:{}//用户群组
        }
      ]
    }
},
```

## 套装组件
```js
{
    "name": "优惠套装",//组件名称
        "type": "cSuit",//组件标识
        "attribute": {
        "list": [
            {
                "id": 321,//活动id
                "title": "测试套装",//活动标题
                "time": "2023-03-28~2023-03-31",//活动时间
                "imgs": [
                    "https://wx-dealer.oss-cn-shenzhen.aliyuncs.com/commodity/20230113/0375d387d412d41a8d9db97485f4df42.jpeg",
                    "https://wx-dealer.oss-cn-shenzhen.aliyuncs.com/commodity/20230317/0bb2e9fd582a51c484e7f1bc13cc0104.jpg"
                ],
                "total": 3//套装数量
            }
        ],
            "title": "优惠套装",
            "textColor": "#333333",
            "styleType": "swiper",
            "src": "",
            "path": "",
            "savePath": {
            "type": "",
                "url": "javascript:;",
                "id": ""
        }
    }
},
```

## 瀑布流商品组件
```js
{
    name: "瀑布流商品组件",  //组件名称
    type: "cWaterfallGoods",   //组件标识
    attribute: {
    styleTemplate: "two", //样式 one:一行一个，two:一行两个，three:一行三个
        goods_data: [
            goods_name: "ROYDX莱德斯时尚水纹直身杯B"  //商品名称
            id: 3619   //商品id
            image_path: "http://wx-dealer.oss-cn-shenzhen.aliyuncs.com/commodity/20201030/44fbbb1f215f399ed7e49e2336076dcf.jpg"  //商品展示图片地址
            one: "" //一级分类  
            original_price: "278.00"  //商品原价
            price: "278.00"          //商品现价
            sales_price: "278.00"    //同上
            special: ""    //商品亮点
            tag_name: "新品"   //商品标签
            three: "生活家居" //三级分类
            two: "品质生活"   //二级分类
            pay_style: 1 // 1现金+积分 2现金 3积分
            max_point: 10 // 最大可用积分,0为不限制
        ],
        total: "30",   //总条数
        per_page: "12",   //页条数
        current_page: "1",   //当前页数
        all_page: "3"   //总页数
    }
},
```
## 众筹组件
```js
{
    name: "众筹活动",//组件名称
        type: "cCrowdfunding",//组件标识
        attribute: {
        goods_data: [{
            image_path: "", //商品图片
            goods_name: "商品名称商品名称", //商品名称
            price: "299", //价格
            id: "", //商品id
            commodity_id: "", //商品id
            commodity_label:"",//自定义标签 商品名称前面显示
            tag_name:["众筹"],//标签名称
            savePath: { //跳转路径
                type: "", //跳转类型：1:商品，2:分类，3:app路径，4:h5
                url: "javascript:;",  // type=3的时候根据这个跳转
                id: ""   //type=1,2，根椐id跳指定商品/分类页面
                h5_url:'' // type=4的时候根据这个跳转
            },
            crowdfund_info: {
                title:'',//活动名称
                start_time:'',//开始时间
                end_time:'',//结束时间
                target:'',//活动目标 1众筹金额 2众筹数量
                target_val:'',//活动目标值
                alr_crowd:'',//已筹显示 1已筹金额 2已筹数量 逗号分割，比如1,2就是两个都要显示
                purchase_num:'',//限购数量
                theme_name:'',//主题活动名称
                act_status:'',//活动状态:1未开始;2进行中;3结束
                plan_status:'',//进度状态:0未开始;1进行中-未达标;2进行中-已达标;3项目成功-自动;4项目成功-人为;5项目失败-自动;6项目失败-人为;7项目失败-待处理
                commodity_id:'',//商品id
                money:'',//已筹金额
                sum_num:'',//已筹数量
                peo_count:'',//参与人数
            }
        }],
            title: "众筹活动",//众筹活动标题
            textColor: "#333333",//标题颜色
            styleType: "list", // 展示形式 list 列表 、swiper 轮播
            type: "9",
            morePath: { url: "", bk_url: "" }, //组件右上角更多 url:跳转路径  bk_url:可用url
        activity: {
            title: "",//活动标题
                id: "", //活动id
                start_time: "", //开始时间
                end_time: "" //结束时间
        },
        src: "",
            path: "",
            savePath: {
            type: "",
                url: "javascript:;",
                id: ""
        }
    }
},
```


## 浮窗
```js
{
    "name": "浮窗",
    "type": "cFloatingWindow",
    "attribute": {
        "type": "1", // 1单张优惠券 2其他类型
        "cards_data": [
            {
                // 领取前图片. 、其他类型的图片
                "img": "https://wx-dealer.oss-cn-shenzhen.aliyuncs.com/home_page_small_set/20240723/8f1303f6963effc3cd9c4415b8c2b901.jpg",
                //领取后图片
                "img_got": "https://wx-dealer.oss-cn-shenzhen.aliyuncs.com/home_page_small_set/20240723/0738f8b0cf0392818425569a17769bb6.jpeg",
                "path": "",
                "savePath": { // 跳转路径 参考其他模块
                    "type": "",
                    "url": "javascript:;",
                    "id": ""
                },
                "card_data": { // 单张优惠券信息
                    "card_id": "17175275086316544", // 卡券id
                    "card_type_name": 1,
                    "card_name": "融合一级商品分类", // 卡券名称
                    "act_code": "402407236544"
                },
                "is_received": 0, // 是否领取过卡券 1-已领取 0-未领取  只有单张卡券才有这个字段
                "article": "",
                "change_car": ""
            }
        ],
        "other_data": [
            {
                "img": "",
                "path": "",
                "savePath": {
                    "type": "",   //跳转类型：1:商品，2:分类，3:app路径，4:h5，5:app页面跳转 6:小程序路径 7:活动列表
                    "url": "javascript:;",
                    "id": ""
                }
            }
        ]
    }
}
```