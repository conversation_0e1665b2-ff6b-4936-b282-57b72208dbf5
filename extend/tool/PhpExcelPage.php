<?php


namespace tool;


class PhpExcelPage
{

    private $objPHPExcel;
    private $objWriter;
    private $filePath;

    public function __construct()
    {
        include VENDOR_PATH . "phpexcel3/PHPExcel.php";
        $this->objPHPExcel = new \PHPExcel();
        $this->objWriter   = new \PHPExcel_Writer_Excel5($this->objPHPExcel);
    }


    /**
     * 设置工作表名称
     * @param $sheetTitle
     * @throws \PHPExcel_Exception
     */
    public function setSheetTitle($sheetTitle)
    {
        //设置指定索引的sheet为当前处于活动状态的sheet（在操作指定单元格前，必须获取其所属于的、处于活动状态的sheet）
        $this->objPHPExcel->setActiveSheetIndex(0);
        $this->objPHPExcel->getActiveSheet()->setTitle($sheetTitle);
    }


    /**
     * 设置表头
     * @param $titleArr array 表头标题 ['A'=>’商品id‘,'B'=>'商品名称','C'=>’商品编码‘]
     * @throws \PHPExcel_Exception
     */
    public function setTitle(array $titleArr)
    {
        foreach ($titleArr as $key => $item) {
            $this->objPHPExcel->getActiveSheet()->setCellValue($key . '1', $item);
        }
        //冻结表头
        $this->objPHPExcel->getActiveSheet()->freezePane('A2');
    }



    /**
     * 插入数据
     * @param array $data  数据 [['A'=>'1234','B'=>'3D抱枕','C'=>’FPX6218002-V066‘]]
     * @param int $page 页码
     * @param int $limit 条数
     * @throws \PHPExcel_Exception
     */
    public function setData(array $data, int $page, int $limit)
    {
        foreach ($data as $row => $line) {
            $row = ($page - 1) * $limit + 2 + $row;
            foreach ($line as $key => $item) {
                if (is_int($item)) {
                    $item = (string)$item;
                }

                $this->objPHPExcel->getActiveSheet()->setCellValueExplicit((string)($key.$row), $item);
            }
        }
    }


    /**
     * 设置列宽
     * @param $widthArr array 宽度 ['A'=>’20‘,'B'=>'30','C'=>'50']
     * @throws \PHPExcel_Exception
     */
    public function setWidth(array $widthArr)
    {
        foreach ($widthArr as $key => $item) {
            $this->objPHPExcel->getActiveSheet()->getColumnDimension($key)->setWidth($item);
        }

        //设置水平居中
        $this->objPHPExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        //垂直居中
        $this->objPHPExcel->getDefaultStyle()->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);

        //设置默认行高
        $this->objPHPExcel->getActiveSheet()->getDefaultRowDimension()->setRowHeight(28);

        $re       = array_keys($widthArr);
        $last_key = end($re);

        //设置表头填充颜色
        $this->objPHPExcel->getActiveSheet()->getStyle('A1:' . $last_key . '1')->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID);
        $this->objPHPExcel->getActiveSheet()->getStyle('A1:' . $last_key . '1')->getFill()->getStartColor()->setARGB('ffdddddd');
        //设置表头水平居中
        $this->objPHPExcel->getActiveSheet()->getStyle('A1:' . $last_key . '1')->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        //设置表头垂直居中
        $this->objPHPExcel->getActiveSheet()->getStyle('A1:' . $last_key . '1')->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);

    }



    /**
     * 保存文件
     * @param $filename
     * @throws \PHPExcel_Writer_Exception
     */
    public function saveFile($filename)
    {
        $this->objWriter->save('public/uploads/exports/' . $filename);
        $this->filePath = 'public/uploads/exports/' . $filename;
    }


    /**
     * 下载文件
     * @param $objTitle
     * @throws \PHPExcel_Writer_Exception
     */
    public function downloadFile($objTitle)
    {
        //输出excel
        ob_end_clean();//清除缓冲区,避免乱码
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename='.$objTitle.date('Y-m-d H:i:s',time()).'.xls');
        header("Content-Transfer-Encoding:binary");
        $this->objWriter->save('php://output');
    }


    /**
     * 获取文件
     * @return mixed
     */
    public function getFilePath()
    {
        return $this->filePath;
    }


}