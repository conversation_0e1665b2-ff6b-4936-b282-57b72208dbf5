# Git 合并工具使用说明

为了简化频繁的"提交当前分支并合并到 feat-sit"操作，我创建了以下几种便捷工具：

## 🚀 方法一：Git Alias（推荐）

最简单的方式，直接使用 git 命令：

```bash
git merge-sit "你的提交信息"
```

### 示例：
```bash
git merge-sit "fix: 修复售后订单重复提交导致金额翻倍问题"
```

## 📝 方法二：Shell 脚本

### 完整版脚本（带详细输出和错误处理）
```bash
./merge-to-sit.sh "你的提交信息"
```

### 简化版脚本（快速执行）
```bash
./quick-merge.sh "你的提交信息"
```

## 🔧 工具功能

所有工具都会自动执行以下操作：

1. **检查并提交当前更改**
   - 如果有未提交的文件，自动添加并提交
   - 使用你提供的提交信息

2. **推送当前分支**
   - 将当前分支推送到远程仓库

3. **切换到 feat-sit 分支**
   - 自动切换到目标分支

4. **拉取最新代码**
   - 确保 feat-sit 分支是最新的

5. **执行合并**
   - 将你的分支合并到 feat-sit
   - 使用默认的合并提交信息

6. **推送合并结果**
   - 将合并后的 feat-sit 推送到远程

7. **切换回原分支**
   - 自动切换回你原来的工作分支

## 💡 使用建议

- **推荐使用 Git Alias 方式**，因为它最简洁且不需要额外文件
- 提交信息要清晰描述你的更改内容
- 如果遇到合并冲突，工具会停止并提示你手动解决

## 🛠️ 安装说明

### Git Alias 已经自动安装
如果需要重新安装或在其他机器上使用，运行：

```bash
git config --global alias.merge-sit '!f() { 
    if [ $# -eq 0 ]; then 
        echo "使用方法: git merge-sit \"提交信息\""; 
        return 1; 
    fi; 
    COMMIT_MESSAGE="$1"; 
    CURRENT_BRANCH=$(git branch --show-current); 
    echo "🚀 开始合并 $CURRENT_BRANCH 到 feat-sit..."; 
    if ! git diff-index --quiet HEAD --; then 
        echo "📝 提交当前更改..."; 
        git add -A && git commit -m "$COMMIT_MESSAGE"; 
    fi; 
    echo "⬆️  推送当前分支..."; 
    git push origin "$CURRENT_BRANCH" || true; 
    echo "🔄 切换到 feat-sit 并合并..."; 
    git checkout feat-sit && git pull origin feat-sit && git merge "$CURRENT_BRANCH" --no-edit; 
    echo "⬆️  推送 feat-sit..."; 
    git push origin feat-sit; 
    git checkout "$CURRENT_BRANCH"; 
    echo "✅ 完成！已将 $CURRENT_BRANCH 合并到 feat-sit"; 
}; f'
```

### Shell 脚本已经创建并设置了执行权限
- `merge-to-sit.sh` - 完整版，带详细输出和错误处理
- `quick-merge.sh` - 简化版，快速执行

## ⚠️ 注意事项

1. 确保你在正确的分支上（不要在 feat-sit 分支上运行）
2. 如果有合并冲突，需要手动解决
3. 工具会自动提交所有未提交的更改，请确保这些更改是你想要的
4. 建议在重要操作前先备份或确认当前状态

## 🎯 快速开始

最简单的使用方式：

```bash
# 在你的功能分支上做了一些修改后
git merge-sit "feat: 添加新功能"
```

就这么简单！🎉
