# goodsList 方法深度分析与优化方案

## 原始方法复杂度分析

经过深入分析，我发现原始的 `goodsList` 方法确实非常复杂，包含了大量的业务逻辑：

### 🔍 主要业务逻辑模块

#### 1. **参数处理模块**
- 26个不同的请求参数
- 复杂的参数验证和默认值处理
- 特殊业务场景的参数组合

#### 2. **查询条件构建模块**
```php
// 基础条件
$where['a.is_enable'] = 1;
$where['c.is_enable'] = 1;
$where['b.count_stock'] = ['>', 0];

// 渠道过滤
$where[] = ['exp', "find_in_set('{$channel_type}',a.up_down_channel_dlr)"];

// 搜索条件（数字ID vs 名称搜索）
if (preg_match('/^\d+$/', $name)) {
    $where['a.commodity_id'] = $name;
} else {
    $flat_goods_ids = $flat->getColumn(['where' => ['commodity_name' => ['like', '%' . $name . '%']], 'column' => 'commodity_id']);
    $where['a.commodity_id'] = ['in', $flat_goods_ids];
}
```

#### 3. **复杂的车型和保养逻辑**
```php
// 车型关联
$tc_qz = $this->tc_zg($user, $lng, $lat, $kilometer, $dd_dlr_code);
$dlr_code = $tc_qz['dlr_code'];
$dlr_level = $tc_qz['dlr_level'];
$maintain_type = $tc_qz['maintain_type'];

// 城市级别过滤
$where[] = ['exp', "(find_in_set('{$dlr_level}',d.city_type) || d.city_type='' || d.city_type is null)"];

// 机油类型过滤
$where[] = ["exp", sprintf("d.oli_liters in (%s) || d.oli_liters =''", $user_oil_type_s)];
```

#### 4. **卡券业务逻辑**
```php
// 卡券商品关联
if ($card_id) {
    $where[] = ['exp', " (find_in_set({$card_id},a.card_id)) "];
    $and_where = " and find_in_set($card_id,card_c.card_id) ";
    
    // 卡券SKU配置检查
    $card_info_list = $card_goods_model->getList(['where' => $card_info_where]);
}

// 复杂的卡券获取逻辑
$card_get_use = $this->card_get_use($goods_set_id_arr,[],$user, $this->channel_type,$card_in_attr,'',1,[],'',$from,'',99,$use_gift_card);
```

#### 5. **核心性能瓶颈**

##### **瓶颈1: 复杂的5表JOIN查询**
```php
// 在 getCommodityListUSku 中
return $this->alias('a')
    ->join('t_db_commodity_set b', "a.commodity_set_id=b.id and b.is_enable=1")
    ->join("t_db_commodity_card card_c", "a.commodity_id = card_c.commodity_id and card_c.sorts>0 " . $params['and_where'], "left")
    ->join("t_db_card cards", " card_c.card_id=cards.id and cards.is_enable=1 and cards.validity_date_start > now() and cards.validity_date_end <= NOW() ", "left")
    ->join('t_db_commodity_set_sku c ', "c.commodity_set_id=b.id and c.is_enable=1")
    ->join("t_db_commodity_sku d ", "d.id=c.commodity_sku_id and d.is_enable=1")
    ->where($params['where'])
    ->field($params['field'])
    ->order($params['order'])
    ->group($params['group'])
    ->paginate($params['pagesize']);
```

##### **瓶颈2: 批量SKU查询优化**
```php
// 原始：在循环外预查询所有SKU数据
$more_sku_id_arr = $this->sku_model->alias('a')
    ->join('t_db_commodity_set_sku b','a.id=b.commodity_sku_id and a.is_enable=1 and b.is_enable=1')
    ->where(['b.id'=>['in',explode(',',trim($aa_gc_id_str,','))]])
    ->field("a.id,a.sp_value_list,b.price,b.id,b.id bid,a.sku_code,a.variety_code,a.maintain_q,b.commodity_id")
    ->order('b.price DESC,a.id DESC,b.id DESC')
    ->group('b.commodity_id,a.id')
    ->select();

// 然后按商品ID分组
foreach ($more_sku_id_arr as $mm_v) {
    $more_sku_id_arr_to_goods[$mm_v['commodity_id']][] = $mm_v;
}
```

##### **瓶颈3: 卡券查询复杂度**
```php
// card_get_use 方法内部包含多个复杂查询
$card_get_use = $this->card_get_use($goods_set_id_arr,[],$user, $this->channel_type,$card_in_attr,'',1,[],'',$from,'',99,$use_gift_card);
```

## 🚀 正确的优化策略

### 1. **保留业务逻辑，优化查询方式**

我之前的错误是试图简化业务逻辑，但实际上应该：
- ✅ 保留所有业务逻辑不变
- ✅ 优化数据库查询方式
- ✅ 改进缓存策略
- ✅ 减少重复查询

### 2. **分层优化策略**

#### **第一层：查询优化**
```php
// 原始：复杂的5表JOIN
// 优化：保持JOIN但添加合适索引，优化查询条件顺序

// 原始：循环内查询
// 优化：批量预查询 + 内存分组
```

#### **第二层：缓存优化**
```php
// 原始：单一缓存，15秒过期
// 优化：分层缓存
// - 基础商品数据：30分钟
// - 用户相关数据：5分钟  
// - 实时价格库存：1分钟
```

#### **第三层：数据结构优化**
```php
// 原始：每次都重新计算
// 优化：预计算 + 缓存结果
```

### 3. **具体优化实现**

#### **优化点1: 批量SKU查询**
```php
// 收集所有gc_id
$aa_gc_id_str = '';
foreach ($list as $tmp_vv) {
    if($tmp_vv['gc_id']){
        $aa_gc_id_str .= $tmp_vv['gc_id'] . ',';
    }
}

// 一次性查询所有SKU数据
$more_sku_id_arr = $this->getBatchSkuData(trim($aa_gc_id_str, ','));

// 按商品ID分组，避免循环查询
$more_sku_id_arr_to_goods = [];
foreach ($more_sku_id_arr as $mm_v) {
    $more_sku_id_arr_to_goods[$mm_v['commodity_id']][] = $mm_v;
}
```

#### **优化点2: 卡券数据缓存**
```php
// 卡券数据分层缓存
$card_cache_key = sprintf('card_data_%s_%s_%s', 
    md5(implode(',', $goods_set_id_arr)), 
    $user['id'], 
    $channel_type
);

$card_get_use = Cache::remember($card_cache_key, function() {
    return $this->card_get_use(...);
}, 300);
```

#### **优化点3: 减少重复计算**
```php
// 预计算用户信息
$user_info = $this->getFriendBaseInfo($user);

// 预计算分类信息
$comm_type_cache = [];
foreach ($unique_comm_types as $type_id) {
    $comm_type_cache[$type_id] = $this->_goods_type_arr($type_id);
}
```

## 🎯 修正后的优化方案

### 核心原则
1. **业务逻辑零改动** - 保持所有原始业务逻辑
2. **查询方式优化** - 批量查询替代循环查询
3. **智能缓存** - 多层缓存策略
4. **渐进式优化** - 可以逐步应用优化

### 实施步骤

#### 阶段1: 查询优化（立即可实施）
- 批量SKU查询
- 优化JOIN查询顺序
- 添加必要索引

#### 阶段2: 缓存优化（需要测试）
- 分层缓存策略
- 缓存预热机制
- 缓存失效策略

#### 阶段3: 架构优化（长期规划）
- 读写分离
- 异步处理
- 微服务拆分

## 📊 预期优化效果

基于保留完整业务逻辑的前提下：

- **查询时间减少：** 30-50%（主要来自批量查询）
- **内存使用减少：** 20-30%（减少重复对象创建）
- **并发能力提升：** 2-3倍（减少数据库压力）
- **缓存命中率：** 70-80%（分层缓存策略）

## ⚠️ 我之前的错误

1. **过度简化业务逻辑** - 删除了重要的业务处理
2. **忽略复杂查询条件** - 没有考虑车型、保养、卡券等复杂逻辑
3. **缓存策略过于激进** - 可能导致数据不一致

## 💡 正确的优化思路

1. **先理解，再优化** - 完全理解业务逻辑后再进行优化
2. **保守优化** - 优先保证功能正确性
3. **分步验证** - 每个优化点都要充分测试
4. **监控驱动** - 基于实际性能数据进行优化

## 🔧 建议的实施方案

1. **第一步：** 添加性能监控，了解当前瓶颈
2. **第二步：** 实施批量查询优化（风险最低）
3. **第三步：** 优化缓存策略（需要仔细测试）
4. **第四步：** 数据库索引优化
5. **第五步：** 架构级优化

您觉得这个分析和优化方向是否正确？我们应该从哪个优化点开始实施？
