<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

use think\Env;

return [
    // +----------------------------------------------------------------------
    // | 应用设置
    // +----------------------------------------------------------------------

    // 应用模式状态 开发环境:develop 测试环境:testing 生产环境:product#
    'app_status'             => Env::get('ENV_APP_STATUS', 'develop'),

    // 由于配置文件只有生产和测试，为了区分生产:product，灰度:graw，和测试环境:develop，新增一个key
    'app_status_cron'        => env::get('APP_STATUS_CRON', 'product'),

    // 应用命名空间
    'app_namespace'          => 'app',
    // 是否支持多模块
    'app_multi_module'       => true,
    // 入口自动绑定模块
    'auto_bind_module'       => false,
    // 注册的根命名空间
    'root_namespace'         => [],
    // 扩展函数文件
    'extra_file_list'        => [THINK_PATH . 'helper' . EXT],
    // 默认输出类型
    'default_return_type'    => 'html',
    // 默认AJAX 数据返回格式,可选json xml ...
    'default_ajax_return'    => 'json',
    // 默认JSONP格式返回的处理方法
    'default_jsonp_handler'  => 'jsonpReturn',
    // 默认JSONP处理方法
    'var_jsonp_handler'      => 'callback',
    // 默认时区
    'default_timezone'       => 'PRC',
    // 是否开启多语言
    'lang_switch_on'         => false,
    // 默认全局过滤方法 用逗号分隔多个
    'default_filter'         => 'custom_filter',
    // 默认语言
    'default_lang'           => 'zh-cn',
    // 应用类库后缀
    'class_suffix'           => false,
    // 控制器类后缀
    'controller_suffix'      => false,

    // +----------------------------------------------------------------------
    // | 模块设置
    // +----------------------------------------------------------------------

    // 默认模块名
    'default_module'         => 'index',
    // 禁止访问模块
    'deny_module_list'       => ['common'],
    // 默认控制器名
    'default_controller'     => 'Index',
    // 默认操作名
    'default_action'         => 'index',
    // 默认验证器
    'default_validate'       => '',
    // 默认的空控制器名
    'empty_controller'       => 'Error',
    // 操作方法后缀
    'action_suffix'          => '',
    // 自动搜索控制器
    'controller_auto_search' => false,

    // +----------------------------------------------------------------------
    // | URL设置
    // +----------------------------------------------------------------------

    // PATHINFO变量名 用于兼容模式
    'var_pathinfo'           => 's',
    // 兼容PATH_INFO获取
    'pathinfo_fetch'         => ['ORIG_PATH_INFO', 'REDIRECT_PATH_INFO', 'REDIRECT_URL'],
    // pathinfo分隔符
    'pathinfo_depr'          => '/',
    // URL伪静态后缀
    'url_html_suffix'        => '',
    // URL参数方式 0 按名称成对解析 1 按顺序解析
    'url_param_type'         => 0,
    // 是否开启路由
    'url_route_on'           => true,
    // 路由使用完整匹配
    'route_complete_match'   => false,
    // 路由配置文件（支持配置多个）
    'route_config_file'      => ['route', 'net_small', 'net_api', 'net_dealer', 'admin_v2'],
    // 是否强制使用路由
    'url_route_must'         => false,
    // 域名部署
    'url_domain_deploy'      => false,
    // 域名根，如thinkphp.cn
    'url_domain_root'        => '',
    // 是否自动转换URL中的控制器和操作名
    'url_convert'            => true,
    // 默认的访问控制器层
    'url_controller_layer'   => 'controller',
    // 表单请求类型伪装变量
    'var_method'             => '_method',
    // 表单ajax伪装变量
    'var_ajax'               => '_ajax',
    // 表单pjax伪装变量
    'var_pjax'               => '_pjax',
    // 是否开启请求缓存 true自动缓存 支持设置请求缓存规则
    'request_cache'          => false,
    // 请求缓存有效期
    'request_cache_expire'   => null,

    // +----------------------------------------------------------------------
    // | 模板设置
    // +----------------------------------------------------------------------

    'template'              => [
        // 模板引擎类型 支持 php think 支持扩展
        'type'         => 'Think',
        // 模板路径
        'view_path'    => '',
        // 模板后缀
        'view_suffix'  => 'php',
        // 模板文件名分隔符
        'view_depr'    => DS,
        // 模板引擎普通标签开始标记
        'tpl_begin'    => '{',
        // 模板引擎普通标签结束标记
        'tpl_end'      => '}',
        // 标签库标签开始标记
        'taglib_begin' => '{',
        // 标签库标签结束标记
        'taglib_end'   => '}',
    ],

    // 视图输出字符串内容替换
    'view_replace_str'      => [

    ],
    // 默认跳转页面对应的模板文件
    'dispatch_success_tmpl' => THINK_PATH . 'tpl' . DS . 'dispatch_jump.tpl',
    'dispatch_error_tmpl'   => THINK_PATH . 'tpl' . DS . 'dispatch_jump.tpl',

    // +----------------------------------------------------------------------
    // | 异常及错误设置
    // +----------------------------------------------------------------------

    // 异常页面的模板文件
    'exception_tmpl'        => THINK_PATH . 'tpl' . DS . 'think_exception.tpl',

    // 错误显示信息,非调试模式有效
    'error_message'         => '页面错误！请稍后再试～',
    // 显示错误信息
    'show_error_msg'        => false,
    // 异常处理handle类 留空使用 \think\exception\Handle
    'exception_handle'      => '\\app\\common\\exception\\Http',

    // +----------------------------------------------------------------------
    // | 日志设置
    // +----------------------------------------------------------------------

    'log' => [
        // 日志记录方式，内置 file socket 支持扩展
        'type'      => 'File',
        // 日志保存目录
        'path'      => LOG_PATH,
        // 日志记录级别
        'level'     => ['error'],
        'file_size' => 209715200,
    ],

    // +----------------------------------------------------------------------
    // | 会话设置
    // +----------------------------------------------------------------------

    'session'  => [
        'id'             => '',
        // SESSION_ID的提交变量,解决flash上传跨域
        'var_session_id' => '',
        // SESSION 前缀
        'prefix'         => 'think',
        // 是否自动开启 SESSION
        'auto_start'     => true,
        'type'           => Env::get('session_type', ''),
        // 是否自动开启 SESSION
        //        'auto_start'     => true,
        // redis主机
        'host'           => Env::get('cache_host', 'r-wz96f04ed5fc0194.redis.rds.aliyuncs.com'),
        // redis端口
        'port'           => Env::get('cache_port', 6379),
        // 密码
        'password'       => Env::get('cache_password', 'aLHdlnMzQgq6Av18'),

        'secure'   => Env::get('cache_secure', true),
        'httponly' => Env::get('cache_httponly', true),
    ],

    // +----------------------------------------------------------------------
    // | Cookie设置
    // +----------------------------------------------------------------------
    'cookie'   => [
        // cookie 名称前缀
        'prefix'    => '',
        // cookie 保存时间
        'expire'    => 0,
        // cookie 保存路径
        'path'      => '/',
        // cookie 有效域名
        'domain'    => '',
        //  cookie 启用安全传输
        'secure'    => false,
        // httponly设置
        'httponly'  => '',
        // 是否使用 setcookie
        'setcookie' => true,
    ],

    //分页配置
    'paginate' => [
        'type'      => 'bootstrap',
        'var_page'  => 'page',
        'list_rows' => 10,
    ],

    'tim' => [
        'sdk_app_id'  => Env::get('TIM_SDKAPPID', 1400426629),
        'sdk_app_key' => Env::get('TIM_SDKAPPKEY', 'bf48f82555866881a74101971fe1b00ff670f021ad7b57c6c21f5a1b37426962'),
        'group_id'    => Env::get('TIM_GROUPID', '@TGS#aRKDISWGM'),
    ],

    'nissan_en_key'   => Env::get('NISSAN_EN_KEY', '@fSlPP2QPK=!zY2ZmNkMD=~yZjFlMmJiNzc=$GNkYzd#'),

    //缓存 key
    'cache_prefix'    => [
        'home'                    => 'tmp_home_page_',#
        'catalog'                 => 'tmp_catalog_page',
        'special'                 => 'tmp_special_page_',
        'search'                  => 'tmp_search_page_',
        'car_series'              => 'tmp_car_series',
        'car_series_set'          => 'tmp_car_series_set',#集合数据删除
        'commodity_detail'        => 'tmp_commodity_detail',
        'commodity_detail_set'    => 'tmp_commodity_detail_set',#商品详情集合数据删除
        'gwset_product_imgs'      => 'tmp_gwset_product_imgs',
        'gwset_products'          => 'tmp_gwset_products',
        'gwset_recommendations'   => 'tmp_gwset_recommendations',
        'gwset_banners'           => 'tmp_gwset_banners',
        'gwset_hots'              => 'tmp_gwset_hots',
        'gwset_catalogs'          => 'tmp_gwset_catalogs',
        'goods_class_set'         => 'tmp_goods_class_set',
        'spec_value'              => 'tmp_spec-value-suit-spec',
        'dlr_str'                 => 'tmp_check_dlr_str',
        'ch_area'                 => 'tmp_ch_area_cho_dlr_100',
        'goods_section'           => 'tmp_goods_section',
        'gwset_popup'             => 'tmp_gwset_popup',
        'more_people_more_home'   => 'tmp_home_one_vins_info_',#千人千面首页缓存
        'more_people_more_sp'     => 'tmp_special_o_ids_vin_',#千人千面专题页缓存
        'more_people_more_hm_set' => 'tmp_special_o_ids_hm_set',#千人千面首页面缓存集合
        'more_people_more_sp_set' => 'tmp_special_one_ids_vin_set',#千人千面专题页缓存集合
        'all_dd_commodity'        => 'all_dd_commodity',#所有的到店商品ID集合
        'member_info'             => 'tmp_member_info',#数营member信息
        'vin_car_18n_list'        => 'tmp_vin_car_18n_list',#vin码对应18位码关系
        'user_car_18n_list'       => 'tmp_user_car_18n_list_1',#vin码对应18位码关系
        'user_car_18n_set'        => 'tmp_user_car_18n_set',#vin码对应18位码关系
        'basic_data'              => 'tmp_basic_data_',#基础数据缓存
        'index_tip'               => 'index_tip',#进入首页需要弹出提示
        'index_bind_vin'               => 'index_bind_vin',#已经绑定vin了
    ],

    #是否通过java转发所有内部接口
    'market_transfer' => Env::get('MARKET_TRANSFER', true),

    'port' => [
        'market_base'        => [
            'client' => [
                'base_uri'        => Env::get('MARKET_BASE_URL', 'http://java-base-data-api.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
        ],
        'payment'            => [
            'client'  => [
                'base_uri'        => Env::get('PORT_PAYMENT_URL', 'http://feat-nissan201126--php-finance-service.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'cashier' => [
                'cashier_appid'     => Env::get('CASHIER_APPID', 'SC07'),
                'appid'             => Env::get('APPID', '27467aba-88ca-4a5e-ba1d-7b6e130c5020'),
                'key'               => Env::get('CASHIER_KEY', '4co1DOa5cCNoPb9Cu'),
                'center_account_id' => Env::get('CASHIER_CENTER_ACCOUNT_ID', 'PL02'),
            ]
        ],
        'payment_pz'         => [
            'client'  => [
                'base_uri'        => Env::get('PORT_PAYMENT_URL', 'http://feat-nissan201126--php-finance-service.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            //            appid: SC05
            //    appkey: 2co1DOa5cCNoPb9Cu
            //    center:
            //      accountid: PL03
            //72e485ab-7827-4307-b63d-6d300c064d99
            'cashier' => [
                'cashier_appid'     => Env::get('CASHIER_APPID_PZ', 'SC05'),
                'appid'             => Env::get('APPID_PZ', '72e485ab-7827-4307-b63d-6d300c064d99'),
                'key'               => Env::get('CASHIER_KEY_PZ', '2co1DOa5cCNoPb9Cu'),
                'center_account_id' => Env::get('CASHIER_CENTER_ACCOUNT_ID_PZ', 'PL03'),
            ]
        ],
        'ly_payment'         => [
            'client'              => [
                // url 统一经 数营 THIRD 转发到 LY
                'base_uri'        => Env::get('LY_PAYMENT_THIRD', 'http://feat-nissan201126-uat--php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false,
            ],
            'appId'               => Env::get('LY_PAYMENT_APP_ID', '1048'),
            'app_secret'          => Env::get('LY_PAYMENT_APP_SECRET', 'bJP1GY1B3Tdy7bAUUSwso9LYXKyIcBWG'),
            'private_key'         => Env::get('LY_PAYMENT_PRIVATE_KEY', 'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCRga/PYK1U167rqlOr8dfRKlg2crNY6CjjXT7B+qrbPdsc/6xm/N7ectDnXBpWJDpeuTn1qLEpRF2fZpCW/icK52GjzHHEZiHx4yhz58gQfSiFQyVq+roBdsN7Emfum5SEFR/mPMKX7vHH8huiBlKCwal6JngJk08EK2n4gLo7TyLdd0MoYcZULkyjNBRQLeGtWssU7At61ZwlfDKJMpfqjrNXyXk4oi8ieWyYY6cpydLd/iqcciJTs/M4tGaFtzBNg6iFrrzkoU92y/MOHEA71BzF2SFe7DGdnNB4jrfw5a0flSb2yHwb3bkfFxgbA4HngW/Dr6M/RjjLiwtIhJLFAgMBAAECggEAafQPe2igjslHnF+48dn3F15IQc2f/zt+mqFJk6LeSyttpF4gwhWcjEr7B52vp85VmqgyJkoiRzR7IBllbC3BbFU0ajvzE9pzGVoaGNxMeIVKvk84rf/I9kvu6G1stPkhWlSvl83ivRPHh5sqt/72kjRHHqVof0B/J0AduFC0S36lO77o7x0AxEDiz9d5AxFhy1PC7wBsJs1cdtL9DVwO5ee3N2WoqhjLgZEU/oavXNsNrgp9ZULYU6nuPFMPF/wnxeGQ2ycwrcW0Cp7x2mk9Woj8T4AuyYFErwmxi1xo6AQQX8zLfZWBk+qAjD/7kGTXVUFrruXJ9HpL1loRCKNPeQKBgQDlzNWvi8JCzSRBwDe4CUNB+ByuPdy5tyKKavPjghAw5OWSOFWfDiecrzbxhGh0dAOG36dmozXcILIupkWUoPrmxMAeHN5UOVlKdjMhNQwN2CyjAUB46HOFKIWyGpCaf+KERM9NTUwWvgw5eHn7yLH84tIEnyDB8wiKlv0jpytrCwKBgQCiGJSXUufkFHqaeiGz7a7T3hxsPWfFrr+t9eMbc+AmkwqjMWLr/N+g+Z/DxI7AfmEqDWw3JgxiDykcZYlgdoXDxy1E4gcjLGaDBWitUd0eRiMvWbEeQYkAe9YyDIHy+Xz+aYNpGqiipEuiAttsraPM3lufHIZr06MZIsQ3oZUbbwKBgFMVpyDMKBVc9EHtjHe2qaGdYklBRp/C7fMbzkgwi0gSSkp+ob5bMMk+tF/IHrgUBf5quKysT9nh3GpvZXLibTSoaEm66EuNXJJGgB/+fPg4c5MP7c6JRNMdHpGRdzzVBgIKmuT5EIycc/EHdRsqqeej3okbO43EIA9zndyZIWKXAoGAWBz2gP+/MDt1ng1vzqgQVfkyCCClDU99wKmeB6lImo9pw+YvuMpgIdgv4bIySy8n0D01qxDSwGcvWNFVCQvEkPTvhbc8XAFhxC0nhreudCt6Kt4dqq3DNHEcsV4dMaAvMvjWR0cHjkmzF2FoN754AQCKW4adB3Ym/Z3pfbgnHp8CgYA+lt/oqNhAsuc3Pnz5NRIG5IpnyFoDaAilwD1p94qldkdIlqwRt8xw0+wE9lAao/tkKVsZWiqllpz/H+C6vVP06rNodLnOluNoN17PtHeQit95aMYMieWDo66F8vQehdaq047PQ9RnOOuD0Taw/f1x/YenJnKK55aaxzbItGWuKA=='),
            'public_key'          => Env::get('LY_PAYMENT_PUBLIC_KEY', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkYGvz2CtVNeu66pTq/HX0SpYNnKzWOgo410+wfqq2z3bHP+sZvze3nLQ51waViQ6Xrk59aixKURdn2aQlv4nCudho8xxxGYh8eMoc+fIEH0ohUMlavq6AXbDexJn7puUhBUf5jzCl+7xx/IbogZSgsGpeiZ4CZNPBCtp+IC6O08i3XdDKGHGVC5MozQUUC3hrVrLFOwLetWcJXwyiTKX6o6zV8l5OKIvInlsmGOnKcnS3f4qnHIiU7PzOLRmhbcwTYOoha685KFPdsvzDhxAO9QcxdkhXuwxnZzQeI638OWtH5Um9sh8G925HxcYGwOB54Fvw6+jP0Y4y4sLSISSxQIDAQAB'),
            'notice_url'          => Env::get('LY_PAYMENT_NOTICE_URL', 'https://feat-nissan201126-dndcfenge-php-wxmp-dealers.nissan-aftermarket.dev.dndc.cloud/net-small/pay_plat/unifyPayCallback'),
            'refund_url'          => Env::get('LY_PAYMENT_REFUND_URL', 'https://feat-nissan201126-dndcfenge-php-wxmp-dealers.nissan-aftermarket.dev.dndc.cloud/net-small/normal/refundCallback'),
            'sub_app_id'          => [
                'rc_sub_app_id'   => Env::get('LY_PAYMENT_RC_SUB_APP_ID', 'wxfd5a0daa1bdcadf6'), // 日产appid
                'qc_sub_app_id'   => Env::get('LY_PAYMENT_QC_SUB_APP_ID', 'wx9c6a1d0939f213f6'), // 启辰appid
                'pz1a_sub_app_id' => Env::get('LY_PAYMENT_PZ1A_SUB_APP_ID', 'wx8814d55e0a8a9722'), // pz1a appid
            ],
            // sceneType 场景编码
            'scene_type'          => [
                'rc_scene_type'   => Env::get('LY_PAYMENT_RC_SCENE_TYPE', 'MALL-NV-COMMON'),
                'qc_scene_type'   => Env::get('LY_PAYMENT_QC_SCENE_TYPE', 'MALL-V-COMMON'),
                'pz1a_scene_type' => Env::get('LY_PAYMENT_PZ1A_SCENE_TYPE', 'MALL-A-COMMON'),
            ],
            // DNDC 支付账号
            'ly_pay_dlr_id'       => [
                '01' => Env::get('LY_PAYMENT_01_DLR_ID', 'DNDC2'), // 招行
                '02' => Env::get('LY_PAYMENT_02_DLR_ID', 'DNDC1'), // 工行
            ],
            // 支付默认银行
            'ly_pay_default_bank' => Env::get('LY_PAY_DEFAULT_BANK', '02'),
            'gversion'            => Env::get('LY_PAYMENT_G_VERSION', ''),
            'prefix'              => Env::get('LY_PAYMENT_PREFIX', 'busiplat-pay-protocol-service/')
        ],
        //联友取送车预约支付配置
        'ly_app_pay'         => [
            'client'     => [
                'base_uri'        => Env::get('LY_APP_PAY_THIRD', 'http://*************:37094/rest/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false,
            ],
            'appid'      => Env::get('LY_APP_PAY_APP_ID', '20210604850297939669549056'),
            'secure_key' => Env::get('LY_APP_PAY_SECRET_KEY', '48083a9373394f42ec6013d2ad010604'),
            'gversion'   => Env::get('LY_APP_PAY_G_VERSION', ''),
            'prefix'     => Env::get('LY_APP_PAY_PREFIX', 'ly.mp.sy.client.proxy.service/')
        ],
        // 维保
        'maintain'           => [
            'client'  => [
                'base_uri'        => Env::get('MAINTAIN_SERVICE_URL', ''),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false,
            ],
            'prefix'              => Env::get('MAINTAIN_SERVICE_PREFIX', 'rest/busiplat-se-salemake-service/'),
            'cashier' => [
                'appid'      => Env::get('MAINTAIN_SERVICE_API', '20211217921430540341477376'),
                'secure_key' => Env::get('MAINTAIN_SERVICE_SECURE', '31935653caac1f14060412052c011ffe'),
                'gversion'   => Env::get('MAINTAIN_G_VERSION', ''),
            ]
        ],
        'member'             => [
            'client' => [
                'base_uri'        => Env::get('MEMBER_URL', 'http://feat-nissan201126--php-member-service.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
        ],
        'e4s'                => [
            'client' => [
                'base_uri'        => Env::get('E4S_URL', 'http://feat-nissan201126--php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
        ],
        'e3s'                => [
            'client'         => [
                'base_uri' => Env::get('E3S_URL', 'http://feat-sit--php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'cashier'        => [
                'appid'      => Env::get('E3S_API', '20211217921430540341477376'),
                'secure_key' => Env::get('E3S_SECURE', '31935653caac1f14060412052c011ffe'),
                'gversion'   => Env::get('E3S_GVERSION', 'suying-middle-service-sesale-sprint2'),
            ],
            //取送车固定专营店配置
            'card_fixed_dlr' => [
                'rc_dlr'     => [//日产
                                 'dlr_name' => '车巴巴东风日产数据服务有限公司',
                                 'dlr_code' => 'T9932',
                ],
                'pz1a_dlr'   => [//日产
                                 'dlr_name' => '车巴巴东风日产数据服务有限公司',
                                 'dlr_code' => 'T9932',
                ],
                'qc_dlr'     => [//启辰
                                 'dlr_name' => '车巴巴（启辰）东风日产数据服务有限公司',
                                 'dlr_code' => 'V0001',
                ],
                'card_quota' => [
                    // 商品id
                    Env::get('GW_COMMODITY_START', '5485')   => 'card_quota_start',//起步券
                    Env::get('GW_COMMODITY_EXTRA', '5487')   => 'card_quota_extra',//额外公里券
                    Env::get('PZ1A_COMMODITY_START', '5477') => 'card_quota_start',//起步券
                    Env::get('PZ1A_COMMODITY_EXTRA', '5478') => 'card_quota_extra',//额外公里券
                    Env::get('QC_COMMODITY_START', '5480')   => 'card_quota_start',//起步券
                    Env::get('QC_COMMODITY_EXTRA', '5481')   => 'card_quota_extra',//额外公里券
                ],
            ],
        ],
        'crm'                => [
            'client' => [
                'T9932'           => ['GWSC', 'CHEBABA', 'NISSAN', 'YGDLR', 'CSJTZH', 'GWSM', 'GWNET', 'GWAPP'],#
                'base_uri'        => Env::get('CRM_URL', 'http://feat-nissan201126--php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false,
                'mark_uuid'       => true,
            ],
        ],
        'pusher'             => [
            'client' => [#统一对接转发平台
                         'base_uri'        => Env::get('CRM_URL', 'http://feat-nissan201126--php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/'),
                         'timeout'         => 0,
                         'allow_redirects' => false,
                         'verify'          => false
            ],
        ],
        'quick_win'          => [
            'client' => [#统一对接转发平台
                         'base_uri' => Env::get('QUICK_WIN_URL', 'http://feat-nissan201126-uat--php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/proxy/quick-win/middle/'),

                         'timeout'         => 0,
                         'allow_redirects' => false,
                         'verify'          => false
            ],
        ],
        /**
         * 数营统一接口
         */
        'marketing_platform' => [
            'client'  => [
                'base_uri'        => Env::get('MARKETING_PLATFORM_URL', 'http://feat-nissan201126--php-base-site-api.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'cashier' => [
                'appid' => Env::get('APPID', '27467aba-88ca-4a5e-ba1d-7b6e130c5020'),
            ]
        ],
        'order'              => [
            'client'  => [
                'base_uri'        => Env::get('MARKETING_PLATFORM_URL', 'http://feat-nissan201126--php-base-site-api.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'cashier' => [
                'appid' => Env::get('APPID', '27467aba-88ca-4a5e-ba1d-7b6e130c5020'),
            ]
        ],
        'coupon'             => [
            'client'  => [
                'base_uri'        => Env::get('MARKETING_PLATFORM_URL', 'http://feat-nissan201126--php-base-site-api.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'cashier' => [
                'appid' => Env::get('APPID', '27467aba-88ca-4a5e-ba1d-7b6e130c5020'),
            ]
        ],
        'cbb'                => [
            'client'        => [
                'base_uri'        => Env::get('CBB_URL', 'http://feat-nissan201126--java-open-api.nissan-digital-marketing-platform.svc.cluster.local/'),
                'transfer'        => 'v1/common/relay',
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'x-feat'        => Env::get('CBB_FEAT', ''),
            'client_id'     => Env::get('CBB_CLIENT_ID', 'cbbmlEK52cOmyGEARFYgBk987JnU0Nn6Ytq'),
            'client_secret' => Env::get('CBB_CLIENT_SECRET', 'NTBmMWUxYzM3OGRmZTIzZjk3ZDJhYjk5MDZjODE4OTc='),
        ],
        'car_live'           => [
            'client'   => [
                'base_uri'        => Env::get('CAR_LIVE_URL', 'http://feat-nissan201126--php-wxmp-site-api.nissan-combined-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'name'     => Env::get('CAR_LIVE_NAME', 'net_small'),
            'password' => Env::get('CAR_LIVE_PASSWORD', 'dndc@2021#!!')
        ],
        'base_tool'          => [
            'client' => [
                'base_uri'        => Env::get('BASE_TOOL_URL', 'http://java-base-data-api.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
        ],
        'community'          => [
            'client'        => [
                'base_uri'        => Env::get('COMMUNITY_URL', 'http://feat-nissan-test20210915--php-api.nissan-community.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'client_id'     => Env::get('COMMUNITY_CLIENT_ID', 'root'),
            'client_secret' => Env::get('COMMUNITY_CLIENT_SECRET', 'Dndc2021!!'),
        ],
        'website'            => [
            'client' => [
                'base_uri'        => Env::get('API_WEBSITE_URL', 'http://feat-nissan201126--php-nissan-site-api.nissan-website.svc.cluster.local/platform/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
        ],
        'bdp'                => [
            'client' => [#大数据平台,通过third转发，同 CRM_URL 配置
                         //                         'base_uri'        => Env::get('CRM_URL', 'http://feat-nissan201126--php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/'),
                         'base_uri'        => Env::get('CRM_URL', 'http://feat-sit--php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/'),
                         'timeout'         => 0,
                         'allow_redirects' => false,
                         'verify'          => false
            ],
        ],
        'bdpai'              => [
            'client' => [
                'base_uri'    => Env::get('BDPAI_URL', 'https://bdp-test-gw.szlanyou.com/bdp/gateway'),// 综测没开权限，要用代理，，钟克峥说要用这个，Ip是VV改的，不知道为什么
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false,
                'user_key'    => Env::get('BDP_AI_USER_KEY', 'e87356490e9e41269007fdab4d1350fb'),
                'user_secret' => Env::get('BDP_AI_SECURET_USER_SECRET', '97fa512af6144bb6'),
                'bdp_env' => Env::get('BDP-ENV', ''),
            ]
        ],
        'third'              => [
            'client' => [
                'base_uri'        => Env::get('THIRD_URL', 'http://php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
        ],
        'pz_point'           => [
            'client' => [
                'base_uri'        => Env::get('PZ_POINT_URL', 'http://*************:37094/rest/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
        ],

        'e3s_dlr'    => [
            'client'  => [#统一对接转发平台
                          'base_uri'        => Env::get('QUICK_WIN_URL', 'http://feat-nissan201126--php-third-interface.nissan-digital-marketing-platform.svc.cluster.local/proxy/quick-win/middle/'),
                          'timeout'         => 0,
                          'allow_redirects' => false,
                          'verify'          => false
            ],
            'cashier' => [
                'appCode'    => Env::get('E3S_DLR_APP_CODE', 'nissan'),
                'clientid'   => Env::get('E3S_DLR_CLIENT_ID', 'nissanapp'),
                'uid'        => Env::get('E3S_DLR_U_ID', 'a8435c78cc2f4e3e877cf2985d5745c73536'),
                'uuid'       => Env::get('E3S_DLR_UU_ID', '54bc78cca21b4b92884f4553b91ff57b3536'),
                'appid'      => Env::get('E3S_DLR_APP_ID', '20211217921433304815632384'),
                'securetkey' => Env::get('E3S_DLR_SECURET_KEY', '9ac847a909fc5e14bfaf6dfb350146e7'),
            ]
        ],
        'ly_e3s_dlr' => [
            'client'  => [#统一对接转发平台
                          //'base_uri'        => Env::get('QUICK_WIN_URL', 'http://apigateway.cloudweb.cm-system.*************.nip.io:24413/rest/'),
                          'base_uri'        => Env::get('QUICK_WIN_URL', 'https://ms-share-service.dongfeng-nissan.com.cn/rest/'),
                          'timeout'         => 0,
                          'allow_redirects' => false,
                          'verify'          => false,

            ],
            'cashier' => [
                'appCode'    => Env::get('E3S_DLR_APP_CODE', 'nissan'),
                'clientid'   => Env::get('E3S_DLR_CLIENT_ID', 'nissanapp'),
                'uid'        => Env::get('E3S_DLR_U_ID', 'a8435c78cc2f4e3e877cf2985d5745c73536'),
                'uuid'       => Env::get('E3S_DLR_UU_ID', '54bc78cca21b4b92884f4553b91ff57b3536'),
                'appid'      => Env::get('LY_E3S_DLR_APP_ID', '20211120911563131849801728'),
                'securetkey' => Env::get('LY_E3S_DLR_SECURET_KEY', 'a7ef5c082648c1787897ff34916df61c'),
            ]
        ],

        // 启辰小程序
        'inter_fun'  => [
            'client'     => [
                'base_uri' => Env::get('INTER_FUN_URL', 'http://feat-dev--php-inter-fun.venucia-zhiqu.svc.cluster.local/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'app_secret' => Env::get('INTER_FUN_APP_SECRET', 'a98e0c812814c8e9ee18e3b8d67b68e1'),
        ],
        // 供应商发货
        'supplier'  => [
            'client'     => [
                'base_uri' => Env::get('SUPPLIER_URL', 'https://feat-sit-dndcfenge-java-mall-supplier-platform-client.dndc-mall-supplier-erp.dev.dndc.cloud/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'app_secret' => Env::get('SUPPLIER_APP_SECRET', ''),
        ],
        // pz1a小程序
        'pz1a' => [
            'client'     => [
                'base_uri'        => Env::get('PZ1A_URL', ''),
                'timeout'         => 10,
                'allow_redirects' => false,
                'verify'          => false,
            ],
            'prefix'     => [
                'a' => Env::get('PZ1A_URL_PREFIX', 'rest/business-service/'),
                'b' => Env::get('PZ1A_URL_PREFIX_B', 'rest/miniprogram-notification-middle-service/'),
                'c' => Env::get('PZ1A_URL_PREFIX_C', 'rest/activity-center-middle-service/'),
                'd' => Env::get('PZ1A_URL_PREFIX_D', 'rest/busiplat-se-salemake-service/'),
                'e' => Env::get('PZ1A_URL_PREFIX_E', 'rest/activity-center-third-service/'),
                'f' => Env::get('PZ1A_URL_PREFIX_F', 'rest/busiplat-nev-dc-service/'),
            ],
            'gversion'   => Env::get('PZ1A_G_VERSION', ''),
            'app_id'     => Env::get('PZ1A_APP_ID', '20211217921433304815632384'),
            'secure_key' => Env::get('PZ1A_SECURE_KEY', '9ac847a909fc5e14bfaf6dfb350146e7'),
        ],
        'ariya_api'  => [
            'client'  => [
                'base_uri' => Env::get('ARIYA_API_URL', 'https://ariya-api.dongfeng-nissan.com.cn/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false

            ],
            'cashier' => [
                'appCode'      => 'venucia',
                'clientid'     => 'venuciaminiapp',
                'content-type' => 'application/json',
                'noncestr'     => '3805e899a84747a4a56aeb997fa1c90b',
                'range'        => '2',
                'timestamp'    => time() . '888',
            ]
        ],
        'ota_api'    => [
            'client'  => [
                'base_uri' => Env::get('OTA_API_URL', 'https://nvitapp.venucia.com/gw/gw'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'cashier' => [
                'api'          => 'ly.iov.third.mall.ni.updateNiGoods',
                'appid'        => Env::get('OTA_APPID', 'iov491da385eee943319d0aa671d1eb87ba'),
                'appkey'       => Env::get('OTA_APPKEY', 'e6aa791397bc43beaca9b5e3978275dd'),
                'content-type' => 'application/json',
                'noncestr'     => '3805e899a84747a4a56aeb997fa1c90b',
                'timestamp'    => time() . '888',
            ]
        ],

        //移动积分
        'yd_pay'     => [
            'client'         => [
                'base_uri'        => Env::get('YD_PAY_URL', 'https://jifenjiawechatapi.zmmv.cn/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false,
            ],
            'auth_url'       => Env::get('YD_PAY_AUTH_URL', 'https://m.changyoyo.com/event/2019/blankPage/index.html'), // 授权地址
            'sign_key'       => Env::get('YD_PAY_SIGN_KEY', '123456'), // 密钥
            'partner_id'     => Env::get('YD_PAY_PARTNER_ID', 'S9999075'), // 商户号
            'channel_source' => Env::get('YD_PAY_CHANNEL_SOURCE', '02000000'), // 渠道号
            'auth_token'     => Env::get('YD_PAY_AUTH_TOKEN', '123456'), // token
            'tax_rate'       => Env::get('YD_PAY_TAX_RATE', ''), // 商品税率
        ],

        'hao_wan'        => [
            'client'     => [
                'base_uri' => Env::get('HAO_WAN_URL', 'http://open.api.24haowan.com'),
            ],
            'game_id'    => Env::get('HAO_WAN_GAME_ID', '*********'),
            'appId'      => Env::get('HAO_WAN_APPID', 'e9a9dbe058b911ecaaec295031c85afd'),
            'app_secret' => Env::get('HAO_WAN_APP_SECRET', '9bbdb1fd163d846f901e676e172cc336')
        ],
        'payment_system' => [
            'client'      => [
                // url 统一经 数营 THIRD 转发到 LY
                'base_uri'        => Env::get('LY_PAYMENT_SYSTEM_THIRD', 'https://pz1a-uat.dongfeng-nissan.com.cn/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false,
            ],
            'appId'       => Env::get('LY_PAYMENT_APP_ID', '1048'),
            'app_secret'  => Env::get('LY_PAYMENT_APP_SECRET', 'bJP1GY1B3Tdy7bAUUSwso9LYXKyIcBWG'),
            'private_key' => Env::get('LY_PAYMENT_PRIVATE_KEY', 'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCRga/PYK1U167rqlOr8dfRKlg2crNY6CjjXT7B+qrbPdsc/6xm/N7ectDnXBpWJDpeuTn1qLEpRF2fZpCW/icK52GjzHHEZiHx4yhz58gQfSiFQyVq+roBdsN7Emfum5SEFR/mPMKX7vHH8huiBlKCwal6JngJk08EK2n4gLo7TyLdd0MoYcZULkyjNBRQLeGtWssU7At61ZwlfDKJMpfqjrNXyXk4oi8ieWyYY6cpydLd/iqcciJTs/M4tGaFtzBNg6iFrrzkoU92y/MOHEA71BzF2SFe7DGdnNB4jrfw5a0flSb2yHwb3bkfFxgbA4HngW/Dr6M/RjjLiwtIhJLFAgMBAAECggEAafQPe2igjslHnF+48dn3F15IQc2f/zt+mqFJk6LeSyttpF4gwhWcjEr7B52vp85VmqgyJkoiRzR7IBllbC3BbFU0ajvzE9pzGVoaGNxMeIVKvk84rf/I9kvu6G1stPkhWlSvl83ivRPHh5sqt/72kjRHHqVof0B/J0AduFC0S36lO77o7x0AxEDiz9d5AxFhy1PC7wBsJs1cdtL9DVwO5ee3N2WoqhjLgZEU/oavXNsNrgp9ZULYU6nuPFMPF/wnxeGQ2ycwrcW0Cp7x2mk9Woj8T4AuyYFErwmxi1xo6AQQX8zLfZWBk+qAjD/7kGTXVUFrruXJ9HpL1loRCKNPeQKBgQDlzNWvi8JCzSRBwDe4CUNB+ByuPdy5tyKKavPjghAw5OWSOFWfDiecrzbxhGh0dAOG36dmozXcILIupkWUoPrmxMAeHN5UOVlKdjMhNQwN2CyjAUB46HOFKIWyGpCaf+KERM9NTUwWvgw5eHn7yLH84tIEnyDB8wiKlv0jpytrCwKBgQCiGJSXUufkFHqaeiGz7a7T3hxsPWfFrr+t9eMbc+AmkwqjMWLr/N+g+Z/DxI7AfmEqDWw3JgxiDykcZYlgdoXDxy1E4gcjLGaDBWitUd0eRiMvWbEeQYkAe9YyDIHy+Xz+aYNpGqiipEuiAttsraPM3lufHIZr06MZIsQ3oZUbbwKBgFMVpyDMKBVc9EHtjHe2qaGdYklBRp/C7fMbzkgwi0gSSkp+ob5bMMk+tF/IHrgUBf5quKysT9nh3GpvZXLibTSoaEm66EuNXJJGgB/+fPg4c5MP7c6JRNMdHpGRdzzVBgIKmuT5EIycc/EHdRsqqeej3okbO43EIA9zndyZIWKXAoGAWBz2gP+/MDt1ng1vzqgQVfkyCCClDU99wKmeB6lImo9pw+YvuMpgIdgv4bIySy8n0D01qxDSwGcvWNFVCQvEkPTvhbc8XAFhxC0nhreudCt6Kt4dqq3DNHEcsV4dMaAvMvjWR0cHjkmzF2FoN754AQCKW4adB3Ym/Z3pfbgnHp8CgYA+lt/oqNhAsuc3Pnz5NRIG5IpnyFoDaAilwD1p94qldkdIlqwRt8xw0+wE9lAao/tkKVsZWiqllpz/H+C6vVP06rNodLnOluNoN17PtHeQit95aMYMieWDo66F8vQehdaq047PQ9RnOOuD0Taw/f1x/YenJnKK55aaxzbItGWuKA=='),
            'prefix'      => Env::get('LY_PAYMENT_SYSTEM_PREFIX', 'ly/busicen/pay/base/')

        ],

        'tag'           => [
            'client'  => [
                'base_uri'        => Env::get('TAG_URL', 'http://apigateway.pz1a-middle-platform.svc:8080/rest/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'cashier' => [
                'appid'        => Env::get('TAG_APPID', '20211217921436552385527808'),
                'securetkey'   => Env::get('TAG_SECURETKEY', '2f7ad6d34928458959a3f93decff5565'),
                'contentAppid' => Env::get('TAG_CONTENT_APPID', 'bf7a8f5d5544489290adfdc0811001f9'),
                'key'          => Env::get('TAG_KEY', 'bb4d8e9493c64d658c03c22e32f35789'),
                'content-type' => 'application/json',
                'timestamp'    => time() . '888',
                'model_code'   => '9b16d27ca2'
            ]
        ],
        'inter_fun_app' => [
            'client' => [
                'base_uri' => Env::get('INTER_FUN_APP_URL', 'http://feat-dev--php-inter-fun.venucia-zhiqu.svc.cluster.local/'),
            ],
            "AppId"  => Env::get('INTER_FUN_APP_APP_ID', 'iov24cfa3d34920d701e736b0fa20c70462'),
            'AppKey' => Env::get('INTER_FUN_APP_APP_KEY', '8c2f3c92e875a872c71f431e15673ada')
        ],

        'wang_dian'          => [
            'client'  => [
                'base_uri'        => Env::get('WANG_DIAN_URL', 'https://gimcszjh.com/wecomact/wdt/proxy/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'cashier' => [
                'appId'     => Env::get('WANG_DIAN_APPID', 'wdt01'),
                'secret'    => Env::get('WANG_DIAN_SECURET', 'e6w3sVYv58lCuMzG'),
                'shop_no'   => Env::get('WANG_DIAN_SHOP_NO', 'shuzi2-test'),
                'timestamp' => time(),
            ]
        ],

        'idm' => [
            'client'  => [
                'base_uri'        => Env::get('OAUTH_URL_TOKEN', 'https://dev-dealer.dongfeng-nissan.com.cn/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'cashier' => [
                'appId'  => Env::get("OAUTH_APP_ID",'12e02b7123054f1b'),
                'secret' => Env::get("OAUTH_APP_SECRET",'a23b2e8c7d234ec2bb1f92d18b194759'),
            ]
        ],

        // 京东云仓
        'jd_cloud_warehouse' => [
            'client'       => [
                'base_uri'        => Env::get('JD_CLOUD_WAREHOUSE_URL', 'https://uat-api.jdl.com/'),
                'timeout'         => 0,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'app_code'     => Env::get('JD_CLOUD_WAREHOUSE_APP_CODE', '2ad2354a31f44605900d71f3dacee5ae'),
            'app_key'      => Env::get('JD_CLOUD_WAREHOUSE_APP_KEY', ''),
            'app_secret'   => Env::get('JD_CLOUD_WAREHOUSE_APP_SECRET', ''),
            'app_pin'      => Env::get('JD_CLOUD_WAREHOUSE_APP_PIN', ''),
            'owner_no'     => Env::get('JD_CLOUD_WAREHOUSE_OWNER_NO', ''), // 商家编码
            'shop_no'      => Env::get('JD_CLOUD_WAREHOUSE_SHOP_NO', ''), // 店铺编码
            'warehouse_no' => Env::get('JD_CLOUD_WAREHOUSE_WAREHOUSE_NO', ''), // 仓库编号
            'access_token' => Env::get('JD_CLOUD_WAREHOUSE_ACCESS_TOKEN', ''),
            'algorithm'    => Env::get('JD_CLOUD_WAREHOUSE_ALGORITHM', 'md5-salt')
        ],

        //
        'e3sp_refactor' => [
            'client'  => [
                'base_uri' => Env::get('E3SP_REFACTOR_URL', 'https://ariya-api-sit.dongfeng-nissan.com.cn/'),
                'timeout'         => 10,
                'allow_redirects' => false,
                'verify'          => false
            ],
            'prefix' => Env::get('E3SP_REFACTOR_PREFIX', 'java-refactor-gateway-service/'),
            'prefix_2' => Env::get('E3SP_REFACTOR_PREFIX_2', 'e3sp-refactor/original/'),//20250407之后用这个，新的都会走这个路由
            'gversion'   => Env::get('E3SP_REFACTOR_G_VERSION',''),
        ],

        'invoice_hx' => [
            'client'  => [
                'base_uri' => Env::get('INVOICE_HX_URL', 'http://172.24.175.68:8094/'),
                'timeout'         => 10,
                'allow_redirects' => false,
                'verify'          => false
            ],
        ],
    ],

    'withdraw_url'   => Env::get('WITHDRAW_URL', 'https://feat-cloudshop-dndcfenge-node-base-site-admin.nissan-wechat-alliance-platform.dev.dndc.cloud/dealer/app-management/'),
    'is_WeiXin'      => Env::get('IS_WEIXIN', true),
    'IMG_DOMAIN_URL' => Env::get('env_img_domain_url', 'https://wxstoreimg.dongfeng-nissan.com.cn/'),

    'DOMAIN_URL'       => Env::get('env_domain_url', 'https://wxstore.dongfeng-nissan.com.cn/'),
    'DOMAIN_INNER_URL' => Env::get('env_domain_url_inner', 'https://wxstore.dongfeng-nissan.com.cn/'),
    'NODE_WEBSITE_URL' => Env::get('env_node_website_url', 'https://www.dongfeng-nissan.com.cn/'),

    #配置可以进行behavior行为操作的数组
    'behavior_in'      => ['GrowthValueBehavior', 'PushInfoBehavior'],
    'xi_kf_url'        => Env::get('XI_KF_URL', 'https://xiqd-test.dongfeng-nissan.com.cn/static/html-hysc/'),
    'xi_kf_qc_url'     => Env::get('XI_KF_QC_URL', 'https://xiqd-test.dongfeng-nissan.com.cn/static/html-qichen/'),
    'sm_appid'         => Env::get('SM_APPID', 'wxa184df7a4a6860c8'),
    'pz1a_sm_appid'    => Env::get('SM_APPID_PZ1A', 'wx8814d55e0a8a9722'),
    'app_appid'        => Env::get('APP_APPID', 'wx590f7f93afe62fc2'),//APP appid
    'pz1a_app_appid'   => Env::get('APP_APPID_PZ1A', 'wx8814d55e0a8a9722'),//PZ1A小程序 appid

    #
    'qr_code'          => [
        'rc' => [
            'detail' => 'package_mall/pages/mall_detail/index',
            'ping'   => 'package_mall/pages/mall_ping/index',
        ],
        'qc' => [
            'detail' => 'package_merge_mall/pages/mall_detail/index',
            'ping'   => 'package_merge_mall/pages/mall_ping/index',
        ],
        'pz' => [
            'detail' => 'packageShop/pages/mall_detail/index',
            'ping'   => 'packageShop/pages/mall_ping/index',
        ],

    ],


    # 成长体系
    'growth_code'      => [
        'address'    => 4,#收货地址
        'like'       => 40, #点赞
        'collection' => 41, #收藏
        'order'      => 21, #订单
        'comment'    => 39, #点评
        'cart'       => 38, #点评
    ],

    #one app push info template
    'one_app_template' => [
        'card_get'     => '20210722',#卡券领取成功#
        'card_consume' => '20210723',#卡券核销
        'card_due'     => '20210724',#卡券到期前3天
        'card_expire'  => '20210725',#卡券到期

        'order_success' => '20210726',#订单购买支付成功

        'order_ship'  => '20210727',#订单已发货
        'order_close' => '20210730',#交易关闭

        'afs_change' => '20210728',#换货中
        'afs_return' => '20210729',#退货中

        //        'order_expire' => '20220502',#核销有效期(30天)商城订单过期提醒（在订单即将过期前15天及7天，推送消息）
        //        'down_price'   => '20220503',#购物车中的商品发生降价10%，推送提醒
    ],

    'push_limit' => [
        1 => ['card_get', 'card_consume', 'card_due', 'card_expire', 'order_success', 'order_ship', 'order_close', 'afs_change', 'afs_return', 'order_expire', 'down_price'],#日产全发
        2 => ['order_expire', 'down_price'], # 启辰发最后两个
        3 => [], # pz1a 先不发
    ],

    'push_code' => [
        1 => 'nissan',
        2 => 'venucia',
        3 => '',
    ],

    'one_app_redirect_url' => [
        'down_price_url'   => Env::get('DOWN_PRICE_URL', 'https://h5zh.venucia.com/oneapp/shoppingTrolley'),
        'order_expire_url' => Env::get('ORDER_EXPIRE_URL', 'https://h5zh.venucia.com/oneapp/orderdetail/'),
    ],

    'gsecurity' => Env::get('G_SECURITY', 'aes'),

    'gsecurity_TMP' => Env::get('G_SECURITY', 'rsa'),
    'CDP'           => [
        "projectId"    => Env::get('CDP_PROJECT_ID', 'tindex_X8kk_k0xi_project_j5c_jTX_y'),
        "token"        => Env::get('CDP_TOKEN', 'daf7e1db776e03d54415d89eecf05b11'),
        "app_host"     => Env::get('CDP_APP_HOST', 'http://**************:18000'),
        "gateway_host" => Env::get('CDP_GATEWAY_HOST', 'http://**************:18000'),

    ],


    'xunsearch' => [
        'url' => Env::get('xunsearch_url', 'http://*************') . '/xunsearch/Searchnew/index',
    ],

    'special_fix' => [
        ['id' => 1, 'title' => '老友惠保养套餐', 'routes' => '/oneapp/friendsIntroductionPage', 'sm_routes' => 'package_mall/pages/upkeep_buy/index'],
        ['id' => 2, 'title' => '心悦套餐', 'routes' => '/oneapp/happyMaintain', 'sm_routes' => 'package_mall/pages/upkeep_happy_heart_1/index'],
        ['id' => 3, 'title' => 'CCS流量&养修套餐', 'routes' => '/oneapp/CarLinkSuit', 'sm_routes' => 'package_mall/pages/service_package_1/index'],
        ['id' => 4, 'title' => '五年双保', 'routes' => '/oneapp/doubleGuarantee', 'sm_routes' => 'package_mall/pages/upkeep_fiveyears_double_1/index'],
        ['id' => 5, 'title' => '五月活动', 'routes' => '/oneapp/summer', 'sm_routes' => ''],
        ['id' => 6, 'title' => '会员周', 'routes' => '/oneapp/SpareWeek', 'sm_routes' => ''],
        ['id' => 7, 'title' => '备件618活动', 'routes' => '/oneapp/Spare618', 'sm_routes' => ''],
        ['id' => 8, 'title' => '备件双月活动', 'routes' => '/nissanactivity/membermonth9', 'sm_routes' => ''],
        ['id' => 9, 'title' => '随心配活动', 'routes' => '/oneapp/discountmatch', 'sm_routes' => 'package_mall/pages/activity_follow_heart_buy/index'],
    ],

    'ocr_ali_yun' => Env::get('OCR_ALI_YUN', '4a0293607dbe439181b01f5b336e110e'),
    'order_to_ly' => [

        'index'  => Env::get('TO_E3S_INDEX', 'https://ariya-api-sit.dongfeng-nissan.com.cn/e3sp-refactor/refactor/e3s-egt/postdata/DNDC_ONLINESHOP/DNDC_RECEIVE_MAIN'),
        'detail' => Env::get('TO_E3S_DETAIL', 'https://ariya-api-sit.dongfeng-nissan.com.cn/e3sp-refactor/refactor/e3s-egt/postdata/DNDC_ONLINESHOP/DNDC_RECEIVE_DETAIL'),
        'reback' => Env::get('TO_E3S_REBACK', 'https://ariya-api-sit.dongfeng-nissan.com.cn/e3sp-refactor/refactor/e3s-egt/postdata/DNDC_ONLINESHOP/DNDC_RECEIVE_STATUS'),
        'refund' => Env::get('TO_E3S_REBACK', 'https://ariya-api-sit.dongfeng-nissan.com.cn/e3sp-refactor/refactor/e3s-egt/postdata/DNDC_ONLINESHOP/DNDC_RECEIVE_STATUS'),

    ],

    'bdp_env'                => Env::get('BDP_ENV', ''),
    'win_card'               => [
        'receive_scene_id' => Env::get('RECEIVE_SCENE_ID', 57), // 领取场景id
        'door_scene_id'    => Env::get('DOOR_SCENE_ID', 18), // 领取场景id
    ],


    #优惠券跳转路径，todo
    'card_redirect_url'      => [
        '1' => [#日产
                'coupon_list' => [
                    'app'  => 'oneapp/classificationGoods/GoodsList?card_id=',
                    'mini' => 'package_mall/pages/goods_list/index?cardId=',
                ],
                'order_info'  => [
                    'app'  => '/oneapp/orderdetail/',
                    'mini' => 'package_mall/pages/order_detail/index?orderId=',
                ],
        ],
        '2' => [#启辰
                'coupon_list' => [
                    'app'  => 'oneapp/classificationGoods/GoodsList?card_id=',
                    'mini' => 'package_merge_mall/pages/goods_list/index?cardId=',
                ],
                'order_info'  => [
                    'app'  => '/oneapp/orderdetail/',
                    'mini' => 'package_merge_mall/pages/order_detail/index?orderId=',
                ],
        ],
        '3' => [#ariya
                'coupon_list' => [
                    'app'  => 'oneapp/classificationGoods/GoodsList?card_id=',
                    'mini' => 'packageShop/pages/goods_list/index?cardId=',
                ],
                'order_info'  => [
                    'app'  => '/oneapp/orderdetail/',
                    'mini' => 'packageShop/pages/order_detail/index?orderId=',
                ],
        ],
    ],

    #pz积分通知
    'pz_point_call_back_url' => Env::get('LY_PZ_POINT_BACK_URL', 'https://feat-nissan201126-dndcfenge-php-wxmp-dealers.nissan-aftermarket.dev.dndc.cloud/net-small/pay_plat/pointCallBack'),

    #爱车养护id配置
    'love_car'               => [
        'GWSM'  => Env::get('LOVE_CAR_GWSM', 1),
        'GWAPP' => Env::get('LOVE_CAR_GWAPP', 44),
    ],

    'maintain_rules_card_ids' => Env::get('MT_RULES_CARD_IDS', '1507,1508'),
    //取送车商品id--特殊配置
    'pick_up_order_goods'     => Env::get('PICK_UP_ORDER_GOODS', '39737'),
    'staff_url'               => Env::get('STAFF_URL', 'http://**************:8089/WebServiceReconmandBuyCar.asmx/SingInCheck'),//生产http://************:8090/WebServiceReconmandBuyCar.asmx/SingInCheck

    // 订阅消息模板
    'template_id'             => [
        'miniprogram_state' => Env::get('pz1a_miniprogram_state', 'formal'),
        'pz1a'              => [
            0 => Env::get('PZ1A_TEMPLATE_ID_0', 'lJtkSiND0qx8u-Tdac8H4DoKKpaqHXywcYCF9NphgKk'), // 商家发货提醒（直邮类）
            1 => Env::get('PZ1A_TEMPLATE_ID_1', 'zXaOkXdH8WV9b5n-xLSADTZtN73_r2ka2lvRo5WJL9o'), // 取货通知（到店类）
            2 => Env::get('PZ1A_TEMPLATE_ID_2', 'CeTizcB8tsgLJ20IIO_oR3xJQw5lgvWS6AVwmKvH7mo'), // 商品退款成功提醒
        ],
    ],


    'sign_checker' => [
        'open' => Env::get('SIGN_CHECKER_OPEN', false),
        'key' => 'FbDOCNQMwawE1MKALGoCGyoEcBMBGAZgBZgYw4pcg'
    ],

    'order_code_prefix' => Env::get('ORDER_CODE_PREFIX',''), // 不同环境的订单前缀
    'shop_title' => Env::get('SHOP_TITLE', '售后商城平台'),
    'gong_hui'=>[
        'sp_id'=>Env::get('GONG_HUI_SP_ID','342'),
        'time'=>Env::get('GONG_HUI_TIME','2024-06-26 13:50:55,2024-06-30 23:51:03'),
        'goods_id'=>Env::get('GONG_HUI_GOODS_ID','39903,39904,39905,39906'),
    ],

    // 发票回调域名
    'invoice_callback_url' => Env::get('INVOICE_CALLBACK_URL', 'https://feat-sit-dndcfenge-php-wxmp-dealers.nissan-aftermarket.dev.dndc.cloud/'),
];
