<?php
/*测试环境定时任务*/
$crond = [
    'crond' => [
        /***************************每分钟执行****************************/
        ['00:01','app\admin_v2\command\db\DbLog::doIt'], // 每天执行一次
        ['00:02','app\admin_v2\command\db\DbJobLog::doIt'], // 每天执行一次
        ['00:03','app\admin_v2\command\db\IRequestLog::doIt'], // 每天执行一次
        ['00:04','app\admin_v2\command\db\DbCardLog::doIt'], // 每天执行一次


        ['*/2','app\admin_v2\command\admin\RoleSync::doIt'], //角色同步    每2分钟执行一次
        ['*/2','app\admin_v2\command\admin\AccountSync::doIt'], //账号同步    每2分钟执行一次
        //省广物流更新
        ['*/5', 'app\net_small\command\OrderWaybill::doIt'],

        ['00:38', 'app\net_small\command\ExpireRefund::doIt'], #0点38分订单自动退款
        ['01:38', 'app\net_small\command\ExpireRefund::doIt'], #1点38分订单自动退款补漏


        ['*/1', 'app\net_small\command\Mailer::doIt'], # 发送邮件
        ['*/1', 'app\admin_v2\command\base\RefreshCheap::doIt'], #优惠套装

        ['*/1', 'app\admin_v2\command\base\RefreshFullDiscount::doIt'], #满优惠

        ['*/1', 'app\admin_v2\command\base\RefreshLimitDiscount::doIt'], #限时优惠

        ['*/1', 'app\admin_v2\command\base\RefreshFightGroup::doIt'], #多人拼团

        ['*/1', 'app\admin_v2\command\base\RefreshNDiscount::doIt'], #N件N折

        ['*/1', 'app\admin_v2\command\base\RefreshPreSale::doIt'], #预售活动

        ['*/1', 'app\admin_v2\command\base\RefreshDraw::doIt'], #抽奖活动

        ['*/1', 'app\admin_v2\command\base\RefreshSeckill::doIt'], #秒杀

        ['*/1', 'app\admin_v2\command\base\RefreshGift::doIt'], #买赠

        ['*/1', 'app\admin_v2\command\base\RefreshCrowdfund::doIt'], #众筹

        ['*/1', 'app\admin_v2\command\commodity\RefreshCommodityAllType::doIt'], #1.数据检查 2.商品数据更新

        ['*/1', 'app\net_small\command\OrderGrowthValue::doIt'], #订单成长值

        ['*/3', 'app\net_small\command\CommentGrowthValue::doIt'], #评论成长值

        ['*/1', 'app\net_small\command\CartGrowthValue::doIt'], #购物车成长值

        ['*/1', 'app\net_small\command\PushInfoCron::doIt'], #订单,售后订单触发推送

        ['*/1', 'app\net_small\command\NoticeRefund::doIt'], #排除退款失败实际成功的订单

        ['*/1', 'app\net_small\command\OrderBdp::doIt'], #订单数据vin推送到bdp



        /***************************间隔几分钟执行****************************/
        ['*/12', 'app\admin_v2\command\commodity\RefreshCommodity::doIt'], #商品数据

//        ['*/7', 'app\admin_v2\command\card\RemoveNotSucceedCard::doIt'], #间隔几分钟过期无效的优惠券

        /***************************每小时某分钟****************************/
        //        ['*/59', 'app\net_small\command\RefreshCbbToken::doIt'], #每小时第59分钟

        /***************************固定时间执行****************************/
        ['00:01', 'app\admin_v2\command\card\ExpireCard::doIt'], #每天过期一次优惠券数据
        ['00:12', 'app\common\command\RmJobsLog::doIt'], #每天删除一次队列执行日志

//        ['09:00', 'app\net_small\command\OrderRemind::doIt'], // 订单到期提醒

        //        ['08:57', 'app\net_small\command\PushInfoCard::doIt'], #定时任务触发推送卡券相关信息

        //        ['*/17', 'app\net_small\command\OrderNotice::doIt'], #待核销订单 剩余 15天/7 天提醒

        ['*/1', 'app\fuli_api\command\Commodity::doIt'], #京东商品自动上下架

        //        ['00:02', 'app\net_small\command\PushDlrLyPayInfo::doIt'], # 定时更新经销商联友支付信息

        ['*/1', 'app\net_small\command\CardReceiveRecord::doIt'], #速赢中心卡券领取/核销

//        ['*/1', 'app\net_small\command\PushCommodityDepreciate::doIt'], # 商品降价通知


        // 现金支付结算
        ['*/5', 'app\net_small\command\OrderSettle::doIt'],


        //更新售出数量
//        ['02:00', 'app\net_small\command\Order::doIt'], #定时任务触发推送卡券相关信息
        ['*/1', 'app\net_small\command\Order::doIt'], #2023.12.18 修改成1小时更新售出数量

        //更新库存回退
        ['*/1', 'app\net_small\command\Order::backstock'],

        // 补偿结算回调消息
        ['*/5', 'app\net_small\command\OrderSettleInfo::doIt'],

        ['06:30', 'app\net_small\command\SynLyPayException::doIt'], #异常信息重试
        ['08:30', 'app\net_small\command\SynLyPayData::doIt'], #8点30分自动同步财务报表
        //'*-d H:i',  /*每月-某天 某时:某分*/
        ['03:00', 'app\net_small\command\SynLyPayData::supplierOrder'], #同步上月交易数据


        //        ['*/5', 'app\task\controller\Order::get_jd_wl'],
        //app\net_small\command\Commodity

        // 库存预警通知
        ['01:00', 'app\net_small\command\JdCommodity::doIt'], // 同步京东商品
        ['01:21','app\admin_v2\command\early_warning\Price::doIt'], //价格预警 生成excel
        ['01:41','app\admin_v2\command\early_warning\Material::doIt'], //素材预警 生成excel
        ['*:01','app\admin_v2\command\early_warning\Price::sendMailer'], //价格预警    每小时执行一次
        ['*:01','app\admin_v2\command\early_warning\Material::sendMailer'], //素材预警    每小时执行一次
        ['*:01','app\admin_v2\command\early_warning\Stock::doIt'], //库存预警    每小时执行一次

        //通知活动中心
        ['*/10', 'app\net_small\command\OrderActivityNotify::doIt'], #商城已下单但未付款
        ['*/10', 'app\net_small\command\AddShoppingCartActivityNotify::doIt'], #商城已加入购物车但未下单
    ]
];


return $crond['crond'];
