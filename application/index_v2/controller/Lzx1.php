<?php /** @noinspection ForgottenDebugOutputInspection */

/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2017/6/8
 * Time: 下午5:31
 */

namespace app\index_v2\controller;

use api\jd_sdk\JdOrderN;
use api\lianyou\Ccs;
use api\wechat\AccessToken;
use api\wechat\Carer;
use api\wechat\CarerNew;
use api\wechat\Clue;
use api\wechat\CustomMessage;
use api\wechat\JsSdk;
use api\wechat\Card;
use api\wechat\Mail;
use api\wechat\Menu;
use api\wechat\QrCode;
use api\wechat\Ticket;
use api\wechat\UserManage;
use app\common\fuli_service\UserOrder;
use app\common\model\act\AcRenewalQrc;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuComponentAuth;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderErrList;
use app\common\model\bu\BuOrderJd;
use app\common\model\bu\BuOrderMoreCardPoint;
use app\common\model\bu\BuOrderPoint;
use app\common\model\bu\BuOrderRefund;
use app\common\model\bu\BuOrderSettlement;
use app\common\model\bu\BuPhoneBlacklist;
use app\common\model\bu\BuQyDepartmentAll;
use app\common\model\bu\BuQyUserAll;
use app\common\model\bu\BuTplMsg;
use app\common\model\bu\BuUserBrowsing;
use app\common\model\bu\BuVinListHhr;
use app\common\model\bu\BuWxContrastOpenid;
use app\common\model\db\DbActivityCenterLog;
use app\common\model\db\DbAdvertisement;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbAfterSalePlatformAddresses;
use app\common\model\db\DbArea;
use app\common\model\db\DbCard;
use app\common\model\db\DbCarSeries;
use app\common\model\db\DbCarUser;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityJdSku;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbConsumeRuleRecord;
use app\common\model\db\DbDlr;
use app\common\model\db\DbJdGoodsImage;
use app\common\model\db\DbJdSkuInfo;
use app\common\model\db\DbLog;
use app\common\model\db\DbLySettleOrder;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSpecialSm;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\model\db\DbUserDrawRecord;
use app\common\model\db\DbVinRule;
use app\common\model\e3s\E3sMaintenanceProduct;
use app\common\model\e3s\E3sMaintenanceProductCarSeries;
use app\common\model\e3s\E3sPackage;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\e3s\E3sSpecificRelationPart;
use app\common\model\jd\JdMessage;
use app\common\model\jd\JdOrder;
use app\common\model\jd\JdOrderGoods;
use app\common\model\pss\PssInventoryInout;
use app\common\model\sys\SysLogModule;
use app\common\model\zz\ZzLog;
use app\common\net_service\Common;
use app\common\net_service\Inventory;
use app\common\net_service\MaintainService;
use app\common\net_service\NetOrder;
use app\common\net_service\PaymentSystem;
use app\common\port\connectors\Idm;
use app\common\port\connectors\MarketBase as MB;
use app\common\port\connectors\Member;
use app\common\port\connectors\Payment;
use app\common\port\connectors\PzPoint;
use app\common\port\connectors\QuickWin;
use app\common\service\CarIndexService;
use app\common\service\CommodityService;
use app\net_small\command\OrderSettle;
use app\common\net_service\NetGoods;
use app\common\net_service\NetGoodsOptimized;
use think\Cache;
use think\cache\driver\Redis;
use think\Config;
use think\Controller;
use think\Db;
use think\Exception;
use think\Env;
use think\Hook;
use think\Log;
use think\Queue;
use think\Model;
use tool\Curl;
use tool\Logger;
use tool\OssUploadFile;
use function GuzzleHttp\Psr7\build_query;

class Lzx1 extends Controller
{
    public function __construct()
    {
        parent::__construct();
        $user = input('user_token');
        $data = input('user_data','');
        if($user!='lzx123' || $data<>date('md')){
            die('未经授权，不允许访问');
        }
    }

    function getCombinations($array, $length) {
        $result = [];
        $comb = array_fill(0, $length, 0);
        $count = count($array);
        $i = 0;

        while ($i >= 0) {
            if ($comb[$i] < $count) {
                $i++;
                if ($i < $length) {
                    $comb[$i] = $comb[$i - 1] + 1;
                } else {
                    $resultSet = [];
                    foreach ($comb as $index) {
                        $resultSet[] = $array[$index - 1];
                    }
                    $result[] = $resultSet;
                    $i--;
                    $comb[$i]++;
                }
            } else {
                $i--;
                if ($i >= 0) {
                    $comb[$i]++;
                }
            }
        }

        return $result;
    }

    function shuffleAnswers($answers) {
        $shuffled = $answers;
        shuffle($shuffled);
        return $shuffled;
    }

    function generateOutput($combinations, $questions, $answers) {
        $output = [];
        foreach ($combinations as $combination) {
            $row = [];
            $row['encodings'] = $combination;
            $row['questions'] = array_map(function($index) use ($questions) {
                return $questions[$index - 1];
            }, $combination);

            $row['originalAnswers'] = array_map(function($index) use ($answers) {
                return $answers[$index - 1];
            }, $combination);

            $row['shuffledAnswers'] = $this->shuffleAnswers($row['originalAnswers']);

            $order = '';
            foreach ($row['shuffledAnswers'] as $shuffledAnswer) {
                $order .= array_search($shuffledAnswer, $row['originalAnswers']) + 1;
            }

            $row['order'] = $order;
            $output[] = $row;
        }
        return $output;
    }
    public function test_sj(){
        $encodings = range(1, 16);
        $questions = [
            'Question1', 'Question2', 'Question3', 'Question4',
            'Question5', 'Question6', 'Question7', 'Question8',
            'Question9', 'Question10', 'Question11', 'Question12',
            'Question13', 'Question14', 'Question15', 'Question16'
        ];
        $answers = [
            'Answer1', 'Answer2', 'Answer3', 'Answer4',
            'Answer5', 'Answer6', 'Answer7', 'Answer8',
            'Answer9', 'Answer10', 'Answer11', 'Answer12',
            'Answer13', 'Answer14', 'Answer15', 'Answer16'
        ];

        $combinations = $this->getCombinations($encodings, 3);
        $output = $this->generateOutput($combinations, $questions, $answers);

        header('Content-Type: text/plain');
        foreach ($output as $row) {
            echo implode("\t", $row['encodings']) . "\t";
            echo implode("\t", $row['questions']) . "\t";
            echo implode("\t", $row['shuffledAnswers']) . "\t";
            echo $row['order'] . "\n";
        }
    }

    public function test_h3(){
        $num_arr = ['α0101111','α0101112','α0101113','α0101121','α0101122','α0101123','α0101124','α0101125','α0101131','α0101132','α0101141','α0101142','α0101143','α0101144','α0101211','α0101212'];
        $question_arr = ['导电体','绝缘体','价电子','本征半导体','载流子','电子','空穴','电子空穴对','N型半导体','P型半导体','空间电荷区','耗尽层','电位壁垒','阻挡层','二极管','二极管的结构和分类'];
        $answer_arr = ['a1','a2','a3','a4','a5','a6','a7','a8','a9','a10','a11','a12','a13','a14','a15','a16'];

        $combinations = [];

// Generate all unique combinations of 3 elements from num_arr and question_arr
        for ($i = 0; $i < count($num_arr) - 2; $i++) {
            for ($j = $i + 1; $j < count($num_arr) - 1; $j++) {
                for ($k = $j + 1; $k < count($num_arr); $k++) {
                    $selected_nums = [$num_arr[$i], $num_arr[$j], $num_arr[$k]];
                    $selected_questions = [$question_arr[$i], $question_arr[$j], $question_arr[$k]];

                    // Shuffle answers and determine the correct order
                    $selected_answers = [$answer_arr[$i], $answer_arr[$j], $answer_arr[$k]];
                    $correct_order = ['A', 'B', 'C'];
                    shuffle($correct_order);

                    // Map shuffled order to selected answers
                    $shuffled_answers = [
                        $correct_order[0] => $selected_answers[0],
                        $correct_order[1] => $selected_answers[1],
                        $correct_order[2] => $selected_answers[2],
                    ];

                    $combination = [
                        'num1' => $selected_nums[0],
                        'num2' => $selected_nums[1],
                        'num3' => $selected_nums[2],
                        'question1' => $selected_questions[0],
                        'question2' => $selected_questions[1],
                        'question3' => $selected_questions[2],
                        'ans1' => $shuffled_answers['A'],
                        'ans2' => $shuffled_answers['B'],
                        'ans3' => $shuffled_answers['C'],
                        'order' => implode('', array_keys($shuffled_answers))
                    ];

                    $combinations[] = $combination;
                }
            }
        }

// Output the combinations
        foreach ($combinations as $combination) {
            echo implode(", ", $combination) . PHP_EOL.'<br/>';
        }
    }

    public function test_h4()
    {


        $num_arr = ['α0101111', 'α0101112', 'α0101113', 'α0101121', 'α0101122', 'α0101123', 'α0101124', 'α0101125', 'α0101131', 'α0101132', 'α0101141', 'α0101142', 'α0101143', 'α0101144', 'α0101211', 'α0101212'];
        $question_arr = ['导电体', '绝缘体', '价电子', '本征半导体', '载流子', '电子', '空穴', '电子空穴对', 'N型半导体', 'P型半导体', '空间电荷区', '耗尽层', '电位壁垒', '阻挡层', '二极管', '二极管的结构和分类'];
        $answer_arr = ['a1', 'a2', 'a3', 'a4', 'a5', 'a6', 'a7', 'a8', 'a9', 'a10', 'a11', 'a12', 'a13', 'a14', 'a15', 'a16'];

        $combinations = [];

        for ($i = 0; $i < count($num_arr); $i++) {
            for ($j = $i + 1; $j < count($num_arr); $j++) {
                for ($k = $j + 1; $k < count($num_arr); $k++) {
                    $nums = [$num_arr[$i], $num_arr[$j], $num_arr[$k]];
                    $questions = [$question_arr[$i], $question_arr[$j], $question_arr[$k]];
                    $answers = [$answer_arr[$i], $answer_arr[$j], $answer_arr[$k]];

                    // Shuffle answers and determine order
                    $shuffled_answers = $answers;
                    shuffle($shuffled_answers);
                    $order = '';
                    foreach ($shuffled_answers as $answer) {
                        $index = array_search($answer, $answers);
                        $order .= chr(65 + $index); // Convert index to A, B, C
                    }

                    $combinations[] = [
                        'nums' => $nums,
                        'questions' => $questions,
                        'shuffled_answers' => $shuffled_answers,
                        'order' => $order
                    ];
                }
            }
        }

// Output combinations
        foreach ($combinations as $combination) {
            echo implode(',', $combination['nums']) . ',' .
                implode(',', $combination['questions']) . ',' .
                implode(',', $combination['shuffled_answers']) . ',' .
                $combination['order'] . "\n<br/>";
        }
    }

    //gpt
    public function test_card_js()
    {

        $data =  $this->zx_data();
        ini_set('memory_limit', '2566M');
        // 检查组合是否合法
        function isValidCombination($combination, $dataMap) {
            $usedActIds = [];
            foreach ($combination as $cardId) {
                $card = $dataMap[$cardId];
                $actId = $card['act_id'];

                // 检查 can_use
                if ($card['can_use'] !== 'all') {
                    foreach ($combination as $otherCardId) {
                        if ($otherCardId !== $cardId && !in_array($otherCardId, $card['can_use'])) {
                            return false; // 无效组合
                        }
                    }
                }

                // 检查 can_no_use
                if ($card['can_no_use'] === 'all') {
                    return false; // 无效组合
                } elseif (!empty($card['can_no_use'])) {
                    foreach ($combination as $otherCardId) {
                        if (in_array($otherCardId, $card['can_no_use'])) {
                            return false; // 无效组合
                        }
                    }
                }

                // 检查 can_no_with
                if ($card['can_no_with'] === 'all') {
                    return false; // 无效组合
                } elseif (!empty($card['can_no_with'])) {
                    if (in_array($actId, $usedActIds)) {
                        return false; // 无效组合
                    }
                }

                $usedActIds[] = $actId;
            }

            return true; // 合法组合
        }

// 使用生成器逐步生成所有组合（只包含 act_card_id）
        function generateAllCombinations($data) {
            $n = count($data);
            $totalCombinations = 1 << $n; // 2^n

            for ($i = 1; $i < $totalCombinations; $i++) {
                $combination = [];
                for ($j = 0; $j < $n; $j++) {
                    if ($i & (1 << $j)) { // 检查第 j 位是否为 1
                        $combination[] = $data[$j]['act_card_id'];
                    }
                }
                yield $combination; // 返回当前组合
            }
//            for ($j = 0; $j < $count; $j++) {
//                if ($i & (1 << $j)) {
//                    // 检查新卡片是否可以与组合中的其他卡片共组
//                    foreach ($combination as $existingCard) {
//                        if (!canCombine($cards[$j], $existingCard)) {
//                            $valid = false;
//                            break 2;
//                        }
//                    }
//                    $combination[] = $cards[$j];
//                    $sum += $cards[$j]['no_rel_value'];
//                }
//            }
        }

// 主逻辑
        $dataMap = [];
        foreach ($data as $item) {
            $dataMap[$item['act_card_id']] = $item; // 将数据映射为以 act_card_id 为键的数组
        }

        $results = [];
        foreach (generateAllCombinations($data) as $combination) {
            if (isValidCombination($combination, $dataMap)) {
                $totalValue = 0;
                foreach ($combination as $cardId) {
                    $totalValue += $dataMap[$cardId]['no_rel_value'];
                }
                $results[] = [
                    'combination' => $combination, // 只保存 act_card_id
                    'total_value' => $totalValue,
                ];
            }
        }

// 按 total_value 从高到低排序
        usort($results, function ($a, $b) {
            return $b['total_value'] <=> $a['total_value'];
        });
        print_json($results);

// 输出结果
        header('Content-Type: application/json');
        echo json_encode($results, JSON_PRETTY_PRINT);
    }

    private function zx_data()
    {
        $data_str ='[{"id":"33943704140022784","card_id":"33943704140022784","can_use":"all","can_no_use":[],"can_no_with":[],"value":35,"card_value":35,"no_rel_value":35,"act_id":"1877268100662333441","act_card_id":307610,"card_code":"kafkac202501091620239Hi"},{"id":"33942042462684160","card_id":"33942042462684160","can_use":"all","can_no_use":[],"can_no_with":[],"value":34.2,"card_value":34.2,"no_rel_value":34.2,"act_id":"1877260061167226881","act_card_id":307600,"card_code":"kafkac20250109154507PwH"},{"id":"33943676497462272","card_id":"33943676497462272","can_use":"all","can_no_use":[],"can_no_with":[],"value":34,"card_value":34,"no_rel_value":34,"act_id":"1877268100662333441","act_card_id":307609,"card_code":"kafkac20250109162017Aan"},{"id":"33943648889504768","card_id":"33943648889504768","can_use":"all","can_no_use":[],"can_no_with":[],"value":33,"card_value":33,"no_rel_value":33,"act_id":"1877268100662333441","act_card_id":307608,"card_code":"kafkac20250109162020G89"},{"id":"33943621873992704","card_id":"33943621873992704","can_use":"all","can_no_use":[],"can_no_with":[],"value":32,"card_value":32,"no_rel_value":32,"act_id":"1877268100662333441","act_card_id":307607,"card_code":"kafkac20250109162017ofo"},{"id":"33943579615331328","card_id":"33943579615331328","can_use":"all","can_no_use":[],"can_no_with":[],"value":31,"card_value":31,"no_rel_value":31,"act_id":"1877268100662333441","act_card_id":307606,"card_code":"kafkac20250109162012vhg"},{"id":"33943550709236736","card_id":"33943550709236736","can_use":"all","can_no_use":[],"can_no_with":[],"value":30,"card_value":30,"no_rel_value":30,"act_id":"1877268100662333441","act_card_id":307605,"card_code":"kafkac202501091620127Cl"},{"id":"33941970934072320","card_id":"33941970934072320","can_use":"all","can_no_use":[],"can_no_with":[],"value":29.8,"card_value":29.8,"no_rel_value":29.8,"act_id":"1877260061167226881","act_card_id":307599,"card_code":"kafkac20250109154505EtU"},{"id":"33943510650487808","card_id":"33943510650487808","can_use":"all","can_no_use":[],"can_no_with":[],"value":29,"card_value":29,"no_rel_value":29,"act_id":"1877268100662333441","act_card_id":307604,"card_code":"kafkac20250109162013UNj"},{"id":"33943478167700480","card_id":"33943478167700480","can_use":"all","can_no_use":[],"can_no_with":[],"value":28,"card_value":28,"no_rel_value":28,"act_id":"1877268100662333441","act_card_id":307603,"card_code":"kafkac20250109162008gir"},{"id":"33943433112486912","card_id":"33943433112486912","can_use":"all","can_no_use":[],"can_no_with":[],"value":27,"card_value":27,"no_rel_value":27,"act_id":"1877268100662333441","act_card_id":307602,"card_code":"kafkac20250109162006UnO"},{"id":"33943381931492352","card_id":"33943381931492352","can_use":"all","can_no_use":[],"can_no_with":[],"value":26,"card_value":26,"no_rel_value":26,"act_id":"1877268100662333441","act_card_id":307601,"card_code":"kafkac20250109162004c3L"},{"id":"33939090722292736","card_id":"33939090722292736","can_use":"all","can_no_use":[],"can_no_with":[],"value":25,"card_value":25,"no_rel_value":25,"act_id":"1877247024871936001","act_card_id":307595,"card_code":"kafkac20250109145515Nwv"},{"id":"33939050679272448","card_id":"33939050679272448","can_use":"all","can_no_use":[],"can_no_with":[],"value":20,"card_value":20,"no_rel_value":20,"act_id":"1877247024871936001","act_card_id":307594,"card_code":"kafkac20250109145515T1t"},{"id":"33941923866641408","card_id":"33941923866641408","can_use":"all","can_no_use":[],"can_no_with":[],"value":17.8,"card_value":17.8,"no_rel_value":17.8,"act_id":"1877260061167226881","act_card_id":307598,"card_code":"kafkac20250109154505kWN"},{"id":"33939013386667008","card_id":"33939013386667008","can_use":"all","can_no_use":[],"can_no_with":[],"value":15,"card_value":15,"no_rel_value":15,"act_id":"1877247024871936001","act_card_id":307593,"card_code":"kafkac20250109145514A7b"}]';
        $data =  json_decode($data_str,true);
        return $data;
    }


    public function test_card_js_old()
    {
        $data=$this->zx_data();
        ini_set('memory_limit', '2566M');
        function generateCombinationsRecursively($cards, $start = 0, $currentCombo = [], $currentActs = [], &$result = []) {
//        print_json($cards);
            for ($i = $start; $i < count($cards); $i++) {
                $newCombo = array_merge($currentCombo, [$cards[$i]]);
                $newActs = array_merge($currentActs, [$cards[$i]['act_id']]);

                // 检查组合是否有效
                if (isValidCombination($newCombo, $newActs)) {
//                $comboIds = array_map(function($card) { return $card['act_card_id']; }, $newCombo);
//                sort($comboIds);
//                $comboKey = implode(',', $comboIds);

                    // 获取组合的标识（使用完整的卡片数据，而不是仅基于 card_id）
                    $comboKey = array_map(function($card) { return json_encode($card); }, $newCombo);
                    // 检查当前组合的所有子集是否已存在
                    $isSubset = false;
                    foreach ($result as $existingCombo) {

                        if (array_diff($comboKey, $existingCombo) === []) {
                            //暂时去掉这里，不然 比如  321的时候21就不能成组了，那么这个时候选了2，1也不能用了

//                        print_json($comboKey,$existingCombo);
//                        $isSubset = true;
//                        break;
                        }
                    }

                    if (!$isSubset) {
                        // 移除任何已存在的组合是新组合的子集
                        foreach ($result as $key => $existingCombo) {
                            if (array_diff($existingCombo, $comboKey) === []) {
                                unset($result[$key]);
                            }
                        }

                        // 添加新组合
                        $result[] = $comboKey;

                        // 递归生成更大的组合

                        // 递归生成更大的组合
                        generateCombinationsRecursively($cards, $i + 1, $newCombo, $newActs, $result);
                    }
                }
            }
        }

// 检查组合是否有效
        function isValidCombination($combo, $currentActs) {
        $comboIds = array_map(function($card) { return $card['act_card_id']; }, $combo);

        foreach ($combo as $card) {
            // 检查不可共用的卡券
            if ($card['can_no_use'] === 'all') {
                if (count($combo) > 1) {
                    return false;
                }
            } else {
                foreach ($card['can_no_use'] as $noUseId) {
                    if (in_array($noUseId, $comboIds)) {
                        return false;
                    }
                }
            }

            // 检查必须共用的卡券
            if (!empty($card['can_use']) && $card['can_use']!=='all') {
                foreach ($comboIds as $id) {
                    if ($id !== $card['card_id'] && !in_array($id, $card['can_use'])) {
                        return false;
                    }
                }
            }

            // 检查act层次的约束
            if ($card['can_no_with'] === 'all') {
                if (count($currentActs) > 1) {
                    return false;
                }
            } else {
                foreach ($card['can_no_with'] as $noWithActId) {
                    if (in_array($noWithActId, $currentActs)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

// 主逻辑
        $validCombinations = [];
        generateCombinationsRecursively($data, 0, [], [], $validCombinations);
// 计算每个组合的总价值并排序
        $sortedCombinations = [];
        foreach ($validCombinations as $combo) {
            $comboIds = array_map(function($card) { return json_decode($card, true)['card_code']; }, $combo);
            $totalValue = array_sum(array_map(function($card) { return json_decode($card, true)['card_value']; }, $combo));
            $notRelValue = array_sum(array_map(function($card) { return json_decode($card, true)['no_rel_value']; }, $combo));
            $combo_arr = [];
            $sortedCombinations[] = ['combo' => $combo, 'total_value' => $totalValue,'not_rel_value'=>$notRelValue];
//            foreach ($comboIds as $co_v){
////                $one_co_v =  json_decode($co_v,true);
//                $sortedCombinations[] = ['combo' => $combo, 'total_value' => $totalValue];
//            }

//            $sortedCombinations[] = ['combo' => $combo_arr, 'total_value' => $totalValue,'not_rel_value'=>$notRelValue];
        }
//

        usort($sortedCombinations, function($a, $b) {
            return $b['not_rel_value'] - $a['not_rel_value'];
//            return $b['total_value'] - $a['total_value'];
        });
        print_json($sortedCombinations);
    }

    //ds
    public function test_zh_ds()
    {
        $data=$this->zx_data();

        /**
         * 检查两个卡片是否可以共组
         */
        function canCombine($card1, $card2) {
            // 检查 card1 的 can_use 规则
            if ($card1['can_use'] !== 'all') {
                if (!in_array($card2['act_card_id'], $card1['can_use'])) {
                    return false;
                }
            }

            // 检查 card1 的 can_no_use 规则
            if ($card1['can_no_use'] === 'all') {
                return false;
            } elseif (!empty($card1['can_no_use'])) {
                if (in_array($card2['act_card_id'], $card1['can_no_use'])) {
                    return false;
                }
            }

            // 检查 card1 的 can_no_with 规则
            if ($card1['can_no_with'] === 'all') {
                return false;
            } elseif (!empty($card1['can_no_with'])) {
                if (in_array($card2['act_id'], $card1['can_no_with'])) {
                    return false;
                }
            }

            // 检查 card2 的 can_use 规则
            if ($card2['can_use'] !== 'all') {
                if (!in_array($card1['act_card_id'], $card2['can_use'])) {
                    return false;
                }
            }

            // 检查 card2 的 can_no_use 规则
            if ($card2['can_no_use'] === 'all') {
                return false;
            } elseif (!empty($card2['can_no_use'])) {
                if (in_array($card1['act_card_id'], $card2['can_no_use'])) {
                    return false;
                }
            }

            // 检查 card2 的 can_no_with 规则
            if ($card2['can_no_with'] === 'all') {
                return false;
            } elseif (!empty($card2['can_no_with'])) {
                if (in_array($card1['act_id'], $card2['can_no_with'])) {
                    return false;
                }
            }

            return true;
        }

        /**
         * 生成所有有效的组合
         */
        function generateCombinations($cards) {
            $combinations = [];
            $count = count($cards);

            // 遍历所有可能的组合
            for ($i = 1; $i < (1 << $count); $i++) {
                $combination = [];
                $sum = 0;
                $valid = true;

                // 检查当前组合是否有效
                for ($j = 0; $j < $count; $j++) {
                    if ($i & (1 << $j)) {
                        // 检查新卡片是否可以与组合中的其他卡片共组
                        foreach ($combination as $existingCard) {
                            if (!canCombine($cards[$j], $existingCard)) {
                                $valid = false;
                                break 2;
                            }
                        }
                        $combination[] = $cards[$j];
                        $sum += $cards[$j]['no_rel_value'];
                    }
                }

                if ($valid) {
                    $combinations[] = [
                        'cards' => $combination,
                        'sum' => $sum
                    ];
                }
            }

            return $combinations;
        }

// 生成所有有效组合
        $combinations = generateCombinations($data);

// 按组合的 sum 从高到低排序
        usort($combinations, function($a, $b) {
            return $b['sum'] <=> $a['sum'];
        });

// 输出结果
        foreach ($combinations as $combination) {
            echo "Combination Sum: " . $combination['sum'] . "\n";
            echo  json_encode(array_column($combination['cards'],'act_card_id'));
//            foreach ($combination['cards'] as $card) {
//                echo "Card ID: " . $card['act_card_id'] . ", no_rel_value: " . $card['no_rel_value'] . "\n";
//            }
            echo "<br/>\n";
        }

    }


    public function get_card_error()
    {
        $type = input('type',1);
        $user_id = input('user_id','');
        $phone = input('phone','');
        $act_log_model =  new DbActivityCenterLog();
        $where=['is_enable'=>1];
        if($type ==1){
            $where['request_id'] = 'mallActivityReceiveCoupon';
        }
        if($type ==2){
            $where['request_id'] = 'getMallActivityCouponList';
        }
        if($user_id || $phone){
            $user_model =  new DbUser();
            if($user_id){
                $user_where = ['id'=>$user_id];
            }
            if($phone){
                $user_where = ['mid_phone'=>$phone];
            }
            $user = $user_model->getOne(['where'=>$user_where,'order'=>'id desc']);
            $where['oneid'] = $user['one_id'];
            $user_id = $user['id'];
        }
        $list =  $act_log_model->getList(['where'=>$where,'limit'=>'2000','order'=>'id desc']);
        $table = '<table style="background:#f0f8ff">';
        foreach ($list as $v){
            $table.='<tr><td>'.$v['request_id'].'</td><td>'.$v['oneid'].'</td><td>'.$v['created_date'].'</td><td>'.$v['request_info'].'</td><td>'.$v['response_info'].'</td><td>'.$user_id.'</td></tr>';
        }
        $table.='</table>';
        echo  $table;
    }

    public function test_check_log()
    {
        $type = input('type','packagesb');
        $send_note = input('vin','9999999');
        $log_model =  new DbLog();
        $where=['type'=>$type];
        if(in_array($type,['packagesb','jiexu'])){
            $where['send_note'] = $send_note;
        }else{
            $where['send_note']=['like','%'.$send_note.'%s'];
        }
        $list = $log_model->getList(['where'=>$where,'order'=>'id desc','limit'=>'1000']);
        $table = '<table style="background: #dedede">';
        foreach ($list as $v){
            $table.='<tr><td>'.$v['type'].'</td><td>'.$v['send_note'].'</td><td>'.$v['created_date'].'</td><td>'.$v['receive_note'].'</td></tr>';
        }
        $table.='</table>';
        echo  $table;
    }

    /**
     * 商品列表性能对比测试
     * 访问: /index_v2/lzx1/goodsListPerformanceTest?user_token=lzx123&user_data=0814
     */
    public function goodsListPerformanceTest()
    {
        // 模拟用户数据
        $user = [
            'id' => 12345,
            'bind_unionid' => 'test_unionid_12345',
            'car_series_id' => 100,
            'member_id' => 'M12345',
            'one_id' => 'ONE12345',
            'car_18n' => 'TEST18N',
            '18_oil_type' => 4,
            'car_offline_date' => '2025-12-31'
        ];

        $channel_type = 'GWSM';

        $test_params = [
            'page' => 1,
            'pageSize' => 20,
            'search' => '',
            'comm_type_id' => '',
            'car_id' => $user['car_series_id'],
            'dd_dlr_code' => 'TEST001'
        ];

        $results = [];

        // 测试原方法
        $results['original'] = $this->testOriginalGoodsListMethod($test_params, $user, $channel_type);

        // 测试优化方法
        $results['optimized'] = $this->testOptimizedGoodsListMethod($test_params, $user, $channel_type);

        // 性能对比
        $performance_comparison = $this->compareGoodsListPerformance($results);

        return json([
            'code' => 200,
            'msg' => '商品列表性能测试完成',
            'data' => [
                'results' => $results,
                'comparison' => $performance_comparison,
                'test_params' => $test_params,
                'user_info' => $user
            ]
        ]);
    }

    /**
     * 测试原始goodsList方法
     */
    private function testOriginalGoodsListMethod($params, $user, $channel_type)
    {
        $start_time = microtime(true);
        $start_memory = memory_get_usage();

        try {
            $net_goods = new NetGoods();
            $result = $net_goods->goodsList($params, $user, $channel_type);

            $end_time = microtime(true);
            $end_memory = memory_get_usage();

            return [
                'success' => true,
                'execution_time' => round(($end_time - $start_time) * 1000, 2), // 毫秒
                'memory_usage' => round(($end_memory - $start_memory) / 1024 / 1024, 2), // MB
                'peak_memory' => round(memory_get_peak_usage() / 1024 / 1024, 2), // MB
                'data_count' => isset($result['msg']['data']) ? count($result['msg']['data']) : 0,
                'total_count' => $result['msg']['total'] ?? 0,
                'result_code' => $result['code'] ?? 0,
                'cache_hit' => false // 原方法缓存信息
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'execution_time' => round((microtime(true) - $start_time) * 1000, 2),
                'memory_usage' => round((memory_get_usage() - $start_memory) / 1024 / 1024, 2)
            ];
        }
    }

    /**
     * 测试优化后的goodsList方法
     */
    private function testOptimizedGoodsListMethod($params, $user, $channel_type)
    {
        $start_time = microtime(true);
        $start_memory = memory_get_usage();

        try {
            $net_goods_optimized = new NetGoodsOptimized();
            $result = $net_goods_optimized->goodsListOptimized($params, $user, $channel_type);

            $end_time = microtime(true);
            $end_memory = memory_get_usage();

            return [
                'success' => true,
                'execution_time' => round(($end_time - $start_time) * 1000, 2), // 毫秒
                'memory_usage' => round(($end_memory - $start_memory) / 1024 / 1024, 2), // MB
                'peak_memory' => round(memory_get_peak_usage() / 1024 / 1024, 2), // MB
                'data_count' => isset($result['msg']['data']) ? count($result['msg']['data']) : 0,
                'total_count' => $result['msg']['total'] ?? 0,
                'result_code' => $result['code'] ?? 0,
                'cache_hit' => true, // 优化方法有缓存
                'error_details' => $result['msg'] ?? 'No error details'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'execution_time' => round((microtime(true) - $start_time) * 1000, 2),
                'memory_usage' => round((memory_get_usage() - $start_memory) / 1024 / 1024, 2)
            ];
        }
    }

    /**
     * 性能对比分析
     */
    private function compareGoodsListPerformance($results)
    {
        $original = $results['original'];
        $optimized = $results['optimized'];

        if (!$original['success'] || !$optimized['success']) {
            return [
                'status' => 'error',
                'message' => '测试过程中出现错误，无法进行对比',
                'original_error' => $original['error'] ?? '',
                'optimized_error' => $optimized['error'] ?? ''
            ];
        }

        $time_improvement = 0;
        $memory_improvement = 0;

        if ($original['execution_time'] > 0) {
            $time_improvement = round((($original['execution_time'] - $optimized['execution_time']) / $original['execution_time']) * 100, 2);
        }

        if ($original['memory_usage'] > 0) {
            $memory_improvement = round((($original['memory_usage'] - $optimized['memory_usage']) / $original['memory_usage']) * 100, 2);
        }

        return [
            'status' => 'success',
            'time_improvement_percent' => $time_improvement,
            'memory_improvement_percent' => $memory_improvement,
            'original_time_ms' => $original['execution_time'],
            'optimized_time_ms' => $optimized['execution_time'],
            'original_memory_mb' => $original['memory_usage'],
            'optimized_memory_mb' => $optimized['memory_usage'],
            'data_consistency' => $original['data_count'] === $optimized['data_count'],
            'summary' => $this->generateGoodsListSummary($time_improvement, $memory_improvement)
        ];
    }

    /**
     * 生成性能总结
     */
    private function generateGoodsListSummary($time_improvement, $memory_improvement)
    {
        $summary = [];

        if ($time_improvement > 0) {
            $summary[] = "执行时间优化了 {$time_improvement}%";
        } elseif ($time_improvement < 0) {
            $summary[] = "执行时间增加了 " . abs($time_improvement) . "%";
        } else {
            $summary[] = "执行时间基本相同";
        }

        if ($memory_improvement > 0) {
            $summary[] = "内存使用优化了 {$memory_improvement}%";
        } elseif ($memory_improvement < 0) {
            $summary[] = "内存使用增加了 " . abs($memory_improvement) . "%";
        } else {
            $summary[] = "内存使用基本相同";
        }

        return implode('，', $summary);
    }

    /**
     * 显示性能测试页面
     * 访问: /index_v2/lzx1/performanceTestPage?user_token=lzx123&user_data=0814
     */
    public function performanceTestPage()
    {
        return $this->fetch('performance_test');
    }

}
