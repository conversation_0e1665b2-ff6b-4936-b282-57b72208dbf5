<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品列表性能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .results {
            margin-top: 20px;
        }
        .result-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .comparison {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
        }
        .metric {
            display: inline-block;
            margin: 5px 15px 5px 0;
            padding: 5px 10px;
            background: #e9ecef;
            border-radius: 3px;
            font-size: 14px;
        }
        .improvement {
            color: #28a745;
            font-weight: bold;
        }
        .degradation {
            color: #dc3545;
            font-weight: bold;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>商品列表性能测试工具</h1>
            <p>对比原始goodsList方法与优化后方法的性能差异</p>
        </div>
        
        <div class="test-controls">
            <button class="test-button" onclick="runPerformanceTest()">运行性能测试</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results" class="results"></div>
    </div>

    <script>
        function runPerformanceTest() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">正在运行性能测试，请稍候...</div>';
            
            const testUrl = '/index_v2/lzx1/goodsListPerformanceTest?user_token=lzx123&user_data=' + 
                           (new Date().getMonth() + 1).toString().padStart(2, '0') + 
                           new Date().getDate().toString().padStart(2, '0');
            
            fetch(testUrl)
                .then(response => response.json())
                .then(data => {
                    displayResults(data);
                })
                .catch(error => {
                    resultsDiv.innerHTML = `
                        <div class="result-card error">
                            <h3>测试失败</h3>
                            <p>错误信息: ${error.message}</p>
                        </div>
                    `;
                });
        }
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            if (data.code !== 200) {
                resultsDiv.innerHTML = `
                    <div class="result-card error">
                        <h3>测试失败</h3>
                        <p>${data.msg}</p>
                    </div>
                `;
                return;
            }
            
            const results = data.data.results;
            const comparison = data.data.comparison;
            
            let html = '<h2>测试结果</h2>';
            
            // 原始方法结果
            html += `
                <div class="result-card ${results.original.success ? 'success' : 'error'}">
                    <h3>原始goodsList方法</h3>
                    ${results.original.success ? 
                        `<div class="metric">执行时间: ${results.original.execution_time}ms</div>
                         <div class="metric">内存使用: ${results.original.memory_usage}MB</div>
                         <div class="metric">峰值内存: ${results.original.peak_memory}MB</div>
                         <div class="metric">数据条数: ${results.original.data_count}</div>
                         <div class="metric">总记录数: ${results.original.total_count}</div>` :
                        `<p class="error">执行失败: ${results.original.error}</p>`
                    }
                </div>
            `;
            
            // 优化方法结果
            html += `
                <div class="result-card ${results.optimized.success ? 'success' : 'error'}">
                    <h3>优化goodsList方法</h3>
                    ${results.optimized.success ? 
                        `<div class="metric">执行时间: ${results.optimized.execution_time}ms</div>
                         <div class="metric">内存使用: ${results.optimized.memory_usage}MB</div>
                         <div class="metric">峰值内存: ${results.optimized.peak_memory}MB</div>
                         <div class="metric">数据条数: ${results.optimized.data_count}</div>
                         <div class="metric">总记录数: ${results.optimized.total_count}</div>` :
                        `<p class="error">执行失败: ${results.optimized.error}</p>`
                    }
                </div>
            `;
            
            // 性能对比
            if (comparison.status === 'success') {
                const timeClass = comparison.time_improvement_percent > 0 ? 'improvement' : 
                                 comparison.time_improvement_percent < 0 ? 'degradation' : '';
                const memoryClass = comparison.memory_improvement_percent > 0 ? 'improvement' : 
                                   comparison.memory_improvement_percent < 0 ? 'degradation' : '';
                
                html += `
                    <div class="result-card comparison">
                        <h3>性能对比分析</h3>
                        <div class="metric ${timeClass}">
                            执行时间优化: ${comparison.time_improvement_percent > 0 ? '+' : ''}${comparison.time_improvement_percent}%
                        </div>
                        <div class="metric ${memoryClass}">
                            内存使用优化: ${comparison.memory_improvement_percent > 0 ? '+' : ''}${comparison.memory_improvement_percent}%
                        </div>
                        <div class="metric">数据一致性: ${comparison.data_consistency ? '✓ 通过' : '✗ 失败'}</div>
                        <p><strong>总结:</strong> ${comparison.summary}</p>
                    </div>
                `;
            } else {
                html += `
                    <div class="result-card error">
                        <h3>性能对比失败</h3>
                        <p>${comparison.message}</p>
                    </div>
                `;
            }
            
            // 测试参数
            html += `
                <div class="result-card">
                    <h3>测试参数</h3>
                    <div class="json-display">${JSON.stringify(data.data.test_params, null, 2)}</div>
                </div>
            `;
            
            // 详细数据
            html += `
                <div class="result-card">
                    <h3>详细测试数据</h3>
                    <div class="json-display">${JSON.stringify(data.data, null, 2)}</div>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载完成后自动运行一次测试
        window.onload = function() {
            // runPerformanceTest();
        };
    </script>
</body>
</html>
