<?php
/**
 *
 * @author: lian<PERSON><PERSON><PERSON>
 * @time: 2017-03-29
 */

namespace app\admin\controller;

use app\common\model as model;
use packages\Curl;
use tool\mutex\RedisMutex;

class Index extends Common{




    function test(){
        //$model=model('DbAdmin');
        $model=new model\DbCommodity();
        $list=$model->getList(['where'=>['validity_date_start'=>[['>=',date('Y-m-d')],['exp','is null'],'or'],'validity_date_end'=>[['>=',date('Y-m-d')],['exp','is null'],'or']]]);
        var_dump($list);
        $list=$model->getList();
       // var_dump($list);
       // $model=new model\DbAdmin();

       // $lis=$model->getList();


    }

    function index(){

        //var_dump($list);
        $this->redirect(request()->root(true).'/admin/consume_card/index');
        return $this->fetch("index/index");


    }


    public function update(){

        return $this->fetch("index/update");

    }

    public function add(){
        $model=new model\BuUserCollection();
        $res=$model->where(['vin'=>'LGBF1DE03BR058605'])->update(['openid'=>'oYwsZv9h_dc77ajtYazNAhb1zodM22']);
        var_dump($res);



    }

    public function delete(){



    }

    public function redis(){

        $text = '<br><p>Test paragraph.</p><!-- Comment --> <a href="#fragment">Other text</a>';
        echo strip_tags($text);
        echo "\n";

// 允许 <p> 和 <a>
        echo strip_tags($text, '<p><a>');
    }

    public function redisT(){
        $params='';
        $vars=array_merge($params,['kk'=>1] );
        var_dump($vars);
        exit();
        $kk=new RedisMutex();
        $kk->acquireLock('4444',10);
        var_dump($kk);
    }

    public function test4(){

        var_dump( request()->pathinfo());

    }






}

