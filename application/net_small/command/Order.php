<?php

namespace app\net_small\command;

use app\common\command\Base;
use app\common\model\bu\BuOrder;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbSeckill;
use app\common\net_service\Common;
use app\common\net_service\LyPay;
use app\common\net_service\NetOrder;
use app\common\port\connectors\Bdp;
use think\Config;
use think\Log;
use tool\Logger;

/**
 * Class order
 * @package app\net_small\command
 */
class Order extends Base
{

    public static function doIt()
    {
        trace('cron order value start');
        static::loadConfig();
        Config::load(ROOT_PATH . 'config/net_small/config.php');
        Config::load(ROOT_PATH . 'config/net_small/' . config('app_status') . '.php');

        $order_model = new BuOrder();
        try {
            $order_goods_model =  new DbCommoditySet();
            $be_date= date('2022-05-23 14:24:53');
//            $s_where = sprintf("is_enable=1 and shelves_type<>8");
//        $s_where = sprintf("is_enable=1 and ( FIND_IN_SET('%s',up_down_channel_dlr) or FIND_IN_SET('%s',up_down_channel_dlr))",'PZ1ASM','PZ1AAPP');
//            $s_data['front_sale_num'] = ['exp',"quantitys"];
//            $order_goods_model->saveData($s_data,$s_where);
            $where = ['a.order_status'=>['not in',[1,3,5,8,15,18]],'a.created_date'=>['>=',$be_date]];
            $list = $order_model->alias('a')->join('t_bu_order_commodity b ',' a.order_code=b.order_code ')->where($where)->group('b.commodity_set_id')->field('SUM(count) sc,commodity_set_id')->select();
            if($list){
                foreach ($list as $v){
                    $data=[];
                    $com_where = ['id'=>$v['commodity_set_id']];
                    $data['front_sale_num'] = ['exp',sprintf("quantitys+%s",$v['sc'])];
                    $res = $order_goods_model->saveData($data,$com_where);
                }
            }

        } catch (\Exception $e) {
            Log::error("order  error : " . $e->getMessage() . ' : ' . $order_model->getLastSql());
        }

        trace('cron order value end');
    }

    public static function backstock()
    {
        trace('cron backstock start');
        static::loadConfig();
        Config::load(ROOT_PATH . 'config/net_small/config.php');
        Config::load(ROOT_PATH . 'config/net_small/' . config('app_status') . '.php');

        $order_model = new BuOrder();
        try {
//            where d.order_status=8 and TIMESTAMPDIFF(MINUTE,d.last_updated_date,now())>=5 group by a.id) b ,
            $order_where = ['a.order_status'=>8,'a.last_updated_date'=>['<=',date('Y-m-d H:i:s',strtotime('-5 minute'))]];
            $order_list = $order_model->alias('a')->join('t_bu_order_commodity b ',' a.order_code=b.order_code')->where($order_where)->field('a.id,b.sku_id,b.mo_sub_id,b.count,b.seckill_id,b.order_code,b.commodity_id,a.created_date,a.ms_order_code,a.ms_order_code2,a.link_order_code,a.link_order_code2,a.order_status')->select();
            if($order_list){
                $net_order =  new NetOrder();
                foreach($order_list as $v){
                    //回退秒杀缓存库存--暂时不退
                    if($v['ms_order_code'] ){
                        $param =[
                            'msOrderCode'=>$v['ms_order_code'],
                            'linkOrderCode'=>$v['link_order_code'],
                        ];
                        $lyPay  = new LyPay();
                        $result = $lyPay->closeOrder($param);
                        if($result->isSuccess()){
                            //如果关闭订单成功
                        }else{
                            //如果不成功--直接不处理这个订单
                            continue;
                        }
                    }
                    if($v['seckill_id']){
                        $seckill_type = DbSeckill::where('id', $v['seckill_id'])->value('seckill_type');
                        $screening = '';
                        if ($seckill_type == 2) {
                            $screening = date('Y-m-d', strtotime($v['created_date']));
                        }
                        $net_order->inc_kill_count($v['seckill_id'],$v['commodity_id'],$v['count'],$v['order_code'],$screening);
                    }
                    $res = $net_order->_addStock($v['sku_id'],$v['count'],$v['mo_sub_id'],$v['order_code']);
                    $order_model->saveData(['order_status'=> 1,'modifier'=>'b_s_tak'],['id'=>$v['id']]);
                    Logger::error('orderbackstock',['sku'=>$v['sku_id'], 'count'=>$v['count'],'mo_sub_id'=>$v['mo_sub_id'],'res'=>$res]);
                }
            }
        } catch (\Exception $e) {
            Logger::error("orderbackstockerror : " . $e->getMessage() . ' : ' . $order_model->getLastSql());
        }

        trace('cron order value end');
    }


}
