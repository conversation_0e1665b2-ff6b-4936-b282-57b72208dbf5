<?php

namespace app\net_small\command;

use app\common\command\Base;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\db\DbCard;
use app\common\model\db\DbDlr;
use app\common\model\db\DbUser;
use app\common\model\e3s\E3sCarSeries;
use app\common\net_service\Common;
use app\common\port\connectors\QuickWin;
use think\Config;
use think\Hook;
use think\Log;

/**
 * Class CardReceiveRecord
 * @package app\net_small\command
 */
class CardReceiveRecord extends Base
{

    public static function doIt()
    {
        trace('cron card receive record start');
        static::loadConfig();
        Config::load(ROOT_PATH . 'config/net_small/config.php');
        Config::load(ROOT_PATH . 'config/net_small/' . config('app_status') . '.php');

        $BuCardReceiveRecord = new BuCardReceiveRecord();
        $DbCard              = new DbCard();
        try {
            $time  = time();
            $start = date('Y-m-d H:i:s', $time - 600);
            $end   = date('Y-m-d H:i:s', $time);
            $BuCardReceiveRecord
                ->whereBetween('last_updated_date', [$start, $end])
                ->whereIn('status', [3, 5])
                ->where(['is_enable' => 1])
                ->chunk(
                    100, function ($arr) {
                    $common_model = new Common();
                    $card_ids     = array_column($arr, 'card_id');
                    $user_ids     = array_column($arr, 'user_id');
                    $cards        = DbCard::where(['id' => ['in', $card_ids]])->column('*', 'id');
                    $users        = DbUser::where(['id' => ['in', $user_ids]])->column('*', 'id');
                    foreach ($arr as $item) {
                        $lock = 'CardReceiveRecord:' . $item['card_code'] . '-' . $item['status'];
                        if (!getRedisLock($lock, mt_rand(40, 80))) {
                            continue;
                        }
                        if ($cards[$item['card_id']]['quick_win_card_id'] > 0) {
                            $receive_data    = $common_model->getReceiveChannel($item['dlr_code']);
                            $receive_channel = $receive_data['receive_channel'];
                            $receive_source  = $receive_data['receive_source'];
                            if (empty($item['coupon_code'])) {
                                $get_info = (new BuCardReceiveRecord())->where(['id' => $item['id']])->column('coupon_code');
                                if (!empty($get_info[0])) continue;
                                $brand = DbDlr::channel_to_brand($item['dlr_code']);
                                $data  = [
                                    "coupon_id"          => $cards[$item['card_id']]['quick_win_card_id'],
                                    "intention_brand_id" => $brand == 3 ? 9 : $brand, // pz品牌id在卡券中心是9
                                    "one_id"             => $users[$item['user_id']]['one_id'],
                                    "receive_channel"    => $receive_channel,
                                    "receive_source"     => $receive_source,
                                    "reserve_status"     => 0,
                                    "user_name"          => $item['name'],
                                    "user_phone"         => $item['phone'],
                                    "vin"                => $item['vin'],
                                    "consume_begin_date" => $item['validity_date_start'],
                                    "consume_end_date"   => $item['validity_date_end'],
                                ];
                                if ($cards[$item['card_id']]['consume_condition'] == 1) {
                                    $data['intention_store']       = $item['get_dlr_code'];
                                    $data['intention_car_type_id'] = $item['car_18n'];
                                    $redis_dlr_key                 = 'e3s_dlr_id_' . $item['get_dlr_code'];
                                    $redis_dlr_id                  = redis($redis_dlr_key);
                                    if (!empty($redis_dlr_id)) {
                                        $data['intention_store_id'] = $redis_dlr_id;
                                    } else {
                                        $dlr_list                   = QuickWin::create('e3s_dlr')->postDlr(['dlrCode' => $item['get_dlr_code'], 'sortType' => '2'], $data['intention_brand_id']);
                                        $data['intention_store_id'] = $dlr_list['rows'][0]['dlrId'] ?? 0;
                                        redis($redis_dlr_key, $data['intention_store_id'], 3600);
                                    }
                                    $redis_car_key  = 'e3s_car_id_' . $item['car_18n'];
                                    $redis_car_info = redis($redis_car_key);
                                    if (!empty($redis_car_info)) {
                                        $car_info = $redis_car_info;
                                    } else {
                                        $car_info = E3sCarSeries::where('car_config_code', $item['car_18n'])->find();
                                        redis($redis_car_key, $car_info, 3600);
                                    }
                                    $data['intention_car_series_id'] = $car_info['base_series_code'];
                                    BuCardReceiveRecord::where('id', $item['id'])->update(['intention_dlr_id' => $data['intention_store_id']]); // 意向门店id填充进卡券领取表
                                }
                                $event_type = 1;
                                $arr1       = ['id' => $item['id'], 'event_type' => $event_type, 'user_id' => $item['user_id'], 'card_id' => $item['card_id'], 'data' => $data];
                                Hook::exec('app\\net_small\\behavior\\CardReceiveRecordBehavior', 'run', $arr1);
                            } elseif ($item['status'] == 3 && $item['quick_win_is_consume'] == 0) {
                                $data       = [
                                    [
                                        "business_order_name"     => "",
                                        "business_order_no"       => $item['consume_order_code'],
                                        "coupon_code"             => $item['coupon_code'],
                                        "coupon_discounted_price" => (int)($item['card_all_yh'] * 100),
                                        "coupon_receive_id"       => '',
                                        "creator"                 => "",
                                        "order_source"            => "",
                                        "used_store"              => $item['get_dlr_code'],
                                        "used_store_id"           => $item['intention_dlr_id'],
                                        'consume_channel'          => 3, // 核销渠道  3-商城
                                    ]
                                ];
                                $event_type = 2;
                                $arr1       = ['id' => $item['id'], 'event_type' => $event_type, 'user_id' => $item['user_id'], 'card_id' => $item['card_id'], 'coupon_code' => $item['coupon_code'], 'data' => $data];
                                Hook::exec('app\\net_small\\behavior\\CardReceiveRecordBehavior', 'run', $arr1);
                            }
                        }
                    }
                }
                );
            // 激活卡券失败补充机制 卡券融合后没有此步骤
            /*$DbCard
                ->where(['is_enable' => 1, 'act_status' => 2, 'is_succeed' => 1])
                ->whereBetween('last_updated_date', [$start, $end])
                ->chunk(
                    100, function ($arr) {
                    foreach ($arr as $item) {
                        $lock = 'DbCard:' . $item['card_id'];
                        if (!getRedisLock($lock, mt_rand(40, 80))) {
                            continue;
                        }
                        $quickWinInfo = QuickWin::create('quick_win')->getCouponInfo($item['quick_win_card_id']);
                        if ($quickWinInfo['rows']['coupon_status'] == 0) {
                            QuickWin::create('quick_win')->postCouponInfo($item['quick_win_card_id']);
                        }
                    }
                });*/
        } catch (\Exception $e) {
            Log::error("card receive record error : " . $e->getMessage() . ' : ' . $BuCardReceiveRecord->getLastSql());
        }

        trace('cron card receive record end');
    }

}
