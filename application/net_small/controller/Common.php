<?php
/**
 * Created by PhpStor<PERSON>.
 * User: lzx
 * Date: 2020-12-22 15:08:50
 */


namespace app\net_small\controller;

use api\wechat\Carer;
use app\admin_v2\controller\ArticleAdmin;
use app\common\model\db\DbActivityMuchCard;
use app\common\model\db\DbArea;
use app\common\model\act\AcGroup;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbCard;
use app\common\model\db\DbCarSeries;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbJobsLog;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbNDiscountCommodity;
use app\common\model\db\DbNDiscountInfo;
use app\common\model\db\DbPreSaleCommodity;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\model\db\DbUserAddress;
use app\common\model\db\DbUserCarSeries;
use app\common\model\db\DbUserSub;
use app\common\model\db\DbVinRule;
use app\common\net_service\NetCardKafKa;
use app\common\port\connectors\E3spRefactor;
use app\common\port\connectors\MarketBase as MB;
use app\common\port\connectors\Member;
use Firebase\JWT\JWT;
use Firebase\JWT\SignatureInvalidException;
use ForkModules\Traits\ResponseTrait;
use think\Controller;
use think\Env;
use think\Exception;
use think\exception\HttpResponseException;
use think\Hook;
use think\Queue;
use think\Request;
use tool\Logger;

class Common extends Controller
{
    use ResponseTrait;

    protected $_getCarer;
    protected $user_id;
    protected $source;
    protected $channel_type;
    protected $user;
    protected $unionid;
    protected $memberid;
    protected $openid;
    protected $oneid;
    protected $event_param;
    protected $brand;
    protected $user_vin = '';
    protected $shelves_type = 5;
    protected $member_info;

    protected function _initialize()
    {
        $ack = $this->goCheckToken();
        if (empty($ack)) {
            $response = $this->setResponseError('Bad Auth', 401)->send();
            throw new HttpResponseException($response);
        }

        $validate    = validate('BeforeSmall');
        $common_data = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->check($common_data);

        if (empty($result)) {
            $response = $this->setResponseError($validate->getError())->send();
            throw new HttpResponseException($response);
        }

        $this->_controller  = request()->controller();
        $this->unionid      = isset($common_data['unique_sm']) ? nissanDecrypt($common_data['unique_sm']) : session('net_api-unionid');
        $this->memberid     = isset($common_data['member_id']) ? nissanDecrypt($common_data['member_id']) : '';
        $this->openid       = isset($common_data['open_sm']) ? nissanDecrypt($common_data['open_sm']) : session('net_api-openid');
        $this->oneid       = isset($common_data['one_id']) ? nissanDecrypt($common_data['one_id']) : session('net_api-oneid');
        $this->channel_type = isset($common_data['channel_sm']) ? $common_data['channel_sm'] : session('net_api_channel_type');
        $this->sourcecode = isset($common_data['sourcecode']) ? $common_data['sourcecode'] : session('net_api_sourcecode');
        if(config('app_status')=='develop'){
            Logger::error('user_input-test:',['m'=>$common_data,'gversion'=>request()->header('gversion')]);
        }
//        if ($this->memberid == '60354425982951620608') {
//            $this->memberid = '60294394743205396480';
//        }
        if (!$this->unionid) {
            $response = $this->setResponseError("unionid必传")->send();
            throw new HttpResponseException($response);
        }
//        if ($this->channel_type=='PZ1ASM') $this->channel_type='GWSM';
        $this->brand = DbDlr::channel_to_brand($this->channel_type);
        $this->shelves_type = DbDlr::channel_to_shelves($this->channel_type);
        session('net_api_channel_type', $this->channel_type);
        session('net_api-unionid', $this->unionid);
        session('net_api-openid', $this->openid);
        session('net_api-brand', $this->brand);
        session('net_api-oneid', $this->oneid);
        session('net_api-member_id', $this->memberid);
        session('net_api_sourcecode', $this->sourcecode);

        $this->checkToken();
        $this->source = 1;//大部分用了这个位置，字段并没有透传过来
        $this->event_param = Request::instance()->header('eventparam');
    }

    public function goCheckToken()
    {
        if (input('test') == '123456') {
            return true;#test
        }

//        if (empty(config('market_transfer'))) {
//            return true;
//        }

        try {
            $token = Request::instance()->header('access-token');

            if (empty($token)) {
                return false;
            }
            $token_arr = explode(".", $token);
            if (count($token_arr) != 3) {
                return false;
            }
            $key  = md5(config('nissan_en_key'));
            $info = JWT::decode($token, $key, ['HS256']); //解密jwt

            if (!empty($info) && $info->exp > time()) {
                return true;
            }
            return false;
        } catch (\Exception $exception) {
            Logger::error('validate ack');
            return false;
        }
    }

    public function checkToken()
    {
//        $token = nissanDecrypt(input('unique_sm', nissanEncrypt("oYwsZv0PtBaNqQyLH-fmhdYVnwLY")));

//        $this->check_user_new($this->unionid, $this->channel_type, $this->openid, $this->memberid);
        $this->check_user($this->unionid, $this->channel_type, $this->openid, $this->memberid,$this->oneid);

        return;
    }

    /**
     * 先查member_id有没有存在；1没有存在，查unionid有没有存在;a存在则更新memberid到那个表;b不存在则新用户新建
     * 2member_id存在；查更新对应渠道的unionid
     * 3开接口给平台更新用户。
     * @param string $token
     * @param string $channel_type
     * @param string $openid
     * @param int $member_id
     * @return bool
     */
    private function check_user($token = '', $channel_type = "GWSM", $openid = '', $member_id = 0,$one_id=0)
    {
        $user       = [
            'id'            => '',
            'plat_id'       => '',
            'openid'        => '',
            'car_series_id' => '',
            'bind_unionid'  => '',
            'vin'           => '',
            'trade_phone'           => '',
        ];
        $this->user = $user;
        $time       = date('Y-m-d H:i:s');
        $user_phone='';
        if ($member_id && !in_array($member_id, config('not_mid'))) {
            $member_cache_key = 'mmm_usr_che1_' . $member_id;
            $member =  redis($member_cache_key);
            if(!$member){
                $member = Member::create('member')->member($member_id); // 获取平台详情
                redis($member_cache_key,$member,400);
            }
            $this->member_info = $member;
            if (!empty($member['oauth'][0]['unionid'])){
                $token = $member['oauth'][0]['unionid'];
            }
            $data = [];
            if ($member && isset($member['oneid'])) {
                $data   = [
                    'headimg_market'  => $member['avatar'],
                    'headimg_app'     => $member['app_avatar'],
                    'nickname_market' => $member['nickname'],
                    'nickname_app'    => $member['app_nickname'],
                    'one_id'          => $member['oneid'],
                    'mid_phone'=>$member['phone'],
                ];
                $one_id = $member['oneid'];
            }

            $user_model  = new DbUser();
            $u_sub_model = new DbUserSub();
            $mem_where   = ['plat_id' => $member_id, 'is_enable' => 1];
            $user_cache_key = 'cmn_usr_che1_' . $member_id;
//            $user_cache_key=0;
            $user  = redis($user_cache_key);
//            $user = "";//empty($user) ||
            if (!$user) {
                $user        = $user_model->getOne(['where' => $mem_where]);
                redis($user_cache_key, $user, mt_rand(1200, 1800));
            }
//            Logger::error('test-member-check-user:',['sql'=>$user_model->getLastSql(),'rr'=>$user,'unionid'=>$token,'openid'=>$openid]);
            $user_phone=$member['phone']??'';
//            var_dump($user);
//            dd($user);
            if ($user) {
                $user = $user_model->getOneByPk($user['id']);
                $user_id     = $user['id'];
                $user_cache_key_sub = 'cmn_usr_che1_sub_' . $user_id . $channel_type ;
                $user_sub  = redis($user_cache_key_sub);
                if (!$user_sub) {
                    $user_sub = $u_sub_model->getOne(['where' => ['user_id' => $user_id, 'channel_type' => $channel_type]]);
                    $user_model->saveData($data, ['id' => $user['id']]);//没必要每次都更新，还有事件推送补充的
                    redis($user_cache_key_sub, $user_sub, mt_rand(1200, 1800));
                }

                $in_up_lock = 'cmn_insert_update_' . md5($token . $openid . $channel_type . $user_id);

                if(getRedisLock($in_up_lock, mt_rand(300, 600))){
                    $user_s_data = ['creator' => 'c_user1', 'unionid' => $token, 'openid' => $openid, 'channel_type' => $channel_type, 'user_id' => $user_id];
                    if (!$user_sub) {
                        $u_sub_model->insertData($user_s_data);
                    } else {
                        unset($user_s_data['user_id']);
                        $user_s_data['modifier'] = 'c_user1';
                        $u_sub_model->saveData($user_s_data, ['user_id' => $user_id ,'channel_type' => $channel_type]);
                    }
                }

                //新建
            } else {
                //判断unionid有没有存在 刚开始只有小程序有存在后面的逻辑应该是不会有unionid没有member的情况
                //增加了小程序渠道并且小程序的memberid不存在的情况才去更新
                $un_where = ['unionid' => $token, 'is_enable' => 1];
                $user     = $user_model->getOne(['where' => $un_where]);

                $data = [];
                if ($member && isset($member['oneid'])){
                    $data = [
                        'headimg_market' => $member['avatar'],
                        'headimg_app' => $member['app_avatar'],
                        'nickname_market' => $member['nickname'],
                        'nickname_app' => $member['app_nickname'],
                        'one_id'=>$member['oneid'],
                        'mid_phone'=>$member['phone'],
                    ];
                    $one_id = $member['oneid'];
                }
                if ($user && $channel_type == "GWSM" && !$user['plat_id']) {
                    //第一次登录
                    $data = array_merge($data, ['modifier' => 'c_user2', 'dlr_code' => $channel_type, 'plat_id' => $member_id, 'last_updated_date' => $time]);
                    $user_model->saveData($data, ['id' => $user['id']]);
                    $user_id = $user['id'];
                    Queue::push('app\common\queue\Member', json_encode(['type' => 'addlist', 'user_id' => $user_id, 'created_at' => time()]), config('queue_type.member'));
                } else {
                    $data = array_merge($data, ['creator' => 'c_u_g_1', 'dlr_code' => $channel_type, 'unionid' => $token, 'plat_id' => $member_id]);
                    $user_in_lock = 'user_create_tmp_' . $channel_type . $member_id;

                    $user_id = redis($user_in_lock) ?? 0;

                    //原子操作，防并发插入
                    if (empty($user_id) && getRedisLock($user_in_lock.'-lock',8)) {
                        $user_id = $user_model->insertGetId($data);
                        redis($user_in_lock, $user_id, 5);
                    } else {
                        //捡漏查询
                        $user        = $user_model->getOne(['where' => $mem_where]);
                        $user_id     = $user['id'] ?? 0;
                    }

                }
                $user_sub    = $u_sub_model->getOne(['where'=>['user_id' => $user_id, 'channel_type' => $channel_type]]);
                if(empty($user_sub) && !empty($user_id)){
                    $u_sub_model->insertData(['creator' => 'c_u_g_s', 'unionid' => $token, 'openid' => $openid, 'channel_type' => $channel_type, 'user_id' => $user_id]);
                }
                $user = $user_model->getOneByPk($user_id);
            }
            $user['brand'] = $this->brand;
            if (!empty($user['id'])) {
                $user['channel_type']  = $this->channel_type;
                $common = new \app\common\net_service\Common();
                $common->_getVin18n($member, $user);//获取所有vin
                $user_vin               = $common->user_car_type_redis($user);
                $this->user_vin         = $user_vin['user_vin']['vin'] ?? '';
                $user['car_series_id']  = $user_vin['user_vin']['car_series_id'] ?? 0;
                $user['car_18n']        = $user_vin['user_vin']['car_config_code'] ?? 0;
                $user['18_oil_type']    = $user_vin['user_vin']['18_oil_type'] ?? 4; // 烂口汤说没有油量就默认4L
                $user['have_brand_car'] = $user_vin['user_vin']['have_brand_car'] ?? 0;
                $user['car_offline_date'] = $user_vin['user_vin']['car_offline_date'] ?? '';//vin车下线时间
                $user['is_empower']       = $user_vin['user_vin']['is_empower'] ?? 0;//是否授权车型：1是0否
                $user['user_status']       = $user_vin['user_vin']['user_status'] ?? 0;//2 是默认车
                $user['vin_list']       = $user_vin['vin_list'] ?? [];// 用户绑定vin列表
                $user['is_nev']       = $user_vin['user_vin']['is_nev'] ?? 0;//2 是默认车
            }
        } else {
            //官网临时用户
            $user_id = 0;
        }

        $user = [
            'id'            => $user_id,
            'phone'         => $user_phone,
            'name'          => $user['name'] ?? '',
            'car_series_id' => $user['car_series_id'] ?? '',
            'address_id'    => $user['address_id'] ?? '',
            'openid'        => $openid,
            'plat_id'       => $member_id,
            'bind_unionid'  => $token,
            'unionid'       => $token,
            'member_id'     => $member_id,
            'one_id'        => $one_id,
            'car_18n'       => $user['car_18n'] ?? '',
//            'car_18n'       => 'TDBALSWZ52EXA--AA-',//测试写死的用户18
            'vin'           => $this->user_vin,
            'brand'         => $this->brand,
            '18_oil_type'   => $user['18_oil_type'] ?? 4,// 烂口汤说没有油量就默认4L
            'channel_type'  => $this->channel_type,
            'staff_phone'   => $user['staff_phone'] ?? '',
            'mid_phone'   => $user['mid_phone'] ?? '',
            'is_empower'    => $user['is_empower']??0,
            'trade_phone'    => $user['trade_phone']??0,
            'user_status'    => $user['user_status']??0,
            'vin_list'    => $user['vin_list']??[],
            'is_nev'    => $user['is_nev']??0,
        ];
        if ($user['brand'] == 2) {
            $group_id = [37];
        }elseif ($user['brand'] == 3) {
            $group_id = [38];
        }else{
            $group_id = [15];
        }
        $user['group_id'] = $group_id;

        if(isset($user['is_nev']) && $user['is_nev']){
            $nev_where = ['has_nev_service_type'=>['<>',0]];
        }else{
            $nev_where = ['has_ice_service_type'=>1];
        }
        $nev_where['is_enable']=1;
        $user['nev_where'] = $nev_where;
        if($user_id && $user['user_status']==2){
            $card_r_model =  new BuCardReceiveRecord();
            //按照产品的说法，这里要判断卡券跟活动都必须是在有效期内
            $card_r_where = [
                'b.is_gift_card'=>1,'a.activity_id'=>['>',0],
                'a.validity_date_start'=>['<=',$time],
                'a.validity_date_end'=>['>=',$time],
                'act.activity_time_start'=>['<=',$time],
                'act.activity_time_end'=>['>=',$time],
                'a.status'=>7,
                'b.brand_id'=>$this->brand,'b.is_enable'=>1,'a.is_enable'=>1
            ];
            if($this->user_vin){
//                or a.user_id='%s'
                $card_r_where[]=['exp',sprintf("(a.receive_vin='%s' and (a.user_id='%s' or a.user_id=0)) || ((a.receive_vin='' || a.receive_vin is null) and a.user_id='%s')",$this->user_vin,$user_id,$user_id)];
            }else{
                $card_r_where[]=['exp',sprintf("((a.receive_vin='' || a.receive_vin is null) and a.user_id='%s')",$user_id)];

//                $card_r_where['a.user_id'] =  $user_id;
            }
            $card_r_filed = "a.card_code,a.card_id,a.status,a.is_enable,a.activity_id";
            $card_r =  $card_r_model->alias('a')->join('t_db_card b ','a.card_id=b.id')
                ->join('t_db_activity act ','act.activity_id = a.activity_id and act.activity_status_flag=1')
                ->where($card_r_where)->field($card_r_filed)->select();
//            print_json($card_r,$card_r_model->getLastSql());
            if($card_r){
                $user['card_r_gift'] = $card_r;
                $user['card_r_gift_wjh'] = [];
                foreach ($card_r as $card_r_v){
                    $user['card_r_gift_wjh'][]=$card_r_v;
                    $user['card_r_gift_card_code'][$card_r_v['card_id']][]=$card_r_v['card_code'];
                }

            }
            if($this->user_vin){
                if(getRedisLock('tmp_much_card.'.$this->user_vin,60*20)){
                    $this->much_card($this->user_vin,$this->brand);
                }
            }
        }


        if (input('test') == 1) {
            var_dump($user);
        }
        //根据绑定车主插入用户车型库
        if ($user_id > 0) {
            $this->vin_rule($user['id'], $user['bind_unionid'], $member_id);//新用户插入根据vin判断车系
        }
        session("net-api-user-info", $user);
        session("net-api-user-user-id", $user['id']);
        session("net_api-oneid", $user['one_id']);
        $this->user    = $user;
        $this->user_id = $user['id'];

        $lock = getRedisLock('tmp_port_coupon_lock:'.$user['one_id'],60*60*24);
        if ($lock) {
            // 请求卡券中心
            Queue::push('app\common\queue\Coupon', $one_id, config('queue_type.coupon'));
        }
        return true;

    }

    private function much_card($vin,$brand)
    {
//
//        $jobsLog        = new DbJobsLog();
//        $add = [
//            'queue'       => 'much-card',
//            'source_type' => $vin,
//            'data_info'   => '',
//            'modifier' => 'job',
//        ];
//        $jobId = $jobsLog->insertGetId($add);
//
//        $much_model =  new DbActivityMuchCard();
//        $to_date  = date('Y-m-d');
//        if($brand){
//            $brand_on = sprintf('b.id=a.card_id and b.brand_id=%s',$brand);
//        }else{
//            $brand_on = sprintf('b.id=a.card_id');
//
//        }
//        $where = ['b.is_enable'=>1,'a.is_enable'=>1,'b.validity_date_start'=>['<=',$to_date],'b.validity_date_end'=>['>=',$to_date]]; //
//        $list = $much_model->alias('a')->join('t_db_card b',$brand_on)->where($where)->field('b.quick_win_card_id,b.id')->group('b.quick_win_card_id')->select();
//
//        $card_kafka_service = new NetCardKafKa();
//
//        $res = [];
//        if($list) {
//            $card_ids = array_unique(array_column($list, 'id'));
//            $q_card_id_arr = array_unique(array_column($list, 'quick_win_card_id'));
//            $re = $card_kafka_service->getCouponReceiveRecord('', implode(',', $q_card_id_arr), $vin, 1, 'mch_c_kafka');
//            $res = $re->getData() ?? $re->getMessage();
//            $upd = ['result_info' => json_encode($res), 'data_info' => json_encode($q_card_id_arr), 'last_updated_date' => date('Y-m-d H:i:s')];
//            $jobsLog->where('id', $jobId)->update($upd);
//        }


        Queue::push('app\common\queue\MuchCard', ['vin'=>$vin,'brand'=>$brand], config('queue_type.much_card'));

    }

    /**
     * 先查member_id有没有存在；1没有存在，查unionid有没有存在;a存在则更新memberid到那个表;b不存在则新用户新建
     * 2member_id存在；查更新对应渠道的unionid
     * 3开接口给平台更新用户。
     * @param string $token
     * @param string $channel_type
     * @param string $openid
     * @param int $member_id
     * @return bool
     */
    private function check_user_new($token = '', $channel_type = "GWSM", $openid = '', $member_id = 0)
    {
        //增加判断TOKEN是否存在于表中，不存在就要去获取

//        $user       = [
//            'id'            => '',
//            'plat_id'       => '',
//            'openid'        => '',
//            'car_series_id' => '',
//            'bind_unionid'  => '',
//        ];
//        $this->user = $user;
//        if ($token) {
//            $user_model  = new DbUser();
//            $where       = [
//                'b.unionid'      => $token,
//                'b.channel_type' => $channel_type
//            ];
//            $field       = "b.openid,b.unionid,b.channel_type,a.plat_id,b.is_bind_flat,a.id,b.token,a.name,a.phone,a.address_id,a.car_series_id";
//            $u_sub_model = new DbUserSub();
//            //小程序现在的会员还是以unionid为主 其他渠道以member为主
//            if ($channel_type == 'GWSM') {
//                $user = $user_model->getOneByToken(['where' => $where, 'field' => $field]);
//                if (!$user) {
//                    $user_id = $user_model->insertGetId(['creator' => 'c_user', 'dlr_code' => $channel_type, 'unionid' => $token, 'plat_id' => $member_id]);
//                    $u_sub_model->insertData(['creator' => 'c_user', 'unionid' => $token, 'openid' => $openid, 'channel_type' => $channel_type, 'user_id' => $user_id]);
//                } else {
//                    $user_id = $user['id'];
//                    if ($member_id) {
//                        $user_model->saveData(['plat_id' => $member_id], ['id' => $user_id]);//小程序的meid只填一次不更改
//                    }
//                }
//            } else {
////                var_dump($member_id);die();
//                if (!empty($member_id)) {
//                    $user = $user_model->getOne(['where' => ['plat_id' => $member_id, 'is_enable' => 1]]);
//                    if ($user) {
//                        $user_id  = $user['id'];
//                        $user_sub = $u_sub_model->getOne(['user_id' => $user_id, 'unionid' => $token]);
//                        if (!$user_sub) {
//                            $u_sub_model->insertData(['creator' => 'c_user', 'unionid' => $token, 'openid' => $openid, 'channel_type' => $channel_type, 'user_id' => $user_id]);
//                        }
//
//                    } else {
//                        $user_id = $user_model->insertGetId(['creator' => 'c_u_g_', 'dlr_code' => $channel_type, 'unionid' => $token, 'plat_id' => $member_id]);
//                        $u_sub_model->insertData(['creator' => 'c_u_g_', 'unionid' => $token, 'openid' => $openid, 'channel_type' => $channel_type, 'user_id' => $user_id]);
//                        $user = $user_model->getOneByPk($user_id);
//
//                    }
//                } else {
//                    //官网临时用户
//                    $user_id = 0;
//                }
//            }
//            $user = [
//                'id'            => $user_id,
//                'phone'         => $user['phone'] ?? '',
//                'name'          => $user['name'] ?? '',
//                'car_series_id' => $user['car_series_id'] ?? '',
//                'address_id'    => $user['address_id'] ?? '',
//                'openid'        => $openid,
//                'plat_id'       => $member_id,
//                'bind_unionid'  => $token,
//                'unionid'       => $token,
//                'member_id'     => $member_id,
//            ];
//            if (input('test') == 1) {
//                var_dump($user);
//            }
//            if ($user_id > 0) {
//                $this->vin_rule($user['id'], $user['bind_unionid'], $member_id);//新用户插入根据vin判断车系
//            }
//            session("net-api-user-info", $user);
//            session("net-api-user-user-id", $user['id']);
//            $this->user    = $user;
//            $this->user_id = $user['id'];
//            return true;

//
//            //mmid逻辑需要改 传member_id证明平台有数据
//            if (!empty($member_id)) {
//                $user_im = $user_model->getOne(['where' => ['plat_id' => $member_id, 'is_enable' => 1]]);
//                if ($user_im) {
////                    $user_sub =  $user_model
//                    $user_id  = $user_im['id'];
//                    $user_sub = $u_sub_model->getOne(['user_id' => $user_id, 'unionid' => $token]);
//                    if (!$user_sub) {
//                        $u_sub_model->insertData(['creator' => 'c_user', 'unionid' => $token, 'openid' => $openid, 'channel_type' => $channel_type, 'user_id' => $user_id]);
//                    }
//
//                } else {
//                    $user_id = $user_model->insertGetId(['creator' => 'c_user', 'dlr_code' => $channel_type, 'unionid' => $token, 'plat_id' => $member_id]);
//                    $u_sub_model->insertData(['creator' => 'c_user', 'unionid' => $token, 'openid' => $openid, 'channel_type' => $channel_type, 'user_id' => $user_id]);
//                }
//
//            } else {
//                if ($this->channel_type == 'GWSM') {
//                    $user = $user_model->getOneByToken(['where' => $where, 'field' => $field]);
//                    if (!$user) {
//                        $user_id = $user_model->insertGetId(['creator' => 'c_user', 'dlr_code' => $channel_type, 'unionid' => $token]);
//                        $u_sub_model->insertData(['creator' => 'c_user', 'unionid' => $token, 'openid' => $openid, 'channel_type' => $channel_type, 'user_id' => $user_id]);
//                    } else {
//                        $user_id = $user['id'];
//                    }
//                } else {
//                    $user_id = 0;
//                }
//            }
//
//            $user = [
//                'id'            => $user_id,
//                'openid'        => $openid,
//                'plat_id'       => $member_id,
//                'bind_unionid'  => $token,
//                'unionid'       => $token,
//                'car_series_id' => '',
//            ];
//
//            $this->vin_rule($user['id'], $user['bind_unionid']);//新用户插入根据vin判断车系
//        }
//
//
//        return true;
    }


    /**
     * 车主信息
     * @param int $channel_type
     * @return array|bool|mixed
     */
    public function _getCarer($bind_unionid = "", $memberid = 0,$one_id = 0, $channel_type = 'GWSM')
    {

        $common = new \app\common\net_service\Common();
        return $common->_getCarer($bind_unionid, $memberid,$one_id,$channel_type);
    }


    /**
     * 通过vin分析车型
     * vin换18位码
     * 18位查库替换车型，
     * 新用户首次进线
     *
     */

    private function vin_rule($user_id, $bind_unionid, $memberid = 0)
    {
        return false;
        $this->user_id = $user_id;
        $carer         = $this->_getCarer($bind_unionid, $memberid);
        $vin_model     = new DbVinRule();
        $sys_model     = new DbSystemValue();
        $car_model     = new DbCarSeries();
        $user_model    = new DbUser();
//        var_dump($this->user_id);
        $user = $user_model->getOne(array('where' => array('id' => $this->user_id, 'is_enable' => 1)));
        if (!$user) {
            return false;
        }
        if ($user['car_series_id']) {
            return false;
        }
        if (empty($carer)) {
            return false;
        }

        $vin   = $carer['vin'];
        $digit = 4;
        $code  = substr($vin, $digit - 1, 1);
        if ($code != 'G') {
            $car_name = $vin_model->getOne(array('where' => array('digit' => $digit, 'code' => $code, 'is_enable' => 1)));
            if ($car_name) {
                $car_name = $car_name['name'];
            } else {
                return false;
            }
        } else {
            $code = substr($vin, 4 - 1, 1);
            if ($code == 1) {
                $car_name = '颐达';
            } else {
                $car_name = '骐达';
            }
        }
        $value_code = $sys_model->getOne(array('where' => array('is_enable' => 1, 'county_name' => $car_name, 'value_type' => 1, "(value_code REGEXP '[^0-9.]')" => 0)));

        $digit    = 6;
        $code     = substr($vin, $digit - 1, 1);
        $dis_code = $vin_model->getOne(array('where' => array('digit' => $digit, 'code' => $code, 'is_enable' => 1)));
        $digit    = 10;
        $code     = substr($vin, $digit - 1, 1);
        $age      = $vin_model->getOne(array('where' => array('digit' => $digit, 'code' => $code, 'is_enable' => 1)));
        if ($value_code && $dis_code && $age) {
            $car = $car_model->getOne(array('where' => array('car_series_code' => $value_code['value_code'], 'displacement' => $dis_code['name'], 'age' => $age['name'], 'is_enable' => 1)));
            if ($car) {
                $user_car_model = new DbUserCarSeries();
                $res            = '';
                if (!$user) {
                    $data = array(
                        'car_series_id' => $car['id'],
                        'modifier'      => 'vin_rule2'
                    );
                    $res  = $user_model->saveData($data, array('id' => $this->user_id, 'is_enable' => 1));
                } else {
                    if (!$user['car_series_id']) {
                        $data = array(
                            'car_series_id' => $car['id'],
                            'modifier'      => 'vin_rule3'
                        );
                        $res  = $user_model->saveData($data, array('id' => $this->user_id, 'is_enable' => 1));
                    }
                }
                if ($res) {
                    $data = array(
                        'openid'        => $this->unionid,
                        'user_id'       => $this->user_id,
                        'car_series_id' => $car['id'],
                        'modifier'      => "common",
                        'is_vin_car'    => 1
                    );
                    $user_car_model->insertData($data);
                }
                return $res;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }






//    /**
//     * 生成订单号
//     */
//    protected function _getOrderNo($preStr = '', $length = 7)
//    {
//        $chars = "0123456789";
//        $str   = "";
//        for ($i = 0; $i < $length; $i++) {
//            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
//        }
//
//        return $preStr . $str;
//    }

//    //专营店信息
//    protected function _getDlr($dlr_code)
//    {
//        $dlr_model = new DbDlr();
//        $dlr       = $dlr_model->getOne(['where' => ['dlr_code' => $dlr_code]]);
//        return $dlr;
//    }

//    //获取基础数据信息
//    protected function _getSysName($type, $value_code)
//    {
//        $sys_model = new DbSystemValue();
//        $sys       = $sys_model->getOne(array('where' => array('value_type' => $type, 'value_code' => $value_code, 'is_enable' => 1)));
//        if ($sys) {
//            return $sys['county_name'];
//        } else {
//            return false;
//        }
//    }

//
//    //返回是否可以续团
//    protected function _checkGroup($group_id, $group_order_code = '', $order_code = '', $goods_id)
//    {
//        $group_model = new DbFightGroup();
//        $time        = date('Y-m-d H:i:s');
//
//        $group_info = $group_model->getGroupInfo([
//            'where' =>
//                ['a.id' => $group_id, 'a.is_enable' => 1,
////            'a.start_time'=>["<=",$time],
////            'a.end_time'=>[">=",$time]
//                ],
//            'field' => "a.id,a.title,a.start_time,a.end_time,a.people_number,a.purchase_number,a.buy_hour,a.rule,b.commodity_id,b.lowest_price"
//        ]);
//        if ($group_info) {
//            $ac_group_model = new AcGroup();
//            if ($order_code) {
//                $ac_where = ['group_code' => $group_order_code, 'commodity_id' => $goods_id, 'user_id' => $this->user_id, 'status' => ['in', '3,4,5']];
//                $ac       = $ac_group_model->getOne(['where' => $ac_where]);
//                //不能参与自己的团购
//                if ($ac) {
//                    return false;
//                }
//            }
//
//            //参与
//            if ($group_order_code) {
//                //状态:1下单，2待支付，3已支付,4待发货,5已发货,6已退款,7已取消
//                //待发货,已发货直接fasle
//                $f_where       = ['group_code' => $group_order_code, 'commodity_id' => $goods_id, 'status' => ['in', '4,5']];
//                $f_group_order = $ac_group_model->getOne(['where' => $f_where]);
//                if ($f_group_order) {
//                    return false;
//                }
//
//                $where            = ['group_code' => $group_order_code, 'commodity_id' => $goods_id, 'status' => ['in', '1,2,3']];
//                $group_order      = $ac_group_model->getList(['where' => $where]);
//                $where['status']  = ['in', '2,3,4,5'];//2 在支付页面的状态也包含进来
//                $where['user_id'] = ['<>', $this->user_id];
//                $count            = $ac_group_model->getCount(['where' => $where]);
//                if (!$group_order) {
//                    //没有成团的订单
//                    return false;
//                } elseif ($count >= $group_info['people_number']) {
//                    //参团人数
//                    return false;
//                } elseif (strtotime(sprintf("%s +%s hours", $group_order[0]['group_start_time'], $group_info['buy_hour'])) < time()) {
//                    //参团过期
//                    return false;
//                }
//            }
//            return true;
//        } else {
//            return false;
//        }
//    }

//    /**
//     * 是否预售及预售信息
//     * @param $commodity
//     * @param $pre_id 预售ID
//     * @param $dj 1支付定金2 支付尾款!
//     * @return array|bool|false|\PDOStatement|string|\think\Model
//     */
//    protected function _pre_sale($commodity, $pre_id, $dj = 0)
//    {
//        $pre_sale_com_model = new DbPreSaleCommodity();
//        $order_model        = new BuOrder();
//        $today              = date('Y-m-d H:i:s');
//        $where              = [
//            'a.commodity_id' => $commodity,
//            'b.id'           => $pre_id,
//            'a.is_enable'    => 1,
//            'b.is_enable'    => 1,
//        ];
//        if ($dj == 1) {
//            $where["b.front_s_time"] = ['<=', $today];
//            $where["b.front_e_time"] = ['>=', $today];
//        }
//        if ($dj == 2) {
//            $where["b.balance_s_time"] = ['<=', $today];
//            $where["b.balance_e_time"] = ['>=', $today];
//        }
//
//        $fileds   = "b.purchase_number,b.front_money,b.dedu_money,b.id,b.can_use_card,b.dec,b.front_s_time,b.front_e_time,b.balance_s_time,b.balance_e_time";
//        $pre_sale = $pre_sale_com_model->GetSalePreInfo(['where' => $where, 'field' => $fileds]);
//        $can_buy  = 999;
//        if ($pre_sale) {
//            $pre_sale['pre_status'] = 0;//0不在任何支付时间内 1定金支付时间 2尾款支付时间
//            if ($today >= $pre_sale['front_s_time'] && $today <= $pre_sale['front_e_time']) {
//                $pre_sale['pre_status'] = 1;
//            }
//            if ($today >= $pre_sale['balance_s_time'] && $today <= $pre_sale['balance_e_time']) {
//                $pre_sale['pre_status'] = 2;
//            }
//            if ($pre_sale['purchase_number'] <> 0) {
//                $o_g_where = ['a.pre_sale_id' => $pre_sale['id'], 'b.commodity_id' => $commodity, 'a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8']];//不包括1、已下单；3、已取消；5、已退款；6、已过期；8、未支
//                $o_g_count = $order_model->getOneJoin(['where' => $o_g_where, 'field' => "sum(b.count) number"]);
//                if ($o_g_count) {
//                    $can_buy = $pre_sale['purchase_number'] - $o_g_count['number'];
//                }
//            }
//            if (!$pre_sale['dec']) {
//                $pre_sale['dec'] = "暂无描述";
//            }
//            $pre_sale['yh_money'] = $pre_sale['dedu_money'] - $pre_sale['front_money'];//优惠金额
//            $pre_sale['can_buy']  = $can_buy;
//            return $pre_sale;
//        } else {
//            return false;
//        }
//    }

//    /**
//     * 商品卡券
//     * @param $goods_card_where
//     * @param $field
//     * @return bool|false|mixed|\PDOStatement|string|\think\Collection
//     */
//    protected function _commodityCard($goods_card_where, $field)
//    {
//        $key  = 'detail_commodity_card_' . json_encode($goods_card_where);
//        $data = redis($key);
//
//        if (empty($data)) {
//            $goods_card_model = new DbCard();
//            $goods_card       = $goods_card_model->getCommodityCard(['where' => $goods_card_where, 'field' => $field]);
////            echo $goods_card_model->getLastSql();die();
//
//            $data = $goods_card;
//            redis($key, $data, 60);
//        }
//
//        return $data;
//    }

//    protected function _checkNDisById($commodity_id, $n_dis_id = '')
//    {
//        $n_dis_goods_model = new DbNDiscountCommodity();
//        $time              = date('Y-m-d H:i:s');
//        $where             = ['b.start_time' => ['<=', $time], 'b.end_time' => ['>=', $time], 'b.is_enable' => 1, 'a.is_enable' => 1, "a.commodity_id" => $commodity_id
//        ];
//        if ($n_dis_id) {
//            $where['b.id'] = $n_dis_id;
//        }
//
//        $param['where'] = $where;
//        $param['field'] = "a.n_id,a.commodity_id,b.title,b.des";
//        $row            = $n_dis_goods_model->getNdisInfo($param);
//        return $row;
//    }

    /**
     * 运费模板公用方法
     */
    protected function _mail_price($order_code, $address, $action = 'mail_ajax')
    {

        $com = new \app\common\net_service\Common();
        $res = $com->_mail_price($order_code, $address, $action);
        return $res;
//        $add   = explode(' ', $address)[0];
//        $pro   = mb_substr($add, 0, 2, 'utf-8');//截取前两个字符
//        $model = new DbArea();
//        $area  = $model->getOne(['where' => ['area_name' => ['like', $pro . "%"], 'area_parent_id' => 0], 'field' => "area_id"]);
////        echo $model->getLastSql();
//        if (!$area) {
//            Logger::error('mail_price-wrong-pro:', ['pros' => $address, 'pro' => $pro]);
//            return ['error' => 1, 'msg' => '省市选择错误'];
////            print_json(1,'省市选择错误');
//        }
//
//        $sql = sprintf("SELECT a.count,d.first_count,d.first_fee,d.continue_count,d.continue_fee from t_bu_order_commodity a
//JOIN t_db_commodity_flat b on a.commodity_id =b.commodity_id and find_in_set(a.dlr_code,b.up_down_channel_dlr)
//JOIN t_db_commodity_set c on b.commodity_set_id=c.id
//LEFT  JOIN t_db_freight_template d on c.template_guid=d.guid
//where a.order_code='%s' and FIND_IN_SET('%s',d.regionid_list)", $order_code, $area['area_id']);
////        echo  $sql; die();
//        $list  = $model->query($sql);
//
//        $price = 0;
//        if ($list) {
//            foreach ($list as $v) {
//                if ($v['first_count']) {
//                    if ($v['count'] <= $v['first_count']) {
//                        $price += $v['first_fee'];
//                    } else {
//                        $price += $v['first_fee'] + ($v['count'] - $v['first_count']) / $v['continue_count'] * $v['continue_fee'];
//                    }
//                }
//            }
//            return ['error' => 0, 'msg' => 'ok', 'data' => $price];
//        } else {
//            Logger::error('mail_price_error', ['pro' => $pro, 'add' => $add, 'action' => $action, 'sql' => $sql]);
//            return ['error' => 1, 'msg' => '运费出错，请联系客服'];
//        }
    }


//    /**
//     * 商品ID获取运费
//     */
//    protected function _mail_price_by_goods($goods_id, $address, $action = 'mail_ajax')
//    {
//        $add   = explode(' ', $address)[0];
//        $pro   = mb_substr($add, 0, 2, 'utf-8');//截取前两个字符
//        $model = new DbArea();
//        $area  = $model->getOne(['where' => ['area_name' => ['like', $pro . "%"], 'area_parent_id' => 0], 'field' => "area_id"]);
////        echo $model->getLastSql();
//        if (!$area) {
//            Logger::error('mail_price-wrong-pro:', ['pros' => $address, 'pro' => $pro]);
//            return ['error' => 1, 'msg' => '省市选择错误'];
//        }
//        $sql  = sprintf("SELECT d.first_count,d.first_fee,d.continue_count,d.continue_fee from t_db_commodity a
//LEFT  JOIN t_db_freight_template d on a.template_guid=d.guid
//where a.id='%s' and FIND_IN_SET('%s',d.regionid_list)", $goods_id, $area['area_id']);
//        $list = $model->query($sql);
//        if ($list) {
//            $price = isset($list[0]['first_fee']) ? $list[0]['first_fee'] : 999;
//            return ['error' => 0, 'msg' => 'ok', 'data' => $price];
//        } else {
//            Logger::error('mail_price_error', ['pro' => $pro, 'add' => $add, 'action' => $action, 'sql' => $sql]);
//            return ['error' => 1, 'msg' => '运费出错，请联系客服'];
//        }
//    }

//
//    //卡券商品可领卡券
//    protected function card_order_card_id($order_code)
//    {
//        //获取卡券
//        $orde_goods_model = new BuOrderCommodity();
//        $order_goods      = $orde_goods_model->getOne(['where' => ['order_code' => $order_code]]);
//        $cards            = $order_goods['card_ids'];
//        $card_id_arr      = [];
//        $card_id          = '';
//        $dlr_code         = '';
//        $card_js          = [];
//        if ($cards) {
//            $card_arr = explode(',', $cards);
//
//            foreach ($card_arr as $v) {
//                $card_r_model = new BuCardReceiveRecord();
//                $card_model   = new DbCard();
//                $card_info    = $card_model->getOneByPk($v);
//                if ($card_info) {
//                    $dlr_code = $card_info['dlr_code'];
//                    $card_r   = $card_r_model->getCount(['where' => ['status' => ['in', "1,3,4"], 'card_id' => $v, 'act_id' => $order_code, 'user_id' => $this->user_id]]);
//                    if ($order_goods['count'] > 0) {
//                        if ($card_r < $order_goods['count']) {
//                            for ($i = 1; $i <= $order_goods['count'] - $card_r; $i++) {
//                                $card_id_arr[]       = $v;
//                                $card_id             .= $v . ',';
//                                $card_js[]['cardid'] = $card_info['card_id'];
//                            }
//                        }
//                    }
//                }
//            }
//        }
//
//        return ['card_id_arr' => $card_id_arr, 'dlr_code' => $dlr_code, 'cards' => $card_js, 'card_id' => trim($card_id, ','), 'count' => $order_goods['count']];//主键，专营店，生成JSSDK数组
//    }
//
//    /**
//     * 计算活动优惠
//     * @param $order_code
//     */
//    protected function yh_js($order_code)
//    {
//        $order_model = new BuOrder();
//        $where       = ['order_code' => $order_code];
//        $field       = "a.order_code,d.n_dis,d.group_dis,d.pre_sale,d.suit_dis,d.full_dis,d.point_dis,d.commodity_id,b.count,b.price";
//        $list        = $order_model->getListFlat(['where' => $where, 'field' => $field]);
//        if ($list) {
//            foreach ($list as $k => $v) {
//
//
//            }
//        }
//

//    }

//    /**
//     * 取消团购订单
//     * @param $order_code
//     * @return $this|bool
//     */
//    protected function cacel_group($order_code)
//    {
//        $order_model    = new BuOrder();
//        $group_ac_model = new AcGroup();
//
//        $where  = ['order_code' => $order_code];
//        $data   = array(
//            'order_status'      => 3,
//            'last_updated_date' => date('Y-m-d H:i:s'),
//
//        );
//        $res    = $order_model->saveData($data, $where);
//        $g_data = array(
//            'status'            => 7,//1下单，2待支付，3已支付,4待发货,5已发货,6已退款(没有),7已取
//            'last_updated_date' => date('Y-m-d H:i:s'),
//        );
//        $res    = $group_ac_model->saveData($g_data, $where);
//        return $res;
//    }

//
//    //N件N折折扣信息  多少名按下单人数，订单数量折扣按照商品总数量
//    protected function getNDisCount($com_ids, $counts, $n_id='')
//    {
//        $n_dis_goods_model = new DbNDiscountCommodity();
//        $time              = date('Y-m-d H:i:s');
//        $where             = ['b.start_time' => ['<=', $time], 'b.end_time' => ['>=', $time], 'b.is_enable' => 1, 'a.is_enable' => 1, "a.commodity_id" => ['in', $com_ids]
//        ];
//        if($n_id){
//            $where['b.id']=$n_id;
//        }
//        $param['where']    = $where;
//        $param['group']    = "b.id";
//        $param['field']    = "GROUP_CONCAT(a.commodity_id SEPARATOR ',') g_ids,b.id";
//        $row               = $n_dis_goods_model->getNdisInfo($param);
//        $dis_info = [];
//        if ($row) {
//            $com_counts     = [];
//            $com_ids_arr    = explode(',', $com_ids);
//            $com_counts_arr = explode(',', $counts);
//            foreach ($com_ids_arr as $kk => $vv) {
//                foreach ($com_counts_arr as $kkk => $vvv) {
//                    if ($kk == $kkk) {
//                        $com_counts[] = [
//                            'goods' => $vv,
//                            'count' => $vvv,
//                        ];
//                    }
//                }
//            }
//            //判断多少折扣
//            $order_model       = new BuOrder();
//            $count_order_where = [
//                'order_status' => ['not in', [1, 3, 5, 6, 8, 10]],
//                'sale_source'  => 5,
//                'n_dis_id'     => $row['id'],
//                'dlr_code'     => $this->channel_type,
//            ];
//            $order_count       = $order_model->getCount(['where' => $count_order_where]);//统计该活动订单数
//
//            $n_dis_info_model = new DbNDiscount();
//            $ac_goods_id      = explode(',', $row['g_ids']);
//            $g_count_real     = 0;//活动订单商品数
//            foreach ($com_counts as $cc) {
//                if (in_array($cc['goods'], $ac_goods_id)) {
//                    $g_count_real += $cc['count'];
//                }
//            }
//            $n_dis_info_list = $n_dis_info_model->getList(['where' => ['n_id' => $row['id'], 'is_enable' => 1], 'field' => 'p_number', 'group' => "p_number"]);
//            if ($n_dis_info_list) {
//                $where = [
//                    'n_id' => $row['id'], 'piece' => ['<=', $g_count_real]
//                ];
//                //设置了两条大规则 else 设定了一条大规则
//                if (isset($n_dis_info_list[1]['p_number'])) {
//                    if ($n_dis_info_list[1]['p_number'] > $order_count) {
//                        $where['p_number'] = $n_dis_info_list[0]['p_number'];
//                    } else {
//                        $where['p_number'] = $n_dis_info_list[1]['p_number'];
//                    }
//                } else {
//                    $where['p_number'] = $n_dis_info_list[0]['p_number'];
//                }
//
//
//                $order_by = "p_number desc,piece desc";
//                $field    = "p_number,piece,discount,n_id";
//                $param    = [
//                    'where' => $where,
//                    'order' => $order_by,
//                    'field' => $field,
//                ];
//                //获得购买次数最高 ，购买数量最多的折扣信息
//                $dis_info = $n_dis_info_model->getOne($param);
//                if ($dis_info) {
//                    $dis_info['g_dis']        = $ac_goods_id;
//                    $dis_info['discount_per'] = $dis_info['discount'] / 10;
//                }
//            }
//        }
//        return $dis_info;
//        //todo
//    }
//
//    /**
//     * 优惠套装
//     * 缓存 1 min
//     * @param $id
//     * @return array
//     */
//    protected function _suit($id, $commodity_id)
//    {
//        $time_now = strtotime(date("Y-m-d H:i"));
//
//        $key  = 'detail_suit_cheap_' . $this->channel_type . $commodity_id . $time_now . '_' . $id;
//        $data = redis($key);
//        $suit_price=0;
//        $suit_yh_price=0;
//        if (empty($data)) {
//            $suit_list  = [];
//            $suit_count = 0;
//            $suit_model = new BuCheapSuitIndex();
//            $suit_where = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) and b.commodity_id='%s' and a.s_time<='%s' and a.e_time>='%s'", $this->channel_type, $commodity_id, $time_now, $time_now);
////            echo $suit_where; die();
//            $if_suit = $suit_model->getOneUsuit(['_where' => $suit_where, 'field' => "index_id"]);
//            if ($if_suit) {
//                $suit_where = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) and b.index_id='%s'", $this->channel_type, $if_suit['index_id']);
//                $suit_list  = $suit_model->getListUsuitFlat(['_where' => $suit_where, 'field' => "index_id,b.commodity_id,flat.cover_image,b.price,d.price old_price", 'group' => "commodity_id"]);
//                if ($suit_list) {
//                    foreach ($suit_list as $k => $v) {
//                        $suit_price +=$v['price'];
//                        $suit_yh_price +=$v['old_price']-$v['price'];
//                        $suit_list[$k]['url'] = url('suit', ['id' => $id]);
//                    }
//                }
//                $suit_count = count($suit_list);
//            }
//            $data = ['suit_list' => $suit_list, 'suit_count' => $suit_count,'suit_price'=>$suit_price,'suit_yh_price'=>$suit_yh_price];
//            redis($key, $data, 60);
//        }
//
//        return $data;
//    }
//
//    /**
//     * 满优惠
//     * @param $commodity_set_id
//     * @return bool|false|mixed|\PDOStatement|string|\think\Collection
//     */
//    protected function _full($commodity_id)
//    {
//        $time_now = date("Y-m-d H:i:00");
//
//        $key  = 'detail_full_' . $this->channel_type . $commodity_id . $time_now;
//        $data = redis($key);
//
//        if (empty($data)) {
//            $full_model = new DbFullDiscount();
//            $full_where = [
//                'b.commodity_id' => $commodity_id,
//                'b.dlr_code'     => $this->channel_type,
//                'a.start_time'   => ['<=', $time_now],
//                'a.end_time'     => ['>=', $time_now]
//            ];
//
//            $field     = "a.id,a.money,a.money,a.is_preferential_money,a.is_preferential_card,a.preferential_money,a.preferential_card_id,b.commodity_id,a.activity_title,a.full_discount_rules";
//            $full_list = $full_model->getListU(['where' => $full_where, 'field' => $field]);
////            echo $full_model->getLastSql();die();
//            if ($full_list) {
//                foreach ($full_list as $k => $v) {
//                    $full_list[$k]['word'] = $v['activity_title'];
//                    $full_list[$k]['url']  = url('search', ['full_cut_id' => $v['id']], false, true);
//                }
//            }
//            $data = $full_list;
//            redis($key, $data, 60);
//        }
//
//        return isset($data[0])?$data[0]:[];
//    }
//
//    /**
//     * 团购信息
//     * @param $id
//     * @param $group_id
//     * @return array|bool|false|mixed|\PDOStatement|string|\think\Model|null
//     */
//    protected function _group($id, $group_id, $commodity_id)
//    {
//        $time_now = date("Y-m-d H:i:00");
//
//        $key  = 'detail_group_' . $this->channel_type . $commodity_id . $time_now;
//        $data = redis($key);
////        $data='';
//        if (empty($data)) {
//            $group_model = new DbFightGroup();
//            $g_where = ['b.commodity_id' => $commodity_id, 'a.is_enable' => 1, 'a.start_time' => ["<=", $time_now], 'a.end_time' => [">=", $time_now]];
//            if ($group_id) {
//                $g_where['a.id']=$group_id;
//            }
//            $g_info  = $group_model->getGroupInfo(['where' => $g_where, 'field' => "a.id"]);
//
//            $data = $g_info;
//            redis($key, $data, 60);
//        }
//
//        return $data;
//    }
//
//    /**
//     * 限时折扣 通过商品ID+上下架渠道去做
//     * @param $commodity_id
//     * @return array|bool|mixed
//     */
//    protected function _limit($commodity_id,$limit_id='')
//    {
//        $com =  new \app\common\net_service\Common();
//        return $com->_limit($commodity_id,$limit_id,$this->user_id,$this->channel_type);
//        $time_now    = date("Y-m-d H:i:00");
//        $order_model = new BuOrder();
//        $key         = 'detail_limit_' . $this->channel_type . $commodity_id.$limit_id . $time_now;
//        $data        = redis($key);
//
//        if (empty($data)) {
//            $limit_model     = new DbLimitDiscount();
//            $l_where         = [
//                'a.start_time'   => ['<=', $time_now],
//                'a.end_time'     => ['>=', $time_now],
//                'b.commodity_id' => $commodity_id
//            ];
//            if($limit_id){
//                $l_where['a.id']=$limit_id;
//            }
//            $_l_where        = sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr) ", $this->channel_type);
//            $limit_info      = $limit_model->getGroupInfo(['where' => $l_where, 'field' => "a.id,a.title,a.start_time,a.end_time,a.purchase_number,a.des", '_where' => $_l_where]);
//            $limit_stop_time = '';
//            $can_number      = 999;
//            if ($limit_info) {
//                if ($limit_info['purchase_number'] == 0) {
//                    $limit_info['purchase_number'] = 999;
//                }
//                $o_g_where       = ['b.limit_id' => $limit_info['id'], 'a.dlr_code' => $this->channel_type, 'b.commodity_id' => $commodity_id, 'a.user_id' => $this->user_id, 'a.order_status' => ['NOT IN', '1,3,5,6,8']];//不包括1、已下单；3、已取消；5、已退款；6、已过期；8、未支
//                $o_g_count       = $order_model->getOneJoin(['where' => $o_g_where, 'field' => "sum(count) number"]);
//                $can_number      = $limit_info['purchase_number'] - $o_g_count['number'];
//                $limit_stop_time = date('Y/m/d H:i:s', strtotime($limit_info['end_time']));
//            }
//            $data = ['stop_time' => $limit_stop_time, 'can_no' => $can_number, 'limit_info' => $limit_info];
//            redis($key, $data, 60);
//        }
//
//        return $data;
//    }
//
//    /**
//     * NN促销
//     * @param $com_set_id
//     * @return array|bool|false|mixed|\PDOStatement|string|\think\Model|null
//     */
//    protected function _nn($com_id)
//    {
//        $time_now = date("Y-m-d H:i:00");
//
//        $key  = 'detail_nn_' . $this->channel_type . $com_id . $time_now;
//        $data = redis($key);
//
//        if (empty($data)) {
//            $n_dis_goods_model = new DbNDiscountCommodity();
//            $where             = [
//                'b.start_time'   => ['<=', $time_now],
//                'b.end_time'     => ['>=', $time_now],
//                'b.is_enable'    => 1,
//                'a.is_enable'    => 1,
//                "a.commodity_id" => $com_id
//            ];
//
//            $param['where'] = $where;
//            $param['field'] = "a.n_id,a.commodity_id,b.title,b.des";
//            $row            = $n_dis_goods_model->getNdisInfo($param);
//            $data           = $row;
//            redis($key, $data, 60);
//        }
//
//        return $data;
//    }
//
//
//    /**
//     * 选择优惠券对应商品优惠信息
//     * @param $cards 多个
//     * @param $order_code
//     * @return false|\PDOStatement|string|\think\Collection
//     */
//    protected function _goods_card_info($cards, $order_code)
//    {
//        $order_goods_model = new BuOrderCommodity();
//        $where             = ['a.order_code' => $order_code, 'b.id' => ['in', $cards]];
//        $where[]           = ['exp', sprintf("FIND_IN_SET('%s',apply_dlr_code)", $this->channel_type)];
//        $field             = "a.commodity_id,a.count,c.card_type,c.card_quota,c.card_discount,c.least_cost,a.price,c.id card_id";
//        $list              = $order_goods_model->orderGoodsCard(['where' => $where, "field" => $field]);
//        $list_group        = $order_goods_model->orderGoodsCard(['where' => $where, "field" => "c.id card_id,sum(c.price*a.count) as s_money", 'group' => "c.id"]);
//        if ($list) {
//            foreach ($list as $k => $v) {
//                if ($v['type'] == 1) {
//                    foreach ($list_group as $vv) {
//                        if ($vv['card_id'] == $v['card_id']) {
////                            $list[$k]['all_money'] =$vv['s_money'];
//                            $list[$k]['yh_money'] = round($v['price'] / ($vv['s_money']) * $v['card_quota'], 2);
////                            $list[$k]['yh_dj_money'] =  round($v['price']/($vv['s_money'])*$v['card_quota'],2);
//                        }
//                    }
//                } else {
//                    $list[$k]['yh_money'] = round($v['price'] * (1 - $v['card_discount']), 2);
//                }
//            }
//        }
//        return $list;
//    }
//
//    /**
//     * 订单号对应能使用哪些卡券
//     * @param $order_code
//     * @return false|\PDOStatement|string|\think\Collection
//     */
//    protected function _order_goods_use_card($order_code)
//    {
//        $order_goods_model = new BuOrderCommodity();
//        $where             = ['a.order_code' => $order_code, 'e.user_id' => $this->user_id];
//        $where[]           = ['exp', sprintf("FIND_IN_SET('%s',apply_dlr_code)", $this->channel_type)];
//        $field             = "c.*";
//        $list              = $order_goods_model->orderGoodsUseCard(['where' => $where, "field" => $field]);
//        $card_list         = [];
//        if ($list) {
//            $card_list = $this->_checkUserCard($list, 2);
//            if ($card_list) {
//                foreach ($card_list as $k => $v) {
//                    if ($v['available_count'] < 1) {
//                        unset($card_list[$k]);
//                    } else {
//                        $card_list[$k]['card_date'] = $v['validity_date_start'] . '~' . $v['validity_date_end'];
//                    }
//                }
//            }
//        }
//        return $card_list;
//    }


//    /**
//     * 商品对应领取那些卡券,隔开多个
//     * @param $commodity_ids
//     * @param int $is_get
//     * @return array
//     */
//    protected function _goods_can_get_card($commodity_ids, $is_get = 1)
//    {
//        //8.商品卡券信息
//        $goods_card_where = [
//            "b.commodity_set_id" => ['in', $commodity_ids],
//            "a.type"=>2,
//        ];
//        if ($is_get == 1) {
//            $goods_card_where['b.is_can_receive'] = 1;
//        }
////        $goods_card_where[] = ['exp', sprintf("FIND_IN_SET('%s', b.dlr_code)", $this->channel_type)];
//
//        $goods_card = $this->_commodityCard($goods_card_where, "a.*");
//
//        $card_list  = [];
//        if ($goods_card) {
//            $card_list = $this->_checkUserCard($goods_card, $is_get);
//            if ($card_list) {
//                foreach ($card_list as $k => $v) {
//                    if ($v['available_count'] < 1) {
//                        unset($card_list[$k]);
//                    } else {
//                        $card_list[$k]['card_date'] = $v['validity_date_start'] . '~' . $v['validity_date_end'];
//                    }
//                }
//            }
//        }
//        return array_values($card_list);
//    }


//    /**
//     * 查询用户已领卡券及可领取卡券
//     * STATE卡券状态：0正常 1核销 2删除 3转赠,9可领取
//     * $type 1领取，2使用
//     * $v['validity_date_start'] . '--' . $v['validity_date_end'] 必须重新处理
//     */
//
//    protected function _checkUserCard($cards, $type)
//    {
//        if (!$cards) {
//            return false;
//        }
//        $card_res = array();
//        $card_arr = array();
//        foreach ($cards as $key => $v) {
//            $card_can_r = 1;
//            $res        = $this->_checkCardEvent($v['id'], $this->user_id);
//            if ($res) {
//                //2或者1都加入判断
//                if ($res['status'] < 3) {
//                    $res['status'] = 0;
//                }
//                if ($res['status'] != 2) {
//                    $card_can_r = 0;//已经领过的了就不能领了的意思
//                }
//            }
//            if ($type == 2) {
//                if ($res && $res['status'] == 1) {
//                    if ($v['date_type'] == 2) {
//                        //加入判断领取多少天之后起效多少天失效
//                        $fixed_end_date   = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $res['created_date'], $v['fixed_term'])));
//                        $fixed_begin_date = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $res['created_date'], $v['fixed_begin_term'])));
//
//                        if (!in_array($v['id'], $card_arr) && date('Y-m-d H:i:s') > $fixed_begin_date && date('Y-m-d H:i:s') < $fixed_end_date) {
//                            $card_res[$key] = $v;
//                        }
//                    } else {
//                        if (!in_array($v['id'], $card_arr)) {
//                            $card_res[$key] = $v;
//                        }
//                    }
//
//                    $card_arr[] = $v['id'];//验证是否已经插入过了过滤重复的卡券
//                }
//            } elseif ($type == 1) {//可领取卡券
//                if ($card_can_r == 1) {
//                    $card_res[$key] = $v;
//                    //这里不做判断是否能领取，关联了就能领
//                }
//            }
//        }
//        return $card_res;
//    }
//
//    //查看用户领券记录表中是否领取了
//    private function _checkCardEvent($card_id, $user_id)
//    {
//        $event_model = new BuCardReceiveRecord();
//        $card_event  = $event_model->getCardEventByUserId($card_id, $user_id);
//        return $card_event;
//    }

//    /**
//     * 通过订单号查询订单商品得优惠，满减,N件N折
//     * @param $order_code
//     */
//    protected function order_goods_yh($order_code, $is_change_db=1)
//    {
//        $order_goods_model = new BuOrderCommodity();
//        $order_goods       = $order_goods_model->getList(['where' => ['order_code' => $order_code]]);
//        $yh_goods          = [];
//        $full_dis_goods    = [];
//        //limit_id' , 'suit_id', 'full_id' , 'n_dis_id' ,'group_id' , 'pre_sale_id'
//        if ($order_goods) {
//            foreach ($order_goods as $v) {
//                if ($v['full_id']) {
//                    $yh_goods[$v['full_id']]['full_id']     = $v['full_id'];
//                    $yh_goods[$v['full_id']]['full_list'][] = [
//                        'full_id'      => $v['full_id'],
//                        'commodity_id' => $v['commodity_id'],
//                        'count'        => $v['count'],
//                        'price'        => $v['price'],
//                        'goods_prices' => $v['count'] * $v['price'],
//                    ];
//                }
////                if ($v['n_dis_id']) {
////                    $yh_goods[$v['n_dis_id']]['n_dis_id']     = $v['n_dis_id'];
////                    $yh_goods[$v['n_dis_id']]['n_dis_list'][] = [
////                        'n_dis_id'     => $v['n_dis_id'],
////                        'commodity_id' => $v['commodity_id'],
////                        'count'        => $v['count'],
////                        'price'        => $v['price'],
////                        'goods_prices' => $v['count'] * $v['price'],
////                    ];
////                }
//            }
//            if ($yh_goods) {
//                foreach ($yh_goods as $k => $vv) {
//                    $full_all_price = 0;
////                    $n_dis_count    = 0;
//                    if ($vv['full_id']) {
//                        foreach ($vv['full_list'] as $vvv) {
//                            $full_all_price += $vvv['goods_prices'];
//                        }
//                        $yh_goods[$k]['full_all_price'] = $full_all_price;
//                        $mj_yj                          = $this->mj_yj($vv['full_id'], $full_all_price, $vv['full_list']);
//                        $yh_goods[$k]['all_mj']         = $mj_yj['mg'];
//                        $yh_goods[$k]['full_list']      = $mj_yj['list'];
//                    }
////                    if ($vv['n_dis_id']) {
////                        foreach ($vv['n_dis_list'] as $vvv) {
////                            $n_dis_count += $vvv['count'];
////                        }
////                        $yh_goods[$k]['n_dis_count'] = $n_dis_count;
////                    }
//                }
//            }
//            if ($is_change_db) {
//                if ($yh_goods) {
//                    $order_goods = $order_goods_model->getList(['where' => ['order_code' => $order_code]]);
//                    foreach ($order_goods as $v) {
//                        if ($v['full_id']) {
//                            foreach ($yh_goods['full_list'] as $vv) {
//                                if ($vv['commodity_id'] == $v['commodity_id']) {
//                                    $order_goods_data = [
//                                        'full_dis_money'    => $vv['yh_money'],
//                                        'all_dis'    => $vv['yh_money'],//总优惠金额7
//                                        'last_updated_date' => date('Y-m-d H:i:s')
//                                    ];
//                                    $order_goods_model->saveData($order_goods_data, ['id' => $v['id']]);//更新满减优惠金额
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }

//    /**
//     * @param $mj_id 满减ID
//     * @param $all_price 订单总价
//     * @param $list 订单列表
//     * @return array mj:满减金额,list:添加了各个商品得优惠
//     */
//
//    public function mj_yj($mj_id, $all_price, $list)
//    {
//        $full_model = new DbFullDiscount();
//        $full_info  = $full_model->getOneByPk($mj_id);
//        //满减full_discount_rules按照满减额度从高到底排序，循环到得最先一个为满减金额
//        $mj = 0;
//        if ($full_info) {
//            $full_rule = $full_info['full_discount_rules'];
//            if ($full_rule) {
//                foreach ($full_rule as $v) {
//                    if ($all_price >= $v[0]) {
//                        $mj = $v[1];
//                    }
//                }
//            }
//        }
//        if ($mj) {
//            $toEnd  = count($list);
//            $i      = 1;//
//            $y_yh_m = 0;//已优惠
//            foreach ($list as $k => $v) {
//                if (0 === --$toEnd) {
//                    $yh_money = $mj - $y_yh_m;
//                } else {
//                    $yh_money = round($v['goods_prices'] * $mj / $all_price, 2);
//                    $y_yh_m   += $yh_money;
//                }
//                $i++;
//                $list[$k]['yh_money'] = $yh_money;
//            }
//        }
//        return ['mg' => $mj, 'list' => $list];
//
//
//    }

    /**
     * 根据vin码获取车龄和保养信息
     * @param string $vin
     * @return mixed
     */
    public function _get_maintain($url, $vin = '')
    {
        $params = [["vin" => $vin]];
        return E3spRefactor::create('e3sp_refactor')->getMaintainInfo($params);

        $header = array(
            'Content-Type: application/json',
        );
        $ch     = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }

    /**
     * 保养套餐 商品id
     * @return array
     */
    public function maintain_commodity_id()
    {
        return [
            1569, 1570, 1571, 1572, 1573, 1574, 1575, 1577, 1578, 1579, 1580, 1581, 1582, 1583, 1584,
            1585, 1586, 1587, 1588, 1589, 1715, 1716, 1717, 1718, 3538, 3539, 3540, 3541,
            4258, 4259, 4260, 4261, 4262, 4263, 4264, 4265, 4266, 4267, 4268, 4269, 4270, 4271, 4272,
            4273, 4274, 4275, 4276, 4277, 4278, 4279, 4280, 4281, 4282, 4283, 4284, 4285,
        ];
    }

    /**
     * 通用行为调用
     * @param $behavior
     * @param $params
     */
    public function behavior($behavior, $params)
    {

        #读取配置,查看是否唤起behavior
        $behavior_in = config('behavior_in');

        if (in_array($behavior, $behavior_in)) {
            $detail_param = $params;
            Hook::exec('app\\net_small\\behavior\\' . $behavior, 'run', $detail_param);
        }
    }




}
