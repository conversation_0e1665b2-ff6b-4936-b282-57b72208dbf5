<?php

/*** Created by demo.
 * PROJECT：php-wxmp-dealers
 * User: <EMAIL>
 * Date: 2025/2/19
 * Time: 14:54
 * remark:
 */

namespace app\net_small\controller;

use app\admin_v2\service\InvoiceApplyService;
use app\admin_v2\service\InvoiceRecordService;
use app\common\model\db\DbOrderInvoice;
use app\common\model\db\InvoiceLogModel;
use app\common\model\db\InvoiceRecordModel;
use app\common\port\connectors\InvoiceHX;
use ForkModules\Traits\ResponseTrait;
use think\Controller;
use think\Model;
use think\Response;

class InvoiceCallback extends Controller
{
    use ResponseTrait;

    /**
     * 发票回调
     * @return Response
     */
    public function sendInvoiceCallback(): Response
    {
        $params = input();
        $log = new InvoiceLogModel();
        try {
            $log->request_url = '/net_small/invoice_callback/sendInvoiceCallback';
            $log->request_params = json_encode_cn($params);
            if ($params['invoiceType'] == 1) {
                // 蓝票
                $log->order_no = $params['orderNo'];
            } else {
                // 红票
                $log->order_no = $params['oriOrderNo'];
            }

            $request_data = $this->sendInvoiceCallbackProcess($params);
            $log->response = json_encode_cn($request_data);
        } catch (\Exception $exception) {
            $request_data = [
                'code' => '9999',
                'message' => $exception->getMessage(),
            ];
            $log->response = json_encode_cn($request_data);
        }
        $log->save();
        return $this->setResponseData($request_data)->send();
    }

    /**
     *
     * 发票回调处理
     * @param $params
     * @return string[]
     */
    protected function sendInvoiceCallbackProcess($params): array
    {
        if (empty($params['invoiceId'] ?? '')) {
            return [
                'code' => '9999',
                'message' => '流水号为空',
            ];
        }
        //1:开票成功、 2:开票失败、3:作废成功、4:作废失败；5:开票失败删除成功
        if (!in_array($params['invoiceStatus'] ?? 0, [1, 2, 3, 4, 5])) {
            return [
                'code' => '9999',
                'message' => '发票状态不正确',
            ];
        }
        //2红字发票, 1蓝字发票
        if (!in_array($params['invoiceType'] ?? 0, [1, 2])) {
            return [
                'code' => '9999',
                'message' => '发票类型不正确',
            ];
        }

        if ($params['invoiceType'] == 1) {
            // 蓝票

//            $_where = "invoice_id='{$params['invoiceId']}'";
            $_where = [
                'order_no'       => $params['orderNo'],
                'is_red_invoice' => 0, // 未冲红
            ];
        } else {
            // 红票
            $_where = [
                'order_no'       => $params['oriOrderNo'],
                'red_invoice_data_status' => InvoiceRecordModel::INVOICE_REQUEST_WAITING, //
            ];

//            $_where = "red_invoice_id='{$params['invoiceId']}'";
//            if ($params['billId'] ?? '') $_where .= "OR red_invoice_id='{$params['billId']}'";
//            if ($params['oriInvoiceNumber'] ?? '') $_where .= "OR invoice_number='{$params['oriInvoiceNumber']}'";
        }

        $record = InvoiceRecordModel::where($_where)->order('id desc')->find();
        if (empty($record)) {
            return [
                'code' => '9999',
                'message' => '发票不存在',
            ];
        }
        $data_update = [];

        $data_update['fault_message'] = json_encode_cn($params['errorMessage']);
        try {
            switch ($params['invoiceStatus']) {
                case 1:
                    // 1开票成功
                    //开票类型，1:蓝票；2:红票
                    if ($params['invoiceType'] == 1) {
                        if (empty($record->invoice_number)) {
                            //1蓝票处理
                            $data_update['invoice_status'] = InvoiceRecordModel::INVOICE_SUCCESS;
                            $data_update['invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_SUCCESS;
                            $data_update['message'] = json_encode_cn($params['errorMessage']);

                            $data_update['invoice_code'] = $params['invoiceCode'];
                            $data_update['invoice_number'] = $params['invoiceNumber'];

                            $data_update['invoice_date'] = date("Y-m-d H:i:s", $params['invoiceTime'] / 1000);
                            $data_update['els_code'] = $params['allElectronicInvoiceNumber'];
                            $data_update['image_url'] = $params['imageUrl'];
                            $data_update['pdf_url'] = $params['pdfUrl'];
                            $data_update['ofd_url'] = $params['ofdUrl'];
                            $data_update['picture_url'] = $params['pictureUrl'];

//                            $data_update['tax'] = $params['taxTotal'];
//                            $data_update['tax_amount'] = $params['taxAmountTotal'];
//                            $data_update['tax_free_amount'] = $params['taxFreeAmountTotal'];

                            $data_update['last_updated_date'] = date('Y-m-d H:i:s');

                            $record->where('id', '=', $record->id)->update($data_update);


                            DbOrderInvoice::updateOrderInvoiceStatus($record->relation_good_order_no, InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($record->invoice_status), $record->invoice_date);
                        }
                    }
                    if ($params['invoiceType'] == 2) {

                        if (empty($record->red_invoice_number)) {
                            //红票处理
                            $data_update['is_red_invoice'] = 1;

                            $data_update['invoice_status'] = InvoiceRecordModel::INVOICE_RED_SUCCESS;

                            $data_update['red_invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_SUCCESS;

                            $data_update['red_message'] = json_encode_cn($params['errorMessage']);

                            $data_update['red_invoice_code'] = $params['invoiceCode'];
                            $data_update['red_invoice_number'] = $params['invoiceNumber'];

                            //红字申请表编号
                            $data_update['red_billinfo_no'] = $params['billInfoNo'] ?? '';

                            $data_update['red_invoice_date'] = date("Y-m-d H:i:s", ($params['invoiceTime']) / 1000);
                            $data_update['red_els_code'] = $params['allElectronicInvoiceNumber'];
                            $data_update['red_image_url'] = $params['imageUrl'];
                            $data_update['red_pdf_url'] = $params['pdfUrl'];
                            $data_update['red_ofd_url'] = $params['ofdUrl'];
                            $data_update['red_picture_url'] = $params['pictureUrl'];

                            // 新增维护
                            $data_update['red_bill_status'] = 5; // 已开具

                            $record->where('id', '=', $record->id)->update($data_update);

                            if ($record->after_red_need_blue == 1) {
                                //换开数据时
                                //开红成功后，需要激活蓝票开票
                                InvoiceApplyService::getInstance()->submbitApplyByFromId($record->apply_id, $message);
                            }

                            DbOrderInvoice::updateOrderInvoiceStatus($record->relation_good_order_no, InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($record->invoice_status));
                        }

                    }
                    break;

                case 2:
                    //开票失败
                    if ($params['invoiceType'] == 1) {
                        $data_update['invoice_status'] = InvoiceRecordModel::INVOICE_FAIL;
                        $data_update['invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_FAIL;

                        $data_update['message'] = json_encode_cn($params['errorMessage']);
                        $record->where('id', '=', $record->id)->update($data_update);

                        DbOrderInvoice::updateOrderInvoiceStatus($record->relation_good_order_no, InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($record->invoice_status), $record->invoice_date);

                    }
                    if ($params['invoiceType'] == 2) {

//                        if ($record->invoice_status == InvoiceRecordModel::INVOICE_REPLACE) {
//                            //如果是换开的申请，则冲红成功表示换开成功
//                        } else {
//                            $record->invoice_status = InvoiceRecordModel::INVOICE_RED_FAIL;
//                        }

                        $data_update['invoice_status'] = InvoiceRecordModel::INVOICE_RED_FAIL;
                        $data_update['red_invoice_data_status'] = InvoiceRecordModel::INVOICE_REQUEST_FAIL;

                        $data_update['red_message'] = json_encode_cn($params['errorMessage']);
                        $record->update($data_update);

                        DbOrderInvoice::updateOrderInvoiceStatus($record->relation_good_order_no, InvoiceRecordService::getInstance()->getInvoiceStatusByStatus($record->invoice_status), $record->invoice_date);

                    }

                    break;
                case 3:
                    // 3作废成功
                    break;

                case 4:
                    // 4作废失败
                    break;

                case 5:
                    // 5开票失败删除成功
                    break;

                default:
                    // 可选：处理其他状态或提供错误处理
                    break;
            }
        } catch (\Exception $exception) {
            return [
                'code' => '9999',
                'message' => $exception->getMessage(),
            ];
        }

        return [
            'code' => '0000',
            'message' => '业务方接收同步成功',
        ];
    }

    /**
     * 确认红字发票回调
     * @return Response
     */
    public function sendRedConfirmInvoiceCallback(): Response
    {
        $params = input();
        $log = new InvoiceLogModel();
        try {
            $log->request_url = '/net_small/invoice_callback/sendRedConfirmInvoiceCallback';
            $log->request_params = json_encode_cn($params);

            $request_data = $this->sendRedConfirmInvoiceCallbackProcess($params);
            $log->response = json_encode_cn($request_data);
        } catch (\Exception $exception) {
            $request_data = [
                'code' => '9999',
                'message' => $exception->getMessage(),
            ];
            $log->response = json_encode_cn($request_data);
        }
        $log->save();
        return $this->setResponseData($request_data)->send();
    }

    /**
     * 确认红字发票回调处理
     * @return string[]
     */
    protected function sendRedConfirmInvoiceCallbackProcess($params): array
    {
        if (!in_array($params['backType'] ?? 0, [1, 2])) {
            return [
                'code' => '9999',
                'message' => '回传类型不能不正确',
            ];
        }

        // 2025年4月8日 10:05:58  产品：日产商城只有数电票，就只有红字确认单
        if ($params['backType'] == 1) {
            //回传红字信息表结果
            return $this->runSendRedInvoiceBackType1($params);
        }
        if ($params['backType'] == 2) {
            //红字确认单结果
            return $this->runSendRedInvoiceBackType2($params);
        }

        return [
            'code' => '9999',
            'message' => '回传类型不能不正确',
        ];
    }

    protected function runSendRedInvoiceBackType1($params): array
    {
        $_where = [
            'red_invoice_id' => $params['billNo']
        ];
        $record = InvoiceRecordModel::where($_where)->find();
        if (empty($record)) {
            return [
                'code' => '9999',
                'message' => '发票不存在',
            ];
        }
        $data_update = [];
        //		信息表类型(0:正常 1:逾期(仅销方开具)，2:机动车专票-退货和开具错误，3:机动车专票-销售折让和合格证不退回
        //		4、矿产品类专用信息表（涉及销售数量和金额变更）,5、矿产品类专用信息表（仅涉及销售金额变更，不涉及数量变动
        if ($params['billType'] != 0) {
            $data_update['red_message'] = "信息表类型非正常状态:" . $params['billType'];
            $record->where('id', '=', $record->id)->update($data_update);
            return [
                'code' => '9999',
                'message' => '信息表类型非正常状态',
            ];
        }
        //		信息表状态(-1:未提交 0:申请中 1:审核成功 2:审核失败 3:申请成功 4:申请失败
        //		5:已开具 6:撤销中 7:撤销失败 8:已撤销 -2:删除成功)
        if (!in_array($params['billStatus'], ["1", "3", "5"])) {
            $data_update['red_message'] = "信息表状态非正常状态:" . $params['billStatus'];
            $record->where('id', '=', $record->id)->update($data_update);
            return [
                'code' => '9999',
                'message' => '信息表状态非正常状态',
            ];
        }
        //如果是 1:审核成功 3:申请成功 5:已开具
        //请求快速开票
        $this->fastRepeatedRedSingle($params);

        return [
            'code' => '0000',
            'message' => '业务方接收同步成功',
        ];
    }


    /**
     * 红字确认单
     * @param $params
     * @return string[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    protected function runSendRedInvoiceBackType2($params): array
    {
        $_where = [
            'red_invoice_id' => $params['billId']
        ];
        $record = InvoiceRecordModel::where($_where)->find();
        if (empty($record)) {
            return [
                'code' => '9999',
                'message' => '发票不存在',
            ];
        }
        $data_update = [];
        //确认单状态:-1 提交失败,00 提交中,01 无需确认02 销方录入待购方确认,03 购方录入待销方确认,04 购销双方已确认,
        // 05 作废（销方录入购方否认）,06 作废（购方录入销方否认）,07 作废（超72小时未确认）,
        // 08 作废（发起方撤销）,09 作废（确认方撤销）,10作废（异常凭证）,11作废（纳税人状态异常阻断）,15 提交税局中
        // 16 提交税局失败
        if (in_array($params['billStatus'], ["01", "04"])) {
            $data_update['red_message'] = "确认单状态:" . $params['billStatus'];

            $data_update['red_bill_status'] = $params['billStatus'];
            $data_update['red_billinfo_no'] = $params['billInfoNo'] ?? '';

            //		开具状态0：未开具,1：已开具
            if ($params['openStatus'] == 0) {
                $record->where('id', '=', $record->id)->update($data_update);
                //未开时，请求开
                $this->fastRepeatedRedSingle($params);
            }
        } elseif ($params['billStatus'] == '16') {
            $data_update = [
                'red_invoice_data_status' => InvoiceRecordModel::INVOICE_REQUEST_FAIL,
                'red_bill_status' => 4,
                'red_message' => $params['billMessage'],
                'invoice_status' => InvoiceRecordModel::INVOICE_RED_FAIL,
            ];
            $record->where('id', '=', $record->id)->update($data_update);

        }

        return [
            'code' => '0000',
            'message' => '业务方接收同步成功',
        ];
    }

    /**
     * 快x冲红接口
     * @param $params
     */
    public function fastRepeatedRedSingle($params)
    {
        //
        $callbackUrl = config('invoice_callback_url') .'net_small/invoice_callback/sendInvoiceCallback';

        $invoice_params = [
            'sellerTaxnum' => $params['sellerTaxnum'] ?? '',
            'companyCode' => '',
            'invoiceId' => '',
            'invoiceCode' => '',
            'invoiceNumber' => '',
            'billId' => $params['billId'] ?? '',
            'billNo' => $params['billNo'] ?? '',
            'billUuid' => $params['billUuid'] ?? '',
            'fastRedType' => 1,//快捷冲红类型 不填或者0为普通快捷冲红 1:数电发票快捷冲红(数电发票必传1)
            'invoiceLine' => $params['blueInvoiceLine'] ?? '',
            'orderNo' => $params['orderNo'] ?? '',
            'bizCallbackUrl' => $callbackUrl,
            'paperInvoiceType' => '',
            'columnFirst' =>'NI+商城',
            'columnSecond' => '',
            'columnThree' => '',
        ];
        return InvoiceHX::create('invoice_hx')->fastRepeatedRedSingle($invoice_params);
    }
}