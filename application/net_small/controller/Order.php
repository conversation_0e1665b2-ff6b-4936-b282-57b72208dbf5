<?php
/**
 * Created by PhpStor<PERSON>.
 * User: lzx
 * Date: 2020/12/22
 * Time: 3:11 PM
 */

namespace app\net_small\controller;


use api\wechat\Carer;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrderAct;
use app\common\model\bu\BuOrderRefund;
use app\common\model\bu\BuOrderSettlement;
use app\common\model\bu\BuPhoneCheck;
use app\common\model\bu\BuQyUserAll;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbJobsLog;
use app\common\model\db\DbOrderRefundReason;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\net_service\NetCart;
use app\common\net_service\NetOrder;
use app\common\model\act\AcGroup;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\net_service\PayCenter;
use app\common\net_service\SendSms;
use app\common\port\connectors\Crm;
use app\common\port\connectors\Member;
use app\common\port\connectors\Payment;
use app\common\port\connectors\QuickWin;
use ForkModules\Traits\ResponseTrait;
use app\common\validate\Order as OrderValidate;
use think\exception\HttpResponseException;
use think\Queue;
use tool\Logger;


/**
 * @title 订单中心
 * @description 接口说明
 */
class Order extends Common
{
    use ResponseTrait;

    protected $order_model = [];
    protected $order_commodity_model = [];
    private $point_times = 10;//积分倍数

    private $car_vip = ['会员金卡', '会员金卡(VIP)', '会员金卡（VIP）', '会员银卡', '会员银卡VIP', '会员银卡（VIP）', '员工卡', '铂金卡', '黑卡'];//金银卡会员登记，用于积分兑换
    private $jk_goods = [2780, 2782, 2784, 2786, 2788, 2790];
    private $pk_goods = [2779, 2781, 2783, 2785, 2787, 2789];
    //口罩商品ID
    private $kz_goods = [3790];
    private $kz_dlr = "JSJY01";//口罩专营店

    private function sj_ccs()
    {
        if (config('app_status') == "develop") {
            $ids = [2940, 2941];
        } else {
            $ids = [3610, 3611];
        }
        return $ids;
    }

    public function __construct()
    {
        parent::__construct();
        $this->order_model           = new BuOrder();
        $this->order_commodity_model = new BuOrderCommodity();
        if (!$this->user_id) {
            $response = $this->setResponseError('请登录!', 401)->send();
            throw new HttpResponseException($response);
        }
    }


    /**
     * @title 填充订单
     * @description 接口说明
     * @param name:sku_ids type:string require:1 default:0 other: desc:商品SKUID多个用,分开
     * @param name:cart_id type:string require:0 default:0 other: desc:购物车多个用,分开
     * @param name:numbers type:string require:1 default:0 other: desc:商品对应数量,分开必须与商品ID一致,
     * @param name:order_source type:int require:0 default:0 other: desc:订单来源1正常商品订单；2团购订单；3套装；4礼包；5到店礼6积分-日产
     * @param name:is_by_tc type:int require:0 default:0 other: desc:是否老友惠保养套餐
     * @param name:name type:string require:0 default:0 other: desc:姓名老友惠保养套餐填写
     * @param name:phone type:string require:0 default:0 other: desc:电话老友惠保养套餐填写
     * @param name:channel_type type:int require:0 default:0 other: desc:渠道
     * @param name:act_type_id type:int require:0 default:0 other: desc:活动类型ID1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减
     * @param name:act_id type:int require:0 default:0 other: desc:活动类型对应ID一个商品参与一个活动
     * @param name:nick_name type:string require:0 default:0 other: desc:昵称，团购要传
     * @param name:head_img type:string require:0 default:0 other: desc:头像，团购要传
     * @param name:group_order_code type:string require:0 default:0 other: desc:头像，团购要传
     * @param name:mail_method type:int require:1 default:0 other: desc:快递或者到店1到店2快递
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:订单ID后续都用此ID
     *or
     * <AUTHOR>
     * @url /net-small/order/fill
     * @method POST
     *
     */
    public function fill(OrderValidate $validate)
    {
//     * @param name:n_dis_id type:int require:0 default:0 other: desc:N件N折活动ID
//     * @param name:suit_id type:int require:0 default:0 other: desc:套装ID
//     * @param name:cart_id type:int require:0 default:0 other: desc:购物车ID
//     * @param name:group_id type:int require:0 default:0 other: desc:团购ID
//     * @param name:group_order_code type:int require:0 default:0 other: desc:团购团ID
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("fill")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $source = $this->source;
        $source_code = $this->sourcecode;
        $id     = $requestData['sku_ids'];
        $count  = $requestData['numbers'];
//        $suit_id = $requestData['suit_id']??0;
//        $cart_id = $requestData['cart_id']??0;
//        $group_id = $requestData['group_id']??0;
//        $order_source = $requestData['order_source']??1;
//        $group_order_code = $requestData['group_order_code']??0;
//
//        $is_by_tc = $requestData['is_by_tc']??0;
//        $name = $requestData['name']??'';
//        $phone = $requestData['phone']??'';
//        $dlr_code = $requestData['choose_dlr_code']??0;
//
//        $act_type_id = $requestData['act_type_id']??0;//活动类型ID
//        $act_id = $requestData['act_id']??0;//活动类型对应活动ID
        $requestData['source'] = $source;
        $requestData['sourcecode'] = $source_code;

        $id    = explode(',', $id);
        $count = explode(',', $count);
        if (count($id) != count($count)) {
            return $this->setResponseError('参数缺失')->send();
        }
        if (count($id) <= 0 || !$id) {
            return $this->setResponseError('请选择购买的商品')->send();
        }
        $net_order = new NetOrder();

        $user_ip = '';
        if (getenv("HTTP_CLIENT_IP")) {
            $user_ip = getenv("HTTP_CLIENT_IP");
        } else if (getenv("HTTP_X_FORWARDED_FOR")) {
            $user_ip = getenv("HTTP_X_FORWARDED_FOR");
        } else if (getenv("REMOTE_ADDR")) {
            $user_ip = getenv("REMOTE_ADDR");
        }
        $ips = [
            'user_ip'   => $user_ip,
            'server_ip' => gethostbyname(php_uname('n')),
        ];
        $res = $net_order->fill($requestData, $this->user, $this->channel_type, $ips,$this->brand);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            $res_arr = explode('-',$res['msg']);
            return $this->setResponseData(['order_id' => $res_arr[0],'sub_goods_stock'=>$res_arr[1]])->send();
        }


    }


    /**
     * @title 订单确认
     * @description 接口说明
     * @param name:order_id type:int require:1 default:0 other: desc:订单ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/confirm
     * @method GET
     *
     */
    public function orderConfirm(OrderValidate $validate)
    {   $buordercommodity = new BuOrderCommodity();
        //var_dump($this->channel_type);

      //  $r = $buordercommodity->noActivity(1,7,"QCSM");

        /**
         * 需要判断一系列的优惠规则
         */

        //openid黑名单
        if (in_array($this->user_id, [401125])) {
            return $this->setResponseError('系统异常', 301)->send();
        }

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("confirm")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $open_check = config('sign_checker.open');

        if (in_array($this->channel_type, ['GWSM', 'GWAPP']) && $open_check) {
            $key = config('sign_checker.key');
            $order_id = $this->request->get('order_id', '392324');
            $nonce_str = $this->request->get('x_nonce_str', 'S23JNCi44iffd4f');
            $timestamp = intval($this->request->get('x_timestamp', time()));

            $sign = $this->request->get('x_sign', '');
            if (empty($sign)) {
                return $this->setResponseError('签名不能为空')->send();
            }
//            print_json($timestamp, time());
            if (abs($timestamp - time()) > 600) {
                return $this->setResponseError('接口签名已失效')->send();
            }
//            print_json($order_id . $nonce_str . $timestamp . $key,md5($order_id . $nonce_str . $timestamp . $key),$sign);
            if ($sign != md5($order_id . $nonce_str . $timestamp . $key)) {
                return $this->setResponseError('接口签名不合法!')->send();
            }
        }

        $net_order = new NetOrder();
        $res       = $net_order->confirm($requestData, $this->user, $this->channel_type);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseHeaders(['gsecurity' => config('gsecurity')])->setResponseData($res['msg'])->send();
        }
    }


    /**
     * @title 订单确认--新版取送车
     * @description 接口说明
     * @param name:order_id type:int require:1 default:0 other: desc:订单ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/confirm-pick-up
     * @method GET
     *
     */
    public function orderConfirmPickUp(OrderValidate $validate)
    {   $buordercommodity = new BuOrderCommodity();

        /**
         * 需要判断一系列的优惠规则
         */

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("confirm")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_order = new NetOrder();
        $res       = $net_order->confirm_pick_up($requestData, $this->user, $this->channel_type);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseHeaders(['gsecurity' => config('gsecurity')])->setResponseData($res['msg'])->send();
        }
    }

    public function calcDiscounts(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result = $validate->scene("calcDiscounts")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order_code = $requestData['order_code'];
        $choose_promotion_json = $requestData['choose_promotion_json'] ?? [];
        $choose_card_ids = $requestData['choose_card_ids'] ?? '';
        $choose_card_codes = $requestData['choose_card_codes'] ?? '';
        $is_choose = $requestData['is_choose'] ?? 0;
        $choose_card_ids = explode(',', $choose_card_ids);
        $choose_card_codes_arr=[];
        if($choose_card_codes){
            $choose_card_codes_arr =  explode(',', $choose_card_codes);
        }
        $net_order = new NetOrder();
        $res = $net_order->calcDiscounts($this->user, $this->channel_type, $order_code, $choose_promotion_json, $choose_card_ids, $is_choose,$choose_card_codes_arr);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }

    public function calcDiscountsBak(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result = $validate->scene("calcDiscounts")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order_code = $requestData['order_code'];
        $choose_promotion_json = $requestData['choose_promotion_json'] ?? [];
        $choose_card_ids = $requestData['choose_card_ids'] ?? '';
        $is_choose = $requestData['is_choose'] ?? 0;
        $choose_card_ids = explode(',', $choose_card_ids);

        $net_order = new NetOrder();
        $res = $net_order->calcDiscounts_bak($this->user, $this->channel_type, $order_code, $choose_promotion_json, $choose_card_ids, $is_choose);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }

    public function confirmDynamic(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result = $validate->scene("confirmDynamic")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $choose_promotion_json = $requestData['choose_promotion_json'] ?? [];
        $choose_card_ids = $requestData['choose_card_ids'] ?? '';
        $choose_card_codes = $requestData['choose_card_codes'] ?? '';
        $choose_card_code_arr=[];
        if($choose_card_codes){
            $choose_card_code_arr = explode(',', $choose_card_codes);
        }
        $choose_card_ids = explode(',', $choose_card_ids);
        $net_order = new NetOrder();
        $res = $net_order->confirm($requestData, $this->user, $this->channel_type, $choose_promotion_json, $choose_card_ids, 1,$choose_card_code_arr);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }

    public function orderCard(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("mail_price")->check($requestData);
    }

    /**
     * @title 订单运费
     * @description 接口说明
     * @param name:order_id type:int require:1 default:0 other: desc:订单ID1
     * @param name:address type:string require:0 default:0 other: desc:省
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/mail
     * @method GET
     *
     */
    public function orderMail(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("mail_price")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order_id    = $requestData['order_id'];
        $address     = $requestData['address'] ?? '';
        $order_model = new BuOrder();
        $order       = $order_model->getOneByPk($order_id);
        if (!$order) {
            return $this->setResponseError("订单不存在", 301)->send();
        }
        $res = $this->_mail_price($order['parent_order_code'], $address, 'mail_ajax');
        if ($res['error'] == 1) {
            return $this->setResponseError($res['msg'])->send();
        } else {
            return $this->setResponseData(['message' => '成功获取运费', 'mail_price' => $res['data']['price'], 'mail_goods' => $res['data']['mail_goods']])->send();


        }
    }

    /**
     * @title 去支付
     * @description 接口说明
     * @param name:order_id type:int require:1 default:0 other: desc:订单ID
     * @param name:name type:string require:0 default:0 other: desc:姓名
     * @param name:phone type:int require:0 default:0 other: desc:电话
     * @param name:address type:string require:0 default:0 other: desc:地址
     * @param name:cards type:string require:0 default:0 other: desc:卡券ID，多个,隔开
     * @param name:card_money type:int require:0 default:0 other: desc:卡券总金额数据会进行核对匹配
     * @param name:pay_type type:varchar require:0 default:0 other: desc:支付方式，传01，02
     * @param name:money type:int require:0 default:0 other: desc:实际支付金额可以是0
     * @param name:logistics_mode type:int require:0 default:0 other: desc:1到店2快递
     * @param name:point type:int require:0 default:0 other: desc:厂家积分
     * @param name:dlr_point type:int require:0 default:0 other: desc:专营店积分
     * @param name:choose_dlr_code type:string require:0 default:0 other: desc:选择专营店到店类型必填
     * @param name:return_url type:string require:0 default:0 other: desc:web支付完成回跳地址
     *  @param name:invoiceParam type:string require:0 default:0 other: desc:web支付完成回跳地址
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/go-pay
     * @method POST
     *
     */
    public function goPay(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("go_pay")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $point              = $requestData['point'] ?? 0;
        $pick_up_order_code = $requestData['pick_up_order_code'] ?? '';

        $check_sms_redis_name = 'net-order-check-sms'.$this->user_id;
        $check_sms  = redis($check_sms_redis_name);
        if(!$check_sms && $point && !$pick_up_order_code){
            return $this->setResponseData('请输入正确验证码!')->send();
        }

        $net_order = new NetOrder();
        $res       = $net_order->go_pay($requestData, $this->user, $this->channel_type);
        $net_order->uploadToApp($requestData['order_code'], $res['code'], $this->event_param);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }

    /**
     * @title 订单详情
     * @description 接口说明
     * @param name:order_id type:int require:1 default:0 other: desc:订单ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/detail
     * @method GET
     *
     */
    public function detail(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("detail")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_order = new NetOrder();
        $res       = $net_order->detail($requestData, $this->user, $this->channel_type);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseHeaders(['gsecurity' => config('gsecurity')])->setResponseData($res['msg'])->send();
        }
    }

    /**
     * @title 订单列表
     * @description 接口说明
     * @param name:page type:int require:1 default:1 other: desc:页码
     * @param name:pageSize type:int require:0 default:20 desc:当前页显示数量,不传默认20
     * @param name:order_status type:int require:0 default:20 desc:订单状态8待付款,11带安装2待收货12待拼单13待评价14待支付不传就是所有
     * @param name:order_code type:int require:0 default:0 desc:订单号
     * @param name:list_type type:int require:0 default:0 desc:官网渠道订单列表专用1:可申请售后列表2:已申请售后列表
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/list
     * @method GET
     *
     */
    public function orderList(OrderValidate $validate)
    {

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("list")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_order = new NetOrder();
        $res       = $net_order->order_list($requestData, $this->user, $this->channel_type,$this->brand);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
//            return $this->setResponseHeaders(['gsecurity' => config('gsecurity')])->setResponseData($res['msg'])->send();
            return $this->setResponseData($res['msg'])->send();
        }

    }

    /**
     * @title 取消订单
     * @description 接口说明
     * @param name:order_id type:int require:1 default:0 other: desc:订单ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/cancel
     * @method DELETE
     *
     */
    public function cancel(OrderValidate $validate)
    {

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("cancel")->check($requestData);

        //校验失败,返回异常--
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order_no = $requestData['order_id'];
        $where    = ['id' => $order_no,'user_id'=>$this->user_id];
        $order    = $this->order_model->getOne(array('where' => $where));
        if (!$order) {
            return $this->setResponseError("订单不存在", 403)->send();
        } else {
            if ($order['order_status'] <> 1) {
                return $this->setResponseError("订单暂时不可以取消", 403)->send();
            }
            $data = array(
                'order_status'      => 3,
                'modifier'          => 'nt_qd_cl',
                'last_updated_date' => date('Y-m-d H:i:s'),
            );
            $res  = $this->order_model->saveData($data, $where);

            // 赠品券订单 返激活
//            $net_order = new NetOrder();
//            $net_order->invalidCoupon($order['order_code'], 3, $order['user_id']);

            (new NetOrder())->orderChange($order['order_code']);
            if ($res) {
                return $this->setResponseData('ok')->send();
            } else {
                return $this->setResponseError("取消订单失败", 405)->send();
            }
        }
    }


    /**
     * @title 订单完成
     * @description 接口说明 完成之后页面可以领卡券之类的团购订单限时团购完成详情
     * @param name:order_id type:int require:1 default:0 other: desc:订单ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/finish
     * @method GET
     *
     */

    public function finish(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("finish")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $id       = $requestData['order_id'];
        $dlr_code = $this->channel_type;
        if ($dlr_code == 'YGDLR') {
            $dlr_code = 'NISSAN';
        }
        $order = $this->order_model->getOneByPk($id);
        if (!$order) {
            return $this->setResponseError("订单不存在", 403)->send();
        }

        $is_cap_commodity = 0;
        $act_dlr_code = '';
        $capActivity = config('cap_activity');
//        $capActivity0 = $capActivity[0];
//        $capActivity1 = $capActivity[1];

        if (!empty($capActivity)) {
//            $capCommodityIdArr0 = array_column($capActivity0, 'commodity_id');
//            $capCommodityIdArr1 = array_column($capActivity1, 'commodity_id');
//            $order_commodity_model = new BuOrderCommodity();
//            $map = ['order_code'=>$order['order_code']];
//            $commodityId = $order_commodity_model->where($map)->value('commodity_id');
//            if (in_array($commodityId, $capCommodityIdArr0) || in_array($commodityId, $capCommodityIdArr1)) {
//                $is_cap_commodity = 1;
//            }
//            $order_act_model = new BuOrderAct();
//            $map = ['order_code'=>$order['order_code']];
//            $act_dlr_code = $order_act_model->where($map)->value('act_dlr_code');
            $order_commodity_model = new BuOrderCommodity();
            $map = ['order_code'=>$order['order_code']];
            $commodityId = $order_commodity_model->where($map)->value('commodity_id');
            foreach ($capActivity as $capInfo) {
                $capCommodityIdArr = array_column($capInfo['data'], 'commodity_id');
                if (in_array($commodityId, $capCommodityIdArr)) {
                     $is_cap_commodity = 1;
                     break;
                }
            }
            $order_act_model = new BuOrderAct();
            $map = ['order_code'=>$order['order_code']];
            $act_dlr_code = $order_act_model->where($map)->value('act_dlr_code');
        }
        $order['is_cap_commodity'] = $is_cap_commodity;
        $order['act_dlr_code'] = $act_dlr_code;



        $net_order = new NetOrder();


        $group_list  = [];
        $group_info  = [];
        //跳转这个页面得时候判断是否已经支付过了。更新支付状态---暂时判断快递类的
        if (in_array($order['order_status'], [1, 8]) && $order['money'] > 0 && $order['cashier_trade_no']) {
            $pay_center =  new PayCenter();
            $pay_center->pay_status($order['order_code']);
//            $pay_info = Payment::create('payment')->queryOrder(['cashier_trade_no' => $order['cashier_trade_no']]);
//            if (isset($pay_info['state'])) {
//                if ($pay_info['state'] == 2) {
//                    $r_o_save = $net_order->orderSave($order['order_code'], $pay_info['order_id'], 'order_finish');
//                    $order    = $this->order_model->getOneByPk($id);
//                }
//            }
        }
        $order_code     = $order['order_code'];
        $ac_group_model = new AcGroup();
        $group_order    = $ac_group_model->getOne(['where' => ['order_code' => $order_code]]);
        if ($group_order) {
//
//            if (!$group_order) {
//                return $this->setResponseError("团购订单未开始或者已结束!", 403)->send();
//            }
            $group_id         = $group_order['group_id'];
            $group_order_code = $group_order['group_code'];
            //显示支付以上的
            $group_list  = $ac_group_model->getList(['where' => ['group_code' => $group_order['group_code'], 'commodity_id' => $group_order['commodity_id'], 'status' => ['in', '3,4,5']]]);
            $group_model = new DbFightGroup();
            $time        = date('Y-m-d H:i:s');
            $group_info  = $group_model->getGroupInfo(['where' => ['a.id' => $group_id, 'a.is_enable' => 1],
                                                       'field' => "a.id,a.title,a.start_time,a.end_time,a.people_number,a.purchase_number,a.buy_hour,a.rule,b.commodity_id,b.lowest_price,d.shelves_type"]);
            if (!$group_info) {
                return $this->setResponseError("团购订单未开始或者已结束", 403)->send();
            } else {
                if ($group_list) {
                    foreach ($group_list as $k => $v) {
                        if ($v['status'] == 4) {
                            $group_order['status'] = 4;
                        }
                        $group_list[$k]['nickname'] = $v['nickname'];
                        $group_list[$k]['pic']      = $v['headimg'];
                    }
                }
                $goods_model                   = new DbCommodity();
                $goods                         = $goods_model->getOneByPk($group_order['commodity_id']);
                $group_order['cover_image']    = $goods['cover_image'];
                $group_order['commodity_name'] = $goods['commodity_name'];
                $group_order['title']          = $group_info['title'];
                if (strtotime(sprintf("%s +%s hours", $group_order['group_start_time'], $group_info['buy_hour'])) < time()) {
                    return $this->setResponseError("团购订单未开始或者已结束~", 403)->send();
                }
                $number = $group_info['people_number'] - count($group_list);
                if ($number < 1) {
                    $group_order['status'] = 4;
                }
                $group_order['inc_number'] = $number;
            }
        } else {

            if ($order['user_id']!=$this->user_id) {
                return $this->setResponseError("订单权限异常", 403)->send();
            }

            //团购没计入满减啊领卡圈
            $order_get_card    = NetOrder::full_card($order['order_code']);
            $order['get_card'] = $order_get_card;
        }
        //拉去到店类订单
        $dd_order = $this->order_model->getOne(['where' => ['parent_order_code' => $order['parent_order_code'], 'logistics_mode' => 1, 'parent_order_type' => ['<>', 3]]]);
        if ($dd_order) {
            $dd_order['money'] = $order['money'];//总单价格
            $order             = $dd_order;
            $yy_info           = $net_order->yyInfo($order);
            if ($yy_info) {
                $order['yy_info'] = [
                    'list'    => isset($yy_info['yy_list']) ? $yy_info['yy_list'] : '',
                    'can_yy'  => isset($yy_info['can_yy']) ? $yy_info['can_yy'] : '',
                    'yy_type' => isset($yy_info['yy_type']) ? $yy_info['yy_type'] : 0,
                ];
            }
        }
        $all_data = [
            'order'       => $order,
            'group_order' => $group_order,
            'group_list'  => $group_list,
            'group_info'  => $group_info,
        ];
//        if ($order['full_id']) {
//            $full_model = new DbFullDiscount();
//            $card_r_model = new BuCardReceiveRecord();
//            $full_info = $full_model->getOneByPk($order['full_id']);
//            $card_r = $card_r_model->getOne(['where' => ['status' => ['<>', 2], 'card_id' => $full_info['preferential_card_id'], 'act_id' => $order['order_code']]]);
//            if ($full_info) {
//                if ($full_info['is_preferential_card'] == 1 && !$card_r) {
//                    $order['get_card'] = "领取卡券";
//                    $order['get_card_id'] = $full_info['preferential_card_id'];
//                }
//            }
//        }

        /**** 订单支付抽奖活动202203-begin ****/
        $all_data['order']['is_jump_draw'] = 0;
        if ($order['brand'] == 1 && date('Y-m-d H:i:s', time()) >= config('draw_order_act.start_date') && date('Y-m-d H:i:s', time()) <= config('draw_order_act.end_date')) { // 生产
            $parent_order = $this->order_model->getList(['where' => ['parent_order_code' => $order['parent_order_code']]]);
            $all_money    = 0;
            foreach ($parent_order as $v) {
                $all_money += ($v['money'] + ($v['integral'] / 10));
            }
            if ($all_money >= 49) {
                $all_data['order']['is_jump_draw'] = 1;
            }
        }
        /**** 订单支付抽奖活动202203-end ****/
        return $this->setResponseData($all_data)->send();
//        $redis_name = "user-pay-finish-" . $dlr_code;
//        $qr_code = redis($redis_name);
//        $qrc = 0;
//        if (!$wx_user) {
//            if (!$qr_code) {
//                $QRC = new \api\wechat\QrCode();
//                $qrc_id = 20001;
//                $qr_code = $QRC::createQrCodeLimit($dlr_code, $qrc_id);
//                if ($qr_code) {
//                    $qr_code = $qr_code['qrcode'];
//                    redis($redis_name, $qr_code, 3600);
//                }
//            }
//            $qrc = 1;
//        }

    }

    /**
     * @title 卡券冲突选择
     * @description 接口说明 返回可使用，不可使用卡券
     * @param name:choose_card type:string require:1 default:0 other: desc:已选卡券ID,隔开
     * @param name:all_card type:string require:1 default:0 other: desc:所有卡券ID,隔开
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/card-clash
     * @method GET
     *
     */
    public function card_clash(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("card_clash")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $choose_card = $requestData['choose_card'];
        $all_card    = $requestData['all_card'];

        $net_order = new NetOrder();
        $res       = $net_order->card_clash($choose_card, $all_card);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }


    /**
     * @title 使用卡券之后最高可用积分
     * @description 接口说明 返回最高可用积分
     * @param name:choose_card type:string require:1 default:0 other: desc:已选卡券ID,隔开
     * @param name:order_code type:string require:1 default:0 other: desc:订单编码
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/card-most-point
     * @method GET
     *
     */
    public function card_most_point(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("card_most_point")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $order_code  = $requestData['order_code'];
        $choose_card = $requestData['choose_card']??'';

        $net_order = new NetOrder();
        $res       = $net_order->card_most_point($order_code, $choose_card,$this->user,$this->channel_type);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }


    /**
     * @title 申请退款(废弃,请对接售后)
     * @description 接口说明 返回退款原因等
     * @param name:order_id type:string require:1 default:0 other: desc:订单ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/refund
     * @method GET
     *
     */
    public function refund(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("refund")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $id = $requestData['order_id'];
        if (!$id) {
            return $this->setResponseError('非法入口')->send();
        }
        $order = $this->order_model->getOneByPk($id);
        if (!$order) {
            return $this->setResponseError('非法入口1')->send();
        }
        if ($order['user_id'] != $this->user_id) {
            return $this->setResponseError('非法入口!')->send();
        }

        $order_goods = $this->order_commodity_model->getList(array('where' => ['order_code' => $order['order_code'], 'is_enable' => 1]));

        if ($order['order_status'] != 2 && !($order_goods[0]['commodity_class'] != 1 && $order['order_status'] == 7)) {
            //非实物商品状态为7已结算的依旧可以退款
            return $this->setResponseError('订单状态异常')->send();
        }
        if ($order['order_source'] == 2) {
            return $this->setResponseError('拼团订单不可退款')->send();

        }
        $refund_model = new DbOrderRefundReason();
        $point_text   = '';

        if ($order['gift_score']) {
            $Car    = new  Carer();
            $car_er = $Car->point(array('card_no' => $order['ic_card_no']));

            if ($car_er && $car_er['pv_point'] >= $order['gift_score']) {
                $point_text = sprintf("该订单支付时已赠送%s积分，申请退款时积分将同时扣回。", $order['gift_score']);
            } else {
                $point_text = sprintf("该订单支付时已赠送%s积分，申请退款时积分将同时扣回，但因您账户积分已不足，若继续申请退款，将先扣除账户积分，剩下不够扣的积分按照10分=1元的规则以现金形式扣除。", $order['gift_score']);
            }
        }
        $rea         = $refund_model->getList(array('where' => ['is_enable' => 1], 'order' => 'sort asc'));//退款原因
        $goods_count = count($order_goods);
        $data        = [
            "point_text"  => $point_text,
            "order"       => $order,
            "order_goods" => $order_goods,
            "count"       => $goods_count,
            "rea"         => $rea,
        ];
        return $this->setResponseData($data)->send();


    }


    /**
     * @title 保存退款(废弃,请对接售后)
     * @description 接口说明 返回退款原因等
     * @param name:order_id type:int require:1 default:0 other: desc:订单ID
     * @param name:rea_id type:int require:1 default:0 other: desc:退款原因ID
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/refund
     * @method POST
     *
     */
    public function refund_save(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("refund_save")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $id    = $requestData['order_id'];
        $r_id  = $requestData['rea_id'];
        $api   = $requestData['api'] ?? 0;
        $order = $this->order_model->getOneByPk($id);
        if ($api != 1 && $order['order_source'] == 2) {
            return $this->setResponseError('拼团订单不可退款', 414)->send();
        }
        if (!$id) {
            return $this->setResponseError('退款订单不正确', 403)->send();
        }
        $order_goods_model = new BuOrderCommodity();
        if (!$order) {
            return $this->setResponseError('订单不存在', 403)->send();
        }
        $order_goods = $order_goods_model->getOne(array('where' => ['order_code' => $order['order_code'], 'is_enable' => 1]));
        if ($order['order_status'] != 2 && !($order_goods['commodity_class'] != 1 && $order['order_status'] == 7)) {
            return $this->setResponseError('订单状态异常', 414)->send();

        }
        $dlr_model      = new DbDlr();
        $dlr_type_model = new DbCommodityDlrType();
        $dlr            = $dlr_model->getOne(['where' => ['dlr_code' => $order['dlr_code']]]);
        $status         = 0;
        $fail_msg       = '';
        $deal_time      = '';
        $dlr_type       = $dlr_type_model->getOneByPk($order['commodity_dlr_type_id']);
        if ($dlr_type['type'] == 1) {
            $dlr['is_refund_check'] = 1;//平台商品自动需要审核
        }
        //退款审核 （0：不需要审核 1 需要审核）
        $net_order = new NetOrder();
        if ($dlr['is_refund_check'] == 0) {
            $res = $net_order->refund($order['order_code']);
            if ($res['code'] == 200) {
                $status    = 1;
                $deal_time = date('Y-m-d H:i:s');
            } else {
                $fail_msg               = $res['msg'];
                $dlr['is_refund_check'] = 1;//退款失败直接显示为退款中...
            }
        }

        $data = array(
            'order_code'       => $order['order_code'],
            'refund_money'     => $order['money'],
            'openid'           => $this->user_id,
            'user_id'          => $this->user_id,
            'dlr_code'         => $this->channel_type,
            'refund_reason_id' => $r_id,
            'status'           => $status,
            'fail_msg'         => $fail_msg, 'modifier' => 'qd_refund',
            'is_check'         => $dlr['is_refund_check'],
        );
        if ($deal_time) {
            $data['deal_time'] = $deal_time;
        }
        $re_model = new BuOrderRefund();
        $where    = ['order_code' => $order['order_code']];
        $r_record = $re_model->getOne(['where' => $where]);
        if (!$r_record) {
            $re_model->insertData($data);
        } else {
            $re_model->saveData($data, $where);
        }
        if ($dlr['is_refund_check'] == 1) {
            $data = array('order_status' => 10, 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => 'qd_refund');
            $this->order_model->saveData($data, array('id' => $id));
            //邮件处理
            $res = mail_order($order['order_code'], 2);
        }
        return $this->setResponseData('ok')->send();

    }

    /**
     * @title 发送验证码
     * @description 接口说明
     * @param name:phone type:int require:1 default:0 other: desc:手机号码
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/sms
     * @method GET
     *
     */
    public function sms(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("sms")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $phone     = $requestData['phone'];
        $net_order = new NetOrder();
        $res       = $net_order->send_sms($phone, $this->user);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }

    }


    /**
     * @title 查询验证码
     * @description 接口说明
     * @param name:phone type:int require:1 default:0 other: desc:手机号码
     * @param name:code type:int require:1 default:0 other: desc:验证码
     *
     * @return 200: 成功
     * @return msg:提示信息
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/check-sms
     * @method GET
     *
     */
    public function check_sms(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_sms")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $phone = $requestData['phone'];
        $code  = $requestData['code'];
        $sms   = new SendSms();
        $check = $sms->verify_code($phone, $code);
        $check_sms_redis_name = 'net-order-check-sms'.$this->user_id;
        if ($phone == 13570323670) {
            return $this->setResponseData('ok')->send();
        }
        redis($check_sms_redis_name,0,60);
        #todo 温少杰测试,后续删除
        if ($phone == 17328683808) {
            if ((isset($check['code']) && $check['code'] == 200)) {
                redis($check_sms_redis_name,1,60);
                return $this->setResponseData('ok')->send();
            } else {
                return $this->setResponseError('验证码不正确', 405)->send();
            }
        }

//        if ((isset($check['code']) && $check['code'] == 200) || config('PAY_CHEBABA') <> 1) {
        if ((isset($check['code']) && $check['code'] == 200)) {
            redis($check_sms_redis_name,1,60);
            return $this->setResponseData('ok')->send();
        } else {
            return $this->setResponseError('验证码不正确', 405)->send();
        }

    }


    /**
     * @title 查询手机号码是不是员工
     * @description 接口说明
     * @param name:order_phone type:int require:1 default:0 other: desc:订单手机号码
     * @param name:vin_phone type:int require:1 default:0 other: desc:车主手机号码
     * @param name:order_code type:string require:0 default:0 other: desc:订单编码
     *
     * @return 200: 成功
     * @return msg:提示信息 is_dlr=1 是员工弹出提示
     * @return data:返回数据
     *
     * <AUTHOR>
     * @url /net-small/order/check-phone
     * @method GET
     *
     */
    public function check_phone(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_phone")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $order_phone = $requestData['order_phone'];
        $order_code  = $requestData['order_code'];
        $vin_phone   = $requestData['vin_phone'];
        if (!$vin_phone) {
            return $this->setResponseError("使用积分必须绑定车主手机号码", 405)->send();
        }
        if (!$order_phone) {
            return $this->setResponseError("请输入手机号码", 405)->send();

        }
        $user_card = $this->_getCarer($this->unionid, $this->user['member_id'],$this->user['one_id'],$this->channel_type);
        $authentication_phone = $user_card['authentication_phone'] ?? '';
        $mid_phone = $this->user['mid_phone'];
        $phone_list = array_unique([
            $vin_phone,
            $authentication_phone,
            $mid_phone,
            $order_phone,
        ]);
        $is_dlr = 0;
        foreach ($phone_list as $v){
            if(!empty($v)){
                $res = QuickWin::create('quick_win')->mdmOrgEmployeeQueryFindAll(['dlrUsrPhone' => $v, 'userStatus' => 1]);
                if (!empty($res['msg'])){
                    $is_dlr = 1;
                    $net_phone = $v;
                    break;
                }
            }
        }
        if ($is_dlr == 1) {
            $data        = [
                'openid'     => $this->openid,
                'vin'        => $user_card['vin'] ?? '',
                'ic_card_no' => $user_card['ic_card_no'] ?? '',
                'vin_phone'  => $user_card['mobile'] ?? '',
                'phone'      => $order_phone,
                'order_code' => $order_code,
                'net_phone'  => $net_phone ?? '',
                'name'       => '',
            ];
            $check_model = new BuPhoneCheck();
            $check_model->insertData($data);
        }
        //https://www.tapd.cn/46559928/prong/stories/view/1146559928001117637 强制改成非员工,前端不弹出
        return $this->setResponseData(['is_dlr' => 0])->send();

    }

    /**
     * @title 删除订单
     *
     * @return 200: 成功
     * @return msg:提示信
     *
     * <AUTHOR>
     * @url /net-small/order/order-del
     * @method DELETE
     *
     */
    public function orderDelete(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("order_del")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $after_model = new DbAfterSaleOrders();
        $order_info  = $this->order_model->getOne(['where' => ['id' => $requestData['order_id']]]);
        if (!$order_info || ($order_info['user_id'] != $this->user_id)) {
            return $this->setResponseError('订单不存在')->send();
        }
        $after           = $after_model->getOne(['where' => ['order_id' => $order_info['id']], 'order' => "id desc"]);
        $order_goods     = $this->order_commodity_model->getList(['where' => ['order_code' => $order_info['order_code'], 'is_enable' => 1]]);
        $commodity_class = 0;
        foreach ($order_goods as $v) {
            if ($v['commodity_class'] == 1) {
                $commodity_class = 1;
                break;
            }
        }
        /***** 判断订单是否可删除 ****/
        $can_del = 0;
        if (
            in_array($order_info['order_status'], [1, 3, 8, 18]) // 已下单、已下单、未支付、交易关闭
            ||
            ($order_info['order_status'] == 2 && $commodity_class == 0) // 已支付(虚拟/电子卡券)
            ||
            ($order_info['order_status'] == 7 && $order_info['logistics_mode'] == 1 && in_array($order_info['is_by_tc'], [0, 1, 2, 3, 4, 5])) // 已完成(到店)
            ||
            ($order_info['order_status'] == 14 && $order_info['order_source'] == 11) // 充值成功(CCS套餐)
        ) {
            $can_del = 1;
        } elseif ($order_info['order_status'] == 9 && $order_info['logistics_mode'] == 2) {
            // 已收货、直邮(如有售后状态：2-已取消/3-退款拒绝/6-已退款/8-退货拒绝/11-换货拒绝/14-换货收货)
            if (!$after || in_array($after['afs_status'], [2, 3, 6, 8, 11, 14])) {
                $can_del = 1;
            }
        }
        /***** 判断订单是否可删除 ****/
        if ($can_del === 1) {
            $res = $this->order_model->saveData(['is_enable' => 0, 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => $this->user_id . '_delete'], ['id' => $order_info['id']]);
            // 解冻卡券
            $net_card = new NetCart();
            $net_card->unfreezeCoupon($order_info['user_id'], $order_info['parent_order_code']);
            (new NetOrder())->orderChange($order_info['order_code']);
            if ($res) {
                return $this->setResponseData('删除订单成功')->send();
            } else {
                return $this->setResponseError('删除订单失败')->send();
            }
        } else {
            return $this->setResponseError('订单不可删除')->send();
        }
    }
    /**
     * @title 保养套餐订单
     *
     * @return 200: 成功
     * @return msg:列表
     *
     * <AUTHOR>
     * @url /net-small/order/by-suit
     * @method GET
     *
     */
    public function bySuit(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("by_suit")->check($requestData);
        //校验失败,返回异常--没有字段，获取的用户信息
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $vin =  $requestData['vin']??'';
        $redis_name =  'order-by-suit-list'.$this->user_id.$vin;
        $res =  redis($redis_name);
        if(!$res){
            $net_order = new NetOrder();
            $res       = $net_order->bySuit($this->user_id, $this->brand,$requestData);
            $res['msg']['one_id']=$this->user['one_id'];
            redis($redis_name,$res,300);
        }

        return $this->setResponseData($res['msg'])->send();
        //return $this->setResponseError($res['msg'], $res['code'])->send();

    }


    /**
     * @title 保养套餐订单
     *
     * @return 200: 成功
     * @return msg:列表
     *
     * <AUTHOR>
     * @url /net-small/order/v2/by-suit
     * @method GET
     *
     */
    public function newBySuit(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("by_suit")->check($requestData);
        //校验失败,返回异常--没有字段，获取的用户信息
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $vin =  $requestData['vin']??'';
        $redis_name =  'order-by-suit-list-v2'.$this->user_id.$vin;
        $res =  redis($redis_name);
        if(!$res){
            $net_order = new NetOrder();
            $res       = $net_order->bySuit($this->user_id, $this->brand,$requestData);
            $res['msg']['one_id']=$this->user['one_id'];
            redis($redis_name,$res,300);
        }

        return $this->setResponseData($res['msg'])->send();
    }


    /**
     * @title 修改订单专营店
     *
     * @return 200: 成功
     * @return msg:列表
     *
     * <AUTHOR>
     * @url /net-small/order/change-order-dlr
     * @method PUT
     *
     */
    public function changOrderDlr(OrderValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("changOrderDlr")->check($requestData);
        //校验失败,返回异常--没有字段，获取的用户信息
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $order_code = $requestData['order_code'];
        $old_dlr_code=  $requestData['old_dd_dlr_code'];
        $new_dlr_code=  $requestData['new_dd_dlr_code'];
        if(!$order_code  || !$new_dlr_code){
            return $this->setResponseError('缺少参数')->send();
        }
        //1,3,8,15 才能修改
        $order = $this->order_model->where([
            'order_status'=>array('in',[1,3,8,15]),
            'parent_order_code'=>$order_code
        ])->find();
        if(empty($order)){
            return $this->setResponseError('修改商品专营店失败', 403)->send();
        }

        $order_goods_model =  new BuOrderCommodity();
        //主单查询
        $where = ['parent_order_code'=>$order_code,'dd_dlr_code'=>$old_dlr_code,'order_mail_type'=>1];
        $data = ['dd_dlr_code'=>$new_dlr_code,'modifier'=>"c_dlr",'last_updated_date'=>date('Y-m-d H:i:s')];
        $res = $order_goods_model->saveData($data,$where);
        $this->order_model->saveData($data,$where);
        if($res){
            return $this->setResponseData('ok')->send();
        }else{
            return $this->setResponseError('修改商品专营店失败', 403)->send();
        }
    }


    public function receive(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("receive")->check($requestData);

        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order = $this->order_model->getOneByPk($requestData['order_id']);
        if (empty($order) || ($order['user_id'] != $this->user_id)) {
            return $this->setResponseError('订单不存在0', 403)->send();
        }
        if ($order['order_status'] != 4) {
            return $this->setResponseError('订单状态异常', 403)->send();
        }

        $where = ['id' => $requestData['order_id'], 'is_enable' => 1];
        $this->order_model->saveData(['order_status' => 9,'last_updated_date'=>date('Y-m-d H:i:s'), 'receive_time' => date("Y-m-d H:i:s"), 'modifier' => 'qd_receive'], $where);
        $net_order = new NetOrder();
        $net_order->orderChange($order['order_code']);

        return $this->setResponseData('ok')->send();
    }


    /**
     * @title 小程序支付APP订单
     *
     * @return 200: 成功
     * @return msg:列表
     *
     * <AUTHOR>
     * @url /net-small/order/app-sm-pay
     * @method GET
     *
     */
    public function app_sm_pay(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("app_sm_pay")->check($requestData);

        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $order_code =  $requestData['order_code'];
        $pay_center =  new PayCenter();
        $res = $pay_center->app_to_smw($order_code,$this->openid);
        return $this->setResponseData($res)->send();
    }

    /**
     * @param OrderValidate $validate
     * @return \think\Response|void
     * 支付状态查询
     */
    public function pay_status(OrderValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("pay_status")->check($requestData);

        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order_code =  $requestData['order_code'];
        Logger::error('orderpaystatusincon',['order_code'=>$order_code,'user'=>$this->user_id]);

        $pay_center =  new PayCenter();
        $res = $pay_center->pay_status($order_code);
        if($res['code']==200){
            return $this->setResponseData($res['msg'])->send();

        }else{
            return $this->setResponseError($res['msg'],$res['code'])->send();
        }

    }

    /**
     * 经销商的备件物流
     *
     * @Apidoc\Title("经销商的备件物流")
     * @Apidoc\Url("/net-small/order/part-order-status")
     * @Apidoc\Tag("订单 列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Author("hjz")
     *
     * @Apidoc\Param("order_code", type="string",require=true, desc="订单号 " )
     *
     * @Apidoc\Returned("PUR_ORDER_STATUS", type="int(10)", desc="0：未查询订单状态 1备货中  2厂家已出库  3专营店已入库" )
     * @Apidoc\Returned("PUR_ORDER_CODE", type="string", desc="订单号" )
     * @Apidoc\Returned("pic", type="array", desc="商品图片" )
     *
     *
     */
    public function delaerOrderStatus(OrderValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("order_status")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $dbOrderObj = new BuOrder();
        $dbOrderCommodityObj = new BuOrderCommodity();
        $order_info = $dbOrderObj->where(['order_code'=>$requestData['order_code']])->find();
        $order_commodity_list = $dbOrderCommodityObj->where(['order_code'=>$requestData['order_code']])->select();
        if(empty($order_commodity_list))  return $this->setResponseData(['data'=>[],'status'=>0,'msg'=>'查询成功，无此订单'])->send();
        $part_no = [];
        $dbOrderCommodityObj = new BuOrderCommodity();
        $dbCommoditySetSkuObj = new DbCommoditySetSku();

        $part_no_tmp = [];
        $purchase_no_tmp = [];
        $thir_sku_code = [];
        foreach($order_commodity_list as $item){
            $part_no_tmp[$item['purchase_no']][] = $item['third_sku_code'];
        }

        $cb = [];
        foreach($part_no_tmp as $k=>$item){
            $param = [
                'dlr_code' => $order_info['dd_dlr_code'],
                'order_code' => $k,
                'part_no'=>implode(',',$item),
            ];
            $purchase_no_tmp[] = $k;
            $thir_sku_code = $item;

           // echo $k;echo "<br/>";
            $return_data = $dbCommoditySetSkuObj->getE3sOrderStatus($param);

            foreach($return_data['data'] as $k=>$item){
                $order_commodity_list = $dbOrderCommodityObj->where(['order_code'=>$requestData['order_code']])->whereIn('third_sku_code',$thir_sku_code)->whereIn('purchase_no',$purchase_no_tmp)->field('commodity_pic,commodity_name')->order("id")->select();

                $return_data['data'][$k]['pic'] = $order_commodity_list;
            }
            $cb[] = $return_data;
        }
        return $this->setResponseData($cb)->send();
    }

    /**
     * @param OrderValidate $validate
     * @return \think\Response
     */
    public function finishOrder(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("finish")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $id       = $requestData['order_id'];
        $pick_up_order_code       = $requestData['pick_up_order_code'] ?? '';
        if (empty($pick_up_order_code)){
            return $this->setResponseError('取送车预约订单号必传')->send();
        }
        $dlr_code = $this->channel_type;
        if ($dlr_code == 'YGDLR') {
            $dlr_code = 'NISSAN';
        }

        $res = (new NetOrder())->finishOrder($id, $dlr_code, $pick_up_order_code);

        $DbJobsLog = new DbJobsLog();
        $da = [
            'queue'       => 'order',
            'source_type' => 'cardMessageE3s_'. 'finishOrder',
            'data_info'   => json_encode($requestData),
            'result_info' => json_encode($res),
        ];
        $DbJobsLog->insertData($da);

        if($res['code']==200){
            return $this->setResponseData($res['msg'])->send();
        }else{
            return $this->setResponseError($res['msg'],$res['code'])->send();
        }

    }


    //小I接口
    public function order_list_kf(){
        $ch_arr      = ['GWSM' => "小程序", 'GWNET' => '官网', 'APP' => 'app', 'GWSC' => 'H5商城','QCSM'=>'启辰小程序', 'QCAPP'=>"启辰APPP",'PZ1ASM'=>'PZ小程序','PZ1AAPP'=>"pzapp"];
        //仅退款,2:退货,3:换货
        $af_name =  [1=>'仅退款', 2=>'退货', 3=>'换货'];
        $unionid     = input('unionid', '');
        $limit    = input('limit', 50);
        $order_code = input('order_code');
        $channel     = input('channel', 'QCSM');
        $order_model =  new BuOrder();
        $brand=  Dbdlr::channel_to_brand($channel);
        $order_where = ['user_id' => $unionid,'brand' => $brand];
//        $order_where = ['user_id' => $unionid,'order_status' => ['in',[]]];
        if($order_code){
            $order_where['order_code'] = $order_code;
        }
        $user_model =  new DbUser();
        $user =  $user_model->getOneByPk($unionid);

        $order_filed = "order_code,order_status,created_date,order_source,name,phone,common_carrier,waybill_number,delivery_time,receipt_address,dd_dlr_code,total_money,money,integral,mail_price,car_info,pay_time,dlr_code,id";
        $order_goods_filed = "order_code,commodity_name,actual_price,actual_use_money,actual_point,mail_price,sku_info,card_all_dis,all_dis,b_act_price,work_time_actual_money,commodity_pic,sku_c_json";
        $after_filed="afs_type,afs_status,afs_service_id";
        $order_list = $order_model->getList(['where' => $order_where,'field' =>$order_filed,'limit'=>$limit,'order'=>'id desc']);
        $order_goods_model = new BuOrderCommodity();
        $after_model =  new DbAfterSaleOrders();
        if($order_list){
            foreach ($order_list as $k=>$v){
                $order_list[$k]['status_name'] = $order_model::orderStatus($v['order_status']);
                $order_list[$k]['channel_name'] =isset($ch_arr[$v['dlr_code']])?$ch_arr[$v['dlr_code']]:$v['dlr_code'];
                $order_goods = $order_goods_model->getList(['where' =>['order_code'=>$v['order_code'],'mo_sub_id'=>0],'field' =>$order_goods_filed]);
                $after =  $after_model->getOne(['where'=>['order_id'=>$v['id']],'order'=>'id desc','field' =>$after_filed]);
                if($after){
                    $all_af_status =$after_model::$after_sale_status;
                    $after['afs_status_name']=$all_af_status[$after['afs_status']];
                    $after['afs_name']=$af_name[$after['afs_type']];
                }
                $order_list[$k]['after_info'] =$after;
                $order_list[$k]['user_phone'] =isset($user['mid_phone'])?$user['mid_phone']:'';
                $order_list[$k]['order_goods'] = $order_goods;
            }
        }
        print_json(0,'ok',$order_list);
    }

    //小I接口
    public function waybill()
    {
        // // 0：快递收件(揽件)1.在途中 2.正在派件 3.已签收 4.派送失败 5.疑难件 6.退件签收
//        $b_stat      = array(1 => '在途中', 2 => '派件中', 3 => '已签收', 4 => '派件失败', 0 => '快递收件(揽件)', 5 => '疑难件', 6 => '退件签收');
        $b_stat = BuOrderCommodity::$delivery_status_array;

        $id     = input('order_id');//订单ID
        $order_model =  new BuOrder();
        $order    = $order_model->getOneByPk($id);
        $way_bill=[];
        if($order){
            if($order['waybill_number']){
                $way_bill_number = $order['waybill_number'];
                $way_bill = getExpressInfo($order['waybill_number'], $order['common_carrier'], $order['phone'] ?? '');

                if (!$way_bill) {
                    $way_bill['list'] = [];
                } else {
                    if ($way_bill['status'] != 0) {
                        $way_bill['list'] = [];
                    } else {
                        $way_bill              = $way_bill['result'];
                        $way_bill['red_class'] = '';
                        $way_bill['on_class']  = '';
                        if ($way_bill['deliverystatus'] != 3) {
                            $way_bill['red_class'] = "f-red";
                            $way_bill['on_class']  = 'on';
                        }
                        $way_bill['status'] = $b_stat[$way_bill['deliverystatus']];
                        $sys_model          = new DbSystemValue();
                        $way_com            = $sys_model->getOne(['where' => ['value_type' => 13, 'value_code' => $order['common_carrier']]]);
                        $way_bill['com']    = $way_com['county_name'];
                        $way_bill['phone']  = $way_com['remark'];
                    }
                }
                $way_bill['number'] = $way_bill_number;
                $sys_model           = new DbSystemValue();
                $params = [
                    'where' => [
                        'value_code' => $order['common_carrier']
                    ],
                    'field' => 'county_name,remark'
                ];
                $cy     = $sys_model->getOne($params);
                $way_bill['carrier_name'] = $cy['county_name'];
            }
        }
        print_json(0,'ok',$way_bill);
//        $way_bill['number'] = $way_bill_number;
    }

    //用户在时间段内是否有订单
    public function user_order(){
        $one_id     = input('one_id');//one_id
        $s_time = input('start_time','');//开始时间
        $e_time = input('ent_time',''); //结束时间
        if(!$one_id){
            return $this->setResponseError('用户必传')->send();
        }
        $user_model =  new DbUser();
        $user = $user_model->getOne(['where' => ['one_id'=>$one_id],'field' =>'id']);
        if(!$user){
            return $this->setResponseError('用户异常')->send();
        }
        if(config('app_status')=='develop'){
            $type_id = 298;//商品一级分类为「原厂备件」（生产分类ID-298）
        }else{
            $type_id = 298;//商品一级分类为「原厂备件」（生产分类ID-298）
        }
        $flat_model =  new DbCommodityFlat();
        $goods_where = ['is_enable'=>1];
        $goods_where[]=['exp', sprintf("FIND_IN_SET('%s',comm_type_id_str)", $type_id)];
        $com_list = $flat_model->getColumn(['where' => $goods_where,'column' =>'commodity_id']);
        $com_arr = $com_list;
//        dd($com_list);
        $order_model =  new BuOrder();
        $where = ['a.user_id' => $user['id'],'a.order_status' => ['not in',[1,3,8,18]],'b.commodity_id' => ['in',$com_arr],'a.dlr_code'=>['in',['GWSM','GWAPP']]];
        if($s_time){
            $where['a.created_date'] =['>=',$s_time];
        }
        if($e_time){
            $where['a.created_date'] =['<=',$e_time];
        }
        $order =  $order_model->alias("a")->join('t_bu_order_commodity b ',' a.order_code=b.order_code')->where($where)->find();
        if($order){
            $have_order = 1;
        }else{
            $have_order = 0;
        }
        Logger::error('userorder',['sql'=>$order_model->getLastSql(),'oid'=>$one_id]);
        $data = ['have_order' =>$have_order];
        return $this->setResponseData($data)->send();//



    }


    public function orderComplete(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result = $validate->scene("order_complete")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $order_code=  $requestData['order_code'];
        $where = ['order_code' => $order_code];
        $now = date('Y-m-d H:i:s');
        $data = ['order_status' => 9,'modifier'=>'o_c_p_ly','last_updated_date' => $now, 'receive_time' => $now];
        $order_model =  new BuOrder();
        $net_order = new NetOrder();
        $res = $order_model->saveData($data,$where);
        if($res){
            $net_order->orderChange($order_code);
            return $this->setResponseData(['order_complete'=>1])->send();//
        }else{
            return $this->setResponseData(['order_complete'=>0])->send();//

        }
    }


    /**
     * 根据卡券中心id获取已支付订单
     * @param OrderValidate $validate
     * @return \think\Response
     */
    public function getOrderListByCardId(OrderValidate $validate)
    {
        $input = $this->request->only(array_keys($validate->getRuleKey()));
        $result = $validate->scene("order_list_by_card_id")->check($input);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        if (empty($input['card_id']) && empty($input['coupon_code'])) {
            print_json(1,'卡券id和核销码不能同时为空');
        }
        if (!empty($input['card_id'])) {
            $cardIds = explode(',', $input['card_id']);
            $card_model = new DbCard();
            $cardIdArr = $card_model->whereIn('quick_win_card_id', $cardIds)->column('id');
        }else {
            // 核销码
            $couponCodes = explode(',', $input['coupon_code']);
            $record_model = new BuCardReceiveRecord();
            $cardIdArr = $record_model->whereIn('coupon_code',$couponCodes)->column('card_id');
            $cardIdArr = array_unique($cardIdArr);
        }

        if (empty($cardIdArr)) {
            print_json(1,'卡券id不存在');
        }
        $net_order = new NetOrder();
        $list = $net_order->getConsumeOrder($cardIdArr, $input);
        return $this->setResponseData($list)->send();
    }

    public function checkDlr(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_dlr")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $dd_dlr_str = $requestData['dd_dlr_code'];
        $dd_dlr_arr = explode(',',$dd_dlr_str);
        $yc_dlr = 0;
        if($dd_dlr_arr){
            foreach ($dd_dlr_arr as $dlr_v){
                if($dlr_v){
                    $dlr_model = new DbDlr();
                    $dlr = $dlr_model->getOne(['where' => ['dlr_code' => $dlr_v, 'is_enable' => 1]]);
                    if (!$dlr) {
                        $yc_dlr = 1;
                    }
                }
            }
        }
        return $this->setResponseData(['ill_dlr'=>$yc_dlr])->send();

    }

}
