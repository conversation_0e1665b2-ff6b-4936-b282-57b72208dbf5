<?php

namespace app\net_small\controller;

use app\common\model\bu\BuGwRecommendationBannerCommodity;
use app\common\model\db\DbHomeTitle;
use app\common\model\wlz\WlzCrowdsLogs;
use app\common\net_service\GoodsCustomize;
use app\common\net_service\NetGoods;
use app\common\validate\ComponentDetailValidate;
use app\common\validate\ComponentLayoutValidate;
use ForkModules\Traits\ResponseTrait;
use app\common\model\db\DbHomeSm;
use think\Cache;

/**
 * 页面配置-自定义组件
 */
class Component extends Common
{
    use ResponseTrait;

    public $richComponentType = ['cGoods', 'cLottery', 'cSeckill', 'cSuit', 'cWaterfallGoods', 'cLimit', 'cCoupon', 'cCouponAndGoods', 'cCrowdfunding', 'cFloatWindow', 'cFloatingWindow'];

    public $advComponentType = ['cAdvertising1', 'cAdvertising2', 'cAdvertising'];

    public function pageComponentList(ComponentLayoutValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result = $validate->scene('component_list')->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $home_sm_id = $requestData['home_sm_id']??0;
        if(!$home_sm_id){
            if ($this->channel_type=='GWSM'){
                $home_sm_id=1;
            }else{
                $home_sm_id=2;
            }
        }

        $componentLayout = $this->getComponentData($home_sm_id);
        $componentList   = [];
        foreach ($componentLayout as $index => $componentItem) {
            $component                 = $componentItem;
            $component['component_id'] = $componentItem['type'] . '_' . $index;

            // 组件列表，不返回动态组件配置详情
            if (in_array($componentItem['type'], $this->richComponentType)) {
                unset($component['attribute']);
            }
            $componentList[] = $component;
        }
        // 处理静态组件，如广告、导航
        $componentList = $this->staticComponentProcess($componentList, $this->user, $this->channel_type);
        return $this->setResponseData($componentList)->setStatusCode(200)->send();
    }

    public function componentDetail(ComponentDetailValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        if (empty($validate->check($requestData))) {
            return $this->setResponseError($validate->getError())->send();
        }

        $home_sm_id = $requestData['home_sm_id']??0;
        if(!$home_sm_id){
            if ($this->channel_type=='GWSM'){
                $home_sm_id=1;
            }else{
                $home_sm_id=2;
            }
        }
        $componentList = $this->getComponentData($home_sm_id);
//        if (empty($componentList[$requestData['component_id']])) {
//             $this->setResponseError("找不到组件信息!")->send();
//        }

        $componentId = explode('_', $requestData['component_id'])[1] ?? 0;
        $component = $componentList[$componentId] ?? [];
        if (empty($component)) {
            return $this->setResponseData($component)->setStatusCode(200)->send();
        }
        // 广告组件要判断 act_code
        if (in_array($component['type'], ['cCarousel', 'cAdvertising', 'cAdvertising1','cAdvertisin2', 'cAdvertising3'])) {
            if (!isset($requestData['act_code']) || empty($requestData['act_code'])) {
                return $this->setResponseError('act_code 不能为空')->send();
            }
        }
        $component = (new GoodsCustomize())->componentHandle($component, $requestData, $this->user, $this->channel_type);
        return $this->setResponseData($component)->setStatusCode(200)->send();
    }

    protected function getComponentData($pageId)
    {
        $key             = config('cache_prefix.home') . ':component_data:' . $pageId;
        $componentLayout = redis($key);
        $componentLayout = '';
        if (empty($componentLayout)) {
            $componentData   = DbHomeSm::field('data_json')->find($pageId);
            $componentLayout = $componentData->data_json;
            redis($key, $componentLayout, mt_rand(3600, 7200) * 10);
            // 加入缓存队列，后台编辑时清除缓存
            $redis = Cache::redisHandler();
            $redis->sadd(config('cache_prefix.more_people_more_hm_set') . $pageId, $key);
        }
        return json_decode_assoc($componentLayout);
    }

    protected function getUserGroups($user)
    {
        if (empty($user['id'])) {
            return [];
        }
        $userGroupModel    = new WlzCrowdsLogs();
        $userGroup         = $userGroupModel->where(['user_id' => $user['id']])->field('id,crowd_id,user_id')->select();
        $userGroupCrowdIds = [];
        foreach ($userGroup as $group) {
            $userGroupCrowdIds[] = $group['crowd_id'];
        }
        return $userGroupCrowdIds;
    }

    protected function staticComponentProcess($componentList, $user, $channelType)
    {
        $userGroupCrowdIds = $this->getUserGroups($user);
        // 判断是否需要查询千人千面数据
        $is_select_banner_commodity = 0;
        foreach ($componentList as $index => $componentItem) {
            if ($componentItem['type'] == 'cCarousel') {
                $styleTemplate = $componentItem['attribute']['styleTemplate'] ?? 0; // 1: 常规轮播广告,  2: 组合轮播广告
                $recommended_logical_types = $componentItem['attribute']['recommended_logical_types'] ?? [];
                if (!empty($recommended_logical_types) && $styleTemplate == 1) {
                    foreach ($recommended_logical_types as $logical_index => $logical_type) {
                        if ($logical_type == 4) {
                            $is_select_banner_commodity = 1;
                            break;
                        }
                    }
                }
            }
        }


        // 查询数据组推荐
        $ads_like = [];
        if ($is_select_banner_commodity == 1) {
            $ads_like =getAbsDataNew($this->user_vin,$this->memberid,0,1);
        }

        // 查询bannerList的商品
        $banner_commodity_list = [];
        if (!empty($ads_like)) {
            $commodityIdArr = array_column($ads_like['goods'], 'commodity_id');
            $commodityIds = implode(',', $commodityIdArr);
            $requestData = [
                'pageSize' => count($commodityIdArr),
                'order_by' => "field(a.commodity_id, $commodityIds)",
                'commodity_ids' => $commodityIds,
            ];
            $goodsListResult = (new NetGoods())->goodsList($requestData, $user, $channelType, [], 'home', 'cGoods');
            if (!empty($goodsListResult['msg']['data'])) {
                $commodityIdArr = array_column($goodsListResult['msg']['data'], 'commodity_id');
                $condition              = 'a.banner_id=b.id and b.start_date <="' . date('Y-m-d H:i:s') . '" and (end_type = 1 or end_date > "' . date('Y-m-d H:i:s') . '" ) and b.is_enable=1';
                $field                  = 'a.commodity_id,cover_image,image_type,a.is_enable';
                $banner_commodity_model = new BuGwRecommendationBannerCommodity();
                $banner_map             = ['a.is_enable' => 1, 'a.commodity_id' => ['in', $commodityIdArr]];

                $banner_commodity_list = $banner_commodity_model->alias('a')
                    ->join('t_bu_gw_recommendation_banner b', $condition)
                    ->where($banner_map)
                    ->field($field)
                    ->order("field(a.commodity_id, $commodityIds)")
                    ->group('commodity_id,image_type')
                    ->select();
//                echo $this->user_vin."\n";
//                echo "数据组推荐数据\n";
//                print_r($commodityIds);
//                echo "商品匹配数据\n";
//                print_r(implode(',',$commodityIdArr));
//                echo "banner匹配数据\n";
//                print_json($banner_commodity_list);
            }
        }


        $currentTime       = time();

        foreach ($componentList as $index => $componentItem) {
            if (in_array($componentItem['type'], $this->advComponentType)) {
                $images = [];
                foreach ($componentItem['attribute']['imgs'] as $image) {
                    if (!empty($image['show_time']['start_time']) && $currentTime < strtotime($image['show_time']['start_time'])) {
                        continue;
                    }
                    if (!empty($image['show_time']['end_time']) && $currentTime > strtotime($image['show_time']['end_time'])) {
                        continue;
                    }
                    if (!empty($image['userGroups']) && count(array_intersect($image['userGroups'], $userGroupCrowdIds)) <= 0) {
                        continue;
                    }
                    $images[] = $image;
                }
                if (empty($images)) {
                    unset($componentList[$index]);
                } else {
                    $componentList[$index]['attribute']['imgs'] = $images;
                }
            } else if ($componentItem['type'] == 'cCarousel') {
                // 轮播广告
                $allImages = [];
                // recommended_logical_types 推荐逻辑
                $styleTemplate = $componentItem['attribute']['styleTemplate'] ?? 0; // 1: 常规轮播广告,  2: 组合轮播广告
                $recommended_logical_types = $componentItem['attribute']['recommended_logical_types'] ?? [];

                if (!empty($recommended_logical_types) && $styleTemplate == 1) {
                    foreach ($recommended_logical_types as $logical_index => $logical_type) {
                        // 每个轮播图位置的推荐逻辑. 2:仅后台配置 4: 猜你喜欢推荐
                        if ($logical_type == 2) {
                            foreach ($componentItem['attribute']['imgs'] as $imageIndex => $imageList) {
                                // 获取同一个位置的数据
                                if ($logical_index == $imageIndex) {
                                    $images = [];
                                    foreach ($imageList as $image) {
                                        if (!empty($image['show_time']['start_time']) && $currentTime < strtotime($image['show_time']['start_time'])) {
                                            continue;
                                        }
                                        if (!empty($image['show_time']['end_time']) && $currentTime > strtotime($image['show_time']['end_time'])) {
                                            continue;
                                        }
                                        $image['sort_order'] = $imageIndex + 999;
                                        if (!empty($image['userGroups']) && count(array_intersect($image['userGroups'], $userGroupCrowdIds)) <= 0) {
                                            continue;
                                        } else if (!empty($image['userGroups'])) {
                                            $image['sort_order'] = $imageIndex + 1;
                                        }
                                        $images[] = $image;
                                    }

                                    if (!empty($images)) {
                                        $images = array_values(collection($images)->sort("arraySortOrder")->toArray());
                                        if (in_array($channelType, ['GWAPP', 'GWSM'])) {
                                            $allImages[] = $images[0];
                                        } else {
                                            $allImages = $images;
                                        }
                                    }
                                }
                            }
                        } elseif ($logical_type == 4) {
                            // 猜你喜欢
                            $images = [];
                            if (!empty($banner_commodity_list)) {
                                $isFixedTop = $componentItem['attribute']['isFixedTop'] ?? '';

                                $bannerSize = $componentItem['attribute']['bannerSize'] ?? 0;

                                foreach ($banner_commodity_list as $banner_index => &$banner) {
                                    // 顶部导航
                                    if ($isFixedTop == true) {
                                        if ($banner['image_type'] == 1 && $banner['is_enable'] == 1) {
                                            $images = [
                                                "title"=>"",//·主标题
                                                "titleColor"=>"#333333",//主标题颜色
                                                "subTitle"=>"",//副标题
                                                "subTitleColor"=>"#333333",//·副标题颜色
                                                "path" => "", //忽略，后台显示路径
                                                'src' => $banner['cover_image'], //图片链接地址
                                                'savePath' => [
                                                    "type" => "1", //跳转类型：1:商品
                                                    "id"=> $banner['commodity_id'] ,  //type=1,2，根椐id跳指定商品
                                                    "url" => "",
                                                    'h5_url' => '',
                                                    'app_url'=>'',
                                                ],
                                            ];
                                            $banner['is_enable'] = 0;
                                            $allImages[] = $images;
                                            break;
                                        }
                                    } else {
                                        // 非顶部导航
                                        if ($bannerSize == $banner['image_type'] && $banner['is_enable'] == 1) {
                                            $images = [
                                                "title"=>"",//·主标题
                                                "titleColor"=>"#333333",//主标题颜色
                                                "subTitle"=>"",//副标题
                                                "subTitleColor"=>"#333333",//·副标题颜色
                                                "path" => "", //忽略，后台显示路径
                                                'src' => $banner['cover_image'], //图片链接地址
                                                'savePath' => [
                                                    "type" => "1", //跳转类型：1:商品
                                                    "id"=> $banner['commodity_id'] ,  //type=1,2，根椐id跳指定商品
                                                    "url" => "",
                                                    'h5_url' => '',
                                                    'app_url'=>'',
                                                ],
                                            ];
                                            $banner['is_enable'] = 0;
                                            $allImages[] = $images;
                                            break;
                                        }
                                    }
                                }

                            }
                            // 猜你喜欢没有匹配到数据组推荐的数据  选用默认数据
                            if (empty($images)) {
                                foreach ($componentItem['attribute']['imgs'] as $imageIndex => $imageList) {
                                    // 获取同一个位置的数据
                                    if ($logical_index == $imageIndex) {
                                        foreach ($imageList as $image) {
                                            if (!empty($image['show_time']['start_time']) && $currentTime < strtotime($image['show_time']['start_time'])) {
                                                continue;
                                            }
                                            if (!empty($image['show_time']['end_time']) && $currentTime > strtotime($image['show_time']['end_time'])) {
                                                continue;
                                            }
                                            $image['sort_order'] = $imageIndex + 999;
                                            if (!empty($image['userGroups']) && count(array_intersect($image['userGroups'], $userGroupCrowdIds)) <= 0) {
                                                continue;
                                            } else if (!empty($image['userGroups'])) {
                                                $image['sort_order'] = $imageIndex + 1;
                                            }
                                            $images[] = $image;
                                        }

                                        if (!empty($images)) {
                                            $images = array_values(collection($images)->sort("arraySortOrder")->toArray());
                                            if (in_array($channelType, ['GWAPP', 'GWSM'])) {
                                                $allImages[] = $images[0];
                                            } else {
                                                $allImages = $images;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                } else {
                    // 兼容旧数据
                    foreach ($componentItem['attribute']['imgs'] as $imageIndex => $imageList) {
                        $images = [];
                        foreach ($imageList as $image) {
                            if (!empty($image['show_time']['start_time']) && $currentTime < strtotime($image['show_time']['start_time'])) {
                                continue;
                            }
                            if (!empty($image['show_time']['end_time']) && $currentTime > strtotime($image['show_time']['end_time'])) {
                                continue;
                            }
                            $image['sort_order'] = $imageIndex + 999;
                            if (!empty($image['userGroups']) && count(array_intersect($image['userGroups'], $userGroupCrowdIds)) <= 0) {
                                continue;
                            } else if (!empty($image['userGroups'])) {
                                $image['sort_order'] = $imageIndex + 1;
                            }
                            $images[] = $image;
                        }
                        if (!empty($images)) {
                            $images = array_values(collection($images)->sort("arraySortOrder")->toArray());
                            if (in_array($channelType, ['GWAPP', 'GWSM'])) {
                                $allImages[] = $images[0];
                            } else {
                                $allImages = $images;
                            }
                        } else {
                            unset($componentList[$index]['attribute']['imgs'][$imageIndex]);
                        }
                    }
                }



                if (empty($allImages)) {
                    unset($componentList[$index]);
                } else {
                    $componentList[$index]['attribute']['imgs'] = $allImages;
                }
            } else if ($componentItem['type'] == 'cTabs') {
                $componentList[$index]['is_classify'] = 1;
            }
        }
        return array_values($componentList);
    }


    /**
     * 首页标题
     * @param ComponentLayoutValidate $validate
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function title(ComponentLayoutValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result = $validate->scene('title')->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $title_model = new DbHomeTitle();

        $map = [
            'page_type' => $requestData['page_id'],
            'is_enable' => 1
        ];
        $field = 'id,name,page_cate,is_show_car,is_show_classify,classify_id,path,save_path,home_sm_id';
        $list = $title_model->where($map)->field($field)->order('sort')->select();
        $index_tip = redis(config('cache_prefix.index_tip') . $this->user_id . $this->channel_type);
        foreach ($list as $key => $item) {
            if (!empty($item['save_path'])) {
                $item['save_path'] = json_decode($item['save_path'], true);
            } else {
                $item['save_path'] = (object)[];
            }
            $list[$key]['index_tip']=$index_tip;
        }
        redis(config('cache_prefix.index_tip') . $this->user_id . $this->channel_type,null);

        return $this->setResponseData($list)->setStatusCode(200)->send();
    }

    //缓存一个时间给前端
    //未登录我存app内 已登录用你新出的接口
    //type:0首页
    public function cache_time()
    {
        $type =  input('type',0);
        $date_time =  input('date_time','');
        $redis_name = 'every-cache-time-1:'.$type.$this->channel_type.$this->user_id;
        if(!$date_time){
            $date_time =  redis($redis_name);
        }else{
            redis($redis_name,$date_time,3600*24*20);//存20天
//            redis($redis_name,$date_time,60*5);//测试，-五分钟
        }
        return $this->setResponseData($date_time)->send();

    }
}
