<?php
/**
 * Created by PhpStor<PERSON>.
 * User: lzx
 * Date: 2017/6/8
 * Time: 下午5:31
 */

namespace app\net_small\controller;

use app\common\command\Base;
use app\common\model\act\AcByArea;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderSettlement;
use app\common\model\bu\BuToE3sDetail;
use app\common\model\bu\BuToE3sIndex;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCustomerService;
use app\common\model\db\DbDlr;

use app\common\model\db\DbDlrGroup;
use app\common\model\db\DbEmployee;
use app\common\model\db\DbEmployeeLog;
use app\common\model\db\DbJobsLog;
use app\common\model\db\DbLyPayLog;
use app\common\model\db\DbLyPaySettle;
use app\common\model\db\DbSeparateAccountRules;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\model\inter\IRequestLog;
use app\common\model\inter\IResponesLog;
use app\common\net_service\Common;
use app\common\net_service\E3sPostData;
use app\common\net_service\LyPay;
use app\common\net_service\MaintainService;
use app\common\net_service\NetOrder;
use app\common\net_service\PayCenter;
use app\common\net_service\SendSms;
use app\common\port\connectors\CarLive;
use app\common\port\connectors\Crm;
use app\common\port\connectors\HaoWan;
use app\common\port\connectors\Maintain;
use app\common\port\connectors\InterFun;
use app\common\port\connectors\Payment;

use app\common\port\connectors\QuickWin;
use app\common\service\OrderService;
use app\common\service\PushMaintainService;
use app\common\validate\Goods as GoodsValidate;
use app\net_small\command\OrderRemind;
use app\net_small\command\OrderSettle;
use app\net_small\command\OrderSettleInfo;
use think\Controller;
use think\Exception;
use think\Log;
use think\Model;
use think\Queue;
use think\Validate;
use tool\Logger;

class Normal extends Controller
{
    private $orderS = array(1 => '待付款', 2 => '已付款', 3 => '已取消', 4 => '已发货', 5 => '已退款', 6 => '已过期', 7 => '已结算', 8 => '待付款', 9 => '已收货', 10 => '退款中', 11 => '发货中', 12 => '待发货', 13 => '安装中', 14 => '充值成功', 15 => "已付定金");
    private $payType = array(1 => '现金', 2 => '积分', 3 => '卡劵', 4 => '现金+积分', 5 => '现金+卡劵', 6 => '积分+卡劵', 7 => '现金+积分+卡劵');

    public function cho_store()
    {
        $this->redirect(url('Index/index', array('dlr_code' => "GWSC"), false, true));
        die();
        $name      = input('search');
        $dlr_model = new DbDlr();
        session(config('appid') . '-openid', '');
        $where = array('a.dlr_code' => "GWSC", 'a.id' => ['>', 2]);
        if ($name) {
            $where['a.dlr_name'] = ['like', '%' . $name . '%'];
        }
        $dlr_list = $dlr_model->getAuthDlr(['where' => $where]);
//        echo $dlr_model->getLastSql();
        if ($dlr_list) {
            foreach ($dlr_list as $k => $v) {
                $dlr_list[$k]['url'] = url('Index/index', array('dlr_code' => $v['dlr_code']), false, true);
            }
        }
        $this->assign('list', $dlr_list);
        $this->assign('name', $name);
        return $this->fetch('stores');
    }

    /**
     * @title 客服获取商品
     * @description 接口说明
     * @param name:goods_id type:int require:1 default:0 other: desc:商品ID
     * @param name:channnel type:string require:0 default:0 other: desc:渠道默认GWSM
     *
     * @return error: 0 成功 1 错误参考提示msg
     * @return msg:提示信息
     * @return data:返回数据地区列表或者专营店列表
     *
     * <AUTHOR>
     * @url /net-small/normal/goods
     * @method GET
     *
     */
    public function goods_kf()
    {
        $ch_arr      = ['GWSM' => "小程序", 'GWNET' => '官网', 'APP' => 'app', 'GWSC' => 'H5商城','QCSM'=>'启辰小程序', 'QCAPP'=>"启辰APPP",'PZ1ASM'=>'PZ小程序','PZ1AAPP'=>"pzapp"];
        $id          = input('goods_id');
        $channel     = input('channel', 'GWSM');
        $unionid     = input('unionid', '');
        $goods_model = new DbCommodityFlat();
        $goods       = $goods_model->getOne(['where' => ['commodity_id' => $id]]);
        $goods_type  = '';

        if ($goods) {
            $goods_type_model = new DbCommodityType();
            $goods_type_arr   = $goods_type_model->getList(['where' => ['id' => ['in', $goods['comm_type_id_str']]], 'field' => "comm_type_name"]);
//            echo $goods_type_model->getLastSql();

            if ($goods_type_arr) {
                foreach ($goods_type_arr as $v) {
                    $goods_type .= $v['comm_type_name'] . ',';
                }
                $goods_type = trim($goods_type, ',');
            }
        } else {

            $goods = [
                'commodity_id'   => $id,
                'commodity_name' => '非商品渠道进线.' . $unionid,
                'cover_image'    => '',
                'final_price'    => 0,
                'price'          => 0,
            ];
        }
        $qd = isset($ch_arr[$channel]) ? $ch_arr[$channel] : $channel;
        if ($id == 67) {
            $qd .= '-首页';
        }
        $data = array(
            'id'          => $goods['commodity_id'],
            'name'        => $qd . ' ' . $goods['commodity_name'] . '-' . $goods['commodity_id'],
            'imageurl'    => $goods['cover_image'],
            'url'         => '',
            'currency'    => "￥",
            'siteprice'   => $goods['final_price'],//现价格
            'marketprice' => $goods['price'],
            'category'    => $goods_type,//分类名称
            'brand'       => "",
            'custom1'     => $unionid,//扩展字段，最多到10
        );
        print_json(0, 'ok', $data);
    }

    public function order_list_kf(){
        $comm =  new \app\net_small\controller\Common();
        $ack = $comm->goCheckToken();
        if (empty($ack)) {
            print_json(1,'Bad Auth');
        }
        $ch_arr      = ['GWSM' => "小程序", 'GWNET' => '官网', 'APP' => 'app', 'GWSC' => 'H5商城','QCSM'=>'启辰小程序', 'QCAPP'=>"启辰APPP",'PZ1ASM'=>'PZ小程序','PZ1AAPP'=>"pzapp"];
        //仅退款,2:退货,3:换货
        $af_name =  [1=>'仅退款', 2=>'退货', 3=>'换货'];
        $unionid     = input('unionid', '');
        $limit    = input('limit', 50);
        $order_code = input('order_code');
        $channel     = input('channel', 'QCSM');
        $order_model =  new BuOrder();
        $brand=  Dbdlr::channel_to_brand($channel);
        $user_model =  new DbUser();
        $user =  $user_model->getOne(['where'=>['plat_id' => $unionid]]);
        $order_where = ['user_id' => $user['id'],'brand' => $brand];
//        $order_where = ['user_id' => $unionid,'order_status' => ['in',[]]];
        if($order_code){
            $order_where['order_code'] = $order_code;
        }


        $order_filed = "order_code,order_status,created_date,order_source,name,phone,common_carrier,waybill_number,delivery_time,receipt_address,dd_dlr_code,total_money,money,integral,mail_price,car_info,pay_time,dlr_code,id";
        $order_goods_filed = "order_code,commodity_name,actual_price,actual_use_money,actual_point,mail_price,sku_info,card_all_dis,all_dis,b_act_price,work_time_actual_money,commodity_pic,sku_c_json";
        $after_filed="afs_type,afs_status,afs_service_id";
        $order_list = $order_model->getList(['where' => $order_where,'field' =>$order_filed,'limit'=>$limit,'order'=>'id desc']);
        $order_goods_model = new BuOrderCommodity();
        $after_model =  new DbAfterSaleOrders();
        if($order_list){
            foreach ($order_list as $k=>$v){
                $order_list[$k]['status_name'] = $order_model::orderStatus($v['order_status']);
                $order_list[$k]['channel_name'] =isset($ch_arr[$v['dlr_code']])?$ch_arr[$v['dlr_code']]:$v['dlr_code'];
                $order_goods = $order_goods_model->getList(['where' =>['order_code'=>$v['order_code'],'mo_sub_id'=>0],'field' =>$order_goods_filed]);
                $after =  $after_model->getOne(['where'=>['order_id'=>$v['id']],'order'=>'id desc','field' =>$after_filed]);
                if($after){
                    $all_af_status =$after_model::$after_sale_status;
                    $after['afs_status_name']=$all_af_status[$after['afs_status']];
                    $after['afs_name']=$af_name[$after['afs_type']];
                }
                $order_list[$k]['after_info'] =$after;
                $order_list[$k]['user_phone'] =isset($user['mid_phone'])?$user['mid_phone']:'';
                $order_list[$k]['order_goods'] = $order_goods;
            }
        }
        print_json(0,'ok',$order_list);
    }

    public function waybill()
    {
        $comm =  new \app\net_small\controller\Common();
        $ack = $comm->goCheckToken();
        if (empty($ack)) {
            print_json(1,'Bad Auth');
        }
        // // 0：快递收件(揽件)1.在途中 2.正在派件 3.已签收 4.派送失败 5.疑难件 6.退件签收
//        $b_stat      = array(1 => '在途中', 2 => '派件中', 3 => '已签收', 4 => '派件失败', 0 => '快递收件(揽件)', 5 => '疑难件', 6 => '退件签收');
        $b_stat = BuOrderCommodity::$delivery_status_array;

        $id     = input('order_id');//订单ID
        $order_model =  new BuOrder();
        $order    = $order_model->getOneByPk($id);
        $way_bill=[];
        if($order){
            if($order['waybill_number']){
                $way_bill_number = $order['waybill_number'];
                $way_bill = getExpressInfo($order['waybill_number'], $order['common_carrier'], $order['phone'] ?? '');

                if (!$way_bill) {
                    $way_bill['list'] = [];
                } else {
                    if ($way_bill['status'] != 0) {
                        $way_bill['list'] = [];
                    } else {
                        $way_bill              = $way_bill['result'];
                        $way_bill['red_class'] = '';
                        $way_bill['on_class']  = '';
                        if ($way_bill['deliverystatus'] != 3) {
                            $way_bill['red_class'] = "f-red";
                            $way_bill['on_class']  = 'on';
                        }
                        $way_bill['status'] = $b_stat[$way_bill['deliverystatus']];
                        $sys_model          = new DbSystemValue();
                        $way_com            = $sys_model->getOne(['where' => ['value_type' => 13, 'value_code' => $order['common_carrier']]]);
                        $way_bill['com']    = $way_com['county_name'];
                        $way_bill['phone']  = $way_com['remark'];
                    }
                }
                $way_bill['number'] = $way_bill_number;
                $sys_model           = new DbSystemValue();
                $params = [
                    'where' => [
                        'value_code' => $order['common_carrier']
                    ],
                    'field' => 'county_name,remark'
                ];
                $cy     = $sys_model->getOne($params);
                $way_bill['carrier_name'] = $cy['county_name'];
            }
        }
        print_json(0,'ok',$way_bill);



//        $way_bill['number'] = $way_bill_number;
    }

    public function test_js()
    {
        $model = new AcByArea();
        $sql   = "SELECT area_prov, area_city,id FROM t_ac_by_area GROUP BY area_city ORDER BY id ASC ";
        $res   = $model->query($sql);

        $prov_arr = array_unique(array_column($res, 'area_prov'));
        $all_arr  = [];
        foreach ($prov_arr as $k => $v) {
            $all_arr[$k]['name'] = $v;
            $all_arr[$k]['code'] = $k;
            foreach ($res as $kk => $vv) {
                $vvv['name'] = $vv['area_city'];
                $vvv['code'] = $vv['id'];
                if ($v == $vv['area_prov']) {
                    $all_arr[$k]['sub'][] = $vvv;
                }
            }
        }
        $js = json_encode_cn(array_values($all_arr));
        echo $js;
        echo 22;
    }


    public function employee()
    {
        $back_url      = input('back_url', urlencode(url('index/index', '', false, true)));
        $openid        = input('openid');
        $emp_log_model = new DbEmployeeLog();
        $emp_log       = $emp_log_model->getOne(['where' => ['openid' => $openid]]);
        if ($emp_log) {
            $this->redirect(urldecode($back_url));
        } else {
            $this->assign('save_url', url('save_emp'));
            $this->assign('openid', $openid);
            $this->assign('back_url', urldecode($back_url));
            return $this->fetch('normal/employee');
        }
    }

    public function save_emp()
    {
        $user_id   = input('post.user_id');
        $user_name = input('post.user_name');
        $openid    = input('post.openid');
        $emp_model = new DbEmployee();
        $emp       = $emp_model->getOne(['where' => ['user_id' => $user_id, 'name' => $user_name, 'is_enable' => 1]]);
//        echo $emp_model->getLastSql();
        if ($emp) {
            $emp_log_model = new DbEmployeeLog();
            $data          = ['openid' => $openid, 'user_id' => $user_id];
            $res           = $emp_log_model->insertData($data);
//            var_dump($res);
//            echo $emp_log_model->getLastSql();
            print_json(0, 'ok');
        } else {
            print_json(1, '用户信息不正确');
        }
    }


    /**
     * 一个定时任务去跑结算单确认ddd
     */
    public function conSett()
    {
        $user = input('user');
        $this->check_user($user);
        $order_code      = input('order_code', '');
        $net_order       = new NetOrder();
        $order_set_model = new BuOrderSettlement();
        $to_date         = date('Y-m-d') . ' 00:00:00';
        $where           = ['is_enable' => 1, 'state' => 2, 'pay_time' => ['<', $to_date]];
        if ($order_code) {
            $where['order_code'] = $order_code;
        }
        $order_list = $order_set_model->getList(['where' => $where]);
        foreach ($order_list as $v) {
            $res = $net_order->sumbitSett($v['order_code']);
            var_dump($res);
        }


    }


    private function _getCustomer($good_type_id)
    {
        $model    = new DbCustomerService();
        $where    = array('comm_type_id' => $good_type_id);
        $_params  = array(
            'where' => $where
        );
        $customer = $model->getOne($_params);
        return $customer;
    }


    public function hand_create_set($parent_code)
    {
        //实际支付>0
        $parent_order_code = input('order_code', 'GWSM202107122048440476218-1');
        if ($parent_code) {
            $parent_order_code = $parent_code;
        }
        $order_model       = new BuOrder();
        $order_goods_model = new BuOrderCommodity();
        $sep_rule_model    = new DbSeparateAccountRules();
        $order_set_model   = new BuOrderSettlement();
        //拆单之后另外结算
        $order_list = $order_model->getList(['where' => ['order_code' => $parent_order_code, 'parent_order_type' => ["<>", 3]]]);

        $in_data     = [];
        $notice_url  = config("DOMAIN_INNER_URL") . "net_small/pay_plat/settlement";
        $goods_class = 1;
        $order_code  = '';
        foreach ($order_list as $v) {
            $set_data = [];
            if ($v['money'] > 0) {
                $order_goods   = $order_goods_model->getList(['where' => ['order_code' => $v['order_code']]]);
                $plat_m        = 0;
                $dlr_m         = 0;
                $account       = '';
                $fees          = '';
                $all_set_money = 0;
                foreach ($order_goods as $vv) {
                    if (in_array($vv['commodity_class'], [2, 3, 4])) {
                        $goods_class = 2;
                        $order_code  = $v['order_code'];
                    }
//                    var_dump($vv['spr_id']);
                    if ($vv['spr_id'] > 0) {
                        //加入预售点...
                        if ($vv['pre_sale_id'] > 0) {
                            $set_money = $v['money'];
                            if ($v['order_status'] == 15) {
                                $v['pay_order_code']   = $v['pay_order_code2'];
                                $v['cashier_trade_no'] = $v['cashier_trade_no2'];
                            }
                        } else {
                            $set_money = $vv['actual_use_money'];
                        }
                        $all_set_money += $set_money;
                        $sep_rule      = $sep_rule_model->getOneByPk($vv['spr_id']);
                        $sep           = json_decode($sep_rule['rule_info'], true);
                        $plat_m        += round($set_money * $sep['platform'], 1);
                        if ($vv['order_mail_type'] == 2) {
                            $plat_m += round($set_money * $sep['dlr'], 1);
                        } else {
                            $dlr_m += round($set_money * $sep['dlr'], 1);
                        }
                    } else {
                        return false;//没有设置分账不往下走
                        continue;
                    }
                }
                $mail_price = $v['mail_price'] ?? 0;
                $plat_all_m = round($plat_m, 0) + ($mail_price * 100);
                if ($plat_all_m > 0) {
                    $account    .= $order_set_model->setAccount("platform") . ',';
                    $fees       .= $plat_m . ',';
                    $set_data[] = [
                        "settlement_fee"    => $plat_all_m,//运费加到平台
                        "center_account_id" => $order_set_model->setAccount("platform"),
                    ];
                    $dlr_m      = $all_set_money * 100 - $plat_all_m;
                    $dlr_m      = round($dlr_m, 0);
                }
                $account .= $order_set_model->setAccount($v['dlr_code']) . ',';
                $fees    .= $dlr_m . ',';
                if ($dlr_m > 0) {
                    $set_data[] = [

                        "settlement_fee"    => $dlr_m,
                        "center_account_id" => $order_set_model->setAccount($v['dlr_code']),

                    ];
                }
                $params = [
                    'cashier_trade_no'  => $v['cashier_trade_no'],
                    'settlement_remark' => "商城分账",
                    'notify_url'        => $notice_url,
                    'settlement'        => $set_data,
                ];
                var_dump($params);
                $res = Payment::create('payment')->createSett($params);
                var_dump($res);
                $log = new DbJobsLog();
                $log->insertData([
                    'queue'       => 'default',
                    'source_type' => 'settlement',
                    'data_info'   => json_encode($params),
                    'result_info' => json_encode($res),
                ]);
                if (!isset($res['message'])) {
                    if ($goods_class == 2) {
                        $goods_class = 3;
                    }
                    foreach ($res as $rv) {
                        $in_data[] = [
                            'order_code'                    => $v['order_code'],
                            'pay_order_code'                => $v['pay_order_code'],
                            'cashier_trade_no'              => $v['cashier_trade_no'],
                            'center_account_ids'            => trim(',', $account),
                            'settlement_fees'               => trim(',', $fees),
                            'cashier_settlement_no'         => $rv['cashier_settlement_no'],
                            'cashier_appid'                 => $rv['cashier_appid'],
                            'total_fee'                     => $rv['total_fee'],
                            'body'                          => $rv['body'],
                            'settlement_fee'                => $rv['settlement_fee'],
                            'settlement_net_fee'            => $rv['settlement_net_fee'],
                            'settlement_can_refund_balance' => $rv['settlement_can_refund_balance'],
                            'intention_account_id'          => $rv['intention_account_id'],
                            'confirm_account_id'            => $rv['confirm_account_id'],
                            'settlement_state'              => $rv['settlement_state'],
                            'finish_refund_date'            => $rv['finish_refund_date'],
                            'settlement_serial_id'          => $rv['settlement_serial_id'],
                            'settlement_front_serial_id'    => $rv['settlement_front_serial_id'],
                            'order_id'                      => $rv['order_id'],
                            'notify_url'                    => $rv['notify_url'],
                            'cashier_refund_no'             => $rv['cashier_refund_no'],
                            'remark'                        => $rv['remark'],
                            'pay_time'                      => $v['pay_time'],//支付时间
                        ];
                    }
                }
            }
        }
        var_dump($in_data);
        $order_set_model->insertAll($in_data);
        if ($goods_class == 3) {
            //ccs， 虚拟商品，卡券 都在这个时候触发确定结算单
            $net_order = new NetOrder();
            $net_order->confirmSett($order_code);
        }
    }

    public function hand_c_set_more()
    {
        $sql        = "select * from t_bu_order where pay_order_code in ('GWSM202109122125224832949','GWSM202109122055434565713','GWSM202109121338193553971','GWSM202109121052483411583','GWSM202109112210289772937','GWSM202109112153305933505','GWSM202109111238081599956','GWSM202109102342109043272','GWSM202109102341097453347','GWSM202109102313218767885','GWSM202109102232026080229','GWSM202109101758065193668','GWSM202109101304532339974','GWSM202109101038058343991','GWSM202109100813162184101','GWSM202109092254446374944','GWSM202109091646413889690','GWSM202109091617139834332','GWSM202109091328253240129','GWSM202109082236231289694','GWSM202109081204013553968','GWSM202109072207598715407','GWSM202109071615069754362','GWSM202109071605175052006','B0701202109071416321851362','GWSM202109062217149339768','GWSM202109061955277559954','GWSM202109061624482427138','GWSM202109061453151456802','GWSM202109060728546345365','GWSM202109051520217316777','GWSM202109051512359779923','GWSM202109051356496620963','GWSM202109050913188896669','GWSM202109042336142078857','GWSM202109042137045150737','GWSM202109041931504292576','GWSM202109041908589003415','GWSM202109041300357500074','GWSM202109041030386849135','GWSM202109040017117767233','GWSM202109031724272924539','GWSM202109022054238435715','GWSM202109021528585310694','GWSM202109021224007917456','GWSM202109021056036461357','GWSM202109020859365369001','GWSM202109012010398714460','GWSM202109010802175564514','GWSM202108311528490540868','GWSM202108311045179769940','GWSM202108310953114448466','GWSM202108302011396771703','GWSM202108300951548568299','GWSM202108292030441324678','GWSM202108291643415834609','GWSM202108291624075113542','GWSM202108291619227365042','GWSM202108291110234191468','GWSM202108281430265010453','GWSM202108280139315364799','GWSM202108271919072748896','GWSM202108271534043869554','GWSM202108262117045377195','GWNET202108261731423808613','GWNET202108261720454643493','GWSM202108261632561416811','GWSM202108261240563720270'
) and order_status not in(1,8,3,18) and order_code not in (select order_code from t_bu_order_settlement GROUP BY order_code)

order by order_status desc ";
        $order_code = input('order_code');
        if ($order_code) {
            if ($order_code) {
                $sql = sprintf("SELECT * from t_bu_order where order_code ='%s' ", $order_code);
            }
        }
        $model = new BuOrder();
        $list  = $model->query($sql);
        if ($list) {
            foreach ($list as $v) {
                $this->hand_create_set($v['order_code']);
            }
        } else {
            echo "没数据";
        }

    }


    //确定结算单 状态2
    public function con_sett()
    {
        $user = input('user');
        $this->check_user($user);
        $order_code = input('order_code');
        $net_order  = new NetOrder();
        $res        = $net_order->confirmSett($order_code);
        var_dump($res);
    }

    public function test_create_set()
    {
        //实际支付>0
        $parent_order_code = input('order_code', 'GWNET202107160836164160685');
        $order_model       = new BuOrder();
        $order_goods_model = new BuOrderCommodity();
        $sep_rule_model    = new DbSeparateAccountRules();
        $order_set_model   = new BuOrderSettlement();
        //拆单之后另外结算
        $order_list = $order_model->getList(['where' => ['parent_order_code' => $parent_order_code, 'parent_order_type' => ["<>", 3]]]);

        $in_data     = [];
        $notice_url  = config("DOMAIN_URL") . "net_small/pay_plat/settlement";
        $goods_class = 1;
        $order_code  = '';
        foreach ($order_list as $v) {
            $set_data = [];
            if ($v['money'] > 0) {
                $order_goods   = $order_goods_model->getList(['where' => ['order_code' => $v['order_code']]]);
                $plat_m        = 0;
                $dlr_m         = 0;
                $account       = '';
                $fees          = '';
                $all_set_money = 0;
                foreach ($order_goods as $vv) {
                    if (in_array($vv['commodity_class'], [2, 3, 4])) {
                        $goods_class = 2;
                        $order_code  = $v['order_code'];
                    }
                    if ($vv['spr_id'] > 0) {
                        //加入预售点...
                        if ($vv['pre_sale_id'] > 0) {
                            $set_money = $v['money'];
                            if ($v['order_status'] == 15) {
                                $v['pay_order_code']   = $v['pay_order_code2'];
                                $v['cashier_trade_no'] = $v['cashier_trade_no2'];
                            }
                        } else {
                            $set_money = $vv['actual_use_money'];
                        }
                        $all_set_money += $set_money;
                        $sep_rule      = $sep_rule_model->getOneByPk($vv['spr_id']);
                        $sep           = json_decode($sep_rule['rule_info'], true);
                        $plat_m        += round($set_money * $sep['platform'], 1);
                        if ($vv['order_mail_type'] == 2) {
                            $plat_m += round($set_money * $sep['dlr'], 1);
                        } else {
                            $dlr_m += round($set_money * $sep['dlr'], 1);
                        }
                    } else {
                        return false;//没有设置分账不往下走
                        continue;
                    }
                }
                $mail_price = $v['mail_price'] ?? 0;
                $plat_all_m = round($plat_m, 0) + ($mail_price * 100);
//                die();
                if ($plat_all_m > 0) {
                    $account    .= $order_set_model->setAccount("platform") . ',';
                    $fees       .= $plat_m . ',';
                    $set_data[] = [
                        "settlement_fee"    => $plat_all_m,//运费加到平台
                        "center_account_id" => $order_set_model->setAccount("platform"),
                    ];
                    $dlr_m      = $all_set_money * 100 - $plat_all_m;
                    $dlr_m      = round($dlr_m, 0);
                }
                $account .= $order_set_model->setAccount($v['dlr_code']) . ',';
                $fees    .= $dlr_m . ',';
                if ($dlr_m > 0) {
                    $set_data[] = [

                        "settlement_fee"    => round($dlr_m, 0),
                        "center_account_id" => $order_set_model->setAccount($v['dlr_code']),

                    ];
                }
                $params = [
                    'cashier_trade_no'  => $v['cashier_trade_no'],
                    'settlement_remark' => "商城分账",
                    'notify_url'        => $notice_url,
                    'settlement'        => $set_data,
                ];
                var_dump($params);
//                $res    = Payment::create('payment')->createSett($params);
//                $this->logDb([
//                    'queue'       => 'default',
//                    'source_type' => 'settlement',
//                    'data_info'   => json_encode($params),
//                    'result_info' => json_encode($res),
//                ]);
//                if (!isset($res['message'])) {
//                    if($goods_class==2){
//                        $goods_class=3;
//                    }
//                    foreach ($res as $rv) {
//                        $in_data[] = [
//                            'order_code'                    => $v['order_code'],
//                            'pay_order_code'                => $v['pay_order_code'],
//                            'cashier_trade_no'              => $v['cashier_trade_no'],
//                            'center_account_ids'            => trim(',', $account),
//                            'settlement_fees'               => trim(',', $fees),
//                            'cashier_settlement_no'         => $rv['cashier_settlement_no'],
//                            'cashier_appid'                 => $rv['cashier_appid'],
//                            'total_fee'                     => $rv['total_fee'],
//                            'body'                          => $rv['body'],
//                            'settlement_fee'                => $rv['settlement_fee'],
//                            'settlement_net_fee'            => $rv['settlement_net_fee'],
//                            'settlement_can_refund_balance' => $rv['settlement_can_refund_balance'],
//                            'intention_account_id'          => $rv['intention_account_id'],
//                            'confirm_account_id'            => $rv['confirm_account_id'],
//                            'settlement_state'              => $rv['settlement_state'],
//                            'finish_refund_date'            => $rv['finish_refund_date'],
//                            'settlement_serial_id'          => $rv['settlement_serial_id'],
//                            'settlement_front_serial_id'    => $rv['settlement_front_serial_id'],
//                            'order_id'                      => $rv['order_id'],
//                            'notify_url'                    => $rv['notify_url'],
//                            'cashier_refund_no'             => $rv['cashier_refund_no'],
//                            'remark'                        => $rv['remark'],
//                            'pay_time'                      => $v['pay_time'],//支付时间
//                        ];
//                    }
//                }
            }
        }
//        $order_set_model->insertAll($in_data);
//        if($goods_class==3){
//            //ccs， 虚拟商品，卡券 都在这个时候触发确定结算单
//            $net_order= new NetOrder();
//            $net_order->confirmSett($order_code);
//        }
    }

    public function test_group_order()
    {
        $user = input('user');
        $this->check_user($user);
        $order_code     = input('order_code', "AP220809142408354");
        $pay_order_code = input('pay_order_code', '');
        $net_order      = new NetOrder();
        $res            = $net_order->orderSave($order_code, $pay_order_code);
        var_dump($res);
    }


    public function test_group_order_all()
    {
        $user = input('user');
        $this->check_user($user);
        $order_code     = input('order_codes', "AP220809142408354");
        $order_model =  new BuOrder();
        $order_code_arr = explode(',',$order_code);
        $order_list =  $order_model->getList(['where'=>['order_code'=>['in',$order_code_arr]]]);
        $net_order      = new NetOrder();
        foreach ($order_list as $v){
            $res            = $net_order->orderSave($v['order_code'], $v['pay_order_code'],'h_n_1217',$v['pay_time']);
            echo $v['order_code'].'--'.
            var_dump($res);
        }
        die(333);

    }

    public function test_q_order()
    {
        $ctno     = input('ctno', 'MT20210715127794');
        $pay_info = Payment::create('payment')->queryOrder(['cashier_trade_no' => $ctno]);
        var_dump($pay_info);
    }


    //模拟核销发货
    public function deliver_order()
    {
        $order_code  = input('order_code');
        $user_id     = input('user_id');
        $where       = ['order_code' => $order_code];
        $order_model = new BuOrder();

        $order = $order_model->getOne(array('where' => $where));
        if ($order) {
            if (!in_array($order['order_status'], array(2, 11, 12,22))) {
                print_json(1, '订单不可发货');
            } else {

                $af_model = new DbAfterSaleOrders();
                $af_info =  $af_model->getOne(['where' =>['order_id' =>$order['id']],'order' =>'id desc']);
                if($af_info){
                    if(!in_array($af_info['afs_status'],[2, 3, 6, 8, 11])){
                        print_json(1, '订单售后中不可核销');
                    }
                }
                $data = array(
                    'order_status'      => 19,
                    'verification_user' => $user_id,
                    'last_updated_date' => date('Y-m-d H:i:s'),
                    'delivery_time'     => date('Y-m-d H:i:s')
                );
                $res  = $order_model->saveData($data, $where);

                if ($res) {
                    $net_order = new NetOrder();
                    $net_order->orderChange($order['order_code']);
                    print_json(0, 'ok');
                } else {
                    print_json(1, '发货失败');
                }
            }
        } else {
            print_json(1, '订单不存在');
        }
    }

    public function test_sms()
    {
        $phone     = input('phone', 18819288640);
        $mid       = input('mid', 60268660365699579904);
        $net_order = new NetOrder();
        $res       = $net_order->send_sms($phone, ['plat_id' => $mid]);
        var_dump($res);
    }

    public function check_sms()
    {
        $phone = input('phone', 13570323670);
        $code  = input('cc', 299765);
        $sms   = new SendSms();
        $check = $sms->verify_code($phone, $code);
        var_dump($check);


    }

    public function test_plat_pay()
    {
        $order = new NetOrder();
        $par   = [
            'openid'         => input('openid', 'oqz8L0RSfk-djf3fMzsuQCHrWGkc'),
            'member_id'      => input('member_id', ''),
            'pay_order_code' => $order::buildOrderNo(),
            'attch'          => "998877003344",
            'body'           => "测试商品9988770033",
            'type'           => input('member_id', 'MWEB'),
        ];
        $res   = $order->flat_pay($par);
        var_dump($res);

    }

    //订单结算 1
    //直接修改价格就可以按照价格发起结算单了
    //可以发起多个结算单，同一个订单 可以二次分账
    public function test_sett_order()
    {
        $user = input('user');
        $this->check_user($user);
        $order_code = input('order_code', 'GWSM202107070951087246520');
        $net_order  = new NetOrder();
        $res        = $net_order->settOrder($order_code);
        var_dump($res);
    }


    //手工调用订单修改
    public function test_order_change()
    {
        $order_code = input('order_code');
        $test       = input('test', 1);
        $user       = input('user');
        $this->check_user($user);
        $order = new NetOrder();
        $res   = $order->orderChange($order_code, $test);
        var_dump($res);
    }

    //手工创建平台订单
    public function test_create_plat_order()
    {
        $order_code = input('order_code');
        $user       = input('user');
        $this->check_user($user);
        if (!$order_code) {
            die('缺少参数');
        }
        $order = new NetOrder();
        $res   = $order->creat_plat_order($order_code);
    }

    private function check_user($user)
    {
        if ($user <> 'lzx') {
            die('路径参数异常');
        }
    }

    //模拟订单确认
    public function test_order_confirm()
    {
        $user = input('user');
        $this->check_user($user);
        $order_id   = input('order_id');
        $user_id    = input('user_id');
        $channel    = input('channel');
        $user_model = new DbUser();
        $user       = $user_model->getOneByPk($user_id);
        if (!$user) {
            $user['id']           = $user_id;
            $user['unionid']      = $user_id;
            $user['bind_unionid'] = $user_id;
            $user['member_id']    = $user_id;
            $user['plat_id']      = $user_id;
        }
        $net_order = new NetOrder();
        $res       = $net_order->confirm(['order_id' => $order_id], $user, $channel);
        echo json_encode_cn($res['msg']);

    }



    public function test_sett_point_old()
    {
//        $order_goods_model = new BuOrderCommodity();
//        $sep_rule_model    = new DbSeparateAccountRules();
//        $all_order         = $this->order_model->getList(['where' => ['parent_order_code' => $order_code, 'parent_order_type' => ["<>", 3]]]);
//        $dh_point          = 0;
//        $openid            = '';
//        $dlr_code          = '';
//        foreach ($all_order as $order_a) {
//            $order_goods = $order_goods_model->getList(['where' => ['order_code' => $order_a['order_code']]]);
//            $set_point   = $order_a['integral'];
//            $plat_m      = 0;
//            $dlr_m       = 0;
//            foreach ($order_goods as $vv) {
//                if ($vv['spr_id'] > 0) {
//                    //加入预售点...
//                    //改改改
//                    if ($vv['pre_sale_id'] > 0) {
//                        if ($order_a['order_status'] == 15) {
//                            $set_point = $order_a['pre_point'];
//                        }
//                    }
//                    $sep_rule = $sep_rule_model->getOneByPk($vv['spr_id']);
//                    $sep      = json_decode($sep_rule['rule_info'], true);
//                    $plat_m   += round($set_point * $sep['platform'] / 100, 1);
//                    if ($vv['order_mail_type'] == 2) {
//                        $plat_m += round($set_point * $sep['dlr'] / 100, 1);
//                    } else {
//                        $dlr_m += round($set_point * $sep['dlr'] / 100, 1);
//                    }
//                    Logger::error('set-point-all',['pm'=>$plat_m,'dm'=>$dlr_m,'sp'=>$set_point,'sep'=>$sep]);
//                } else {
//                    return false;//没有设置分账不往下走
//                    continue;
//                }
//            }
//            $order_a['set_point'] = $set_point;
//            $plat_m               = round($plat_m, 0);
//            if ($plat_m > 0) {
//                //GWSM代表总部
//                $this->userPoint($order_a, $plat_m, 'GWSM', $remark, $modifier);
//                $dlr_m = $set_point - $plat_m;
//            }
//            if ($dlr_m > 0) {
//                $this->userPoint($order_a, round($dlr_m, 0), $order_a['dlr_code'], $remark, $modifier);
//            }
//            $openid   = $order_a['openid'];
//            $dlr_code = $order_a['dlr_code'];
//            $dh_point = $set_point;
//        }
    }


    public function hand_set_point_all()
    {
        $order_code = input('order_code');
        $sql        = "SELECT * from t_bu_order where order_code in (select order_code from t_bu_order_commodity where last_updated_date >='2021-08-26 01:37:43' and  actual_point>0  GROUP BY order_code)
and order_code not in (SELECT order_code from t_bu_order_point GROUP BY order_code) and order_status not in (1,8 ,18,3) and is_cc_ok =1";
        if ($order_code) {
            $sql = sprintf("SELECT * from t_bu_order where order_code ='%s' ", $order_code);
        }
        $model = new BuOrder();
        $list  = $model->query($sql);
        if ($list) {
            foreach ($list as $v) {
                $res = $this->test_sett_point($v['order_code']);
                var_dump($res);
            }
        } else {
            echo $sql;
            die();
        }
    }

    //扣除积分
    public function test_sett_point($se_order_code)
    {


        $order_code = input('order_code', $se_order_code);
        if (!$order_code) {
            die('nonono');
        }
        $this->order_model = new BuOrder();
        $order_s           = new NetOrder();
        $where             = ['parent_order_code' => $order_code];//方便后续下面的查询
        $order             = $this->order_model->getOne(['where' => $where]);
        $user_id           = $order['user_id'];
        $user_model        = new DbUser();
        $where             = [
            'a.id' => $user_id,
        ];
        $field             = "b.openid,b.unionid bind_unionid,b.channel_type,a.plat_id member_id,b.is_bind_flat,a.id,b.token,a.name,a.phone,a.address_id,a.car_series_id,b.unionid";
        $user              = $user_model->getOneByToken(['where' => $where, 'field' => $field]);
        $dlr_code          = !empty($order['pay_dlr_code']) ? $order['pay_dlr_code'] : $order['dlr_code'];
        if ($dlr_code == 'PV') {
            $remark = "积分兑礼品";
        } else {
            $remark = "积分购买精品";
        }
//                var_dump($order['order_code']);
//        var_dump($order['integral']);
        if ($order['integral'] || $order['dlr_integral']) {
            $res = $order_s->settPoint($order['order_code'], $remark, $user, '手-913');
        }

    }

//    //少量确认结算单
//    public function con_sett_more(){
//        $user =  input('user');
//        $this->check_user($user);
//        $sql = sprintf("SELECT * from t_bu_order_settlement where settlement_state=1 and  cashier_trade_no in ('MT20210826083122','MT20210826921127','MT20210826949143','MT20210826576721','MT20210826884488','MT20210826675356','MT20210826264414','MT20210826185678','MT20210826970891','MT20210826329016','MT20210826272493','MT20210826568887','MT20210826004142','MT20210827044719','MT20210827569630','MT20210827310198','MT20210827671616','MT20210827509029','MT20210827399065','MT20210827729697','MT20210827403588','MT20210827515906','MT20210827725930','MT20210827174046','MT20210827529137','MT20210827929886','MT20210827028624','MT20210827289173','MT20210827049572','MT20210827368616','MT20210827261773','MT20210827927327','MT20210827591947','MT20210827952246','MT20210827230457','MT20210828026928','MT20210828409890','MT20210828081726','MT20210828137808','MT20210828274278','MT20210828156741','MT20210828494398','MT20210828783603','MT20210828134219','MT20210828577584','MT20210828849564','MT20210828504182','MT20210828765684','MT20210828693763','MT20210828682240','MT20210828748298','MT20210828274759','MT20210828647079','MT20210829705500','MT20210829513100','MT20210829454187','MT20210829801844','MT20210829070896','MT20210829178015','MT20210829778066','MT20210829648312','MT20210829943935','MT20210830227731','MT20210829798590','MT20210830329291','MT20210830052203','MT20210831350722','MT20210830861922','MT20210830230061','MT20210830868823','MT20210830611712','MT20210830332380','MT20210830382719','MT20210830103784','MT20210830564601','MT20210831541776','MT20210831553332','MT20210831042076','MT20210831766371','MT20210831262823','MT20210831892209','MT20210831248613','MT20210831654452','MT20210831944522','MT20210831846109','MT20210831254892','MT20210831647885','MT20210831953019','MT20210831816119','MT20210831780423','MT20210831465884','MT20210831581588','MT20210831374520','MT20210831150846','MT20210831012667','MT20210831170694','MT20210831872394','MT20210831376023','MT20210831376548','MT20210901659678','MT20210901067051','MT20210901822971','MT20210901884482','MT20210901185871','MT20210901188963','MT20210901335913','MT20210901621085','MT20210901784847','MT20210902054510','MT20210902502335','MT20210902116698','MT20210902688746','MT20210902672678','MT20210902290646','MT20210902851338','MT20210902067438','MT20210902101687','MT20210902115955','MT20210902467289','MT20210902603550','MT20210902993669','MT20210902899504','MT20210902263676','MT20210902933684','MT20210902954810','MT20210902062758','MT20210902756563','MT20210902532196','MT20210902444573','MT20210902455501','MT20210903464521','MT20210903218298','MT20210903847960','MT20210903124231','MT20210903051443','MT20210903912244','MT20210903723128','MT20210903872490','MT20210903790007','MT20210903504536','MT20210903207380','MT20210903110294','MT20210903853100','MT20210903240565','MT20210903891659','MT20210903405882','MT20210903833544','MT20210904168181','MT20210904881860','MT20210904381773','MT20210904207449','MT20210904103065','MT20210904020530','MT20210904576185','MT20210904236489','MT20210904865760','MT20210904902879','MT20210904225320','MT20210904119285','MT20210904087687','MT20210904455514','MT20210904150426','MT20210904798915','MT20210904881607','MT20210904804727','MT20210904509084','MT20210904590007','MT20210905820286','MT20210905100941','MT20210905000397','MT20210905205963','MT20210905849795','MT20210905418800','MT20210905007996','MT20210905193363','MT20210905283158','MT20210905426148','MT20210905558819','MT20210905423620','MT20210905981327','MT20210905037005','MT20210905990621','MT20210906299504','MT20210906541066','MT20210906297102','MT20210906616550','MT20210906063239','MT20210906411909','MT20210906383033','MT20210906735671','MT20210906139232','MT20210906545003','MT20210906524007','MT20210906421868','MT20210906069858','MT20210907437578','MT20210907905408','MT20210907038001','MT20210907387798','MT20210907625714','MT20210907566690','MT20210907872675','MT20210907415930','MT20210907231096','MT20210907583454','MT20210907389209','MT20210908862164','MT20210908162816','MT20210908604315','MT20210908503924','MT20210908783365','MT20210908565031','MT20210908390076','MT20210908503713','MT20210908375359','MT20210908256712','MT20210908368391','MT20210908328630','MT20210908667642','MT20210908105094','MT20210909340322','MT20210909417997','MT20210909347437','MT20210909125389','MT20210909818231','MT20210909166203','MT20210909669487','MT20210909207034','MT20210909064417','MT20210909626782','MT20210909648095','MT20210909077621','MT20210909783711','MT20210909385988','MT20210909708175','MT20210909879161','MT20210909002925','MT20210909732176','MT20210909048085','MT20210909835772','MT20210909725817','MT20210909019284','MT20210909861911','MT20210910566987','MT20210910737285','MT20210910690579','MT20210910323909','MT20210910082712','MT20210910217286','MT20210910418280','MT20210826745005','MT20210829093057','MT20210831952789','MT20210901888611','MT20210901424274','MT20210902382420','MT20210902845101','MT20210904362242','MT20210910249350','MT20210910620092','MT20210910859116','MT20210910163566','MT20210910680324','MT20210910187675','MT20210910176032','MT20210910942719','MT20210910045686','MT20210910236959','MT20210910331499','MT20210911377124','MT20210912040179'
//)");
//        $model =  new BuOrderSettlement();
//        $list =  $model->query($sql);
//        $net_order =  new NetOrder();
//        if($list){
//            foreach ($list as $v){
//                $res =  $net_order->confirmSett($v['order_code']);
//                var_dump($res);
//            }
//        }
//
//    }


    public function test_some_order()
    {
        $net_order       = new NetOrder();
        $order_set_model = new BuOrderSettlement();
        $to_date         = date('Y-m-d') . ' 00:00:00';
        $order_codes     = input('order_codes', "'GWSM202109111428527477585','GWSM202109222145498118133'");
        $in_test         = input('test', '');
        $where           = ['is_enable' => 1, 'state' => 2, 'pay_time' => ['<', $to_date]];
        if ($in_test == 1) {
            $where['order_code'] = ['in', $order_codes];
        }
        $order_list = $order_set_model->getList(['where' => $where]);
        foreach ($order_list as $v) {
            $res = $net_order->test_some_order($v['order_code']);
            echo $v['order_code'] . '<br/>';
            Logger::error('test_some_order_controller:', ['order' => $v['order_code'], 'rr' => $res]);
        }
        echo "没数据";

    }

    //订单有最高积分+卡券情况下的分摊重算（去掉最高可用积分限制)
    public function hand_order_point()
    {
        $order_code        = input('order_code');
        $where             = ['order_code' => $order_code];
        $store_order_goods = [];
        $mail_order_goods  = [];
        $all_use_point     = 0;
        //实际使用积分数，比较max_point跟实际积分
        $net_order_goods   = [];
        $order_goods_model = new BuOrderCommodity();
        $order_goods       = $order_goods_model->getList(['where' => $where]);
        $order_model       = new BuOrder();
        $order             = $order_model->getOne(['where' => $where]);
        foreach ($order_goods as $v) {
            //计算积分分担
//            $v['point_price'] = sprintf("%.2f",($v['actual_price'] * $v['count'] - $v['card_all_dis']) * 10); //积分十倍
//            $v['point_price'] = floor((((float)$v['price']* (float)$v['count']-(float)$v['all_dis'])  - (float)$v['card_all_dis']) * 10); //积分十倍 计算总数取整，实际计算四舍五入，跟下面不一样20220126  直接不要去掉小数保证跟下面一致 20220104 GWSM202201021819408095283
////
            $all_price_one               = bcmul($v['price'], $v['count'], 2);
            $all_price_one_yh            = bcsub($all_price_one, $v['all_dis'], 2);
            $all_price_one_yh_cardyh     = bcsub($all_price_one_yh, $v['card_all_dis'], 2);
            $all_price_one_yh_cardyh_ten = bcmul($all_price_one_yh_cardyh, 10, 2);

//            echo $all_price_one_yh_cardyh_ten;
            $v['point_price'] = floor($all_price_one_yh_cardyh_ten); //积分十倍 直接不要去掉小数保证跟下面一致 20220104 GWSM202201021819408095283
//            var_dump($v['point_price']);
//            var_dump($v['max_point'] * $v['count']);
            if ($v['point_price'] < $v['max_point']) {
                $use_point = $v['point_price'];
            } else {
                $use_point = $v['max_point'];
            }
            $use_point = $v['point_price'];

//            var_dump($v['point_price']);
//            var_dump($v['max_point'] * $v['count']);
            $v['h_use_point'] = $use_point;
            echo $use_point;
            echo "<br/>";
            $v['h_price_count'] = sprintf("%.2f", (($v['price'] * $v['count'] - $v['all_dis']) - $v['card_all_dis']));
            $all_use_point      += $use_point;
            $net_order_goods[]  = $v;
        }
//        $store_goods = $this->share_point($store_order_goods, $all_use_point, $order['integral'], 1);
        $toEnd             = count($net_order_goods);
        $i                 = 1;//
        $actual_all_point  = 0;
        $all_point         = $order['integral'];
        $res_point         = round($all_use_point, 2);
        $total_money       = 0;
        $money             = 0;
        $integral          = 0;
        $mail_price        = 0;
        $b_act_goods_price = 0;
        $actual_point      = 0;
        foreach ($net_order_goods as $k => $v) {

            if ($all_point > 0) {
                //最后一条就是总使用积分-已使用
                //前面的就是 最高使用积分/总最高使用*实际使用积分的比例
                if (0 === --$toEnd) {
                    $actual_point = $all_point - $actual_all_point;
                } else {
                    $actual_point = round($v['h_use_point'] * $all_point / $res_point, 0);
//                    $actual_point     = round($v['h_use_point'] * $all_point / $res_point, 1);//1的时候会多出1积分 GWSM202109012209327766315
                    $actual_all_point += $actual_point;
                }
                $i++;
            }
            echo $v['h_price_count'] . '---' . ($actual_point / 10) . '====' . (bcsub($v['h_price_count'], $actual_point / 10)) . '+++++' . $all_point . '>>>>' . $res_point . '.....' . $v['h_use_point'] . '}}}' . $res_point;
            echo "<br/>";

            $o_g_data = [
                'actual_point'     => round($actual_point, 0),
                'actual_use_money' => bcsub($v['h_price_count'], ($actual_point / 10), 2),
            ];
//            $new_order_code = $o_g_data['order_code'];
//            $order_code = $v['order_code'];
//            $mail_type = $v['order_mail_type'];
            $b_act_goods_price += $v['price'] * $v['count'];//活动前商品总价
            $mail_price        += $v['mail_price'];//运费
            $integral          += $actual_point;//使用厂家积分
            $money             += $o_g_data['actual_use_money'];//实际支付金钱
            $total_money       += $v['h_price_count'];//实际总额=钱+积分
//            echo  $v['h_price_count'];
            var_dump($o_g_data);
//            die();
            $order_goods_model->saveData($o_g_data, ['id' => $v['id']]);

        }
    }


    /**
     * 测试支付
     */
    public function unifyPayService()
    {
        // 统一支付
        $lyPay = new LyPay();

        $orderType = input('orderType', 'A2');
        $terminal  = input('terminal', '02');
        $sceneType = input('sceneType', 'sit');
        $smType = input('smType', 1);
        $bankCode = input('bank_code', '01');
        $dlrId     = '';
        $openid    = input('openid', '');
        // 招行 微信
        if (in_array($orderType, ['A2', 'A7'])) {
            $dlrId  = 1292;
            if (empty($openid)) {
                $openid = 'o0VJR5E8KZfVpKnXO5cmrgExfb7I';
            }
        }

        // 招行 支付宝
        if (in_array($orderType, ['A3', 'A4', 'A8'])) {
            $dlrId = 212;
        }

        // 工行 微信
        if ($orderType == 'A15') {
            $dlrId  = 1458;
            if (empty($openid)) {
                $openid = 'oLiKz5Rn8D0J6Chig6vmIbLygrkI';
            }
        }
//        if (in_array($orderType, ['A13', 'A19'])) {
//            $dlrId = 301;
//        }

        // 工行 支付宝
        if (in_array($orderType, ['A16', 'A17', 'A18'])) {
            $dlrId = 301;
        }

        if (!empty(input('dlrId'))) {
            $dlrId = input('dlrId');
        }

        if (empty($dlrId)) {
            $dlrId = 1458;
        }


        $param = [
            'orderType'         => $orderType,
            'terminal'          => $terminal,
            'subOrderList'      => [
                [
                    'subMchReserved' => "fd444fa",
                    'dlrId'          => $dlrId,
                    'subPayAmount'   => 1,
                    'subOrderCode'   => "S" . date('YmdHis') . mt_rand(100, 999),
                    'subGoodsName'   => "机油@",
                    'subServiceFee'  => 1,
                ],
//                [
//                    'subMchReserved' => "21312321312",
//                    'dlrId' => $dlrId,
//                    'subPayAmount' => 1,
//                    'subOrderCode' => "S".date('YmdHis').mt_rand(1000,9999),
//                    'subGoodsName' => "测试商品2",
//                    'subServiceFee'  => 1,
//                ]
            ],
            'version'           => "1.0",
            'customerName'      => "刘洋",
            'bussOrderCode'     => "B" . date('YmdHis') . mt_rand(100, 999),
            'linkOrderCode'     => "L" . date('YmdHis') . mt_rand(100, 999),
            'buyer'             => "",
            'mchReserved'       => "mchReserved",
            'payAmount'         => 1,
            'openId'            => $openid,
            'isClosePostToIcbc' => "",
            'sceneType'         => $sceneType,
            'goodsName'         => "机油！",
            'serviceFee'        => 1,
            'bank_code'         => $bankCode
        ];

        $subPayAmount       = array_column($param['subOrderList'], 'subPayAmount');
        $payAmount          = array_sum($subPayAmount);
        $subServiceFee       = array_column($param['subOrderList'], 'subServiceFee');
        $serviceFee          = array_sum($subServiceFee);
        $param['payAmount'] = $payAmount;
        $param['serviceFee'] = $serviceFee;
        $param['smType'] = $smType;

//        版本号	version;开发者ID	appId;签名	sign;支付类型	orderType;场景类型	sceneType;终端	terminal;回调通知地址	noticeUrl;商品名称	goodsName;终端IP	spbillCreateIp;业务单订编号	bussOrderCode;业务系统支付水流号	linkOrderCode;金额总数（分）	payAmount;客户姓名	customerName;小程序，公众号ID	subAppId;openId	openId;支付宝买家用户id	buyerId;保留域	mchReserved;子单集合	subOrderList;专营店ID	dlrId;子单订单号	subOrderCode;子单商品名称	subGoodsName;子单金额（分）	subPayAmount;子单保留域	subMchReserved;子单服务费	subServiceFee;服务费	serviceFee
        $re = $lyPay->unifyPayService($param);
        if ($re->isSuccess()) {
            print_json(0, $re->getMessage(), $re->getData());
        } else {
            print_json(-1, $re->getMessage());
        }
    }


    /**
     * 测试支付详情接口
     */
    public function lyPayInfo()
    {
        $msOrderCode   = input('msOrderCode');
        $linkOrderCode = input('linkOrderCode');
        if (empty($msOrderCode)) {
            print_json(-1, 'msOrderCode不能为空');
        }
        if (empty($linkOrderCode)) {
            print_json(-1, 'linkOrderCode不能为空');
        }
        $param = [
            'msOrderCode'   => $msOrderCode,
            'linkOrderCode' => $linkOrderCode,
        ];
        // 支付详情查询
        $lyPay  = new LyPay();
        $result = $lyPay->unifyPayResultInfo($param);
        if ($result->isSuccess()) {
            print_json(0, 'ok', $result->getData());
        } else {
            print_json(-1, $result->getMessage());
        }
    }


    /**
     * 支付结果通知
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function unifyPayCallback()
    {
        $input = input();
        Logger::info('pay-ment-unifyPay-callback', $input);

        $lyPay        = new LyPay();
        $re           = $lyPay->unifyPayCallback($input);
        $appId        = config("port.ly_payment")['appId'];
        $data         = ['returnCode' => $re, 'version' => '1.0', 'appId' => $appId];
        $sign         = $lyPay->getSign($data);
        $data['sign'] = $sign;
        echo json_encode($data);
        exit();
    }


    /**
     * 退款结果通知
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function refundCallback()
    {
        $input = input();
        Logger::info('pay-ment-refund-callback', $input);
        $lyPay        = new LyPay();
        $re           = $lyPay->refundCallback($input);
        $appId        = config("port.ly_payment")['appId'];
        $data         = ['returnCode' => $re, 'version' => '1.0', 'appId' => $appId];
        $sign         = $lyPay->getSign($data);
        $data['sign'] = $sign;
        echo json_encode($data);
        exit();
    }


    /**
     * 退款接口
     */
    public function refundOrder()
    {
        $msOrderCode   = input('msOrderCode');
        $linkOrderCode = input('linkOrderCode');
        $subOrderCode  = input('subOrderCode');
        $txnAmt        = input('txnAmt', 1);
        $refundAmt        = input('refundAmt', 0);
        if (empty($msOrderCode)) {
            print_json(-1, 'msOrderCode不能为空');
        }
        if (empty($linkOrderCode)) {
            print_json(-1, 'linkOrderCode不能为空');
        }
        if (empty($subOrderCode)) {
            print_json(-1, 'subOrderCode不能为空');
        }
        if (empty($refundAmt)) {
            $refundAmt = $txnAmt;
        }
        $param  = [
            'linkRefundCode' => 'T' . date('YmdHis') . mt_rand(100, 999),
            'linkOrderCode'  => $linkOrderCode,
            'subOrderCode'   => $subOrderCode,
            'txnAmt'         => (int)$txnAmt,
            'refundAmt'      => (int)$refundAmt,
            'refundDesc'     => '测试退款',
            'msOrderCode'    => $msOrderCode
        ];
        $lyPay  = new LyPay();
        $result = $lyPay->refundOrder($param);
        if ($result->isSuccess()) {
            print_json(0, 'ok', $result->getData());
        } else {
            print_json(-1, $result->getMessage());
        }
    }


    /**
     * 退款详情接口
     */
    public function LyPayReturnInfo()
    {
        $linkRefundCode    = input('linkRefundCode');
        $msRefundOrderCode = input('msRefundOrderCode');
        if (empty($linkRefundCode)) {
            print_json(-1, 'linkRefundCode不能为空');
        }
        if (empty($msRefundOrderCode)) {
            print_json(-1, 'msRefundOrderCode不能为空');
        }
        $param  = [
            'linkRefundCode'    => $linkRefundCode,
            'msRefundOrderCode' => $msRefundOrderCode,
        ];
        $lyPay  = new LyPay();
        $result = $lyPay->unifyRefundInfo($param);
        if ($result->isSuccess()) {
            print_json(0, 'ok', $result->getData());
        } else {
            print_json(-1, $result->getMessage());
        }
    }


    public function test_re_order()
    {
        $order_code = input('order_code');
        $net_order  = new NetOrder();

        $res = $net_order->refund($order_code);
        var_dump($res);

    }

    public function test_pay_order()
    {
        $js        = '{"order_id":"1297180","name":"\u4e86","phone":"13570597528","address":"\u5e7f\u4e1c\u7701\u5e7f\u5dde\u5e02\u6d77\u73e0\u533a\u65b0\u6e2f\u4e2d\u8def397\u53f7","cards":"1123","money":"1","logistics_mode":1,"point":"756","dlr_point":"","choose_dlr_code":"H2992","card_money":"2"}';
        $data      = json_decode($js, true);
        $net_order = new NetOrder();
        $res       = $net_order->go_pay($data, $this->user, $this->channel_type);
        var_dump($res);
    }

    public function test_card_order()
    {
        $order_code = input('order_code');
        $net_order  = new NetOrder();
        $res        = $net_order->order_card($order_code, $this->user, $this->channel_type);
        var_dump($res);

    }


    public function test_ref_order()
    {
        $order_id  = input('order_id', 1297678);
        $net_order = new NetOrder();
        $res       = $net_order->rf_font($order_id, 1);
        var_dump($res);


    }

    public function test_redis_name()
    {
        var_dump(redis(input('redis')));
    }


    public function test_order_pay()
    {
        $order = new NetOrder();
        $par   = [
            'openid'         => input('openid', 'oojWg4mc_03Ab7k5JEkwM_YDtc-o'),
            'pay_order_code' => $order::buildOrderNo(),
            'attch'          => "9988770033",
            'body'           => "测试商品9988770033",
        ];
        $res   = $order->t_pay($par);
        var_dump($res);

    }


    public function test_order()
    {
        $order      = new NetOrder();
        $order_code = input('order_code', 'GWSM202105141019280919848');
        $res        = $order->orderChange($order_code);
    }

    public function test_refund()
    {
        $order_id = input('order_id');
        $time     = time();
        if ($time > 1627300000) {
            dd('error');
        }
        $orderModel = new BuOrder();
        $order      = $orderModel->getOneByPk($order_id);

        $settle_model = new BuOrderSettlement();
        $settle_where = ['order_code' => $order['order_code']];
        $settle       = $settle_model->getOne([
            'where' => $settle_where
        ]);

        $refund_money  = $order['money'] * 100;
        $order_money   = $order['money'] * 100;
        $refund_ration = ($refund_money / $order_money);

        $refund_req = [
            'cashier_trade_no'  => $order['cashier_trade_no'],
            'refund_type'       => 0,#固定部分退
            'refund_fee'        => $refund_money,
            'refund_notify_url' => config("DOMAIN_INNER_URL") . "net_small/pay_plat/refund"
        ];

        if (is_array($settle) && !empty($settle)) {
            foreach ($settle as $st_item) {
                $refund_req['refund_array'][] = [
                    'cashier_settlement_no' => $st_item['cashier_settlement_no'],
                    'refund_fee'            => $st_item['settlement_fee'] * $refund_ration,
                ];
            }
        }

        $prs = Payment::create('payment')->refundOrder($refund_req);

        dd($prs);
    }


    public function test_refund_point()
    {
        $order_id = input('order_id');
        $time     = time();
        if ($time > 1629506182) {
            dd('error');
        }

        $orderModel = new BuOrder();
        $order      = $orderModel->getOneByPk($order_id);


        $data = array(
            'vin'               => $order['vin'],
            'card_no'           => $order['ic_card_no'],
            'balance_bill_code' => $order['order_code'],
            'bus_code'          => 'JPSC',
            'bus_scene_code'    => 'WXYF',
            'dlr_code'          => 'T9932',
            'anti_balance_man'  => $order['dlr_code'],
            'source_bill_time'  => date("Y-m-d H:i:s"),
        );

        $point_res = Crm::create('crm')->antiBalance($data);

        dd($point_res);
    }


    public function test_up_order()
    {
        $order_id = input('order_id', "SF0c502020210630134610255740");
        $order    = new NetOrder();
        $res      = $order->test_up_order($order_id);
        var_dump($res);
    }

    public function test_qrc()
    {
        $order = new NetOrder();

        $qrc = $order->test_qrc();
        var_dump($qrc);
    }


    public function seBuServerInfo()
    {

        $type = input('type',1);
        if ($type == 1) {
            $orderIds = input('order_ids');
            $orderArr = explode(',', $orderIds);
            foreach ($orderArr as $orderId) {
                Queue::push('app\common\queue\PushOrder', json_encode(['order_id' => $orderId]), config('queue_type.push_order'));
            }
        } else {
            $service = new E3sPostData();
            $orderCodes = input('order_codes');
            $orderCodeArr = explode(',', $orderCodes);
            if ($type == 2) {
                // 全推
                $service->pushOrder($orderCodeArr);
            }
            if ($type == 3) {
                // 只推主表
                foreach ($orderCodeArr as $orderCode) {
                    $service->pushIndexOrder($orderCode);
                }
            }
            if ($type == 4) {
                // 只推详情表
                foreach ($orderCodeArr as $orderCode) {
                    $service->pushDetailOrder($orderCode);
                }
            }
        }
        print_json(0, 'success');
    }


    public function updateOrderStatus()
    {

        $data        = [
            'ORDER_CODE'       => 'DD' . date('YmdHis') . mt_rand(100, 999),
            'SETTLEMENT_STATE' => 1
        ];
        $maintainService = new MaintainService();

        $result = $maintainService->updStatus($data);

        if ($result->isSuccess()) {
            print_json(0, $result->getMessage(), $result->getData());
        } else {
            print_json(-1, $result->getMessage());
        }
    }


    /**
     * 同步商户列表dlrId
     */
    public function queryMerchantInfo()
    {
        $brandCode = input('brandCode', 1);
        $storeType = input('storeType', 1);
        $lyPay  = new LyPay();
        $result = $lyPay->lyDlrId($brandCode, $storeType);
        if ($result->isSuccess()) {
            print_json(0, $result->getMessage(), $result->getData());
        } else {
            print_json(-1, $result->getMessage());
        }
    }


    /**
     * 支付方式
     */
    public function getPaymentSourceList()
    {
        $lyPay  = new LyPay();
        $result = $lyPay->getPaymentSourceList();
        if ($result->isSuccess()) {
            print_json(0, $result->getMessage(), $result->getData());
        } else {
            print_json(-1, $result->getMessage());
        }
    }




    //测试结算。。
    public function lySettle(){
        $order_code= input('order_code');
        $order_model =  new BuOrder();
        $order =  $order_model->getOne(['where'=>['order_code'=>$order_code]]);
        $ms_order_code = $order['ms_order_code'];
        if($order['parent_order_type']==2){
            $order_p =  $order_model->getOne(['where'=>['parent_order_code'=>$order['parent_order_code'],'parent_order_type'=>3]]);
            $ms_order_code =  $order_p['ms_order_code'];
        }
        $data       = ['subOrderCode' => $order_code,'msOrderCode'=>$ms_order_code];
        $lyPay      = new LyPay();
        $result     = $lyPay->settle($data);
//        if ($result->isSuccess()) {
//            $saveData = ['settlement_state'=>1,'last_updated_date'=>date('Y-m-d H:i:s'), 'settlement_time'=>date('Y-m-d H:i:s')];
//            $order_model->saveData($saveData,['order_code'=>$order_code]);
//            print_json(0, $result->getMessage(), $result->getData());
//        } else {
//            print_json(-1, $result->getMessage());
//        }
        if ($result->isSuccess()) {
            // 添加结算记录
            $result = ['msg' => $result->getMessage(), 'data' => $result->getData()];
            $upd = ['settlement_state' => 3, 'settlement_time' => date('Y-m-d H:i:s')];
            Queue::push('app\common\queue\PushOrder', json_encode(['order_id' => $order['id']]), config('queue_type.push_order'));
        } else {
            $add['settle_status'] = 'S3';
            $upd = ['settlement_state' => 2, 'settlement_time' => date('Y-m-d H:i:s')];
        }
        DbLyPaySettle::insertGetId($add);
        $order_model->saveData($upd,['order_code'=>$order_code]);
        print_json($result);

    }


    /**
     * 查询交易账单信息
     */
    public function tradeBillQuery()
    {
        $tradeDate = input('tradeDate');
        if (empty($tradeDate)) {
            print_json(-1, 'tradeDate 不能为空');
        }
        $lyPay = new LyPay();
        $result = $lyPay->tradeBillQuery($tradeDate);
        if ($result->isSuccess()) {
            print_json(0, $result->getMessage(), $result->getData());
        } else {
            print_json(-1, $result->getMessage());
        }
    }


    /**
     * 查询交易账单信息
     */
    public function drawBillQuery()
    {
        $tradeDate = input('tradeDate');
        if (empty($tradeDate)) {
            print_json(-1, 'tradeDate 不能为空');
        }
        $lyPay = new LyPay();
        $result = $lyPay->withDrawBillQuery($tradeDate);
        if ($result->isSuccess()) {
            print_json(0, $result->getMessage(), $result->getData());
        } else {
            print_json(-1, $result->getMessage());
        }
    }






    /**
     * 生成小程序短链接
     */
    public function urlLink()
    {

        $input = input('post.');
        $rules = [
            'path'        => ['require'],
            'query'       => ['require'],
            'is_expire'   => ['require', 'boolean'],
            'expire_type' => ['require', 'integer'],
            'env_version' => ['require'],
        ];
        $validate = new Validate($rules);
        if (!$validate->check($input)) {
            print_json(-1, $validate->getError());
        }
        if ($input['expire_type'] == 1) {
            if (empty($input['expire_interval'])) {
                print_json(-1, 'expire_interval 不能为空');
            }
        } else {
            if (empty($input['expire_time'])) {
                print_json(-1,  'expire_time 不能为空');
            }
        }
        $result = CarLive::create('car_live')->urlLink($input);
        Logger::debug('urlLink', ['params'=>$input, 'result'=>$result]);
        if (isset($result['code']) && ($result['code'] == 10000)) {
            print_json(0, 'success', $result['data']);
        } else {
            print_json(-1, 'error', $result['data']);
        }
    }

    public function test_pay_link(){
        $order_code= input('order_code');
        $smType = input('sm_type',1);
        $pay =  new PayCenter();
        $link =  $pay->urlLink($order_code,$smType);
        print_json(0, 'success', $link);
    }



    /**
     * 测试支付切换小程序appid
     * type 1:设置  2:删除  3:获取
     */
    public function setAppId()
    {
        $arr = input('post.');
        $type = $arr['type'];
        unset($arr['type']);
        $list = [];
        if ($type == 1) {
            $re = redis('ly_pay_sub_app_id', $arr, 24 * 3600);
        } elseif ($type == 2) {
            $re = redis('ly_pay_sub_app_id', null);
        } else {
            $list = redis('ly_pay_sub_app_id');
            $re = 1;
        }
        if ($re) {
            print_json(0,'success', $list);
        } else {
            print_json(-1, 'error');
        }

    }

    /**
     * 手动触发批量结算方法处理
     */
    public function orderSettleDoIt()
    {
        try {
            $order_code = input('post.order_code', '');
            $instruct = input('post.instruct', '');
            $str = 'zx'.date('md');
            if($str != $instruct){
                print_json(-1, '指令不正确');
            }
            OrderSettle::doIt2($order_code);
            print_json(0, '成功，手动触发批量结算方法处理');
        }catch (\Exception $exception){
            print_json(-1, $exception->getMessage());
        }
    }

    public function getSettleInfo()
    {
        $input = input('post.');
        $map = ['ms_order_code' => $input['msOrderCode'], 'is_enable' => 1];
        $settleInfo = DbLyPaySettle::where($map)->find();
        if (empty($settleInfo)) {
            print_json(-1, '结算单不存在');
        }
        $lyPay = new LyPay();
        $result = $lyPay->withdrawInfoQuery($input);
        if ($result->isSuccess()) {
            // 修改结算
            $data = $result->getData();
            if (!empty($data)) {
                $orderService = new OrderService();
                $result1 = $orderService->settCallback($data, $settleInfo);
                if (!$result1->isSuccess()) {
                    print_json(-1, $result1->getMessage());
                }
            } else {
                DbLyPaySettle::where(['id'=>$settleInfo['id']])->update(['is_enable'=>0]);
            }
            print_json(0, $result->getMessage(), $result->getData());
        } else {
            print_json(-1, $result->getMessage());
        }
    }


    /**
     * 获取支付列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getPayList()
    {
        $input = input('post.');
        $rules = [
            'pay_type'=>'require|in:1,2',
            'start_time'=>'require',
            'end_time'=>'require',
        ];
        $validate = new Validate($rules);
        if (!$validate->check($input)) {
            print_json(-1, $validate->getError());
        }
        if (strtotime($input['end_time']) < strtotime($input['start_time'])) {
            print_json(-1, '结束时间不能小于开始时间');
        }

        $model = new DbLyPayLog();
        $map = [
            'params' => ['neq', ''],
            'pay_type' => $input['pay_type'],
            'created_date' => ['between', [$input['start_time'].' 00:00:00', $input['end_time'].' 00:00:00']]
        ];
        if ($input['pay_type'] == 1) {
            $map['callback'] = ['like', '%"tradeState":"P2"%'];
        }
        $list = $model->where($map)->field('id,params,ms_order_code,ms_refund_code,created_date')->select();
        $data = [];
        foreach ($list as $key => $value){
            $param = json_decode($value['params'], true);
            $data[$key] = [
                'id' => $value['id'],
                'ms_order_code' => $value['ms_order_code'],
                'ms_refund_code' => $value['ms_refund_code'],
                'created_date' => $value['created_date'],
            ];
            if ($input['pay_type'] == 1) {
                $data[$key]['order_code'] = $param['mchReserved'];
                $data[$key]['txn_amt'] = $param['payAmount'];
            } else {
                $data[$key]['order_code'] = $param['subOrderCode'] ?? '';
                $data[$key]['txn_amt'] = $param['refundAmt'] ?? '';
            }
        }
        print_json(0, 'success', $data);
    }


    // 测试结算任务
    public function orderSettle()
    {
        $input = input('get.');
//        $rules = [
//            'order_code'=>'require',
//        ];
//        $validate = new Validate($rules);
//        if (!$validate->check($input)) {
//            print_json(-1, $validate->getError());
//        }
        $orderCode = '';
        if (!empty($input['order_code'])) {
            $orderCode = $input['order_code'];
        }
        $re = OrderSettle::doIt2($orderCode);
        dd($re);
    }

    public function orderSettleCb(){
        $orderService = new OrderService();
        $input = input('get.');
        $orderCode = '';
        if (!empty($input['order_code'])) {
            $orderCode = $input['order_code'];
        }
        $orderobj = new BuOrder();
        $orderInfo = $orderobj->where('order_code', $orderCode)->find();
        if (empty($orderInfo)) {
            print_json(1,'订单不存在');
        }
        BuOrder::where('order_code', $orderCode)->update(['settlement_state'=>1]);
        $orderService->autoInvoice($orderInfo);
    }


    public function sms1()
    {
        $sms = new SendSms();
        $input = input('get.');
        $data = [
            'phone' => $input['phone'],
            'order_code'=> $input['order_code'],
            'dlr_name'=> $input['dlr_name'],
            'plat_id' => $input['plat_id']
        ];
        $re = $sms->send_sms(10, $data);
        dd($re);
    }



    // 订单到期提醒
    public function orderRemind()
    {
        $day = input('day', 7);
        $order_code = input('order_code', '');
        OrderRemind::orderList($day,$order_code);
    }


    // 测试app回调
    public function app_callback()
    {
        $orderCode = input('order_code', '');
        $payStatus = input('pay_status', '');
        $payData = [
            "order_code" => $orderCode,
            "payStatus" => $payStatus,//1-成功，2-失败
        ];
        $result = QuickWin::create('ly_app_pay')->appPayCallBack($payData);
        print_json(0, 'success', $result);

    }


    /**
     * 获取优惠次数
     */
    public function getDiscount()
    {
        $vin = input('vin');
        $data = Maintain::create('maintain')->isDiscount($vin);
        print_json(0, 'success', $data);
    }




    /**
     * 测试启辰积分上报
     */
    public function point_report()
    {
        $one_id = input('one_id');
        $unionid = input('unionid');
        $channel_type = input('channel_type', 'QCSM');
        $data = ['oneId'=>$one_id, 'unionid'=>$unionid, 'channel_type'=>$channel_type];
        Queue::push('app\common\queue\PointReport', json_encode($data), config('queue_type.order'));
    }

    /**
     * 查询积分上报
     */
    public function get_point_report()
    {
        $one_id = input('one_id');
        $request_id = input('request_id');
        $channel_code = input('channelCode', 'V_SR_002');
        $data = [
            'oneId'=>$one_id,
            'requestId' => $request_id,
            "channelCode"=>$channel_code,
        ];
        $re = InterFun::create('inter_fun')->getPointReport($data);
        if ($re['status'] == 200) {
            print_json(0, 'success', $re['data']);
        } else {
            print_json(1,$re['msg'], $re['data'] ?? []);
        }
    }


    public function test_get_carer()
    {
        $mid = input('mid');
        $unionid = input('unionid');
        $oneid = input('oneid');
        $channel_type = input('channel_type');
        $service = new Common();

        $re = $service->_getCarer($unionid, $mid, $oneid, $channel_type);
        print_json(0, 'success', ['re'=>$re]);

    }


    public function addChance()
    {
        $mid = input('mid');
        $gameId = input('game_id');
        $result = HaoWan::create('hao_wan')->addChance($mid, $gameId);
        $data = $result->getData();
        print_json(0, 'success', ['re'=>$data]);
    }


    public function getNum()
    {
        $mid = input('mid');
        $gameId = input('game_id');
        $result = HaoWan::create('hao_wan')->getLotteryNum($mid, $gameId);
        $data = $result->getData();
        print_json(0, 'success', ['re'=>$data]);
    }


    public function queryIsFirstOrder()
    {
        $vin = input('vin');
        $data = Maintain::create('maintain')->queryIsFirstOrder($vin);
        print_json(0, 'success', $data);
    }
    /**
     * 交易账单查询
     */
    public function tradeBillNewQuery()
    {
        $tradeDate = input('tradeDate');
        $payType = input('payType', '01');
        $storeCode = input('storeCode', 'DNDC1');

        $data = [
            'tradeDate' => $tradeDate,
            'payType' => $payType,
            'storeCode' => $storeCode,
            'storecode' => $storeCode,
        ];
        $lyPay = new LyPay();
        $result = $lyPay->tradeBillNewQuery($data);
        if ($result->isSuccess()) {
            print_json(0, 'success', $result->getData());
        } else {
            print_json(1, $result->getMessage());
        }
    }



    /**
     * 结算账单查询
     */
    public function withdrawNewQuery()
    {
        $settleDate = input('settleDate');
        $storeCode = input('storeCode', 'DNDC1');

        $data = [
            'settleDate' => $settleDate,
            'storeCode' => $storeCode,
            'storecode' => $storeCode,
        ];
        $lyPay = new LyPay();
        $result = $lyPay->withdrawNewQuery($data);
        if ($result->isSuccess()) {
            print_json(0, 'success', $result->getData());
        } else {
            print_json(1, $result->getMessage());
        }
    }


    public function test_base()
    {
        $order_code =  input('order_code');
        $res = Base::isNotifyActivity(explode(',',$order_code));
        print_json($res);
    }
}
