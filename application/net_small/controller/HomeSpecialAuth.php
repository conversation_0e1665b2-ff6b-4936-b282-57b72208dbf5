<?php

namespace app\net_small\controller;

use app\common\model\act\AcGongHuiInfo;
use app\common\model\act\AcHaveTradeList;
use app\common\model\act\AcPhonelist;
use app\common\model\bu\BuOrder;
use app\common\model\db\DbBdpRecommend;
use app\common\model\db\DbDlr;
use app\common\model\db\DbLog;
use app\common\model\db\DbSmExpand;
use app\common\model\db\DbSpecialSm;
use app\common\model\db\DbUser;
use app\common\net_service\GoodsCustomize;
use app\common\net_service\NetOrder;
use app\common\net_service\SendSms;
use app\common\port\connectors\Bdp;
use app\common\service\QrCodeService;
use app\common\validate\Order as OrderValidate;
use app\common\validate\Special as SpecialValidate;
use ForkModules\Traits\ResponseTrait;
use app\common\model\db\DbHomeSm;
use app\common\validate\Home as HomeValidate;
use think\Db;
use think\exception\HttpResponseException;
use tool\Logger;

/**
 * 首页
 * Class HomeSpecialAuth
 * @package app\net_small\controller
 * @title 首页&专题页 登录态
 * @description 接口说明
 */
class HomeSpecialAuth extends Common
{
    use ResponseTrait;

    public function __construct()
    {
        parent::__construct();
        if (!$this->user_id) {
            $response = $this->setResponseError('请登录!', 401)->send();
            throw new HttpResponseException($response);
        }
        $this->gong_hui_id = config('gong_hui.goods_id');
        $this->gong_hui_sp_id = config('gong_hui.sp_id');
    }

    /**
     * @title OneApp首页
     * @description 接口说明
     * @param name:page_type type:int require:0 default:1 other: desc:主页页面类型，1-车生活(默认),2-OneApp,3Pz1a-小程序&APP,5启辰小程序,6启辰App
     * @return 200: 成功
     *
     * <AUTHOR>
     * @url /net-small/home-auth
     * @method GET
     */
    public function home(HomeValidate $validate)
    {
        $api_start_at = microtime(true);

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $pageType  = $requestData['page_type'] ?? DbHomeSm::DEFAULT_PAGE;
        $lng       = $requestData['lng'] ?? ''; //经度
        $lat       = $requestData['lat'] ?? ''; //纬度
        $kilometer = $requestData['kilometer'] ?? ''; //公里数
        $key       = config('cache_prefix.home') . $pageType;
        $home_info = redis($key);

        $home_info = false;
        if (empty($home_info)) {
            //1.获取数据,2.写入redis
            $home_info = DbHomeSm::field('data_json')->find($pageType);
            redis($key, $home_info, mt_rand(3600, 7200) * 10);
        }

        $net_service_common = new \app\common\net_service\Common();
        $user_vin           = $net_service_common->user_car_type_redis($this->user);
        $user_status        = $user_vin['user_status'] ?? 0;
        Logger::error('homeauth0', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-".$this->user['id'].'-'.$this->channel_type);
        //原先是 !in_array($this->brand 20230714
        if (in_array($this->brand, [1, 2])) {
            if ($user_status == 2) {
                //首页先关掉这个，等大数据做好了==0不更新公里数之后再打开
//            if (getRedisLock('bdp_recommend_' . $this->user_vin, 30)) {
//                Bdp::create('bdp')->recommend(['vin' => $this->user_vin]);
//            }
                $data_bdp = getCacheBdpVin($this->user_vin);
                $ads_like =getAbsDataNew($this->user_vin,$this->memberid,0,1);

            }
            if ($this->brand == 1) {
                $data_ads = getCacheAbsMall($this->user_vin, $this->memberid);
            }
        }
        Logger::error('homeauth1', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" . $this->user['id'] . '-' . $this->channel_type);

        $data_tj  = ['data_bdp' => $data_bdp ?? [], 'data_ads' => $data_ads ?? [],'data_ads_like'=>$ads_like??[]];

        # 首页第一个商品模块中的备件商品要根据用户当前的vin来做处理，减少前端缓存时间，30分钟
        $home_key  = config('cache_prefix.more_people_more_home') . $this->user_vin . $pageType.$this->user_id.$lng.$lat.$kilometer;

        $this->user['sm_type'] = 1;
        $this->user['set_id'] = $pageType;
        $home_new = redis($home_key);
        $home_new = false;
        //前端卡券还调用 /net-small/user/card-center  这个接口循环判断是否已领取
        //也就是首页卡券只能能领一张的意思
        if(empty($home_new)){
            $data_json = (new GoodsCustomize())->analyze($home_info->data_json, $this->shelves_type, $data_tj, $this->user, $this->channel_type,'home',$lng,$lat,$kilometer);
            $home_info->data_json = $data_json;
            //}
            $home_info->data_json = (new GoodsCustomize())->activity($home_info->data_json, $this->user,$this->channel_type,$lng,$lat,$kilometer,'home');
            redis($home_key,$home_info,30);
            $redis = \think\Cache::redisHandler();
            $redis->sadd(config('cache_prefix.more_people_more_hm_set') . $pageType, $home_key);
            $home_new  = $home_info;
        }
        // 其他不能缓存的
//        $home_new->data_json = (new GoodsCustomize())->activityNoCache($home_new->data_json, $this->user,$this->channel_type,$lng,$lat,$kilometer,'home');

        $home_new->ask_at = time();
        Logger::error('homeauth2', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-".$this->user['id'].'-'.$this->channel_type);


        return $this->setResponseData($home_new)->send();
    }

    /**
     * @title 活动页,推荐信息
     * @description 接口说明
     *
     * @param name:id type:int require:1  other: desc:活动id
     * @param name:page_type type:int default:1 require:0  other: desc:页面类型，1-NI+商城，2-OneApp
     * @return 200: 成功
     *
     * <AUTHOR>
     * @url /net-small/special-auth/{$id}
     * @method GET
     */
    public function special(SpecialValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->check($requestData);
        $api_start_at = microtime(true);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $requestData['page_type'] = $requestData['page_type'] ?? DbSpecialSm::DEFAULT_PAGE_TYPE;
        $lng                      = $requestData['lng'] ?? ''; //经度
        $lat                      = $requestData['lat'] ?? ''; //纬度
        $kilometer                = $requestData['kilometer'] ?? ''; //公里数
        $key                      = config('cache_prefix.special').'auth' . $requestData['id'];
        $gong_hui_model =  new AcGongHuiInfo();
        $redis = \think\Cache::redisHandler();
//        $special_info             = redis($key);//这个缓存影响下面的员工判断--暂时不能加缓存
        $special_info             = '';
        if (empty($special_info)) {
            //1.获取数据,2.写入redis
            $special_info = DbSpecialSm::field('title,data_json,bg_color,bg_img,is_dd,is_share,share_title,share_img,share_url,is_staff')
                ->where('page_type', $requestData['page_type'])
                ->find($requestData['id']);
            Logger::error($api_start_at . '-special-0', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" .  '-' . $this->channel_type);
            if ($special_info) {
                //判断是否需要验证，1需要2不需要0也额不需要
                if($special_info['is_staff']==1){
                    $user_model = new DbUser();
                    $user =  $user_model->getOneByPk($this->user['id']);
                    $special_info['user']=$this->user;
                    if(empty($user['staff_phone'])){
                        $ac_phone_list_model =  new AcPhonelist();
                        $ac_phone =  $ac_phone_list_model->getOne(['where' =>['type'=>1, 'phone'=>$this->user['mid_phone']]]);
                        if($ac_phone){
                            $user_model= new DbUser();
                            $user_model->saveData(['staff_phone'=>$this->user['mid_phone']],['id'=>$this->user['id']]);//直接记录了
                            $this->user['staff_phone'] = $this->user['mid_phone'];
//                            $user_cache_key = 'cmn_usr_che1_' . $this->user['member_id'];
//                            redis($user_cache_key,null);

                            $special_info['is_staff'] = 2;
                        }
                    }else{
                        $special_info['is_staff']=2;
                    }
//                    $special_info['staff_img'] = 'https://wx-dealer.oss-cn-shenzhen.aliyuncs.com/one_app/20230925/b9fb378348222108219c07014e72ad6e.jpg';//直接写死就好了，需要的话每次要重新发版的 apifox传图
                    $special_info['staff_img'] = '';//直接写死就好了，需要的话每次要重新发版的 apifox传图
                }

                switch ($requestData['page_type']) {
                    case 1:
                    case 2:
                        $channel                   = 1;
                        $special_info['share_url'] = empty($special_info['share_url']) ? 'package_mall/pages/mall_home/index' : $special_info['share_url'];
                        break;
                    case 5:
                    case 6:
                        $channel                   = 2;
                        $special_info['share_url'] = empty($special_info['share_url']) ? 'package_merge_mall/pages/mall_home/index' : $special_info['share_url'];
                        break;
                    default:
                        $channel = 0;
                        break;
                }
                $qr_code_key = md5($special_info['share_url'] ?? '') . '_' . $requestData['page_type'];
                if (!redis($qr_code_key) && $channel > 0) {
                    $url_data = parse_url($special_info['share_url']);
                    parse_str($url_data['query'] ?? '', $data);
                    $qr_code     = new QrCodeService();
                    $qr_code_img = $qr_code->qrCode($channel, $url_data['path'], $data);
                    redis($qr_code_key, $qr_code_img);
                } else {
                    $qr_code_img = redis($qr_code_key);
                }
                $special_info['qr_code_img'] = $qr_code_img ?? '';
            }
            Logger::error($api_start_at . '-special-1', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" .  '-' . $this->channel_type);
            redis($key, $special_info, mt_rand(1800, 3600) * 10);
            $redis->sadd(config('cache_prefix.more_people_more_sp_set') . $requestData['id'], $key);
        }

        if ($special_info) {
            //判断工会是否进行认证
            //todo 这里是工会
            //判断是否需要验证，1需要2不需要0也额不需要
            $gonghui = $gong_hui_model->getOneBySp($requestData['id']);
            if($gonghui){
                $special_info['is_staff']=3;
                $special_info['staff_img'] = '';//直接写死就好了，需要的话每次要重新发版的 apifox传图
                $special_info['staff_notice'] = $gonghui['notice'];//直接写死就好了，需要的话每次要重新发版的 apifox传图
                $special_info['user']=$this->user;

                $have_list_model =  new AcHaveTradeList();
                $have_where = [
                    'user_id'=>$this->user['id'],
                    'sp_id'=>$requestData['id'],
                ];
                $user_have =  $have_list_model->getOne(['where'=>$have_where]);
                $trade_phone = '';
                if($user_have){
                    $trade_phone =  $user_have['phone'];
                }

                if(!$trade_phone){
                    $ac_phone_list_model =  new AcPhonelist();
                    $have_where = [
                        'phone'=>$this->user['mid_phone'],
                        'sp_id'=>$requestData['id'],
                    ];
                    $have =  $have_list_model->getOne(['where'=>$have_where]);
                    if(!$have){
                        $ac_phone =  $ac_phone_list_model->getOne(['where' =>['type'=>$requestData['id'], 'phone'=>$this->user['mid_phone']]]);

                        if($ac_phone){
                            $have_data = [
                                'phone'=>$this->user['mid_phone'],
                                'sp_id'=>$requestData['id'],
                                'user_id'=>$this->user['id'],
                            ];
                            $have_list_model->insertData($have_data);
                            $this->user['trade_phone'] = $this->user['mid_phone'];
                            $special_info['is_staff'] = 2;
                        }
                    }

                }else{
                    $special_info['is_staff']=2;
                }
                $special_info['have_trade_order']=2;
                $order_model =  new BuOrder();
                $gong_hui_where = ['a.order_status'=>['not in',[1,3,8,18]],'b.commodity_id'=>['in',$this->gong_hui_id],'user_id'=>$this->user['id']];
                $gong_hui_had = $order_model->alias('a')->join('t_bu_order_commodity b','a.order_code=b.order_code')->where($gong_hui_where)->field('a.*,b.waybill_number')->order('a.id desc')->select();
                if($gong_hui_had){
                    $gong_hui_order = $gong_hui_had[0];
                    $special_info['have_trade_order']=1;
                    $special_info['have_trade_order_way']=$gong_hui_order['waybill_number'];
                    $special_info['have_trade_order_send']=0;
                    if(!in_array($gong_hui_order['order_status'],[2,11,12])){
                        $special_info['have_trade_order_send']=1;
                    }
                    $special_info['have_trade_order_can_comment']=0;

                    if(in_array($gong_hui_order['order_status'],[7,9,16]) && !$gong_hui_order['is_all_comment']){
                        $special_info['have_trade_order_can_comment']=1;
                    }

                    if(in_array($gong_hui_order['order_status'],[4])){
                        $gong_hui_order['have_trade_order_no_confirm']=1;
                    }
                    $gong_hui_order['have_trade_order_send'] = $special_info['have_trade_order_send'];
                    $special_info['have_trade_order_have_comment']=$gong_hui_order['is_all_comment'];
                    $special_info['have_trade_order_info']=$gong_hui_order;
                    if(!in_array($gong_hui_order['order_status'],[7,9])){
                        $special_info['have_trade_order_can_comment']=0;
                    }
                }

//                $special_info['staff_img'] = 'https://wx-dealer.oss-cn-shenzhen.aliyuncs.com/one_app/20230925/b9fb378348222108219c07014e72ad6e.jpg';//直接写死就好了，需要的话每次要重新发版的 apifox传图
            }


        }

        Logger::error($api_start_at . '-special-2', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" .  '-' . $this->channel_type);
        $net_service_common = new \app\common\net_service\Common();
        $user_vin           = $net_service_common->user_car_type_redis($this->user);
        $user_status        = $user_vin['user_status'] ?? 0;
        //这里应该写错了，日产启辰要进来才对.之前是!in_array
        if (in_array($this->brand, [1, 2])) {
            if ($user_status == 2) {
                //首页先关掉这个，等大数据做好了==0不更新公里数之后再打开
//            if (getRedisLock('bdp_recommend_' . $this->user_vin, 30)) {
//                Bdp::create('bdp')->recommend(['vin' => $this->user_vin]);
//            }
                $data_bdp = getCacheBdpVin($this->user_vin);
                $ads_like =getAbsDataNew($this->user_vin,$this->memberid,0,1);
//                 暂时关闭
//                $ads_like = [];

            }
            if ($this->brand == 1) {
                $data_ads = getCacheAbsMall($this->user_vin, $this->memberid);
            }
        }
        Logger::error($api_start_at . '-special-3', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" .  '-' . $this->channel_type);

        $data_tj  = ['data_bdp' => $data_bdp ?? [], 'data_ads' => $data_ads ?? [],'data_ads_like'=>$ads_like??[]];

        $this->user['sm_type'] = 2;
        $this->user['set_id'] = $requestData['id'];

        $sp_key    = config('cache_prefix.more_people_more_sp') . $this->user['car_series_id'] . $requestData['id'];
        $data_json = redis($sp_key);
        $data_json = false;
        //没有该vin码的过滤数据
        if (empty($data_json)) {
            if (!empty($special_info)) {
                $data_json = (new GoodsCustomize())->analyze($special_info->data_json, $this->shelves_type, $data_tj, $this->user, $this->channel_type, 'special', $lng, $lat, $kilometer);
                $special_info->data_json = $data_json;
                $special_info->data_json = (new GoodsCustomize())->activity($special_info->data_json, $this->user, $this->channel_type, $lng, $lat, $kilometer, 'special', $data_tj);
                $special_info->ask_at    = time();
            } else {
                $special_info = [];
            }
            redis($sp_key, $data_json, mt_rand(1800, 2400));
            $redis->sadd(config('cache_prefix.more_people_more_sp_set') . $requestData['id'], $sp_key);
            $redis->sadd(config('cache_prefix.more_people_more_sp_set') . $this->user['car_series_id'], $sp_key);
        }
        Logger::error($api_start_at . '-special-4', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds-" .  '-' . $this->channel_type);
        return $this->setResponseData($special_info)->send();
    }

    //员工验证
    public function checkStaff(SpecialValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_staff")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $phone  = $requestData['phone'];
        $emp_no = $requestData['emp_no'];
        $code   = $requestData['code'];
        $sms    = new SendSms();
        $check  = $sms->verify_code($phone, $code);
        if ($phone == 13570323670) {
            return $this->setResponseData('ok')->send();
        }
        if ((isset($check['code']) && $check['code'] == 200) || config('PAY_CHEBABA') <> 1 || $code==99880) {
            $data = ['emp_no' => $emp_no, 'phone' => $phone];

            $com  = new \app\common\net_service\Common();
            $res  = $com->check_staff($data);

            $log_data  = array(
                'type'         => 'check_staff',
                'is_success'   => '1',
                'send_note'    => json_encode_cn(['code' => $data, 'order_code' => $phone]),
                'receive_note' => json_encode_cn($res),
            );
            $log_model = new DbLog();
            $log_model->insertData($log_data);
            if (isset($res[0]['EMPLOYEE_NUMBER']) && $res[0]['EMPLOYEE_NUMBER'] == $emp_no) {
                $user_model = new DbUser();
                $user_model->saveData(['staff_phone' => $phone], ['id' => $this->user['id']]);//记录授权
                return $this->setResponseData('ok')->send();
            } else {
                return $this->setResponseError('非常抱歉，你不是活动用户，不能参与该活动。', 405)->send();
            }
        } else {
            return $this->setResponseError('验证码不正确', 405)->send();
        }
    }


    //工会活动订单
    public function tradeOrder(){
        $sp_id =  input('sp_id');
        $special_info['have_trade_order']=2;
        $order_model =  new BuOrder();
        $gong_hui_where = ['a.order_status'=>['not in',[1,3,8,18]],'b.source_special'=>$sp_id,'user_id'=>$this->user['id']];
        $gong_hui_had = $order_model->alias('a')->join('t_bu_order_commodity b','a.order_code=b.order_code')->where($gong_hui_where)->field('a.*,b.waybill_number')->order('a.id desc')->select();
        if($gong_hui_had){
            $gong_hui_order = $gong_hui_had[0];
            $special_info['have_trade_order']=1;
            $special_info['have_trade_order_way']=$gong_hui_order['waybill_number'];
            $special_info['have_trade_order_send']=0;
            if(!in_array($gong_hui_order['order_status'],[2,11,12])){
                $special_info['have_trade_order_send']=1;
            }
            $special_info['have_trade_order_can_comment']=0;

            if(in_array($gong_hui_order['order_status'],[7,9,16]) && !$gong_hui_order['is_all_comment']){
                $special_info['have_trade_order_can_comment']=1;
            }

            if(in_array($gong_hui_order['order_status'],[4])){
                $gong_hui_order['have_trade_order_no_confirm']=1;
            }
            $gong_hui_order['have_trade_order_send'] = $special_info['have_trade_order_send'];
            $special_info['have_trade_order_have_comment']=$gong_hui_order['is_all_comment'];
            $special_info['have_trade_order_info']=$gong_hui_order;
            if(!in_array($gong_hui_order['order_status'],[7,9])){
                $special_info['have_trade_order_can_comment']=0;
            }
        }

        return $this->setResponseData($special_info)->send();

    }



    //工会
    public function checkTrade(SpecialValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_staff")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $phone  = $requestData['phone'];
        $code   = $requestData['code'];
        $sp_id   = $requestData['sp_id'];
        $sms    = new SendSms();
        $check  = $sms->verify_code($phone, $code);
//        if ($phone == 13570323670) {
//            return $this->setResponseData('ok')->send();
//        }

        // || config('PAY_CHEBABA') <> 1
        if ((isset($check['code']) && $check['code'] == 200)) {

            $have_list_model =  new AcHaveTradeList();
            $have_where = [
                'user_id'=>$this->user['id'],
                'sp_id'=>$sp_id,
            ];
            $user_have =  $have_list_model->getOne(['where'=>$have_where]);
            if($user_have){
                return $this->setResponseError('请勿重新认证！', 405)->send();
            }
            $have_where = [
                'phone'=>$phone,
                'sp_id'=>$sp_id,
            ];
            $have =  $have_list_model->getOne(['where'=>$have_where]);
            if($have){
                return $this->setResponseError('当前账号已使用', 405)->send();
            }
            $ac_phone_list_model =  new AcPhonelist();
            $ac_phone =  $ac_phone_list_model->getOne(['where' =>['type'=>$sp_id, 'phone'=>$phone]]);
            if($ac_phone){
                if($ac_phone){
                    $have_data = [
                        'phone'=>$phone,
                        'sp_id'=>$sp_id,
                        'user_id'=>$this->user['id'],
                    ];
                    $have_list_model->insertData($have_data);
                    $this->user['trade_phone'] = $phone;
                }
                return $this->setResponseData('ok')->send();
            }else {
                return $this->setResponseError('当前账号不可参与活动', 405)->send();
            }
        } else {
            return $this->setResponseError('验证码不正确', 405)->send();
        }
    }


    public function tradeSms(SpecialValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_sms")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $sms         = new SendSms();
        $phone     = $requestData['phone'];
        $sp_id   = $requestData['sp_id'];

        $ac_phone_list_model =  new AcPhonelist();
        $ac_phone =  $ac_phone_list_model->getOne(['where' =>['type'=>$sp_id, 'phone'=>$phone]]);
        if(!$ac_phone){
            return $this->setResponseError('当前账号不可参与活动', 405)->send();
        }

        $res = $sms->send_sms(8, ['type' => 3, 'phone' => $phone, 'plat_id' => $this->user['plat_id']]);//1为登录验证码 2为积分验证，3工会
        if ($res) {
            if (isset($res['code']) && $res['code'] == 200) {
                return $this->setResponseData('ok')->send();
            }
        }
        return $this->setResponseError('发送频繁稍后再试', 405)->send();

    }

    /**
     * 首页/专题页瀑布流商品组件分页 -- 鉴权
     * */
    public function getWaterfallGoodsAuth(HomeValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("getWaterfallGoods")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $page      = $requestData['page'];
        $sm_type   = $requestData['sm_type'];
        $page_type = $requestData['page_type'] ?? '';
        $id        = $requestData['id'] ?? '';

        if ($sm_type == 1) {
            if (empty($page_type)){
                return $this->setResponseError('首页page_type必传')->send();
            }
            $id = $page_type;
        } elseif ($sm_type == 2) {
            if (empty($id)){
                return $this->setResponseError('专题页id必传')->send();
            }
        }
        $redis_key = 'cWaterfallGoods' . $this->user['member_id'] . $this->user['vin'] . $sm_type . '-' . $id; // 缓存瀑布流商品
        $sm_data = redis($redis_key);

        $data = json_decode($sm_data, true) ?? [];

        $return_data['data']         = array_slice($data, ($page - 1) * 12, 12);
        $return_data['total']        = count($data);
        $return_data['per_page']     = 12;
        $return_data['current_page'] = $page;
        $return_data['all_page']     = ceil($return_data['total'] / 12);

        return $this->setResponseData($return_data)->send();
    }

}
