<?php
namespace app\net_small\controller;

use app\common\model\db\DbBdpRecommend;
use app\common\model\db\DbCard;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbNDiscountInfo;
use app\common\model\db\DbRecommendActivity;
use app\common\model\db\DbRecommendActivityCard;
use app\common\model\db\DbRecommendActivityCommodity;
use app\common\model\db\DbRecommendHotBanner;
use app\common\model\db\DbRecommendHotCommodity;
use app\common\model\db\DbRecommendHotTab;
use app\common\model\db\DbRecommendHotTabClass;
use app\common\model\db\DbRecommendInfomercial;
use app\common\model\db\DbRecommendJumpLink;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSendCardPage;
use app\common\model\wlz\WlzCrowdsLogs;
use app\common\net_service\NetGoods;
use app\common\net_service\NetUser;
use app\common\validate\Service;

class ServicePage extends Common{

    public function __construct()
    {
        parent::__construct();

    }
    public function youLike(Service $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("you_like")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $nvi_vin = $requestData['nvi_vin'] ?? '';
        if(!empty($nvi_vin)){
            $bindVin = inBindVin($nvi_vin,$this->user,1);
        }
//        $dbBdpRecommendObj =  new DbBdpRecommend();
//        $abs_new = $dbBdpRecommendObj->where(['vin' => $nvi_vin])->find();

        $abs_new =  getAbsDataNew($nvi_vin,'',0,1);
//        $abs_new =  getCacheAbsMall($nvi_vin,'',0);

        if(!$abs_new){
            $re_data = ['list'=>[],'user'=>$this->user];
        }else{
            //todo 需要再筛选分类
            $goods = $abs_new['goods'];
            $goods_ids =  array_column($goods,'commodity_id');
            $net_goods =  new NetGoods();
            $commodity_ids_str = implode(',', $goods_ids);
            $arr = [
                'commodity_ids' => $commodity_ids_str,
                'user' => $this->user['id'],
            ];
            $cache_key = 'tmp_goods_list_like:'.md5(implode("_", $arr));
            $goods_list = redis($cache_key);

            if (empty($goods_list)) {
                $goods_list        = $net_goods->goodsList(['page' => 1, 'pageSize' => count($goods_ids), 'commodity_ids' => $commodity_ids_str, 'order_by' => "field(a.commodity_id, $commodity_ids_str),"], $this->user, $this->channel_type);
                redis($cache_key, $goods_list, mt_rand(300, 600));
            }
            $list =  $goods_list['msg']['data'];
            $re_data = ['list'=>empty($list)?[]:$list,'user'=>$this->user];
        }

        return $this->setResponseData($re_data)->send();
    }
    //推荐活动
    public function actList(Service $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("hot_sale")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $nvi_vin = $requestData['nvi_vin'] ?? '';
        if(!empty($nvi_vin)){
            $bindVin = inBindVin($nvi_vin,$this->user,1);
        }
        if(!$this->user_id){
            return $this->setResponseData("{}")->send();
        }

        $act_model  =new DbRecommendActivity();
        $date_now = date('Y-m-d H:i:s');
        $info_user   = new WlzCrowdsLogs();
        $user_info_list = $info_user->getList(['where'=>['user_id'=>$this->user_id]]);
        $where = ['start_date'=>['<=',$date_now],'end_date'=>['>=',$date_now],'is_enable'=>1];

        if($user_info_list){
            $user_wlz =  array_column($user_info_list,'crowd_id');
            $user_wlz[]=0;
            $where['crowd_id'] =['in',$user_wlz];
        }else{
            $where['crowd_id'] =0;
        }

        //那就是合法范围内，按照最新生效倒序，取第一条？
        $act =  $act_model->getOne(['where'=>$where,'order'=>'start_date desc']);
        if(!$act){
            return $this->setResponseData("{}")->send();
        }
        $act_list =  $act_model->getList(['where'=>$where,'order'=>'start_date desc']);
        $act = [];
        foreach ($act_list as $a_v){
            $segment_info = get_user_segment_info();
            $membership = $segment_info['membership_level'];
            $owner = $segment_info['brand_owner_label'];
            if($a_v['recommend_type']==5){
                $act =$a_v;
                break;
            }
            if(in_array($a_v['recommend_type'],[1,2])){
                if($a_v['recommend_type']==1){
                    $ac_model =  new DbLimitDiscount();
                }else{
                    $ac_model =  new DbSeckill();
                }
                $ac_info =  $ac_model->getOneByPk($a_v['act_id']);
                $user_segment_options = explode(',',$ac_info['user_segment_options']);
                $can_act = 0;
                if ($ac_info['user_segment']) {
                    if ($ac_info['user_segment'] == 1) {
                        if(in_array($membership,$user_segment_options)){
                            $can_act = 1;
                        }
                    } else {
                        if(in_array($owner,$user_segment_options)){
                            $can_act = 1;
                        }
                    }
                }else{
                    $can_act = 1;
                }
                if($can_act){
                    $act['start_date'] = $ac_info['start_time'];
                    $act['end_date'] = $ac_info['end_time'];
                    $act =$a_v;
                    if($act['recommend_type']==1){
                        $act['title'] = "限时优惠";
                    }
                    if($act['recommend_type']==2){
                        $act['title'] = "限时秒杀";
                        $act['seckill_type'] = $ac_info['seckill_type'];
                        $act['day_start_time'] = $ac_info['day_start_time'];
                        $act['day_end_time'] = $ac_info['day_end_time'];
                        if($act['seckill_type']==2){
                            $time_data = date('H:i:s');
                            if($time_data<$act['day_start_time'] || $time_data>$act['day_end_time']){
                                unset($act);
                                continue;
                            }else{
                                $act['end_date'] = date('Y-m-d').' '.$act['day_end_time'];
                            }
                        }
                        //如果不在进行中则取下一个活动

//                        $act['seckill_type'] = $ac_info['seckill_type'];
//                        $act['day_start_time'] = $ac_info['day_start_time'];
//                        $act['day_end_time'] = $ac_info['day_end_time'];
                        //seckill_type秒杀类型 0：固定时间 1:每天重复
                        //day_start_time 10:00:00 day_end_time 23:59:00 如果seckill_type==1的情况下 倒计时取下一个
                    }
                    break;
                }
            }
//            if($a_v['recommend_type']==2){
//                $ac_model =  new DbSeckill();
//                $ac_info =  $ac_model->getOneByPk($a_v['act_id']);
//                $user_segment_options = explode(',',$ac_info['user_segment_options']);
//                $can_act = 0;
//                if ($ac_info['user_segment']) {
//                    if ($ac_info['user_segment'] == 1) {
//                        if(in_array($membership,$user_segment_options)){
//                            $can_act = 1;
//                        }
//                    } else {
//                        if(in_array($owner,$user_segment_options)){
//                            $can_act = 1;
//                        }
//                    }
//                }else{
//                    $can_act = 1;
//                }
//                if($can_act){
//                    $act =$a_v;
//                    break;
//                }
//            }
            if(in_array($a_v['recommend_type'],[3,4])){
                if($a_v['recommend_type']==3){
                    $ac_model =  new DbFullDiscount();
                    $ac_info =  $ac_model->getOneByPk($a_v['act_id']);
                    $full_discount_rules_list = json_decode($ac_info['full_discount_rules'],true);
                    if ($ac_info['user_segment']) {
                        if ($ac_info['user_segment'] == 1) {
                            $full_discount_rules_list = $full_discount_rules_list[$membership]??'';
                        } else {
                            $full_discount_rules_list = $full_discount_rules_list[$owner]??'';
                        }
                    }
                    if(!$full_discount_rules_list){
                        continue;
                    }else{
                        $act =$a_v;
                        $act['act_word'] = sprintf("满%s减%s",$full_discount_rules_list[0][0],end($full_discount_rules_list[0]));
                        break;
                    }
                }else{
                    $ac_model =  new DbNDiscount();
                    $n_dis_info_model = new DbNDiscountInfo();
                    $ac_info =  $ac_model->getOneByPk($a_v['act_id']);
                    $where = ['n_id'=>$a_v['act_id']];
                    if ($ac_info['user_segment']) {
                        if ($ac_info['user_segment'] == 1) {
                            $where['segment_label'] = $membership;
                        } else {
                            $where['segment_label'] = $owner;
                        }
                    }

//                    $order_by = "p_number desc,piece desc";
                    $order_by = "id asc";
                    $field    = "p_number,piece,discount,n_id";
                    $param    = [
                        'where' => $where,
                        'order' => $order_by,
                        'field' => $field,
                    ];
                    //获得购买次数最高 ，购买数量最多的折扣信息
                    $dis_info = $n_dis_info_model->getOne($param);
                    if(!$dis_info){
                        continue;
                    }else{
                        $act =$a_v;
//                        $act_word = '';
//                        foreach($dis_info as $v_n){
//                            $act_word.= sprintf("%s件%s折,",$v_n['piece'],$v_n['discount']);
//                        }

//                        $act['act_word'] = trim($act_word,',');
                        $act['act_word'] = sprintf("%s件%s折",$dis_info['piece'],formatNumber($dis_info['discount']));
                        break;
                    }
                }
            }

        }

        if(!$act){
            return $this->setResponseData("{}")->send();
        }
        //推荐类型 1-限时优惠、2-秒杀活动、3-满减、4-N件N折、5-优惠券
        //前端自己写限时秒杀，限时折扣
        $act['act_title'] = $act['title']??'';
//        if($act['recommend_type']==1){
//            $act['title'] = "限时优惠";
//        }
//        if($act['recommend_type']==2){
//            $act['title'] = "限时秒杀";
//        }
        $jump =  $this->getJumpUrl(1,$act['id'],$this->channel_type);
        if($jump){
            $act['jump_type'] = $jump['jump_type'];
            $act['jump_url'] = $jump['path'];
        }

//        if(in_array($act['recommend_type'],[3,4])){
//            $segment_info = get_user_segment_info();
//            $membership = $segment_info['membership_level'];
//            $owner = $segment_info['brand_owner_label'];
//            if($act['recommend_type']==3){
//                $ac_model =  new DbFullDiscount();
//                $ac_info =  $ac_model->getOneByPk($act['act_id']);
//                $full_discount_rules_list = json_decode($ac_info['full_discount_rules'],true);
//                if ($ac_info['user_segment']) {
//                    if ($ac_info['user_segment'] == 1) {
//                        $full_discount_rules_list = $full_discount_rules_list[$membership]??'';
//                    } else {
//                        $full_discount_rules_list = $full_discount_rules_list[$owner]??'';
//                    }
//                }
//                //我按照上面的条件就只能取这个活动，因为这里被过滤了，那就这个模块不显示了
//                if(!$full_discount_rules_list){
//                    return $this->setResponseData("{}")->send();
//                }
////                print_json($full_discount_rules_list);
////                $full_dis_title = '';
////                foreach ($full_discount_rules_list as $f_v){
////
//                $act['act_word'] = sprintf("满%s减%s",$full_discount_rules_list[0][0],end($full_discount_rules_list[0]));
//            }else{
//                $ac_model =  new DbNDiscount();
//                $n_dis_info_model = new DbNDiscountInfo();
//                $ac_info =  $ac_model->getOneByPk($act['act_id']);
//                $where = ['n_id'=>$act['act_id']];
//                if ($ac_info['user_segment']) {
//                    if ($ac_info['user_segment'] == 1) {
//                        $where['segment_label'] = $membership;
//                    } else {
//                        $where['segment_label'] = $owner;
//                    }
//                }
//
//                $order_by = "p_number desc,piece desc";
//                $field    = "p_number,piece,discount,n_id";
//                $param    = [
//                    'where' => $where,
//                    'order' => $order_by,
//                    'field' => $field,
//                ];
//                //获得购买次数最高 ，购买数量最多的折扣信息
//                $dis_info = $n_dis_info_model->getOne($param);
//                if(!$dis_info){
//                    return $this->setResponseData("{}")->send();
//                }
//                $act['act_word'] = sprintf("%s件%s折",$dis_info['piece'],$dis_info['discount']);
//            }
//        }
        $net_goods =   new NetGoods();
        if(in_array($act['recommend_type'],[1,2,3,4])){
            $act_goods_model =  new DbRecommendActivityCommodity();
            $where = ['recommend_id'=>$act['id'],'is_enable'=>1];
            $order = 'sort asc';
            $act_goods_list =  $act_goods_model->getList(['where'=>$where,'order'=>$order]);
            $goods_ids = array_column($act_goods_list,'commodity_id');
            $commodity_ids_str = implode(',', $goods_ids);
            $goods_list        = $net_goods->goodsList(['page' => 1, 'pageSize' => count($goods_ids), 'commodity_ids' => $commodity_ids_str, 'order_by' => "field(a.commodity_id, $commodity_ids_str),"], $this->user, $this->channel_type);
            $list =  $goods_list['msg']['data'];
            $act['act_list'] = $list;
        }
        if($act['recommend_type']==5){

            $act_card_model  = new DbRecommendActivityCard();
            $where = ['recommend_id'=>$act['id'],'is_enable'=>1];
            $order = 'sort asc';
            $act_card_list =  $act_card_model->getList(['where'=>$where,'order'=>$order]);
            $act_card_id_arr = [];
            foreach ($act_card_list as $v){
                $act_card_id_arr[] = $v['card_id'];
            }
            $act_card_id_str = implode(',',$act_card_id_arr);
            if(!$act_card_id_arr){
                return $this->setResponseData("{}")->send();
            }
            $card_model        = new DbCard();
            $canGetList = $card_model->getList(['where'=>['is_enable'=>1,'id'=>['in',$act_card_id_arr]],'field'=>"*,'' as order_id",'order'=>"field(id, $act_card_id_str)"]);
            if(!$canGetList){
                return $this->setResponseData("{}")->send();
            }
            foreach($canGetList as $kk=>$activityCardItem){
                //1 可领取
                if(empty($query_card_arr_tmp[$activityCardItem['id']])){//空表示没领过
                    $canGetList[$kk]['is_can_receive'] = 1;
                }else{
                    $canGetList[$kk]['is_can_receive'] = $query_card_arr_tmp[$activityCardItem['id']] > 0 ? 1 : 0;
                }
                $canGetList[$kk]['card_id'] = $activityCardItem['id'];
            }
            $card_list =  $net_goods->card_get_use([],[],$this->user,$this->channel_type,[],[],1,$canGetList,"","","");
            $get_card_list = $card_list['get_card_list'];
            if($get_card_list){
                foreach ($get_card_list as $g_k=>$g_v){
                    $get_card_list[$g_k]['card_quota'] =  formatNumber($g_v['card_quota']);
                    $get_card_list[$g_k]['card_discount'] =  formatNumber($g_v['card_discount']);
                }
            }
            $act['card_list'] = $get_card_list;
            $act['act_code'] = 'SPAC-0218'.$this->channel_type;
        }
        return $this->setResponseData($act)->send();



    }

    //热卖推荐
    ///net-small/service-page/hot-sale
    public function hotSaleRec(Service $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("hot_sale")->check($requestData);
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $nvi_vin = $requestData['nvi_vin'] ?? '';
        if(!empty($nvi_vin)){
            $bindVin = inBindVin($nvi_vin,$this->user,1);
        }

        //end
        $res =  $this->recGoods(2,$this->user,$this->channel_type);
        $car_info = $this->_getCarer($this->user['bind_unionid'], $this->user['member_id'], $this->user['one_id'], $this->channel_type);
        if (empty($car_info)) {
            $car_info = [];
        }
        $re_data = ['list'=>$res['res_list'],'car'=>['point'=>$car_info['pv_point']??0,'member_type'=>$car_info['member_type']??'']];
        return $this->setResponseData($re_data)->send();


    }

    //潜客
    public function potCustRec(){
        $goods_model =  new DbRecommendHotCommodity();
        $where = ['recommend_type'=>2,'is_enable'=>1];
        $list =  $goods_model->getList(['where'=>$where,'order'=>'sort asc']);
        $goods_ids = array_column($list,'commodity_id');
        $commodity_ids_str = implode(',', $goods_ids);
        if($list){
            $net_goods =   new NetGoods();
            $goods_list        = $net_goods->goodsList(['page' => 1, 'pageSize' => count($goods_ids), 'commodity_ids' => $commodity_ids_str, 'order_by' => "field(a.commodity_id, $commodity_ids_str),"], $this->user, $this->channel_type);
            $list =  $goods_list['msg']['data'];
        }else{
            $list=[];
        }


        $img_model =  new DbRecommendInfomercial();
        $date_now = date('Y-m-d H:i:s');
        $img_where = ['start_date'=>['<=',$date_now],'is_enable'=>1];
        $img_where[]=['exp',sprintf("end_type =1 or end_date>='%s'",$date_now)];

        //那就是开始时候倒序取第一条
        $img_info = $img_model->getOne(['where'=>$img_where,'order'=>'start_date desc']);
        $re_data = ['list'=>$list];
        $commodity_ids_new =  array_column($list,'commodity_id');
        if($img_info){
            $re_data['img'] = $img_info['cover_image'];
        }
        $jump =  $this->getJumpUrl(3,0,$this->channel_type);
        if($jump){
            $re_data['jump_type'] = $jump['jump_type'];
            $re_data['jump_url'] = $jump['path'];
            if($jump['jump_type']==7){
                $re_data['jump_url'] = implode(',', $commodity_ids_new);
            }
        }

        return $this->setResponseData($re_data)->send();
    }

    //service-page/rec-page
    //服务页-热卖推荐专题页
    public function recPage(Service $validate)
    {
        //外层做就好，这一层不做
//        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
//        $result      = $validate->scene("hot_sale")->check($requestData);
//        if (empty($result)) {
//            return $this->setResponseError($validate->getError())->send();
//        }
//        $nvi_vin = $requestData['nvi_vin'] ?? '';
//        if(!empty($nvi_vin)){
//            $bindVin = inBindVin($nvi_vin,$this->user,1);
//        }

        $date_now = date('Y-m-d H:i:s');
        $banner_model =  new DbRecommendHotBanner();
        $img_where = ['start_date'=>['<=',$date_now],'is_enable'=>1];
        $img_where[]=['exp',sprintf("end_date is null or end_date>='%s'",$date_now)];
        $user_info_list=[];
        if($this->user_id){
            $info_user   = new WlzCrowdsLogs();
            $user_info_list = $info_user->getList(['where'=>['user_id'=>$this->user_id]]);
        }

        if($user_info_list){
            $user_wlz =$userGroupCrowdIds =   array_column($user_info_list,'crowd_id');
//            $img_where['crowd_id'] =['in',$user_wlz];
        }else{
            $userGroupCrowdIds = 0;
//            $img_where['crowd_id'] =0;
        }
        $img_list = $banner_model->getList(['where'=>$img_where,'order'=>'position asc,crowd_id desc,sort asc ','field'=>'id,cover_image,position,crowd_id']);
        $new_img = [];
        foreach ($img_list as $kk=>$vv){
            if(!isset($new_img[$vv['position']])){
                //第一个选择人群包匹配上
                if($vv['crowd_id'] && $userGroupCrowdIds && in_array($vv['crowd_id'],$userGroupCrowdIds)){
                    $new_img[$vv['position']] = $vv;
                    continue;
                }
                //如果已经被匹配上了也不会到这里来，排序的时候优先人群包在前面了
                if(!$vv['crowd_id']){
                    $new_img[$vv['position']] = $vv;
                    continue;
                }

            }

        }

//        if (!empty($image['userGroups']) && count(array_intersect($image['userGroups'], $userGroupCrowdIds)) <= 0) {
//            continue;
//        }
        foreach ($new_img as $k=>$v){
            $jump =  $this->getJumpUrl(4,$v['id'],$this->channel_type);
//            if(!$jump){
//                unset($new_img[$k]);
//            }else{
//                $new_img[$k]['jump_type'] = $jump['jump_type'];
//                $new_img[$k]['jump_url']= $jump['path'];
//            }
            if($jump){
                $new_img[$k]['jump_type'] = $jump['jump_type'];
                $new_img[$k]['jump_url']= $jump['path'];
            }else{
                $new_img[$k]['jump_type'] = $new_img[$k]['jump_url']='';
            }


        }
        $res =  $this->recGoods(1,$this->user,$this->channel_type);
        foreach ($res['res_list'] as $k=>$v){
            if($v['title_id']!='more'){
                $jump =  $this->getJumpUrl(2,$v['title_id'],$this->channel_type);
                if($jump){
                    $res['res_list'][$k]['jump_type'] = $jump['jump_type'];
                    $res['res_list'][$k]['jump_url'] = $jump['path'];
                }
            }

        }
        $re_data = ['list'=>$res['res_list'],'tab_list'=>$res['tab_list'],'user_crow_id'=>$userGroupCrowdIds,'user'=>$this->user];
        $re_data['img_list'] = array_values($new_img);

        return $this->setResponseData($re_data)->send();

    }

    private function recGoods($class_type,$user,$channel)
    {
        $abs_new =  getAbsDataNew($user['vin'],'',0,2);
        $goods_abs_ids = [];
        //大数据计算的暂时不要了--
//        $abs_new =  getCacheAbsMall($nvi_vin,'',0);
        if($abs_new){
            $goods_abs_ids =  array_column($abs_new['goods'],'commodity_id');
        }

        $goods_model =  new DbRecommendHotCommodity();
        $where = ['recommend_type'=>1,'is_enable'=>1];
        $order = 'location asc ,sort asc';
        $r_list =  $goods_model->getList(['where'=>$where,'order'=>$order]);
        if(!$r_list){
            return ['res_list'=>[],'tab_list'=>[]];
        }
        $r_tab_list = [];
        $r_tab_sort_list = [];
        $l_list_1= [];
        $l_list_sort= 0;
        $l_list_2 = [];
        foreach ($r_list as $vv){
            if($vv['location']==1){
                $l_list_1[]=$vv;
                $l_list_sort++;
            }else{
                $l_list_2[]=$vv;
            }
        }
        $goods_abs_ids_new= [];
        if($goods_abs_ids){
            foreach ($goods_abs_ids as $kkk=> $abv){
                $goods_abs_ids_new[$kkk]['tab_id'] = '';
                $goods_abs_ids_new[$kkk]['sort'] = $l_list_sort;
                $goods_abs_ids_new[$kkk]['recommend_type'] = 1;
                $goods_abs_ids_new[$kkk]['commodity_id'] = $abv;
            }
        }

        $r_list_new = array_merge($l_list_1,$goods_abs_ids_new);
        $r_list_new = array_merge($r_list_new,$l_list_2);
        foreach ($r_list_new as $v){
            if($v['tab_id']){
                $r_tab_list[$v['commodity_id']]=$v['tab_id'];
            }
            $r_tab_sort_list[$v['commodity_id']] = $v['sort'];
        }

        $goods_ids = array_column($r_list_new,'commodity_id');
        $commodity_ids_str = implode(',', $goods_ids);
        $cache_key = 'tmp_goods_rec_hot:'.md5($commodity_ids_str.$user['id'].$channel);
        $goods_list = redis($cache_key);
        if(!$goods_list){
            $net_goods =   new NetGoods();
            $goods_list        = $net_goods->goodsList(['page' => 1, 'pageSize' => count($goods_ids), 'commodity_ids' => $commodity_ids_str, 'order_by' => "field(a.commodity_id, $commodity_ids_str),"], $user, $channel);
            redis($cache_key, $goods_list, mt_rand(300, 600));

        }

        $list =  $goods_list['msg']['data'];
        $type_goods_list = [];


        foreach ($list as $k=> $v){
            $list[$k]['r_tab_id'] = $r_tab_list[$v['commodity_id']]??'';
            $list[$k]['sort'] = $r_tab_sort_list[$v['commodity_id']]??'';//预置排序
            $type_goods_list[$v['comm_type_id']][]=$v;
        }

        $tab_model =  new DbRecommendHotTab();
        $tab_where = ['class_type'=>$class_type,'is_enable'=>1];
        $tab_list =  $tab_model->getList(['where'=>$tab_where,'order'=>'sort asc']);
        $tab_class_model =  new DbRecommendHotTabClass();
        $tab_class =  $tab_class_model->getList(['where'=>['is_enable'=>1]]);
        $tc_arr = [];
        if($tab_class){
            foreach ($tab_class as $t_c_v){
                $tc_arr[$t_c_v['tab_id']][]=$t_c_v['comm_type_id'];
            }
        }
        $res_list = [];

        foreach ($tab_list as $t_v){
            $tab_class_one =  $tc_arr[$t_v['id']]??[];
            //从单个分类改成分类表
//            if(isset($type_goods_list[$t_v['comm_type_id']])){
//                if($class_type==2){
//                    $res_list[] = $type_goods_list[$t_v['comm_type_id']][0];
//                }else{
//                    $res_list[$t_v['id']]['title'] = $t_v['title'];
//                    foreach ($list as $l_v){
//                        if($l_v['r_tab_id'] && $l_v['r_tab_id']==$t_v['id']){
//                            $res_list[$t_v['id']]['list'][] = $l_v;
//                        }elseif(!$l_v['r_tab_id'] && $l_v['comm_type_id']==$t_v['comm_type_id']){
//                            $res_list[$t_v['id']]['list'][] = $l_v;
//                        }
//                    }
//                }
//            }

            $tab_class_one =  array_unique($tab_class_one);
            if($class_type==2){
                if($tab_class_one){
                    foreach ($tab_class_one as $tco_v){
                        if(isset($type_goods_list[$tco_v])){
                            $res_list[$t_v['id']] = $type_goods_list[$tco_v][0];
                            break;
                        }
                    }
                }
            }else{
                $have_data=0;
                foreach ($list as $l_k=> $l_v){
                    $list[$l_k]['have_tab'] = 0;
                    if($l_v['r_tab_id'] && $l_v['r_tab_id']==$t_v['id']){
                        $list[$l_k]['have_tab'] =$l_v['have_tab'] =1;
                        $res_list[$t_v['id']]['list'][] = $l_v;
                        $have_data=1;
                        unset($list[$l_k]);
                    }elseif(!$l_v['r_tab_id'] && in_array($l_v['comm_type_id'],$tab_class_one)){
                        $list[$l_k]['have_tab'] = $l_v['have_tab'] =1;
                        $have_data=1;
                        $res_list[$t_v['id']]['list'][] = $l_v;
                        unset($list[$l_k]);
                    }
                }
                if($have_data){
                    $res_list[$t_v['id']]['title'] = $t_v['title'];
                    $res_list[$t_v['id']]['title_id'] = $t_v['id'];
                }
            }
        }
        if($class_type!=2){
            if($list && $res_list){
                foreach ($list as $l_k1=> $l_v1){
                    if($l_v1['have_tab']<>1){
                        $res_list['more9999']['list'][] = $l_v1;
                        $res_list['more9999']['title'] = '更多';
                        $res_list['more9999']['title_id'] = 'more';
                        unset($list[$l_k1]);
                    }
                }
            }
        }
        if(!$res_list){
            if($class_type!=2){
                $res_list[]['list'] =  $list;
            }else{
                $res_list = $list;
            }
        }else{
            if($class_type!=2){
                $res_list =  array_values($res_list);
            }
        }
        return ['res_list'=>array_values($res_list),'tab_list'=>$tab_list];
    }

    /**
     * @param $type 活动类型 1-活动 2-热卖tab 3-潜客商品 4-热卖banner
     * @param $act_id
     * @param $channel
     * @return array|bool|\PDOStatement|string|\think\Model|null
     */
    private function getJumpUrl($type,$act_id=0,$channel)
    {
        $model =  new DbRecommendJumpLink();
        //跳转类型 1-商品详情 2-商品分类 3-跳转路径 4-页面路径 5-h5页面  6-商城领券页
        if($channel=='GWAPP'){
            $channel=2;
        }elseif($channel=='GWSM') {
            $channel=1;
        }else{
            $channel=0;
        }
        $where = ['activity_type'=>$type,'is_enable'=>1,'channel'=>$channel];

        if($act_id){
            $where['activity_id'] = $act_id;
        }
        $jump_info = $model->getOne(['where'=>$where,'field'=>'jump_type,path']);
        return $jump_info;


    }
}
