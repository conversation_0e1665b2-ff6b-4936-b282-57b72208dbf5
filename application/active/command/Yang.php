<?php


namespace app\active\command;


use app\common\model\db\DbCommoditySku;
use app\common\net_service\SendMailer;
use app\common\port\connectors\Pz1a;
use think\Config;
use think\console\Command;
use think\console\Input;
use think\console\Output;

class Yang extends Command
{

    protected function configure()
    {
        $this->setName('yang')->setDescription('修复满减补贴金额数据');
    }


    protected function execute(Input $input, Output $output)
    {
        Config::load(ROOT_PATH . 'config/config.php');
        Config::load(ROOT_PATH . 'config/' . config('app_status') . '.php');
        $email[] = [
            'n_product_code' => '延保产品编码：'."<br />",
            'n_product_name' => '延保名称：'."<br/>",
            'use_price' => '用户价:'."<br/>",
            'is_enable' => '可用状态:'."<br/>",
            'modified_date' => '更新时间:'."<br/>",
        ];
        $sendMailer = new SendMailer();
        $data       = [
            'type'         => 16,
            'update_no'    => '2025070313400588122',
            'service_type' => '延保服务包',
            'data'         => $email
        ];
        $sendMailer->send_mail($data);
    }
}
