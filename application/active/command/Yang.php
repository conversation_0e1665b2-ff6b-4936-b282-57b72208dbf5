<?php


namespace app\active\command;


use app\common\model\db\DbCommoditySku;
use app\common\port\connectors\Pz1a;
use think\Config;
use think\console\Command;
use think\console\Input;
use think\console\Output;

class Yang extends Command
{

    protected function configure()
    {
        $this->setName('yang')->setDescription('修复满减补贴金额数据');
    }


    protected function execute(Input $input, Output $output)
    {
        Config::load(ROOT_PATH . 'config/config.php');
        Config::load(ROOT_PATH . 'config/' . config('app_status') . '.php');
        $sku_model        = new DbCommoditySku();

        $variety_code_arr = [];
        $map   = ['a.variety_code' => ['in', $variety_code_arr], 'a.is_enable' => 1];
        $field = 'a.id as commodity_sku_id,a.sku_code,c.commodity_id,c.commodity_set_id,c.is_grouped,
                b.group_sub_commodity_id,GROUP_CONCAT(b.id) as set_sku_id';

        $list = $sku_model->alias('a')
            ->join('t_db_commodity_set_sku b', 'a.id=b.commodity_sku_id and b.is_enable=1')
            ->join('t_db_commodity_flat c', 'b.commodity_set_id=c.commodity_set_id and shelves_type=5')
            ->where($map)
            ->field($field)
            ->group('b.commodity_set_id')
            ->select();
        print_json($sku_model->getLastSql());


        $re = Pz1a::create('pz1a')->subItemCodeDetail('LQDW'); // 领取点位
        print_json($re);
    }
}
