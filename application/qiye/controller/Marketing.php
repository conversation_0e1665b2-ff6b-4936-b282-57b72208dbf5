<?php
/**
 * 商城发货
 * Created by PhpStorm.
 * User: lzx
 * Date: 2019/7/11
 * Time: 4:21 PM
 */

namespace app\qiye\controller;




use api\wechat\JsSdk;
use app\common\model\act\AcRenewalQrc;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuQyPoster;
use app\common\model\bu\BuQyUser;
use app\common\model\db\DbCard;
use app\common\model\db\DbDlr;
use app\common\model\db\DbSendCardPage;
use app\common\model\db\DbSendCardPageSub;
use app\common\model\db\DbSpecialIndex;
use app\common\port\connectors\CarLive;
use Intervention\Image\ImageManager;
use tool\Curl;
use tool\OssUploadFile;

class Marketing extends Common
{

    private $token = "4s_STORE_api";
    public function __construct()
    {
        parent::__construct();
        if(input('test')==1){
            $jssdk =  qy_js_sdk('cgj_001',true);
        }else{
            $jssdk =  qy_js_sdk('cgj_001');
        }
        header("Access-Control-Allow-Origin: *");
        $this->assign('title','发货');
        $this->assign('jssdk', $jssdk);
    }



    public function index(){
        $url =  "dongfeng-nissan.com.cn";
        $go_url = url('go_url','',true,true);
        if(input('test')==1){

        }
        $this->assign('test',input('test'));
        $this->assign('url',$url);
        $this->assign('go_url',$go_url);
        return $this->fetch('marketing/index');
    }

    public function deliverList(){
        $user = new BuUsers();
        $time = time();
        $user_id = $this->userid;
        $sig = md5($this->token.$user_id.$time);
        $user_depart = $user->getOneUserDepart(sprintf("userid='%s'",$user_id));
        $url =  "http://4s-store.chebaba.com/dealer/index/goods/deliverList";
        if($user_depart){
            if($user_depart['dlr_code']!="H2906"){
                $url =  sprintf("http://4s-store.chebaba.com/dealer_%s/index/goods/deliverList",$user_depart['dlr_code']);
            }
        }

        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'sig'=>$sig,
            'dlr_code'=>'NISSAN',
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }

    public function deliverList2(){
        $time = time();
        $user_id = $this->userid;
        $sig = md5($this->token.$user_id.$time);
        $url  =   url("index_v5/Normal/deliverList",'',false,true);
//        $url =  "http://wxstore.chebaba.com/index_v2/goods/deliverList";

        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'sig'=>$sig,
            'dlr_code'=>'NISSAN',
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }


    public function go_url(){
        $url = input('url');
        $url = urldecode($url);
        if(strpos($url,"dongfeng-nissan.com.cn")!==false){
            $time = time();
            $user_id = $this->userid;
            $sig = md5($this->token.$user_id.$time);
            $data = array(
                'time'=>$time,
                'user_id'=>$user_id,
                'sig'=>$sig,
                'dlr_code'=>'NISSAN',
            );
            $html = http_build_query($data);
            $go_url = $url."&".$html;
            $this->redirect($go_url);
            die();
        }else{
            $this->redirect($url);
        }
    }

    public function renewal_url(){
        $user = new BuUsers();
        $time = time();
        $user_id = $this->userid;
        $sig = md5($this->token.$user_id.$time);
        $user_depart = $user->getOneUserDepart(sprintf("userid='%s'",$user_id));
        $url =  "http://4s-store.chebaba.com/dealer/active/renewal/qrc";
//        $url =  "http://4s-store.chebaba.com/dev_dealer/active/renewal/qrc";
        if($user_depart){
            if($user_depart['dlr_code']!="H2906"){
                $url =  sprintf("http://4s-store.chebaba.com/dealer_%s/active/renewal/qrc",$user_depart['dlr_code']);
//                $url =  "http://4s-store.chebaba.com/dev_dealer/active/renewal/qrc";
            }
        }

        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'sig'=>$sig,
            'dlr_code'=>'NISSAN',
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }

    public function renewal_url2(){
        $time = time();
        $user_id = $this->userid;
        $sig = md5($this->token.$user_id.$time);
        $url =  "http://wxstore.dongfeng-nissan.com.cn/active/renewal/qrc";
        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'sig'=>$sig,
            'dlr_code'=>'H2902',
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }

    // 发券页不替换链接，旧方法改名
    public function marketing_card_url_two(){
        $userid = $this->userid;

        $send_card_model =  new DbSendCardPage();
        $qy_model = new BuQyUser();
        $user = $qy_model->getOne(['where'=>['user_id'=>$userid]]);
        if(!$user){
            die('员工不存在');
        }
        $time=time();
        $sql = sprintf("SELECT a.title,a.`thumb`,a.`start_time`,a.`end_time`,a.`id`,a.`send_type` 
FROM t_db_send_card_page a 
inner join t_bu_qy_user b on FIND_IN_SET(b.user_id, a.staff_list) and b.is_enable=1
inner join t_db_dlr c on b.dlr_code=c.dlr_code and FIND_IN_SET(c.dlr_code, a.dlr_code) and c.is_enable=1
WHERE  (a.is_enable=1 and b.user_id='%s') 
and a.set_type in(1,2)
and (a.start_time<='%s' || a.start_time=0) 
and (a.end_time>='%s' || a.end_time=0)
union all
SELECT a.title,a.`thumb`,a.`start_time`,a.`end_time`,a.`id`,a.`send_type` 
FROM t_db_send_card_page a 
inner join t_bu_qy_user b on FIND_IN_SET(b.user_id, a.staff_list) and b.is_enable=1
inner join t_db_dlr c on b.dlr_code=c.dlr_code and FIND_IN_SET(c.dlr_code, a.dlr_code) and c.is_enable=1
inner join t_db_dlr d on c.pid=d.id and d.dlr_code=a.creator and d.is_enable=1
WHERE  (a.is_enable=1 and b.user_id='%s') 
and a.set_type in(3)
and (a.start_time<='%s' || a.start_time=0) 
and (a.end_time>='%s' || a.end_time=0)",$userid,$time,$time,$userid,$time,$time);
        $list = $send_card_model->query($sql);
//        echo $send_card_model->getLastSql();
        if($list){
            foreach ($list as $k=>$v){
                $b_tn_01='';
                $b_tn_02='';
                if($v['send_type']==2){
                    $b_tn_01= sprintf("<button class='btn red' data-action='%s'>发券</button>",url('marketing_card_url_one',['id'=>$v['id'],'user_id'=>$userid,'send_type'=>$v['send_type']]));
                    $b_tn_02 ='';
                }else{
                    $b_tn_01= sprintf("<button class='btn red' data-action='%s'>发券</button>",url('share_page',['id'=>$v['id'],'user_id'=>$userid,'send_type'=>$v['send_type'],'is_qrc'=>1]));
                    $b_tn_02= sprintf("<button class='btn blue' data-action='%s'>分享</button>",url('share_page',['id'=>$v['id'],'user_id'=>$userid,'send_type'=>$v['send_type']]));
                }
                $list[$k]['button'] = $b_tn_01.' '.$b_tn_02;
                if($v['start_time']!=0){
                    $list[$k]['time'] = "有效期: ".date('Y.m.d',$v['start_time']).'-'.date('Y.m.d',$v['end_time']);
                }else{
                    $list[$k]['time'] = '';
                }

            }
        }
        $this->assign('spe_url',url('special_url',['user_id'=>$userid]));
        $this->assign('list',$list);
        $jsSdk = JsSdk::get('NISSAN');
        $this->assign('jsSdk', $jsSdk);
        $this->assign('title', '发券');
        return $this->fetch('marketing_card/list');

//        $time = time();
//        $user_id = $this->userid;
//        if(!$user_id){
//            die('请从企业微信进入');
//        }
//        $sig = md5($this->token.$user_id.$time);
//        $url =  "http://wxstore.chebaba.com/active/marketing_card/m_list";
//        $data = array(
//            'time'=>$time,
//            'user_id'=>$user_id,
//            'sig'=>$sig,
//            'dlr_code'=>'NISSAN',
//        );
//        $html = http_build_query($data);
//        $go_url = $url."?".$html;
//        $this->redirect($go_url);
    }

    public function special_url(){
        $userid = $this->userid;

        $send_card_model = new DbSpecialIndex();
        $qy_model = new BuQyUser();
        $user = $qy_model->getOne(['where' => ['user_id' => $userid]]);
        if (!$user) {
            die('员工不存在');
        }
        $time = time();
//        $where = sprintf(" is_enable=1 and ( FIND_IN_SET('%s', staff_list)) and (start_time<='%s' || start_time=0) and (end_time>='%s' || end_time=0)", $userid,$time,$time);
        $sql = sprintf("SELECT a.title,a.`thumb`,a.`start_time`,a.`end_time`,a.`id`
FROM t_db_special_index a 
inner join t_bu_qy_user b on FIND_IN_SET(b.user_id, a.staff_list) and b.is_enable=1
inner join t_db_dlr c on b.dlr_code=c.dlr_code and FIND_IN_SET(c.dlr_code, a.dlr_code) and c.is_enable=1
WHERE  (a.is_enable=1 and b.user_id='%s') 
and a.set_type in(1,2)
and (a.start_time<='%s' || a.start_time=0) 
and (a.end_time>='%s' || a.end_time=0)
union all
SELECT a.title,a.`thumb`,a.`start_time`,a.`end_time`,a.`id`
FROM t_db_special_index a 
inner join t_bu_qy_user b on FIND_IN_SET(b.user_id, a.staff_list) and b.is_enable=1
inner join t_db_dlr c on b.dlr_code=c.dlr_code and FIND_IN_SET(c.dlr_code, a.dlr_code) and c.is_enable=1
inner join t_db_dlr d on c.pid=d.id and d.dlr_code=a.creator and d.is_enable=1
WHERE  (a.is_enable=1 and b.user_id='%s') 
and a.set_type in(3)
and (a.start_time<='%s' || a.start_time=0) 
and (a.end_time>='%s' || a.end_time=0)", $userid, $time, $time, $userid, $time, $time);
        $list = $send_card_model->query($sql);
        if ($list) {
            foreach ($list as $k => $v) {
                if ($v['start_time'] != 0) {
                    $list[$k]['time'] = "有效期: " . date('Y.m.d', $v['start_time']) . ' - ' . date('Y.m.d', $v['end_time']);
                } else {
                    $list[$k]['time'] = '';
                }
                $list[$k]['url'] = url('cards', ['id' => $v['id'], 'user_id' => $userid]);
            }
        }
        $this->assign('card_url', url('marketing_card_url', ['user_id' => $userid]));
        $this->assign('list', $list);
        $jsSdk = JsSdk::get('NISSAN');
        $this->assign('jsSdk', $jsSdk);
        $this->assign('title', '专题');
        return $this->fetch('marketing_card/special_list');
    }


    public function share_page(){
        $id = input('id');
        $send_type = input('send_type');
        $is_qrc = input('is_qrc',0);
        $userid = input('user_id');
        $send_card_model =  new DbSendCardPage();
        $send_card = $send_card_model->getOneByPk($id);
        if($send_card['send_type']==2){
            $this->redirect(url('marketing_card_url_one',input('get.')));
        }
        $qy_model = new BuQyUser();
        $user = $qy_model->getOne(['where'=>['user_id'=>$userid]]);

        if(!$user){
            die('员工不存在');
        }
        if(!$send_card){
            die('非法入口');
        }
        $dlr_code= $user['dlr_code'];
        $qrc_model= new AcRenewalQrc();
        $where = ['user_id'=>$userid,'card_page_id'=>$id];
        $qrc = $qrc_model->getOne(['where'=>$where]);
        if(!$qrc){
            $data = array(
                'user_id'=>$userid,
                'number'=>$send_card['card_num'],
                'dlr_code'=>$dlr_code,
                'card_page_id'=>$id
            );

            $r_id = $qrc_model->insertGetId($data);
        }else{
            $r_id = $qrc['id'];
        }
        $com_url = url('Active/MarketingCard/cards', ['scene_id' => $r_id,'dlr_code'=>$dlr_code,'send_type'=>$send_type,'card_page_id'=>$id], true, true);
        if($is_qrc){
            $file_name = 'qc_code_image/card_page-' . $userid .'-'.date('Y-m-d'). $r_id . '.png';
            \tool\QrCode::png($com_url, config('upload.path') . $file_name, 'L', 10);
            $qrc_img = config('upload.url') . $file_name;
            $aliyun_oss=new OssUploadFile();
            $os_res    =$aliyun_oss::upload($file_name);
            if($qrc_img){
                if(isset($os_res['url'])){
                    $jsSdk = JsSdk::get('NISSAN');
                    $this->assign('jsSdk', $jsSdk);
                    $this->assign('qrc_url',$os_res['url']);
                    $this->assign('title', '发券');
                    return $this->fetch('marketing_card/share_page');
                }else{
                    die('页面出错，请刷新');
                }
            }else{
                die('页面出错，请刷新');
            }
        }else{
            $this->redirect($com_url);
        }
    }

    public function marketing_card_url_one(){
        $id = input('id');
        $number = input('number');
        $send_type = input('send_type');
        $userid = $this->userid;
        $send_card_model =  new DbSendCardPage();
        $send_card = $send_card_model->getOneByPk($id);
        $qy_model = new BuQyUser();
        $user = $qy_model->getOne(['where'=>['user_id'=>$userid]]);
        if(!$user){
            die('员工不存在');
        }
        if(!$send_card){
            die('非法入口');
        }
        $dlr_code= $user['dlr_code'];
        if(request()->isAjax()){
            $data = array(
                'user_id'=>$userid,
                'number'=>$number,
                'dlr_code'=>$dlr_code,
                'card_page_id'=>$id
            );
            $qrc_model= new AcRenewalQrc();
            $r_id = $qrc_model->insertGetId($data);
            if($r_id){
                $com_url = url('Active/MarketingCard/cards', ['scene_id' => $r_id,'dlr_code'=>$dlr_code,'send_type'=>$send_type,'card_page_id'=>$id], true, true);
                $file_name = 'qc_code_image/card_page-' . $userid .'-'.date('Y-m-d'). $r_id . '.png';
                \tool\QrCode::png($com_url, config('upload.path') . $file_name, 'L', 10);
                $qrc_img = config('upload.url') . $file_name;
                $aliyun_oss=new OssUploadFile();
                $os_res    =$aliyun_oss::upload($file_name);
                if($qrc_img){
                    if(isset($os_res['url'])){
                        print_json(0,'ok',$os_res['url']);
                    }else{
                        print_json(1,'失败,请刷新');
                    }
                }else{
                    print_json(1,'失败,请刷新');
                }
            }else{
                print_json(1,'fail');
            }
        }else{
            $jsSdk = JsSdk::get('NISSAN');
            $this->assign('jsSdk', $jsSdk);
            $this->assign('send_card',$send_card);
            $this->assign('user_id',$userid);
            $this->assign('url',request()->url(true));
            $this->assign('dlr_code',$dlr_code);
            $this->assign('id',$id);
            $this->assign('title', '发券');
            return $this->fetch('marketing_card/qrc');
        }
    }



    public function marketing_card_url_one_old(){
        $time = time();
        $user_id = $this->userid;
        if(!$user_id){
            die('请从企业微信进入');
        }
        $sig = md5($this->token.$user_id.$time);
        $card_page_id = input('id');
        $send_type = input('send_type');
        $url =  "http://wxstore.dongfeng-nissan.com.cn/active/marketing_card/qrc_page";
        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'sig'=>$sig,
            'dlr_code'=>'NISSAN',
            'id'=>$card_page_id,
            'send_type'=>$send_type,
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }

    public function dlr_draw_qrc(){
        $time = time();
        $user_id = $this->userid;
        $sig = md5($this->token.$user_id.$time);
        $url =  "http://wxstore.dongfeng-nissan.com.cn/index_v2/Normal/dlr_draw_qrc";

        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'sig'=>$sig,
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }
    //回厂信息
    public function chat_url(){
        $time = time();
        $user_id = $this->userid;
        $sig = md5($this->token.$user_id.$time);
        $url =  "http://wxstore.dongfeng-nissan.com.cn/index_v2/Chat/chatList";

        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'sig'=>$sig,
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }

    //新车礼包跳转
    public function new_car_bag_url(){

        $time = time();
        $user_id = $this->userid;
        $sig = md5($this->token.$user_id.$time);
        $url =  "http://wxstore.dongfeng-nissan.com.cn/active/new_car_bag/bag_order_list";

        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'dlr_code'=>'NISSAN',
            'sig'=>$sig,
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }
    //一人一码
    public function user_qrc_on_line(){
        $time = time();
        $user_id = $this->userid;
        $sig = md5($this->token.$user_id.$time);
        $url =  "http://wxstore.dongfeng-nissan.com.cn/index_v2/Normal/user_qrc_on_line";
        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'sig'=>$sig,
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }

    //核销卡券
    public function c_card(){
        $time = time();
        $user_id = $this->userid;
        $sig = md5($this->token.$user_id.$time);
        $url =  "http://wxstore.dongfeng-nissan.com.cn/index_v2/Normal/c_card";
        $data = array(
            'time'=>$time,
            'user_id'=>$user_id,
            'sig'=>$sig,
        );
        $html = http_build_query($data);
        $go_url = $url."?".$html;
        $this->redirect($go_url);
    }

    // 新发券页
    public function marketing_card_url(){
        $userid = $this->userid;

        $send_card_model =  new DbSendCardPage();
        $qy_model = new BuQyUser();
        $user = $qy_model->getOne(['where'=>['user_id'=>$userid]]);
        if(!$user){
            die('员工不存在');
        }
        $time=time();
        $where = [
            'is_enable' => 1,
            'start_time' => ['<=', $time],
            'end_time' => ['>', $time],
            ['exp', "find_in_set('{$user['dlr_code']}', dlr_code)"],
        ];
        $params = [
            'where' => $where,
            'order' => 'created_date DESC'
        ];
        $list = $send_card_model->getList($params);
        if($list){
            foreach ($list as $k=>$v){
                $b_tn_01='';
                $b_tn_02='';
                if($v['send_type']==2){
                    $b_tn_01= sprintf("<button class='btn red' data-action='%s'>发券</button>",url('marketing_card_url_one',['id'=>$v['id'],'user_id'=>$userid,'send_type'=>$v['send_type']]));
                    $b_tn_02 ='';
                }elseif($v['send_type']==3){
                    $b_tn_01= sprintf("<button class='btn red' data-action='%s'>发券</button>",url('marketing_card_set',['id'=>$v['id'],'user_id'=>$userid,'send_type'=>$v['send_type'],'is_qrc'=>1]));
                    $b_tn_02 ='';
                }else{
                    $b_tn_01= sprintf("<button class='btn red' data-action='%s'>发券</button>",url('share_page',['id'=>$v['id'],'user_id'=>$userid,'send_type'=>$v['send_type'],'is_qrc'=>1]));
                    $b_tn_02= sprintf("<button class='btn blue' data-action='%s'>分享</button>",url('share_page',['id'=>$v['id'],'user_id'=>$userid,'send_type'=>$v['send_type']]));
                }
                $list[$k]['button'] = $b_tn_01.' '.$b_tn_02;
                if($v['start_time']!=0){
                    $list[$k]['time'] = "有效期: ".date('Y.m.d',$v['start_time']).'-'.date('Y.m.d',$v['end_time']);
                }else{
                    $list[$k]['time'] = '';
                }

            }
        }
        $this->assign('spe_url',url('special_url',['user_id'=>$userid]));
        $this->assign('list',$list);
        $jsSdk = JsSdk::get('NISSAN');
        $this->assign('jsSdk', $jsSdk);
        $this->assign('title', '发券');
        return $this->fetch('marketing_card/list');
    }

    // 设置优惠券页
    public function marketing_card_set(){
        $id = input('id');
        $userid = $this->userid;
        $send_card_model =  new DbSendCardPage();
        $send_card_sub_model =  new DbSendCardPageSub();
        $qy_model = new BuQyUser();
        $card_model = new DbCard();
        $dlr_model = new DbDlr();

        $send_card = $send_card_model->getOneByPk($id);
        $user = $qy_model->getOne(['where'=>['user_id'=>$userid]]);
        if(!$user){
            die('员工不存在');
        }
        $dlr_code_arr = explode(',',$send_card['dlr_code']);
        if(!$send_card || !in_array($user['dlr_code'],$dlr_code_arr)){
            die('非法入口');
        }

        // 发券页详细信息
        $send_card_sub_list = $send_card_sub_model->getList(['where'=>['send_card_page_id'=>$send_card['id']]]);
        $send_card_sub = [];
        foreach ($send_card_sub_list as $vl){
            switch ($vl['module']){
                case 1:
                    $send_card_sub['sub_wenan1'] = $vl['sub_wenan1'];
                    $send_card_sub['sub_wenan2'] = $vl['sub_wenan2'];
                    $send_card_sub['sub_wenan3'] = $vl['sub_wenan3'];
                    $send_card_sub['pic'] = $vl['pic'];
                    break;
                case 2:
                    $send_card_sub['main_title'] = $vl['main_title'];
                    $send_card_sub['sub_title'] = $vl['sub_title'];
                    break;
                case 3:
                    $send_card_sub['card_list'] = json_decode($vl['card_list'], true);
                    break;
                case 4:
                    $send_card_sub['rule'] = $vl['rule'];
                    break;
            }
        }
        // 优惠券列表
        $time = date('Y-m-d');
        $where = [
            'id' => ['in', $send_card['card_list']],
            ['exp', "find_in_set('{$user['dlr_code']}',apply_dlr_code)"],
            'act_status' => ['in', [1, 2]],
            'is_enable' => 1,
            'receive_start_date'=>['<=', $time],
            'receive_end_date'=>['>=', $time],
        ];
        $field = 'id,card_name,card_type,apply_dlr_code,card_quota,card_discount,available_count,use_des,apply_des,date_type,
        validity_date_start,validity_date_end,least_cost,fixed_begin_term,fixed_term';
        $card_list = $card_model->getList(['where'=>$where,'field'=>$field]);
        $card_ids = explode(',', $send_card['card_list']);
        $return_data = [];
        foreach ($card_list as $k => $v){

            if ($v['date_type'] == 1 && $v['validity_date_end'] < $time){
                unset($card_list[$k]);
                continue;
            }
            if ($v['least_cost'] > 0) {
                $card_list[$k]['word'] = sprintf("满%s使用", $v['least_cost']);
            } else {
                $card_list[$k]['word'] = sprintf("限购部分商品");
            }
            $validity_date = '';
            if ($v['date_type'] == 2){
                if ($v['fixed_begin_term'] > 0){
                    $validity_date = '自领取后' . $v['fixed_begin_term'] . '天开始生效，';
                }
                $validity_date .= '有效期' . $v['fixed_term'] . '天';
            }else{
                $validity_date =  $v['validity_date_start'] . '~' . $v['validity_date_end'];
            }
            $v['validity_date'] = $validity_date;
            $card_list[$k]['can_num'] = $send_card_sub['card_list'][$v['id']];
            $card_list[$k]['id'] = (string)$v['id'];
            $card_list[$k]['all_num'] = 1;
            $return_data[array_search($v['id'],$card_ids)] = $card_list[$k];
        }

        $dlr = $dlr_model->getOne(['where'=>['dlr_code'=>$user['dlr_code']],'field'=>'dlr_name,base_address,base_service_tel']);
        $this->assign('dlr',$dlr);
        $this->assign('user',$user);
        $this->assign('send_card',$send_card);
        $this->assign('send_card_sub',$send_card_sub);
        $this->assign('card_list',json_encode($card_list));
        return $this->fetch('marketing_card');
    }

    // 生成海报
    public function poster(){
        $send_card_model =  new DbSendCardPage();
        $qy_model = new BuQyUser();
        $qy_poster_model = new BuQyPoster();

        $userid = $this->userid;
        $id = input('id');
        $send_card = $send_card_model->getOneByPk($id);
        $user = $qy_model->getOne(['where'=>['user_id'=>$userid]]);
        $dlr_code_arr = explode(',',$send_card['dlr_code']);
        if(!$send_card || !in_array($user['dlr_code'],$dlr_code_arr)){
            die('非法入口');
        }
        $data = input('poster/a',[]);
        $card_list = [];
        foreach ($data as $v){
            $card_list[$v['id']] = $v['all_num'];
        }

        $data = [
            'user_id'           => $userid,
            'send_card_page_id' => $id,
            'card_list'         => json_encode($card_list),
            'img'               => '',
            'dlr_code'          => $user['dlr_code'],
            'is_enable'         => 1,
            'creator'           => $user['name'],
            'modifier'          => $user['name'],
        ];
        $poster_id = $qy_poster_model->insertGetId($data);
        if ($poster_id){
            $qr_params = [
                'param'     => json_encode(['posterId' => $poster_id, 'dlrCode' => $user['dlr_code']]),
                'code_type' => 6,
                'width'     => 280,
                'page'      => 'package_mall/pages/draw_coupon/index',
            ];
            $group_qrc = CarLive::create('car_live')->getSmallCodeImg($qr_params);

            if ($group_qrc['code'] == 10000) {
                $ch = curl_init();
                curl_setopt ($ch, CURLOPT_URL, $group_qrc['data']['qrcode']);
                curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt ($ch, CURLOPT_CONNECTTIMEOUT,10);
                $data = curl_exec($ch);
                curl_close($ch);
                $filename = "public/uploads/qc_code_image/qr_code_{$poster_id}.jpg";//文件名称生成
                $fp2 = @fopen(ROOT_PATH.$filename, "a");
                @fwrite($fp2, $data);//向当前目录写入图片文件，并重新命名
                @fclose($fp2);

                $path = 'public/uploads/qc_code_image/qr_code_'.$poster_id . '_95.jpg';

                $font_path = ROOT_PATH.'public/static/1.ttf';

                $image = new ImageManager();
                $image->make($filename)
                    ->resize(95, 95)
                    ->save($path);
                $post_img = 'public/uploads/qc_code_image/poster_'.$poster_id . '.jpg';
                $dlr_model = new DbDlr();
                $dlr = $dlr_model->getOne(['where'=>['dlr_code'=>$user['dlr_code']]]);
                $address_len = mb_strlen($dlr['base_address']);
                if ($address_len > 18){
                    $add1 = mb_substr($dlr['base_address'],0,18);
                    $add2 = mb_substr($dlr['base_address'],18,$address_len);
                    $y1 = 405;
                    $y2 = 425;
                }else{
                    $add1 = $dlr['base_address'];
                    $add2 = '';
                    $y1 = 415;
                    $y2 = 440;
                }
                $image->make('public/static/qiye/images/qi_coupon/bg.png')
                    ->resize(320, 490)
                    ->insert($path,'', 210, 350)
                    ->text('长按识别领取优惠券',210,465, function ($font) use ($font_path){
                        $font->file($font_path);
                        $font->size('11');
                        $font->color('#999999');
                    })
                    ->text($dlr['base_service_tel'],10,465, function ($font) use ($font_path){
                        $font->file($font_path);
                        $font->size(12);
                        $font->color('#999999');
                    })
                    ->text($dlr['dlr_name'],10,$y1, function ($font) use ($font_path){
                        $font->file($font_path);
                        $font->size(12);
                        $font->color('#999999');
                    })
                    ->text($add1,10,$y2, function ($font) use ($font_path){
                        $font->file($font_path);
                        $font->size(12);
                        $font->color('#999999');
                    })
                    ->text($add2,10,440, function ($font) use ($font_path){
                        $font->file($font_path);
                        $font->size(12);
                        $font->color('#999999');
                    })
                    ->text($send_card['poster_title'],10,355, function ($font) use ($font_path){
                        $font->file($font_path);
                        $font->size(20);
                        $font->color('#333333');
                    })
                    ->text($send_card['give_copywriting'],59.4,315, function ($font) use ($font_path){
                        $font->file($font_path);
                        $font->size(11);
                        $font->color('#A86748');
                    })
                    ->text($user['name'],10,315, function ($font) use ($font_path){
                        $font->file($font_path);
                        $font->size(16);
                        $font->color('#333333');
                    })
                    ->save($post_img);

                //上传阿里云
                $file_name =config('upload.url').'qc_code_image/poster_'.$poster_id . '.jpg';
                $aliyun_oss=new OssUploadFile();
                $os_res    =$aliyun_oss::upload($file_name);
                $qy_poster_model->save(['img' => $os_res['url']], ['id'=>$poster_id]);
                die(json_encode(['error'=>0,'poster_id'=>$poster_id,'dlr_code'=>$user['dlr_code'],'qrcode'=>$os_res['url']]));
            }else{
                $qy_poster_model->where(['id'=>$poster_id])->delete();
            }
        }
        die(json_encode(['error'=>1,'msg'=>'生成海报失败']));
    }

    // 发券统计列表
    public function couponStatistics(){
        $qy_poster_model = new BuQyPoster();
        $userid = $this->userid;
        $poster_list = $qy_poster_model->getList(['where'=>['user_id'=>$userid],'order'=>'created_date DESC']);

        $this->assign('poster_list',json_encode($poster_list));
        return $this->fetch('statistics');
    }

    // 发券统计详情
    public function couponStatisticsMsg(){
        $qy_model = new BuQyUser();
        $userid = $this->userid;
        $user = $qy_model->getOne(['where'=>['user_id'=>$userid]]);

        $qy_poster_model = new BuQyPoster();
        $card_model = new DbCard();
        $card_receive_model =  new BuCardReceiveRecord();
        $poster_id = input('poster_id',0);
        $poster = $qy_poster_model->getOne(['where'=>['id'=>$poster_id]]);

        $card_msg = json_decode($poster['card_list'], true);
        $card_ids = array_keys($card_msg);

        // 优惠券列表
        $return_data = [];
        $card_list = $card_model->getList(['where'=>['id' => ['in', $card_ids]],'field'=>'id,card_name,available_count,use_des,apply_des,date_type,validity_date_start,validity_date_end,least_cost,fixed_term,fixed_begin_term']);
        foreach ($card_list as $k => $v){
            if ($v['least_cost'] > 0) {
                $v['word'] = sprintf("满%s使用", $v['least_cost']);
            } else {
                $v['word'] = sprintf("限购部分商品");
            }
            $validity_date = '';
            if ($v['date_type'] == 2){
                if ($v['fixed_begin_term'] > 0){
                    $validity_date = '自领取后' . $v['fixed_begin_term'] . '天开始生效，';
                }
                $validity_date .= '有效期' . $v['fixed_term'] . '天';
            }else{
                $validity_date =  $v['validity_date_start'] . '~' . $v['validity_date_end'];
            }
            $v['validity_date'] = $validity_date;
            $v['get_count']     = $card_receive_model->where(['poster_id'=>$poster_id,'card_id'=>$v['id']])->count();
            $v['use_count']     = $card_receive_model->where(['status'=>3,'poster_id'=>$poster_id,'card_id'=>$v['id']])->count();
            $v['all_count']     = $card_msg[$v['id']];
            $return_data[array_search($v['id'],$card_ids)] = $v;
        }

        $this->assign('card_list',json_encode($return_data));
        $this->assign('dlr_name',$user['dlr_name']);
        return $this->fetch('statistics_info');
    }
}