<?php

namespace app\api\service;

use app\common\model\e3s\E3sDelayInsurance;
use app\common\model\e3s\E3sDelayInsuranceLog;
use app\common\model\e3s\E3sSparePart;
use app\common\model\e3s\E3sSparePartUpdate;
use app\common\net_service\SendMailer;
use think\Db;
use think\Exception;
use tool\Logger;

class E3sService
{

    /**
     * 保存
     * @param $input
     * @return int
     */
    public function save($input)
    {
        $start_time = microtime(true);
        $update_no  = date("YmdHis", time()) . rand(10000, 99999);
        $add_num    = 0;
        $update_num = 0;
        $delete_num = 0;
        $email      = [];
        try {
            Db::startTrans();
            $e3s_insurance_model = new E3sDelayInsurance();
            $spare_part          = new E3sSparePart();

            $add     = [];
            $add_log = [];

            foreach ($input as $key => $datum) {
                $map       = [
                    'car_series_code' => $datum['carSeriesCode'],
                    'n_product_code'  => $datum['guaranteeProductId'],
                ];
                $info      = $e3s_insurance_model->where($map)->find();
                $data      = $this->handleInsuranceData($datum, $update_no);
                $add_log[] = $data;
                if (!empty($info)) {
                    // 更新
                    if ($datum['isEnable'] == 1) {
                        $update_num = $update_num + 1;
                    } else {

                        $delete_num = $delete_num + 1;
                    }
                    $time                  = date('Y-m-d H:i:s');
                    $data['modified_date'] = $time;
                    $e3s_insurance_model->where('id', $info['id'])->update($data);


                    // 判断是否有变更
                    $is_change = 0;
                    if (($info['use_price'] != $data['use_price']) || ($info['is_enable'] != $data['is_enable'])) {
                        $is_change = 1;
                    }

                    $is_goods = $spare_part->is_goods($datum['nproductCode']);

                    if (!empty($is_goods) && $is_change) {
                        $email[$key]['n_product_code'] = "延保产品编码：" . $data['n_product_code'] . "<br />";
                        $email[$key]['n_product_name'] = "延保名称：" . $data['n_product_name'] . "<br />";
                        if ($info['use_price'] != $data['use_price']) {
                            $email[$key]['use_price'] = "用户价：" . $data['use_price'] . "(变更前：" . $info['use_price'] . ")<br />";
                        } else {
                            $email[$key]['use_price'] = "用户价：" . $data['use_price'] . "<br />";
                        }
                        $is_enable = $data['is_enable'] == 1 ? '正常' : '停用';
                        if ($info['is_enable'] != $data['is_enable']) {
                            $first_is_enable          = $info['is_enable'] == 1 ? '正常' : '停用';
                            $email[$key]['is_enable'] = "可用状态：" . $is_enable . "(变更前：" . $first_is_enable . ")<br />";
                        } else {
                            $email[$key]['is_enable'] = "可用状态：" . $is_enable . "<br />";
                        }
                        $email[$key]['modified_date'] = "更新时间：" . $time . "<br />";
                    }

                } else {
                    $add_num = $add_num + 1;
                    $add[]   = $data;
                }

            }

            if (!empty($add)) {
                $add_data_list = array_chunk($add, 100);
                foreach ($add_data_list as $key => $add_data) {
                    $e3s_insurance_model->insertAll($add_data);
                }
            }
            if (!empty($add_log)) {
                $e3s_insurance_log_model = new E3sDelayInsuranceLog();
                $add_log_data_list       = array_chunk($add_log, 100);
                foreach ($add_log_data_list as $add_log_data) {
                    $e3s_insurance_log_model->insertAll($add_log_data);
                }
            }

            Db::commit();
            $update_type = 1;
            $msg         = 'success';
        } catch (Exception $e) {
            Db::rollback();
            $msg = $e->getMessage();
            Logger::error('insurance-save:', $msg);
            $update_type = 0;
        }
        $end_time = microtime(true);

        $part_update_model = new E3sSparePartUpdate();
        $part_update_data  = [
            'service_type'    => '延保产品',
            'update_no'       => $update_no,
            'update_duration' => $end_time - $start_time,
            'new_add'         => $add_num,
            'new_update'      => $update_num,
            'new_del'         => $delete_num,
            'update_type'     => $update_type,
            'modifier'        => $msg,
        ];
        $part_update_model->insertGetId($part_update_data);

        $new_email = array_values($email);
        if (!empty($new_email)) {
            $sendMailer = new SendMailer();
            $data       = [
                'type'         => 16,
                'update_no'    => $update_no,
                'service_type' => '延保服务包',
                'data'         => $new_email
            ];
            $sendMailer->send_mail($data);
        }
        return $update_type;
    }


    /**
     * 处理数据
     * @param $datum
     * @param $update_no
     * @return mixed
     */
    private function handleInsuranceData($datum, $update_no)
    {
        return [
            'car_series_code'             => $datum['carSeriesCode'],
            'car_series_name'             => $datum['carSeriesName'],
            'n_car_type'                  => $datum['ncarType'],
            'n_car_type_remark'           => $datum['ncarTypeRemark'],
            'product_id'                  => $datum['nproductCode'], // id 存code
            'n_product_code'              => $datum['guaranteeProductId'], // code 存id
            'n_product_name'              => $datum['nproductName'],
            'guarantee_month_num'         => $datum['guaranteeMonthNum'],
            'n_guarantee_condition'       => $datum['nguaranteeCondition'],
            'n_guarantee_content'         => $datum['nguaranteeContent'],
            'dlr_price'                   => $datum['dlrPrice'],
            'use_price'                   => $datum['userPrice'],
            'pro_guarantee_type_code'     => $datum['proGuaranteeTypeCode'],
            'pro_guarantee_pro_type_name' => $datum['proGuaranteeProTypeName'],
            'service_kind_code'           => $datum['serviceKindCode'],
            'service_kind_name'           => $datum['serviceKindCn'],
            'part_no'                     => $datum['partNo'],
            'start_month_qty'             => $datum['startMonthQty'],
            'end_month_qty'               => $datum['endMonthQty'],
            'start_mile_qty'              => $datum['startMileQty'],
            'end_mile_qty'                => $datum['endMileQty'],
            'car_brand_code'              => $datum['carBrandCode'],
            'car_brand_cn'                => $datum['carBrandCn'],
            'e3s_update_no'               => $update_no,
            'is_enable'                   => $datum['isEnable'],
        ];
    }
}