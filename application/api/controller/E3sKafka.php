<?php
namespace app\api\controller;

use app\api\service\E3sService;
use app\common\model\e3s\E3sLog;
use app\common\model\e3s\E3sMaintenanceProductCarSeries;
use app\common\model\e3s\E3sPz1aMaintenanceCarSeries;
use app\common\model\e3s\E3sPz1aMaintenanceCarSeriesLog;
use app\common\model\e3s\E3sPz1aMaintenancePackage;
use app\common\model\e3s\E3sPz1aMaintenancePackageLog;
use app\common\model\e3s\E3sPz1aMaintenancePart;
use app\common\model\e3s\E3sPz1aMaintenancePartLog;
use app\common\model\e3s\E3sPz1aMaintenanceWi;
use app\common\model\e3s\E3sPz1aMaintenanceWiLog;
use app\common\model\e3s\E3sSpecificRelation;
use app\common\model\e3s\E3sSpecificRelationDlr;
use app\common\model\e3s\E3sSpecificRelationPart;
use app\common\model\e3s_log\E3sSpecificRelationDlrLog;
use app\common\model\e3s_log\E3sSpecificRelationLog;
use app\common\model\e3s_log\E3sSpecificRelationPartLog;
use app\common\net_service\SendMailer;
use ForkModules\Traits\ResponseTrait;
use think\Controller;
use think\Exception;
use think\Hook;
use think\Queue;
use tool\Logger;
use app\common\model\e3s\E3sPartCarSeries;
use app\common\model\e3s\E3sSparePartUpdate as E3sUpdateModel;
use app\common\model\e3s\E3sPackage as E3sPackageModel;
use app\common\model\e3s\E3sPackagePart as E3sPackagePartModel;
use app\common\model\e3s\E3sMaintenancePackageWi;
use app\common\model\e3s\E3sPackageRuleItem;
use app\common\model\e3s\E3sSparePart;
use app\common\model\e3s\E3sMaintenanceProduct;
use app\common\model\e3s\E3sCarSeries;
use app\common\model\e3s_log\E3sMaintenanceProductCarSeriesLog;
use app\common\model\e3s_log\E3sMaintenanceProductLog;
use app\common\model\e3s_log\E3sPackageLog;
use app\common\model\e3s_log\E3sPackagePartLog;
use app\common\model\e3s_log\E3sPackageRuleItemLog;
use app\common\model\e3s_log\E3sPartCarSeriesLog;
use app\common\model\e3s_log\E3sSparePartLog;
use app\common\model\e3s_log\E3sCarSeriesLog;
use app\common\model\e3s_log\E3sMaintenancePackageWiLog;


class E3sKafka extends Controller
{
    use ResponseTrait;

    private $e3s_update_model;
    private $spare_part;
    private $e3s_specific_relation_model;
    private $e3s_specific_relation_log_model;
    private $e3s_specific_dlr_model;
    private $e3s_specific_dlr_log_model;
    private $e3s_specific_part_model;
    private $e3s_specific_part_log_model;
//    private $factory = "e3s";

//    private $secret = "8YtvpmFW0SsMqBTjxKPGOCDIR";

    public function __construct()
    {
        parent::__construct();
        $this->e3s_update_model = new E3sUpdateModel();
        $this->spare_part = new E3sSparePart();

        $this->e3s_specific_relation_model = new E3sSpecificRelation();
        $this->e3s_specific_relation_log_model = new E3sSpecificRelationLog();
        $this->e3s_specific_dlr_model = new E3sSpecificRelationDlr();
        $this->e3s_specific_dlr_log_model = new E3sSpecificRelationDlrLog();
        $this->e3s_specific_part_model = new E3sSpecificRelationPart();
        $this->e3s_specific_part_log_model = new E3sSpecificRelationPartLog();
    }

    //记录日志
    private function e3s_log($type, $is_success, $receive_note = [])
    {
        $log = new E3sLog();
        $log->insertData([
            'msg_type' => $type,
            'is_success' => $is_success,
            'receive_note' => json_encode($receive_note)
        ]);
        return true;
    }

    //更新到e3s更新表
    private function e3s_update($data)
    {
        try {
            $this->e3s_update_model->insertData($data);
        } catch (Exception $exception) {
            $this->e3s_log('e3s_spare_part_update', 'error', [
                'data' => json_encode($data),
                'th'=>"e3s_update",
                'e' => $exception->getMessage()
            ]);
        }
        return true;
    }

    /**
     * @param $status string    必须    状态码    状态码，200时表示请求成功
     * @param $message String    可选    状态信息
     */
    private function _echo_json($status, $message = '', $data = [])
    {

        return [
            'result' => (string)$status,
            'msg' => $message,
            'rows' => $data
        ];
    }

    /*
     * 将数组中，大写的键名转换为小写
     */
    private function arraykeyToLower($data = array())
    {
        if (!empty($data)) {
            //一维数组
            if (count($data) == count($data, 1)) {
                $data = array_change_key_case($data, CASE_LOWER);
            } else    //二维数组
            {
                foreach ($data as $key => $value) {
                    $data[$key] = array_change_key_case($value, CASE_LOWER);
                }
            }
        }
        return $data;
    }

    /**
     * 保养重点备件与18位码匹配关系获取接口
     */
    public function e3s_part_car_series()
    {
        $all_data = trim(file_get_contents('php://input'));//一个JSON
        $all_data = json_decode($all_data, true);
        $this->e3s_log('e3s_part_car_series', 'success', $all_data);
        if (empty($all_data)) {
            return $this->setResponseError('error')->send();
        }
        $res = json_decode($all_data['msgBody']['result'], true);
        $array = $this->arraykeyToLower($res['DATA']);
        if (empty($array)) {
            return $this->setResponseError('error')->send();
        }
        $part_car_series = new E3sPartCarSeries();
        $part_car_series_log = new E3sPartCarSeriesLog();
        $start_time = microtime(true);
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $update_no = date("YmdHis", time()) . rand(10000, 99999);
        $new_data['update_no'] = $update_no;
        $new_data['service_type'] = '备件车型车系';

        $new_array_add = [];
        $new_array_add_log = [];
        $new_array_update = [];
        $k = 0;
        foreach ($array as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_add_log[] = [
                'car_config_code' => $value['car_config_code'] ?? '',
                'part_no' => $value['part_no'] ?? '',
                'other_attr' => $value['other_attr'] ?? '',
                'wi_code' => $value['wi_code'] ?? '',
                'wi_qty' => $value['wi_qty'] ?? '',
                'pub_series' => $value['pub_car_series_code'] ?? '',//销售车系
                'pub_series_name' => $value['pub_car_series_name'] ?? '',//销售车系中文
                'pub_car_type_code' => $value['pub_car_type_code'] ?? '',//销售车型
                'pub_car_type_code_cn' => $value['pub_car_type_name'] ?? '',//销售车型中文
                'part_variety_code' => $value['part_variety_code'] ?? '',//备件品种
                'part_variety_name' => $value['part_variety_name'] ?? '',//备件品种名称
                'part_series_code' => $value['car_series_code'] ?? '',//备件车系
                'part_series_cn' => $value['car_series_name'] ?? '',//备件车系中文
                'part_car_type_code' => $value['car_type_code'] ?? '',//备件车型
                'part_car_type_name' => $value['car_type_name'] ?? '',//备件车型中文
                'car_brand_code' => $value['cart_brand_code'] ?? '',
                'car_brand_name' => $value['car_brand_name'] ?? '',
                'car_config_cn' => $value['car_config_cn'] ?? '',
                'e3s_update_no' => $update_no,
                'wi_hours' => $value['wi_time'],
                'wi_type' => $value['wi_status'],
                'part_con_rf_id' => $value['part_con_rf_id'],
                'wi_name' => $value['wi_name'] ?? '',
                'remark' => $value['remark'],
                'upgrade_type' =>$value['upgrade_type'],
                'fit_beg_time' =>$value['fit_beg_time'] ?? '',
                'fit_end_time' =>$value['fit_end_time'] ?? ''
            ];
            $params['where']['car_config_code'] = $value['car_config_code'];
            $params['where']['part_no'] = $value['part_no'];
            $params['order'] = 'id desc';
            $find = $part_car_series->getOne($params);
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array_add[] = [
                        'car_config_code' => $value['car_config_code'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'other_attr' => $value['other_attr'] ?? '',
                        'wi_code' => $value['wi_code'] ?? '',
                        'wi_qty' => $value['wi_qty'] ?? '',
                        'pub_series' => $value['pub_car_series_code'] ?? '',//销售车系
                        'pub_series_name' => $value['pub_car_series_name'] ?? '',//销售车系中文
                        'pub_car_type_code' => $value['pub_car_type_code'] ?? '',//销售车型
                        'pub_car_type_code_cn' => $value['pub_car_type_name'] ?? '',//销售车型中文
                        'part_variety_code' => $value['part_variety_code'] ?? '',//备件品种
                        'part_variety_name' => $value['part_variety_name'] ?? '',//备件品种名称
                        'part_series_code' => $value['car_series_code'] ?? '',//备件车系
                        'part_series_cn' => $value['car_series_name'] ?? '',//备件车系中文
                        'part_car_type_code' => $value['car_type_code'] ?? '',//备件车型
                        'part_car_type_name' => $value['car_type_name'] ?? '',//备件车型中文
                        'car_brand_code' => $value['cart_brand_code'] ?? '',
                        'car_brand_name' => $value['car_brand_name'] ?? '',
                        'car_config_cn' => $value['car_config_cn'] ?? '',
                        'e3s_update_no' => $update_no,
                        'wi_hours' => $value['wi_time'],
                        'wi_type' => $value['wi_status'],
                        'part_con_rf_id' => $value['part_con_rf_id'],
                        'wi_name' => $value['wi_name'] ?? '',
                        'remark' => $value['remark'],
                        'fit_beg_time' =>$value['fit_beg_time'] ?? '',
                        'fit_end_time' =>$value['fit_end_time'] ?? ''
                    ];
                } else {
                    $update_data = [
                        'car_config_code' => $value['car_config_code'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'other_attr' => $value['other_attr'] ?? '',
                        'wi_code' => $value['wi_code'] ?? '',
                        'wi_qty' => $value['wi_qty'] ?? '',
                        'pub_series' => $value['pub_car_series_code'] ?? '',//销售车系
                        'pub_series_name' => $value['pub_car_series_name'] ?? '',//销售车系中文
                        'pub_car_type_code' => $value['pub_car_type_code'] ?? '',//销售车型
                        'pub_car_type_code_cn' => $value['pub_car_type_name'] ?? '',//销售车型中文
                        'part_variety_code' => $value['part_variety_code'] ?? '',//备件品种
                        'part_variety_name' => $value['part_variety_name'] ?? '',//备件品种名称
                        'part_series_code' => $value['car_series_code'] ?? '',//备件车系
                        'part_series_cn' => $value['car_series_name'] ?? '',//备件车系中文
                        'part_car_type_code' => $value['car_type_code'] ?? '',//备件车型
                        'part_car_type_name' => $value['car_type_name'] ?? '',//备件车型中文
                        'car_brand_code' => $value['cart_brand_code'] ?? '',
                        'car_brand_name' => $value['car_brand_name'] ?? '',
                        'car_config_cn' => $value['car_config_cn'] ?? '',
                        'e3s_update_no' => $update_no,
                        'wi_hours' => $value['wi_time'],
                        'wi_type' => $value['wi_status'],
                        'part_con_rf_id' => $value['part_con_rf_id'],
                        'wi_name' => $value['wi_name'] ?? '',
                        'remark' => $value['remark'],
                        'fit_beg_time' =>$value['fit_beg_time'] ?? '',
                        'fit_end_time' =>$value['fit_end_time'] ?? '',
                        'is_enable' => 1,
                    ];
                    $part_car_series->saveData($update_data, $params['where']);
                }
            }else{
                if (empty($find)) {
                    $new_array_update[] = [
                        'car_config_code' => $value['car_config_code'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'other_attr' => $value['other_attr'] ?? '',
                        'wi_code' => $value['wi_code'] ?? '',
                        'wi_qty' => $value['wi_qty'] ?? '',
                        'pub_series' => $value['pub_car_series_code'] ?? '',//销售车系
                        'pub_series_name' => $value['pub_car_series_name'] ?? '',//销售车系中文
                        'pub_car_type_code' => $value['pub_car_type_code'] ?? '',//销售车型
                        'pub_car_type_code_cn' => $value['pub_car_type_name'] ?? '',//销售车型中文
                        'part_variety_code' => $value['part_variety_code'] ?? '',//备件品种
                        'part_variety_name' => $value['part_variety_name'] ?? '',//备件品种名称
                        'part_series_code' => $value['car_series_code'] ?? '',//备件车系
                        'part_series_cn' => $value['car_series_name'] ?? '',//备件车系中文
                        'part_car_type_code' => $value['car_type_code'] ?? '',//备件车型
                        'part_car_type_name' => $value['car_type_name'] ?? '',//备件车型中文
                        'car_brand_code' => $value['cart_brand_code'] ?? '',
                        'car_brand_name' => $value['car_brand_name'] ?? '',
                        'car_config_cn' => $value['car_config_cn'] ?? '',
                        'e3s_update_no' => $update_no,
                        'wi_hours' => $value['wi_time'],
                        'wi_type' => $value['wi_status'],
                        'part_con_rf_id' => $value['part_con_rf_id'],
                        'wi_name' => $value['wi_name'] ?? '',
                        'remark' => $value['remark'],
                        'is_enable'=>0,
                        'fit_beg_time' =>$value['fit_beg_time'] ?? '',
                        'fit_end_time' =>$value['fit_end_time'] ?? ''
                    ];
                } else {
                    $update_data = [
                        'car_config_code' => $value['car_config_code'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'other_attr' => $value['other_attr'] ?? '',
                        'wi_code' => $value['wi_code'] ?? '',
                        'wi_qty' => $value['wi_qty'] ?? '',
                        'pub_series' => $value['pub_car_series_code'] ?? '',//销售车系
                        'pub_series_name' => $value['pub_car_series_name'] ?? '',//销售车系中文
                        'pub_car_type_code' => $value['pub_car_type_code'] ?? '',//销售车型
                        'pub_car_type_code_cn' => $value['pub_car_type_name'] ?? '',//销售车型中文
                        'part_variety_code' => $value['part_variety_code'] ?? '',//备件品种
                        'part_variety_name' => $value['part_variety_name'] ?? '',//备件品种名称
                        'part_series_code' => $value['car_series_code'] ?? '',//备件车系
                        'part_series_cn' => $value['car_series_name'] ?? '',//备件车系中文
                        'part_car_type_code' => $value['car_type_code'] ?? '',//备件车型
                        'part_car_type_name' => $value['car_type_name'] ?? '',//备件车型中文
                        'car_brand_code' => $value['cart_brand_code'] ?? '',
                        'car_brand_name' => $value['car_brand_name'] ?? '',
                        'car_config_cn' => $value['car_config_cn'] ?? '',
                        'e3s_update_no' => $update_no,
                        'wi_hours' => $value['wi_time'],
                        'wi_type' => $value['wi_status'],
                        'part_con_rf_id' => $value['part_con_rf_id'],
                        'wi_name' => $value['wi_name'] ?? '',
                        'remark' => $value['remark'],
                        'is_enable' => 0,
                        'fit_beg_time' =>$value['fit_beg_time'] ?? '',
                        'fit_end_time' =>$value['fit_end_time'] ?? ''
                    ];
//                    $part_car_series->saveData($update_data,['id' => $find['id']]);
                    $part_car_series->saveData($update_data,$params['where']);
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array_add)){
                        $part_car_series->insertAll($new_array_add);
                    }
                    if(!empty($new_array_update)){
                        $part_car_series->insertAll($new_array_update);
                    }
                    if(!empty($new_array_add_log)){
                        $part_car_series_log->insertAll($new_array_add_log);
                    }
                } catch (Exception $exception) {
                    Logger::error('e3s_part_car_series_error:', [
                        'type' => 'PushMaintainKeyAnd18BitRelation',
                        'data' => json_encode([]),
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array_add = [];
                $new_array_update = [];
                $new_array_add_log = [];
            }
        }
        try {
            $new_data['update_type'] = 1;
            if(!empty($new_array_add)){
                $part_car_series->insertAll($new_array_add);
            }
            if(!empty($new_array_update)){
                $part_car_series->insertAll($new_array_update);
            }
            if(!empty($new_array_add_log)){
                $part_car_series_log->insertAll($new_array_add_log);
            }
        } catch (Exception $exception) {
            $new_data['update_type'] = 0;
            $this->e3s_log('e3s_part_car_series_error', 'error', [
                'type' => 'PushMaintainKeyAnd18BitRelation1',
                'e' => $exception->getMessage()
            ]);
        }
        $new_data['new_add'] = $new_add;
        $new_data['new_update'] = $new_update;
        $new_data['new_del'] = $new_del;
        $end_time = microtime(true);
        $new_data['update_duration'] = $end_time - $start_time;
        $this->e3s_update($new_data);
//        $SendMailer = new SendMailer();
//        $SendMailer->send_mail(['type'=>8,'update_no'=>$update_no]);
        return $this->setResponseData('ok')->send();
    }

    /**
     * 保养套餐
     * @param $array
     * @return \think\Response
     */
    public function e3s_maintenance_package()
    {
        $all_data = trim(file_get_contents('php://input'));//一个JSON
        $all_data = json_decode($all_data, true);
        $this->e3s_log('PushMaintainGroup_kafka_data', 'success', $all_data);
        if (empty($all_data)) {
            return $this->setResponseError('error')->send();
        }
        $res = json_decode($all_data['msgBody']['result'], true);
        $array = $this->arraykeyToLower($res['DATA']);
        if (empty($array)) {
            return $this->setResponseError('error')->send();
        }
//        $this->add_e3s_maintenance_package($array);
        Queue::push('app\common\queue\E3sKafka', json_encode($array), config('queue_type.e3s_kafka'));
        return $this->setResponseData('ok')->send();
    }

    //队列执行
    public function add_e3s_maintenance_package($array)
    {
        $start_time = microtime(true);
        $update_no = date("YmdHis", time()) . rand(10000, 99999);
        $new_data['update_no'] = $update_no;
        $new_data['service_type'] = '保养套餐';
        $part_item = $this->maintenance_package_part($this->arraykeyToLower($array['PART_ITEM']), $update_no);
        $order = $this->maintenance_package($this->arraykeyToLower($array['ORDER']), $update_no);
        $this->maintenance_package_wi($this->arraykeyToLower($array['WORK_ITEM']), $update_no);
        $this->maintenance_package_rule_item($this->arraykeyToLower($array['RULE_ITEM']), $update_no);
        $new_data['update_type'] = 1;
//        $new_data['new_add'] = $order['new_add'] + $part_item['new_add'] + $work_item['new_add'] + $rule_item['new_add'];
        $new_data['new_add'] = $order['new_add'];
        $new_data['new_update'] = $order['new_update'];
        $new_data['new_del'] = $order['new_del'];
        $end_time = microtime(true);
        $new_data['update_duration'] = $end_time - $start_time;
        $this->e3s_update($new_data);

        return $this->setResponseData('ok')->send();
    }

    //添加E3S保养套餐表
    private function maintenance_package($data, $order_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_package = new E3sPackageModel();
        $e3s_package_log = new E3sPackageLog();
        $e3s_package_part = new E3sPackagePartModel();
        $new_array = [];
        $new_array_log = [];
        $k = 0;
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        $email = [];
        foreach ($data as $key=>$value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'maintain_group_id' => $value['maintain_group_id'] ?? '',
                'maintain_group_code' => $value['maintain_group_code'] ?? '',
                'maintain_total_count' => $value['maintain_total_count'] ?? '',
                'discount' => $value['discount'] ?? '',
                'valid_end_date' => $value['valid_end_date'] ?? '0000-00-00 00:00:00',
                'belong_zone_code' => $value['belong_zone_code'] ?? '',
                'saler_amount' => $value['saler_amount'] ?? '',
                'product_type_id' => $value['product_type_id'] ?? '',
                'product_type_code' => $value['product_type_code'] ?? '',
                'product_type_name' => $value['product_type_name'] ?? '',
                'dlr_brand_code' => $value['dlr_brand_code'] ?? '',
                'point_code' => $value['point_code'] ?? '',
                'maintain_group_name' => $value['maintain_group_name'] ?? '',
                'deposit' => $value['deposit'] ?? '',
                'discount_amount' => $value['discount_amount'] ?? '',
                'upgrade_type' => $value['upgrade_type'] ?? '',
                'e3s_update_no' => $order_no,
                'car_brand_en' => $value['car_brand_en'],
                'car_brand_cn' => $value['car_brand_cn'],
                'product_variety_name' => $value['product_variety_name']
            ];
            $params['where']['maintain_group_id'] = $value['maintain_group_id'];
            $find = $e3s_package->getOne($params);
            $is_goods = $this->spare_part->is_goods($value['maintain_group_code']);
            $otter = $e3s_package_part->where([
                'maintain_group_id'=>$value['maintain_group_id'],
                'maintain_parttype'=>'ENGINEOIL',
            ])->column('part_name');
            if(!empty($is_goods)){
                $email[$key]['maintain_group_id'] = "套餐ID：" . $value['maintain_group_id'] .  "<br />";
                $email[$key]['maintain_group_code'] = "套餐编码：" . $value['maintain_group_code'] .  "<br />";
                $email[$key]['maintain_group_name'] = "套餐名称：" . $value['maintain_group_name'] .  "<br />";
                $email[$key]['belong_zone_code'] = "TOP地区：" . $value['belong_zone_code'] .  "<br />";
                if(!empty($find)){
                    if($find['saler_amount'] != $value['saler_amount']){
                        $email[$key]['saler_amount'] = "套餐金额：" . $value['saler_amount'] .  "(变更前：".$find['saler_amount'].")<br />";
                    }else{
                        $email[$key]['saler_amount'] = "套餐金额：" . $value['saler_amount'] .  "<br />";
                    }
                }else{
                    $email[$key]['saler_amount'] = "套餐金额：" . $value['saler_amount'] .  "<br />";
                }

                $email[$key]['otter'] = "套餐油型：" . implode(' / ',$otter) .  "<br />";
                $email[$key]['maintain_total_count'] = "套餐次数：" . $value['maintain_total_count'] .  "<br />";
                if($value['upgrade_type'] == 1){
                    $upgrade_type = '正常';
                }else{
                    $upgrade_type = '停用';
                }
                if(!empty($find)){
                    if($find['upgrade_type'] != $value['upgrade_type']){
                        $email[$key]['product_type_code'] = '套餐编码：'.$value['maintain_group_code'].'<br />';
                        if($find['upgrade_type'] == 1){
                            $upgrade_types = '正常';
                        }else{
                            $upgrade_types = '停用';
                        }
                        $email[$key]['upgrade_type'] = "套餐状态：" . $upgrade_type .  "(变更前：" . $upgrade_types . ")<br />";
                    }else{
                        $email[$key]['upgrade_type'] = "套餐状态：" . $upgrade_type .  "<br />";
                    }
                }else{
                    $email[$key]['upgrade_type'] = "套餐状态：" . $upgrade_type .  "<br />";
                }

                $email[$key]['is_update'] = "是否新增：否<br />";
                $email[$key]['update_time'] = "更新时间：" . date('Y-m-d H:i:s',time()) .  "<br />";
            }
            $new_array[$k] = [
                'maintain_group_id' => $value['maintain_group_id'] ?? '',
                'maintain_group_code' => $value['maintain_group_code'] ?? '',
                'maintain_total_count' => $value['maintain_total_count'] ?? '',
                'discount' => $value['discount'] ?? '',
                'valid_end_date' => $value['valid_end_date'] ?? '0000-00-00 00:00:00',
                'belong_zone_code' => $value['belong_zone_code'] ?? '',
                'saler_amount' => $value['saler_amount'] ?? '',
                'product_type_id' => $value['product_type_id'] ?? '',
                'product_type_code' => $value['product_type_code'] ?? '',
                'product_type_name' => $value['product_type_name'] ?? '',
                'dlr_brand_code' => $value['dlr_brand_code'] ?? '',
                'point_code' => $value['point_code'] ?? '',
                'maintain_group_name' => $value['maintain_group_name'] ?? '',
                'deposit' => $value['deposit'] ?? '',
                'discount_amount' => $value['discount_amount'] ?? '',
                'upgrade_type' => $value['upgrade_type'] ?? '',
                'car_brand_en' => $value['car_brand_en'],
                'car_brand_cn' => $value['car_brand_cn'],
                'e3s_update_no' => $order_no,
                'product_variety_name' => $value['product_variety_name']
            ];
            if(!empty($find)){
                $new_array[$k]['id'] = $find['id'];
            }else{
                $email[$key]['product_type_code'] = '套餐编码：'.$value['maintain_group_code'].'<br />';
                if($value['upgrade_type'] == 1){
                    $upgrade_type = '正常';
                }else{
                    $upgrade_type = '停用';
                }
                $email[$key]['maintain_group_id'] = "套餐ID：" . $value['maintain_group_id'] .  "<br />";
                $email[$key]['maintain_group_code'] = "套餐编码：" . $value['maintain_group_code'] .  "<br />";
                $email[$key]['maintain_group_name'] = "套餐名称：" . $value['maintain_group_name'] .  "<br />";
                $email[$key]['belong_zone_code'] = "TOP地区：" . $value['belong_zone_code'] .  "<br />";
                $email[$key]['saler_amount'] = "套餐金额：" . $value['saler_amount'] .  "<br />";
                $email[$key]['otter'] = "套餐油型：" . implode(' / ',$otter) .  "<br />";
                $email[$key]['maintain_total_count'] = "套餐次数：" . $value['maintain_total_count'] .  "<br />";
                $email[$key]['upgrade_type'] = "套餐状态：" . $upgrade_type .  "<br />";
                $email[$key]['is_update'] = "是否新增：是<br />";
                $email[$key]['update_time'] = "更新时间：" . date('Y-m-d H:i:s',time()) .  "<br />";

            }
            if ($update_status != 1){
                $new_array[$k]['is_enable'] = 0;
            }
            $k++;
            if ($k == 100) {
                try {
                    $e3s_package->saveAll($new_array);
                    $e3s_package_log->saveAll($new_array_log);
                } catch (Exception $exception) {
                    $this->e3s_log('e3s_maintenance_package', 'error', [
                        'type' => 'maintenance_package_0',
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_array_log = [];
            }
        }
        try {
            $e3s_package->saveAll($new_array);
            $e3s_package_log->saveAll($new_array_log);
        } catch (Exception $exception) {
            $this->e3s_log('e3s_maintenance_package', 'error', [
                'e' => $exception->getMessage()
            ]);
        }
        $new_email = array_values($email);
        if(!empty($new_email)){
            $SendMailer = new SendMailer();
            $SendMailer->send_mail(['type'=>9,'update_no'=>$order_no,'service_type'=>'保养套餐','data'=>$new_email]);
        }
        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    //添加E3S保养套餐表
    private function maintenance_package_part($data, $order_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_package_part = new E3sPackagePartModel();
        $e3s_package_part_log = new E3sPackagePartLog();
        $new_array = [];
        $new_update_array = [];
        $new_array_log = [];
        $k = 0;
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        foreach ($data as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'maintain_group_part_id' => $value['maintain_group_part_id'] ?? '',
                'maintain_group_id' => $value['maintain_group_id'] ?? '',
                'discount' => $value['discount'] ?? '',
                'part_no' => $value['part_no'] ?? '',
                'part_name' => $value['part_name'] ?? '',
                'part_qty' => $value['part_qty'] ?? '',
                'part_price' => $value['part_price'] ?? '',
                'pre_part_amount' => $value['pre_part_amount'] ?? '',
                'remark' => $value['remark'] ?? '',
                'maintain_count' => $value['maintain_count'] ?? '',
                'maintain_parttype' => $value['maintain_parttype'] ?? '',
                'e3s_update_no' => $order_no ?? '',
                'upgrade_type' =>$value['upgrade_type']
            ];//每次请求都记录 不更新删除操作
            $params['where']['maintain_group_part_id'] = $value['maintain_group_part_id'];
            $find = $e3s_package_part->getOne($params);
//            print_json($find,$update_status);
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array[] = [
                        'maintain_group_part_id' => $value['maintain_group_part_id'] ?? '',
                        'maintain_group_id' => $value['maintain_group_id'] ?? '',
                        'discount' => $value['discount'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'part_name' => $value['part_name'] ?? '',
                        'part_qty' => $value['part_qty'] ?? '',
                        'part_price' => $value['part_price'] ?? '',
                        'pre_part_amount' => $value['pre_part_amount'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'maintain_count' => $value['maintain_count'] ?? '',
                        'maintain_parttype' => $value['maintain_parttype'] ?? '',
                        'e3s_update_no' => $order_no ?? '',
                    ];
                } else {
                    try {
                        $update_data = [
                            'maintain_group_part_id' => $value['maintain_group_part_id'] ?? '',
                            'maintain_group_id' => $value['maintain_group_id'] ?? '',
                            'discount' => $value['discount'] ?? '',
                            'part_no' => $value['part_no'] ?? '',
                            'part_name' => $value['part_name'] ?? '',
                            'part_qty' => $value['part_qty'] ?? '',
                            'part_price' => $value['part_price'] ?? '',
                            'pre_part_amount' => $value['pre_part_amount'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'maintain_count' => $value['maintain_count'] ?? '',
                            'maintain_parttype' => $value['maintain_parttype'] ?? '',
                            'e3s_update_no' => $order_no ?? '',
                        ];
                        $e3s_package_part->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('maintenance_package_part', 'error', [
                            'type'=>'maintenance_package_part_0',
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }else{
                if (empty($find)) {
                    $new_update_array[] = [
                        'maintain_group_part_id' => $value['maintain_group_part_id'] ?? '',
                        'maintain_group_id' => $value['maintain_group_id'] ?? '',
                        'discount' => $value['discount'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'part_name' => $value['part_name'] ?? '',
                        'part_qty' => $value['part_qty'] ?? '',
                        'part_price' => $value['part_price'] ?? '',
                        'pre_part_amount' => $value['pre_part_amount'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'maintain_count' => $value['maintain_count'] ?? '',
                        'maintain_parttype' => $value['maintain_parttype'] ?? '',
                        'e3s_update_no' => $order_no ?? '',
                        'is_enable' => 0
                    ];
                } else {
                    try {
                        $update_data = [
                            'maintain_group_part_id' => $value['maintain_group_part_id'] ?? '',
                            'maintain_group_id' => $value['maintain_group_id'] ?? '',
                            'discount' => $value['discount'] ?? '',
                            'part_no' => $value['part_no'] ?? '',
                            'part_name' => $value['part_name'] ?? '',
                            'part_qty' => $value['part_qty'] ?? '',
                            'part_price' => $value['part_price'] ?? '',
                            'pre_part_amount' => $value['pre_part_amount'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'maintain_count' => $value['maintain_count'] ?? '',
                            'maintain_parttype' => $value['maintain_parttype'] ?? '',
                            'e3s_update_no' => $order_no ?? '',
                            'is_enable' => 0
                        ];
                        $e3s_package_part->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('maintenance_package_part', 'error', [
                            'type'=>'maintenance_package_part_1',
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array)){
                        $e3s_package_part->insertAll($new_array);
                    }
                    if(!empty($new_update_array)){
                        $e3s_package_part->insertAll($new_update_array);
                    }
                    if(!empty($new_array_log)){
                        $e3s_package_part_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('maintenance_package_part', 'error', [
                        'type'=>'maintenance_package_part_2',
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_update_array = [];
                $new_array_log = [];
            }
        }
        try {
            if (!empty($new_array)) {
                $e3s_package_part->insertAll($new_array);
            }
            if (!empty($new_update_array)) {
                $e3s_package_part->insertAll($new_update_array);
            }
            if (!empty($new_array_log)) {
                $e3s_package_part_log->insertAll($new_array_log);
            }
        } catch (Exception $exception) {
            $this->e3s_log('maintenance_package_part', 'error', [
                'type'=>'maintenance_package_part_3',
                'e' => $exception->getMessage()
            ]);
        }
        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    //添加E3S保养套餐 工时
    private function maintenance_package_wi($data, $order_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_package_part_wi = new E3sMaintenancePackageWi();
        $e3s_package_part_wi_log = new E3sMaintenancePackageWiLog();
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        $new_array = [];
        $new_update_array = [];
        $new_array_log = [];
        $k = 0;
        foreach ($data as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'maintain_group_wi_id' => $value['maintain_group_wi_id'] ?? '',
                'maintain_group_id' => $value['maintain_group_id'] ?? '',
                'wi_code' => $value['wi_code'] ?? '',
                'wi_name' => $value['wi_name'] ?? '',
                'wi_qty' => $valuee['wi_qty'] ?? '',
                'special_wi_flag' => $valuee['special_wi_flag'] ?? '',
                'wi_price' => $valuee['wi_price'] ?? '',
                'wi_total_amount' => $valuee['wi_total_amount'] ?? '',
                'maintain_count_wi' => $valuee['maintain_count_wi'] ?? '',
                'part_no' => $valuee['part_no'] ?? '',
                'discount' => $value['discount'] ?? '',
                'e3s_update_no' => $order_no ?? '',
                'upgrade_type' =>$value['upgrade_type']
            ];
            $params['where']['maintain_group_wi_id'] = $value['maintain_group_wi_id'];
            $find = $e3s_package_part_wi->getOne($params);
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array[] = [
                        'maintain_group_wi_id' => $value['maintain_group_wi_id'] ?? '',
                        'maintain_group_id' => $value['maintain_group_id'] ?? '',
                        'wi_code' => $value['wi_code'] ?? '',
                        'wi_name' => $value['wi_name'] ?? '',
                        'wi_qty' => $valuee['wi_qty'] ?? '',
                        'special_wi_flag' => $valuee['special_wi_flag'] ?? '',
                        'wi_price' => $valuee['wi_price'] ?? '',
                        'wi_total_amount' => $valuee['wi_total_amount'] ?? '',
                        'maintain_count_wi' => $valuee['maintain_count_wi'] ?? '',
                        'part_no' => $valuee['part_no'] ?? '',
                        'discount' => $value['discount'] ?? '',
                        'e3s_update_no' => $order_no ?? '',
                    ];
                } else {
                    try {
                        $update_data = [
                            'maintain_group_id' => $value['maintain_group_id'] ?? '',
                            'wi_code' => $value['wi_code'] ?? '',
                            'wi_name' => $value['wi_name'] ?? '',
                            'wi_qty' => $valuee['wi_qty'] ?? '',
                            'special_wi_flag' => $valuee['special_wi_flag'] ?? '',
                            'wi_price' => $valuee['wi_price'] ?? '',
                            'wi_total_amount' => $valuee['wi_total_amount'] ?? '',
                            'maintain_count_wi' => $valuee['maintain_count_wi'] ?? '',
                            'part_no' => $valuee['part_no'] ?? '',
                            'discount' => $value['discount'] ?? '',
                            'e3s_update_no' => $order_no ?? '',
                        ];
                        $e3s_package_part_wi->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('maintain_group_wi', 'error', [
                            'type'=>'maintain_group_wi_0',
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }else{
                if (empty($find)) {
                    $new_update_array[] = [
                        'maintain_group_wi_id' => $value['maintain_group_wi_id'] ?? '',
                        'maintain_group_id' => $value['maintain_group_id'] ?? '',
                        'wi_code' => $value['wi_code'] ?? '',
                        'wi_name' => $value['wi_name'] ?? '',
                        'wi_qty' => $valuee['wi_qty'] ?? '',
                        'special_wi_flag' => $valuee['special_wi_flag'] ?? '',
                        'wi_price' => $valuee['wi_price'] ?? '',
                        'wi_total_amount' => $valuee['wi_total_amount'] ?? '',
                        'maintain_count_wi' => $valuee['maintain_count_wi'] ?? '',
                        'part_no' => $valuee['part_no'] ?? '',
                        'discount' => $value['discount'] ?? '',
                        'e3s_update_no' => $order_no ?? '',
                        'is_enable' => 0,
                    ];
                } else {
                    try {
                        $update_data = [
                            'maintain_group_id' => $value['maintain_group_id'] ?? '',
                            'wi_code' => $value['wi_code'] ?? '',
                            'wi_name' => $value['wi_name'] ?? '',
                            'wi_qty' => $valuee['wi_qty'] ?? '',
                            'special_wi_flag' => $valuee['special_wi_flag'] ?? '',
                            'wi_price' => $valuee['wi_price'] ?? '',
                            'wi_total_amount' => $valuee['wi_total_amount'] ?? '',
                            'maintain_count_wi' => $valuee['maintain_count_wi'] ?? '',
                            'part_no' => $valuee['part_no'] ?? '',
                            'discount' => $value['discount'] ?? '',
                            'e3s_update_no' => $order_no ?? '',
                            'is_enable' => 0,
                        ];
                        $e3s_package_part_wi->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('maintain_group_wi', 'error', [
                            'type'=>'maintain_group_wi_1',
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array)){
                        $e3s_package_part_wi->insertAll($new_array);
                    }
                    if(!empty($new_update_array)){
                        $e3s_package_part_wi->insertAll($new_update_array);
                    }
                    if(!empty($new_array_log)){
                        $e3s_package_part_wi_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('maintain_group_wi', 'error', [
                        'type'=>'maintain_group_wi_2',
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_update_array = [];
                $new_array_log = [];
            }
        }
        try {
            if (!empty($new_array)) {
                $e3s_package_part_wi->insertAll($new_array);
            }
            if (!empty($new_update_array)) {
                $e3s_package_part_wi->insertAll($new_update_array);
            }
            $e3s_package_part_wi_log->insertAll($new_array_log);
        } catch (Exception $exception) {
            $this->e3s_log('maintain_group_wi', 'error', [
                'type'=>'maintain_group_wi_2',
                'e' => $exception->getMessage()
            ]);
        }
        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    //添加E3S保养套餐积分规则关系表_工时信息
    private function maintenance_package_rule_item($data, $order_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_package_rule = new E3sPackageRuleItem();
        $e3s_package_rule_log = new E3sPackageRuleItemLog();
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        $new_array = [];
        $new_update_array = [];
        $new_array_log = [];
        $k = 0;
        foreach ($data as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'maintain_group_mp_id' => $value['maintain_group_mp_id'] ?? '',
                'maintain_group_id' => $value['maintain_group_id'] ?? '',
                'maintain_group_code' => $value['maintain_group_code'] ?? '',
                'mp_code' => $value['mp_code'] ?? '',
                'mp_name' => $valuee['mp_name'] ?? '',
                'mponetime_canuse' => $valuee['mponetime_canuse'] ?? '',
                'mp_times' => $valuee['mp_times'] ?? '',
                'mp_total' => $valuee['mp_total'] ?? '',
                'binding_wi' => $valuee['binding_wi'] ?? '',
                'binding_part' => $valuee['binding_part'] ?? '',
                'send_flag' => $valuee['send_flag'] ?? '',
                'e3s_update_no' => $order_no ?? '',
                'upgrade_type' =>$value['upgrade_type']
            ];
            $params['where']['maintain_group_mp_id'] = $value['maintain_group_mp_id'];
            $find = $e3s_package_rule->getOne($params);
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array[] = [
                        'maintain_group_mp_id' => $value['maintain_group_mp_id'] ?? '',
                        'maintain_group_id' => $value['maintain_group_id'] ?? '',
                        'maintain_group_code' => $value['maintain_group_code'] ?? '',
                        'mp_code' => $value['mp_code'] ?? '',
                        'mp_name' => $valuee['mp_name'] ?? '',
                        'mponetime_canuse' => $valuee['mponetime_canuse'] ?? '',
                        'mp_times' => $valuee['mp_times'] ?? '',
                        'mp_total' => $valuee['mp_total'] ?? '',
                        'binding_wi' => $valuee['binding_wi'] ?? '',
                        'binding_part' => $valuee['binding_part'] ?? '',
                        'send_flag' => $valuee['send_flag'] ?? '',
                        'e3s_update_no' => $order_no ?? '',
                    ];
                } else {
                    try {
                        $update_data = [
                            'maintain_group_mp_id' => $value['maintain_group_mp_id'] ?? '',
                            'maintain_group_id' => $value['maintain_group_id'] ?? '',
                            'maintain_group_code' => $value['maintain_group_code'] ?? '',
                            'mp_code' => $value['mp_code'] ?? '',
                            'mp_name' => $valuee['mp_name'] ?? '',
                            'mponetime_canuse' => $valuee['mponetime_canuse'] ?? '',
                            'mp_times' => $valuee['mp_times'] ?? '',
                            'mp_total' => $valuee['mp_total'] ?? '',
                            'binding_wi' => $valuee['binding_wi'] ?? '',
                            'binding_part' => $valuee['binding_part'] ?? '',
                            'send_flag' => $valuee['send_flag'] ?? '',
                            'e3s_update_no' => $order_no ?? '',
                        ];
                        $e3s_package_rule->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('maintenance_package_rule_item', 'error', [
                            'type'=>'maintenance_package_rule_item_0',
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }else{
                if (empty($find)) {
                    $new_update_array[] = [
                        'maintain_group_mp_id' => $value['maintain_group_mp_id'] ?? '',
                        'maintain_group_id' => $value['maintain_group_id'] ?? '',
                        'maintain_group_code' => $value['maintain_group_code'] ?? '',
                        'mp_code' => $value['mp_code'] ?? '',
                        'mp_name' => $valuee['mp_name'] ?? '',
                        'mponetime_canuse' => $valuee['mponetime_canuse'] ?? '',
                        'mp_times' => $valuee['mp_times'] ?? '',
                        'mp_total' => $valuee['mp_total'] ?? '',
                        'binding_wi' => $valuee['binding_wi'] ?? '',
                        'binding_part' => $valuee['binding_part'] ?? '',
                        'send_flag' => $valuee['send_flag'] ?? '',
                        'e3s_update_no' => $order_no ?? '',
                        'is_enable' => 0,
                    ];
                } else {
                    try {
                        $update_data = [
                            'maintain_group_mp_id' => $value['maintain_group_mp_id'] ?? '',
                            'maintain_group_id' => $value['maintain_group_id'] ?? '',
                            'maintain_group_code' => $value['maintain_group_code'] ?? '',
                            'mp_code' => $value['mp_code'] ?? '',
                            'mp_name' => $valuee['mp_name'] ?? '',
                            'mponetime_canuse' => $valuee['mponetime_canuse'] ?? '',
                            'mp_times' => $valuee['mp_times'] ?? '',
                            'mp_total' => $valuee['mp_total'] ?? '',
                            'binding_wi' => $valuee['binding_wi'] ?? '',
                            'binding_part' => $valuee['binding_part'] ?? '',
                            'send_flag' => $valuee['send_flag'] ?? '',
                            'e3s_update_no' => $order_no ?? '',
                            'is_enable' => 0,
                        ];
                        $e3s_package_rule->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('maintenance_package_rule_item', 'error', [
                            'type'=>'maintenance_package_rule_item_1',
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array)){
                        $e3s_package_rule->insertAll($new_array);
                    }
                    if(!empty($new_update_array)){
                        $e3s_package_rule->insertAll($new_update_array);
                    }
                    if(!empty($new_array_log)){
                        $e3s_package_rule_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('maintenance_package_rule_item', 'error', [
                        'type'=>'maintenance_package_rule_item_2',
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_update_array = [];
                $new_array_log = [];
            }
        }
        try {
            if (!empty($new_array)) {
                $e3s_package_rule->insertAll($new_array);
            }
            if (!empty($new_update_array)) {
                $e3s_package_rule->insertAll($new_update_array);
            }
            if (!empty($new_array_log)) {
                $e3s_package_rule_log->insertAll($new_array_log);
            }
        } catch (Exception $exception) {
            $this->e3s_log('maintenance_package_rule_item', 'error', [
                'type'=>'maintenance_package_rule_item_3',
                'e' => $exception->getMessage()
            ]);
        }
        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    /**
     * E3S备件库
     */
    public function e3s_spare_part()
    {
        $all_data = trim(file_get_contents('php://input'));//一个JSON
        $all_data = json_decode($all_data, true);
        $this->e3s_log('e3s_spare_part','success',$all_data);
        if (empty($all_data)) {
            return $this->setResponseError('error')->send();
        }
        $res = json_decode($all_data['msgBody']['result'], true);
        $array = $this->arraykeyToLower($res['DATA']);
        if (empty($array)) {
            return $this->setResponseError('error')->send();
        }
        $e3s_spare_part_log = new E3sSparePartLog();
        $start_time = microtime(true);
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $update_no = date("YmdHis", time()) . rand(10000, 99999);
        $new_data['update_no'] = $update_no;
        $new_data['service_type'] = '备件';
        $new_array_add = [];
        $new_array_log = [];
        $k = 0;
        $email = [];
        foreach ($array as $key=>$value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'variety_code_big_code' => $value['variety_code_big_code'] ?? '',
                'variety_code_big_name' => $value['variety_code_big_name'] ?? '',
                'variety_code_mid_code' => $value['variety_code_mid_code'] ?? '',
                'variety_code_mid_name' => $value['variety_code_mid_name'] ?? '',
                'variety_code_small_code' => $value['variety_code_small_code'] ?? '',
                'variety_code_small_name' => $value['variety_code_small_name'] ?? '',
                'rep_part_no' => $value['rep_part_no'] ?? '',
                'part_no' => $value['part_no'] ?? '',
                'part_name' => $value['part_name'] ?? '',
                'pub_series' => $value['pub_series'] ?? '',
                'pub_series_name' => $value['pub_series_name'] ?? '',
                'pub_car_type_code' => $value['pub_car_type_code'] ?? '',
                'pub_car_type_code_cn' => $value['pub_car_type_code_cn'] ?? '',
                'dlr_price' => $value['dlr_price'] ?? '',
                'sale_price' => $value['sale_price'] ?? '',
                'part_status' => $value['part_status'] ?? '',
                'part_status_cn' => $value['part_status_cn'] ?? '',
                'part_brand_code_cn' => $value['part_brand_code_cn'] ?? '',
                'part_brand_code' => $value['part_brand_code'] ?? '',
                'e3s_update_no' => $update_no,
                'upgrade_type' =>$value['upgrade_type'],
                'other_attr' =>$value['other_attr'] ?? '',
                'dlr_order_switch' => isset($value['dlr_order_switch']) ? $value['dlr_order_switch'] : '',
                'dlr_order_switch_cn' => isset($value['dlr_order_switch_cn']) ? $value['dlr_order_switch_cn'] : '',
                'variety_code' => $value['variety_code'] ?? '', // 备件分类编码
                'variety_name' => $value['variety_name'] ?? '', // 备件分类名称
            ];
            $params['where']['part_no'] = $value['part_no'];
            $find = $this->spare_part->getOne($params);
            $is_goods = $this->spare_part->is_goods($value['part_no']);
            if(!empty($is_goods)){
                $email[$key]['part_no'] = '备件号：'.$value['part_no'].'<br />';
                $email[$key]['sale_price'] = '';
                if($find['sale_price'] != $value['sale_price']){
                    $email[$key]['sale_price'] = "备件单价：￥". $value["sale_price"]."(变更前：￥".$find['sale_price'].")<br />";
                }
                if($find['dlr_price'] != $value['dlr_price']){
                    $email[$key]['dlr_price'] = "备件成本价：￥". $value["dlr_price"]."(变更前：￥".$find['dlr_price'].")<br />";
                }
                $email[$key]['rep_part_no'] = '';
                if($find['rep_part_no'] != $value['rep_part_no']){
                    $email[$key]['rep_part_no'] = '被替换件编号：'. $value['rep_part_no'].' (变更前：'.$find['rep_part_no'].')<br />';
                }
                $email[$key]['part_status'] = '';
                if($find['part_status'] != $value['part_status']){
                    $email[$key]['part_status'] =  '备件状态：'. $value['part_status_cn'].' (变更前：'.$find['part_status_cn']. ')<br />';
                }
                $email[$key]['dlr_order_switch'] = '';
                if($find['dlr_order_switch'] != $value['dlr_order_switch']){
                    $email[$key]['dlr_order_switch'] =  '订货状态：'. $value['dlr_order_switch_cn'].' (变更前：'.$find['dlr_order_switch_cn'] ?? '' .')';
                }
                if(empty($email[$key]['sale_price']) && empty($email[$key]['rep_part_no']) && empty($email[$key]['part_status']) && empty($email[$key]['dlr_order_switch'])){
                    unset($email[$key]);
                }
            }
            $new_array_add[$k] = [
                'variety_code_big_code' => $value['variety_code_big_code'] ?? '',
                'variety_code_big_name' => $value['variety_code_big_name'] ?? '',
                'variety_code_mid_code' => $value['variety_code_mid_code'] ?? '',
                'variety_code_mid_name' => $value['variety_code_mid_name'] ?? '',
                'variety_code_small_code' => $value['variety_code_small_code'] ?? '',
                'variety_code_small_name' => $value['variety_code_small_name'] ?? '',
                'rep_part_no' => $value['rep_part_no'] ?? '',
                'part_no' => $value['part_no'] ?? '',
                'part_name' => $value['part_name'] ?? '',
                'pub_series' => $value['pub_series'] ?? '',
                'pub_series_name' => $value['pub_series_name'] ?? '',
                'pub_car_type_code' => $value['pub_car_type_code'] ?? '',
                'pub_car_type_code_cn' => $value['pub_car_type_code_cn'] ?? '',
                'dlr_price' => $value['dlr_price'] ?? '',
                'sale_price' => $value['sale_price'] ?? '',
                'part_status' => $value['part_status'] ?? '',
                'part_status_cn' => $value['part_status_cn'] ?? '',
                'part_brand_code_cn' => $value['part_brand_code_cn'] ?? '',
                'part_brand_code' => $value['part_brand_code'] ?? '',
                'e3s_update_no' => $update_no,
                'other_attr' =>$value['other_attr'] ?? '',
                'dlr_order_switch' =>$value['dlr_order_switch'] ?? '',
                'dlr_order_switch_cn' =>$value['dlr_order_switch_cn'] ?? '',
                'variety_code' => $value['variety_code'] ?? '', // 备件分类编码
                'variety_name' => $value['variety_name'] ?? '', // 备件分类名称
            ];
            if (!empty($find)) {
                $new_array_add[$k]['id'] = $find['id'];
            }
            if ($update_status != 1) {
                $new_array_add[$k]['is_enable'] = 0;
            }
            $k++;
            if ($k == 100) {
                try {
                    $this->spare_part->saveAll($new_array_add);
                    $e3s_spare_part_log->saveAll($new_array_log);
                } catch (Exception $exception) {
                    $this->e3s_log('e3s_spare_part', 'error', [
                        'type'=>'e3s_spare_part_0',
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array_add = [];
                $new_array_log = [];
            }
        }

        try {
            $new_data['update_type'] = 1;
            $this->spare_part->saveAll($new_array_add);
            $e3s_spare_part_log->saveAll($new_array_log);
        } catch (Exception $exception) {
            $new_data['update_type'] = 0;
            $this->e3s_log('e3s_spare_part', 'error', [
                'type'=>'e3s_spare_part_1',
                'e' => $exception->getMessage()
            ]);
        }
        $new_data['new_add'] = $new_add;
        $new_data['new_update'] = $new_update;
        $new_data['new_del'] = $new_del;
        $end_time = microtime(true);
        $new_data['update_duration'] = $end_time - $start_time;
        $this->e3s_update($new_data);
        $new_email = array_values($email);
        if($new_data['update_type'] != 0){
            if(!empty($new_email)){
                $SendMailer = new SendMailer();
                $SendMailer->send_mail(['type'=>8,'update_no'=>$update_no,'service_type'=>'重点备件','data'=>$new_email]);
            }
        }

        return $this->setResponseData('ok')->send();
    }

    /**
     * 保养套餐产品
     */
    public function e3s_maintenance_product()
    {
        $all_data = trim(file_get_contents('php://input'));//一个JSON
        $all_data = json_decode($all_data, true);
        $this->e3s_log('e3s_maintenance_product', 'success', $all_data);
        if (empty($all_data)) {
            return $this->setResponseError('error')->send();
        }
        $res = json_decode($all_data['msgBody']['result'], true);
        $array = $this->arraykeyToLower($res['DATA']);
        if (empty($array)) {
            return $this->setResponseError('error')->send();
        }
        $start_time = microtime(true);
        $update_no = date("YmdHis", time()) . rand(10000, 99999);
        $new_data['update_no'] = $update_no;
        $new_data['service_type'] = '保养套餐产品';
        $order = $this->maintenance_product($this->arraykeyToLower($array['ORDER']), $update_no);
        $part_item = $this->maintenance_product_car_series($this->arraykeyToLower($array['PRODUCT_ITEM']), $update_no);
        $new_data['update_type'] = 1;
        $new_data['new_add'] = $order['new_add'] + $part_item['new_add'];
        $new_data['new_update'] = $order['new_update'] + $part_item['new_update'];
        $new_data['new_del'] = $order['new_del'] + $part_item['new_del'];
        $end_time = microtime(true);
        $new_data['update_duration'] = $end_time - $start_time;
        $this->e3s_update($new_data);
//        $SendMailer = new SendMailer();
//        $SendMailer->send_mail(['type'=>8,'update_no'=>$update_no]);
        return $this->setResponseData('ok')->send();
    }

    //E3S套餐产品表
    private function maintenance_product($data, $order_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_maintenance_product = new E3sMaintenanceProduct();
        $e3s_maintenance_product_log = new E3sMaintenanceProductLog();
        $new_array = [];
        $new_del_array = [];
        $new_array_log = [];
        $k = 0;
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        $email = [];
        $k = 0;
        foreach ($data as $key=>$value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0; // 删除数据
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'product_type_id' => $value['product_type_id'] ?? '',
                'product_type_code' => $value['product_type_code'] ?? '',
                'product_type_name' => $value['product_type_name'] ?? '',
                'wi_qty' => $value['wi_qty'] ?? '',
                'discount' => $value['discount'] ?? '',
                'wi_code' => $value['wi_code'] ?? '',
                'service_year' => $value['service_year'] ?? '',
                'product_type_varieties_id' => $value['product_type_varieties_id'] ?? '',
                'oil' => $value['oil'] ?? '',
                'gp_type' => $value['gp_type'] ?? '',
                'is_ev' => $value['is_ev'] ?? '',
                'is_vehicle_age' => $value['is_vehicle_age'] ?? '',
                'e3s_update_no' => $order_no,
                'upgrade_type' =>$value['upgrade_type']
            ];
            $params['where']['product_type_id'] = $value['product_type_id'];
            $find = $e3s_maintenance_product->getOne($params);

            $new_array[$k] = [
                'product_type_id' => $value['product_type_id'] ?? '',
                'product_type_code' => $value['product_type_code'] ?? '',
                'product_type_name' => $value['product_type_name'] ?? '',
                'wi_qty' => $value['wi_qty'] ?? '',
                'discount' => $value['discount'] ?? '',
                'wi_code' => $value['wi_code'] ?? '',
                'service_year' => $value['service_year'] ?? '',
                'product_type_varieties_id' => $value['product_type_varieties_id'] ?? '',
                'oil' => $value['oil'] ?? '',
                'gp_type' => $value['gp_type'] ?? '',
                'is_ev' => $value['is_ev'] ?? '',
                'is_vehicle_age' => $value['is_vehicle_age'] ?? '',
                'e3s_update_no' => $order_no,
            ];

            if(!empty($find)){
                $new_array[$k]['id'] = $find['id'];
            }
            if ($update_status != 1){
                $new_array[$k]['is_enable'] = 0;
            }
            $k++;
            if ($k == 100) {
                try {
                    $e3s_maintenance_product->saveAll($new_array);
                    $e3s_maintenance_product_log->saveAll($new_array_log);
                } catch (Exception $exception) {
                    $this->e3s_log('e3s_maintenance_package', 'error', [
                        'type'=>'e3s_maintenance_package_0',
                        'e' => $exception->getMessage(),
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_array_log = [];
            }
        }
        try {
            $e3s_maintenance_product->saveAll($new_array);
            $e3s_maintenance_product_log->saveAll($new_array_log);
        } catch (Exception $exception) {
            $this->e3s_log('e3s_maintenance_package', 'error', [
                'type'=>'e3s_maintenance_package_1',
                'e' => $exception->getMessage()
            ]);
        }

        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    //E3S套餐产品 - 车型车系
    private function maintenance_product_car_series($data, $order_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_maintenance_product_car = new E3sMaintenanceProductCarSeries();
        $e3s_maintenance_product_car_log = new E3sMaintenanceProductCarSeriesLog();
        $new_array = [];
        $new_del_array = [];
        $new_array_log = [];
        $k = 0;
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        foreach ($data as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'cartype_id' => $value['cartype_id'] ?? '',
                'product_type_id' => $value['product_type_id'] ?? '',
                'service_car_type' => $value['service_car_type'] ?? '',
                'e3s_update_no' => $order_no,
                'upgrade_type' =>$value['upgrade_type']
            ];
            $params['where']['cartype_id'] = $value['cartype_id'];
            $find = $e3s_maintenance_product_car->getOne($params);
            if (!empty($find)){
                //删除套餐车系车型
                $e3s_maintenance_product_car->where(['cartype_id'=>$value['cartype_id']])->delete();
            }
            if ($update_status == 1) {
                $new_array[] = [
                    'cartype_id' => $value['cartype_id'] ?? '',
                    'product_type_id' => $value['product_type_id'] ?? '',
                    'service_car_type' => $value['service_car_type'] ?? '',
                    'e3s_update_no' => $order_no,
                    'is_enable' => 1,
                ];
            }else{
                if (empty($find)) {
                    $new_del_array[] = [
                        'cartype_id' => $value['cartype_id'] ?? '',
                        'product_type_id' => $value['product_type_id'] ?? '',
                        'service_car_type' => $value['service_car_type'] ?? '',
                        'e3s_update_no' => $order_no,
                        'is_enable' => 0
                    ];
                } else {
                    try {
                        $update_data = [
                            'cartype_id' => $value['cartype_id'] ?? '',
                            'product_type_id' => $value['product_type_id'] ?? '',
                            'service_car_type' => $value['service_car_type'] ?? '',
                            'e3s_update_no' => $order_no,
                            'is_enable' => 0
                        ];
                        $e3s_maintenance_product_car->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('maintenance_product_car_series', 'error', [
                            'type'=>'maintenance_product_car_series_0',
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 500) {
                try {
                    if(!empty($new_array)){
                        $e3s_maintenance_product_car->insertAll($new_array);
                    }
                    if(!empty($new_del_array)){
                        $e3s_maintenance_product_car->insertAll($new_del_array);
                    }
                    if(!empty($new_array_log)){
                        $e3s_maintenance_product_car_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('maintenance_product_car_series', 'error', [
                        'type'=>'maintenance_product_car_series_1',
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_del_array = [];
                $new_array_log = [];
            }
        }
        try {
            if (!empty($new_array)) {
                $e3s_maintenance_product_car->insertAll($new_array);
            }
            if (!empty($new_del_array)) {
                $e3s_maintenance_product_car->insertAll($new_del_array);
            }
            if (!empty($new_array_log)) {
                $e3s_maintenance_product_car_log->insertAll($new_array_log);
            }
        } catch (Exception $exception) {
            $this->e3s_log('maintenance_product_car_series', 'error', [
                'type'=>'maintenance_product_car_series_2',
                'data' => json_encode([]),
                'e' => $exception->getMessage()
            ]);
        }
        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    //18位码和车系的关系
    public function e3s_car_series()
    {
        $all_data = trim(file_get_contents('php://input'));//一个JSON
        $all_data = json_decode($all_data, true);
        $this->e3s_log('e3s_car_series', 'success', $all_data);
        if (empty($all_data)) {
            return $this->setResponseError('error')->send();
        }
        $res = json_decode($all_data['msgBody']['result'], true);
        $array = $this->arraykeyToLower($res['DATA']['ORDER']);
        if (empty($array)) {
            return $this->setResponseError('error')->send();
        }
        $e3s_car_series = new E3sCarSeries();
        $e3s_car_series_log = new E3sCarSeriesLog();
        $start_time = microtime(true);
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $update_no = date("YmdHis", time()) . rand(10000, 99999);
        $new_data['update_no'] = $update_no;
        $new_data['service_type'] = '车系18位码关系';

        $new_array_add = [];
        $new_array_del = [];
        $new_array_log = [];
        $k = 0;
        foreach ($array as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'car_series_code' => $value['car_series_code'] ?? '',
                'car_series_cn' => $value['car_series_cn'] ?? '',
                'car_config_code' => $value['car_config_code'] ?? '',
                'car_config_cn' => $value['car_config_cn'] ?? '',
                'supply_status_cn' => $value['supply_status_cn'] ?? '',
                'power_type_cn' => $value['power_type_cn'] ?? '',
                'base_series_code' => $value['base_series_code'] ?? '',
                'base_car_series_cn' => $value['base_car_series_cn'] ?? '',
                'service_car_type' => $value['service_car_type'] ?? '',
                'large_car_type_code' => $value['large_car_type_code'] ?? '',
                'large_car_type_cn' => $value['large_car_type_cn'] ?? '',
                'e3s_update_no' => $update_no,
                'car_brand_code' => $value['car_brand_code'],
                'car_brand_cn' => $value['car_brand_cn'],
                'upgrade_type' =>$value['upgrade_type']
            ];
            $params['where']['car_config_code'] = $value['car_config_code'];
            $find = $e3s_car_series->getOne($params);
//            if(in_array($value['car_series_code'],['FE0'])) {
            if($value['car_series_code'] == 'FE0') {
                $car_brand_code = 3;
            }else{
                $car_brand_code = $value['car_brand_code'];
            }
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array_add[] = [
                        'car_series_code' => $value['car_series_code'] ?? '',
                        'car_series_cn' => $value['car_series_cn'] ?? '',
                        'car_config_code' => $value['car_config_code'] ?? '',
                        'car_config_cn' => $value['car_config_cn'] ?? '',
                        'supply_status_cn' => $value['supply_status_cn'] ?? '',
                        'power_type_cn' => $value['power_type_cn'] ?? '',
                        'base_series_code' => $value['base_series_code'] ?? '',
                        'base_car_series_cn' => $value['base_car_series_cn'] ?? '',
                        'service_car_type' => $value['service_car_type'] ?? '',
                        'large_car_type_code' => $value['large_car_type_code'] ?? '',
                        'large_car_type_cn' => $value['large_car_type_cn'] ?? '',
                        'e3s_update_no' => $update_no,
                        'car_brand_code' => $car_brand_code,
                        'car_brand_cn' => $value['car_brand_cn'],
                    ];
                } else {
                    try {
                        $update_data = [
                            'car_series_code' => $value['car_series_code'] ?? '',
                            'car_series_cn' => $value['car_series_cn'] ?? '',
                            'car_config_code' => $value['car_config_code'] ?? '',
                            'car_config_cn' => $value['car_config_cn'] ?? '',
                            'supply_status_cn' => $value['supply_status_cn'] ?? '',
                            'power_type_cn' => $value['power_type_cn'] ?? '',
                            'base_series_code' => $value['base_series_code'] ?? '',
                            'base_car_series_cn' => $value['base_car_series_cn'] ?? '',
                            'service_car_type' => $value['service_car_type'] ?? '',
                            'large_car_type_code' => $value['large_car_type_code'] ?? '',
                            'large_car_type_cn' => $value['large_car_type_cn'] ?? '',
                            'e3s_update_no' => $update_no,
                            'car_brand_code' => $car_brand_code,
                            'car_brand_cn' => $value['car_brand_cn'],
                        ];
                        $e3s_car_series->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_car_series', 'error', [
                            'type'=>'e3s_car_series_0',
                            'data' => json_encode([]),
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }else{
                if (empty($find)) {
                    $new_array_del[] = [
                        'car_series_code' => $value['car_series_code'] ?? '',
                        'car_series_cn' => $value['car_series_cn'] ?? '',
                        'car_config_code' => $value['car_config_code'] ?? '',
                        'car_config_cn' => $value['car_config_cn'] ?? '',
                        'supply_status_cn' => $value['supply_status_cn'] ?? '',
                        'power_type_cn' => $value['power_type_cn'] ?? '',
                        'base_series_code' => $value['base_series_code'] ?? '',
                        'base_car_series_cn' => $value['base_car_series_cn'] ?? '',
                        'service_car_type' => $value['service_car_type'] ?? '',
                        'large_car_type_code' => $value['large_car_type_code'] ?? '',
                        'large_car_type_cn' => $value['large_car_type_cn'] ?? '',
                        'e3s_update_no' => $update_no,
                        'car_brand_code' => $car_brand_code,
                        'car_brand_cn' => $value['car_brand_cn'],
                        'is_enable' =>0
                    ];
                } else {
                    try {
                        $update_data = [
                            'car_series_code' => $value['car_series_code'] ?? '',
                            'car_series_cn' => $value['car_series_cn'] ?? '',
                            'car_config_code' => $value['car_config_code'] ?? '',
                            'car_config_cn' => $value['car_config_cn'] ?? '',
                            'supply_status_cn' => $value['supply_status_cn'] ?? '',
                            'power_type_cn' => $value['power_type_cn'] ?? '',
                            'base_series_code' => $value['base_series_code'] ?? '',
                            'base_car_series_cn' => $value['base_car_series_cn'] ?? '',
                            'service_car_type' => $value['service_car_type'] ?? '',
                            'large_car_type_code' => $value['large_car_type_code'] ?? '',
                            'large_car_type_cn' => $value['large_car_type_cn'] ?? '',
                            'e3s_update_no' => $update_no,
                            'car_brand_code' => $car_brand_code,
                            'car_brand_cn' => $value['car_brand_cn'],
                            'is_enable' =>0
                        ];
                        $e3s_car_series->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_car_series', 'error', [
                            'type'=>'e3s_car_series_1',
                            'data' => json_encode([]),
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array_add)){
                        $e3s_car_series->insertAll($new_array_add);
                    }
                    if(!empty($new_array_del)){
                        $e3s_car_series->insertAll($new_array_del);
                    }
                    if(!empty($new_array_log)){
                        $e3s_car_series_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('e3s_car_series', 'error', [
                        'type'=>'e3s_car_series_2',
                        'data' => json_encode([]),
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array_add = [];
                $new_array_del = [];
                $new_array_log = [];
            }
        }
        try {
            $new_data['update_type'] = 1;
            if (!empty($new_array_add)) {
                $e3s_car_series->insertAll($new_array_add);
            }
            if (!empty($new_array_del)) {
                $e3s_car_series->insertAll($new_array_del);
            }
            if (!empty($new_array_log)) {
                $e3s_car_series_log->insertAll($new_array_log);
            }
        } catch (Exception $exception) {
            $new_data['update_type'] = 0;
            $this->e3s_log('e3s_car_series', 'error', [
                'type'=>'e3s_car_series_3',
                'data' => json_encode([]),
                'e' => $exception->getMessage()
            ]);
        }
        $new_data['new_add'] = $new_add;
        $new_data['new_update'] = $new_update;
        $new_data['new_del'] = $new_del;
        $end_time = microtime(true);
        $new_data['update_duration'] = $end_time - $start_time;
        $this->e3s_update($new_data);
        $live_param = ['key' => 'cache_prefix.car_series', 'suffix' => '', 'set' => 'cache_prefix.car_series_set'];
        Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $live_param);
        return $this->setResponseData('ok')->send();
    }

    //pz1a产品表
    public function e3s_pz1a_maintenance_product()
    {

        $all_data = trim(file_get_contents('php://input'));//一个JSON
        $all_data = json_decode($all_data, true);
        $this->e3s_log('pz1a_product_kafka_data', 'success', $all_data);
        if (empty($all_data)) {
            return $this->setResponseError('error')->send();
        }
        $e3s_pz1a_package = new E3sPz1aMaintenancePackage();
        $e3s_pz1a_package_log = new E3sPz1aMaintenancePackageLog();
        $new_array = [];
        $new_array_log = [];
        $new_array_update = [];
        $res = json_decode($all_data['msgBody']['result'], true);
        $array = $this->arraykeyToLower($res['DATA']);
        if (empty($array)) {
            return $this->setResponseError('error')->send();
        }
        $start_time = microtime(true);
        $update_no = date("YmdHis", time()) . rand(10000, 99999);
        $new_data['update_no'] = $update_no;
        $new_data['service_type'] = 'PZ1A保养套餐';
        $new_add = 0;
        $new_del = 0;
        $k = 0;
        foreach ($array as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'sp_basic_id' => $value['sp_basic_id'] ?? '',
                'sp_basic_code' => $value['sp_basic_code'] ?? '',
                'sp_basic_name' => $value['sp_basic_name'] ?? '',
                'sp_basic_type' => $value['sp_basic_type'] ?? '',
                'maintain_total_count' => $value['maintain_total_count'] ?? '',
                'discount' => $value['discount'] ?? '',
                'is_wi_only' => $value['is_wi_only'] ?? '',
                'service_year' => $value['service_year'] ?? '',
                'belong_zone_code' => $value['belong_zone_code'] ?? '',
                'level_id' => $value['level_id'] ?? '',
                'level_name' => $value['level_name'] ?? '',
                'saler_amount' => $value['saler_amount'] ?? '',
                'car_brand_code' => $value['car_brand_code'] ?? '',
                'remark' => $value['remark'] ?? '',
                'e3s_update_no' => $update_no,
                'upgrade_type' => $value['upgrade_type'] ?? ''
            ];
            $params['where']['sp_basic_id'] = $value['sp_basic_id'];
            $find = $e3s_pz1a_package->getOne($params);
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array[] = [
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'sp_basic_code' => $value['sp_basic_code'] ?? '',
                        'sp_basic_name' => $value['sp_basic_name'] ?? '',
                        'sp_basic_type' => $value['sp_basic_type'] ?? '',
                        'maintain_total_count' => $value['maintain_total_count'] ?? '',
                        'discount' => $value['discount'] ?? '',
                        'is_wi_only' => $value['is_wi_only'] ?? '',
                        'service_year' => $value['service_year'] ?? '',
                        'belong_zone_code' => $value['belong_zone_code'] ?? '',
                        'level_id' => $value['level_id'] ?? '',
                        'level_name' => $value['level_name'] ?? '',
                        'saler_amount' => $value['saler_amount'] ?? '',
                        'car_brand_code' => $value['car_brand_code'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'e3s_update_no' => $update_no,
                    ];
                } else {
                    try {
                        $update_data = [
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'sp_basic_code' => $value['sp_basic_code'] ?? '',
                            'sp_basic_name' => $value['sp_basic_name'] ?? '',
                            'sp_basic_type' => $value['sp_basic_type'] ?? '',
                            'maintain_total_count' => $value['maintain_total_count'] ?? '',
                            'discount' => $value['discount'] ?? '',
                            'is_wi_only' => $value['is_wi_only'] ?? '',
                            'service_year' => $value['service_year'] ?? '',
                            'belong_zone_code' => $value['belong_zone_code'] ?? '',
                            'level_id' => $value['level_id'] ?? '',
                            'level_name' => $value['level_name'] ?? '',
                            'saler_amount' => $value['saler_amount'] ?? '',
                            'car_brand_code' => $value['car_brand_code'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'e3s_update_no' => $update_no,
                        ];
                        $e3s_pz1a_package->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_maintenance_package', 'error', [
                            'type'=>'e3s_maintenance_package_0',
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }else{
                if (empty($find)) {
                    $new_array_update[] = [
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'sp_basic_code' => $value['sp_basic_code'] ?? '',
                        'sp_basic_name' => $value['sp_basic_name'] ?? '',
                        'sp_basic_type' => $value['sp_basic_type'] ?? '',
                        'maintain_total_count' => $value['maintain_total_count'] ?? '',
                        'discount' => $value['discount'] ?? '',
                        'is_wi_only' => $value['is_wi_only'] ?? '',
                        'service_year' => $value['service_year'] ?? '',
                        'belong_zone_code' => $value['belong_zone_code'] ?? '',
                        'level_id' => $value['level_id'] ?? '',
                        'level_name' => $value['level_name'] ?? '',
                        'saler_amount' => $value['saler_amount'] ?? '',
                        'car_brand_code' => $value['car_brand_code'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'e3s_update_no' => $update_no,
                        'is_enable' => 0,
                    ];
                } else {
                    try {
                        $update_data = [
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'sp_basic_code' => $value['sp_basic_code'] ?? '',
                            'sp_basic_name' => $value['sp_basic_name'] ?? '',
                            'sp_basic_type' => $value['sp_basic_type'] ?? '',
                            'maintain_total_count' => $value['maintain_total_count'] ?? '',
                            'discount' => $value['discount'] ?? '',
                            'is_wi_only' => $value['is_wi_only'] ?? '',
                            'service_year' => $value['service_year'] ?? '',
                            'belong_zone_code' => $value['belong_zone_code'] ?? '',
                            'level_id' => $value['level_id'] ?? '',
                            'level_name' => $value['level_name'] ?? '',
                            'saler_amount' => $value['saler_amount'] ?? '',
                            'car_brand_code' => $value['car_brand_code'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'e3s_update_no' => $update_no,
                            'is_enable' => 0,
                        ];
                        $e3s_pz1a_package->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_maintenance_package', 'error', [
                            'type'=>'e3s_maintenance_package_1',
                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array)){
                        $e3s_pz1a_package->insertAll($new_array);
                    }
                    if(!empty($new_array_update)){
                        $e3s_pz1a_package->insertAll($new_array_update);
                    }
                    if(!empty($new_array_log)){
                        $e3s_pz1a_package_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('e3s_pz1a_maintenance_package', 'error', [
                        'type'=>'e3s_pz1a_maintenance_package_2',
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_array_update = [];
                $new_array_log = [];
            }
            try {
                $this->pz1a_package_part($this->arraykeyToLower($value['part_item']), $update_no);//基础套餐_备件信息
                $this->pz1a_package_wi($this->arraykeyToLower($value['work_item']), $update_no);//基础套餐_工时信息
                $this->pz1a_rule_item($this->arraykeyToLower($value['rule_item']), $update_no);//基础套餐_适用车型信息
            }catch (Exception $exception){
                $this->e3s_log('e3s_pz1a_maintenance_package', 'error', [
                    'type'=>'e3s_pz1a_maintenance_package_3',

                    'e' => $exception->getMessage()
                ]);
            }

        }

        try {
            $new_data['update_type'] = 1;
            if (!empty($new_array)) {
                $e3s_pz1a_package->insertAll($new_array);
            }
            if (!empty($new_array_update)) {
                $e3s_pz1a_package->insertAll($new_array_update);
            }
            if (!empty($new_array_log)) {
                $e3s_pz1a_package_log->insertAll($new_array_log);
            }
        } catch (Exception $exception) {
            $new_data['update_type'] = 0;
            $this->e3s_log('e3s_pz1a_maintenance_package', 'error', [
                'type'=>'e3s_pz1a_maintenance_package_4',
                'e' => $exception->getMessage()
            ]);
        }
        $new_data['new_add'] = $new_add;
        $new_data['new_update'] = 0;
        $new_data['new_del'] = $new_del;
        $end_time = microtime(true);
        $new_data['update_duration'] = $end_time - $start_time;
        $this->e3s_update($new_data);
        $SendMailer = new SendMailer();
//        $SendMailer->send_mail(['type'=>8,'update_no'=>$update_no]);
        return $this->setResponseData('ok')->send();
    }

    //基础套餐_备件信息
    private function pz1a_package_part($data,$update_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_pz1a_part = new E3sPz1aMaintenancePart();
        $e3s_pz1a_part_log = new E3sPz1aMaintenancePartLog();
        $new_array = [];
        $new_array_log = [];
        $new_array_update = [];
        $k = 0;
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        foreach ($data as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'sp_basic_part_id' => $value['sp_basic_part_id'] ?? '',
                'sp_basic_id' => $value['sp_basic_id'] ?? '',
                'part_no' => $value['part_no'] ?? '',
                'part_name' => $value['part_name'] ?? '',
                'part_qty' => $value['part_qty'] ?? '',
                'part_price' => $value['part_price'] ?? '',
                'maintain_count' => $value['maintain_count'] ?? '',
                'maintain_parttype' => $value['maintain_parttype'] ?? '',
                'pre_part_amount' => $value['pre_part_amount'] ?? '',
                'remark' => $value['remark'] ?? '',
                'e3s_update_no' => $update_no,
                'upgrade_type' => $value['upgrade_type'] ?? ''
            ];
            $params['where']['sp_basic_part_id'] = $value['sp_basic_part_id'];
            $find = $e3s_pz1a_part->getOne($params);
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array[] = [
                        'sp_basic_part_id' => $value['sp_basic_part_id'] ?? '',
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'part_name' => $value['part_name'] ?? '',
                        'part_qty' => $value['part_qty'] ?? '',
                        'part_price' => $value['part_price'] ?? '',
                        'maintain_count' => $value['maintain_count'] ?? '',
                        'maintain_parttype' => $value['maintain_parttype'] ?? '',
                        'pre_part_amount' => $value['pre_part_amount'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'e3s_update_no' => $update_no,
                    ];
                } else {
                    try {
                        $update_data = [
                            'sp_basic_part_id' => $value['sp_basic_part_id'] ?? '',
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'part_no' => $value['part_no'] ?? '',
                            'part_name' => $value['part_name'] ?? '',
                            'part_qty' => $value['part_qty'] ?? '',
                            'part_price' => $value['part_price'] ?? '',
                            'maintain_count' => $value['maintain_count'] ?? '',
                            'maintain_parttype' => $value['maintain_parttype'] ?? '',
                            'pre_part_amount' => $value['pre_part_amount'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'e3s_update_no' => $update_no,
                        ];
                        $e3s_pz1a_part->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_pz1a_part', 'error', [
                            'type'=>'e3s_pz1a_part_0',

                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }else{
                if (empty($find)) {
                    $new_array_update[] = [
                        'sp_basic_part_id' => $value['sp_basic_part_id'] ?? '',
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'part_name' => $value['part_name'] ?? '',
                        'part_qty' => $value['part_qty'] ?? '',
                        'part_price' => $value['part_price'] ?? '',
                        'maintain_count' => $value['maintain_count'] ?? '',
                        'maintain_parttype' => $value['maintain_parttype'] ?? '',
                        'pre_part_amount' => $value['pre_part_amount'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'e3s_update_no' => $update_no,
                        'is_enable' => 0,
                    ];
                } else {
                    try {
                        $update_data = [
                            'sp_basic_part_id' => $value['sp_basic_part_id'] ?? '',
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'part_no' => $value['part_no'] ?? '',
                            'part_name' => $value['part_name'] ?? '',
                            'part_qty' => $value['part_qty'] ?? '',
                            'part_price' => $value['part_price'] ?? '',
                            'maintain_count' => $value['maintain_count'] ?? '',
                            'maintain_parttype' => $value['maintain_parttype'] ?? '',
                            'pre_part_amount' => $value['pre_part_amount'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'e3s_update_no' => $update_no,
                            'is_enable' => 0,
                        ];
                        $e3s_pz1a_part->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_pz1a_part', 'error', [
                            'type'=>'e3s_pz1a_part_1',

                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array)){
                        $e3s_pz1a_part->insertAll($new_array);
                    }
                    if(!empty($new_array_update)){
                        $e3s_pz1a_part->insertAll($new_array_update);
                    }
                    if(!empty($new_array_log)){
                        $e3s_pz1a_part_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('e3s_pz1a_part', 'error', [
                        'type'=>'e3s_pz1a_part_2',

                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_array_update = [];
                $new_array_log = [];
            }
        }
        try {
            if (!empty($new_array)) {
                $e3s_pz1a_part->insertAll($new_array);
            }
            if (!empty($new_array_update)) {
                $e3s_pz1a_part->insertAll($new_array_update);
            }
            if (!empty($new_array_log)) {
                $e3s_pz1a_part_log->insertAll($new_array_log);
            }
        } catch (Exception $exception) {
            $this->e3s_log('e3s_pz1a_part', 'error', [
                'type'=>'e3s_pz1a_part_3',

                'e' => $exception->getMessage()
            ]);
        }
        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    //基础套餐_工时信息
    private function pz1a_package_wi($data,$update_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_pz1a_wi = new E3sPz1aMaintenanceWi();
        $e3s_pz1a_wi_log = new E3sPz1aMaintenanceWiLog();
        $new_array = [];
        $new_array_log = [];
        $new_array_update = [];
        $k = 0;
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        foreach ($data as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'sp_basic_wi_id' => $value['sp_basic_wi_id'] ?? '',
                'sp_basic_id' => $value['sp_basic_id'] ?? '',
                'wi_code' => $value['wi_code'] ?? '',
                'wi_name' => $value['wi_name'] ?? '',
                'wi_qty' => $value['wi_qty'] ?? '',
                'special_wi_flag' => $value['special_wi_flag'] ?? '',
                'wi_price' => $value['wi_price'] ?? '',
                'wi_discount' => $value['wi_discount'] ?? '',
                'wi_total_amount' => $value['wi_total_amount'] ?? '',
                'maintain_count_wi' => $value['maintain_count_wi'] ?? '',
                'part_no' => $value['part_no'] ?? '',
                'remark' => $value['remark'] ?? '',
                'e3s_update_no' => $update_no,
                'upgrade_type' => $value['upgrade_type'] ?? ''
            ];
            $params['where']['sp_basic_wi_id'] = $value['sp_basic_wi_id'];
            $find = $e3s_pz1a_wi->getOne($params);
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array[] = [
                        'sp_basic_wi_id' => $value['sp_basic_wi_id'] ?? '',
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'wi_code' => $value['wi_code'] ?? '',
                        'wi_name' => $value['wi_name'] ?? '',
                        'wi_qty' => $value['wi_qty'] ?? '',
                        'special_wi_flag' => $value['special_wi_flag'] ?? '',
                        'wi_price' => $value['wi_price'] ?? '',
                        'wi_discount' => $value['wi_discount'] ?? '',
                        'wi_total_amount' => $value['wi_total_amount'] ?? '',
                        'maintain_count_wi' => $value['maintain_count_wi'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'e3s_update_no' => $update_no,
                    ];
                } else {
                    try {
                        $update_data = [
                            'sp_basic_wi_id' => $value['sp_basic_wi_id'] ?? '',
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'wi_code' => $value['wi_code'] ?? '',
                            'wi_name' => $value['wi_name'] ?? '',
                            'wi_qty' => $value['wi_qty'] ?? '',
                            'special_wi_flag' => $value['special_wi_flag'] ?? '',
                            'wi_price' => $value['wi_price'] ?? '',
                            'wi_discount' => $value['wi_discount'] ?? '',
                            'wi_total_amount' => $value['wi_total_amount'] ?? '',
                            'maintain_count_wi' => $value['maintain_count_wi'] ?? '',
                            'part_no' => $value['part_no'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'e3s_update_no' => $update_no,
                        ];
                        $e3s_pz1a_wi->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_maintenance_package', 'error', [
                            'type'=>'e3s_maintenance_package_0',

                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }else{
                if (empty($find)) {
                    $new_array_update[] = [
                        'sp_basic_wi_id' => $value['sp_basic_wi_id'] ?? '',
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'wi_code' => $value['wi_code'] ?? '',
                        'wi_name' => $value['wi_name'] ?? '',
                        'wi_qty' => $value['wi_qty'] ?? '',
                        'special_wi_flag' => $value['special_wi_flag'] ?? '',
                        'wi_price' => $value['wi_price'] ?? '',
                        'wi_discount' => $value['wi_discount'] ?? '',
                        'wi_total_amount' => $value['wi_total_amount'] ?? '',
                        'maintain_count_wi' => $value['maintain_count_wi'] ?? '',
                        'part_no' => $value['part_no'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'e3s_update_no' => $update_no,
                        'is_enable' => 0,
                    ];
                } else {
                    try {
                        $update_data = [
                            'sp_basic_wi_id' => $value['sp_basic_wi_id'] ?? '',
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'wi_code' => $value['wi_code'] ?? '',
                            'wi_name' => $value['wi_name'] ?? '',
                            'wi_qty' => $value['wi_qty'] ?? '',
                            'special_wi_flag' => $value['special_wi_flag'] ?? '',
                            'wi_price' => $value['wi_price'] ?? '',
                            'wi_discount' => $value['wi_discount'] ?? '',
                            'wi_total_amount' => $value['wi_total_amount'] ?? '',
                            'maintain_count_wi' => $value['maintain_count_wi'] ?? '',
                            'part_no' => $value['part_no'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'e3s_update_no' => $update_no,
                            'is_enable' => 0,
                        ];
                        $e3s_pz1a_wi->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_pz1a_maintenance_wi', 'error', [
                            'type'=>'e3s_pz1a_maintenance_wi_0',

                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array)){
                        $e3s_pz1a_wi->insertAll($new_array);
                    }
                    if(!empty($new_array_update)){
                        $e3s_pz1a_wi->insertAll($new_array_update);
                    }
                    if(!empty($new_array_log)){
                        $e3s_pz1a_wi_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('e3s_pz1a_maintenance_wi', 'error', [
                        'type'=>'e3s_pz1a_maintenance_wi_1',

                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_array_update = [];
                $new_array_log = [];
            }
        }
        try {
            if (!empty($new_array)) {
                $e3s_pz1a_wi->insertAll($new_array);
            }
            if (!empty($new_array_update)) {
                $e3s_pz1a_wi->insertAll($new_array_update);
            }
            if (!empty($new_array_log)) {
                $e3s_pz1a_wi_log->insertAll($new_array_log);
            }
        } catch (Exception $exception) {
            $this->e3s_log('e3s_pz1a_maintenance_wi', 'error', [
                'type'=>'e3s_pz1a_maintenance_wi_2',

                'e' => $exception->getMessage()
            ]);
        }
        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    //基础套餐_适用车型信息
    private function pz1a_rule_item($data,$update_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_pz1a_package = new E3sPz1aMaintenanceCarSeries();
        $e3s_pz1a_package_log = new E3sPz1aMaintenanceCarSeriesLog();
        $new_array = [];
        $new_array_log = [];
        $new_array_update = [];
        $k = 0;
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        foreach ($data as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'cartype_id' => $value['cartype_id'] ?? '',
                'sp_basic_id' => $value['sp_basic_id'] ?? '',
                'service_car_type' => $value['service_car_type'] ?? '',
                'e3s_update_no' => $update_no,
                'upgrade_type' => $value['upgrade_type'] ?? ''
            ];
            $params['where']['cartype_id'] = $value['cartype_id'];
            $find = $e3s_pz1a_package->getOne($params);
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array[] = [
                        'cartype_id' => $value['cartype_id'] ?? '',
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'service_car_type' => $value['service_car_type'] ?? '',
                        'e3s_update_no' => $update_no,
                    ];
                } else {
                    try {
                        $update_data = [
                            'cartype_id' => $value['cartype_id'] ?? '',
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'service_car_type' => $value['service_car_type'] ?? '',
                            'e3s_update_no' => $update_no,
                        ];
                        $e3s_pz1a_package->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_maintenance_car_series', 'error', [
                            'type'=>'e3s_maintenance_car_series_0',

                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }else{
                if (empty($find)) {
                    $new_array_update[] = [
                        'cartype_id' => $value['cartype_id'] ?? '',
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'service_car_type' => $value['service_car_type'] ?? '',
                        'e3s_update_no' => $update_no,
                        'is_enable' => 0,
                    ];
                } else {
                    try {
                        $update_data = [
                            'cartype_id' => $value['cartype_id'] ?? '',
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'service_car_type' => $value['service_car_type'] ?? '',
                            'e3s_update_no' => $update_no,
                            'is_enable' => 0,
                        ];
                        $e3s_pz1a_package->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_maintenance_car_series', 'error', [
                            'type'=>'e3s_maintenance_car_series_1',

                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array)){
                        $e3s_pz1a_package->insertAll($new_array);
                    }
                    if(!empty($new_array_update)){
                        $e3s_pz1a_package->insertAll($new_array_update);
                    }
                    if(!empty($new_array_log)){
                        $e3s_pz1a_package_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('e3s_maintenance_car_series', 'error', [
                        'type' => 'maintenance_package_1',
                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_array_update = [];
                $new_array_log = [];
            }
        }
        try {
            if (!empty($new_array)) {
                $e3s_pz1a_package->insertAll($new_array);
            }
            if (!empty($new_array_update)) {
                $e3s_pz1a_package->insertAll($new_array_update);
            }
            if (!empty($new_array_log)) {
                $e3s_pz1a_package_log->insertAll($new_array_log);
            }
        } catch (Exception $exception) {
            $this->e3s_log('e3s_maintenance_car_series', 'error', [
                'type'=>'e3s_maintenance_car_series_2',

                'e' => $exception->getMessage()
            ]);
        }
        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    //pz1a套餐信息
    private function pz1a_package($data,$update_no)
    {
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $e3s_pz1a_package = new E3sPz1aMaintenancePackage();
        $e3s_pz1a_package_log = new E3sPz1aMaintenancePackageLog();
        $new_array = [];
        $new_array_log = [];
        $new_array_update = [];
        $k = 0;
        if (empty($data)) {
            return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
        }
        foreach ($data as $value) {
            if (!isset($value['upgrade_type'])) {
                $new_add += 1;
                $update_status = 1;
            } else {
                switch ($value['upgrade_type']) {
                    case 0:
                        $update_status = 0;
                        $new_del += 1;
                        break;
                    default:
                        $update_status = 1;
                        $new_add += 1;
                }
            }
            $new_array_log[] = [
                'sp_basic_id' => $value['sp_basic_id'] ?? '',
                'sp_basic_code' => $value['sp_basic_code'] ?? '',
                'sp_basic_name' => $value['sp_basic_name'] ?? '',
                'sp_basic_type' => $value['sp_basic_type'] ?? '',
                'maintain_total_count' => $value['maintain_total_count'] ?? '',
                'discount' => $value['discount'] ?? '',
                'is_wi_only' => $value['is_wi_only'] ?? '',
                'service_year' => $value['service_year'] ?? '',
                'belong_zone_code' => $value['belong_zone_code'] ?? '',
                'saler_amount' => $value['saler_amount'] ?? '',
                'car_brand_code' => $value['car_brand_code'] ?? '',
                'remark' => $value['remark'] ?? '',
                'e3s_update_no' => $update_no,
                'upgrade_type' => $value['upgrade_type'] ?? ''
            ];
            $params['where']['sp_basic_id'] = $value['sp_basic_id'];
            $find = $e3s_pz1a_package->getOne($params);
            if ($update_status == 1) {
                if (empty($find)) {
                    $new_array[] = [
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'sp_basic_code' => $value['sp_basic_code'] ?? '',
                        'sp_basic_name' => $value['sp_basic_name'] ?? '',
                        'sp_basic_type' => $value['sp_basic_type'] ?? '',
                        'maintain_total_count' => $value['maintain_total_count'] ?? '',
                        'discount' => $value['discount'] ?? '',
                        'is_wi_only' => $value['is_wi_only'] ?? '',
                        'service_year' => $value['service_year'] ?? '',
                        'belong_zone_code' => $value['belong_zone_code'] ?? '',
                        'saler_amount' => $value['saler_amount'] ?? '',
                        'car_brand_code' => $value['car_brand_code'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'e3s_update_no' => $update_no,
                    ];
                } else {
                    try {
                        $update_data = [
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'sp_basic_code' => $value['sp_basic_code'] ?? '',
                            'sp_basic_name' => $value['sp_basic_name'] ?? '',
                            'sp_basic_type' => $value['sp_basic_type'] ?? '',
                            'maintain_total_count' => $value['maintain_total_count'] ?? '',
                            'discount' => $value['discount'] ?? '',
                            'is_wi_only' => $value['is_wi_only'] ?? '',
                            'service_year' => $value['service_year'] ?? '',
                            'belong_zone_code' => $value['belong_zone_code'] ?? '',
                            'saler_amount' => $value['saler_amount'] ?? '',
                            'car_brand_code' => $value['car_brand_code'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'e3s_update_no' => $update_no,
                        ];
                        $e3s_pz1a_package->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_maintenance_package', 'error', [
                            'type'=>'e3s_maintenance_package_01',

                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }else{
                if (empty($find)) {
                    $new_array_update[] = [
                        'sp_basic_id' => $value['sp_basic_id'] ?? '',
                        'sp_basic_code' => $value['sp_basic_code'] ?? '',
                        'sp_basic_name' => $value['sp_basic_name'] ?? '',
                        'sp_basic_type' => $value['sp_basic_type'] ?? '',
                        'maintain_total_count' => $value['maintain_total_count'] ?? '',
                        'discount' => $value['discount'] ?? '',
                        'is_wi_only' => $value['is_wi_only'] ?? '',
                        'service_year' => $value['service_year'] ?? '',
                        'belong_zone_code' => $value['belong_zone_code'] ?? '',
                        'saler_amount' => $value['saler_amount'] ?? '',
                        'car_brand_code' => $value['car_brand_code'] ?? '',
                        'remark' => $value['remark'] ?? '',
                        'e3s_update_no' => $update_no,
                        'is_enable' => 0,
                    ];
                } else {
                    try {
                        $update_data = [
                            'sp_basic_id' => $value['sp_basic_id'] ?? '',
                            'sp_basic_code' => $value['sp_basic_code'] ?? '',
                            'sp_basic_name' => $value['sp_basic_name'] ?? '',
                            'sp_basic_type' => $value['sp_basic_type'] ?? '',
                            'maintain_total_count' => $value['maintain_total_count'] ?? '',
                            'discount' => $value['discount'] ?? '',
                            'is_wi_only' => $value['is_wi_only'] ?? '',
                            'service_year' => $value['service_year'] ?? '',
                            'belong_zone_code' => $value['belong_zone_code'] ?? '',
                            'saler_amount' => $value['saler_amount'] ?? '',
                            'car_brand_code' => $value['car_brand_code'] ?? '',
                            'remark' => $value['remark'] ?? '',
                            'e3s_update_no' => $update_no,
                            'is_enable' => 0,
                        ];
                        $e3s_pz1a_package->saveData($update_data, ['id' => $find['id']]);
                    } catch (Exception $exception) {
                        $this->e3s_log('e3s_maintenance_package', 'error', [
                            'type'=>'e3s_maintenance_package_02',

                            'e' => $exception->getMessage()
                        ]);
                    }
                }
            }
            $k++;
            if ($k == 100) {
                try {
                    if(!empty($new_array)){
                        $e3s_pz1a_package->insertAll($new_array);
                    }
                    if(!empty($new_array_update)){
                        $e3s_pz1a_package->insertAll($new_array_update);
                    }
                    if(!empty($new_array_log)){
                        $e3s_pz1a_package_log->insertAll($new_array_log);
                    }
                } catch (Exception $exception) {
                    $this->e3s_log('e3s_pz1a_maintenance_package', 'error', [
                        'type'=>'e3s_pz1a_maintenance_package_03',

                        'e' => $exception->getMessage()
                    ]);
                }
                $k = 0;
                $new_array = [];
                $new_array_update = [];
                $new_array_log = [];
            }
        }
        try {
            if (!empty($new_array)) {
                $e3s_pz1a_package->insertAll($new_array);
            }
            if (!empty($new_array_update)) {
                $e3s_pz1a_package->insertAll($new_array_update);
            }
            if (!empty($new_array_log)) {
                $e3s_pz1a_package_log->insertAll($new_array_log);
            }
        } catch (Exception $exception) {
            $this->e3s_log('e3s_pz1a_maintenance_package', 'error', [
                'type'=>'e3s_pz1a_maintenance_package_04',

                'e' => $exception->getMessage()
            ]);
        }
        return ['new_add' => $new_add, 'new_update' => $new_update, 'new_del' => $new_del];
    }

    //KV养护品 start
    public function e3s_specific_relation()
    {
        $all_data = trim(file_get_contents('php://input'));//一个JSON
        $all_data = json_decode($all_data, true);
        $this->e3s_log('e3s_specific_relation','success',$all_data);
        if (empty($all_data)) {
            return $this->setResponseError('error')->send();
        }
        $res = json_decode($all_data['msgBody']['result'], true);
        $array = $this->arraykeyToLower($res);
        if (empty($array)) {
            return $this->setResponseError('error')->send();
        }
        $start_time = microtime(true);
        $new_add = 0;
        $new_update = 0;
        $new_del = 0;
        $update_no = date("YmdHis", time()) . rand(10000, 99999);
        $new_data['update_no'] = $update_no;
        $new_data['service_type'] = 'KV养护品';
        $new_array_add = [];
        $new_array_log = [];
        foreach ($array as $key=>$value) {
            $new_array_log[] = [
                'spec_part_group_id' => $value['spec_part_group_id'] ?? '',
                'spec_part_group' => $value['spec_part_group'] ?? '',
                'group_type' => $value['group_type'] ?? '',
                'group_type_name' => $value['group_type_name'] ?? '',
                'remark' => $value['remark'] ?? '',
                'e3s_update_no' => $update_no,
                'is_enable' => $value['is_enable'] ?? 1,
            ];
            $find = $this->e3s_specific_relation_model->where(['spec_part_group_id' => $value['spec_part_group_id']])->find();
            if(empty($find)){
                $new_add +=1;
                $new_array_add[] = [
                    'spec_part_group_id' => $value['spec_part_group_id'] ?? '',
                    'spec_part_group' => $value['spec_part_group'] ?? '',
                    'group_type' => $value['group_type'] ?? '',
                    'group_type_name' => $value['group_type_name'] ?? '',
                    'remark' => $value['remark'] ?? '',
                    'e3s_update_no' => $update_no,
                    'is_enable' => $value['is_enable'] ?? 1,
                ];
            }else{
                $new_array_add[] = [
                    'id' => $find['id'],
                    'spec_part_group_id' => $value['spec_part_group_id'] ?? '',
                    'spec_part_group' => $value['spec_part_group'] ?? '',
                    'group_type' => $value['group_type'] ?? '',
                    'group_type_name' => $value['group_type_name'] ?? '',
                    'remark' => $value['remark'] ?? '',
                    'e3s_update_no' => $update_no,
                    'is_enable' => $value['is_enable'] ?? 1,
                ];
                $new_update += 1;
            }
            $this->e3s_specific_relation_dlr($this->arraykeyToLower($value['dlr_item']),$value['spec_part_group_id'],$update_no);
            $this->e3s_specific_relation_part($this->arraykeyToLower($value['part_item']),$value['spec_part_group_id'],$update_no);
        }
        $new_data['new_add'] = $new_add;
        $new_data['new_update'] = $new_update;
        $new_data['new_del'] = $new_del;
        try {
            $new_data['update_type'] = 1;
            if(!empty($new_array_add)){
                $this->e3s_specific_relation_model->saveAll($new_array_add);
            }
            $this->e3s_specific_relation_log_model->saveAll($new_array_log);
        } catch (Exception $exception) {
            $new_data['update_type'] = 0;
            $this->e3s_log('e3s_specific_relation', 'error', [
                'type'=>'e3s_specific_relation_0',

                'e' => $exception->getMessage()
            ]);
        }
        $new_data['new_add'] = $new_add;
        $new_data['new_update'] = $new_update;
        $new_data['new_del'] = $new_del;
        $end_time = microtime(true);
        $new_data['update_duration'] = $end_time - $start_time;
        $this->e3s_update($new_data);
        return $this->setResponseData('ok')->send();
    }

    private function e3s_specific_relation_dlr($data,$spec_part_group_id,$update_no)
    {
        $this->e3s_specific_dlr_model->where(['spec_part_group_id'=>$spec_part_group_id])->update(['is_enable'=>0]);//全部伪删除
        if(!empty($data)){
            $new_array_add = [];
            $new_array_log = [];
            foreach ($data as $key=>$value)
            {
                $find = $this->e3s_specific_dlr_model->where(['spec_part_dlr_id'=>$value['spec_part_dlr_id']])->find();
                $new_array_add[$key]['spec_part_dlr_id'] = $value['spec_part_dlr_id'];
                $new_array_add[$key]['spec_part_group_id'] = $value['spec_part_group_id'];
                $new_array_add[$key]['dlr_id'] = $value['dlr_id'] ?? '';
                $new_array_add[$key]['dlr_code'] = $value['dlr_code'] ?? '';
                $new_array_add[$key]['dlr_short_name'] = $value['dlr_short_name'] ?? '';
                $new_array_add[$key]['car_brand_code'] = $value['car_brand_code'] ?? '';
                $new_array_add[$key]['is_enable'] = $value['is_enable'] ?? 1;
                $new_array_add[$key]['e3s_update_no'] = $update_no;
                if(!empty($find)){
                    $new_array_add[$key]['id'] = $find['id'];
                }
                $new_array_log[] = [
                    'spec_part_dlr_id' => $value['spec_part_dlr_id'],
                    'spec_part_group_id' => $value['spec_part_group_id'],
                    'dlr_id' => $value['dlr_id'] ?? '',
                    'dlr_code' => $value['dlr_code'] ?? '',
                    'dlr_short_name' => $value['dlr_short_name'] ?? '',
                    'car_brand_code' => $value['car_brand_code'] ?? '',
                    'is_enable' => $value['is_enable'] ?? 1,
                    'e3s_update_no' => $update_no,
                ];

            }
            try {
                if(!empty($new_array_add)){
                    $this->e3s_specific_dlr_model->saveAll($new_array_add);
                }
                $this->e3s_specific_dlr_log_model->saveAll($new_array_log);
            } catch (Exception $exception) {
                $this->e3s_log('e3s_specific_relation', 'error', [
                    'type'=>'e3s_specific_relation_01',

                    'e' => $exception->getMessage()
                ]);
            }
        }
        return true;
    }

    private function e3s_specific_relation_part($data,$spec_part_group_id,$update_no)
    {
        $this->e3s_specific_part_model->where(['spec_part_group_id'=>$spec_part_group_id])->update(['is_enable'=>0]);//全部伪删除
        if(!empty($data)){
            $new_array_add = [];
            $new_array_log = [];
            foreach ($data as $key=>$value)
            {
                $find = $this->e3s_specific_part_model->where(['spec_part_group_d_id'=>$value['spec_part_group_d_id']])->find();
                $new_array_add[$key]['spec_part_group_d_id'] = $value['spec_part_group_d_id'];
                $new_array_add[$key]['spec_part_group_id'] = $value['spec_part_group_id'];
                $new_array_add[$key]['part_id'] = $value['part_id'] ?? '';
                $new_array_add[$key]['part_no'] = $value['part_no'] ?? '';
                $new_array_add[$key]['part_name'] = $value['part_name'] ?? '';
                $new_array_add[$key]['remark'] = $value['remark'] ?? '';
                $new_array_add[$key]['is_enable'] = $value['is_enable'] ?? 1;
                $new_array_add[$key]['e3s_update_no'] = $update_no;
                if(!empty($find)){
                    $new_array_add[$key]['id'] = $find['id'];
                }
                $new_array_log[] = [
                    'spec_part_group_d_id' => $value['spec_part_group_d_id'],
                    'spec_part_group_id' => $value['spec_part_group_id'],
                    'part_id' => $value['part_id'] ?? '',
                    'part_no' => $value['part_no'] ?? '',
                    'part_name' => $value['part_name'] ?? '',
                    'remark' => $value['remark'] ?? '',
                    'is_enable' => $value['is_enable'] ?? 1,
                    'e3s_update_no' => $update_no
                ];

            }
            try {
                if(!empty($new_array_add)){
                    $this->e3s_specific_part_model->saveAll($new_array_add);
                }
                $this->e3s_specific_part_log_model->saveAll($new_array_log);
            } catch (Exception $exception) {
                $this->e3s_log('e3s_specific_relation', 'error', [
                    'type'=>'e3s_specific_relation_02',

                    'e' => $exception->getMessage()
                ]);
            }
        }
        return true;
    }
    //KV养护品 end


    /**
     * 延保产品获取
     */
    public function e3s_yb_product()
    {
        $all_data = trim(file_get_contents('php://input'));//一个JSON
        $all_data = json_decode($all_data, true);
        $this->e3s_log('e3s_yb_product', 'success', $all_data);
        if (empty($all_data)) {
            return $this->setResponseError('error')->send();
        }
        $data = $all_data['msgBody']['guaranteeFeeList'] ?? [];
        $data = json_decode($data,true);
        $e3s_service = new E3sService();
        $re = $e3s_service->save($data);
        if ($re) {
            return $this->setResponseData('保存成功')->send();
        } else {
            return $this->setResponseError('保存失败')->send();
        }

    }
}
