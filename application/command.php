<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

return [
    'app\active\command\Test',
    'app\active\command\Zxt',
    'app\active\command\Yang',
    # 更新商品sku表的备件分类
    'app\admin_v2\command\commodity\AddVarietyCode',

    # 同步联友支付方式
    'app\active\command\LyPayWay',
    # 订单结算修复
    'app\active\command\OrderSettle',

    #官威商品全量索引
    'app\admin_v2\command\ReindexH5Card',

    #车生活商品全量索引
    'app\admin_v2\command\Reindex',

    #批量处理计划任务
    'app\common\lib\Crond',

    #积分兑换,用户id
    'app\net_small\command\migrate\PointsUserId',
    'app\net_small\command\migrate\UserSubUnionId',

    #官微=>车生活 数据迁移, 顺序执行
    'app\net_small\command\migrate\CommodityShelvesSources',#2
    'app\net_small\command\migrate\CommoditySet',           #3
    'app\net_small\command\migrate\Card',           #4
    'app\net_small\command\migrate\DlrCarData',           #4

    #GWSC=>GWSM 上线
    'app\net_small\command\migrate\UpOnline',


    #基础数据
    'app\admin_v2\command\market\base\Area',
    'app\admin_v2\command\market\base\Dlr',
    'app\admin_v2\command\migrate\MarketBase',
    'app\admin_v2\command\migrate\MarketComment',
    'app\admin_v2\command\migrate\MarketUser',

    'app\net_small\command\migrate\ImgHttps',

    #评价成长值
    'app\net_small\command\migrate\CommentGrowthValue',
    #订单成长值
    'app\net_small\command\migrate\OrderGrowthValue',
    #购物车成长值
    'app\net_small\command\migrate\CartGrowthValue',

    #更新sku
    'app\admin_v2\command\ReSku',

    #cip发券
    'app\admin_v2\command\market\base\CipCard',

    'app\net_small\command\OrderBd',

];
