<?php


namespace app\admin_v2\queue;


use app\common\model\db\DbCard;
use app\common\service\ActivityCardService;
use think\queue\Job;
use tool\Logger;

class NewCardCommodity extends Base
{
    public function fire(Job $job, $data)
    {
        Logger::info('NewCardCommodity:', $data);
        $data = json_decode($data, true);

        try {
            $service = new ActivityCardService();

                // 后台关联保存
                if ($data['relevance_type'] == 1) {
                    $cardId  = $data['data']['card_id'];
                    if (!empty(array_filter($data['data']['list']))) {
                        $result = $service->saveCardCommodity($data['data'], $data['admin_name']);
                    } else {
                        $relevancy_way = $data['data']['relevancy_way'];
                        // 关联商品为空
                        $result = $service->clearCardCommodity($relevancy_way,$cardId,$data['admin_name']);
                    }

                } else {
                    $cardId = $data['card_id'];
                    // 卡券通知保存
                    if (!empty(array_filter($data['data']))) {
                        $result = $service->notifierRelevance($data['card_id'], $data['notifier_type'], $data['data'], $data['admin_name']);
                    } else {
                        // 关联商品为空
                        $card_model = new DbCard();
                        $relevancy_way = $card_model->where('id', $cardId)->value('relevancy_type');
                        $result = $service->clearCardCommodity($relevancy_way,$cardId,$data['admin_name']);
                    }
                }
            $this->logDb([
                'queue'       => 'new_card_commodity',
                'source_type' => $cardId, // 卡券id
                'data_info'   => json_encode($data),
                'result_info' => json_encode($result),
            ]);
            $job->delete();

        } catch (\Exception $e) {
            $msg = $e->getMessage();
            Logger::error('NewCardCommodity : ' . $msg);
            $job->attempts() > $this->_attempts ? $job->delete() : $job->release($this->_release_time);
        }
        trace('500,end NewCardCommodity');
    }
}