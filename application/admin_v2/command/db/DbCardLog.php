<?php


namespace app\admin_v2\command\db;

use app\common\command\Base;
use think\Db;
use tool\Logger;

class DbCardLog extends Base
{
    public static function doIt()
    {
        Logger::debug('cron db card log start');
        static::loadConfig();
        $monthArr = ['03', '06', '09', '12'];
        $month = date('m');

        // 每3个月执行一次
        if (!in_array($month, $monthArr)) {
            return ;
        }
        // 每月1号执行
        $day = date('d');
        if ($day != '01') {
            return;
        }

        // 备份表
        $backTableName = 't_db_card_log_back';
        $sql           = "SHOW TABLES LIKE '" . $backTableName . "'";
        $isTableExist  = Db::query($sql);
        if (!empty($isTableExist)) {
            // 表存在
            // 删除备份表
            echo "表 {$backTableName} 存在。\n";
            $sql = "DROP TABLE IF EXISTS `$backTableName`";
            try {
                $result = Db::execute($sql);
                dump($result);
                if ($result === 0) {
                    echo "表 {$backTableName} 已经被成功删除。\n";
                } else {
                    echo "删除表 {$backTableName} 失败。\n";
                }
            } catch (\Exception $e) {
                echo "删除表 {$backTableName} 时发生错误：" . $e->getMessage() . "\n";
            }
        } else {
            // 表不存在
            echo "表 {$backTableName} 不存在。\n";
        }
        // 日志表
        $tableName = 't_db_card_log';

        try {
            // 构造SQL语句  重命名
            $sql = "RENAME TABLE `$tableName` TO `$backTableName`";

            $result = Db::execute($sql);
            dump($result);
            if ($result === 0) {
                echo "表重命名成功，从 {$tableName} 重命名为 {$backTableName} \n";
            } else {
                echo "表重命名失败 \n";
            }

        } catch (\PDOException $e) {
            echo "表重命名时发生数据库错误：" . $e->getMessage() . "\n";
        } catch (\Exception $e) {
            echo "表重命名时发生其他错误：" . $e->getMessage() . "\n";
        }

        try {
            // 创建表的SQL语句
            $sql    = "CREATE TABLE `t_db_card_log` (
              `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
              `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
              `event_type` varchar(32) NOT NULL DEFAULT '' COMMENT '推送类型：1领券,2核销, 3失效, 4复活, 5冻结, 6解冻, 7返核销, 8激活并冻结, 9返激活, 10返核销返激活, 11更换冻结业务单号',
              `is_success` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否成功 0否1是',
              `card_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '卡券id',
              `request_info` varchar(500) NOT NULL DEFAULT '' COMMENT '接口请求信息',
              `response_info` varchar(500) NOT NULL DEFAULT '' COMMENT '接口返回信息',
              `is_enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用 0否1是',
              `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `creator` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
              `last_updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              `modifier` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
              PRIMARY KEY (`id`),
              KEY `index_user_id` (`user_id`) USING BTREE,
              KEY `index_event_type` (`event_type`) USING BTREE,
              KEY `index_card_id` (`card_id`) USING BTREE
            ) ENGINE=InnoDB  COMMENT='卡券中心推送记录表';";
            $result = Db::execute($sql);
            if ($result === 0) {
                echo "表创建成功。\n";
            } else {
                echo "表创建失败。\n";
            }
        } catch (\Exception $e) {
            echo "创建表时发生错误：" . $e->getMessage() . "\n";
        }

    }
}