<?php

namespace app\admin_v2\command\db;

use app\common\command\Base;
use think\Db;
use tool\Logger;

class DbJobLog extends Base
{
    public static function doIt()
    {
        Logger::debug('cron db jobs log start');
        static::loadConfig();
        $monthArr = ['01', '03', '05', '07', '09', '11'];

        $month = date('m');

        // 每2个月执行一次
        if (!in_array($month, $monthArr)) {
            return ;
        }
        // 每月1号执行
        $day = date('d');
        if ($day != '01') {
            return;
        }

        // 备份表
        $backTableName = 't_db_jobs_log_back';
        $sql           = "SHOW TABLES LIKE '" . $backTableName . "'";
        $isTableExist  = Db::query($sql);
        if (!empty($isTableExist)) {
            // 表存在
            // 删除备份表
            echo "表 {$backTableName} 存在。\n";
            $sql = "DROP TABLE IF EXISTS `$backTableName`";
            try {
                $result = Db::execute($sql);
                dump($result);
                if ($result === 0) {
                    echo "表 {$backTableName} 已经被成功删除。\n";
                } else {
                    echo "删除表 {$backTableName} 失败。\n";
                }
            } catch (\Exception $e) {
                echo "删除表 {$backTableName} 时发生错误：" . $e->getMessage() . "\n";
            }
        } else {
            // 表不存在
            echo "表 {$backTableName} 不存在。\n";
        }
        // 队列表
        $tableName = 't_db_jobs_log';

        try {
            // 构造SQL语句  重命名
            $sql = "RENAME TABLE `$tableName` TO `$backTableName`";

            $result = Db::execute($sql);
            dump($result);
            if ($result === 0) {
                echo "表重命名成功，从 {$tableName} 重命名为 {$backTableName} \n";
            } else {
                echo "表重命名失败 \n";
            }

        } catch (\PDOException $e) {
            echo "表重命名时发生数据库错误：" . $e->getMessage() . "\n";
        } catch (\Exception $e) {
            echo "表重命名时发生其他错误：" . $e->getMessage() . "\n";
        }

        try {
            // 创建表的SQL语句
            $sql    = "CREATE TABLE `t_db_jobs_log` (
              `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
              `queue` varchar(32) NOT NULL DEFAULT '' COMMENT '队列名称',
              `source_type` varchar(100) NOT NULL DEFAULT '' COMMENT '执行类型,标识符',
              `data_info` text NOT NULL COMMENT '执行内容',
              `result_info` text NOT NULL COMMENT '返回结果',
              `is_enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否有效:0否,1是',
              `creator` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
              `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间,申请时间',
              `modifier` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人',
              `last_updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
              PRIMARY KEY (`id`),
                KEY `index_queue` (`queue`) USING BTREE,
                KEY `index_source_type` (`source_type`) USING BTREE
            ) ENGINE=InnoDB  COMMENT='队列日志表';";
            $result = Db::execute($sql);
            if ($result === 0) {
                echo "表创建成功。\n";
            } else {
                echo "表创建失败。\n";
            }
        } catch (\Exception $e) {
            echo "创建表时发生错误：" . $e->getMessage() . "\n";
        }


    }
}