<?php


namespace app\admin_v2\command\early_warning;


use app\common\command\Base;
use app\common\model\bu\BuEarlyWarningInform;
use app\common\model\bu\BuEarlyWarningPrice;
use app\common\model\db\DbCommodityJdSku;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbJdSkuInfo;
use app\common\model\db\DbSpecValue;
use app\common\net_service\SendMailer;
use think\Config;
use think\Exception;
use think\Log;
use tool\NewPhpExcel;

class Price extends Base
{


    /**
     * @return BuEarlyWarningInform|int|void
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    public static function doIt()
    {
        static::loadConfig();
        Config::load(ROOT_PATH . 'config/net_small/config.php');
        Config::load(ROOT_PATH . 'config/net_small/' . config('app_status') . '.php');

        try {
            trace('cron price early warning start');


            // 查询t_db_commodity_jd_sku
            $jd_sku_model = new DbCommodityJdSku();
            $where        = ['is_enable' => 1, 'shelf_type' => DbCommodityJdSku::SHELF_TYPE_1];

            $skuIdArr = $jd_sku_model->where($where)->column('sku_id');
            if (empty($skuIdArr)) {
                return;
            }

            $model = new DbJdSkuInfo();
            $map   = [
                'a.opdate'    => date('Ymd'),
                'a.is_enable' => 1,
                'a.sku_id'    => ['in', $skuIdArr],
            ];
            $map[] = ['exp', "a.price<>b.price"];

            $map_b = 'a.sku_id= b.sku_id and b.opdate =' . date('Ymd', strtotime("-1 day")) . ' and b.is_enable = 1 ';

            $field              = 'a.sku_id,a.price,a.jd_state,a.created_date,b.price as b_price,b.jd_state as b_jd_state,b.created_date as b_created_date,
            c.commodity_id,c.sku_code,c.sp_value_list,d.commodity_name,d.comm_type_id,e.shelves_type,e.is_enable as set_status,set_sku.price as set_sku_price';
            $list               = $model->alias('a')
                ->join('t_db_jd_sku_info b', $map_b, 'left')
                ->join('t_db_commodity_sku c', 'a.sku_id = c.sku_code and c.is_enable = 1', 'left')
                ->join('t_db_commodity d', 'c.commodity_id = d.id', 'left')
                ->join('t_db_commodity_set e', 'd.id = e.commodity_id', 'left')
                ->join('t_db_commodity_set_sku set_sku', 'e.id = set_sku.commodity_set_id and set_sku.is_enable = 1
', 'left')
                ->field($field)
                ->where($map)
                ->select();
            $arr                = DbCommoditySet::$shelves_type;
            $price_model        = new BuEarlyWarningPrice();
            $specValueModel     = new DbSpecValue();
            $commodityTypeModel = new DbCommodityType();

            foreach ($list as $key => $item) {
                // 插入2条数据
                $add = [
                    'commodity_id'           => $item['commodity_id'],
                    'commodity_name'         => $item['commodity_name'],
                    'store'                  => $arr[$item['shelves_type']] ?? '',
                    'comm_type_name'         => '',
                    'second_comm_type_name'  => '',
                    'thirdly_comm_type_name' => '',
                    'sku_code'               => $item['sku_code'],
                    'sp_value_name'          => '',
                    'price'                  => $item['set_sku_price'] ?? '',
                    'set_status'             => $item['set_status'] ?? '',
                ];

                $sku_value     = '';
                $sp_value_list = $specValueModel->getAllList(['a.id' => ['in', $item['sp_value_list']]]);
                if ($sp_value_list) {
                    foreach ($sp_value_list as $vv) {
                        $sku_value .= $vv['sp_name'] . ":" . $vv['sp_value_name'] . ", ";
                    }
                }
                $add['sp_value_name'] = $sku_value;
                // 商品分类
                $commodityType = $commodityTypeModel->getSubParent($item['comm_type_id']);
                if (!empty($commodityType)) {
                    $add['comm_type_name']         = $commodityType['pparent_type_name'];
                    $add['second_comm_type_name']  = $commodityType['parent_type_name'];
                    $add['thirdly_comm_type_name'] = $commodityType['comm_type_name'];
                }
                $add1 = [
                    'jd_sku_code' => $item['sku_id'],
                    'jd_price'    => $item['price'],
                    'jd_state'    => $item['jd_state'],
                    'opdate'      => $item['created_date'],
                ];
                $add2 = [
                    'jd_sku_code' => $item['sku_id'],
                    'jd_price'    => $item['b_price'],
                    'jd_state'    => $item['b_jd_state'],
                    'opdate'      => $item['b_created_date'],
                ];
                $price_model->insert(array_merge($add, $add1));
                $price_model->insert(array_merge($add, $add2));

            }
            $num = $price_model->whereTime('created_date', 'd')->count();
            if ($num > 0) {
                // 发送邮件
                return self::createExcel();
            } else {
                return 0;
            }
            trace('cron price early warning start');
        } catch (Exception $e) {
            Log::error('price early warning' . $e->getMessage());
        }
    }


    /**
     * 创建excel
     * @return BuEarlyWarningInform
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Writer_Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private static function createExcel()
    {
        $price_model = new BuEarlyWarningPrice();
        $list        = $price_model->whereTime('created_date', 'd')->select();
        $data        = [];
        foreach ($list as $key => $item) {
            $data[] = [
                'a' => $item['commodity_id'],
                'b' => $item['commodity_name'],
                'c' => $item['store'],
                'd' => $item['comm_type_name'],
                'e' => $item['second_comm_type_name'],
                'f' => $item['thirdly_comm_type_name'],
                'g' => $item['sku_code'],
                'h' => $item['sp_value_name'],
                'i' => $item['price'],
                'j' => $item['set_status'],
                'k' => $item['jd_sku_code'],
                'l' => $item['jd_price'],
                'm' => BuEarlyWarningPrice::$jd_state_arr[$item['jd_state']],
                'n' => $item['opdate'],
            ];
        }

        $titleArr  = [
            'A' => '商品id', 'B' => '商品名称', 'C' => '商城名称', 'D' => '一级分类', 'E' => '二级分类',
            'F' => '三级分类', 'G' => '商品sku', 'H' => '商品sku名称', 'I' => '商品sku价格', 'J' => '上架状态',
            'K' => 'JD商品sku', 'L' => 'JD慧采价', 'M' => 'JD商品状态', 'N' => '采集时间'
        ];
        $widthArr  = [
            'A' => '30', 'B' => '30', 'C' => '30', 'D' => '30', 'E' => '30', 'F' => '30', 'G' => '30',
            'H' => '30', 'I' => '30', 'J' => '30', 'K' => '30', 'L' => '30', 'M' => '30', 'N' => '30'
        ];
        $sheetTile = '商品采购预警';
        $filename  = date('Y-m-d') . $sheetTile . '.xls';
        $path      = NewPhpExcel::export_xls($data, $titleArr, $widthArr, $filename, $sheetTile);
        $model     = new BuEarlyWarningInform();
        $map       = ['warning_type' => BuEarlyWarningInform::WARNING_TYPE_3, 'is_enable' => 1];
        $upd       = ['file_path' => $path, 'modified_date' => date('Y-m-d H:i:s')];
        return $model->where($map)->update($upd);
    }


    /**
     * 发送邮件
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function sendMailer()
    {
        static::loadConfig();
        Config::load(ROOT_PATH . 'config/net_small/config.php');
        Config::load(ROOT_PATH . 'config/net_small/' . config('app_status') . '.php');
        // 查询库存预警设置
        $inform_model = new BuEarlyWarningInform();
        $map          = ['warning_type' => BuEarlyWarningInform::WARNING_TYPE_3, 'is_enable' => 1];
        $info         = $inform_model->where($map)->find();
        if (empty($info)) {
            return;
        }

        // 判断当前天是否已发送
        $day = date('Y-m-d');
        if ($day == $info['inform_date']) {
            return;
        }

        // 判断时间点
        if ($info['inform_hour'] != date('H') || empty($info['file_path'])) {
            return;
        }
        $service = new SendMailer();
        $mail    = [
            'type'  => 11,
            'email' => explode(',', $info['email']),
            'file'  => $info['file_path']
        ];
        $re      = $service->send_mail($mail);
        if ($re) {
            $upd = ['inform_date' => $day, 'modified_date' => date('Y-m-d H:i:s')];
            $inform_model->where('id', $info['id'])->update($upd);
        }
    }

}