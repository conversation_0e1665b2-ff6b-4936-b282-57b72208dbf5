<?php

namespace app\admin_v2\behavior\base;

use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbCommoditySub;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbLog;
use app\common\port\connectors\Bdp;
use think\Exception;
use think\Hook;
use think\Log;
use think\Model;
use think\Queue;
use tool\Logger;

/**
 *
 * Class ReindexFlat
 * @package app\admin_v2\behavior\base
 */
class ReindexFlat
{
    /**
     * @param $item
     * @return bool
     */
    public function run(&$item)
    {
        Logger::debug("start reindex flat item");
        try {
            //删除flat表对应数据,然后加入一条
            $flat      = new DbCommodityFlat();
            $flat_item = $flat->getOne(['where' => ['commodity_id' => $item['commodity_id'], 'shelves_type' => $item['shelves_type']]]);
            if(in_array($item['commodity_id'],[5993,5994,5333])){
                (new DbLog())->insertData(
                    [
                        'type'         => 'reindex_flat',
                        'is_success'   => 'reindex_flat_' . $item['commodity_id'] ,
                        'send_note'    => json_encode($flat_item),
                        'receive_note' => '',
                    ]
                );
            }
            DbCommodityFlat::where('commodity_id', $item['commodity_id'])
                ->where('shelves_type', $item['shelves_type'])->delete();
            $commodity = DbCommodity::where('is_enable', 1)->where('id', $item['commodity_id'])->find();
            if (empty($commodity)) {
                return false;
            }
            if (!empty($item['is_delete'])) {

                //下架通知给到bdp
                if(!empty($commodity['arrival_bdp'])){
                    $as_datas['type'] = 'down';
                    $as_datas['item'] = $item;
                    $as_datas['flat_new'] = $flat_item;
                    Queue::push('app\common\queue\CommodityBdp', json_encode($as_datas), config('queue_type.e3s_kafka'));
//                    $this->bdp('down', $item, $flat_item);
                }
                return true;
            }

            $commodity_set = DbCommoditySet::where('id', $item['commodity_set_id'])->find();

            if (empty($commodity_set)) {
                return false;
            }
            $up_stat = 0;
            // 下架
            if(empty($commodity_set['latest_listing_time']) || time() < strtotime($commodity_set['latest_listing_time'])){
                DbCommoditySet::where(['id'=>$item['commodity_set_id']])->update(['is_enable'=>0]);
                return false;
            }else{
                // 上架
                if($commodity_set['is_enable']==0){
                    DbCommoditySet::where(['id'=>$item['commodity_set_id']])->update(['is_enable'=>1,'modifier'=>'r_f713']);
                    $up_stat=1;
                }else{
                    //如果不需要更新则跳出
//                    return false;
                }
                $up_stat=1;
            }

            if(!empty($commodity_set['latest_end_time'])){
                if(time() > strtotime($commodity_set['latest_end_time'])){
                    DbCommoditySet::where(['id'=>$item['commodity_set_id']])->update(['is_enable'=>0]);
                    return false;
                }
            }

            $activity_image = '';
            if(!empty($commodity_set['activity_image'])){
                if(!empty($commodity_set['activity_start_time'])){
                    if(time() > strtotime($commodity_set['activity_start_time']) && time() < strtotime($commodity_set['activity_end_time']) ){
                        $activity_image = $commodity_set['activity_image'];
                    }
                    // 结束时间为空代表永久
                    if (time() > strtotime($commodity_set['activity_start_time']) && empty($commodity_set['activity_end_time'])) {
                        $activity_image = $commodity_set['activity_image'];
                    }
                }
            }

            if (!empty($item['group_commodity_ids_info'])) {
                $commodity_low_price_price = 0;
                $up_down_low_price_price   = 0;

                // 选中的最低价格，如果全选或者全不选，就是所有子上品的上架的最低价
                $group_info = json_decode($item['group_commodity_ids_info'], true);
                if (!empty($group_info)) {
                    foreach ($group_info as $one_info) {
                        $commodity_low_price_price += min(array_column($one_info['sku_list'], 'price'));
                        $up_down_low_price_price   += min(array_column($one_info['sku_list'], 'price'));
                    }
                }

            } else {
                //商品上架最低价
                $up_down_low_price = DbCommoditySetSku::where('is_enable', 1)
                    ->where('commodity_set_id', $item['commodity_set_id'])
                    ->order('price', 'asc')
                    ->find();
                if (empty($up_down_low_price)) {
                    return false;
                }
                //商品管理最低价
                $commodity_low_price = DbCommoditySku::where('is_enable', 1)
                    ->where('commodity_id', $item['commodity_id'])
                    ->order('price', 'asc')
                    ->find();
                if (empty($commodity_low_price)) {
                    return false;
                }
                $commodity_low_price_price = $commodity_low_price['price'];
                $up_down_low_price_price   = $up_down_low_price['price'];
            }

            $commodity_type_key = 'tmp_type_key_' . $commodity['comm_type_id'];
            $commodity_type     = redis($commodity_type_key);

            if (empty($commodity_type)) {
                $commodity_type = (new DbCommodityType())->getSubParent($commodity['comm_type_id']);
                redis($commodity_type_key, $commodity_type, 180);
            }

            if (empty($commodity_type)) {
                return false;
            }
            $item['commodity_low_price_price'] = $commodity_low_price_price;
            $item['up_down_low_price_price'] = $up_down_low_price_price;
            $as_data['type'] = 1;
            $as_data['data'] = $item;
            $as_data['commodity_set'] = $commodity_set;
            $as_data['commodity'] = $commodity;
            $as_data['commodity_type'] = $commodity_type;
//            Queue::push('app\common\queue\CommodityFlat', json_encode($as_data), config('queue_type.e3s_kafka'));
            //获取所有sku的 relate_car_18n, relate_car_ids , 用 set_sku表到数据是考虑到组合商品
            $n18_str    = $car_rel_str = '';
//            $relate_car = DbCommoditySetSku::where('commodity_id', $item['commodity_id'])
//                ->where('is_enable',1)
//                ->field('relate_car_18n,relate_car_ids')
//                ->select();
//            if (!empty($relate_car)) {
//                $n18     = implode(',', array_column($relate_car, 'relate_car_18n'));
//                $n18_arr = array_unique(explode(',', $n18));
//                $n18_str = implode(',', $n18_arr);
//
//                $car_rel     = implode(',', array_column($relate_car, 'relate_car_ids'));
//                $car_rel_arr = array_unique(explode(',', $car_rel));
//                $car_rel_str = implode(',', $car_rel_arr);
//            }

            $tag      = empty($commodity_set['commodity_attr']) ? '' : $commodity_set['commodity_attr'];
            $flat_model  = new DbCommodityFlat();

            $flat_new = $flat_model->insertData([
                'commodity_name'        => $commodity['commodity_name'],
                'commodity_id'          => $item['commodity_id'],
                'commodity_set_id'      => $item['commodity_set_id'],
                'comm_type_id'          => $commodity['comm_type_id'],
                'car_series_id'         => $commodity['car_series_id'],
                'commodity_class'       => $commodity['commodity_class'],
                'tag'                   => $tag,
                'tag_gwnet'             => $tag,
                'tag_gwapp'             => $tag,
                'tag_pz1asm'            => $tag,
                'tag_pz1aapp'           => $tag,
                'tag_qcapp'             => $tag,
                'tag_qcsm'              => $tag,
                'cover_image'           => $commodity['cover_image'],
                'price'                 => $commodity_low_price_price,
                'final_price'           => $up_down_low_price_price,
                'count_stock'           => $commodity_set['count_stock'],
                'sort_order'            => $commodity['sort'],
                'updated_at'            => $commodity_set['last_updated_date'] ?? date("Y-m-d H:i:s"), #用于排序
                'created_date'          => date('Y-m-d H:i:s'),
                'creator'               => 'r_i_flat',
                'modifier'              => $item['modifier'] ??'r_i_f',
                'up_down_channel_name'  => $commodity_set['up_down_channel_name'],
                'up_down_channel_dlr'   => $commodity_set['up_down_channel_dlr'],
                'commodity_dlr_type_id' => $commodity_set['commodity_dlr_type_id'],
                'shelves_type'          => $commodity_set['shelves_type'],
                'is_pure'               => $commodity['is_pure'],
                'sales_channel'         => $commodity_set['sales_channel'], #销售渠道
                'comm_type_id_str'      => $commodity_type->pparent_type_id . ',' . $commodity_type->parent_type_id . ','
                    . $commodity_type->id,
                'pay_dlr_code'          => $commodity_set['pay_dlr_code'],
                'is_shop'               => $commodity['is_shop'],
                'pay_style'             => $commodity_set['pay_style'],#支付方式
                'is_grouped'            => $commodity['is_grouped'],#是否组合商品
                'dd_commodity_type'     => $commodity['dd_commodity_type'],#到店商品类型
                'relate_car_18n'        => $n18_str,
                'relate_car_ids'        => $car_rel_str,
                'machine_oil_type'      => $commodity['machine_oil_type'],# 油型
                'max_point'             => $commodity_set['max_point'],
                'activity_image'        => $activity_image ?? '',
            ]);

            // 组合商品
            if ($commodity['is_grouped']) {
                $this->groupCommodity($item['commodity_id'], $item['commodity_set_id'], $item['group_commodity_ids_info']);
            }


            try {
                $list = DbCommoditySet::where('shelves_type', $commodity_set['shelves_type'])
                    ->where('commodity_id', $item['commodity_id'])
                    ->select();
//                    ->chunk(10, function ($arr) {
                        foreach ($list as $set_info) {
                            $set_info['commodity_set_id'] = $set_info['id'];
                            $param                        = collection($set_info->getData())->toArray();
                            unset($param['id']);
                            $param['modifier'] = $item['modifier'] ?? 'reindex_flat';
                            Hook::listen('flat_activity', $param);
                        }
//                    });
            }catch (Exception $exception){
                Log::error('behavior_commodity_flat_hook: ' . $exception->getMessage());
            }

            //上架通知给到bdp,如果推送，才推送
//            && $up_stat==1
            if (!empty($commodity['arrival_bdp'])  ) {
//                $this->bdp('up', $item);
                $as_data['type'] = 'up';
                $as_data['item'] = [
                    'commodity_id' => $item['commodity_id'],
                    'shelves_type' => $item['shelves_type']
                ];
                Queue::push('app\common\queue\CommodityBdp', json_encode($as_data), config('queue_type.e3s_dlr'));
            }
            return true;
        } catch (\Exception $e) {
            if(in_array($item['commodity_id'],[5993,5994,5333])){
                (new DbLog())->insertData(
                    [
                        'type'         => 'reindex_flat',
                        'is_success'   => 'reindex_flat_' . $item['commodity_id'] ,
                        'send_note'    => json_encode($e->getMessage()),
                        'receive_note' => '',
                    ]
                );
            }
            Log::error("reindex_flat_item_error : " . $e->getMessage());
        }
        Logger::debug("end reindex flat item");
    }



    public function groupCommodity($commId, $commSetId, $groupCommInfo)
    {
        if (empty($groupCommInfo)) {
            return ;
        }
        $groupCommInfoArr = json_decode($groupCommInfo, true);
        // 删除当前组合商品
        $comm_sub_model = new DbCommoditySub();
        $comm_sub_model->where(['commodity_id'=>$commId])->delete();

        $data = [];
        foreach ($groupCommInfoArr as $item) {
            $data[] = [
                'commodity_id' => $commId,
                'commodity_set_id' => $commSetId,
                'group_sub_commodity_id' => $item['commodity_id'],
                'group_sub_commodity_set_id' => $item['commodity_set_id'],
            ];
        }

        $comm_sub_model->insertAll($data);

    }

}

