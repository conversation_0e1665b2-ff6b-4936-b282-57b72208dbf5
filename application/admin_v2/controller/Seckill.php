<?php


namespace app\admin_v2\controller;

use app\admin_v2\service\RecommendActivityService;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCommodityTypeSegmentDiscount;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbGift;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSeckillCommodity;
use app\common\model\db\DbSystemValue;
use app\common\service\BaseDataService;
use app\common\service\CommodityService;
use think\Hook;
use think\Log;
use think\Model;
use app\common\model\db\DbCard;

/**
 * 秒杀控制器
 * Class Seckill
 * @package app\admin_v2\controller
 */
class Seckill extends Common
{
    private $dbSeckil;
    private $dbSeckilCommodity;
    private $comm_type_model;
    private $dbDlr;
    private $gather_list;

    public function __construct()
    {
        parent::__construct();
        $this->dbSeckil          = new DbSeckill();
        $this->dbSeckilCommodity = new DbSeckillCommodity();
        $this->comm_type_model   = new DbCommodityType();
        $this->dbDlr             = new DbDlr();
        $model = new DbSystemValue();
        $this->gather_list = $model->where(['value_type'=>26,'is_enable'=>1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('gather_list', $this->gather_list);
        $this->assign('admin_type', $this->admin_info['type']);
    }

    //秒杀活动列表
    public function live()
    {
        $status = input('get.status');  //1未开始 2 进行中 3已结束
        $is_enable = input('get.is_enable');
        $title  = input('get.title');
        $date   = input('get.date');
        $seckill_type = input('get.seckill_type');
        $gather_id = input('get.gather_id',0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name = input('get.theme_name');

        $where     = [];
        $live_type = input('get.live_type');
        if (empty($live_type)) {
            $set_type                       = 5;
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($live_type == 2) {
            $set_type = 7;
            $where[]  = [['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], 'or'];
        } else {
            $set_type = 6;
            $where[]  = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }

        if (!empty($date)) {
            $date = explode(' ~ ', $date);
            if (count($date) == 2) {
                $where['a.start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($date[0])), date('Y-m-d H:i:s', strtotime($date[1]) + 86400)]];
            }
        }

        if (!empty($title)) {
            $where['a.title'] = ['like', "%{$title}%"];
        }


        if (!empty($is_enable)) {
            if ($is_enable == 1) {
                $where['a.is_enable'] = 1;
            }else{
                $where['a.is_enable'] = 0;
            }
        }
        if (!empty($status)) {
            $where['a.act_status'] = $status;
        }

        if (!empty($seckill_type)) {
            $where['a.seckill_type'] = $seckill_type;
        }
        if(!empty($gather_id)) {
            $where['a.gather_id'] = $gather_id;
        }
        if(!empty($is_pv_subsidy)) {
            if($is_pv_subsidy != 2){
                $where['a.is_pv_subsidy'] = $is_pv_subsidy;
            }else{
                $where['a.is_pv_subsidy'] = 0;
            }
        }
        if(!empty($theme_name)) {
            $where['a.theme_name'] = array('like','%'.$theme_name.'%');
        }

        $field               = 'a.id,a.title,a.created_dlr_code,a.start_time,a.end_time,a.is_enable,a.up_down_channel_name,
        a.up_down_channel_dlr,a.seckill_type,a.day_start_time,a.day_end_time,a.gather_id,a.is_pv_subsidy,a.theme_name,a.is_enable';
        $where['a.set_type'] = $set_type;
        $params              = [
            'where' => $where,
            'query' => input('get.'),
            'order' => 'a.id DESC',
            'field' => $field,
        ];
        $gather_array = [];
        foreach ($this->gather_list as $value){
            $gather_array[$value['id']] = $value['name'];
        }
        $list = $this->dbSeckil->getSeckillList($set_type, $params);
        foreach ($list as $key => $val) {
            // 重复秒杀
            if ($val['seckill_type'] == 2) {
                $list[$key]['start_time'] = date('Y-m-d ', strtotime($val['start_time'])).$val['day_start_time'];
                $list[$key]['end_time'] = date('Y-m-d ', strtotime($val['end_time'])).$val['day_end_time'];
            }
            $in = empty($val['is_enable']) || $status == 4;
            if (strtotime($val['start_time']) > time()) {
                $list[$key]['status'] = $in ? '已关闭' : '未开始';
            } else if (strtotime($val['start_time']) <= time() && strtotime($val['end_time']) >= time()) {
                $list[$key]['status'] = $in ? '已关闭' : '进行中';
            } else if (strtotime($val['end_time']) < time()) {
                $list[$key]['status'] = $in ? '已关闭' : '已结束';
            }
            if($val['gather_id'] == 0 || empty($val['gather_id'])){
                $list[$key]['gather_name'] = '-';
            }else{
                if(isset($gather_array[$val['gather_id']])){
                    $list[$key]['gather_name'] = $gather_array[$val['gather_id']];
                }else{
                    $list[$key]['gather_name'] = '-';
                }
            }
            $list[$key]['is_pv_subsidy_status'] = $val['is_pv_subsidy'] == 1 ?'是': '否';
            $list[$key]['is_enable_status'] = $val['is_enable'] == 1 ?'是': '否';

        }
        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('status', $status);
        $this->assign('is_enable', (int)$is_enable);
        $this->assign('set_type', $set_type);
        $this->assign('live_type', $live_type);
        return $this->fetch('live');
    }

    public function addLive()
    {
        $setType = input('get.set_type', 5);
        $nvi_code = '';
        if($setType == 5){
            $nvi_code = 'N';
        }
        if($setType == 7){
            $nvi_code = 'V';
        }
        $user_level = [];
        if($setType == 5 || $setType == 7){
            $user_level = (new DbSystemValue())->getNameListByCode(25,$nvi_code);
        }
        $arr = ['county_name'=>'非会员','value_code'=>'NONE','order_no'=>0];
        array_unshift($user_level,$arr);
        $this->assign('user_level',$user_level);
        $this->assign('nvi_code',$nvi_code);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', (new DbSystemValue())->getNameList(20));
        $this->assign('set_type', $setType);
        $commodityClass = DbCommodity::commodityClass();
        $this->assign('commodity_class', $commodityClass);
        $this->assign('live_type', input('live_type'));
        return $this->fetch('add_live');
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetLiveCommodityList()
    {
        $commodity_name  = trim(input('commodity_name'));
        $top_type        = input('comm_parent_id');
        $second_type     = input('sub_comm_type_id');
        $third_type      = input('three_comm_type_id');
        $seckill_id      = input('seckill_id');
        $commodity_class = input('commodity_class');
        $live_type       = input('live_type');
        $user_segment    = input('user_segment');

        $where = [
            'b.listing_type' => 1,
        ];

        $brand = 1;
        if (empty($live_type)) {
            $where['b.shelves_type']        = 5;
            $where['b.qsc_group']           = '';  // 取送车服务包不能参加
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($live_type == 2) {
            $brand = 2;
            $where['b.shelves_type'] = 7;
            $where[]                 = [['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], 'or'];
        } else {
            $brand = 3;
            $where['b.shelves_type'] = 6;
            $where[]                 = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
//        $where['a.cheap_dis'] = ''; // 参加优惠套装商品不参与秒杀活动

        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        $type_id = 0;

        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

        if (!empty($commodity_class)) {
            $where['a.commodity_class'] = $commodity_class;
        }

//        $where['commodity_class'] = 1;

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }
//        $where['a.count_stock'] = array('>', 0);
        // $where['b.shelves_type'] = 5;//车生活id
        $field      = "b.commodity_dlr_type_id,a.commodity_class,comm_type_id,a.up_down_channel_name,a.commodity_id,a.commodity_set_id,commodity_name,cover_image,a.price,b.count_stock";
        $params     = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );
        $start_time = input('start_time');
        $end_time   = input('end_time');
        $flat       = new DbCommodityFlat();
        $list       = $flat->getCommodityList($params);
        $date       = date('Y-m-d H:i:s');
        if (empty($start_time)) {
            $start_time = $date;
        }
        if (empty($end_time)) {
            $end_time = $date;
        }
        $where_time = ['start_time'=>$start_time, 'end_time'=>$end_time];

        //是否 限时折扣
        $list = (new DbLimitDiscount())->getIsLimitProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ], $where_time);
//        dd($list);
        //判断是否已经参加了预售
        $list = (new DbPreSale())->getIsPreProduct($list, [
            'a.is_enable'  => ['=', 1],
            //            'a.front_s_time'   => ['<',$end_time],
            //            'a.balance_e_time'   => ['>',$start_time],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);

        //判断是否已经参加了满优惠
        $map = [
            'a.is_enable' => 1,
            'a.set_type'  => $where['b.shelves_type'],
            'act_status'  => ['in', [1,2]]
        ];
        $list = (new DbFullDiscount())->getIsFullDiscount($list, $map, $where_time);

        //判断是否已经参加了拼团
        $list = (new DbFightGroup())->getIsFightGroupProduct($list, [
            'a.is_enable'  => ['=', 1],
            //            'a.start_time'   => ['<',$end_time],
            //            'a.end_time'   => ['>',$start_time],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);
        //是否NN
        $map = [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in',[1,2]],
        ];
        $list = (new DbNDiscount())->getIsNDiscount($list, $map, $where_time);

        //判断是否已经参加秒杀
        $map = [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]],
            'a.id'         => ['<>', empty($seckill_id) ? 0 : $seckill_id],
        ];
        $list = (new DbSeckill())->getIsSeckillProduct($list,$map, $where_time);

        //判断是否参加买赠
        $list = (new DbGift())->getIsGiftProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ], $where_time);

        //是否套装
        $list = (new BuCheapSuitIndex())->getIsCheap($list, [
            'a.is_enable'  => ['=', 1],
            'a.act_status' => ['in', [1, 2]],
//            'a.start_time' => $where_start_time ?? ['<', $date],
//            'a.end_time'   => $where_end_time ?? ['>', $date],
            'a.type'   => $where['b.shelves_type'],
        ]);


        $segment_discount_type_model = new DbCommodityTypeSegmentDiscount();
        // 判断商品分类的定向人群优惠
        foreach ($list as $key => $item) {
            $re = $segment_discount_type_model->getSegmentByCommTypeId($item->comm_type_id, $brand);
            $list[$key]['is_user_segment'] = 0; // 商品没有设置定向人群

            if (!is_null($re)) {
                if ($re['user_segment'] == $user_segment) {
                    $list[$key]['is_user_segment'] = 1; // 定向人群一致
                } else {
                    $list[$key]['is_user_segment'] = 2; // 定向人群不一致
                }
            }
        }



        $res         = [];
        $res['list'] = $list;
        print_json(0, '', $res);
    }

    public function getSkuLive()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $comm_service     = new CommodityService();
        $commodity_flat   = new DbCommodityFlat();
        $sku              = $comm_service->getSetSku($commodity_set_id);
        $sku_list         = $sku['sku_list'];
        $commodity_row    = $commodity_flat->getOne([
            'where'        => ['commodity_id' => $commodity_id, 'commodity_set_id' => $commodity_set_id],
            'field'        => 'commodity_name,cover_image,comm_type_id',
            'shelves_type' => 5
        ]);

        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (empty($val['sp_value_arr'])) {
                continue;
            }
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    /**
     * 保存数据
     */
    public function saveLive()
    {
        $post = input('post.');
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $start_time          = input('post.start_time');
        $end_time            = input('post.end_time');
        $discount            = input('post.discount', 0);
        $dis_money           = input('post.dis_money');
        $seckill_money       = input('post.seckill_money');
        $dis_type            = input('post.dis_type', 0);
        $home                = input('post.home');
        $card_available      = input('post.card_available', 0);
        $gather_id = input('gather_id',0);
        $is_pv_subsidy = input('is_pv_subsidy',0);
        $theme_name = input('theme_name');
        $up_down_channel_arr = $post['up_down_channel'] ?? [];
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $up_down_channel_arr),
            input('post.dlr_code', '')
        );
        // 定向人群
        $user_segment = input('post.user_segment');
        $user_segment_options = input('post.user_segment_options');
        $dis_info = $_POST['dis_info'];

        if ($dis_type == 2) {
            $discount = $dis_money;
        }
        if ($dis_type == 3) {
            $discount = $seckill_money;
        }
        // 非定向人群
        if (!$user_segment) {
            if (!$discount && $dis_type != 0) {
                print_json(1, '折扣不能为空');
            }
        }
        $purchase_number = input('post.purchase_number');      //限购数量
        $des             = input('post.des');
        $is_enable       = input('post.is_enable', '1');
        $id              = input('post.id');
        $seckill_type    = input('post.seckill_type', 1); // 秒杀类型
        if ($seckill_type == 1) {
            if ($start_time > $end_time) print_json(1, '开始时间不能大于结束时间');
        }
        $sku_list   = json_decode($_POST['sku_list'],true) ?? [];
        if (empty($sku_list)) {
            print_json(1, '商品规格不能为空');
        }
        $set_type   = input('post.set_type');
        $act_status = $this->dbSeckil->getActStatus($start_time, $end_time);
        $purchase_commodity_scope = input('post.purchase_commodity_scope',0);
        $purchase_activity_scope  = input('post.purchase_activity_scope',0);
        $day_start_time           = input('post.day_start_time');
        $day_end_time             = input('post.day_end_time');

        // 重复秒杀
        if ($seckill_type == 2) {
            $start_time = input('repetition_start_time');
            $end_time = input('repetition_end_time');
            $repetition_start_time = input('repetition_start_time').' '.$day_start_time;
            $repetition_end_time = input('repetition_end_time').' '.$day_end_time;
            if ($repetition_start_time > $repetition_end_time) {
                print_json(1, '开始时间不能大于结束时间');
            }
            $act_status = $this->dbSeckil->getActStatus($repetition_start_time, $repetition_end_time);
        }


        $data                     = [
            'title'                    => $title,
            'start_time'               => $start_time,
            'end_time'                 => $end_time,
            'tag'                      => $post['tag'],
            'act_status'               => $act_status,
            'discount'                 => $discount,
            'purchase_number'          => $purchase_number,
            'is_enable'                => $is_enable,
            'des'                      => $des,
            'creator'                  => $this->admin_info['username'],
            'created_dlr_code'         => $this->admin_info['dlr_code'],
            'set_type'                 => $set_type,
            'dis_type'                 => $dis_type,
            'card_available'           => $card_available,
            'up_down_channel_name'     => implode(',', $post['up_down_channel_name'] ?? []),
            'up_down_channel_dlr'      => $up_down_channel_dlr,
            'discount_type'            => $post['discount_type'] ?? 1,
            'e3s_activity_id'          => $post['e3s_activity_id'] ?? 0,
            'activity_type'            => $post['activity_type'] ?? 0,
            'settlement_rule_id'       => $post['settlement_rule_id'],
            'settlement_rule_name'     => $post['settlement_rule'],
            'settlement_rule_type'     => $post['settlement_rule_type'],
            'settlement_rule_value'    => $post['settlement_rule_value'],
            'act_sett_standard'        => $post['act_sett_standard'],
            'rel_card_ids'             => $post['rel_card_ids'] ?? '',
            'seckill_type'             => $seckill_type,
            'day_start_time'           => $day_start_time,
            'day_end_time'             => $day_end_time,
            'purchase_commodity_scope' => $purchase_commodity_scope,
            'purchase_activity_scope'  => $purchase_activity_scope,
            'gather_id'                => $gather_id,
            'is_pv_subsidy'            => $is_pv_subsidy,
            'theme_name'               => $theme_name,
        ];
        $nvi_code = 'N';

        if($set_type == 5){
            $nvi_code = 'N';
        }
        if($set_type == 7){
            $nvi_code = 'V';
        }
        if ($user_segment == 1) {
            // 会员级
            $data['user_segment'] = $user_segment;
            $user_level = (new DbSystemValue())->getNameListByCode(25,$nvi_code);
            $user_segment_options_arr = [];
            $user_segment_order_no = '';

            // 非会员
            if ($user_segment_options == 'NONE') {
                $user_segment_order_no = 0;
                $user_segment_options_arr = ['NONE'];
            }
            foreach ($user_level as $key => $val) {
                if ($val['value_code'] == $user_segment_options) {
                    $user_segment_order_no = $val['order_no'];
                }
                if ((is_numeric($user_segment_order_no)) && ($user_segment_order_no <= $val['order_no'])) {
                    $user_segment_options_arr[] = $val['value_code'];
                }
            }
            $data['user_segment_options'] = implode(',',$user_segment_options_arr);;
        }
        if ($user_segment == 2) {
            // 车主
            $data['user_segment'] = $user_segment;
            $user_segment_options_arr = [$nvi_code];
            if ($dis_info['NONE']) {
                array_push($user_segment_options_arr,'NONE');
            }
            $data['user_segment_options'] = implode(',',$user_segment_options_arr);;
        }

        if (!empty($data['e3s_activity_id'])) {
            if (empty($data['activity_type'])) print_json(1, '存在e3s活动时活动设置类型必选');
        } else {
            $data['activity_type'] = 0;
        }
        $e3s_activity = input('e3s_activity', '');
        if (!empty($e3s_activity)) {
            $data['e3s_activity_name'] = explode(' | ', $e3s_activity)[1];
        }

        //开启事物
        $this->dbSeckil->startTrans();
        $rm_commodity_id_arr = [];

        $user_segment_data = ['user_segment'=>$user_segment, 'dis_info'=>$dis_info];
        if (empty($id)) {  //插入
            $last_id = $this->dbSeckil->insertGetId($data);
            $res     = $this->insertCommodiySkuDataLive($sku_list, $last_id, 'add', $set_type, $home, $data,$user_segment_data);
            if (!$res) {
                print_json(1, '保存失败');
            }
        } else {
            //修改
            $where_time = ['start_time' => $start_time, 'end_time' => $end_time];
            if ($is_enable == 1) {
                $date = date('Y-m-d H:i:s');
                //是否 秒杀
                $list = $this->dbSeckil->getIsSeckillProduct(
                    $sku_list, [
                        'a.id'         => ['<>', $id],
                        'a.is_enable'  => ['=', 1],
                        'a.set_type'   => $set_type,
                        'a.act_status' => ['in', [1, 2]]
                    ],$where_time);
                foreach ($list as $v) {
                    if ($v['is_seckill'] == 1) print_json(1, $v['commodity_name'] . '已参加其它秒杀活动');
                }

                //是否 限时折扣
                $list = (new DbLimitDiscount())->getIsLimitProduct($list, [
                    'a.is_enable'  => ['=', 1],
                    'a.set_type'   => $set_type,
                    'a.act_status' => ['in', [1, 2]]
                ], $where_time);
                foreach ($list as $v) {
                    if ($v['is_limit'] == 1) print_json(1, $v['commodity_name'] . '已参加其它限时优惠活动');
                }

                //判断是否已经参加了预售
                $list = (new DbPreSale())->getIsPreProduct(
                    $sku_list, [
                        'a.is_enable'      => ['=', 1],
                        'a.front_s_time'   => ['<', $date],
                        'a.balance_e_time' => ['>', $date],
                        'a.set_type'       => $set_type,
                    ]
                );
                foreach ($list as $v) {
                    if ($v['is_pre'] == 1) print_json(1, $v['commodity_name'] . '已参加预售活动');
                }
                //判断是否已经参加了拼团
                $list = (new DbFightGroup())->getIsFightGroupProduct(
                    $sku_list, [
                        'a.is_enable'  => ['=', 1],
                        'a.start_time' => ['<', $date],
                        'a.end_time'   => ['>', $date],
                        'a.set_type'   => $set_type,
                    ]
                );
                foreach ($list as $v) {
                    if ($v['is_fight'] == 1) print_json(1, $v['commodity_name'] . '已参加拼团活动');
                }
                // 是否满赠
                $list = (new DbGift())->getIsGiftProduct($list, [
                    'a.is_enable'  => ['=', 1],
                    'a.set_type'   => $set_type,
                    'a.act_status' => ['in', [1, 2]]
                ], $where_time);

                foreach ($list as $v) {
                    if ($v['is_gift'] == 1) print_json(1, $v['commodity_name'] . '已参加买赠活动');
                }

                $map = [
                        'a.id'         => ['<>', $id],
                        'a.is_enable'  => ['=', 1],
                        'a.set_type'   => $set_type,
                        'a.act_status' => ['in', [1, 2]]
                ];
                // 是否N件N折
                $list = (new DbNDiscount())->getIsNDiscount($sku_list,$map,$where_time);
                foreach ($list as $v) {
                    if ($v['is_n_discount'] == 1) print_json(1, $v['commodity_name'] . '已参加其它N件N折活动');
                }
            }

            $row     = $this->dbSeckil->getOneByPk($id);
            $last_id = $id;

            if ($row && $row['created_dlr_code'] == $this->admin_info['dlr_code']) {
                $time = date('Y-m-d H:i:s');
                $data['modifier']          = $this->admin_info['username'];
                $data['last_updated_date'] = $time;
                $old_commodity_id_arr = $this->dbSeckilCommodity->where(['seckill_id' => $id])->column('commodity_id');

                // 判断是否是秒杀活动开始前修改
                if ($time < $row['start_time']) {
                    // 删除秒杀
                    $service = new \app\common\net_service\Common();
                    foreach ($old_commodity_id_arr as $old_commodity_id) {
                        $service->del_kill_count($id, $old_commodity_id,$row['seckill_type'], $row['start_time'], $row['end_time']);
                    }
                }


                $res  = $this->dbSeckil->isUpdate(true)->saveData($data, ['id' => $id]);
                if ($res) {
                    $new_commodity_id_arr = array_column($sku_list, 'commodity_id');
                    $rm_commodity_id_arr  = array_diff($old_commodity_id_arr, $new_commodity_id_arr);

                    $this->dbSeckilCommodity->where(['seckill_id' => $id])->delete();

                    $res_sku = $this->insertCommodiySkuDataLive($sku_list, $id, 'update', $set_type, $home, $data, $user_segment_data);
                    if (!$res_sku) {
                        print_json(1, '活动商品保存失败');
                    }
                }
            }
        }
        //提交
        $this->dbSeckil->commit();
        if (in_array($act_status, [1, 2]) && !empty($is_enable)) {
            $limit_discount_comm = $this->dbSeckilCommodity->getSeckillCommodity(['a.seckill_id' => $last_id]);
            $this->doHook('add', $limit_discount_comm, $last_id);
        } else if (empty($is_enable)) {
            $limit_discount_comm = $this->dbSeckilCommodity->getSeckillCommodity(['a.seckill_id' => $last_id]);
            $this->doHook('delete', $limit_discount_comm, $last_id);
        }

        if (!empty($rm_commodity_id_arr)) {
            $this->doHook('delete', $rm_commodity_id_arr, $last_id);
        }

        print_json(0, '保存成功');
    }

    /**
     * 插入商品及规格
     * @param $sku_list
     * @param $fight_id
     * @param $action
     * @param $home
     * @return bool|int|string
     */
    public function insertCommodiySkuDataLive($sku_list, $fight_id, $action, $set_type = 5, $home = 0, $new_data = [],$user_segment_data=[])
    {
        if (empty($sku_list)) {
            return false;
        }
        $data     = [];
        $dlr_code = '';
        if ($set_type == 2) {
            $dlr_code = $this->admin_info['dlr_code'];
        } elseif ($set_type == 3) {
            $dlr_code = 'GWSC';
        }
        foreach ($sku_list as $key => $val) {
            $price = [];
            $dis = [];
            $highest_price = 0;
            $lowest_price = 0;
            foreach ($val['sku_list'] as $k => $v) {
//
                $old_price = 0;
                // 查询set-sku原价 限时折扣和限时立减需要查询原价
                if (in_array($new_data['dis_type'], [1, 2])) {
                    $old_price = DbCommoditySetSku::where('id', $k)->value('price');
                }

                // 判断是否是定向人群
                if (!$user_segment_data['user_segment']) {
                    // 限时折扣
                    if ($new_data['dis_type'] == 1) {
                        $price[$k] = $new_data['discount'] * $old_price / 10; // 原价*折扣/10
                    }
                    // 限时立减
                    if ($new_data['dis_type'] == 2) {
                        $price[$k] = $old_price - $new_data['discount'];
                    }
                    // 固定秒杀价
                    if ($new_data['dis_type'] == 3) {
                        $price[$k] = $new_data['discount'];
                    }
                    $dis[$k] = $new_data['discount'];
                    $sku_price = $price;
                    rsort($sku_price);
                    $highest_price = reset($sku_price);
                    $lowest_price  = end($sku_price);
                } else {
                    $highest_price_arr = [];
                    $lowest_price_arr = [];
                    // 会员
                    if ($user_segment_data['user_segment'] == 1) {
                        foreach ($user_segment_data['dis_info'] as $user_level => $discount) {
                            // 限时折扣
                            if ($new_data['dis_type'] == 1) {
                                $price[$k][$user_level] = round($discount * $old_price / 10,2); // 原价*折扣/10;
                            }
                            // 限时立减
                            if($new_data['dis_type'] == 2) {
                                $price[$k][$user_level] = $old_price - $discount;
                            }
                            // 固定秒杀价
                            if($new_data['dis_type'] == 3) {
                                $price[$k][$user_level] = $discount;
                            }
                            $dis[$k][$user_level] = $discount;
                        }

                    } else {
                        // 车主
                        foreach ($user_segment_data['dis_info'] as $car => $discount) {
                            if ($discount != '') {
                                // 限时折扣
                                if ($new_data['dis_type'] == 1) {
                                    $price[$k][$car] = $discount * $old_price / 10; // 原价*折扣/10;
                                }
                                // 限时立减
                                if($new_data['dis_type'] == 2) {
                                    $price[$k][$car] = $old_price - $discount;
                                }
                                // 固定秒杀价
                                if($new_data['dis_type'] == 3) {
                                    $price[$k][$car] = $discount;
                                }
                                $dis[$k][$car] = $discount;
                            }

                        }


                    }
                    rsort($price[$k]);
                    $highest_price_arr[$k] = reset($price[$k]);
                    $lowest_price_arr[$k] = end($price[$k]);
                    rsort($highest_price_arr);
                    rsort($lowest_price_arr);
                    $highest_price = reset($highest_price_arr);
                    $lowest_price  = end($lowest_price_arr);
                }
            }

            if (empty($val['sku_stock'])) {
                print_json(1, '活动商品库存不能为空');
            }

            $data[]              = [
                'commodity_id'     => $val['commodity_id'],
                'commodity_set_id' => $val['commodity_set_id'],
                'comm_discount'    => $new_data['discount'] ?? '',
                'sku_stock'        => $val['sku_stock'],
                'dlr_code'         => $set_type == 1 ? $val['dlr_code'] : $dlr_code,
                'seckill_id'       => $fight_id,
                'creator'          => $this->admin_info['username'],
                'sku_price'        => json_encode($price),
                'highest_price'    => $highest_price,
                'lowest_price'     => $lowest_price,
                'sku_dis'          => json_encode($dis),
                //'is_home' => $home
            ];
            $commodity_set_ids[] = $val['commodity_set_id'];
        }

        //删除已经添加的商品
        $where = ['seckill_id' => $fight_id];
        if (!$this->admin_info['type'] == 1) {
            $where['dlr_code'] = $this->admin_info['dlr_code'];
        }
        $this->dbSeckilCommodity->where($where)->delete();
        $lastid = $this->dbSeckilCommodity->insertAll($data);

        // 删除推荐活动不存在的商品
        $recommendService = new RecommendActivityService();
        $recommendService->delNotInActivityCommodity(2,$fight_id);

        $isKillCount = 0;
        if ($action == 'add') {
            $isKillCount = 1;
        } else {
            // 判断是否是秒杀活动前
            $row     = $this->dbSeckil->getOneByPk($fight_id);
            $time = date('Y-m-d H:i:s');
            if ($time < $row['start_time']) {
                $isKillCount = 1;
            }

        }
        if ($isKillCount) {
            $service = new \app\common\net_service\Common();
            // 初始化秒杀库存
            foreach ($sku_list as $key => $val) {
                // 添加秒杀库存
                $service->kill_count($fight_id, $val['commodity_id']);
            }
        }
        return $lastid;
    }

    private function doHook($type = 'delete', $commodity_arr = [], $act_id = 0)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;

        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        $model    = new DbSeckill();
        $activity = $model->where('id', $act_id)->find();
        if (!empty($activity['up_down_channel_dlr'])) {

            $up_down_arr = explode(',', $activity['up_down_channel_dlr']);
            $shelves_ni  = array_intersect(DbDlr::$ni_arr, $up_down_arr);
            $shelves_pz  = array_intersect(DbDlr::$pz1a_arr, $up_down_arr);
            $shelves_qc  = array_intersect(DbDlr::$qc_arr, $up_down_arr);

            if (!empty($shelves_ni)) {
                $shelves_type = DbDlr::$ni_shelves;
            } else if (!empty($shelves_pz)) {
                $shelves_type = DbDlr::$pz1a_shelves;
            } else if (!empty($shelves_qc)) {
                $shelves_type = DbDlr::$qc_shelves;
            } else {
                return false;
            }

            switch ($type) {
                case 'add':
                case 'update':
                    # 添加商品不需要刷活动信息
                    foreach ($commodity_arr as $item_one) {
                        $item_one['shelves_type'] = $shelves_type;
                        Hook::listen('flat_seckill', $item_one);
                    }
                    break;
                case 'delete':
                    # 删除就只需要将数据删除就ok
                    foreach ($commodity_arr as $item_one) {
                        $del_params = [
                            'shelves_type' => $shelves_type,
                            'del_dis'      => true,
                            'act_id'       => $act_id,
                            'commodity_id' => empty($item_one['commodity_id']) ? $item_one : $item_one['commodity_id']
                        ];
                        Hook::listen('flat_seckill', $del_params);
                    }
                    break;
            }

            $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
            Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);

            return $result;
        }

        return false;
    }

    /**
     * 更新数据
     * @return mixed
     */
    public function updateLive()
    {
        $id             = input('get.id');
        $limit_discount = $this->dbSeckil->getOneByPk($id);
        if ($limit_discount['created_dlr_code'] != $this->admin_info['dlr_code']) print_json(1, '');
        $limit_discount_comm = $this->dbSeckilCommodity->getSeckillCommodity(['a.seckill_id' => $id]);
        $limit_discount_comm = collection($limit_discount_comm)->toArray();
        $row_comm            = [];
        $sku_dis = [];
        foreach ($limit_discount_comm as $key => $val) {
            $seckill_redis = new \app\common\net_service\Common();
            $field = 'id,seckill_type,day_start_time,day_end_time';
            $seckill_info = DbSeckill::where('id', $id)->field($field)->find();
            $screening = '';
            if ($seckill_info['seckill_type'] == 2) {
                $time = date('H:i:s');
                if ($time > $seckill_info['day_end_time']) {
                    // 明天场次
                    $screening = date("Y-m-d", strtotime("+1 day"));
                } else {
                    // 当天场次
                    $screening = date('Y-m-d');
                }
            }
            $seckill_stock = $seckill_redis->kill_count($id, $val['commodity_id'],$screening);
//            $seckill_stock = redis('acKillCountById'.$id.$val['commodity_id']);
            if ($seckill_stock == false) {
                $seckill_stock = 'false';
            }
            if ($seckill_stock == '-1') {
                $seckill_stock = 0;
            }
            if (empty($seckill_stock)) {
                $seckill_stock = 0;
            }
            $dlr_name             = $this->dbDlr->getColumn(['where' => ['dlr_code' => ['in', explode(',', $val['dlr_code'])]], 'column' => 'dlr_name']);
            $val['seckill_stock'] = $seckill_stock;
            $val['dlr_name']      = implode(',', $dlr_name);
            $val['sku_list']      = json_decode($val['sku_price'], true);
            $val['sku_stock']     = json_decode($val['sku_stock'], true);
            unset($val['sku_price']);
            unset($val['cover_image']);
            $row_comm[] = [
                'commodity_name' => $val['commodity_name'],
                'cover_image'    => $limit_discount_comm[$key]['cover_image'],
                'sku_list'       => json_encode($val),
            ];
            $sku_dis = json_decode($val['sku_dis'], true) ?? [];
        }

        $dis_info = [];
        if ($limit_discount['user_segment'] != 0) {
            // 定向人群优惠
            foreach ($sku_dis as $val) {
                $dis_info = $val;
            }
        }
        $up_down_channel_info = (new DbSystemValue())->getNameList(20);
        $info_str             = implode(',', array_keys($up_down_channel_info));
        $this->assign('id', $id);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', $up_down_channel_info);
        $this->assign('dlr_hidden', (strpos($limit_discount['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
        $this->assign('dlr_str', $this->getDlrInInfo($limit_discount['up_down_channel_dlr']));
        if (empty($limit_discount['dis_type'])) {
            $limit_discount['dis_type'] = 0;
        }

        //优惠券
        $card_arr            = [];
        $selected_data       = [];
        $commodity_card_name = [];
        if (!empty($limit_discount['rel_card_ids'])) {
            $commodity_card_name = (new DbCard())->getColumn(['where' => ['id' => ['in', $limit_discount['rel_card_ids']]], 'column' => 'card_name']);
            $selected_data       = (new DbCard())->getColumn(['where' => ['id' => ['in', $limit_discount['rel_card_ids']]], 'column' => 'id,card_name']);
            $card_arr            = explode(',', $limit_discount['rel_card_ids']);
        }
        $limit_discount['commodity_card_name'] = implode(',', $commodity_card_name);
        $limit_discount['num_card']            = empty($limit_discount['rel_card_ids']) ? 0 : count(explode(',', $limit_discount['rel_card_ids']));

        $nvi_code = '';
        if($limit_discount['set_type'] == 5){
            $nvi_code = 'N';
        }
        if($limit_discount['set_type'] == 7){
            $nvi_code = 'V';
        }

        // 会员级别
        if (!empty($limit_discount['user_segment_options'])) {
            $user_segment_options_arr = explode(',',$limit_discount['user_segment_options']);
            $limit_discount['user_segment_options'] = $user_segment_options_arr[0];
        }
        $new_user_level = [];
        $old_user_level = [];
        if($limit_discount['set_type'] == 5 || $limit_discount['set_type'] == 7){
            $user_level = (new DbSystemValue())->getNameListByCode(25,$nvi_code);
            foreach ($user_level as $item) {
                $item_arr = ['county_name'=>$item['county_name'],'value_code'=>$item['value_code'],'order_no'=>$item['order_no']];
                $new_user_level[] = $item_arr;
            }
            $arr = ['county_name'=>'非会员','value_code'=>'NONE','order_no'=>'0'];
            array_unshift($new_user_level,$arr);

            // 会员
            if ($limit_discount['user_segment'] == 1) {
                $order_no = 0;
                foreach ($new_user_level as $key => $item) {
                    if ($item['value_code'] == $limit_discount['user_segment_options']) {
                        $order_no = $item['order_no'];
                    }
                }
                foreach ($new_user_level as $item) {
                    if ($item['order_no'] >= $order_no) {
                        $old_user_level[] = $item;
                    }
                }
            }

        }




        $this->assign('user_level',$new_user_level);
        $this->assign('old_user_level',$old_user_level);
        $this->assign('nvi_code',$nvi_code);
        $this->assign('dis_info',$dis_info);
        $this->assign('selected', json_encode($card_arr));
        $this->assign('selected_data', $selected_data);
        $this->assign('selected_card_name', json_encode($commodity_card_name));
        $this->assign('row', $limit_discount);
        $this->assign('row_comm', $row_comm);
        $this->assign('set_type', $limit_discount['set_type']);
        $this->assign('commodity_class', DbCommodity::commodityClass());
        $this->assign('live_type', input('live_type'));
        return $this->fetch('update_live');
    }

    /**
     * 获取卡券
     */
    public function ajaxGetCard()
    {
        $this->_checkAjax();
        $model        = new DbCard();
        $card_name    = input('get.card_name');
        $set_type     = input('set_type', 0);
        $where        = [];
        $shelves_type = 5;
        if ($set_type == 5) {
            $where['up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], ['notlike', '%QCSM%'], ['notlike', '%QCAPP%'], 'and'];
        } elseif ($set_type == 6) {
            $shelves_type = 6;
            $where[]      = [['exp', 'FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'], 'or'];
        } elseif ($set_type == 7) {
            $shelves_type = 7;
            $where[]      = [['exp', 'FIND_IN_SET("QCSM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",up_down_channel_dlr)'], 'or'];
        }
        if (!empty($card_name))
            $where['card_name'] = ['like', '%' . $card_name . '%'];

        $where['shelves_type'] = $shelves_type;
        $where['is_enable']    = 1;
        $where['type']         = 2;
        $where['act_status']   = ['in', [1, 2, 3]];
        $params                = [
            'where'    => $where,
            'order'    => 'id desc',
            'field'    => 'id,is_enable,act_status,card_type,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to,available_count,up_down_channel_name',
            'pagesize' => input('get.pagesize'),
            'query'    => input('get.')
        ];
        $list = $model->getListPaginate($params);
        $card_type_arr = $model->cardType();

        foreach ($list as $key => $value) {
            $list[$key]['id'] = (string)$value['id'];
            $list[$key]['card_type_name'] =  $card_type_arr[$value['card_type']];
            switch ($value['date_type']) {
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'] . '至' . $value['validity_date_end'];
                    break;
                case 2:
                    if ($value['fixed_begin_term'] == 0) {
                        $list[$key]['validity_date'] = "自领取当天有效，有效期" . $value['fixed_term'] . '天';
                    } elseif ($value['fixed_begin_term'] == 1) {
                        $list[$key]['validity_date'] = "领取后" . $value['fixed_term'] . '天后有效';
                    }
                    break;
                default:
                    break;
            }
            $value['status_name'] = empty($value['is_enable']) ? '已关闭' : $model->cardStatus()[$value['act_status']];
        }
        print_json(0, '', $list);
    }



    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);

    }


}

