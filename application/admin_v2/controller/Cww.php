<?php


namespace app\admin_v2\controller;


use api\wechat\Carer;
use app\admin_v2\queue\JdCommodityExport;
use app\api\controller\E3sKafka;
use app\common\model\bu\BuEarlyWarningPrice;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbDlr;
use app\common\model\db\DbExports;
use app\common\model\db\DbJdExports;
use app\common\model\db\DbJdSkuInfo;
use app\common\model\db\DbJobsLog;
use app\common\model\db\DbLog;
use app\common\model\db\DbLyTransactionStatic;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSeckillCommodity;
use app\common\model\db\DbSpecValue;
use app\common\model\db\DbUser;
use app\common\model\e3s\E3sMaintenanceProductCarSeries;
use app\common\model\e3s\E3sSparePart;
use app\common\model\e3s\E3sMaintenanceProduct;
use app\common\model\e3s\E3sPackage;
use app\common\model\hk\HkOrder;
use app\common\model\hk\HkOrderBook;
use app\common\model\pss\PssInventoryIn;
use app\common\model\pss\PssInventoryInout;
use app\common\net_service\Common;
use app\common\net_service\GoodsCustomize;
use app\common\net_service\Inventory;
use app\common\net_service\PaymentSystem;
use app\common\net_service\SendMailer;
use app\common\net_service\SendSms;
use app\common\port\connectors\E3spRefactor;
use app\common\port\connectors\QuickWin;
use app\common\queue\InventoryStock;
use think\Controller;
use think\Db;
use think\Exception;
use think\Hook;
use app\common\model\act\AcByPrice;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use app\common\model\db\DbCommodityFlat;
use think\Log;
use think\Model;
use think\Queue;
use think\Request;
use tool\Logger;
use tool\NewPhpExcel;
use tool\PhpExcelPage;

class Cww extends Common
{
    //积分反结算--通过订单号
    public function antibalance()
    {
        /**
         *
         * 2021年03月3日
         */
        $user = input('user');
        if ($user == 'cww') {
            $openid = input('openid');
            $order_code = input('order_code');
            $ic_card_no = input('ic_card_no');
            $vin = input('vin');
            $dlr_code = input('dlr_code');
            $Car = new Carer();
//            $order_model = new BuOrder();
//            $order = $order_model->getOne(['where' => ['order_code' => $order_code]]);
            $data = array(
                'openid' => $openid,
                'ic_card_no' => $ic_card_no,
                'vin' => $vin,
                'order_id' => $order_code,
                'dlr_code' => $dlr_code,
                'deal_man' => $dlr_code,//处理人
            );
            $point_res = $Car->antiBalance($data);
            $log_model = new DbLog();
            $log_data = array(
                'type' => 'antiBalance',
                'modifier' => '手工退',
                'send_note' => json_encode_cn($data),
                'receive_note' => json_encode_cn($point_res),
            );
            if ($point_res) {
                $log_data['is_success'] = 'success';
            } else {
                $log_data['is_success'] = 'fail';
            }
            $res = $log_model->insertData($log_data);
            var_dump($point_res);
        }
    }

    public function card()
    {
        $order_code = input('order_code');
        dump(order_finish($order_code));
        exit();
    }

    public function e3s_demo_wi()
    {
        $dlr_code = input('dlr_code');
        $repair_type = input('repair_type');
        $car_config_code = input('car_config_code');
        $param = [
            'INTERFACE_CODE' => 'QueryWorkTimePrice',
            'E3S_CODE'       => 'ZZYY',
            'dlr_code' => $dlr_code,
            'repair_type' => $repair_type,
            'car_config_code' => $car_config_code,
        ];
        $a = new DbCommoditySetSku();
        $b = $a->getE3sPrice($param);
        dd($b);
    }

    public function e3s_demo_vin()
    {
        $vin = input('vin');
        $param = [
            'INTERFACE_CODE' => 'QueryCarTypeByVin',
            'E3S_CODE'       => 'ZZYY',
            'vin'            => $vin
        ];
        $a = new DbCommoditySetSku();
        $b = $a->get18Code($param);
        dd($b);
    }

    //查询双保次数
    public function get_maintain()
    {
        $vin = input('vin');
//        $url = config('friend.vehicle_age').'/postdata/DNDC_ONLINESHOP/DNDC_GET_MAINTAIN_INFO';
//        $res = http_post($url,$params);
//        $res = E3spRefactor::create('e3sp_refactor')->getMaintainInfo($params);
        $service = new Common();
        $res = $service->_get_maintain('', $vin);

        if(!empty($res['rows'])){
            dump('剩余次数:'.($res['rows'][0]['maintainLeftTimes'] - $res['rows'][0]['upgradeMaintainCount']));
            print_json(0,'success', $res);
        }else{
            print_json(0,'没有升级次数', $res);
            dd('没有升级次数');
        }
        print_json(0,'success', $res);

    }

    public function save_hk_point()
    {
        $id = input('id');
        $member_identity = input('member_identity');
        $member_belongto = input('member_belongto');
        $vin = input('vin');
        $card_no = input('card_no');
        $type = input('type',1);
        if($type == 1){
            $array = [
                'member_identity'=>$member_identity,
                'member_belongto'=>$member_belongto,
                'vin'=>$vin,
                'ic_card_no'=>$card_no,
            ];
            $res = Db::name('hk_point')
                ->where(['id'=>$id])
                ->update($array);
            dump("更新表：".$res);
            exit();
        }
        else if($type == 2){
            $bill_date = input('bill_date');
            $order_no = input('order_no');
            $item_id = input('item_id');
            $user_point = input('user_point',0);
            $data = [
                'bill_date' => $bill_date,
                'vin' => $vin,
                'ic_card_no' => $card_no,
                'member_identity'=>$member_identity,
                'order_no' => $order_no,
                'item_id' => $item_id,
                'user_point' => $user_point,
            ];
            $res = Db::name('hk_merge_point')->insert($data);
            dump("合并表：".$res);
            exit();
        }else if($type == 3){
            $array = [
                'vin'=>$vin,
                'ic_card_no'=>$card_no,
            ];
            $res = Db::name('hk_merge_point')
                ->where(['id'=>$id])
                ->update($array);
            dump("更新hk_merge_point表：".$res);
            exit();
        }

    }

    public function cctest(){

        $user_vin = input('user_vin');
        $page_type = input('page_type');

        $home_key  = config('cache_prefix.more_people_more_home') . $user_vin . $page_type;
        $data_json = redis($home_key);

        if (empty($data_json)) {
            $data_json = 'test-tapo';
            redis($home_key, $data_json, mt_rand(2400, 3600));
            $redis = \think\Cache::redisHandler();
            $redis->sadd(config('cache_prefix.more_people_more_hm_set') . $page_type, $home_key);
        }

        dd('t');
    }

    public function cltest(){
        $vin = input('vin');
        $goodService = new GoodsCustomize(); # 清理对应首页的缓存数据
        $res = $goodService->cacheClear($vin);
        dd($res);
    }

    public function new_save_sku(){
        $type = input('type',1);
        $id = input('id');
        $ids = explode(',',$id);
        $is_enable = input('is_enable');
        if($type == 1){
            $res = Db::name('db_commodity_sku')->where(['id'=>array('in',$ids)])->update([
                'is_enable' => $is_enable
            ]);
        }elseif ($type == 2){
            $res = Db::name('db_commodity_set_sku')->where(['id'=>array('in',$ids)])->update([
                'is_enable' => $is_enable
            ]);
        }elseif($type == 3){
            $res = Db::name('db_commodity')->where(['id'=>array('in',$ids)])->update([
                'is_enable' => $is_enable
            ]);
        }
        else{
            $res = '';
        }

        echo $res;
    }

    public function get_sql(){
        //1日产逍客1.2T 2天籁2.0T处理 3 天籁 非特殊规格设置成0
        $type = input('type',1);
        $sql1 = input('sql');
        $id = input('user');

        if($type == 1){
            $sql = "SELECT id FROM `t_db_commodity_sku` WHERE `commodity_id` = '5993' AND `sp_value_list` LIKE '%,901%' AND `sku_code` LIKE '%_Q%';";

        }elseif ($type == 2){
            $sql = "SELECT id FROM `t_db_commodity_sku` WHERE `commodity_id` = '5993' AND `sp_value_list` LIKE '%,902%' AND sku_code LIKE '%_ASOIL_L260_NKV_%'";
        }elseif($type == 3){
            $sql = "SELECT id FROM `t_db_commodity_sku` WHERE `commodity_id` = '5993' AND `sp_value_list` LIKE '%,902%' AND sku_code not LIKE '%_ASOIL_L260_NKV_%'";
        }
        if($sql1 && $id=='lzx'){
            $sql = $sql1;
        }
        $model =  new DbCommoditySku();
        $list =  $model->query($sql);
        print_json(implode(',',array_column($list,'id')));

    }

    //灰度订单商品添加到生产数据库
    public function bu_order_commodity($order_code)
    {
        $order_commodity = Db::connect('database')->table('t_bu_order_commodity')
            ->where(['order_code'=>$order_code])->select();
        $data = [];
        foreach ($order_commodity as $key=>$value){
            unset($value['id'],$value['created_date'],$value['last_updated_date']);
            $data[$key] = $value;
            $data[$key]['creator'] = 'curl_order';
        }
        if(!empty($data)){
            try {
                Db::connect('database-prod')->table('t_bu_order_commodity')->insertAll(array_values($data));
            }catch (Exception $exception){
                Logger::error('bu_order_commodity:'.$exception->getMessage());
            }
        }
    }

    //灰度预约信息添加到生产数据库
    public function bu_order_subscribe($order_code)
    {
        try {
            $order_commodity = Db::connect('database')->table('t_bu_order_subscribe')
                ->where(['order_code'=>$order_code])->select();
            $order_id = Db::connect('database-prod')->table('t_bu_order')
                ->where(['order_code'=>$order_code])->find();//获取生产的新增id
            $data = [];
            foreach ($order_commodity as $key=>$value){
                unset($value['id'],$value['created_date'],$value['last_updated_date']);
                $data[$key] = $value;
                $data[$key]['order_id'] = $order_id['id'];
                $data[$key]['creator'] = 'curl_order';
            }
            if(!empty($data)){
                try {
                    Db::connect('database-prod')->table('t_bu_order_subscribe')->insertAll(array_values($data));
                }catch (Exception $exception){
                    Logger::error('bu_order_subscribe:'.$exception->getMessage());
                }
            }
        }catch (Exception $exception){
            Logger::error('bu_order_subscribe:'.$exception->getMessage());
        }

    }

    public function edit_car_series()
    {
        $type = input('type');
        $id = input('id');
        if($type == 1){
            //更新多余的
            $is_enable = input('is_enable');
            $where['id'] = array('in',$id);
            if(!empty($id)){
                echo Db::name('e3s_part_car_series')->where($where)->update([
                    'is_enable' => $is_enable
                ]);
            }
        }elseif ($type == 2){
            //删除多余的
            $where['id'] = array('in',$id);
            if(!empty($id)){
                echo Db::name('e3s_part_car_series')->where($where)->delete();
            }
        }elseif ($type == 3){
            //更新
            $is_enable = input('is_enable');
            $where['id'] = array('in',$id);
            if(!empty($id)){
                echo Db::name('e3s_maintenance_product_car_series')->where($where)->update([
                    'is_enable' => $is_enable
                ]);
            }
        }elseif($type == 4){
            //删除
            $where['id'] = array('in',$id);
            if(!empty($id)){
                echo Db::name('e3s_maintenance_product_car_series')->where($where)->delete();
            }
        }elseif ($type == 5){
            //删除多余的
            $where['id'] = array('in',$id);
            if(!empty($id)){
                echo Db::name('e3s_part_car_series_copy')->where($where)->delete();
            }
        }elseif ($type == 6){
            $where['id'] = array('in',$id);
            if(!empty($id)){
                echo Db::name('e3s_spare_part')->where($where)->delete();
            }
        }

    }

    public function wxmp(){
        $user_id  =  input('user_id',264386);
        $test_url = 'http://wxmp.chebaba.com/api/qy/getQcUserInfo?userid=' . $user_id;
        $test_url2 = 'https://wxstore.dongfeng-nissan.com.cn/net-small/home';
        $res = http_get($test_url);
        dump($res);
        $ress = http_get($test_url2);
        dump($ress);
    }

    public function save_sku_cost_price()
    {
        $id = input('commodity_id');
        $list = Db::name('db_commodity_sku')->where(['commodity_id'=>$id,'is_enable'=>1])->select();
        foreach ($list as $value){
            $price = Db::name('e3s_spare_part')
                ->where(['part_no'=>$value['sku_code']])
                ->column('dlr_price');
            if(!empty($price)){
                Db::name('db_commodity_sku')
                    ->where(['id'=>$value['id']])
                    ->update(['cost_price'=>$price['0']]);
                Db::name('db_commodity_set_sku')
                    ->where(['commodity_id'=>$id,'commodity_sku_id'=>$value['id']])
                    ->update(['cost_price'=>$price['0']]);
            }
        }
        echo "done";
        exit;
    }

    public function getDlrLevel(){
        $type = input('type',1);
        if($type == 1){
            $dlr_code = input('dlr_code');
            $params = ['PAGE_NUM' => 1, 'PAGE' => 1, 'DLR_CODE' => $dlr_code];
            $find = QuickWin::create('quick_win')->e3sDlrLevel($params);
            dd($find);
        }else{
            $id = input('id');
            $list = Db::name('db_dlr')->where(['id'=>array('>=',$id)])->select();
            foreach ($list as $value){
                $params = ['PAGE_NUM' => 1, 'PAGE' => 1, 'DLR_CODE' => $value['dlr_code']];
                $find = QuickWin::create('quick_win')->e3sDlrLevel($params);
                if(!empty($find['rows'])){
                    echo Db::name('db_dlr')->where(['id'=>$value['id']])->update(['dlr_level_grand'=>$find['rows'][0]['GRADE']]);
                }
            }
        }
        echo 'done';
//        $param = [
//            'INTERFACE_CODE' => 'QueryDlrPartQty',
//            'E3S_CODE'       => 'ZZYY',
//            'DLR_CODE'            => "H2901",
//            'PART_NO'            => "403125MA1A-C102",
//        ];
//        dd(Third::create('third')->rpc('POST', 'proxy/e3s/', $param));


    }

    public function save_yourself(){
        $type = input('type');
        if($type == 1){
            $id = input('id');
            $set_sku_id = input('set_sku_id');
            echo Db::name('ac_suit_yourself')->where(['id'=>$id])->update(['set_sku_id'=>$set_sku_id]);
        }elseif ($type == 2){
            $id = input('id');
            echo Db::name('ac_suit_yourself')->where(['id'=>$id])->update(['is_enable'=>0]);
        }elseif ($type == 3){
            $data = [
                'commodity_id' =>input('commodity_id'),
                'set_sku_id' =>input('set_sku_id'),
                'discount_price' =>input('price'),
                'category' =>input('category',1),
                'goods_level' =>input('goods_level'),
                'is_oil' =>input('is_oil',0),
            ];
            echo Db::name('ac_suit_yourself')->insert($data);
        }
    }

    public function api_kafka(){
        $otter = Db::name('e3s_maintenance_package_part')->where([
            'maintain_group_id'=>'2B63A157C5804B2C8376854B5801E8631',
            'maintain_parttype'=>'ENGINEOIL',
        ])->column('part_name');
        dd(implode(' / ',$otter));
        $redis_name = 'api_kafka';
        $book_id = redis($redis_name);
        $list = Db::name('e3s_log')->where([
            'msg_type' => 'e3s_part_car_series',
            'id' => array('>=',$book_id ?? 0),
        ])->whereBetween('last_updated_date',"2022-11-11 10:37:00,2022-11-14 10:37:00")
            ->select();
        foreach ($list as $value){
            $find = new E3sKafka();
            $find->e3s_part_car_series($value['receive_note']);
            redis('api_kafka', $value['id']);
            dump($value['id']);
        }
        echo 'done';
    }

    public function find_fit_time($sku_code)
    {
        $spare_part = new E3sSparePart();
        $res = $spare_part->alias('a')
            ->join('t_e3s_part_car_series b','a.part_no = b.part_no')
            ->where([
                'a.part_no'=>$sku_code, 'a.is_enable'=>1,'b.is_enable'=>1,
                'b.fit_beg_time' => array('neq','')
            ])->field('a.sale_price,a.dlr_price,a.part_no,a.rep_part_no,b.fit_beg_time,b.fit_end_time')
            ->group('b.fit_beg_time,b.fit_end_time')
            ->select();
        $data = [];
        foreach ($res as $val){
            $relate_car_work_hour = Db::name('e3s_part_car_series')
                ->where(['part_no' =>$val['part_no'], 'fit_beg_time' =>$val['fit_beg_time'],
                    'fit_end_time' =>$val['fit_end_time'],
                ])->field('id,car_config_code')->select();
            $part_no_array = [];
            $car_ids = [];
            foreach ($relate_car_work_hour as $value) {
                $part_no_array[$value['id']][] = $value['car_config_code'];
                $car_ids[] = $value['car_config_code'];
            }
            $relate_car_ids = Db::name('e3s_car_series')
                ->where(['car_config_code'=> array('in',$car_ids)])
                ->column('id');
            $data[] = [
                'sale_price' =>$val['sale_price'],
                'dlr_price' =>$val['dlr_price'],
                'relate_car_18n' =>implode(',',$car_ids),
                'relate_car_ids' =>implode(',',array_values($relate_car_ids)),
                'relate_car_work_hour' =>json_encode($part_no_array),
                'fit_beg_time' =>$val['fit_beg_time'],
                'fit_end_time' =>$val['fit_end_time'],
                'rep_part_no' =>$val['rep_part_no']
            ];
        }
        return $data;

    }

    public function e3s_save_sku_shop()
    {
        //获取全部到店备件
//        Db::startTrans();
//        try {
        $commodity = new DbCommodity();

        $commodity_sku = new DbCommoditySku();
        $where['dd_commodity_type'] = 9;
        $where['is_grouped'] = 0;
//            $where['id'] = array('in','4743,38430,38440,38471,38474,38497,38498,38499,38503,38504,38512,38520,38533,38549,38557,38559,38598,38601,38602,38610,38611,38612,38613,38614,38615,38616,38617,38626,38633,38636,38647,38650,38651,38652,38655,38663,38666,38671,38672,38673,38674,38676,38678,38679,38684,38702,38710,38721,38726,38727,38728,38753,38755,38764,38773,38778,38783,38784,38786,38787,38790,38796,38799,38801,38802,38815,38822,38823,38824,38825,38833,38842,38851,38855,38856,38857,38863,38868,38872,38879,38889,38890,38893,38898,38906,38907,38908,38916,38917,38918,38920,38921,38922,38923,38924,38926,38959,38964,38969,38970,38972,38973,38975,38976,38977,38978,38979,38980,38981,38982,38983,38984,38987,38988,38989,38998,39000,39016,39017,39018,39019,39020,39021,39022,39024,39025,39026,39028,39030,39031,39032,39033,39036,39037,39038,39039,39040,39041,39047,39049,39061,39063,39064,39065,39067,39068,39070,39074,39075,39076,39078,39079,39082,39083,39084,39103,39107,39123,39124,39126,39128,39131');
//            $where['id'] = '38474';
        //获取全部到店备件普通商品
        $list = $commodity->alias('a')
            ->where($where)
            ->limit(0,5)
            ->select();
        foreach ($list as $value)
        {
            //普通商品
            $res = $commodity_sku->where(['commodity_id'=>$value['id'],'is_enable'=>1])
                ->group('sku_code,sp_value_list')
                ->select();
            dd($res);
            if(!empty($res))
            {
                //设置sku is_enable 为0 start
                $commodity_sku->where(['commodity_id'=>$value['id']])->update(['is_enable' => 0]);
                //设置sku is_enable 为0 end
                $data = [];
                foreach ($res as $val){
                    $part = $this->find_fit_time($val['sku_code']);
                    foreach ($part as $v){
                        $data[] = [
                            'commodity_id' =>$val['commodity_id'],
                            'sp_value_id_1' =>$val['sp_value_id_1'], 'sp_value_id_2' =>$val['sp_value_id_2'],
                            'sp_value_id_3' =>$val['sp_value_id_3'], 'price' =>$v['sale_price'],
                            'stock' =>$val['stock'], 'sp_value_list' =>$val['sp_value_list'],
                            'image' =>$val['image'], 'sku_code' =>$val['sku_code'],
                            'cost_price' =>$v['dlr_price'], 'tax_code' =>$val['tax_code'],
                            'tax' =>$val['tax'],
                            'relate_car_18n' =>$v['relate_car_18n'],
                            'relate_car_ids' =>$v['relate_car_ids'],
                            'relate_car_work_hour' =>$v['relate_car_work_hour'],
                            'e3s_bj_type_name' =>$val['e3s_bj_type_name'], 'card_id' =>$val['card_id'],
                            'city_type' =>$val['city_type'], 'hours_id' =>$val['hours_id'],
                            'part_start_time' =>$v['fit_beg_time'], 'part_end_time' =>$v['fit_end_time'],
                            'rep_part_no' =>$v['rep_part_no']
                        ];
                    }
                }
                if(!empty($data)){
                    $sku_id = $commodity_sku->saveAll($data);
                    $this->save_set_sku($value['id'],$sku_id);//变更set_sku
                }
            }
            redis('e3s_time_sku',$value['id'],1*60*60);
        }
        Logger::error('save_sku:', json_encode($list));
//            Db::commit();
//        }catch (Exception $exception){
//            Db::rollback();
//            Log::error("saveAll sku_info : " . $exception->getMessage());
//        }

        echo 'done';
    }

    public function save_set_sku($sku_id,$data){
        Db::startTrans();
        try {
            $set_sku = new DbCommoditySetSku();
            $list = $set_sku
                ->where(['commodity_id' => $sku_id,'is_enable' => 1])
                ->group('commodity_set_id')
                ->select();
            //更新sku表到set_sku
            if(!empty($list)){
                $array = [];
                foreach ($list as $value){
//                    //把之前的set_sku is_enable 设置0 start
                    $set_sku->where(['commodity_id'=>$sku_id])->update(['is_enable'=>0]);
                    //把之前的set_sku is_enable 设置0 end
                    foreach ($data as $val){
                        $array[] = [
                            'price' => $val['price'], 'stock' => $value['stock'],
                            'dlr_code' => $value['dlr_code'], 'commodity_sku_id' => $val['id'] ?? 0,
                            'set_type' => $value['set_type'], 'commodity_id' => $value['commodity_id'],
                            'commodity_set_id' => $value['commodity_set_id'], 'divided_into' => $value['divided_into'],
                            'install_fee' => $value['install_fee'], 'cost_price' => $val['cost_price'],
                            'group_sub_commodity_id' => $value['group_sub_commodity_id'],
                            'group_sub_set_sku_id' => $value['group_sub_set_sku_id'],
                            'relate_car_18n' => $val['relate_car_18n'],
                            'relate_car_ids' => $val['relate_car_ids'],
                            'city_type' => $value['group_sub_set_sku_id'],
                        ];
                    }
                }
                if(!empty($array)){
                    $set_sku->saveAll($array);
                }
            }
            Db::commit();
        }catch (Exception $exception){
            Db::rollback();
            Log::error("saveAll set_sku : " . $exception->getMessage());
        }
    }

    //取csv内容
    private function _getcsv($path){
        $path = trim($path);
        if (!file_exists($path)) {
            return false;
        }
        $file = fopen($path, "r");
        while (!feof($file)) {
            $data[] = fgetcsv($file);
        }
        return $data;
    }

    public function e3s_execl_part()
    {
        set_time_limit(0);
        ini_set('memory_limit', '1024M');
        $data = $this->_getcsv($_FILES['file']['tmp_name']);
        $titles = '18位码,商城匹配备件,开始时间,结束时间';
        $name = input('name',date("YmdHi"));
        $file_name = 'public/uploads/exports/'.$name.'.csv';
        $fp = fopen(ROOT_PATH . $file_name, "a");
        //添加BOM头
        fwrite($fp, chr(0xEF) . chr(0xBB) . chr(0xBF));
        $title_arr = explode(",", $titles);
        fputcsv($fp, $title_arr);
        foreach ($data as $key => $value) {
            if ($key != 0) {
//            if ($key == 1) {
                $where = [];
                if ($value) {
                    $where['a.car_config_code'] = $value[0];
                    $where['a.part_variety_name'] = array("like","%".$value[2]);
                    $where['b.part_status'] = 1;
//                    $where['a.fit_beg_time'] = array('lt', $value[3]);
                    //拿到查询正常的备件号
                    $time = Db::name('e3s_part_car_series')->alias('a')
                        ->join('t_e3s_spare_part b', 'a.part_no = b.part_no')
                        ->where($where)
                        ->field('a.car_config_code,a.part_no,a.fit_beg_time,a.fit_end_time')
                        ->select();
                    if (empty($time)) {
                        $new_array = [
                            'car_config_code' => $value[0],
                            'part_no' => '',
                            'fit_beg_time' => '',
                            'fit_end_time' => '',
                        ];
                        fputcsv($fp, $new_array);
                    } else {
                        $part_no = [];
                        $fit_beg_time = [];
                        $fit_end_time = [];
                        foreach ($time as $num => $val) {
                            if($num == 3){
                                $find = Db::name('e3s_spare_part')->alias('a')
                                    ->join('t_e3s_part_car_series b','a.part_no = b.part_no')
                                    ->where([
                                        'a.rep_part_no'=>array('like',"%".$val['part_no']),
                                        'b.car_config_code'=>$val['car_config_code'],
                                    ])
                                    ->field('b.fit_beg_time,b.fit_end_time,a.part_no')
                                    ->select();
                                $datas = [];
                                $car_time = strtotime(date("Y-m",strtotime($value[1])));
                                if(!empty($find)){
                                    foreach ($find as $v => $i){
                                        $fit_beg_times = strtotime(date("Y-m",strtotime($i['fit_beg_time'])));
                                        if(empty($i['fit_end_time'])){
                                            $fit_end_times = 0;
                                        }else{
                                            $fit_end_times = strtotime(date("Y-m",strtotime($i['fit_end_time'])));
                                        }
                                        if(($car_time >= $fit_beg_times) && ($car_time < $fit_end_times|| $fit_end_times <= 0)){
                                            //有符合的备件
                                            $datas[] = [
                                                'part_no' => $val['part_no'],
                                                'fit_beg_time' => $val['fit_beg_time'],
                                                'fit_end_time' => $val['fit_end_time'],
                                            ];
                                            $part_no[] = $val['part_no'];
                                            $fit_beg_time[] = $i['fit_beg_time'];
                                            $fit_end_time[] = $i['fit_end_time'];
                                        }
                                    }
                                }else{

                                    if(!empty($val['fit_beg_time'])){
                                        $date_str = $val['fit_beg_time'];
                                        $dateTime  = \DateTime::createFromFormat("Y/m",$date_str);
                                        $date_str_formatted = date('Y-m-01', strtotime($date_str));
                                        if ($dateTime) {
                                            $fit_beg_times = $dateTime->getTimestamp();
                                        }else{
                                            $fit_beg_times = strtotime(date("Y-m",strtotime($date_str_formatted)));
                                        }
                                        if(empty($val['fit_end_time'])){
                                            $fit_end_times = 0;
                                        }else{
                                            $fit_end_times = strtotime(date("Y-m",strtotime($val['fit_end_time'])));
                                        }

                                        if(($car_time >= $fit_beg_times) && ($car_time < $fit_end_times|| $fit_end_times <= 0)){
                                            //有符合的备件
                                            $datas[] = [
                                                'part_no' => $val['part_no'],
                                                'fit_beg_time' => $val['fit_beg_time'],
                                                'fit_end_time' => $val['fit_end_time'],
                                            ];
                                            $part_no[] = $val['part_no'];
                                            $fit_beg_time[] = $val['fit_beg_time'];
                                            $fit_end_time[] = $val['fit_end_time'];
                                        }
                                    }
                                }
                            }
                        }
                        $new_array = [
                            'car_config_code' => $value[0],
                            'part_no' => implode(',', array_unique($part_no)),
                            'fit_beg_time' => implode(',', array_unique($fit_beg_time)),
                            'fit_end_time' => implode(',', array_unique($fit_end_time)),
                        ];
                        fputcsv($fp, $new_array);
                    }
                }
            }
        }


        // 释放资源
        fclose($fp);
        dump('成功');
        exit();
    }

    public function new_e3s_execl_part()
    {
        set_time_limit(0);
        ini_set('memory_limit', '1024M');
        $data = $this->_getcsv($_FILES['file']['tmp_name']);
        $titles = 'VIN码,18位码,车下线时间,问题描述,备件名称,商城匹配名称';
        $name = input('name',date("YmdHi"));
        $file_name = 'public/uploads/exports/'.$name.'.csv';
        $fp = fopen(ROOT_PATH . $file_name, "a");
        //添加BOM头
        fwrite($fp, chr(0xEF) . chr(0xBB) . chr(0xBF));
        $title_arr = explode(",", $titles);
        fputcsv($fp, $title_arr);
        $a = new DbCommoditySetSku();

        foreach ($data as $key => $value) {
            if ($key != 0) {
                $where = [];
                if ($value) {
                    $param = [
                        'INTERFACE_CODE' => 'QueryCarTypeByVin',
                        'E3S_CODE'       => 'ZZYY',
                        'vin'            => $value[0]
                    ];
                    $b = $a->get18Code($param);
                    if(!empty($b)){
//                    $where['a.car_config_code'] = $value[0];
                        $where['a.part_variety_name'] = array("like","%".$value[3]."%");
                        $where['b.part_status'] = 1;
//                    $where['a.fit_beg_time'] = array('lt', $value[3]);
                        //拿到查询正常的备件号
                        $time = Db::name('e3s_part_car_series')->alias('a')
                            ->join('t_e3s_spare_part b', 'a.part_no = b.part_no')
                            ->where($where)
                            ->field('a.car_config_code,a.part_no,a.fit_beg_time,a.fit_end_time')
                            ->select();
                        if (empty($time)) {
                            $new_array = [
                                'vin' => $value[0],
                                'car_config_code' => $b['car_config_code'],
                                'offline_date' => $b['offline_date'],
                                'describe' => $value[2].'____商城没有正常备件',
                                'part_name' => $value[3],
                                'part_no' => '',
                            ];
                            fputcsv($fp, $new_array);
                        } else {
                            $part_no = [];
//                        $fit_beg_time = [];
//                        $fit_end_time = [];
                            foreach ($time as $num => $val) {
                                $count = Db::name('e3s_spare_part')->alias('a')
                                    ->join('t_e3s_part_car_series b','a.part_no = b.part_no')
                                    ->where([
                                        'a.part_no'=>$val['part_no'],
                                        'b.car_config_code'=>$b['car_config_code'],
                                    ])->field('b.fit_beg_time,b.fit_end_time,a.part_no')
                                    ->select();
                                if(!empty($count)){
                                    $car_time = strtotime(date("Y-m",strtotime($b['offline_date'])));
                                    $find = Db::name('e3s_spare_part')->alias('a')
                                        ->join('t_e3s_part_car_series b','a.part_no = b.part_no')
                                        ->where([
                                            'a.rep_part_no'=>array('like',"%".$val['part_no']),
                                            'b.car_config_code'=>$b['car_config_code'],
                                        ])
                                        ->field('b.fit_beg_time,b.fit_end_time,a.part_no')
                                        ->select();
                                    if(!empty($find)){
                                        foreach ($find as $v => $i){
                                            $fit_beg_times = strtotime(date("Y-m",strtotime($i['fit_beg_time'])));
                                            if(empty($i['fit_end_time'])){
                                                $fit_end_times = 0;
                                            }else{
                                                $fit_end_times = strtotime(date("Y-m",strtotime($i['fit_end_time'])));
                                            }
                                            if(($car_time >= $fit_beg_times) && ($car_time < $fit_end_times|| $fit_end_times <= 0)){
                                                //有符合的备件
//                                            $datas[] = [
//                                                'part_no' => $val['part_no'],
//                                                'fit_beg_time' => $val['fit_beg_time'],
//                                                'fit_end_time' => $val['fit_end_time'],
//                                            ];
                                                $part_no[] = $val['part_no'];
//                                            $fit_beg_time[] = $i['fit_beg_time'];
//                                            $fit_end_time[] = $i['fit_end_time'];
                                            }
                                        }
                                    }else{
                                        if(!empty($val['fit_beg_time'])){
                                            $date_str = $val['fit_beg_time'];
                                            $dateTime  = \DateTime::createFromFormat("Y/m",$date_str);
                                            $date_str_formatted = date('Y-m-01', strtotime($date_str));
                                            if ($dateTime) {
                                                $fit_beg_times = $dateTime->getTimestamp();
                                            }else{
                                                $fit_beg_times = strtotime(date("Y-m",strtotime($date_str_formatted)));
                                            }
                                            if(empty($val['fit_end_time'])){
                                                $fit_end_times = 0;
                                            }else{
                                                $fit_end_times = strtotime(date("Y-m",strtotime($val['fit_end_time'])));
                                            }

                                            if(($car_time >= $fit_beg_times) && ($car_time < $fit_end_times|| $fit_end_times <= 0)){
                                                //有符合的备件
//                                            $datas[] = [
//                                                'part_no' => $val['part_no'],
//                                                'fit_beg_time' => $val['fit_beg_time'],
//                                                'fit_end_time' => $val['fit_end_time'],
//                                            ];
                                                $part_no[] = $val['part_no'];
//                                            $fit_beg_time[] = $val['fit_beg_time'];
//                                            $fit_end_time[] = $val['fit_end_time'];
                                            }
                                        }
                                    }
                                }
                            }
                            $new_array = [
                                'vin' => $value[0],
                                'car_config_code' => $b['car_config_code'],
                                'offline_date' => $b['offline_date'],
                                'describe' => $value[2],
                                'part_name' => $value[3],
                                'part_no' => implode(',', array_unique($part_no)),
                            ];
                            fputcsv($fp, $new_array);
                        }
                    }else{
                        $new_array = [
                            'vin' => $value[0],
                            'car_config_code' => '',
                            'offline_date' => '',
                            'describe' => $value[2],
                            'part_name' => $value[3],
                            'part_no' => '',
                        ];
                        fputcsv($fp, $new_array);
                    }
                }
            }
        }


        // 释放资源
        fclose($fp);
        dump('成功');
        exit();
    }

    //判断是否替换件是否存在
    public function is_part_no($part_no,$car_config_code){
        $data = [];
        $is_true = Db::name('e3s_spare_part')->alias('a')
            ->join('t_e3s_part_car_series b','a.part_no = b.part_no')
            ->where(['a.rep_part_no'=>array('in',$part_no),'b.car_config_code'=>$car_config_code])
            ->field('b.fit_beg_time,b.fit_end_time,a.part_no')
            ->group('a.part_no')
            ->select();
        if(!empty($is_true)){
            foreach ($is_true as $value){
                $data[] = [
                    'car_config_code' => $car_config_code,
                    'part_no' => $value['part_no'],
                    'fit_beg_time' => $value['fit_beg_time'],
                    'fit_end_time' => $value['fit_end_time'],
                ];
            }
        }
        return $data;
    }

    public function demo_save_commodity()
    {
        $id = input('id');
        return Db::name('db_commodity')->where(['id'=>$id])->update(['live_refresh'=>0]);
    }


    /**
     * @param $data 原件 （选择的件号是原件的替换件）
     * @param $part_no 选择的件号
     */
    private function request_part_no($data,$part_no,$id)
    {
        $res = Db::name('e3s_part_car_series')->where(['part_no' => $part_no])
            ->group('fit_beg_time,fit_end_time')
            ->select();
        $array = [];
        //原件先入库 start
        foreach ($res as $value) {
            $relate_car_18n = Db::name('e3s_part_car_series')
                ->where([
                    'part_no' => $part_no,
                    'fit_beg_time' => $value['fit_beg_time'],
                    'fit_end_time' => $value['fit_end_time'],
                ])->column('car_config_code');
            $array[] = [
                'commodity_id' => $id,
                'sku_code' => $part_no,
                'relate_car_18n' => implode(',', $relate_car_18n),
                'part_start_time' => $value['fit_beg_time'],
                'part_end_time' => $value['fit_end_time'],
            ];
        }
//        if(!empty($array)){
//            //选择的件号要先入库
//            $a = Db::name('db_commodity_sku')->insertAll($array);
//            dd($a);
//        }
        //原件先入库 end

        foreach ($data as $value) {
            $res_copy = Db::name('e3s_part_car_series')->where(['part_no' => $value])
                ->group('fit_beg_time,fit_end_time')
                ->select();
            foreach ($res_copy as $val) {
                $relate_car_18n = Db::name('e3s_part_car_series')
                    ->where([
                        'part_no' => $value,
                        'fit_beg_time' => $val['fit_beg_time'],
                        'fit_end_time' => $val['fit_end_time'],
                    ])->column('car_config_code');
                $sku_ids = [];
                $sku_relate_car_18n = [];
                foreach ($relate_car_18n as $v) {
                    $sku_id = Db::name('db_commodity_sku')->where(['commodity_id' => $id, 'sku_code' => $part_no, 'relate_car_18n' => array('like', "%" . $v . "%")])->field('id')->find();
                    if (!empty($sku_id)) {
                        $sku_ids[] = $sku_id['id'];
                        $sku_relate_car_18n[] = $v;
                    }
                }
                $new_sku_id = [
                    'sku_code' => $part_no,
                    'fit_beg_time' => $val['fit_beg_time'],
                    'fit_end_time' => $val['fit_end_time'],
                    'copy_sku_id' => implode($sku_ids),
                    'relate_car_18n' => implode($sku_relate_car_18n),
                ];
                dump($new_sku_id);
//                dd($relate_car_18n);
            }
//            dd($res);
        }
        dump($part_no);
        dump('---------------------');
//        dump($array);

//        dump($res);
//        dump($data);
//        exit;
    }

    public function demo_commodity_sku()
    {
        $id = input('id');
        $type = input('type');
        $sku_code = input('sku_code');
        if($type == 1){
            echo Db::name('db_commodity_sku')->where(['id'=>array('in',$id)])->update(['sku_code'=>$sku_code]);
        }elseif ($type == 2){
            echo Db::name('bu_to_e3s_detail')->where(['id'=>array('in',$id)])->update(['to_maintain_group_code'=>$sku_code]);
        }
        exit;
    }

    public function save_order_cost_price()
    {
        $order_code = input('order_code','');
        $commodity_id = input('commodity_id');
        $where['commodity_id'] = array('in',$commodity_id);
        if(!empty($order_code)){
            $where['order_code'] = array('in',$order_code);
        }
        $list = Db::name('bu_order_commodity')
            ->where($where)
            ->field('id,third_sku_code')
            ->select();
        foreach ($list as $value){
            $price = Db::name('e3s_spare_part')
                ->where(['part_no'=>$value['third_sku_code']])
                ->column('dlr_price');
            if(!empty($price)){
                Db::name('bu_order_commodity')
                    ->where(['id'=>$value['id']])
                    ->update(['cost_price'=>$price[0]]);
            }
        }
        echo "done";
        exit;
    }

    //备件新增时间查询车型工时和备件类型名称
    private function part_work_hour($part_no)
    {
        $list = Db::name('e3s_part_car_series')->alias('a')
            ->join('t_e3s_spare_part b','a.part_no = b.part_no','left')
            ->where(['a.part_no'=>$part_no])
            ->field('a.id,a.car_config_code,b.variety_code_big_name,b.variety_code_small_name')
            ->select();
        $part_no_array = [];
        foreach ($list as $value) {
            $part_no_array[$value['id']][] = $value['car_config_code'];

        }
        return $part_no_array;
    }

    public function edit_commodity()
    {
        $commodity_id = input('goods_id');
        $sku_code = input('part_no');
        if(empty($commodity_id)){
            dd('商品id不能为空');
        }
        if(empty($sku_code)){
            dd('备件号不能为空');
        }
        $model = new \app\common\model\e3s\E3sPartCarSeries();
        $CarSeries = new \app\common\model\e3s\E3sCarSeries();
        $db_commodity_sku = new DbCommoditySku();
        $set_sku = new DbCommoditySetSku();
        $sku_list = $model->where(['part_no'=>$sku_code,'is_enable'=>1, 'commodity_id' => $commodity_id])
            ->group('fit_beg_time,fit_end_time')
            ->field('part_no,fit_beg_time,fit_end_time')
            ->select();//获取e3s传过来的备件时间
        //获取商品规格下的时间
        $commodity = $db_commodity_sku->where([
            'commodity_id' => $commodity_id,
            'sku_code' => $sku_code,
            'is_enable' => 1
        ])->group('part_start_time,part_end_time')
            ->column('id');

        //把sku表的数据设置为0
        $db_commodity_sku->where(['id'=>array('in',$commodity)])->update(['is_enable'=>0]);
        $set_sku->where(['commodity_sku_id'=>array('in',$commodity)])->update(['is_enable'=>0]);
        $add = 0;
        $update = 0;
        foreach ($sku_list as $value){
            $res = $db_commodity_sku->where([
                'id' => array('in',$commodity),
                'sku_code' => $value['part_no'],
                'part_start_time' => $value['fit_beg_time'],
                'part_end_time' => $value['fit_end_time'],
            ])->find();
            $n18 = Db::name('e3s_part_car_series')->where([
                'part_no' => $sku_code,
                'fit_beg_time' => $value['fit_beg_time'],
                'fit_end_time' => $value['fit_end_time'],
                'is_enable' => 1,
            ])->column('car_config_code');
            $relate_cars = $CarSeries->whereIn('car_config_code', $n18)
                ->where('is_enable','=',1)->column('id');
            $relate_car_18n = implode(',', $n18);
            $relate_car_ids = implode(',', $relate_cars);
            $relate_car_work_hour = json_encode($this->part_work_hour($sku_code));
            if(empty($res)){
                //新添加
                $sku_find = $db_commodity_sku->where(['sku_code'=>$value['part_no']])
                    ->order('last_updated_date desc')->find()->toArray();
                $set_sku_find = $set_sku->where(['commodity_sku_id'=>$sku_find['id']])->find()->toArray();
                unset($sku_find['id'],$sku_find['created_date'],$sku_find['last_updated_date']);
                unset($set_sku_find['id'],$set_sku_find['created_date'],$set_sku_find['last_updated_date']);
                $sku_find['relate_car_18n'] = $relate_car_18n;
                $sku_find['relate_car_ids'] = $relate_car_ids;
                $sku_find['relate_car_work_hour'] = $relate_car_work_hour;
                $sku_find['part_start_time'] = $value['fit_beg_time'];
                $sku_find['part_end_time'] = $value['fit_end_time'];
                $sku_find['is_enable'] = 1;
                $sku_id = $db_commodity_sku->insertGetId($sku_find);
                $set_sku_find['commodity_sku_id'] = $sku_id;
                $set_sku_find['relate_car_18n'] = $relate_car_18n;
                $set_sku_find['relate_car_ids'] = $relate_car_ids;
                $set_sku_find['is_enable'] = 1;
                $set_sku->insertData($set_sku_find);
                $add++;
            }else{
                $db_commodity_sku->saveData([
                    'relate_car_18n'  => $relate_car_18n,
                    'relate_car_ids'  => $relate_car_ids,
                    'relate_car_work_hour'  => $relate_car_work_hour,
                    'is_enable'  => 1,
                ],['id' => $res['id']]);
                $set_sku->saveData([
                    'relate_car_18n'  => $relate_car_18n,
                    'relate_car_ids'  => $relate_car_ids,
                    'is_enable'  => 1,
                ],['commodity_sku_id' => $res['id']]);
                $update++;
            }
        }
        dump("新增sku：".$add.'条；修改sku：'.$update.'条');
//        $commodity = Db::name('db_commodity_sku')->where([
//            'sku_code' => $sku_code,
//            'is_enable' => 1
//        ])->select();
//        $db_commodity_sku = new DbCommoditySku();
//        $set_sku = new DbCommoditySetSku();
//        foreach ($commodity as $value){
//            $n18 = Db::name('e3s_part_car_series')->where([
//                'part_no' => $sku_code,
//                'fit_beg_time' => $value['part_start_time'],
//                'fit_end_time' => $value['part_end_time'],
//                'is_enable' => 1,
//            ])->column('car_config_code');
//            $relate_cars = $CarSeries->whereIn('car_config_code', $n18)
//                ->where('is_enable','=',1)->column('id');
//            $relate_car_18n = implode(',', $n18);
//            $relate_car_ids = implode(',', $relate_cars);
//            $relate_car_work_hour = json_encode($this->part_work_hour($sku_code));
//            $res = $db_commodity_sku->saveData([
//                'relate_car_18n'  => $relate_car_18n,
//                'relate_car_ids'  => $relate_car_ids,
//                'relate_car_work_hour'  => $relate_car_work_hour,
//            ],['id' => $value['id']]);
//            dump($res);
//            $res1 = $set_sku->saveData([
//                'relate_car_18n'  => $relate_car_18n,
//                'relate_car_ids'  => $relate_car_ids,
//            ],['commodity_sku_id' => $value['id']]);
//            dump($res1);
//        }
        dd('成功');
    }

    public function edit_commodity_back()
    {
        $commodity_id = input('goods_id');
        $sku_code = input('part_no');
        if(empty($commodity_id)){
            dd('商品id不能为空');
        }
        if(empty($sku_code)){
            dd('备件号不能为空');
        }
//        $model = new \app\common\model\e3s\E3sPartCarSeries();
        $CarSeries = new \app\common\model\e3s\E3sCarSeries();
        $commodity = Db::name('db_commodity_sku')->where([
            'sku_code' => $sku_code,
            'is_enable' => 1
        ])->select();
        $n18 = Db::name('e3s_part_car_series_copy')->where([
            'part_no' => $sku_code,
            'is_enable' => 1,
        ])->column('car_config_code');
        $relate_cars = $CarSeries->whereIn('car_config_code', $n18)
            ->where('is_enable','=',1)->column('id');
        $relate_car_18n = implode(',', $n18);
        $relate_car_ids = implode(',', $relate_cars);
        $relate_car_work_hour = json_encode($this->part_work_hour($sku_code));
        $db_commodity_sku = new DbCommoditySku();
        $set_sku = new DbCommoditySetSku();
        foreach ($commodity as $value){
            $res = $db_commodity_sku->saveData([
                'relate_car_18n'  => $relate_car_18n,
                'relate_car_ids'  => $relate_car_ids,
                'relate_car_work_hour'  => $relate_car_work_hour,
            ],['id' => $value['id']]);
            dump($res);
            $res1 = $set_sku->saveData([
                'relate_car_18n'  => $relate_car_18n,
                'relate_car_ids'  => $relate_car_ids,
            ],['commodity_sku_id' => $value['id']]);
            dump($res1);
        }
        dd('成功');
    }

    public function edit_commodity_sku_taocan()
    {
        $id = input('id');
        $city_type = input('city_type');
        $is_enable = input('is_enable',1);
        $where = ['commodity_id'=>$id,'is_enable'=>$is_enable];
        if($city_type){
            $where['city_type'] = $city_type;
        }
        $sku_list = Db::name("db_commodity_sku")
            ->where($where)
            ->field('id,sku_code')
            ->select();
        if(!empty($sku_list)){
            $model = new E3sPackage();
            foreach ($sku_list as $value){
                $sku_code = explode(',',$value['sku_code']);
                $list = $model->alias('a')
                    ->join('t_e3s_maintenance_product_car_series b','a.product_type_id = b.product_type_id')
                    ->where(['a.maintain_group_code'=>array('in',$sku_code),'b.is_enable'=>1])
                    ->column('b.service_car_type');
                $car_18n = Db::name('e3s_car_series')->where(['service_car_type'=>array('in',$list)])->field('id,car_config_code')->select();
                $relate_car_18n = [];
                $relate_car_ids = [];
                foreach ($car_18n as $val){
                    $relate_car_18n[] = $val['car_config_code'];
                    $relate_car_ids[] = $val['id'];
                }
                $find = Db::name('db_commodity_sku')->where(['id'=>$value['id']])->update([
                    'relate_car_18n' => implode(',',$relate_car_18n),
                    'relate_car_ids' => implode(',',$relate_car_ids)
                ]);
                $find1 = Db::name('db_commodity_set_sku')->where(['commodity_sku_id'=>$value['id']])->update([
                    'relate_car_18n' => implode(',',$relate_car_18n),
                    'relate_car_ids' => implode(',',$relate_car_ids)
                ]);
                dump($find);
                dump($find1);
            }

        }
        dump('done');
    }

    //套餐服务车型数据
    public function add_product_car_series()
    {
        $cartype_id = input('cartype_id');
        $product_type_id = input('product_type_id');
        $service_car_type = input('service_car_type');
        $e3s_update_no = input('e3s_update_no');
        echo Db::name('e3s_maintenance_product_car_series')->insert([
            'cartype_id' => $cartype_id,
            'product_type_id' => $product_type_id,
            'service_car_type' => $service_car_type,
            'e3s_update_no' => $e3s_update_no,
        ]);
    }

    public function demo_class_type()
    {
        $id = input('id');
        $page_type = input('page_type');
        $is_publish = input('is_publish');
        return Db::name('db_home_type')->where(['id'=>array('in',$id)])->update(['page_type'=>$page_type,'is_publish'=>$is_publish]);
    }

    public function ly_syn_order()
    {
        $id = input('id');
        $is_enable = input('is_enable');
        echo Db::name('db_ly_syn_order')->where(['id'=>array('in',$id)])->update(['is_enable'=>$is_enable]);
    }

    public function demo_stock()
    {
//        $model = new Inventory();
        $id = input('id');
        $model = new DbJobsLog();
        $find = $model->where(['id'=>$id])->column('data_info');
        $data = json_decode($find[0],true);
//        $list = $this->refundStock($data['afs_type'], $data['sku_code'], $data['count'], $data['sale_no']);
        $this->recordStock($data['order_commodity_id']);
//        dd($data);
//        $model->refund_stock('FPX7317001-V066', 'GWAPP230704152059Uno', 1531292);
//        $model->recordStock('FPX7317001-V066','GWSM230704151913mA3',1531289);
    }

    /**
     * 退款入库 跑队列
     * @param $inout_type
     * @param $sku_code
     * @param $count
     * @param $sale_no
     * @return bool
     */
    public function refundStock($inout_type, $sku_code, $count, $sale_no,$user_name)
    {
        exit;
            $after_order_code   = Db::name('db_after_sale_orders')->alias('a')
                ->join('t_bu_order b', 'a.order_id = b.id')
                ->join('t_bu_order_commodity c', 'c.order_code = b.order_code')
                ->where(['a.afs_service_id' => $sale_no, 'c.third_sku_code' => $sku_code])
                ->field('b.order_code,c.id as order_commodity_id')
                ->find();
            $order_commodity_id = 0;
            $order_code         = '';
            if (!empty($after_order_code)) {
                $order_commodity_id = $after_order_code['order_commodity_id'];
                $order_code         = $after_order_code['order_code'];
            }
            $inout = Db::name('pss_inventory_inout')->alias('a')
                ->join('t_pss_inventory_in b','a.in_id = b.id')
                ->where([
                    'a.order_code'         => $order_code,
                    'a.is_enable' => 1,
                    'a.order_commodity_id' => $order_commodity_id])
                ->field('a.*,b.inout_count as original_stocks')
                ->select();
            if (!empty($inout)) {
                $data = [];
                foreach ($inout as $key=>$value)
                {
                    $data[$key]['inout_tax_price']  = $value['inout_tax_price'];
                    $data[$key]['inout_tax_amount'] = $value['inout_tax_amount'];
                    $data[$key]['inout_price']      = $value['inout_price'];
                    $data[$key]['inout_amount']     = $value['inout_amount'];
                    $data[$key]['inout_unit']       = $value['inout_unit'];
                    $data[$key]['original_stock']   = $value['original_stocks'] ?? 0;
                    //如果没有发货就清除占用库存的名额
                    if ($value['inout'] == 3) {
                        Db::name('pss_inventory_in')->where(['id' => $value['id']])->save(['is_enable' => 0,'modifier'=>$user_name]);
                    }else{
                        //发货后 退款增加真实库存
                        Db::name('pss_inventory_in')->where(['id' => $value['in_id']])->setInc('inout_count',$count);
                        $data[$key]['creator']            = $user_name;
                        $data[$key]['in_sale_no']         = $sale_no;
                        $data[$key]['order_code']         = $order_code;
                        $data[$key]['order_commodity_id'] = $order_commodity_id;
                        $data[$key]['inout_date_time']    = date("Y-m-d H:i:s");
                        $data[$key]['inout_count']        = $count;
                        $data[$key]['inout_type']         = $inout_type;
                        $data[$key]['inout']              = 1;//1入库 2出库
                        $data[$key]['in_id']              = $value['in_id'];
                        $data[$key]['sku_code']           = $sku_code;
                    }
                }
                if(!empty($data)){
                    Db::name('pss_inventory_inout')->insertAll(array_values($data));
                }
            }
            return true;
    }

    public function demo_pss_stock()
    {
        $type = input('type',1);
        $in = new PssInventoryIn();
        $inout = new PssInventoryInout();
        $input = input('');
        $id = input('id');
        if($type == 1){
            //修改主表
            unset($input['type'],$input['id']);
            $list = $in->where(['id'=>$id])->update($input);
            echo $list;
        }elseif ($type == 2){
            //修改流水
            unset($input['type'],$input['id']);
            $list = $inout->where(['id'=>$id])->update($input);
            echo $list;
        }elseif ($type == 3){
            echo $in->where(['id'=>$id])->delete();
            exit;
        } elseif ($type == 4){
            echo $inout->where(['id'=>$id])->delete();
            exit;
        }
    }

    public function demo_stock_job()
    {
        $ids = input('id');
        $order_commodity_model = new BuOrderCommodity();
        $order_commodity_list = $order_commodity_model->where(['id' => ['in', $ids]])->select();
        $commodity_list = [];
        $in_stock = new Inventory();
        foreach ($order_commodity_list as $key=>$v){
            $commodity_list[] = $v;
        }
        echo $in_stock->outbound_stock($commodity_list,'');
    }

    public function export_taocan()
    {
        set_time_limit(0);
        ini_set('memory_limit', '1024M');
        $maintenance_package = new E3sPackage();
        $e3s_model =  new \app\common\model\e3s\E3sCarSeries();
        $maintenance_car_series = new E3sMaintenanceProductCarSeries();

        $list = $maintenance_package->where([
            'is_enable'=>1,
            'product_variety_name'=>array('in',['心悦保养套餐','老友惠保养套餐', '双保升级保养套餐','五年双保专享心悦套餐']),
        ])->select();
        $redis_name = 'api_kafka2';
        foreach ($list as $key=>$value){
            $redis_key = redis($redis_name.'_'.$value['product_type_id']);
            if(!$redis_key){
                $service_car_type = $maintenance_car_series
                    ->where(['product_type_id'=>$value['product_type_id'],'is_enable'=>1])
                    ->group('service_car_type')
                    ->column('service_car_type');
                $car_type_cn =$e3s_model
                    ->where(['service_car_type'=>['in',$service_car_type],'is_enable'=>1])
                    ->group('base_car_series_cn')
                    ->column('base_car_series_cn');
                $list[$key]['service_car_type'] = implode(';',$service_car_type);
                $list[$key]['service_car_type_cn'] = implode(';',$car_type_cn);
                redis($redis_name.'_'.$value['product_type_id'],['car_type'=>implode(';',$service_car_type),'car_type_cn'=>implode(';',$car_type_cn)],1*24*60*60);
            }else{
                $list[$key]['service_car_type'] = $redis_key['car_type'];
                $list[$key]['service_car_type_cn'] = $redis_key['car_type_cn'];
            }
        }
        $fiels = 'maintain_group_code,maintain_group_name,maintain_total_count,discount,belong_zone_code,product_type_code,product_type_name,saler_amount,car_brand_cn,service_car_type,service_car_type_cn';
        $titles = '套餐编码,套餐名称,保养次数,折扣,地区,产品类型编码,产品类型名称,销售金额,品牌名称,服务车型,服务车型-中文';
        export_excel($list, $fiels, $titles, '套餐数据和服务车型' . date('Y-m-d H:i:s'));
        echo "下载完成";
    }


    public function export_bj_list()
    {
//        set_time_limit(0);
//        ini_set('memory_limit', '1024M');
//        $maintenance_package = new E3sPackage();
//        $sql =  sprintf("SELECT  a.part_no,a.part_variety_name,
//a.other_attr,b.part_status_cn,b.dlr_order_switch_cn,c.car_series_code,c.car_series_cn,
//a.car_config_code,a.car_config_cn,c.service_car_type,c.base_series_code,c.base_car_series_cn,b.dlr_price,b.sale_price,b.rep_part_no,b.variety_code_small_name
// from  t_e3s_part_car_series a
//join t_e3s_spare_part b on a.part_no=b.part_no and b.is_enable=1
//join t_e3s_car_series c on a.car_config_code=c.car_config_code and c.is_enable=1
// where a.is_enable=1 ");
//        $list = $maintenance_package->query($sql);
//
//        $fiels = 'part_no,part_variety_name,other_attr,part_status_cn,dlr_order_switch_cn,car_series_code,car_series_cn,car_config_code,car_config_cn,service_car_type,base_series_code,base_car_series_cn,dlr_price,sale_price,rep_part_no,variety_code_small_name';
//        $titles = '备件号,备件名称,属性,备件状态,订货状态,车系编码,车系,18位码,18位码备注,服务车型,基准车系编码,基准车系,网点价,零售价,替换件,备件品种小类名称';
//
//        foreach ($list as $key => $item) {
//            $data[] = [
//                'a'=>$item['part_no'],
//                'b'=>$item['part_variety_name'],
//                'c'=>$item['other_attr'],
//                'd'=>$item['part_status_cn'],
//                'e'=>$item['dlr_order_switch_cn'],
//                'f'=>$item['car_series_code'],
//                'g'=>$item['car_series_cn'],
//                'h'=>$item['car_config_code'],
//                'i'=>$item['car_config_cn'],
//                'j'=>$item['service_car_type'],
//                'k'=>$item['base_series_code'],
//                'l'=>$item['base_car_series_cn'],
//                'm'=>$item['dlr_price'],
//                'n'=>$item['sale_price'],
//                'o'=>$item['rep_part_no'],
//                'p'=>$item['variety_code_small_name']
//            ];
//        }
//
//        $titleArr  = [
//            'A'=>'备件号','B'=>'备件名称','C'=>'属性','D'=>'备件状态','E'=>'订货状态','F'=>'车系编码','G'=>'车系','H'=>'18位码','I'=>'18位码备注','J'=>'服务车型','K'=>'基准车系编码','L'=>'基准车系','M'=>'网点价','N'=>'零售价','O'=>'替换件','P'=>'备件品种小类名称'
//        ];
//        $widthArr  = [
//            'A' => '30', 'B' => '30', 'C' => '30', 'D' => '30', 'E' => '30', 'F' => '30', 'G' => '30',
//            'H' => '30', 'I' => '30', 'J' => '30', 'K' => '30', 'L' => '30', 'M' => '30', 'N' => '30','O'=>'30','P'=>'30'
//        ];
//        $sheetTile = 'E3S备件';
//        $filename  = date('Y-m-d') . $sheetTile . '.xls';
//        $path      = NewPhpExcel::export_xls($data, $titleArr, $widthArr, $filename, $sheetTile);
//        $export     = DbExports::create([
//            'export_type'   => 'trans_static',
//            'filter_params' => date('Y-m-d H:i:s'),
//            'export_key'    => md5($filename),
//            'file_address'    => $filename,
//            'creator'       => 'qt'
//        ]);
//        echo $path;
        Queue::push('app\admin_v2\queue\GoodsExport', json_encode([]), config('queue_type.export'));
        $filename='public/uploads/exports/'.date('Y-m-d') . 'E3S备件.xls';
        $export     = DbExports::create([
            'export_type'   => 'goods',
            'filter_params' => date('Y-m-d H:i:s'),
            'export_key'    => md5($filename),
            'file_address'    => $filename,
            'creator'       => 'qt',
            'export_status'       => 2
        ]);

//        export_excel($list, $fiels, $titles, 'E3S备件数据' . date('Y-m-d H:i:s'));
        echo "down";
    }

    public function exp_goods(){
        set_time_limit(0);
        $maintenance_package = new E3sPackage();
        $sql =  sprintf("SELECT  count(1) cc
 from  t_e3s_part_car_series a
join t_e3s_spare_part b on a.part_no=b.part_no and b.is_enable=1
join t_e3s_car_series c on a.car_config_code=c.car_config_code and c.is_enable=1
 where a.is_enable=1 ");
        $count = $maintenance_package->query($sql);
        $pageExcel = new PhpExcelPage();
        $pageExcel->setSheetTitle('E3S备件');
        $title = [
            'A'=>'备件号','B'=>'备件名称','C'=>'属性','D'=>'备件状态','E'=>'订货状态','F'=>'车系编码','G'=>'车系','H'=>'18位码','I'=>'18位码备注','J'=>'服务车型','K'=>'基准车系编码','L'=>'基准车系','M'=>'网点价','N'=>'零售价','O'=>'替换件','P'=>'备件品种小类名称'
        ];
        $pageExcel->setTitle($title);
        $widthArr = [
            'A' => '30', 'B' => '30', 'C' => '30', 'D' => '30', 'E' => '30', 'F' => '30', 'G' => '30',
            'H' => '30', 'I' => '30', 'J' => '30', 'K' => '30', 'L' => '30', 'M' => '30', 'N' => '30','O'=>'30','P'=>'30'
        ];
        $pageExcel->setWidth($widthArr);
        $total = $count[0]['cc'];
//        $total = 1000;

        if ($total <= 0) {
            return;
        }
        $limit = 100;
        $page  = ceil($total / $limit);

        for ($i = 0; $i < $page; $i++) {

            $sql =  sprintf("SELECT  a.part_no,a.part_variety_name,
a.other_attr,b.part_status_cn,b.dlr_order_switch_cn,c.car_series_code,c.car_series_cn,
a.car_config_code,a.car_config_cn,c.service_car_type,c.base_series_code,c.base_car_series_cn,b.dlr_price,b.sale_price,b.rep_part_no,b.variety_code_small_name
 from  t_e3s_part_car_series a
join t_e3s_spare_part b on a.part_no=b.part_no and b.is_enable=1
join t_e3s_car_series c on a.car_config_code=c.car_config_code and c.is_enable=1
 where a.is_enable=1 limit %s,%s",$i * $limit, $limit);

            $list =  $maintenance_package->query($sql);
            $data = [];
            foreach ($list as $key => $item) {
                $data[$key]   = [
                    'a'=>$item['part_no'],
                    'b'=>$item['part_variety_name'],
                    'c'=>$item['other_attr'],
                    'd'=>$item['part_status_cn'],
                    'e'=>$item['dlr_order_switch_cn'],
                    'f'=>$item['car_series_code'],
                    'g'=>$item['car_series_cn'],
                    'h'=>$item['car_config_code'],
                    'i'=>$item['car_config_cn'],
                    'j'=>$item['service_car_type'],
                    'k'=>$item['base_series_code'],
                    'l'=>$item['base_car_series_cn'],
                    'm'=>$item['dlr_price'],
                    'n'=>$item['sale_price'],
                    'o'=>$item['rep_part_no'],
                    'p'=>$item['variety_code_small_name']
                ];
            }
            $pageExcel->setData($data, $i + 1, $limit);
        }

        $pageExcel->saveFile(date('Y-m-d') . 'E3S备件.xls');
        echo  $pageExcel->getFilePath();
    }

    public function getUserPhone()
    {
        $userId = input('user_id');
        if (empty($userId)) {
            print_json(1,'用户id不能为空');
        }
        $field = 'id,mid_phone as phone,plat_id as mid';
        $user = DbUser::where('id',$userId)->field($field)->find();
        if (empty($user)) {
            print_json(1,'用户不存在');
        }
        print_json(0,'success', $user);
    }


}
