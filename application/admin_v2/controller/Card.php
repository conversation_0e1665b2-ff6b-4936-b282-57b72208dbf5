<?php
/**
 * 优惠券管理
 * @author: zhangjinpeng
 * @time: 2017-07-24
 */

namespace app\admin_v2\controller;

use app\admin_v2\queue\CardExport;
use app\common\model\db;
use app\common\model\db\DbCommodityCard;
use app\common\model\db\DbExports;
use app\common\model\db\DbSystemValue;
use app\common\net_service\Snowflake;
use app\common\port\connectors\QuickWin;
use app\common\port\connectors\Third;
use app\common\service\CommodityService;
use app\common\model\db\DbCard;
use app\common\model\db\DbDlr;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\wlz\WlzCrowdsInfo;
use think\Exception;
use think\Hook;
use think\Queue;
use think\Request;
use think\Validate;
use app\common\model;
use app\common\service\BaseDataService;

use app\common\model\jxs;
use tool\PhpExcel;
use tool\ImgDown;

class Card extends Common
{

    private $_card_model;
    private $_comm_card_model;
    protected $comm_type_model;
    protected $comm_model;
    protected $_dlrmodel;
    private $_card_rr_model;
    private $gather_list;
    private $receive_range_arr = [1 => '所有人', 2 => '车主', 3 => '指定用户', 4 => '指定用户除外'];
    private $wx_card_type = [1 => 'CASH', 2 => 'DISCOUNT', 3 => 'GIFT', 5 => 'GENERAL_COUPON'];
    private $wx_date_type = [1 => 'DATE_TYPE_FIX_TIME_RANGE', 2 => 'DATE_TYPE_FIX_TERM'];
    protected $_wx_logo_url = "https://mmbiz.qlogo.cn/mmbiz/USfarl8LIiblretzkxb1TI1wxewjJmtWdSymZwEgdnHRTzm5mubNdbkkgIlxnibtJGV6k9vDhpIJGYT1fWhjyd3Q/0?wx_fmt=png";

    function __construct()
    {
        parent::__construct();
        $this->_card_model      = new model\db\DbCard();
        $this->_comm_card_model = new model\db\DbCommodityCard();
        $this->_dlrmodel        = new model\db\DbDlr();
        $this->comm_type_model  = new model\db\DbCommodityType();
        $this->comm_model       = new model\db\DbCommodity();
        $this->_card_rr_model   = new model\bu\BuCardReceiveRecord();
        $this->_setDlr();
        $model             = new DbSystemValue();
        $this->gather_list = $model->where(['value_type' => 26, 'is_enable' => 1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('gather_list', $this->gather_list);
    }

    public function index()
    {
        $card_name = input('card_name');
        $type      = input('type');
        $act_name  = input('act_name');

        $this->assign('card_name', $card_name);
        $this->assign('type', $type);
        $this->assign('act_name', $act_name);

        if (!empty($card_name)) {
            $params['where']['card_name'] = ['like', "%$card_name%"];
        }
        if (!empty($type)) {
            $params['where']['type'] = $type;
        }
        if (!empty($act_name)) {
            $params['where']['act_name'] = $act_name;
        }
        $date                         = date('Y-m-d');
        $params['where'][]            = ['exp', " (date_type=1 AND validity_date_end>='{$date}') OR date_type=2 "];
        $params['where']['is_enable'] = 1;
//        $params['where']['set_type'] = $this->admin_info['type'];
        $params['where']['dlr_code'] = $this->admin_info['dlr_code'];
        $params['order']             = 'created_date desc';
        $params['query']             = input('');


        $list = $this->_card_model->getListPaginate($params);
        $page = $list->render();
        foreach ($list as $key => $val) {
            $val['validity_date'] = $val['validity_date_start'] . ' ~ ' . $val['validity_date_end'];
            if ($val['validity_date'] == ' ~ ') {
                $validity_date_test   = '领取后';
                $validity_date_test   .= $val['fixed_term'] == 0 ? '当' : $val['fixed_term'];
                $val['validity_date'] = $validity_date_test . '天有效';
            }
            if ($val['card_type'] == 4) $val['card_type'] = 1;
            $val['card_type_name'] = $this->_card_model->cardType()[$val['card_type']];
            $list[$key]            = $val;
        }
        $act_name_arr = $this->_card_model->where(['is_enable' => 1, 'dlr_code' => $this->admin_info['dlr_code']])->whereNotNull('act_name')->distinct('act_name')->field('act_name')->select();

        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('act_name_arr', $act_name_arr);
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('card_type_arr', $this->_card_model->cardType());
        $this->assign('type_arr', $this->_card_model->type());
        $this->assign('receive_range_arr', $this->receive_range_arr);
        return $this->fetch('index');
    }

    /**
     * 车生活,改版
     * @return mixed
     */
    public function live()
    {
        $card_name = input('card_name');
        if (!empty($card_name)) {
            $params['where']['card_name'] = ['like', "%$card_name%"];
        }
        $gather_id     = input('get.gather_id', 0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name    = input('get.theme_name');
        $live_type     = input('live_type', 0);
        if (empty($live_type)) {
            $shelves_type                           = 5;
            $params['where']['up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], ['notlike', '%QCSM%'], ['notlike', '%QCAPP%'], 'and'];
        } elseif ($live_type == 1) {
            $shelves_type      = 6;
            $params['where'][] = [['exp', 'FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'], 'or'];
        } elseif ($live_type == 2) {
            $shelves_type      = 7;
            $params['where'][] = [['exp', 'FIND_IN_SET("QCSM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",up_down_channel_dlr)'], 'or'];
        }

        $status = input('get.act_status');  //1未开始 2 进行中 3已结束
        if ($status == 4) {
            $params['where']['is_enable'] = 0;
        } else {
            if (!empty($status)) {
                $params['where']['act_status'] = $status;
                $params['where']['is_enable']  = 1;
            }
        }
        if (!empty($gather_id)) {
            $params['where']['gather_id'] = $gather_id;
        }
        if (!empty($is_pv_subsidy)) {
            if ($is_pv_subsidy != 2) {
                $params['where']['is_pv_subsidy'] = $is_pv_subsidy;
            } else {
                $params['where']['is_pv_subsidy'] = 0;
            }
        }
        if (!empty($theme_name)) {
            $params['where']['theme_name'] = array('like', '%' . $theme_name . '%');
        }
        $params['where']['shelves_type'] = $shelves_type;
        $params['where']['is_succeed']   = 1;
        $params['order']                 = 'created_date desc';
        $params['query']                 = input('');
        $list                            = $this->_card_model->getListPaginate($params);
        $page                            = $list->render();
        $gather_array                    = [];
        foreach ($this->gather_list as $value) {
            $gather_array[$value['id']] = $value['name'];
        }
        foreach ($list as $key => $val) {
            $val['id'] = (string)$val['id'];
            $val['validity_date']  = $val['validity_date_start'] . ' ~ ' . $val['validity_date_end'];
            $val['card_type_name'] = $this->_card_model->cardType()[$val['card_type']];
            $val['status_name']    = empty($val['is_enable']) ? '已关闭' : $this->_card_model->cardStatus()[$val['act_status']];
            $list[$key]            = $val;
            if ($val['gather_id'] == 0 || empty($val['gather_id'])) {
                $val['gather_name'] = '-';
            } else {
                if (isset($gather_array[$val['gather_id']])) {
                    $val['gather_name'] = $gather_array[$val['gather_id']];
                } else {
                    $val['gather_name'] = '-';
                }
            }
            $val['is_pv_subsidy_status'] = $val['is_pv_subsidy'] == 1 ? '是' : '否';

        }
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('card_name', $card_name);
        $this->assign('act_status', $status);
        $this->assign('live_type', $live_type);
        $this->assign('card_type_arr', $this->_card_model->cardType());
        return $this->fetch('live');
    }

    /**
     * 创建优惠券
     */
    public function createStep()
    {
        $live_type = input('live_type', 0);
        $id        = input('id', 0);
        //优惠券详情
        $card_detail = null;
        if (!empty($id)) {
            $card_detail                   = $this->_card_model->getOne(['where' => ['id' => $id]]);
            $card_detail['validity_date']  = date('Y.m.d', strtotime($card_detail['validity_date_start'])) . ' ~ ' . date('Y.m.d', strtotime($card_detail['validity_date_end']));
            $card_detail['can_user_value'] = explode(',', $card_detail['can_user_value']);
        }
        $this->assign('card_detail', $card_detail);
        $this->assign('main_name', '商城优惠劵设置维护');
        $card_type_arr = [1 => '立减券', 2 => '折扣劵', 6 => '到店代金券', 7 => '取送车券'];
        $this->assign('card_type_arr', $card_type_arr);

        $up_down_channel_info    = (new DbSystemValue())->getNameList(20);
        $channels                = $up_down_channel_info;
        $level_parent_value_code = ''; // 会员等级品牌
        if ($live_type == 0) {
            ;
            $channels                = DbDlr::$ni_arr;
            $level_parent_value_code = 'N';
        } elseif ($live_type == 1) {
            $channels = DbDlr::$pz1a_arr;
        } elseif ($live_type == 2) {
            $channels                = DbDlr::$qc_arr;
            $level_parent_value_code = 'V';
        }
        foreach ($up_down_channel_info as $k => $v) {
            if (!in_array($k, $channels)) unset($up_down_channel_info[$k]);
        }
        $base_ser = new \app\common\service\BaseDataService();
        $dlr_list = $base_ser->getShelvesDlrList($id, true, 'card');
        $this->assign('dlr_list', $dlr_list);
        $this->assign('up_down_channel', $up_down_channel_info);
        $this->assign('live_type', $live_type);

        // 人群
        $crowds_model = new WlzCrowdsInfo();
        $crowds_list  = $crowds_model->getList(['where' => ['is_enable' => 1]]);
        $this->assign('crowds_list', $crowds_list);
        // 会员等级
        $level_list = [];
        if (!empty($level_parent_value_code)) {
            $level_list = (new DbSystemValue())->getList(['where' => ['value_type' => 25, 'parent_value_code' => $level_parent_value_code, 'is_enable' => 1]]);
        }
        $this->assign('level_list', $level_list);
        if (empty($card_detail)) {
            return $this->fetch('sc_create_step_live');
        } else {
            $up_down_channel_info = (new DbSystemValue())->getNameList(20);
            foreach ($up_down_channel_info as $k => $v) {
                if (!in_array($k, $channels)) unset($up_down_channel_info[$k]);
            }
            $info_str = implode(',', array_keys($up_down_channel_info));
            $this->assign('up_down_channel', $up_down_channel_info);
            $this->assign('dlr_hidden', (strpos($card_detail['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
            $dlr_name  = DbDlr::whereIn('dlr_code', explode(',', $card_detail['up_down_channel_dlr']))->column('dlr_name');
            $card_name = DbCard::whereIn('id', explode(',', $card_detail['can_with_ids']))->column('card_name');
            $this->assign('dlr_code_str', $card_detail['up_down_channel_dlr']);
            $this->assign('dlr_str', implode(',', $dlr_name));
            $this->assign('dlr_str', $this->getDlrInInfo($card_detail['up_down_channel_dlr']));
            $this->assign('card_str', implode(',', $card_name));

            return $this->fetch('sc_edit_step_live');
        }
    }


    /**
     * 删除
     */
    public function delete()
    {
        $id  = input('id');
        $res = $this->_card_model->allowField(true)->isUpdate(true)->update(['is_enable' => 0], ['id' => $id]);     //自动过滤不存在字段
        if (!$res) {
            print_json(1, $this->_card_model->getError());
        }
        print_json(0, '删除成功');
    }

    /**
     * 获取详情
     */
    public function getOne()
    {
        $card_id = input('card_id');
        $card    = new \api\wechat\Card($this->admin_info['dlr_code']);
//        $res = $card::getCardInfo(array('card_id'=>$card_id));
        $res = $card->detail($card_id);

        $card_info = $res['card'];
        if ($card_info['card_type'] == 'CASH') {        //代金券
            $base_info = $res['card']['cash']['base_info'];
        } else if ($card_info['card_type'] == 'DISCOUNT') {      //折扣券
            $base_info = $res['card']['discount']['base_info'];
        } else if ($card_info['card_type'] == 'GIFT') {      //兑换券
            $base_info = $res['card']['gift']['base_info'];
        } else if ($card_info['card_type'] == 'GENERAL_COUPON') {
            $base_info = $res['card']['general_coupon']['base_info'];
        }
        if ($res['errcode'] == 0) {
            if (!isset($base_info)) {
                print_json(1, '卡券信息不存在');
            }
            print_json(0, '', $base_info);
        } else {
            print_json(1, $res['errmsg']);
        }
    }

    /**
     * 同步微信优惠券
     */
    public function synchronousCard()
    {
        $card_id = input('card_id');
        $card    = new \api\wechat\Card($this->admin_info['dlr_code']);
        $res     = $card->detail($card_id);
        if ($res['errcode'] != 0) {
            print_json(1, $res['errmsg']);
        }
        $card_info = $res['card'];

        if ($card_info['card_type'] == 'CASH') {               //代金券：CASH
            $card_type_info         = $res['card']['cash'];
            $advanced_info          = $res['card']['cash']['advanced_info'];
            $base_info              = $res['card']['cash']['base_info'];
            $card_data['card_type'] = 1;
            if (isset($card_type_info['least_cost']) && intval($card_type_info['least_cost']) != 0) $card_data['least_cost'] = $card_type_info['least_cost'] / 100;     //起用金额
            if (isset($card_type_info['reduce_cost']) && intval($card_type_info['reduce_cost']) != 0) $card_data['card_quota'] = $card_type_info['reduce_cost'] / 100;   //减免金额
            if (!empty($card_data['least_cost'])) $card_data['card_type'] = 4;                    //变成满减券
            $card_data['least_type'] = 1;                                                           //最低消费类型，1金额2指定商品
        } else if ($card_info['card_type'] == 'DISCOUNT') {    //折扣券：DISCOUNT
            $card_type_info         = $res['card']['discount'];
            $advanced_info          = $res['card']['discount']['advanced_info'];
            $base_info              = $res['card']['discount']['base_info'];
            $card_data['card_type'] = 2;
            if (isset($card_type_info['discount']) && intval($card_type_info['discount']) != 0) $card_data['card_discount'] = (100 - $card_type_info['discount']) / 10;       //打折额度
        } else if ($card_info['card_type'] == 'GIFT') {        //兑换券：DISCOUNT
            $card_type_info         = $res['card']['gift'];
            $advanced_info          = $res['card']['gift']['advanced_info'];
            $base_info              = $res['card']['gift']['base_info'];
            $card_data['card_type'] = 3;
            if (isset($card_type_info['least_cost']) && intval($card_type_info['least_cost']) != 0) $card_data['least_cost'] = $card_type_info['least_cost'] / 100;      //起用金额
            if (isset($card_type_info['reduce_cost']) && intval($card_type_info['reduce_cost']) != 0) $card_data['card_quota'] = $card_type_info['reduce_cost'] / 100;    //减免金额
            $card_data['least_type'] = 1;                                                            //最低消费类型，1金额2指定商品
            if (isset($advanced_info['use_condition']['object_use_for'])) {
                $card_data['least_type'] = 2;                                                        //指定商品
                $card_data['least_cost'] = $advanced_info['use_condition']['object_use_for'];
            }
        } else if ($card_info['card_type'] == 'GENERAL_COUPON') {        //优惠券
            $card_type_info              = $res['card']['general_coupon'];
            $advanced_info               = $res['card']['general_coupon']['advanced_info'];
            $base_info                   = $res['card']['general_coupon']['base_info'];
            $card_data['card_type']      = 5;
            $card_data['default_detail'] = $card_type_info['default_detail'];
            if (isset($card_type_info['least_cost']) && intval($card_type_info['least_cost']) != 0) $card_data['least_cost'] = $card_type_info['least_cost'] / 100;     //起用金额
            if (isset($card_type_info['reduce_cost']) && intval($card_type_info['reduce_cost']) != 0) $card_data['card_quota'] = $card_type_info['reduce_cost'] / 100;   //减免金额
        } else {
            print_json(1, '同步失败：暂不支持该类型优惠券的同步');
        }

        $card_data['card_name'] = $base_info['title'];
        $card_data['card_id']   = $base_info['id'];
        $card_data['dlr_code']  = $this->admin_info['dlr_code'];
        $is_exist               = $this->_card_model->getOne(['where' => ['card_id' => $card_data['card_id']]]);

        $card_data['type']         = 1;     //微信优惠券
        $card_data['code_type']    = $base_info['code_type'];
        $card_data['brand_name']   = $base_info['brand_name'];
        $card_data['wx_card_type'] = $card_info['card_type'];
        $card_data['is_enable']    = 1;
//        $card_data['page_id'] = $base_info['pageid'];
//        $card_data['act_name'] = $base_info['activity_name'];
        $card_data['get_limit']       = $base_info['get_limit'];                                 //领取限制
        $card_data['count']           = $base_info['sku']['total_quantity'];                        //数量
        $card_data['available_count'] = $base_info['sku']['quantity'];                    //可用数量
        $card_data['use_des']         = $base_info['description'];                                 //使用说明
        $card_data['color']           = $this->_wxColor($base_info['color']);                         //背景颜色
        $card_data['abstract']        = $advanced_info['abstract']['abstract'];                  //封面简介
        $card_data['apply_des']       = isset($advanced_info['use_condition']['accept_category']) ? $advanced_info['use_condition']['accept_category'] : null;         //适用商品
        $card_data['not_apply_des']   = isset($advanced_info['use_condition']['reject_category']) ? $advanced_info['use_condition']['reject_category'] : null;    //不适用商品

        $url = $advanced_info['abstract']['icon_url_list'][0];                            //封面图片
        if (strpos($url, "http://") == 0) {
            $file   = new ImgDown($url);
            $ossUrl = $file->getOssUrl();       //获取阿里云上传后的地址
        }
        $card_data['icon_image'] = $ossUrl;

        if ($this->admin_info['type'] == 1) {       //平台：可多个适用门店
//            $apply_dlr_code = $this->admin_info['dlr_code'].',';    //适用门店
            $apply_dlr_code = '';
            if (isset($base_info['location_id_list']) && count($base_info['location_id_list']) > 0) {
                $poi_id = implode(',', $base_info['location_id_list']);
                $dlr    = $this->_dlrmodel->getList(['where' => ['poi_id' => ['in', $poi_id], 'is_enable' => 1]]);
                foreach ($dlr as $key => $val) {
                    $apply_dlr_code .= $val['dlr_code'] . ',';
                }
            }
            $apply_dlr_code              = substr($apply_dlr_code, 0, strlen($apply_dlr_code) - 1);  //截取字符串
            $card_data['apply_dlr_code'] = $apply_dlr_code;
        } else {  //专营店：适用自己的门店
            $card_data['apply_dlr_code'] = $this->admin_info['dlr_code'];
        }

        if ($base_info['date_info']['type'] == 'DATE_TYPE_FIX_TIME_RANGE') {        //固定区间有效期
            $card_data['date_type']           = 1;
            $card_data['validity_date_start'] = date('Y-m-d', $base_info['date_info']['begin_timestamp']);   //开始时间
            $card_data['validity_date_end']   = date('Y-m-d', $base_info['date_info']['end_timestamp']);   //结束时间
        } else {          //固定时长有效期
            $card_data['date_type']        = 2;
            $card_data['fixed_term']       = $base_info['date_info']['fixed_term'];                       //领取后x天内有效
            $card_data['fixed_begin_term'] = $base_info['date_info']['fixed_begin_term'];          //x天内生效
//            $card_data['validity_date_start'] = date('Y-m-d',time());   //开始时间
//            $card_data['validity_date_end'] = date('Y-m-d',strtotime($card_data['validity_date_start']." + ".$fixed_term." day"));
        }

        if (empty($is_exist)) {
            $card_data['creator'] = $this->admin_info['username'];
            $res                  = $this->_card_model->allowField(true)->isUpdate(false)->save($card_data);
        } else {
            $card_data['modifier'] = $this->admin_info['username'];
            $res                   = $this->_card_model->allowField(true)->isUpdate(true)->save($card_data, ['id' => $is_exist['id']]);
        }
        if (!$res) {
            print_json(1, '同步失败：' . $this->_card_model->getError());
        }
        print_json(0, '同步成功');
    }

    /**
     * 查询专营店名称
     */
    private function _setDlr()
    {
        $row                          = $this->_dlrmodel->getOne(['where' => ['dlr_code' => $this->admin_info['dlr_code']]]);
        $this->admin_info['dlr_name'] = $row['dlr_name'];

    }

    /**
     * 创建优惠券
     */
    public function createSetp()
    {
        $type = input('type');
        $id   = input('id');
        // $comm_parent_id = input('comm_parent_id');
        //优惠券详情
        $card_detail = null;
        if (!empty($id)) {
            $card_detail                  = $this->_card_model->getOne(['where' => ['id' => $id, 'type' => $type]]);
            $card_detail['validity_date'] = date('Y.m.d', strtotime($card_detail['validity_date_start'])) . ' ~ ' . date('Y.m.d', strtotime($card_detail['validity_date_end']));
            $dlr_codes                    = $this->_dlrmodel->getColumn(['where' => ['dlr_code' => ['in', explode(',', $card_detail['apply_dlr_code'])]], 'column' => 'dlr_code']);
            $dlr_names                    = $this->_dlrmodel->getColumn(['where' => ['dlr_code' => ['in', explode(',', $card_detail['apply_dlr_code'])]], 'column' => 'dlr_name']);
//            $card_detail['apply_dlr_code'] =$dlr_ids;
            $card_detail['apply_dlr_code'] = implode(',', $dlr_codes);
            $card_detail['apply_dlr_name'] = implode(',', $dlr_names);
//            $card_detail['icon_image']     =config('upload.url').$card_detail['icon_image'];
            $card_detail['icon_image'] = $card_detail['icon_image'];
            if ($card_detail['card_type'] == 4) $card_detail['card_type'] = 1;
        }
        $this->assign('card_detail', $card_detail);
        // $this->assign('comm_parent_id',$comm_parent_id);
        // $baseService = new BaseDataService();
        //$dlr_data = $baseService->getDlrList();
        //集团：查找集团下的专营店
        if ($this->admin_info['type'] == 3) {
            $this_dlr          = $this->_dlrmodel->getOne(['where' => ['dlr_code' => $this->admin_info['dlr_code'], 'is_enable' => 1]]);
            $dlr_sub           = $this->_dlrmodel->getColumn(['where' => ['pid' => $this_dlr['id']], 'column' => 'dlr_code']);
            $where['dlr_code'] = ['in', implode(',', $dlr_sub)];
        }

        $baseService = new BaseDataService();
        $dlr_data    = $baseService->getDlrList(0, isset($dlr_sub) ? $dlr_sub : '');

        $this->assign('dlr_data', $dlr_data);
        $this->assign('admin_type', $this->admin_info['type']);

        //专营店门店列表
        $params_dlr = ['where' => ['is_enable' => 1], 'field' => 'dlr_name,dlr_code,id', 'order' => 'id desc'];
        //集团查找集团下的专营店
        if ($this->admin_info['type'] == 3) {
            $this_dlr                   = $this->_dlrmodel->getOne(['where' => ['dlr_code' => $this->admin_info['dlr_code']]]);
            $params_dlr['where']['pid'] = $this_dlr['id'];
        }
        $dlr_list = $this->_dlrmodel->getList($params_dlr);
        $this->assign('dlr_list', $dlr_list);
        $this->assign('type', $type);    //类型：1微信优惠券2商城优惠券
        $this->assign('receive_range_arr', $this->receive_range_arr);
        $this->assign('dlr_name', $this->admin_info['dlr_name']);

        $this->assign('color', $this->_color());
        //微信优惠券
        if ($type == '1') {
            $this->assign('main_name', '微信优惠劵设置维护');
//            $this->assign('card_type_arr',array_slice($this->_card_model->cardType(),0,3,true));
            $this->assign('card_type_arr', [1 => '代金券', 2 => '折扣劵', 3 => '兑换券', 5 => '优惠券']);
        } //商城优惠券
        else if ($type == '2') {
            $this->assign('main_name', '商城优惠劵设置维护');
            $card_type_arr = [1 => '代金券', 2 => '折扣劵'];
            //集团优惠券类型
            if ($this->admin_info['type'] == 3) {
                $card_type_arr = [1 => '代金券', 2 => '折扣劵', 3 => '兑换券'];
            }
            $this->assign('card_type_arr', $card_type_arr);
        }

        if (empty($card_detail)) {
            return $this->fetch('sc_create_step');
        } else {
            return $this->fetch('sc_edit_step');
        }
    }

    /**
     * 新增、编辑微信优惠券的背景色数组
     * @return array
     */
    private function _color()
    {
        //旧的
//        $data=[
//            'Color010'=>'#63b359',
//            'Color020'=>'#2c9f67',
//            'Color030'=>'#509fc9',
//            'Color040'=>'#5885cf',
//            'Color050'=>'#9062c0',
//            'Color060'=>'#d09a45',
//            'Color070'=>'#e4b138',
//            'Color080'=>'#ee903c',
//            'Color081'=>'#f08500',
//            'Color082'=>'#a9d92d',
//            'Color090'=>'#dd6549',
//            'Color100'=>'#cc463d',
//            'Color101'=>'#cf3e36',
//            'Color102'=>'#5E6671',
//        ];
        //新的
        $data = [
            'Color010' => '#55BD47',
            'Color020' => '#10AD61',
            'Color030' => '#35A4DE',
            'Color040' => '#3D78DA',
            'Color050' => '#9058CB',
            'Color060' => '#DE9C33',
            'Color070' => '#EBAC16',
            'Color080' => '#F9861F',
            'Color081' => '#E75735',
            'Color082' => '#D54036',
            'Color090' => '#dd6549',
            'Color100' => '#cc463d',
            'Color101' => '#cf3e36',
            'Color102' => '#5E6671',
        ];
        return $data;
    }

    /**
     * 获取微信Color - 同步优惠券专用
     * （与新增时的颜色值不一样，但与color编码一样）
     * @param $color
     * @return mixed
     */
    private function _wxColor($color)
    {
        $data = [
            '#55BD47' => 'Color010',
            '#10AD61' => 'Color020',
            '#35A4DE' => 'Color030',
            '#3D78DA' => 'Color040',
            '#9058CB' => 'Color050',
            '#DE9C33' => 'Color060',
            '#EBAC16' => 'Color070',
            '#F9861F' => 'Color080',
            '#E75735' => 'Color081',
            '#D54036' => 'Color082',
        ];
        if (array_key_exists($color, $data)) {
            return $data[$color];
        } else {
            return 'Color010';
        }
    }

    /**
     * 优惠券状态不正常-获取提示
     * @param $errcode
     * @return string
     */
    private function get_errmsg($errcode)
    {
        $errmsg = '操作失败，该优惠券';
        switch ($errcode) {
            case  40099:
                return $errmsg . '已核销';
                break;
            case  40073:  //无效的卡券id
//                return '查询不到该优惠券';
                return '微信卡券无效';
                break;
            case  48001:
                return '未添加卡劵功能模块';
                break;
            case  45021:
                return '字段超过长度限制';
                break;
            default:
                return $errmsg . '状态异常';
                break;
        }
    }

    /**
     * 添加或修改优惠券
     */
    public function save()
    {
        //检测导入用户数据
        $data = $this->_impData_check();
        $insert_data['type'] = input('type') or print_json(1, '卡券类型错误');
        $insert_data['card_name'] = input('card_name') or print_json(1, '卡券名称错误');
        $insert_data['card_type'] = input('card_type') or print_json(1, '卡券类型错误');
        $insert_data['wx_card_type']            = $this->wx_card_type[$insert_data['card_type']];
        $insert_data['card_quota']              = input('card_quota', 0);
        $insert_data['card_discount']           = input('card_discount');
        $insert_data['least_type']              = input('least_type');
        $insert_data['least_cost']              = input('least_cost');
        $insert_data['receive_range']           = input('receive_range');
        $insert_data['count']                   = input('count');
        $insert_data['available_count']         = input('available_count');
        $insert_data['default_detail']          = input('default_detail');
        $insert_data['use_des']                 = input('use_des');
        $insert_data['apply_des']               = input('apply_des');
        $insert_data['not_apply_des']           = input('not_apply_des');
        $insert_data['apply_dlr_code']          = empty($_POST['apply_dlr_code']) ? [] : $_POST['apply_dlr_code'];
        $insert_data['commodity_use_type']      = input('commodity_use_type');
        $insert_data['act_name']                = input('act_name', '');
        $insert_data['page_id']                 = input('page_id', '');
        $insert_data['dlr_code']                = $this->admin_info['dlr_code'];
        $insert_data['abstract']                = input('abstract', '');
        $insert_data['icon_image']              = input('icon_image', '');
        $insert_data['center_title']            = input('center_title');
        $insert_data['center_url']              = input('center_url');
        $insert_data['custom_url_name']         = input('custom_url_name');
        $insert_data['custom_url']              = input('custom_url');
        $insert_data['custom_url_sub_title']    = input('custom_url_sub_title');
        $insert_data['promotion_url_name']      = input('promotion_url_name');
        $insert_data['promotion_url']           = input('promotion_url');
        $insert_data['promotion_url_sub_title'] = input('promotion_url_sub_title');
        $insert_data['off_line']                = input('post.off_line', 0);
        //领取限制
        $insert_data['get_limit'] = input('get_limit', 1);
//        $insert_data['get_limit']   =1;  //暂时固定成1
        $action            = input('post.action');
        $insert_data['id'] = $id = input('post.id');
        //$card_id = input('post.card_id');

        //有效期内  判断卡券是否重复
        $where_not_empty            = ['where' => ['card_name' => $insert_data['card_name'], 'dlr_code' => $this->admin_info['dlr_code'], 'is_enable' => 1]];
        $date                       = date('Y-m-d');
        $where_not_empty['where'][] = ['exp', " (date_type=1 AND validity_date_end>='{$date}') OR date_type=2 "];
        $not_empty                  = $this->_card_model->getOne($where_not_empty);
        if ($action == 'add') {
            if ($not_empty) {
                print_json(1, '卡券名称已存在');
            }
        } elseif ($action == 'update') {
            if ($not_empty['id'] != $id) {
                print_json(1, '卡券名称已存在');
            }
        }

        //卡券折扣
        if ($insert_data['card_type'] == 2) {
            $insert_data['card_discount'] = $insert_data['card_quota'];
            $insert_data['card_quota']    = '';
        }
        //有效期
        $insert_data['date_type'] = input('date_type', 1) or print_json(1, '有效期类型丢失');
        if ($insert_data['date_type'] == 1) {       //固定日期区间
            $validity_date = input('validity_date') or print_json(1, '有效期不能为空');
            $validity_date = str_replace('.', '-', $validity_date);
            $validity_date = explode(' ~ ', $validity_date);
            if (empty($validity_date[0]) || empty($validity_date[1])) print_json(1, '有效期不能为空');
            $validity_date[0]                   = date('Y-m-d H:i:s', strtotime($validity_date[0]));
            $validity_date[1]                   = date('Y-m-d H:i:s', strtotime($validity_date[1]));
            $insert_data['validity_date_start'] = $validity_date[0];
            $insert_data['validity_date_end']   = $validity_date[1];
        } else {      //固定时长
            if (input('action') == 'add') {
                $insert_data['fixed_term']       = input('fixed_term') == '' ? print_json(1, '固定时长不能为空') : input('fixed_term');
                $insert_data['fixed_begin_term'] = input('fixed_begin_term') == '' ? print_json(1, '固定时长不能为空') : input('fixed_begin_term');
            }
        }
        //适用门店
        $apply_dlr_code = $insert_data['apply_dlr_code'];
        $dlr_model      = new db\DbDlr();

        if ($this->admin_info['type'] == 1 || $this->admin_info['type'] == 3) {   //平台端、集团
            $insert_data['code_type'] = 'CODE_TYPE_TEXT';
            if (count(explode(',', $apply_dlr_code)) == 0) {
                print_json(1, '专营店不能为空');
            }
        } else {       //专营店端
            $insert_data['apply_dlr_code'] = $this->admin_info['dlr_code'];
        }

        $insert_data['brand_name'] = $this->admin_info['dlr_name'];
        $insert_data['set_type']   = $this->admin_info['type'];
        $insert_data['is_enable']  = input('is_enable', 1);
        if ($action == 'add') {   //添加
            //可用库存
            $insert_data['creator']         = $this->admin_info['username'];
            $insert_data['available_count'] = input('count');
            if ($insert_data['type'] == 1) { //微信优惠券
                $insert_data['color'] = input('post.color') or print_json(1, '颜色不能为空');
                $wx_res                 = $this->wxCardSave($insert_data, $action);
                $insert_data['card_id'] = $wx_res['card_id'];
            } else {
                $insert_data['card_id'] = date('YmdHis') . get_rand_str(5);
            }
            //如果有满减：把优惠券类型设置为 4：满减券
            if (!empty($insert_data['least_cost']) && intval($insert_data['least_cost']) != 0 && $insert_data['card_type'] == 1) {
                $insert_data['card_type'] = 4;
            }
            $res = $this->_card_model->allowField(true)->isUpdate(false)->save($insert_data);     //自动过滤不存在字段
            if (!$res) {
                print_json(1, $this->_card_model->getError(), 1);
            }
            $insert_data['id'] = $this->_card_model->id;
        } else if ($action == 'update') {      //修改
            if (empty($id)) print_json(1, '参数丢失');
            $insert_data['last_updated_date'] = date('Y-m-d H:i:s', time());
            $insert_data['modifier']          = $this->admin_info['username'];

            if ($insert_data['type'] == 1) {   //微信优惠券
                $insert_data['color'] = input('post.color') or print_json(1, '颜色不能为空');
                $allow_field = 'id,color,validity_date_start,validity_date_end,get_limit,default_detail,use_des,available_count,count,page_id,act_name,
                                        receive_range,apply_dlr_code,modifier,last_updated_date,abstract,center_title,center_url,custom_url_name,custom_url,
                                        custom_url_sub_title,promotion_url_name,promotion_url,promotion_url_sub_title';
                $this->checkCount($insert_data, $id);    //检测库存数量
                $wx_res = $this->wxCardSave($insert_data, 'update');
                //如果有满减：把优惠券类型设置为 4：满减券
                if (!empty($insert_data['least_cost']) && intval($insert_data['least_cost']) != 0 && $insert_data['card_type'] == 1) {
                    $insert_data['card_type'] = 4;
                }
                $insert_data = $this->updateCount($insert_data, $id);   //更新库存数量
                $res         = $this->_card_model->allowField($allow_field)->isUpdate(true)->saveData($insert_data, ['id' => $id]);
            } else {
                //如果有满减：把优惠券类型设置为 4：满减券
                if (!empty($insert_data['least_cost']) && intval($insert_data['least_cost']) != 0 && $insert_data['card_type'] == 1) {
                    $insert_data['card_type'] = 4;
                }
                $insert_data = $this->updateCount($insert_data, $id);   //更新库存数量
                $res         = $this->_card_model->allowField(true)->isUpdate(true)->save($insert_data, ['id' => $id]);
            }
            if (!$res) {
                print_json(1, $this->_card_model->getError(), 2);
            }

        }

        $this->_impData($insert_data['id'], $data);      //导入用户数据

        print_json(0, '保存成功');

        //商品使用范围 不需要
        // $this->_saveCommodityCard($insert_data);
    }

    /**
     * 添加或修改优惠券
     */
    public function saveLive()
    {
        $insert_data['card_name'] = input('card_name') or print_json(1, '卡券名称错误');
        $insert_data['card_type'] = input('card_type') or print_json(1, '卡券类型错误');
        $validity_date = input('validity_date') or print_json(1, '有效期不能为空');
        $insert_data['card_quota']      = input('card_quota', 0);
        $insert_data['card_discount']   = input('card_discount');
        $insert_data['least_type']      = input('least_type');
        $insert_data['least_cost']      = input('least_cost');
        $insert_data['available_count'] = input('available_count');
        $insert_data['count']           = input('available_count');
        $insert_data['use_des']         = input('use_des');
        $insert_data['apply_des']       = input('apply_des');
        $insert_data['not_apply_des']   = input('not_apply_des');
        $insert_data['get_limit']       = input('get_limit', 1);
        $insert_data['can_with']        = input('can_with', 0);

        $insert_data['is_enable'] = input('is_enable', 0);

        $input_data                   = input('');
        $insert_data['can_user_type'] = $input_data['can_user_type'] ?? 0;

        if ($insert_data['can_user_type'] == 1) {
            $insert_data['can_user_value'] = implode(',', $input_data['can_user_group'] ?? []);
        } elseif ($insert_data['can_user_type'] == 2) {
            $insert_data['can_user_value'] = implode(',', $input_data['can_user_level'] ?? []);
        }

        $live_type = input('live_type', 0);
        $brand_id  = 0;
        if (empty($live_type)) {
            $insert_data['shelves_type'] = 5;
            $brand_id                    = 1;#日产
            $channel_arr                 = DbDlr::$ni_arr;
        } elseif ($live_type == 1) {
            $insert_data['shelves_type'] = 6;
            $brand_id                    = 9;#ariya
            $channel_arr                 = DbDlr::$pz1a_arr;
        } elseif ($live_type == 2) {
            $insert_data['shelves_type'] = 7;
            $brand_id                    = 2;#启辰
            $channel_arr                 = DbDlr::$qc_arr;
        }

        $insert_data['e3s_activity_id']       = input('e3s_activity_id', 0);
        $insert_data['activity_type']         = input('activity_type', 0);
        $insert_data['settlement_rule_id']    = input('settlement_rule_id', 0);
        $insert_data['settlement_rule_value'] = input('settlement_rule_value', '');
        $insert_data['settlement_rule_type']  = input('settlement_rule_type', 0);
        if (!empty($insert_data['e3s_activity_id'])) {
            if (empty($insert_data['activity_type'])) print_json(1, '存在e3s活动时活动设置类型必选');
        } else {
            $insert_data['activity_type'] = 0;
        }

        $e3s_activity = input('e3s_activity', '');
        if (!empty($e3s_activity)) {
            $insert_data['e3s_activity_name'] = explode(' | ', $e3s_activity)[1];
        }
        $settlement_rule = input('settlement_rule', '');
        if (!empty($settlement_rule)) {
            $insert_data['settlement_rule_name'] = explode(' | ', $settlement_rule)[1];
        }

        if (!empty($insert_data['can_with'])) {
            $insert_data['can_with_ids'] = input('can_with_ids', '');
        } else {
            $insert_data['can_with_ids'] = '';
        }
        $insert_data['can_get_in_detail'] = input('can_get_in_detail', 0);
        $action                           = input('post.action');
        $id                               = input('post.id');

        //有效期内  判断卡券是否重复
//        $where_not_empty = ['where' => ['card_name' => $insert_data['card_name'], 'type' => 2, 'act_status' => ['in', [1, 2]]]];
//        $not_empty       = $this->_card_model->getOne($where_not_empty);
//
//        if ($action == 'add') {
//            if ($not_empty) {
//                print_json(1, '卡券名称已存在');
//            }
//        }
//        elseif ($action == 'update') {
//            if ($not_empty['id'] != $id) {
//                print_json(1, '卡券名称已存在');
//            }
//        }

        //卡券折扣
        if ($insert_data['card_type'] == 2) {
            $insert_data['card_discount'] = $insert_data['card_quota'];
            $insert_data['card_quota']    = '';
        }

        //有效期
        $validity_date = str_replace('.', '-', $validity_date);
        $validity_date = explode(' ~ ', $validity_date);
        if (empty($validity_date[0]) || empty($validity_date[1])) print_json(1, '有效期不能为空');
        $start_time                          = strtotime($validity_date[0]);
        $end_time                            = strtotime($validity_date[1]) + 86400;
        $insert_data['validity_date_start']  = date('Y-m-d', strtotime($validity_date[0]));
        $insert_data['validity_date_end']    = date('Y-m-d', strtotime($validity_date[1]));
        $insert_data['up_down_channel_name'] = $_POST['up_down_channel_name'] ?? '';
        $insert_data['up_down_channel_dlr']  = getUpDownChannel(
            implode(',', $_POST['up_down_channel'] ?? []),
            input('post.dlr_code', '')
        );

        $time = time();
        if ($start_time > $time && $end_time > $time) {
            $status = 1;
        } else if ($start_time < $time && $end_time > $time) {
            $status = 2;
        } else {
            $status = 3;
        }
        $insert_data['act_status'] = $status;
        $discount_num              = 0;
        $discount_price            = $insert_data['card_quota'];
        switch ($insert_data['card_type']) {
            case 2:
                $coupon_type    = 3;
                $discount_num   = $insert_data['card_discount'];
                $discount_price = 0;
                break;
            case 7:
            case 6:
                $coupon_type                        = 1;
                $insert_data['up_down_channel_dlr'] = implode(',', $channel_arr);
                $up_down_channel_info               = (new DbSystemValue())->getNameList(20);
                $up_down_channel_name               = [];
                foreach ($up_down_channel_info as $k => $v) {
                    if (in_array($k, $channel_arr)) {
                        $up_down_channel_name[] = $v;
                    }
                }
                $insert_data['up_down_channel_name'] = implode(',', $up_down_channel_name);
                $insert_data['get_limit']            = 0;
                break;
            default:
                $coupon_type = 1;
        }
//        $insert_data['limit_count'] = input('post.limit_count') ?? '';
//        $insert_data['receive_start_time'] = input('post.start_time') ?? '';
//        $insert_data['receive_end_time'] = input('post.end_time') ?? '';
        //新增业务归属 start
        $insert_data['gather_id']     = input('gather_id');
        $insert_data['is_pv_subsidy'] = input('is_pv_subsidy');
        $insert_data['theme_name']    = input('theme_name');
        $insert_data['card_desc']     = input('card_desc') ?? '';
        //新增业务归属 end
        $params = [
            'brand_id'           => $brand_id,
            'coupon_category'    => 0,
            'can_share'          => 0,
            'coupon_num'         => $insert_data['available_count'],
            //            'inventory'         => $insert_data['available_count'],
            'coupon_title'       => $insert_data['card_name'],
            'coupon_type'        => $coupon_type,
            'deadline_type'      => 1,
            'use_start_date'     => $insert_data['validity_date_start'] . ' 00:00:00',
            'use_end_date'       => $insert_data['validity_date_end'] . ' 23:59:59',
            'receive_start_date' => $insert_data['validity_date_start'] . ' 00:00:00',
            'receive_end_date'   => $insert_data['validity_date_end'] . ' 23:59:59',
            'discount_num'       => $discount_num,
            'discount_price'     => $discount_price * 100, // 代金券优惠金额 单位分
            'is_enable'          => 1,
            'rule_content'       => $insert_data['use_des'],
            'receive_limit'      => $insert_data['get_limit'],
            'coupon_desc'        => '',
            'created_role_type'  => 1, // 创建角色 1厂家 2专营店
            'publish_object'     => $insert_data['card_type'] == 7 ? 2 : 1, // 发布对象 1用户 2车辆
            'receive_rule'       => $insert_data['card_type'] == 7 ? 2 : 1, // 领取规则 1-用户 2-vin
            'consume_type'       => 1, // 核销方式 1-在线核销 2-门店核销 3-DMS核销
            'consume_condition'  => $insert_data['card_type'] == 6 ? 1 : 0, // 核销条件 1核销门店与意向店铺一致 0否
            'receive_scene_id'   => $insert_data['card_type'] == 7 ? config('win_card.door_scene_id') : config('win_card.receive_scene_id'), // 领取场景id
            'min_consumption'    => empty($insert_data['least_cost']) ? $insert_data['least_cost'] : $insert_data['least_cost'] * 100,
            'front_scene_type'   => 2,
            'message_type'       => 3,
            'remark'             => '',
            //            'limit_count'       => input('post.limit_count') ?? '',
            //            'receive_start_time'       => input('post.start_time') ?? '',
            //            'receive_end_time'       => input('post.end_time') ?? '',
        ];
        if ($insert_data['card_type'] == 2) {
            unset($params['discount_price']);
        }

        if (!empty($insert_data['e3s_activity_id'])) {
            $params['activity_id']   = $insert_data['e3s_activity_id'];
            $params['activity_type'] = $insert_data['activity_type'];
        }
//        if ($insert_data['settlement_rule_id'] > 0){
//            $params['settlement_rule_id'] = $insert_data['settlement_rule_id'];
//        }
        if ($action == 'add') {   //添加
            //可用库存
            $insert_data['creator']           = $this->admin_info['username'];
            $insert_data['consume_condition'] = $params['consume_condition'];
            $insert_data['card_id']           = date('YmdHis') . get_rand_str(5);

            if ($brand_id > 0) {
                $card_re = QuickWin::create('quick_win')->addCouponInfo($params);
                if ($card_re['result'] != 1) print_json(1, '速赢卡券中心：' . $card_re['msg']);
                $insert_data['quick_win_card_id'] = $card_re['rows']['id'];
            }
            Snowflake::setWorkerId(1); // 设置工作机器 ID
            Snowflake::setDatacenterId(1); // 设置数据中心 ID

            $id                = Snowflake::nextId(); // 生成主键id
            $insert_data['id'] = $id;
            $res               = $this->_card_model->allowField(true)->isUpdate(false)->save($insert_data);     //自动过滤不存在字段
            if (!$res) {
                print_json(1, $this->_card_model->getError(), 1);
            }
//            $id = $this->_card_model->getLastInsID();
            $params['creator'] = $insert_data['creator'];

        } else if ($action == 'update') {      //修改
            $insert_data['last_updated_date'] = date('Y-m-d H:i:s', time());
            $insert_data['modifier']          = $this->admin_info['username'];
            $params['modifier']               = $insert_data['modifier'];
            $params['modified_date']          = $insert_data['last_updated_date'];
            if (empty($id)) print_json(1, '参数丢失');
            $quick_win_card_id = input('quick_win_card_id', 0);
            $params['id']      = $quick_win_card_id;
            if ($quick_win_card_id > 0) {
//                dd(QuickWin::create('quick_win')->getCouponInfo($quick_win_card_id));
//                $is_change     = input('is_change', 0);
                $quickWinInfo = QuickWin::create('quick_win')->getCouponInfo($quick_win_card_id);
//                dd($quickWinInfo['rows']['coupon_status']);
                $params['coupon_num'] = $params['coupon_num'] - $quickWinInfo['rows']['inventory'] + $quickWinInfo['rows']['coupon_num'];
                if ($quickWinInfo['rows']['coupon_num'] == 0){
                    $params['coupon_num'] = 0;
                }
                if ($params['coupon_num'] < 0) {
                    print_json(1, '库存冲突');
                }
                if ($quickWinInfo['rows']['coupon_status'] == 1) {
                    QuickWin::create('quick_win')->postCouponInfo($quick_win_card_id);
                }
                $card_re = QuickWin::create('quick_win')->updateCouponInfo($quick_win_card_id, $params);
                if ($card_re['result'] != 1) print_json(1, '速赢卡券中心：' . $card_re['msg']);
//                $is_redirect_back = input('is_redirect_back', 0);
//                if($is_redirect_back == 1){
                QuickWin::create('quick_win')->postCouponInfo($quick_win_card_id);
//                }
            }
            $res = $this->_card_model->allowField(true)->isUpdate(true)->save($insert_data, ['id' => $id]);
            if (!$res) {
                print_json(1, $this->_card_model->getError(), 2);
            }
        }

        print_json(0, '保存成功', ['id' => (string)$id]);
    }


    /**
     * 更新库存数量
     * @param $insert_data
     */
    private function updateCount($insert_data, $id)
    {
        $available_count = intval($insert_data['available_count']);
        if (!empty($available_count) && $available_count != 0) {
            $card_info = $this->_card_model->getOne(['where' => ['id' => $id]]);
            if ($available_count > 0) {     //正数：加库存
                $insert_data['available_count'] = $card_info['available_count'] + $available_count;
                if ($insert_data['available_count'] > 100000000) {
                    print_json(1, '现有库存数量不能超过100000000');
                }
                $insert_data['count'] = $card_info['count'] + $available_count;
            } else {      //负数：减库存
                $available_count = abs($available_count);
                if ($available_count > $card_info['available_count']) {
                    print_json(1, '当前可用库存不足，无法减少库存');
                }
                $insert_data['available_count'] = $card_info['available_count'] - $available_count;
                $insert_data['count']           = $card_info['count'] - $available_count;
            }
        } else {      //没有改动库存数量
            unset($insert_data['count']);
            unset($insert_data['available_count']);
        }
        return $insert_data;
    }

    /**
     * 检测库存数量
     */
    private function checkCount($insert_data, $id)
    {
        $available_count = intval($insert_data['available_count']);
        if (!empty($available_count) && $available_count != 0) {
            $card_info = $this->_card_model->getOne(['where' => ['id' => $id]]);
            if ($available_count > 0) {     //正数：加库存
                $insert_data['available_count'] = $card_info['available_count'] + $available_count;
                if ($insert_data['available_count'] > 100000000) {
                    print_json(1, '现有库存数量不能超过100000000');
                }
            } else {      //负数：减库存
                $available_count = abs($available_count);
                if ($available_count > $card_info['available_count']) {
                    print_json(1, '当前可用库存不足，无法减少库存');
                }
            }
        }
    }

    /**
     * 提交微信接口
     * @param $data
     * @param $action
     * @return array|bool|mixed
     */
    public function wxCardSave($data, $action)
    {
        $dlr_code      = $this->admin_info['dlr_code'];
        $card          = new  \api\wechat\Card($this->admin_info['dlr_code']);
        $icon_url_list = '';
        if ($action == 'add' && !empty($data['icon_image'])) {  //添加时上传图片
            //替换阿里云链接
            $data['icon_image'] = str_replace('http://wx-dealer.oss-cn-shenzhen.aliyuncs.com/', '', $data['icon_image']);
            $up_res             = $card->upload('./public/uploads/' . $data['icon_image']);
            if (!$up_res) {
                print_json(1, $card->getError()['errmsg'], 3);
            }
            $icon_url_list = $up_res['url'];
        }
        $params = [
            'logo_url'                => $this->_wx_logo_url,
            'brand_name'              => $data['brand_name'],
            'card_type'               => $this->wx_card_type[$data['card_type']],
            'code_type'               => 'CODE_TYPE_TEXT',
            'title'                   => $data['card_name'],
            //  'sub_title'     =>'测试代金券小标题',
            'color'                   => $data['color'],
            'notice'                  => '请出示优惠券',
            'default_detail'          => $data['default_detail'],
            'description'             => $data['use_des'],
            'sku_quantity'            => $data['count'],
            'begin_timestamp'         => isset($data['validity_date_start']) ? strtotime($data['validity_date_start']) : null,
            'end_timestamp'           => isset($data['validity_date_end']) ? strtotime($data['validity_date_end']) + 86399 : null,
            'date_type'               => $this->wx_date_type[$data['date_type']],
            'fixed_term'              => isset($data['fixed_term']) ? $data['fixed_term'] : null,
            'fixed_begin_term'        => isset($data['fixed_begin_term']) ? $data['fixed_begin_term'] : null,
            'abstract'                => $data['abstract'],
            'page_id'                 => $data['page_id'],
            'act_name'                => $data['act_name'],
            'receive_range'           => $data['receive_range'],
            'icon_url_list'           => $icon_url_list,
            'accept_category'         => $data['apply_des'],
            'reject_category'         => $data['not_apply_des'],
            'get_limit'               => $data['get_limit'],
            /*  'least_cost'     =>'322',
              'reduce_cost'    =>'3232',*/
            'center_title'            => $data['center_title'],
            'center_url'              => $data['center_url'],
            'custom_url_name'         => $data['custom_url_name'],
            'custom_url'              => $data['custom_url'],
            'custom_url_sub_title'    => $data['custom_url_sub_title'],
            'promotion_url_name'      => $data['promotion_url_name'],
            'promotion_url'           => $data['promotion_url'],
            'promotion_url_sub_title' => $data['promotion_url_sub_title'],
        ];

        /**
         * 专营店端：
         * use_all_locations == true，
         * location_id_list == []；
         */
        if ($this->admin_info['type'] == 2) {
            $params['use_all_locations'] = true;
            $params['location_id_list']  = [];
        } else {
            $params['use_all_locations'] = false;
            //查询使用门店
            $params['location_id_list'] = $this->_dlrmodel->getColumn(['where' => ['dlr_code' => ['in', explode(',', $data['apply_dlr_code'])]], 'column' => 'poi_id']);
        }
        if ($data['card_type'] == 1) {    //代金券
            $params['least_cost']  = $data['least_cost'];
            $params['reduce_cost'] = $data['card_quota'] * 100;
        } elseif ($data['card_type'] == 2) {  //折购券
            $params['discount'] = 100 - $data['card_discount'] * 10;

        } elseif ($data['card_type'] == 3) {     //兑换券
            $params['gift'] = '';
            if ($data['least_type'] == 1) {
                $params['least_cost'] = $data['least_cost'];
            } else {
                $params['object_use_for'] = $data['least_cost'];
            }

//            $params['reduce_cost']=$data['card_quota']*100;
        }
        $wx_card = new model\bu\BuWxCard();
        if ($action == 'add') {
            $location_id_list           = $params['location_id_list'];
            $params['location_id_list'] = implode(',', $location_id_list);
            $wx_res                     = $wx_card->allowField(true)->isUpdate(false)->save($params);
            if (!$wx_res) {
                print_json(1, $wx_card->getError(), 4);
            }
            $params['location_id_list'] = $location_id_list;
            /*  var_dump($params);
              var_dump($wx_res);*/
            $result = $card->create($params);

            if (!$result) {
                $error = $card->getError();
                $wx_card->where('id', $wx_card->id)->update(['errmsg' => $error['errmsg'], 'errcode' => $error['errcode']]);
                $msg = $this->get_errmsg($error['errcode']);
                print_json(1, $msg, 5);
            } else {
                return $result;
            }
            /*   var_dump($result);
               var_dump($card->getError());*/
        } else if ($action == 'update') {
            $row = $this->_card_model->getOneByPk($data['id']);
            if ($row) {
                if ($data['date_type'] == 1) {
                    if (strtotime($data['validity_date_start']) == strtotime($row['validity_date_start'])) {
                        unset($params['begin_timestamp']);
                    } else if (strtotime($data['validity_date_start']) > strtotime($row['validity_date_start'])) {
                        print_json(1, '新开始时间必须小于或等于旧的开始时间');
                    }
                    if (strtotime($data['validity_date_end']) == strtotime($row['validity_date_end'])) {
                        unset($params['end_timestamp']);
                    } else if (strtotime($data['validity_date_end']) < strtotime($row['validity_date_end'])) {
                        print_json(1, '新结束时间必须大于或等于旧的结束时间');
                    }
                }
//                if(strtotime($data['validity_date_start'])<=strtotime($row['validity_date_start']) || strtotime($data['validity_date_end'])<=strtotime($row['validity_date_end'])){
//                    print_json(1,'新开始时间必须大于旧的开始时间,新结束时间必须大于旧的结束时间');
//                }
                $params['card_id'] = $row['card_id'];
                $result            = $card->update($params);
                if (!$result) {
                    $error = $card->getError();
                    $msg   = $this->get_errmsg($error['errcode']);
                    print_json(1, $msg, 6);
                }
                //更新现有库存
                $available_count = intval($data['available_count']);
                if (!empty($available_count) && $available_count != 0) {
                    $increase = 0;         //增加数
                    $reduce   = 0;           //减少数
                    if ($available_count > 0) {
                        $increase = $available_count;
                    } else {
                        $reduce = $available_count;
                    }
                    $result = $card->stock($params['card_id'], $increase, $reduce);
                }
                if (!$result) {
                    $error = $card->getError();
                    $msg   = $this->get_errmsg($error['errcode']);
                    print_json(1, $msg, 7);
                }
                return $result;
            }

        }

        //var_dump(serialize($params));

    }


    //更新卡券-商品使用范围
    private function _saveCommodityCard($save_data)
    {
        $save_data['commodity_id'] = explode(',', $save_data['commodity_id']);
        if (count($save_data['commodity_id']) > 0) {
            $commodity_id   = [];
            $comm_card_list = $this->_comm_card_model->getlist(['where' => ['card_id' => $save_data['id']]]);
            foreach ($comm_card_list as $k => $v) {
                $commodity_id[] = $v['commodity_id'];
            }
            //不存在的新增
            foreach ($save_data['commodity_id'] as $key => $val) {
                if (!in_array($val, $commodity_id)) {
                    $save_comm_card = [
                        'commodity_id' => $val,
                        'card_id'      => $save_data['id'],
                        'creator'      => $this->admin_info['username'],
                    ];
                    $this->_comm_card_model->insert($save_comm_card);
                }
            }
            //删除没有选中的
            foreach ($commodity_id as $k => $v) {
                if (!in_array($v, $save_data['commodity_id'])) {
                    $this->_comm_card_model->where(['commodity_id' => $v])->delete();
                }
            }
        }
    }

    /**
     * 根据父级id获取 商品分类
     */
    public function ajaxGetCommByParentId()
    {
        $comm_parent_id = input('comm_parent_id');
        print_json(0, '', $this->comm_type_model->getCommodityByParentId($comm_parent_id));
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetCommodityList()
    {
        $commodity_name  = input('commodity_name');
        $comm_type_id    = input('comm_type_id');
        $comm_parent_id  = input('comm_parent_id');
        $params['where'] = [];
        $params['query'] = [];
        if (!empty($commodity_name)) {
            $params['where'] = ['a.commodity_name' => ['like', "%$commodity_name%"]];
        }
        if (!empty($comm_type_id)) {
            $params['where']['a.comm_type_id'] = $comm_type_id;
        } else {
            if (!empty($comm_parent_id)) {
                $comm_type_column                  = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            }
        }

        $params['field']                      = 'a.*,b.comm_type_name,b.comm_parent_id';
        $params['order']                      = 'a.id desc';
        $params['query']                      = input('get.');
        $params['pagesize']                   = input('pagesize');
        $params['where']['a.create_dlr_code'] = $this->admin_info['dlr_code'];
        $params['where']['a.is_enable']       = 1;

        $list              = $this->comm_model->getCommodityList($params);
        $comm_parent_list2 = $this->comm_type_model->getCommTypeName();

        $res                      = [];
        $res['list']              = $list;
        $res['comm_parent_list2'] = $comm_parent_list2;
        print_json(0, '', $res);
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    private function getCommodityList($commodity_id)
    {
        $params['where'] = [];
        $params['query'] = [];
        if (!empty($commodity_id)) {
            $params['where']['a.id'] = ['in', $commodity_id];
        }
        $params['field'] = 'a.*,b.comm_type_name,b.comm_parent_id';
        $params['order'] = 'a.id desc';
        $params['query'] = input('get.');

        $params['where']['a.create_dlr_code'] = $this->admin_info['dlr_code'];
        $params['where']['a.is_enable']       = 1;
        $params['pagesize']                   = 1000;

        return $this->comm_model->getCommodityList($params);
    }

    /**
     * 检测导入用户数据
     * @return array|null|\think\File
     */
    private function _impData_check()
    {
        $file = request()->file('act_personnel');
        $data = null;
        if (!empty($file)) {
            $res = PhpExcel::import_csv($file);
            if ($res['error']) {
                print_json(1, $res['msg'], 8);
            }
            $data      = $res['data'];
            $title     = array_flip($data[0]);
            $title_arr = [
                'VIN码', '专营店编码'
            ];
            foreach ($title_arr as $key => $val) {
                if (!isset($title[$val])) print_json(1, '导入失败,缺少"' . $val . '"字段');
            }
        }
        return $data;
    }

    /**
     * 导入、上传用户信息
     * @param $card_id
     */
    private function _impData($card_id, $data)
    {
        if (!empty($data)) {
            $title     = array_flip($data[0]);
            $title_arr = [
//                'VIN码','专营店编码','卡级别','活动名称','活动编码',
'VIN码', '专营店编码'
            ];
            foreach ($title_arr as $key => $val) {
                if (!isset($title[$val])) print_json(1, '导入失败,缺少"' . $val . '"字段');
            }

            unset($data[0]);
            $_ActJxs = new model\jxs\JxsActivityPersonnel();
            foreach ($data as $key => $val) {
                $arr[] = [
                    'vin'      => $val[$title['VIN码']],
                    'dlr_code' => $val[$title['专营店编码']],
                    //                    'card_level'      =>$val[$title['卡级别']],
                    'card_id'  => $card_id,
                    //                    'activityname'    =>$val[$title['活动名称']],
                    //                    'activit_code'    =>$val[$title['活动编码']],
                ];
                if (count($arr) >= 0 or $key == count($data)) {
                    $res = $_ActJxs->insertAll($arr);
                    if (!$res) {
                        print_json(1, '导入失败');
                    } else {
                        unset($arr);
                    }
                    print_json(0, '保存成功，优惠券用户信息已上传');
                }
            }

        }
    }

    //商品管理卡券
    public function commodityCard()
    {
        $card_id = input('get.card_id') or print_json(1);
        $commodity_name  = input('get.commodity_name');
        $comm_type_id    = input('get.comm_type_id');
        $comm_parent_id  = input('get.comm_parent_id');
        $commodity_type  = input('get.commodity_type');
        $admin_type      = $this->admin_info['type'];
        $dlr_code        = $this->admin_info['dlr_code'];
        $commodity_class = input('get.commodity_class');

        $params['where']   = [];
        $params['query']   = [];
        $params['card_id'] = $card_id;

        $card_row = $this->_card_model->getOneByPk($card_id);

        if (!empty($commodity_name)) {
            $params['where'] = ['commodity_name' => ['like', "%$commodity_name%"]];
        }
        if (!empty($comm_type_id)) {
            $params['where']['a.comm_type_id'] = $comm_type_id;

        } else {
            if (!empty($comm_parent_id)) {
                $comm_type_column                  = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            }
        }

        if (!empty($commodity_class)) {
            $params['where']['a.commodity_class'] = $commodity_class;
        }

        $comm_type_list = [];
        if (!empty($params['where']['a.comm_type_id'])) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        if ($admin_type == 1) {
            $params['where']['e.dlr_code']     = 'NISSAN';
            $params['where']['e.shelves_type'] = ['in', [1, 3]];
        } else {
            $params['where']['e.dlr_code']     = $dlr_code;
            $params['where']['e.shelves_type'] = 2;
        }

        $params['dlr_code']             = $dlr_code;
        $params['field']                = 'a.*,b.comm_type_name,b.comm_parent_id,e.id as commodity_set_id,e.set_type as dlr_set_type,e.commodity_attr as commodity_set_attr,e.created_date as shelves_date,e.original_price_range_start original_s_price,' .
            ' e.original_price_range_end original_e_price ,e.discount_price_range_start discount_s_price , e.discount_price_range_end as discount_e_price,e.is_mail set_is_mail,e.count_stock set_count_stock,e.shelves_type,e.commodity_id,f.id_str,f.card_type_str,f.is_can_receive_str';
        $params['order']                = 'a.id desc';
        $params['query']                = input('get.');
        $params['where']['a.is_enable'] = 1;
        $list                           = $this->comm_model->getCommodityCardList($params);
        $comm_parent_list               = $this->comm_type_model->getCommTypeName();
        $commodity_class_arr            = db\DbCommodity::commodityClass();
//        print_json(1,'',$comm_parent_list);
        $shelves_type_arr = db\DbCommodityDlr::shelvesType();
        $commodity_ids    = [];
        foreach ($list as $key => $val) {
            if (!empty($val['set_is_mail'])) $val['is_mail'] = $val['set_is_mail'];
            if (!empty($val['set_count_stock'])) $list[$key]['count_stock'] = $val['set_count_stock'];
            if (!empty($val['original_s_price'])) $list[$key]['original_price_range_start'] = $val['original_s_price'];
            if (!empty($val['original_e_price'])) $list[$key]['original_price_range_end'] = $val['original_e_price'];
            if (!empty($val['discount_s_price'])) $list[$key]['discount_price_range_start'] = $val['discount_s_price'];
            if (!empty($val['discount_e_price'])) $list[$key]['discount_price_range_end'] = $val['discount_e_price'];

            $list[$key]['is_shop_name']         = $val['is_shop'] == 1 ? '是' : '否';
            $list[$key]['is_pure_name']         = $val['is_pure'] == 1 ? '是' : '否';
            $list[$key]['is_mail_name']         = $val['is_mail'] == 1 ? '是' : '否';
            $list[$key]['commodity_attr_name']  = db\DbCommodity::attribute($val['commodity_set_attr']);
            $list[$key]['cover_image']          = config('upload.url') . $val['cover_image'];
            $list[$key]['shelves_date']         = !empty($val['shelves_date']) ? $val['shelves_date'] : '-';
            $list[$key]['commodity_class_name'] = $commodity_class_arr[$val['commodity_class']];
            $list[$key]['shelves_type']         = $shelves_type_arr[$val['shelves_type']];
            if ($val['comm_parent_id'] && isset($comm_parent_list[$val['comm_parent_id']])) {
                $list[$key]['comm_type_name'] = $comm_parent_list[$val['comm_parent_id']] . '>' . $val['comm_type_name'];
            }
            if (empty($val['dlr_set_type'])) {
                $list[$key]['dlr_set_type_name'] = '_';
            } else if ($val['dlr_set_type'] == 1) {
                $list[$key]['dlr_set_type_name'] = '总部';
            } else {
                $list[$key]['dlr_set_type_name'] = '自己';
            }
            $commodity_ids[]                   = $val['id'];
            $list[$key]['shelves_source']      = '';
            $list[$key]['commodity_card_id_1'] = 0;
            $list[$key]['is_can_receive_1']    = 0;
            $list[$key]['commodity_card_id_2'] = 0;
            $list[$key]['is_can_receive_2']    = 0;
            if (!empty($val['card_type_str'])) {
                $card_type_arr = explode(',', $val['card_type_str']);

                $card_type_1_key = array_search('1', $card_type_arr);
                $card_type_2_key = array_search('2', $card_type_arr);

                if ($card_type_1_key !== false) {
                    $list[$key]['commodity_card_id_1'] = explode(',', $val['id_str'])[$card_type_1_key];
                    $list[$key]['is_can_receive_1']    = explode(',', $val['is_can_receive_str'])[$card_type_1_key];
                }
                if ($card_type_2_key !== false) {
                    $list[$key]['commodity_card_id_2'] = explode(',', $val['id_str'])[$card_type_2_key];
                    $list[$key]['is_can_receive_2']    = explode(',', $val['is_can_receive_str'])[$card_type_2_key];

                }

            }


        }

        $page = $list->render();
        $this->assign('commodity_attribute', db\DbCommodity::attribute());
        $this->assign('page', $page);
        $this->assign('list', $list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('comm_type_id', $comm_type_id);
        $this->assign('commodity_name', $commodity_name);
        $this->assign('commodity_type', $commodity_type);
        $this->assign('admin_type', $admin_type);
        $this->assign('dlr_code', $dlr_code);
        $this->assign('commodity_class', $commodity_class_arr);
        $this->assign('card_row', $card_row);
        $this->assign('card_id', $card_id);
        return $this->fetch('commodity_card');
        //  return $list;
    }

    //商品管理卡券
    public function commodityCardLive()
    {
        $card_id = input('get.card_id') or print_json(1);
        $commodity_name            = input('get.commodity_name');
        $comm_type_id              = input('get.comm_type_id');
        $comm_parent_id            = input('get.comm_parent_id');
        $commodity_type            = input('get.commodity_type');
        $third_type                = input('get.three_comm_type_id');
        $second_type               = input('get.sub_comm_type_id');
        $commodity_class           = input('get.commodity_class');
        $relate_or_not             = input('get.relate_or_not');
        $live_type                 = input('get.live_type', 0);
        $commodity_shellcheck_type = input('get.commodity_shellcheck_type', 1);
        $receive_type              = input('receive_type', 1);
        $where                     = [];
        $query                     = [];
        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
            $query['commodity_name']   = $commodity_name;
        }

        $type_id = 0;
        if (!empty($third_type)) {
            $type_id                     = $third_type;
            $query['three_comm_type_id'] = $third_type;
        } else if (!empty($second_type)) {
            $type_id                   = $second_type;
            $query['sub_comm_type_id'] = $second_type;

        } else if (!empty($comm_parent_id)) {
            $type_id                 = $comm_parent_id;
            $query['comm_parent_id'] = $comm_parent_id;
        }

        if (!empty($commodity_class)) {
            $where['commodity_class'] = $commodity_class;
            $query['commodity_class'] = $commodity_class;
        }

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }

        if (strlen($relate_or_not) > 0) {
            $query['relate_or_not'] = $relate_or_not;
            $card_commodity_id      = $this->_comm_card_model->where(['card_id' => $card_id])->column('commodity_id');
            $card_commodity_id      = implode(',', $card_commodity_id);
            if (!empty($relate_or_not)) {
                if (!empty($card_commodity_id)) {
                    $where[] = ['exp', " (find_in_set({$card_id},a.card_id) or a.commodity_id in ({$card_commodity_id}) ) "];
                } else {
                    $where[] = ['exp', " (find_in_set({$card_id},a.card_id)) "];
                }
            } else {
                if (!empty($card_commodity_id)) {
                    $where[] = ['exp', " (find_in_set({$card_id},a.card_id) = false and a.commodity_id not in ({$card_commodity_id}) ) "];
                } else {
                    $where[] = ['exp', " (find_in_set({$card_id},a.card_id) = false)"];
                }

            }
        }
        if (empty($live_type)) {
            $shelves_type = 5;
        } elseif ($live_type == 1) {
            $shelves_type = 6;
        } elseif ($live_type == 2) {
            $shelves_type = 7;
        }
        $query['live_type']      = $live_type;
        $where['a.shelves_type'] = $shelves_type;
        $where['b.shelves_type'] = $shelves_type;

        $field  = "a.id,a.commodity_id,a.commodity_name,a.cover_image,a.price,a.commodity_set_id,a.up_down_channel_name,
        b.count_stock,a.card_id,a.commodity_class,b.commodity_dlr_type_id,comm_type_id_str,c.is_grouped";
        $params = array(
            'query'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "a.commodity_id desc",
            'field'    => $field
        );

        $flat                 = new DbCommodityFlat();
        $list                 = $flat->getCommodityList($params);
        $commodity_class_arr  = DbCommodity::commodityClass();
        $card_model           = new DbCard();
        $commodity_card_model = new DbCommodityCard();
        $card_commodity       = $commodity_card_model->alias('a')
            ->join('t_db_commodity b', 'a.commodity_id=b.id')
            ->where('a.card_id', $card_id)
            ->field('a.*,b.is_grouped')
            ->group('a.commodity_id')
            ->select();

        $relate_arr  = [];
        $association = [];
        foreach ($card_commodity as $value) {
            $relate_arr[$value['commodity_id']] = $value['is_can_receive'];
            // 组合商品
            if ($value['is_grouped']) {
                // 查询所有子商品的set_sku_ids
                $map         = [
                    'card_id'          => $card_id,
                    'commodity_id'     => $value['commodity_id'],
                    'commodity_set_id' => $value['commodity_set_id'],
                    'is_enable'        => 1
                ];
                $arr         = $commodity_card_model->where($map)->column('set_sku_ids');
                $set_sku_arr = [];
                foreach ($arr as $k => $item) {
                    $set_sku_arr = array_merge($set_sku_arr, explode(',', $item));
                }
                $set_sku_arr = array_filter($set_sku_arr);
            } else {
                if (!empty($value['set_sku_ids'])) {
                    $set_sku_arr = explode(',', $value['set_sku_ids']);
                } else {
                    $set_sku_arr = [];
                }
            }
            $set_sku_arr = array_map('intval', $set_sku_arr);

            $association[$value['commodity_id']] = [
                'commodity_id'     => $value['commodity_id'],
                'commodity_set_id' => $value['commodity_set_id'],
                'card_id'          => $card_id,
                'action'           => true,
                'is_can_receive'   => $value['is_can_receive'],
                'card_type'        => $value['card_type'],
                'set_sku_list'     => $set_sku_arr,
                'is_grouped'       => $value['is_grouped'],
                'group_card_type'  => $value['group_card_type'],
            ];
        }
//        $relate_arr = DbCommodityCard::where('card_id', $card_id)->column('commodity_id,is_can_receive');
        $card_info = $card_model->where(['id' => $card_id])->find();
        $this->assign('card_type', $card_info['card_type']);
        //当前页面数据总数
        $listTotal = count($list);
        //关联当前页所有商品-全选状态
        $allChecked = '';
        //关联当前页所有商品-全选-选中数量
        $allCheckedTotal = 0;
        //全部可领取-全选状态
        $allIsCanReceiveChecked = '';
        //全部可领取-全选-选中数量
        $allIsCanReceiveCheckedTotal = 0;
        $commodity_set_sku_model     = new db\DbCommoditySetSku();
        foreach ($list as $key => $val) {
            $val['commodity_class_name']     = $commodity_class_arr[$val['commodity_class']] ?? '';
            $val['comm_type_id_str']         = getCommTypeIdInfo($val['comm_type_id_str']);
            $val['commodity_checked_status'] = in_array($val['commodity_id'], array_keys($relate_arr)) ? 'checked' : '';
            $val['is_can_receive']           = !empty($relate_arr[$val['commodity_id']]) ? 1 : 0;

            ($val['commodity_checked_status'] == 'checked') ? $allCheckedTotal++ : '';
            ($val['is_can_receive'] == 1) ? $allIsCanReceiveCheckedTotal++ : '';

            // 判断当前商品是否关联卡券和set_sku是否全部关联
            $is_relevancy_set_sku = 0; // 0 未关联 1 set_sku全部关联  2 部分关联
            foreach ($association as $k => $item) {
                // 关联了
                if ($val['commodity_id'] == $item['commodity_id']) {

                    // 查询当前商品的所有set_sku
                    $map  = [
                        'commodity_id'     => $val['commodity_id'],
                        'commodity_set_id' => $val['commodity_set_id'],
                        'is_enable'        => 1
                    ];
                    $num1 = $commodity_set_sku_model->where($map)->count();
                    if (!empty($item['set_sku_list'])) {
                        // 查询已关联的set_sku数
                        $num2 = count($item['set_sku_list']);
                        if ($num2 < $num1) {
                            $is_relevancy_set_sku = 2; // 部分关联
                        } else {
                            $is_relevancy_set_sku = 1; // 全部关联
                        }
                    } else {
                        $is_relevancy_set_sku = 1; // 全部关联
                    }
                }
            }
            $val['is_relevancy_set_sku'] = $is_relevancy_set_sku;
            $list[$key]                  = $val;
        }
        ($listTotal == $allCheckedTotal) ? $allChecked = 'checked' : '';
        ($listTotal == $allIsCanReceiveCheckedTotal) ? $allIsCanReceiveChecked = 'checked' : '';

        $card_row = $card_model->getOneByPk($card_id);
        $page     = $list->render();
        $this->assign('allChecked', $allChecked);
        $this->assign('allIsCanReceiveChecked', $allIsCanReceiveChecked);
        $this->assign('commodity_attribute', DbCommodity::attribute());
        $this->assign('page', $page);
        $this->assign('live_type', $live_type);
        $this->assign('shelves_type', $shelves_type);
        $this->assign('list', $list);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('comm_type_id', $comm_type_id);
        $this->assign('commodity_name', $commodity_name);
        $this->assign('commodity_type', $commodity_type);
        $this->assign('card_id', $card_id);
        $this->assign('card_row', $card_row);
        $this->assign('relate_arr', $relate_arr);
        $this->assign('commodity_class', $commodity_class_arr);
        $this->assign('commodity_shellcheck_type', $commodity_shellcheck_type);//关联类型
        $this->assign('receive_type', $receive_type);//领取类型
        $this->assign('association', json_encode($association));//卡劵全部关联商品数据
        return $this->fetch('commodity_card_live');
    }

    //获取规格
    public function getIndexSku()
    {
        $commodity_id     = input('commodity_id', 30);
        $commodity_set_id = input('commodity_set_id', 0);
        // $list=$this->sku_model->getList(['where'=>['commodity_id'=>$commodity_id,'is_enable'=>1]]);
        $ser_com_model = new CommodityService();
        if (empty($commodity_set_id)) {
            $res = $ser_com_model->getDefaultSku($commodity_id);
        } else {
            $res = $ser_com_model->getSetSku($commodity_set_id);
        }
        $this->assign('sku_list', $res['sku_list']);
        $this->assign('sp_title', $res['sp_title']);
        $this->assign('sp_list', $res['sp_list']);
        return $this->fetch('get_index_sku');
        // print_r($res);


    }

    public function updateCommodityCard()
    {
        $card_id           = input('post.card_id');
        $commodity_id      = input('post.commodity_id');
        $commodity_set_id  = input('post.commodity_set_id');
        $is_can_receive    = input('post.is_can_receive');
        $action            = input('post.action');
        $commodity_card_id = input('post.commodity_card_id');
        $card_type         = input('post.card_type');

        if ($action == 'true') {

            $row        = $this->_comm_card_model->getOne(['where' => ['card_id' => $card_id, 'commodity_set_id' => $commodity_set_id, 'card_type' => $card_type]]);
            $card_model = new db\DbCard();
            $card_row   = $card_model->getOneByPk($card_id);
            //判断满减 和 专营店
            $card_list = $this->_comm_card_model->getCardByCommoditySetId($commodity_set_id);
            foreach ($card_list as $key => $val) {
                if ($val['card_type'] == $card_type) {
                    if (($val['card_class'] == 4 && $card_row['card_type'] != 4) || ($val['card_class'] != 4 && $card_row['card_type'] == 4)) {
                        print_json(1, '满减券不可与其他优惠券同时使用');
                    }

                    if ($val['set_type'] != $card_row['set_type']) {
                        print_json(1, '平台、专营店、集团优惠券无法同时选择,只能选择其中一种');

                    }

                }

            }

            if (!$row) {
                $last_id = $this->_comm_card_model->insertGetId([
                    'card_id'          => $card_id,
                    'commodity_id'     => $commodity_id,
                    'commodity_set_id' => $commodity_set_id,
                    'creator'          => $this->admin_info['username'],
                    'is_can_receive'   => $is_can_receive,
                    'card_type'        => $card_type
                ]);
                if ($last_id) {
                    print_json(0, '关联成功', $last_id);
                } else {
                    print_json(1, '更新失败');
                }

            }

        } else {

            $this->_comm_card_model->where('id', $commodity_card_id)->delete();
            print_json(0, '删除成功');

        }


    }


    public function updateCommodityCardLive()
    {
        $card_id          = input('post.card_id');
        $commodity_id     = input('post.commodity_id');
        $commodity_set_id = input('post.commodity_set_id');
        $action           = input('post.action');
        $card_type        = input('post.card_type');

        $sql      = '';
        $cardObj  = new DbCard();
        $cardInfo = $cardObj->getOneByPk($card_id);
        $max_sort = $this->_comm_card_model->where(['card_id' => $card_id])->min("sorts");
        if (empty($max_sort)) $max_sort = 99999;
        if ($action == 'true') {
            $row = $this->_comm_card_model->getOne([
                'where' => ['card_id' => $card_id, 'commodity_id' => $commodity_id, 'card_type' => $card_type]
            ]);
            if (!$row) {
                $last_id = $this->_comm_card_model->insertGetId([
                    'card_id'          => $card_id,
                    'commodity_id'     => $commodity_id,
                    'commodity_set_id' => $commodity_set_id,
                    'creator'          => $this->admin_info['username'],
                    'card_type'        => $card_type,
                    'sorts'            => $max_sort - 1
                ]);
//                $sql = $this->_comm_card_model->getLastSql();
                if ($last_id) {
                    $this->_comm_card_model->changeFlatCard($commodity_id, $commodity_set_id);
//                    $this->doHook([
//                        'commodity_id' => $commodity_id,
//                        'card_id' => $card_id,
//                        'card_type' => $card_type,
//                        'shelves_type'=>$cardInfo['shelves_type']
//                    ]);


                    print_json(0, '关联成功' . $sql, $last_id);

                } else {
                    print_json(1, '更新失败');
                }
            }
        } else {
            $res = $this->_comm_card_model->where('card_id', $card_id)
                ->where('commodity_id', $commodity_id)
                ->where('card_type', $card_type)
                ->delete();

//            $sql = $this->_comm_card_model->getLastSql();
            if ($res) {
                $this->_comm_card_model->changeFlatCard($commodity_id, $commodity_set_id);
//                $this->doHook([
//                    'commodity_id' => $commodity_id,
//                    'card_id' => $card_id,
//                    'card_type' => $card_type,
//                    'shelves_type'=>$cardInfo['shelves_type']
//                ]);
                print_json(0, '删除成功' . $sql);
            } else {
                print_json(1, '删除失败');
            }

        }
    }

    /**
     * 批量操作--关联当前页所有商品
     * @return void
     */
    public function batchUpdateCommodityCardLive()
    {
        $type      = input('type', 1);
        $card_list = [];
        if ($type == 2) {
            $card_id         = input('post.card_id');
            $dbCommodityCard = new DbCommodityCard();
            $max_sort        = $dbCommodityCard->where(['card_id' => $card_id])->max('sorts');
            $live_type       = input('live_type');
            $commodity_name  = input('commodity_name');
            $third_type      = input('three_comm_type_id');
            $second_type     = input('sub_comm_type_id');
            $comm_parent_id  = input('comm_parent_id');
            $commodity_class = input('commodity_class');
            $checked_status  = input('checked_status');
            $card_type       = input('card_type');
            $type_id         = 0;
            if (!empty($third_type)) {
                $type_id = $third_type;
            } else if (!empty($second_type)) {
                $type_id = $second_type;
            } else if (!empty($comm_parent_id)) {
                $type_id = $comm_parent_id;
            }

            if (!empty($commodity_class)) {
                $where['commodity_class'] = $commodity_class;
            }

            if (!empty($type_id)) {
                $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
            }

            if (!empty($commodity_name)) {
                $where['commodity_name'] = ['like', "%$commodity_name%"];
            }
            if (empty($live_type)) {
                $shelves_type = 5;
            } elseif ($live_type == 1) {
                $shelves_type = 6;
            } elseif ($live_type == 2) {
                $shelves_type = 7;
            }

            $where['a.shelves_type'] = $shelves_type;
            $where['b.shelves_type'] = $shelves_type;
            $field                   = "a.commodity_set_id,b.commodity_dlr_type_id,comm_type_id_str,commodity_class,a.up_down_channel_name,a.id,a.commodity_id,a.commodity_name,a.cover_image,a.price,b.count_stock,a.card_id";

            $params = array(
                'query'   => input('get.'),
                'where'   => $where,
                'order'   => "sort_order asc, updated_at desc", #商品排序规则和上架时间
                'field'   => $field,
                'no_page' => 1
            );

            $flat = new DbCommodityFlat();
            $list = $flat->getCommodity($params);
            foreach ($list as $key => $value) {
                $card_list[$key] = [
                    'card_id'          => $card_id,
                    'commodity_id'     => $value['commodity_id'],
                    'commodity_set_id' => $value['commodity_set_id'],
                    'action'           => $checked_status,
                    'card_type'        => $card_type,
                ];
            }
        } else {
            $card_list       = input('post.card_list/a');
            $dbCommodityCard = new DbCommodityCard();
            $max_sort        = $dbCommodityCard->max('sorts');
        }
        $commodity = [];
        foreach ($card_list as $key => $item) {
            $commodity['card_id']                = $item['card_id'];
            $commodity['action']                 = $item['action'];
            $commodity['card_type']              = $item['card_type'];
            $max_sort                            = $max_sort + 1;
            $commodity['commodity_id'][$key]     = $item['commodity_id'];
            $commodity['commodity_set_id'][$key] = $item['commodity_set_id'];
            $commodity['max_sort'][$key]         = $max_sort;
        }
        $dbCommodityCard->newUpdateCommodityCardLive($this->admin_info, $commodity);
        print_json(0, '批量操作成功', ['commodity_shellcheck_type' => $type]);
    }

    public function batchCommodityList()
    {
        $card_id         = input('post.card_id');
        $dbCommodityCard = new DbCommodityCard();
        $max_sort        = $dbCommodityCard->where(['card_id' => $card_id])->max('sorts');
        $live_type       = input('live_type');
        $commodity_name  = input('commodity_name');
        $third_type      = input('three_comm_type_id');
        $second_type     = input('sub_comm_type_id');
        $comm_parent_id  = input('comm_parent_id');
        $commodity_class = input('commodity_class');
        $checked_status  = input('checked_status');
        $card_type       = input('card_type');
        $type_id         = 0;
        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($comm_parent_id)) {
            $type_id = $comm_parent_id;
        }

        if (!empty($commodity_class)) {
            $where['a.commodity_class'] = $commodity_class;
        }

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},a.comm_type_id_str) ) "];
        }

        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }
        if (empty($live_type)) {
            $shelves_type = 5;
        } elseif ($live_type == 1) {
            $shelves_type = 6;
        } elseif ($live_type == 2) {
            $shelves_type = 7;
        }

        $where['a.shelves_type'] = $shelves_type;
        $where['b.shelves_type'] = $shelves_type;
        $field                   = "a.commodity_set_id,b.commodity_dlr_type_id,comm_type_id_str,a.commodity_class,
        a.up_down_channel_name,a.id,a.commodity_id,a.commodity_name,a.cover_image,a.price,b.count_stock,a.card_id,c.is_grouped";

        $params = array(
            'query'   => input('get.'),
            'where'   => $where,
            'order'   => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'   => $field,
            'no_page' => 1
        );

        $flat = new DbCommodityFlat();
        $list = $flat->getCommodity($params);
        print_json(0, '批量获取成功', $list);
    }

    public function updateCanReceive()
    {
        $commodity_card_id = input('post.commodity_card_id');
        $is_can_receive    = input('post.is_can_receive');
        $this->_comm_card_model->where('id', $commodity_card_id)->update(['is_can_receive' => $is_can_receive]);
        print_json(0, '修改成功');
    }

    public function updateCanReceiveLive()
    {
        $commodity_card_id = input('post.commodity_card_id');
        $is_can_receive    = input('post.is_can_receive');
        $commodity_id      = input('post.commodity_id');
        $commodity_set_id  = input('post.commodity_set_id');
        $r                 = $this->_comm_card_model->where('card_id', $commodity_card_id)
            ->where('commodity_id', $commodity_id)
            ->where('commodity_set_id', $commodity_set_id)
            ->update(['is_can_receive' => $is_can_receive]);
        if (empty($r)) {
            print_json(1, '修改失败');
        } else {
            print_json(0, '修改成功');
        }
    }

    /**
     * 批量操作--全部可领取
     * @return void
     */
    public function batchUpdateCanReceiveLive()
    {
        $type = input('type', 1);
        if ($type == 2) {
            $card_id        = input('card_id');
            $is_can_receive = input('is_can_receive');
            $this->_comm_card_model
                ->where('card_id', $card_id)
                ->update(['is_can_receive' => $is_can_receive]);
        } else {
            $list = input('post.list/a');
            foreach ($list as $item) {
                $this->_comm_card_model
                    ->where('card_id', $item['commodity_card_id'])
                    ->where('commodity_id', $item['commodity_id'])
                    ->where('commodity_set_id', $item['commodity_set_id'])
                    ->update(['is_can_receive' => $item['is_can_receive']]);
            }
        }
        print_json(0, '批量操作成功', ['receive_type' => $type]);
    }

    //获取下级分类
    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);
    }

    //获取规格
    public function getIndexSkuLive()
    {
        $commodity_id     = input('commodity_id', 30);
        $commodity_set_id = input('commodity_set_id', 0);
        // $list=$this->sku_model->getList(['where'=>['commodity_id'=>$commodity_id,'is_enable'=>1]]);
        $ser_com_model = new CommodityService();
        if (empty($commodity_set_id)) {
            $res = $ser_com_model->getDefaultSku($commodity_id);
        } else {
            $res = $ser_com_model->getSetSku($commodity_set_id);
        }
        $this->assign('sku_list', $res['sku_list']);
        $this->assign('sp_title', $res['sp_title']);
        $this->assign('sp_list', $res['sp_list']);
        return $this->fetch('get_index_sku_live');
    }

    /**
     * 更新 is_tmp 字段,并跳转到 成功页面
     */
    public function cardTmpUpdate()
    {
        $input   = input('post.');
        $card_id = $input['id'];
        $array   = $input['data'] ?? '';
        $array   = json_decode($array, true) ?? [];

        $cardObj  = new DbCard();
        $cardInfo = $cardObj->getOneByPk($card_id);
        if (empty($array) && !in_array($cardInfo['card_type'], [6, 7])) {
            print_json(1, '未选择关联商品');
        }

        $commodity       = [];
        $dbCommodityCard = new DbCommodityCard();
        foreach ($array as $key => $item) {
            if (!isset($item['sort'])) continue;
            $commodity['card_id']                = $card_id;
            $commodity['action'][$key]           = 'true';
            $commodity['card_type']              = $cardInfo['card_type'];
            $commodity['commodity_id'][$key]     = $item['commodity_id'];
            $commodity['commodity_set_id'][$key] = $item['commodity_set_id'];
            $commodity['is_can_receive'][$key]   = $item['is_can_receive'];
            $commodity['max_sort'][$key]         = $item['sort'];
            $commodity['set_sku'][$key]          = $item['set_sku_list'];
            $commodity['is_group'][$key]         = $item['is_grouped'] ?? 0;
            $commodity['group_card_type'][$key]  = $item['group_card_type'] ?? 1;
//            $commodity['sp_value_arr'][$key]     = $item['sp_value_arr'] ?? [];
        }
//        $re = $dbCommodityCard->newUpdateCommodityCardLive($this->admin_info, $commodity);
        try {
            if (!empty($commodity)) {
                Queue::push('app\common\queue\Card', json_encode(['data' => $commodity, 'creator' => $this->admin_info]), config('queue_type.default'));
            }
            $dbCommodityCard->newUpdateCommodityCardLive($this->admin_info,$commodity);
            if (empty($cardInfo['is_succeed'])) {
                $cardObj->save(['is_succeed' => 1], ['id' => $card_id]);
                QuickWin::create('quick_win')->postCouponInfo($cardInfo['quick_win_card_id']);
            }
            print_json(0, empty($array) ? '保存成功' : '批量添加成功');
        } catch (Exception $exception) {
            print_json(1, empty($array) ? '保存失败' : '批量添加失败');
        }
    }

    public function cardSort()
    {
        $card_id = input('post.id');
        try {
            $cardObj  = new DbCard();
            $cardInfo = $cardObj->getOneByPk($card_id);
            if (empty($cardInfo['is_succeed'])) {
                $cardObj->save(['is_succeed' => 1], ['id' => $card_id]);
                QuickWin::create('quick_win')->postCouponInfo($cardInfo['quick_win_card_id']);
            }
            print_json(0, '修改成功');
        } catch (Exception $exception) {
            print_json(1, '修改失败');
        }
    }

    public function successStep4()
    {
        $this->assign('live_type', input('live_type', 0));
        $this->assign('card_type', input('card_type', 0));
        return $this->fetch('success_step_4');
    }

    public function sortStep()
    {
        $card_id   = input('card_id');
        $live_type = input('live_type');
        $data      = input('data', '');
        $data      = json_decode($data, true) ?? [];
        foreach ($data as $k => $v) {
            if (!$v['action']) {
                unset($data[$k]);
            } else {
                unset($data[$k]['card_id']);
                unset($data[$k]['card_type']);
                unset($data[$k]['action']);
            }
        }
        $commodity_ids = array_keys($data);

        $shelves_type = 5; // 默认日产
        if ($live_type == 1) {
            $shelves_type = 6;
        } elseif ($live_type == 2) {
            $shelves_type = 7;
        }

        $commodity_flat      = new DbCommodityFlat();
        $flat_list           = $commodity_flat->where(['commodity_id' => ['in', $commodity_ids], 'shelves_type' => $shelves_type])->column('commodity_id,commodity_set_id,commodity_name,cover_image,comm_type_id,up_down_channel_name,is_grouped', 'commodity_id');
        $commodity_card_list = $this->_comm_card_model->where(['card_id' => $card_id, 'commodity_id' => ['in', $commodity_ids], 'is_enable' => 1])->column('id,commodity_id,sorts', 'commodity_id');

        foreach ($flat_list as $k => $v) {
            $flat_list[$k]['id']    = $commodity_card_list[$k]['id'] ?? '';
            $flat_list[$k]['sorts'] = $commodity_card_list[$k]['sorts'] ?? '';
        }
        $list = array_values($flat_list);
        // 查最低sku价格
        $comm_set_ids       = [];
        $comm_group_set_ids = [];
        foreach ($list as $v) {
            if ($v['is_grouped'] == 0) {
                $comm_set_ids[] = $v['commodity_set_id'];
            } else {
                $comm_group_set_ids[] = $v['commodity_set_id'];
            }
        }
        $comm_set_prices = [];
        if (!empty($comm_set_ids)) {
            $comm_set_ids_str = implode(',', $comm_set_ids);
            // 普通商品最低价
            $comm_set_prices = (new db\DbCommoditySetSku())
                ->alias('a')
                ->field('b.commodity_id, b.price')
                ->join("(select id,commodity_id, price from t_db_commodity_set_sku where commodity_set_id in ($comm_set_ids_str) order by price) b", "a.id=b.id")
                ->where(['a.commodity_set_id' => ['in', $comm_set_ids]])
                ->group('b.commodity_id')
                ->select();
        }


        $comm_group_set_prices = [];
        if (!empty($comm_group_set_ids)) {
            // 组合商品最低价
            $comm_group_set_list = (new db\DbCommoditySet())
                ->field('commodity_id, group_commodity_ids_info')
                ->where(['id' => ['in', $comm_group_set_ids]])
                ->select();
            foreach ($comm_group_set_list as $comm_group_set) {
                $all_min_price = 0;
                $comm_group    = json_decode($comm_group_set['group_commodity_ids_info'], true);
                foreach ($comm_group as $v) {
                    $tmp_price = 0;
                    $prices    = array_column($v['sku_list'], 'price');
                    $min_price = min($prices);
                    if (empty($all_min_price) || $min_price < $all_min_price) {
                        $all_min_price = $min_price;
                    }
                    if ($v['can_select'] == 0) { // 不可取消选择
                        if ($v['user_can_des'] == 0) { // 不可修改数量
                            $tmp_price += $min_price * $v['initial_num'];
                        } else {
                            $tmp_price += $min_price;
                        }
                    }
                }
                if (empty($tmp_price)) {
                    $tmp_price = empty($all_min_price) ? '/' : $all_min_price;
                }
                $comm_group_set_prices[] = [
                    'commodity_id' => $comm_group_set['commodity_id'],
                    'price'        => $tmp_price,
                ];
            }
        }

        $comm_prices = array_merge($comm_set_prices, $comm_group_set_prices);
        // 组合分类数据
        $comm_type_commodity_list = [];
        foreach ($list as $v) {
            foreach ($comm_prices as $comm_set_price) {
                if ($v['commodity_id'] == $comm_set_price['commodity_id']) {
                    $v['price'] = $comm_set_price['price'];
                }
            }
            $v['price']                                                       = $v['price'] ?? '/';
            $comm_type_commodity_list[$v['comm_type_id']][$v['commodity_id']] = $v;
        }
        foreach ($comm_type_commodity_list as &$v) {
            $v = array_values($v);
        }

        $comm_type_ids = array_column($list, 'comm_type_id');
        $this->assign('list', $list);
        $this->assign('live_type', $live_type);
        $this->assign('shelves_type', $shelves_type);
        $this->assign('card_id', $card_id);
        $this->assign('card_type', input('card_type', 0));
        $this->assign('comm_type_ids', json_encode($comm_type_ids));
        $this->assign('commodity_ids', json_encode($commodity_ids));
        $this->assign('comm_type_commodity_list', json_encode($comm_type_commodity_list));
        $this->assign('old_data', json_encode($data));
        return $this->fetch('sort_step');

    }

    public function updatesorts()
    {
        $id              = input('id');
        $new_sort        = input('new_sort');
        $dbCommodityCard = new DbCommodityCard();
        $dbCommodityCard->where(['id' => $id])->update(['sorts' => $new_sort]);
        print_json(0, '修改成功');
    }

    public function doHook($item = [])
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $item['modifier'] = 'card';
        Hook::listen('card', $item);
        return true;
    }

    public function getE3sActivity()
    {
        $input                      = input();
        $input['e3s_activity_date'] = $input['e3s_activity_date'] ?? '';
        $date                       = explode(' ~ ', $input['e3s_activity_date']);
        $data                       = [
            'E3S_CODE'       => 'ZZYY',
            'INTERFACE_CODE' => 'QueryRepairActivityListDetail',
            'BEGIN_DATE_S'   => $date[0] ?? '',
            'BEGIN_DATE_E'   => $date[1] ?? '',
            'END_DATE_S'     => $date[0] ?? '',
            'END_DATE_E'     => $date[1] ?? '',
            'ACTION_CODE'    => input('e3s_activity_code', ''),
        ];

        $list   = Third::create('third')->rpc('POST', 'proxy/e3s/', $data);
        $return = [
            'data' => $list['DATA'],
        ];
        print_json(0, '', $return);
    }

    public function getRuleList()
    {
        $input  = input();
        $brand = 1; // 日产
        if ($input['live_type'] == 2) {
            $brand = 2; // 启辰
        }
        $params = [
            'perPage' => $input['pagesize'] ?? 10,
            'page'    => $input['page'] ?? 1,
            'brand_id' => $brand,
        ];
        if (!empty($input['rule_id'])) {
            $params['id'] = $input['rule_id'];
        }
        if (!empty($input['rule_name'])) {
            $params['template_name'] = $input['rule_name'];
        }
        $list   = QuickWin::create('quick_win')->getCouponSettlementRuleList($params);
        $return = [];
        if (!empty($list['rows'])) {
            foreach ($list['rows'] as $key => $item) {
                $list['rows'][$key]['online_standard_type'] = $item['online_standard_type'] ?? 0;
                $list['rows'][$key]['online_standard_type_str'] = $item['online_standard_type_str'] ?? '';
            }
            $return = [
                'total' => $list['records'],
                'page'  => $list['pageindex'],
                'data'  => $list['rows'],
            ];
        }
        print_json(0, '', $return);
    }

    public function exportCardCommodity()
    {

//        if (!getRedisLock('export_card_commodity_' . $this->admin_info['username'], 300)) {
//            print_json(0, '限制5分钟一次');
//        }

        $post_data = input('post.');
        $card_ids  = $post_data['card_ids'];
        if ($post_data['export_type'] == 1) {
            // 查询关联的商品
            $commodity_card_model = new DbCommodityCard();
            $field                = 'card_id,commodity_id,commodity_set_id,is_can_receive,set_sku_ids,created_date';
            $commodities          = $commodity_card_model->whereIn('card_id', $card_ids)->field($field)->select();
            foreach ($commodities as $key => $item) {
                $set_sku_list = [];
                if (!empty($item['set_sku_ids'])) {
                    $set_sku_list = explode(',', $item['set_sku_ids']);
                }
                $commodities[$key]['set_sku_list'] = $set_sku_list;
            }
        } else {
            $commodities = $post_data['data'];
        }
        $params_key = md5(json_encode($commodities));
        $export     = DbExports::create([
            'export_type'   => 'card_commodity',
            'filter_params' => json_encode(['card_id' => $card_ids]),
            'export_key'    => $params_key,
            'creator'       => $this->admin_info['username']
        ]);

        Queue::push('app\admin_v2\queue\CardExport', json_encode([
            'params' => $commodities,
            'id'     => $export->id,
        ]), config('queue_type.export'));

        print_json(0, '操作成功，请到下载列表查看并下载文件');

    }

    public function cardCommodityTypeNum()
    {
        $input         = input('post.');
        $comm_type_ids = $input['comm_type_ids'] ?? [];
        $page          = input('page', 1);
        $page_size     = input('page_size', 5);
        $total         = count($comm_type_ids);
        $all_pages     = ceil($total / $page_size);

        $type_ids_one        = array_unique($comm_type_ids);
        $commodity_type_list = $this->comm_type_model
            ->alias('a')
            ->join('t_db_commodity_type b', 'a.comm_parent_id=b.id', 'left')
            ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id', 'left')
            ->where(['a.id' => ['in', $type_ids_one]])
            ->field('a.id,a.comm_type_name,b.comm_type_name as two_type_name,c.comm_type_name as one_type_name')
            ->select();

        $commodity_type_num = array_count_values($comm_type_ids);
        foreach ($commodity_type_list as &$v) {
            $v['num'] = $commodity_type_num[$v['id']];
        }

        $return_data = [
            'page'                => $page,
            'page_size'           => $page_size,
            'all_pages'           => $all_pages,
            'total'               => $total,
            'commodity_type_list' => $commodity_type_list,
        ];
        print_json(0, '', $return_data);
    }


    /**
     * 普通商品
     * @return mixed
     */
    public function getSkuTable()
    {
        $commodity_set_id = input('commodity_set_id');
        $card_id          = input('card_id');
        $commodity_id     = input('commodity_id');
        $card_type        = input('card_type');

        $field           = 'id,commodity_name,cover_image';
        $commodity_model = new DbCommodity();
        $commodity_info  = $commodity_model->where('id', $commodity_id)->field($field)->find();

        $this->assign('commodity_set_id', $commodity_set_id);
        $this->assign('card_id', $card_id);
        $this->assign('card_type', $card_type);
        $this->assign('commodity', $commodity_info);
        return $this->fetch('card/sku/select_sku');
    }


    public function ajaxGetSku()
    {
        $data          = input('post.');
        $search = [
            'sku_code' => trim($data['sku_code'] ?? '') ,
            'sp_id_arr' => json_decode($data['sp_id_arr'] ?? '', true),
        ];
        $ser_com_model = new CommodityService();
        if (isset($data['is_grouped']) && $data['is_grouped'] == 1) {
            // 组合商品
            $res = $ser_com_model->getNewSetSku($data['parent_commodity_id'], $data['parent_commodity_set_id'], $data['commodity_id'], $search);
        } else {
            $res = $ser_com_model->getNewSetSku($data['commodity_id'], $data['commodity_set_id'], 0, $search);
        }
        $data = [
            'list' => $res,
        ];
        print_json(0, 'success', $data);
    }


    public function sort_get_sku()
    {
        $input                              = input('post.');
        $commodity_model                    = new DbCommodity();
        $map                                = ['id' => $input['commodity_id']];
        $field                              = 'id,commodity_name,cover_image';
        $commodity_info                     = $commodity_model->where($map)->field($field)->find();
        $commodity_info['commodity_set_id'] = $input['commodity_set_id'];
        $commodity_info['commodity_id']     = $input['commodity_id'];

        $this->assign('card_id', $input['card_id']);
        $this->assign('commodity_info', $commodity_info);
        $this->assign('is_select', $input['is_select'] ?? false);
        $service = new CommodityService();
        if ($input['is_grouped'] == 1) {

            // 查询子商品
            $map                = ['commodity_id' => $input['commodity_id'], 'commodity_set_id' => $input['commodity_set_id'], 'is_enable' => 1];
            $set_sku_model      = new db\DbCommoditySetSku();
            $sub_commodity_ids  = $set_sku_model->where($map)->group('group_sub_commodity_id')->column('group_sub_commodity_id');
            $commodity_model    = new DbCommodity();
            $map                = [
                'a.id'           => ['in', $sub_commodity_ids],
                'b.shelves_type' => $input['shelves_type'],
                'a.is_enable'    => 1
            ];
            $field              = 'a.id,commodity_name,b.id as commodity_set_id';
            $sub_commodity_list = $commodity_model->alias('a')
                ->join('t_db_commodity_set b', 'a.id=b.commodity_id and b.is_enable=1', 'left')
                ->where($map)
                ->field($field)
                ->order('a.id')
                ->select();
            // 查询子商品的规格
            $sp_value_name_list = [];
            foreach ($sub_commodity_list as $key => $item) {
                $re = $service->getSubCommoditySpList($input['commodity_id'], $input['commodity_set_id'], $item['id']);
                $sp_value_name_list[$item['id']] = $re;
            }
            // 查询用券方式
            $commodity_card_model = new DbCommodityCard();
            $map                  = ['commodity_id' => $input['commodity_id'], 'commodity_set_id' => $input['commodity_set_id'], 'card_id' => $input['card_id'], 'is_enable' => 1];
            $group_card_type      = $commodity_card_model->where($map)->value('group_card_type');
            $this->assign('group_card_type', $group_card_type);
            $this->assign('sub_commodity_list', $sub_commodity_list);
            $this->assign('sp_value_name_list', json_encode($sp_value_name_list));

            return $this->fetch('card/sort_modal/group_select_sku');
        } else {
            // 普通商品
            $list        = $service->getNewSetSku($input['commodity_id'], $input['commodity_set_id']);
            $sp_value_name_list = $service->getSubCommoditySpList($input['commodity_id'], $input['commodity_set_id']);

            $all_set_sku = array_column($list, 'set_sku_id');
            $this->assign('list', $list);
            $this->assign('all_set_sku', json_encode($all_set_sku));
            $this->assign('sp_value_name_list', json_encode($sp_value_name_list));

            return $this->fetch('card/sort_modal/select_sku');
        }
    }

    // 查询关联的关联的spId
    public function getUnionSpId()
    {
        $input            = input('get.');
        $map              = [
            'group_commodity_set_id' => $input['parent_commodity_set_id'],
            'sub_commodity_id'       => $input['commodity_id'],
            'sp_value_id'            => $input['sp_value_id'],
        ];
        $spec_union_model = new db\DbCommoditySpecUnion();
        $field            = 'assoc_sp_value_id as sp_value_id,assoc_sub_commodity_id as sub_commodity_id, b.id as set_sku_id';
        $sp_value_id      = $spec_union_model->alias('a')
            ->join('t_db_commodity_set_sku b', 'a.assoc_sub_commodity_id = b.group_sub_commodity_id and a.group_commodity_set_id=b.commodity_set_id')
            ->where($map)->field($field)->group('b.group_sub_commodity_id')->find();
        if (!empty($sp_value_id)) {
            print_json(0, 'success', $sp_value_id);
        }

        $map         = [
            'group_commodity_set_id' => $input['parent_commodity_set_id'],
            'assoc_sub_commodity_id' => $input['commodity_id'],
            'assoc_sp_value_id'      => $input['sp_value_id'],
        ];
        $field       = 'sp_value_id, sub_commodity_id, b.id as set_sku_id';
        $sp_value_id = $spec_union_model->alias('a')
            ->join('t_db_commodity_set_sku b', 'a.sub_commodity_id = b.group_sub_commodity_id and a.group_commodity_set_id=b.commodity_set_id')
            ->where($map)->field($field)->group('b.group_sub_commodity_id')->find();
        if ($sp_value_id) {
            print_json(0, 'success', $sp_value_id);
        } else {
            print_json(1, '没有关联规格');
        }
    }

    public function getSetSkuId()
    {
        $input = input('get.');
        $map   = [
            'a.commodity_set_id' => $input['parent_commodity_set_id'],
            'a.is_enable'        => 1,
            'b.sp_value_list'    => $input['sp_value_id'],
        ];
        $model = new db\DbCommoditySetSku();
        $data  = $model->alias('a')
            ->join('t_db_commodity_sku b', 'a.commodity_sku_id = b.id')
            ->where($map)
            ->field('a.id,a.group_sub_commodity_id')->select();
        print_json(0, 'success', $data);
    }


    public function getAllSetSkuId()
    {
        $input          = input('get.');
        $model          = new DbCommodityCard();
        $set_sku_id_arr = $model->getSetSkuArr($input['set_sku_id'], $input['commodity_set_id']);
        print_json(0, 'success', $set_sku_id_arr);
    }


    /**
     * 查询当前组合商品上架id是否有关联规格
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getIsSpecUnion()
    {
        // 暂不使用关联规格
        print_json(1, '无关联规格');

        $group_commodity_set_id = input('group_commodity_set_id');
        $spec_union_model       = new db\DbCommoditySpecUnion();
        $map                    = ['group_commodity_set_id' => $group_commodity_set_id];
        $re                     = $spec_union_model->where($map)->find();
        if ($re) {
            print_json(0, '有关联规格');
        } else {
            print_json(1, '无关联规格');
        }
    }
}
