<?php


namespace app\admin_v2\controller;

use app\common\model\db\DbCommoditySku;
use app\common\model\e3s\E3sDelayInsurance as insuranceModel;
use app\common\model\sys\SysMenu;
use think\Env;
use think\Model;

class E3sDelayInsurance extends Common
{

    public function index()
    {
        $m_model = new SysMenu();
        $list    = $m_model->alias('a')
            ->join('t_sys_menu b', 'a.menu_pid = b.id')
            ->where(['a.controller' => request()->controller(), 'a.left_view' => 1, 'b.left_view' => 1])
            ->field('a.web_menu_url,b.menu_url')
            ->find();
        if (!empty($list['web_menu_url'])) {
            $username['username']  = $this->admin_info['username'];
            $username['timestamp'] = time();
            $token                 = http_build_query($username);
            $url = Env::get('TOB_URL') . '/' . $list['web_menu_url'] . '?sign=' . base64_encode($token);
            $this->redirect($url);
        }
    }


    /**
     * 列表
     * @throws \think\exception\DbException
     */
    public function getList()
    {
        $input = input('get.');
        $map   = [];
        if (!empty($input['n_product_code'])) {
            $map['n_product_code'] = $input['n_product_code'];
        }
        if (!empty($input['n_product_name'])) {
            $map['n_product_name'] = ['like', '%' . $input['n_product_name'] . '%'];
        }
        if (!empty($input['car_brand_code'])) {
            $map['car_brand_code'] = $input['car_brand_code'];
        }
        if (isset($input['is_enable']) && $input['is_enable'] != '') {
            $map['is_enable'] = $input['is_enable'];
        }
        $pageSize        = $input['per_page'] ?? 20;
        $insurance_model = new insuranceModel();
        $list            = $insurance_model->where($map)->order('id desc')->paginate($pageSize);
        $spare_part      = new \app\common\model\e3s\E3sSparePart();
        foreach ($list as $value) {
            $value->is_goods = $spare_part->is_goods($value['n_product_code']);
        }
        print_json(0, 'success', $list);
    }


    /**
     * 获取品牌列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getBrandList()
    {
        $insurance_model = new insuranceModel();
        $list            = $insurance_model->field('car_brand_code,car_brand_cn')->group('car_brand_code')->select();
        print_json(0, 'success', $list);
    }


    /**
     * 关联商品
     * @throws \think\exception\DbException
     */
    public function correlationGoods()
    {
        $input      = input('get.');
        if (empty($input['n_product_code'])) {
            print_json(1,'延保产品编码不能为空');
        }
        $pagesize = $input['pagesize'] ?? 10;
        $where               = [
            'a.sku_code'  => $input['n_product_code'],
            'a.is_enable' => 1,
            'b.is_enable' => 1
        ];
        $commodity_sku_model = new DbCommoditySku();

        $list = $commodity_sku_model->alias('a')
            ->join('t_db_commodity b', 'a.commodity_id = b.id', 'left')
            ->where($where)
            ->field('b.commodity_name,a.id sku_id,a.sku_code,a.price,a.commodity_id')
            ->group('b.id')
            ->paginate($pagesize);
        print_json(0,'success', $list);
    }


}