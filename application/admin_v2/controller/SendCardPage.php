<?php
/**
 * Created by PhpStorm.
 * User: wang-zhipeng
 * Date: 2017/9/12
 * Time: 9:10
 * Use:发券页管理
 */

namespace app\admin_v2\controller;

use app\common\model\bu\BuQyUser;
use app\common\model\db\DbDlr;

use app\common\model\act\AcRenewalQrc;
use app\common\model;
use app\common\model\db\DbSendCardPage;
use app\common\service\BaseDataService;
use think\Request;
use tool\PhpExcelPage;
use tool\UploadFile;
use tool\PhpExcel;

class SendCardPage extends Common
{
    private $dbDlr;
    private $dbCard;
    private $dbSystem;
    private $dbSend;
    private $dbSendSub;
    private $content = [];

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->dbDlr     = new model\db\DbDlr();
        $this->dbCard    = new model\db\DbCard();
        $this->dbSystem  = new model\db\DbSystemValue();
        $this->dbSend    = new model\db\DbSendCardPage();
        $this->dbSendSub = new model\db\DbSendCardPageSub();
        $this->adminInfo = session('admin_info');
    }

    #发券页管理
    public function index()
    {

        $title     = input('get.title');
        $send_type = input('get.send_type', 0);
        $is_enable = input('get.is_enable', 1);
        $dlr_code  = $this->admin_info['dlr_code'];
        $type      = input('get.type');

        if (!empty($title)) {
            $where['title'] = ['like', "%{$title}%"];
        }
        if (!empty($send_type)) {
            $where['send_type'] = $send_type;
        }
        if ($is_enable == 1) {
            $where['is_enable'] = $is_enable;
            /* $where['start_time']=['<',time()];*/
            $where['end_time'] = ['>', time()];
        } else if ($is_enable == 2) {
            $where[] = ['exp', "  (is_enable=0 or end_time<" . time() . ")"];
        }

        if ($this->admin_info['type'] == 1 || $this->admin_info['type'] == 3) {

            $where['creator'] = $dlr_code;

        } else if ($this->admin_info['type'] == 2) {
            if ($type == 'platform') {  //专营店查看平台端
                $where[] = ['exp', "FIND_IN_SET('{$dlr_code}', dlr_code) AND creator='NISSAN' "];
            } else if ($type == 'bloc') { //专营店查看集团,本专营店可见的集团发券页
                $dlr     = $this->dbDlr->getOne(['where' => ['dlr_code' => $dlr_code, 'is_enable' => 1]]);
                $pid_dlr = $this->dbDlr->getOneByPk($dlr['pid']);
//            $where['set_type']=3;
                $where[] = ['exp', "FIND_IN_SET('{$dlr_code}', dlr_code) AND creator='{$pid_dlr['dlr_code']}'"];
            } else {                  //专营店自建发券页
                $where['creator'] = $dlr_code;
            }
        }
//dd($where);
        $template_list = $this->dbSystem->getListByValue(14);
        // print_json($template_list);
        $send_type_list  = DbSendCardPage::$send_type;
        $is_enable_list  = $this->_isEnable();
        $params['where'] = $where;
        $params['order'] = 'created_date desc';
        $params['query'] = input('get.');

        $list = $this->dbSend->getListPaginate($params);
//        print_json(1,'',$list);
        foreach ($list as $key => $val) {
            if ($val['end_time'] < time()) {
                $list[$key]['is_enable'] = 0;
            }
            if ($val['is_enable'] == 1 && $val['send_type'] == 1) {
                $val['fixation_url'] = $this->getSenCard($val['id'], $this->admin_info['dlr_code']);

            } else if ($val['is_enable'] == 1 && $val['send_type'] == 2) {
                $val['send_card_url'] = config('mhome.base_url') . '/after_sale/marketing/marketing_card_url_one?id=' . $val['id'] . '&send_type=' . $val['send_type'];
            }
            $list[$key]['send_type_name'] = $send_type_list[$val['send_type']];
        }
        //print_json($list);
        $page = $list->render();
        $this->assign('page', $page);
        $this->assign('list', $list);
        $this->assign('send_type_list', $send_type_list);
        $this->assign('is_enable_list', $is_enable_list);
        $this->assign('send_type', $send_type);
        $this->assign('is_enable', $is_enable);
        $this->assign('template_list', $template_list);
        $this->assign('type', $type);
        $this->assign('dlr_code', $dlr_code);

        if ($this->admin_info['type'] == 1 || $this->admin_info['type'] == 3) {
            //平台端、集团
            return $this->fetch('send_card_page/index');
        } else {
            //专营店
            return $this->fetch('send_card_page/dlr_index');
        }

    }


    /**
     * 状态
     * @return array
     */
    private function _isEnable()
    {
        return $data = ['3' => '全部', '1' => '可用', '2' => '不可用'];
    }


    public function post(Request $request)
    {
        $temp               = input('get.theme', 1);
        $id                 = input('get.id');
        $adminInfo          = session('admin_info');
        $where['is_enable'] = 1;

        //集团：查找集团下的专营店
        if ($this->admin_info['type'] == 3) {

            $this_dlr          = $this->dbDlr->getOne(['where' => ['dlr_code' => $this->admin_info['dlr_code'], 'is_enable' => 1]]);
            $dlr_sub           = $this->dbDlr->getColumn(['where' => ['pid' => $this_dlr['id']], 'column' => 'dlr_code']);
            $where['dlr_code'] = ['in', implode(',', $dlr_sub)];
        }

        $baseService = new BaseDataService();
        $dlr_data    = $baseService->getDlrList(0, isset($dlr_sub) ? $dlr_sub : '');
        $this->assign('dlr_data', $dlr_data);
        $this->assign('adminInfo', $adminInfo);

        if (!empty($id)) {
            //更新
            $row = $this->dbSend->getOneByPk($id);

            if ($this->admin_info['dlr_code'] != $row['creator']) print_json(1, '数据错误');
            $dlr_name = $this->dbDlr->getColumn(['where' => ['dlr_code' => ['in', explode(',', $row['dlr_code'])]], 'column' => 'dlr_name']);
            $dlr_name = implode(',', $dlr_name);
            if (!empty($row['start_time'])) {
                $row['s_e_time'] = date('Y-m-d', $row['start_time']) . ' ~ ' . date('Y-m-d', $row['end_time']);
            } else {
                $row['s_e_time'] = null;
            }
            $sub_row = $this->dbSendSub->getList(['where' => ['send_card_page_id' => $id]]);
            foreach ($sub_row as $key => $val) {
                if ($val['module'] == 3) {
                    $card_check = json_decode($val['card_list'], true);
                    //获取优惠券
                    if (!empty($card_check)) {

                        $card_list = $this->dbCard->getList(['where' => ['id' => ['in', explode(',', $row['card_list'])], 'is_enable' => 1], 'field' => 'id,dlr_code,card_name,type,card_type,card_quota,card_id']);
                        $card_list = collection($card_list)->toArray();
                        $card_data = [];
                        $card_ids  = explode(',', $row['card_list']);
                        foreach ($card_list as $c_key => $c_val) {
                            if ($row['send_type'] == 3) {
                                if (isset($card_check[$c_val['id']])) {
                                    $c_val['can_num'] = $card_check[$c_val['id']];
                                }
                            }
                            $card_data[array_search($c_val['id'], $card_ids)] = $c_val;
                        }
                        ksort($card_data);
                        $sub_row[$key]['card_sub'] = $card_data;
                    } else {
                        $sub_row[$key]['card_sub'] = [];
                    }

                }
            }
            $theme_row      = $this->dbSystem->getOne(['where' => ['value_type' => 12, 'value_code' => $row['theme']]]);
            $row['theme_v'] = $theme_row['remark'];
//             print_json($sub_row);
            $this->assign('row', $row);
            $this->assign('sub_row', $sub_row);
            $this->assign('dlr_name', $dlr_name);
            $this->assign('card_type', $this->dbCard->cardType());
            $this->assign('card_class', $this->dbCard->type());
            // $this->add($theme);
            $this->assign('themeList', $this->_getTemp($theme_row['parent_value_code']));
            return $this->fetch('send_card_page/update');
        } else {
            /*  $info = $this->dbSend->getOneByPk($id);
              $content = $info['content'];
              $this->assign('content',$content);
              $this->assign('info',$info);
              $this->set($id);*/
            $themeList = $this->_getTemp($temp);
            $theme     = !empty($themeList[0]) ? $themeList[0]['id'] : 0;
            $this->assign('theme', $theme);
            $this->assign('theme_style', $themeList[0]['value']);
            $this->assign('themeList', $this->_getTemp($temp));
            return $this->fetch('send_card_page/add');
        }

    }

    /**
     * 获取模板所有主题
     * @param $temp
     * @return array
     */
    private function _getTemp($temp)
    {
        $theme_where = ['is_enable' => 1, 'parent_value_code' => $temp, 'value_type' => 12];
        $theme_field = 'value_code as id,county_name as name,remark as value';
        $themeList   = $this->dbSystem->getList(['where' => $theme_where, 'field' => $theme_field, 'order' => 'order_no']);
        return $themeList = collection($themeList)->toArray();


    }

    private function _theme($themeList, $value)
    {

        foreach ($themeList as $key => $val) {
            if ($val['id'] == $value) {
                return $val['value'];
            }
        }
        return '';

    }


    //保存数据
    public function save()
    {
        $id = input('post.id');

        $data = $_POST;
//        print_json(1,'',$data);
        if (empty($data['created_date'])) print_json(1, '有效期不能为空');
        $time               = explode(' ~ ', $data['created_date']);
        $data['start_time'] = '';
        $data['end_time']   = '';
        if (count($time) == 2) {
            $data['start_time'] = strtotime($time[0]);
            $data['end_time']   = strtotime($time[1]) + 86399;
        }
        $insertData = [
            'title'      => trim($data['title']),
            'send_type'  => intval($data['send_type']),
            'desc'       => trim($data['desc']),
            'start_time' => $data['start_time'],
            'end_time'   => $data['end_time'],
            'theme'      => $data['theme'],
            'dlr_code'   => $data['drl'],
            'is_enable'  => empty($data['is_enable']) ? 0 : $data['is_enable'],
            'is_send'    => empty($data['is_send']) ? 0 : $data['is_send'],
            'creator'    => $this->admin_info['dlr_code'],
        ];

        if ($this->admin_info['type'] == 2) {
            $insertData['dlr_code'] = $this->admin_info['dlr_code'];
        }
        $insertData['set_type'] = $this->admin_info['type'];

        $file      = request()->file('thumb');
        $file_path = 'send_card';
        $validate  = ['size' => config('upload.image_size'), 'ext' => config('upload.image_ext')];

        if ($file) {
            $thumb = UploadFile::image(\request()->file('thumb'), $file_path);  //,['size'=>102400,'ext'=>['jpg','jpeg','png']]
            if ($thumb['error'] == 1) {
                print_json(1, '上传图' . $thumb['msg']);
            }
            if ($insertData['send_type'] == 3) {
                $insertData['poster_title']     = $data['poster_title'];
                $insertData['give_copywriting'] = $data['give_copywriting'];
                $insertData['poster_img']       = $thumb['data'];
            } else {
                $insertData['thumb'] = $thumb['data'];
            }

        } elseif ($insertData['send_type'] == 3 && empty($data['poster_img'])) {
            print_json(1, '请上传海报图片');
        }
        if (empty($id)) {
            $res = $this->dbSend->save($insertData);
            if (!$res) {
                print_json(1, $this->dbSend->getError());
            } else {
                $id       = $this->dbSend->id;
                $sub_data = $this->_subData(json_decode($data['sub_data'], true), $id, $insertData['send_type']);
                //  print_json($sub_data);
                $this->dbSendSub->insertAll($sub_data['all_data']);
                $this->dbSend->where(['id' => $id])->update(['card_list' => implode(',', $sub_data['card_list']), 'card_num' => count($sub_data['card_list'])]);
                print_json(0, '添加成功');
            }

        } else {
            unset($insertData['creator']);
            $sub_data                        = $this->_subData(json_decode($data['sub_data'], true), $id, $insertData['send_type']);
            $insertData['modifier']          = $this->admin_info['username'];
            $insertData['last_updated_date'] = date('Y-m-d H:i:s');
            $insertData['card_list']         = implode(',', $sub_data['card_list']);
            $insertData['card_num']          = count($sub_data['card_list']);
            $res                             = $this->dbSend->where(['id' => $id])->update($insertData);
            if ($res) {
                $this->dbSendSub->where(['send_card_page_id' => $id])->delete();
                $this->dbSendSub->insertAll($sub_data['all_data']);
                print_json(0, '修改成功');
            } else {
                print_json(1, '修改失败');
            }


        }
        print_json(json_decode($_POST['sub_data'], true));


    }


    private function _subData($sub_data, $id, $send_type)
    {
        $all_data  = [];
        $card_list = [];
        //  print_json($sub_data);
        foreach ($sub_data as $key => $val) {
            $data = [];
            if (empty($val['module'])) continue;

            $data = [
                'sub_wenan1' => empty($val['sub_wenan1']) ? '' : $val['sub_wenan1'],
                'sub_wenan2' => empty($val['sub_wenan2']) ? '' : $val['sub_wenan2'],
                'sub_wenan3' => empty($val['sub_wenan3']) ? '' : $val['sub_wenan3'],
                'pic'        => empty($val['pic']) ? '' : $val['pic'],
                'tw_type'    => empty($val['tw_type']) ? 0 : $val['tw_type'],
                'main_title' => empty($val['main_title']) ? '' : $val['main_title'],
                'sub_title'  => empty($val['sub_title']) ? '' : $val['sub_title'],
                'rule'       => empty($val['rule']) ? '' : $val['rule'],
            ];
            if ($val['module'] == 3) {
                if ($send_type == 3) {
                    $thumb = [];
                    foreach ($val['card_list'] as $k => $v) {
                        $thumb[$v] = $val['can_num_list'][$k];
                    }
                    $card_ids = json_encode($thumb);
                } else {
                    $card_ids = implode(',', $val['card_list']);
                    //   $data['can_get'] = $val['can_get'];
                }
                $card_list         = array_merge($val['card_list'], $card_list);
                $data['card_list'] = $card_ids;


            } else {
                $data['card_list'] = '';

            }
            $data['sort']              = $key + 1;
            $data['module']            = $val['module'];
            $data['send_card_page_id'] = $id;
            $data['creator']           = $this->admin_info['username'];

            $all_data[] = $data;

        }
//dd($all_data);
        return array('all_data' => $all_data, 'card_list' => $card_list);
    }


    private function _isEmpty($val)
    {
        return empty($val) ? '' : $val;

    }


    //新建发券页
    public function add($theme)
    {
        if ($_POST) {
            $data = input('post.');
            if (empty($data['rule'])) $data['rule'] = '';
//            print_json(1,'',$data);
            $time               = explode(' ~ ', $data['created_date']);
            $data['start_time'] = '';
            $data['end_time']   = '';
            if (count($time) == 2) {
                $data['start_time'] = strtotime($time[0]);
                $data['end_time']   = strtotime($time[1]);
            }
            $insertData = [
                'title'      => trim($data['title']),
                'send_type'  => intval($data['send_type']),
                'desc'       => trim($data['desc']),
                'start_time' => $data['start_time'],
                'end_time'   => $data['end_time'],
                'theme'      => intval($data['theme']),
                'sub_wenan1' => trim($data['sub_wenan1']),
                'sub_wenan2' => trim($data['sub_wenan2']),
                'sub_wenan3' => trim($data['sub_wenan3']),
                'main_title' => trim($data['main_title']),
                'sub_title'  => trim($data['sub_title']),
                'can_get'    => intval($data['can_get']),
                'card_num'   => intval($data['card_num']),
                'dlr_code'   => substr($data['drl'], 3),
                'card_list'  => trim($data['card_list']),
                'rule'       => trim($data['rule']),
                'is_enable'  => intval($data['is_use']),
                'is_send'    => intval($data['is_send']),
                'content'    => $data['content'],
                'creator'    => $this->admin_info['dlr_code']
            ];
            if (\request()->file('thumb')) {
                $thumb = UploadFile::image(\request()->file('thumb'), 'customer_service');  //,['size'=>102400,'ext'=>['jpg','jpeg','png']]
                if ($thumb['error'] == 1) {
                    print_json(1, '简略图' . $thumb['msg']);
                }
                $insertData['thumb'] = $thumb['data'];
            }
            if (\request()->file('pic')) {
                $pic = UploadFile::image(\request()->file('pic'), 'customer_service', ['size' => 102400, 'ext' => ['jpg', 'jpeg', 'png']]);      //102400 上传图片大小限制100K
                if ($pic['error'] == 1) {
                    print_json(1, '广告图片' . $pic['msg']);
//                    echo"<script>history.go(-1);alert('".$pic['msg']."');</script>";
                }
                $insertData['pic'] = $pic['data'];
            }
            if ($insertData['dlr_code'] == '') {
                $insertData['dlr_code'] = '_';
            }
            $card_list = explode(',', $insertData['card_list']);
            if (count($card_list) < $insertData['card_num']) {
                $this->error('创建失败,限领数量大于优惠券数量');
                exit;
            }

            print_json(1, '', $insertData);
            $id = $this->dbSend->insertGetId($insertData);
            if ($id) {
                print_json(0, '创建成功');
//                $this->success('创建成功');exit;
            } else {
                print_json(1, '创建失败');
//                $this->error('创建失败');exit;
            }


        }


    }

    //编辑发券页
    public function set($id)
    {
        if ($_POST) {
            $data               = input('post.');
            $time               = explode('~', $data['created_date']);
            $data['start_time'] = strtotime($time[0]);
            $data['end_time']   = strtotime($time[1]);
            $insertData         = [
                'title'      => trim($data['title']),
                'send_type'  => intval($data['send_type']),
                'desc'       => trim($data['desc']),
                'start_time' => $data['start_time'],
                'end_time'   => $data['end_time'],
                'theme'      => intval($data['theme']),
                'sub_wenan1' => trim($data['sub_wenan1']),
                'sub_wenan2' => trim($data['sub_wenan2']),
                'sub_wenan3' => trim($data['sub_wenan3']),
                'main_title' => trim($data['main_title']),
                'sub_title'  => trim($data['sub_title']),
                'can_get'    => intval($data['can_get']),
                'card_num'   => intval($data['card_num']),
                'dlr_code'   => substr($data['drl'], 3),
                'card_list'  => trim($data['card_list']),
                'rule'       => trim($data['rule']),
                'is_enable'  => intval($data['is_use']),
                'is_send'    => intval($data['is_send']),
                'content'    => $data['content'],
                'modifier'   => $this->admin_info['dlr_code']
            ];
            if (\request()->file('thumb')) {
                $thumb = UploadFile::image(\request()->file('thumb'), 'customer_service');       //,['size'=>102400,'ext'=>['jpg','jpeg','png']]
                if ($thumb['error'] == 1) {
                    print_json(1, '简略图' . $thumb['msg']);
                }
                $insertData['thumb'] = $thumb['data'];
            }
            if (\request()->file('pic')) {
                $pic = UploadFile::image(\request()->file('pic'), 'customer_service', ['size' => 102400, 'ext' => ['jpg', 'jpeg', 'png']]);      //102400 上传图片大小限制100K
                if ($pic['error'] == 1) {
                    print_json(1, '广告图片' . $pic['msg']);
//                    echo"<script>history.go(-1);alert('".$pic['msg']."');</script>";
                }
                $insertData['pic'] = $pic['data'];
            } else {
                $insertData['pic'] = '';
            }
            if ($insertData['dlr_code'] == '') {
                $insertData['dlr_code'] = '_';
            }
            // print_r($insertData);exit;
            $card_list = explode(',', $insertData['card_list']);
            if (count($card_list) < $insertData['card_num']) {
                $this->error('修改失败,限领数量大于优惠券数量');
                exit;
            }
            $id = $this->dbSend->saveData($insertData, array('id' => $id));
            if ($id) {
                print_json(0, '修改成功');
//                $this->success('修改成功');exit;
            } else {
                print_json(1, '修改失败');
//                $this->error('修改失败');exit;
            }


        }
    }

    /**
     * 发券页统计
     */
    public function statis()
    {
        $dlr_code  = input('get.dlr_code', "");
        $send_type = input('get.send_type', 0);
        $title     = input('get.title');
        $user_type = $this->admin_info['type'];
        $is_down   = input('get.is_down');
        $where     = ['a.is_enable' => 1];
        if ($user_type == 1) { //平台端

            if (!empty($dlr_code)) {
                $where['a.dlr_code'] = $dlr_code;
            }
            $dlr_model = new DbDlr();
            $dlr_list  = $dlr_model->getList(['where' => 'is_enable=1', 'field' => 'dlr_code,dlr_name']);
            $this->assign('dlr_list', $dlr_list);
        } else {   //专营店端
            $where['a.dlr_code'] = $this->admin_info['dlr_code'];
        }

        if (!empty($send_type)) {
            $where['b.send_type'] = $send_type;
        }
        if (!empty($title)) {
            $where['title'] = ['like', "%{$title}%"];
        }
        $query        = input('get.');
        $poster_model = new model\bu\BuQyPoster();
        $params       = [
            'where'    => $where,
            'field'    => 'GROUP_CONCAT( distinct a.id ) as ids,send_card_page_id,a.dlr_code,b.title,b.send_type,c.dlr_name',
            'query'    => $query,
            'group'    => 'send_card_page_id,a.dlr_code',
            'order'    => 'a.id desc',
            'is_down'  => $is_down,
            'pagesize' => null
        ];
        $list         = $poster_model->getNewStatic($params);

        if ($is_down == 1) {
            $pageExcel = new PhpExcelPage();
            $pageExcel->setSheetTitle(date('Y-m-d') . '发券页统计');
            $pageExcel->setSheetTitle('发券页统计报表');
            $title = [
                'A' => '发券页名称', 'B' => '应用终端', 'C' => '发券模式', 'D' => '领券量', 'E' => '核销量',
            ];
            $pageExcel->setTitle($title);
            $widthArr = [
                'A' => '50', 'B' => '60', 'C' => '30', 'D' => '30', 'E' => '30',
            ];
            $pageExcel->setWidth($widthArr);
            $data = [];
            foreach ($list as $key => $item) {
                $data[] = [
                    'A' => $item['title'],
                    'B' => $item['dlr_name'],
                    'C' => $item['send_type_name'],
                    'D' => $item['get_count'],
                    'E' => $item['consume_count'],
                ];
            }
            $pageExcel->setData($data, 0, 0);
            $pageExcel->downloadFile('发券页统计');
            exit;

        }
        $page = $list->render();
        $list = $list->toArray();
        $this->assign('page', $page);
        $this->assign('list', $list);
        $this->assign('user_type', $user_type);
        $this->assign('send_type', $send_type);
        $this->assign('send_type_arr', DbSendCardPage::$send_type);      //发券模式
        return $this->fetch('send_card_page/statis');
    }


    /**
     * 发券明细
     */
    public function detail()
    {
        $dlr_code  = input('get.dlr_code', "");
        $send_type = input('get.send_type', 0);
        $user_type = $this->admin_info['type'];
        $is_down   = input('get.is_down');
        $user_id   = input('get.user_id');
        $card_name = input('get.card_name');

        $where = ['a.is_enable' => 1, 'a.poster_id' => ['neq', 0]];
        if ($user_type == 1) { //平台端

            if (!empty($dlr_code)) {
                $where['b.dlr_code'] = $dlr_code;
            }
            $dlr_model = new DbDlr();
            $dlr_list  = $dlr_model->getList(['where' => 'is_enable=1', 'field' => 'dlr_code,dlr_name']);
            $this->assign('dlr_list', $dlr_list);
        } else {   //专营店端
            $where['b .dlr_code'] = $this->admin_info['dlr_code'];
        }

        if (!empty($send_type)) {
            $where['c.send_type'] = $send_type;
        }

        if (!empty($user_id)) {
            $where['b.user_id'] = $user_id;
        }
        if (!empty($card_name)) {
            $where['e.card_name'] = ['like', "%{$card_name}%"];
        }

        $query = input('get.');

        $field        = 'GROUP_CONCAT( distinct a.id ) as ids,b.user_id,b.creator,a.card_id,e.card_name,c.title,d.dlr_name,c.send_type';
        $params       = [
            'where'    => $where,
            'field'    => $field,
            'query'    => $query,
            'group'    => 'a.poster_id,a.card_id',
            'order'    => 'a.id desc',
            'is_down'  => $is_down,
            'pagesize' => null
        ];
        $record_model = new model\bu\BuCardReceiveRecord();
        $list         = $record_model->getPosterDetail($params);


        if ($is_down == 1) {
            $pageExcel = new PhpExcelPage();
            $pageExcel->setSheetTitle(date('Y-m-d') . '发券页明细');
            $pageExcel->setSheetTitle('发券页明细报表');
            $title = [
                'A' => '员工编号', 'B' => '员工姓名', 'C' => '卡券名称', 'D' => '卡券编码', 'E' => '发券页名称', 'F' => '应用终端', 'G' => '发券模式', 'H' => '领券量', 'I' => '核销量'
            ];
            $pageExcel->setTitle($title);
            $widthArr = [
                'A' => '50', 'B' => '60', 'C' => '30', 'D' => '30', 'E' => '30', 'F' => '50', 'G' => '50', 'H' => '30', 'I' => '30'
            ];
            $pageExcel->setWidth($widthArr);
            $data = [];
            foreach ($list as $key => $item) {
                $data[] = [
                    'A' => $item['user_id'],
                    'B' => $item['creator'],
                    'C' => $item['card_name'],
                    'D' => $item['card_id'],
                    'E' => $item['title'],
                    'F' => $item['dlr_name'],
                    'G' => $item['send_type_name'],
                    'H' => $item['get_count'],
                    'I' => $item['consume_count'],
                ];
            }
            $pageExcel->setData($data, 0, 0);
            $pageExcel->downloadFile('发券页明细');
            exit;

        }
        $page = $list->render();
        $list = $list->toArray();
        $this->assign('page', $page);
        $this->assign('list', $list);
        $this->assign('user_type', $user_type);
        $this->assign('send_type', $send_type);
        $this->assign('send_type_arr', DbSendCardPage::$send_type);      //发券模式
        return $this->fetch('send_card_page/detail');


    }

    //根据id获取发券页
    public function getInfo()
    {
        $id                 = input('get.id');
        $info               = $this->dbSend->getOneByPk($id);
        $info['start_time'] = date('Y-m-d', $info['start_time']);
        $info['end_time']   = date('Y-m-d', $info['end_time']);
        $info['dlr_name']   = '';
        if ($info['dlr_code']) {
            $dlr_code = explode(',', $info['dlr_code']);
            foreach ($dlr_code as $v) {
                $dlr              = $this->dbDlr->getOneRecord(['dlr_code' => $v]);
                $info['dlr_name'] .= $dlr['dlr_name'] . ',';
            }
        }
        echo json_encode(array($info));
        exit;
    }

    //获取优惠券
    public function getCardList()
    {
        $params['where']['is_enable'] = 1;
        $params['where']['dlr_code']  = $this->admin_info['dlr_code'];
        $params['order']              = 'created_date desc';
        $params['query']              = input('');
        $list                         = $this->dbCard->getListPaginate($params);
        foreach ($list as $k => $v) {
            $list[$k]['type']      = $this->dbCard->type()[$v['type']];
            $list[$k]['card_type'] = $this->dbCard->cardType()[$v['card_type']];
        }
        return $list;
    }

    //优惠券页面
    public function card()
    {
        $date                              = date('Y-m-d');
        $params1['where']['a.is_enable']   = 1;
        $params1['where']['a.activity_id'] = ['neq', 0];
        $params1['where']['a.type']        = 2;
        $params1['where']['a.brand_id']    = 1; // 只能查日产

        $params1['where'][] = ['exp', "((validity_date_end>='{$date}') OR validity_date_start is null)"];
        $params1['where'][] = ['exp', 'FIND_IN_SET("LQDW-ZhuanYingDianFaQuanYe",receive_coupon_points)'];

        //平台只能查平台端的优惠券
        if ($this->admin_info['type'] == 1) {
            $params1['where']['a.set_type'] = $this->admin_info['type'];
            $params1['where']['a.apply_dlr_code'] = ['neq', ''];
        } else if ($this->admin_info['type'] == 2) {
            //专营店能查：平台、集团、本专营店的优惠券
            $dlr = $this->dbDlr->getOne(['where' => ['dlr_code' => $this->admin_info['dlr_code'], 'is_enable' => 1]]);
            if (!empty($dlr) && !empty($dlr['pid'])) {
                //有集团信息
                $pid_dlr            = $this->dbDlr->getOneByPk($dlr['pid']);
                $params1['where'][] = ['exp', "(dlr_code='{$this->admin_info['dlr_code']}' AND set_type=2) OR (dlr_code='{$pid_dlr['dlr_code']}' AND set_type=3) OR (dlr_code='NISSAN' AND FIND_IN_SET('{$this->admin_info['dlr_code']}',apply_dlr_code))"];
            } else {
                //没有集团信息
                $params1['where'][] = ['exp', "(dlr_code='{$this->admin_info['dlr_code']}' AND set_type=2) OR (dlr_code='NISSAN' AND FIND_IN_SET('{$this->admin_info['dlr_code']}',apply_dlr_code))"];
            }
        } else {
            //集团只能查本集团的优惠券
            $params1['where']['dlr_code'] = $this->admin_info['dlr_code'];
            $params1['where']['set_type'] = $this->admin_info['type'];
        }
        $params1['order']    = 'a.created_date desc';
        $params1['query']    = input('');
        $params1['pagesize'] = 5;
        $card_name           = input('get.card_name');
        $this->assign('card_name', $card_name);
        if ($card_name) {
            $params1['where']['card_name'] = array('like', "%$card_name%");
        }
        $params1['field'] = 'a.*,a.id as card_id';
        $params1['where']['act_status'] = ['in', [1, 2]];
        $params1['where']['card_type']  = ['not in', [6, 7]];
//        $params1['where'][]             = ['exp', "find_in_set('GWDLR', a.up_down_channel_dlr) and (find_in_set('GWSM', a.up_down_channel_dlr) or find_in_set('GWAPP', a.up_down_channel_dlr))"];
        $list             = $this->dbCard->getActivityCardPage($params1);
        if (input('test') == 1) {
            echo $this->dbCard->getLastSql();
        }
        foreach ($list as $k => $v) {
            $list[$k]['id']        = (string)$v['id'];
            $list[$k]['type']      = $this->dbCard->type()[$v['type']];
            $list[$k]['card_type'] = $this->dbCard->cardType()[$v['card_type']];
            $list[$k]['affili']    = $v['set_type'] == 2 ? '自建' : $this->dbCard->set_type()[$v['set_type']];
        }
        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('type', $this->admin_info['type']);
        return $this->fetch('send_card_page/card');
    }

    public function getMember()
    {
        $id       = input('get.id');
        $type     = input('get.type');
        $row      = $this->dbSend->getOneByPk($id);
        $qy_model = new BuQyUser();
        if (!empty($row)) {
            /*if($type=='platform'){

            }else{

            } //如果是平台端*/
            $qy_user_list = $qy_model->getList(['where' => ['dlr_code' => ['in', explode(',', $row['dlr_code'])], 'is_enable' => 1], 'field' => 'user_id,dlr_code,name']);
//            print_json(1,'',$row);
            $this->assign('qy_user_list', $qy_user_list);
            $this->assign('staff_list', explode(',', $row['staff_list']));
            $this->assign('dlr_code', $this->admin_info['dlr_code']);
            return $this->fetch('get_member');
        }

        return '';


    }

    public function updateMember()
    {
        $id        = input('post.id');
        $user_list = isset($_POST['user_list']) ? implode(',', $_POST['user_list']) : '';
        //print_json($id);
        $res = $this->dbSend->where(['id' => $id])->update(['last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => $this->admin_info['username'], 'staff_list' => $user_list]);
        print_json(0, '修改成功');

    }

    public function getSenCard($card_page_id, $dlr_code)
    {

        $model    = new AcRenewalQrc();
        $row      = $model->getOne(['where' => ['dlr_code' => $this->admin_info['dlr_code'], 'card_page_id' => $card_page_id]]);
        $page_row = $this->dbSend->getOneByPk($card_page_id);

        if (!$row) {
            $ren_id = $model->insertGetId(['dlr_code' => $dlr_code, 'number' => $page_row['card_num'], 'user_id' => $dlr_code, 'card_page_id' => $card_page_id]);
        } else {
            $ren_id = $row['id'];
        }

        return $url = url('active/marketing_card/cards', ['scene_id' => $ren_id, 'dlr_code' => $dlr_code, 'send_type' => $page_row['send_type'], 'card_page_id' => $card_page_id], true, true);

        /* print_json(0,'',$url);*/
    }

}