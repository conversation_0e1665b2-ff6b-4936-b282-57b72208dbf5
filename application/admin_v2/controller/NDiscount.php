<?php
/**
 * N件N折
 * User: lzx
 * Date: 2020-05-14 15:52:23
 */

namespace app\admin_v2\controller;

use app\admin_v2\service\RecommendActivityService;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlr;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbDlr;
use app\common\model\db\DbNDiscount;
use app\common\service\BaseDataService;
use app\common\model;
use app\common\service\CommodityService;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSystemValue;
use think\Db;
use think\Hook;
use app\common\model\db\DbCard;

class NDiscount extends Common
{
    private $n_model;
    private $comm_type_model;
    private $n_info_model;
    private $n_goods_model;
    private $dbCommodity;
    private $_dbCommodityDlrTypeObj;
    private $gather_list;

    public function __construct()
    {
        parent::__construct();
        $this->n_model                = new model\db\DbNDiscount();
        $this->n_info_model           = new model\db\DbNDiscountInfo();
        $this->n_goods_model          = new model\db\DbNDiscountCommodity();
        $this->dbCommodity            = new model\db\DbCommodity();
        $this->comm_type_model        = new model\db\DbCommodityType();
        $this->_dbCommodityDlrTypeObj = new DbCommodityDlrType();
        $model = new DbSystemValue();
        $this->gather_list = $model->where(['value_type'=>26,'is_enable'=>1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('gather_list', $this->gather_list);
        $this->assign('admin_type', $this->admin_info['type']);
    }

    public function index()
    {
        $discount_name = input('get.discount_name');  //活动名称
        $status        = input('get.status');  //状态

        $admin_type      = $this->admin_info['type'];
        $admin_dlr_code  = $this->admin_info['dlr_code'];
        $params['where'] = [];
        $params['query'] = [];
        if (!empty($discount_name)) {
            $params['where']['title'] = $discount_name;
        }
//        if(!empty($status)){
//            $params['where']['is_enable']=$status;
//        }
        $date = date('Y-m-d H:i:s');

        if ($status == 1) {
            $where['start_time'] = ['>', $date];
        } else if ($status == 2) {
            $where['start_time'] = ['<', $date];
            $where['end_time']   = ['>', $date];
        } else if ($status == 3) {
            $where['is_enable'] = '0';
        } else if ($status == 4) {
            $where['end_time'] = ['<', $date];
        }
        $params['field']    = "*";
        $params['pagesize'] = 20;
        $params['order']    = "id desc";
        $params['query']    = input('get.');
        $list               = $this->n_model->where($params['where'])
            ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));

        $page = $list->render();
        foreach ($list as $key => $val) {
            if ($val['is_enable'] == 1) {
                if ($val['start_time'] > $date) {
                    $list[$key]['status_name'] = '未开始';
                } else if ($val['start_time'] < $date && $val['end_time'] > $date) {
                    $list[$key]['status_name'] = '进行中';
                } else {
                    $list[$key]['status_name'] = '已结束';
                }
            } else {
                $list[$key]['status_name'] = '';
                if ($val['end_time'] < $date) {
                    $list[$key]['status_name'] = '已结束，';
                }
                $list[$key]['status_name'] .= '已关闭';
            }
        }
        $this->assign('page', $page);
        $this->assign('status', $status);
        $this->assign('query', $params['query']);
        $this->assign('list', $list);
        $this->assign('set_type', 3);
        return $this->fetch('index');
    }

    public function live()
    {
        $discount_name = input('get.discount_name');  //活动名称
        $status        = input('get.status');  //状态
        $date          = input('get.date');
        $gather_id = input('get.gather_id',0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name = input('get.theme_name');
        $live_type = input('get.live_type');
        if (empty($live_type)) {
            $set_type = 5;
            $params['where']['up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif($live_type == 2) {
            $set_type = 7;
            $params['where'][] = [['exp', 'FIND_IN_SET("QCSM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",up_down_channel_dlr)'], 'or'];
        } else {
        $set_type = 6;
        $params['where'][] = [['exp', 'FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'], 'or'];
    }
        $params['query'] = [];
        if (!empty($discount_name)) {
            $params['where']['title'] = ['like', "%{$discount_name}%"];
        }

        if (!empty($date)) {
            $date = explode(' ~ ', $date);
            if (count($date) == 2) {
                $params['where']['start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($date[0])), date('Y-m-d H:i:s', strtotime($date[1]) + 86400)]];
            }
        }

        $date = date('Y-m-d H:i:s');

        if ($status == 1) {
            $params['where']['start_time'] = ['>', $date];
        } else if ($status == 2) {
            $params['where']['start_time'] = ['<', $date];
            $params['where']['end_time']   = ['>', $date];
        } else if ($status == 3) {
            $params['where']['end_time'] = ['<', $date];
        } else if ($status == 4) {
            $params['where']['is_enable'] = '0';
        }
        if (in_array($status, [1, 2, 3])) {
            $params['where']['is_enable'] = 1;
        }
        if(!empty($gather_id)) {
            $params['where']['a.gather_id'] = $gather_id;
        }
        if(!empty($is_pv_subsidy)) {
            if($is_pv_subsidy != 2){
                $params['where']['is_pv_subsidy'] = $is_pv_subsidy;
            }else{
                $params['where']['is_pv_subsidy'] = 0;
            }
        }
        if(!empty($theme_name)) {
            $params['where']['theme_name'] = array('like','%'.$theme_name.'%');
        }

        $params['where']['set_type'] = $set_type;
        $params['field']             = "*";
        $params['pagesize']          = 20;
        $params['order']             = "id desc";
        $params['query']             = input('get.');
        $list                        = $this->n_model->where($params['where'])
            ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        $gather_array = [];
        foreach ($this->gather_list as $value){
            $gather_array[$value['id']] = $value['name'];
        }
        $page = $list->render();
        foreach ($list as $key => $val) {
            $in = empty($val['is_enable']) || $status == 4;
            if (strtotime($val['start_time']) > time()) {
                $list[$key]['status'] = $in ? '已关闭' : '未开始';
            } else if (strtotime($val['start_time']) <= time() && strtotime($val['end_time']) >= time()) {
                $list[$key]['status'] = $in ? '已关闭' : '进行中';
            } else if (strtotime($val['end_time']) < time()) {
                $list[$key]['status'] = $in ? '已关闭' : '已结束';
            }
            if($val['gather_id'] == 0 || empty($val['gather_id'])){
                $list[$key]['gather_name'] = '-';
            }else{
                if(isset($gather_array[$val['gather_id']])){
                    $list[$key]['gather_name'] = $gather_array[$val['gather_id']];
                }else{
                    $list[$key]['gather_name'] = '-';
                }
            }
            $list[$key]['is_pv_subsidy_status'] = $val['is_pv_subsidy'] == 1 ?'是': '否';
        }
        $this->assign('page', $page);
        $this->assign('status', $status);
        $this->assign('query', $params['query']);
        $this->assign('list', $list);
        $this->assign('set_type', $set_type);
        $this->assign('live_type', $live_type);
        return $this->fetch('live');
    }

    public function addLive()
    {
        //角色类型
        $adminType  = $this->admin_info['type'];
        $setType = input('set_type', 5);

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');

        $commodity_class = DbCommodity::commodityClass();
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY9]); // 延保服务包不参加活动


        $nvi_code = '';
        if($setType == 5){
            $nvi_code = 'N';
        }
        if($setType == 7){
            $nvi_code = 'V';
        }
        $user_level = [];
        if($setType == 5 || $setType == 7){
            $user_level = (new DbSystemValue())->getNameListByCode(25,$nvi_code);
        }
        $arr = ['county_name'=>'非会员','value_code'=>'NONE','order_no'=>0];
        array_unshift($user_level,$arr);
        $this->assign('user_level',$user_level);
        $this->assign('nvi_code',$nvi_code);
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('set_type', $setType);
        $this->assign('admin_type', $adminType);
        $this->assign('set_type', input('set_type',5));
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', (new DbSystemValue())->getNameList(20));
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));
        $this->assign('live_type', input('live_type'));
        return $this->fetch('add_live');
    }

    public function add()
    {
        $commodity_id = input('commodity_id');

        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);
        $this->assign('set_type', 3);
        return $this->fetch('add');
    }

    public function updateLive()
    {
        $id              = input('id');
        $n_dis           = $this->n_model->getOne(['where' => ['id' => $id]]);
        $where           = ['n_id' => $id, 'is_enable' => 1];
        $n_dis_info      = $this->n_info_model->getList(['where' => $where, 'order' => 'p_number asc,piece asc']);
        $n_dis_commodity = $this->n_goods_model->getList(['where' => $where]);
        $n_discount_comm = $this->n_goods_model->getNDiscountGoodsLive(['a.n_id' => $id]);

        //角色类型
        $adminType  = $this->admin_info['type'];
        $setType = input('set_type');
        // 会员级别
        if (!empty($n_dis['user_segment_options'])) {
            $user_segment_options_arr = explode(',',$n_dis['user_segment_options']);
            $n_dis['user_segment_options'] = $user_segment_options_arr[0];
        }
        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');

        $commodity_class = DbCommodity::commodityClass();
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY9]); // 延保服务包不参加活动


        $oneSkuData = $data['sku_list'][0] ?? [];
        $oneCommodityClass = $oneSkuData['commodity_class'] ?? 0;
        $oneCommodityDlrTypeId = $oneSkuData['commodity_dlr_type_id'] ?? 0;

        $row_comm        = [];
        $comm_id         = [];
        $comm_set_id_arr = [];
        foreach ($n_discount_comm as $key => $val) {
            $row_comm[]        = [
                'commodity_name'   => $val['commodity_name'],
                'cover_image'      => $val['cover_image'],
                'commodity_id'     => $val['commodity_id'],
                'commodity_set_id' => $val['commodity_set_id'],
                'comm_type_id'     => $val['comm_type_id'],
                'commodity_class'  => $val['commodity_class'],
                'is_home'          => $val['is_home'],
            ];
            $comm_set_id_arr[] = $val['commodity_set_id'];
            $comm_id[]         = $val['commodity_id'];
        }

        $dis_info = [];
        $pn_arr   = [];
        foreach ($n_dis_info as $k => $v) {
            if (!in_array($v['p_number'], $pn_arr)) {
                $dis_info[$v['p_number']]['p_number'] = $v['p_number'];
                $pn_arr[]                             = $v['p_number'];
            }

            $dis_info[$v['p_number']]['list'][] = [
                'piece'    => $v['piece'],
                'discount' => $v['discount']
            ];
        }
        $dis_info = array_merge($dis_info);

        $up_down_channel_info = (new DbSystemValue())->getNameList(20);
        $info_str             = implode(',', array_keys($up_down_channel_info));

        $card_arr = [];
        $selected_data = [];
        $commodity_card_name = [];
        if(!empty($n_dis['rel_card_ids'])) {
            $commodity_card_name = (new DbCard())->getColumn(['where' => ['id' => ['in', $n_dis['rel_card_ids']]], 'column' => 'card_name']);
            if (!empty($commodity_card_name)) {
                $selected_data = (new DbCard())->getColumn(['where' => ['id' => ['in', $n_dis['rel_card_ids']]], 'column' => 'id,card_name']);
                $card_arr = explode(',', $n_dis['rel_card_ids']);
            }
        }
        if (empty($card_arr)) {
            $n_dis['rel_card_ids'] = '';
        }
        $n_dis['commodity_card_name'] = implode(',', $commodity_card_name);
        $n_dis['num_card'] = empty($n_dis['rel_card_ids']) ? 0 : count(explode(',', $n_dis['rel_card_ids']));

        $nvi_code = '';
        if($setType == 5){
            $nvi_code = 'N';
        }
        if($setType == 7){
            $nvi_code = 'V';
        }
        $user_level_all = [];
        if($setType == 5 || $setType == 7){
            $user_level = (new DbSystemValue())->getNameListByCode(25,$nvi_code);
            foreach ($user_level as $item) {
                $user_level_all[] = $item->toArray();
            }
        }

        $arr = ['county_name'=>'非会员','value_code'=>'NONE','order_no'=>0];
        array_unshift($user_level_all,$arr);
        $this->assign('user_level',$user_level_all);
        $this->assign('nvi_code',$nvi_code);
        $this->assign('selected',json_encode($card_arr));
        $this->assign('selected_data', $selected_data);
        $this->assign('selected_card_name',json_encode($commodity_card_name));


        $this->assign('oneCommodityClass', $oneCommodityClass);
        $this->assign('oneCommodityDlrTypeId', $oneCommodityDlrTypeId);
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', $up_down_channel_info);
        $this->assign('dlr_hidden', (strpos($n_dis['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
        $this->assign('dlr_str', $this->getDlrInInfo($n_dis['up_down_channel_dlr']));
        $this->assign('comm_set_id_arr', json_encode_cn($comm_set_id_arr));
        $this->assign('comm_id', json_encode_cn($comm_id));
        $this->assign('row_comm', $row_comm);
        $this->assign('row', $n_dis);
        $this->assign('dis_info', $dis_info);
        // 会员
        $user_n_dis_info = [];
        if ($n_dis['user_segment'] == 1) {
            foreach ($n_dis_info as $item) {
                $arr = $item->toArray();
                $segment_label[$arr['segment_label']] = $arr['discount'];
                $user_n_dis_info[$item['piece']] = [
                    'discount'=>$segment_label,
                    'piece'=>$item['piece']
                ];
            }
        }

        // 车主
        $car_n_dis_info = [];
        if ($n_dis['user_segment'] == 2) {
            foreach ($n_dis_info as $item) {
                $arr = $item->toArray();
                $segment_label[$arr['segment_label']] = $arr['discount'];
                $car_n_dis_info[$item['piece']] = [
                    'discount'=>$segment_label,
                    'piece'=>$item['piece'],
                ];
            }
        }
         $old_user_level = [];
        $order_no = 0;

        foreach ($user_level_all as $item) {
            if ($item['value_code'] == $n_dis['user_segment_options']) {
                $order_no = $item['order_no'];
            }
        }
        foreach ($user_level_all as $item) {
            if ($item['order_no'] >= $order_no) {
                $old_user_level[] = $item;
            }
        }

//        dump($n_dis_info);
//        dd(array_merge($car_n_dis_info));
//        dd($user_n_dis_info);
        $this->assign('old_user_level', array_merge($old_user_level));
        $this->assign('user_n_dis_info', array_merge($user_n_dis_info));
        $this->assign('car_n_dis_info', array_merge($car_n_dis_info));
        $this->assign('n_dis_info', $n_dis_info);
        $this->assign('n_dis_commodity', $n_dis_commodity);
        $this->assign('set_type', $setType);
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));
        $this->assign('live_type', input('live_type'));
        return $this->fetch('update_live');
    }

    public function update()
    {
        $id               = input('id');
        $n_dis            = $this->n_model->getOne(['where' => ['id' => $id]]);
        $where            = ['n_id' => $id, 'is_enable' => 1];
        $n_dis_info       = $this->n_info_model->getList(['where' => $where, 'order' => 'p_number asc,piece asc']);
        $n_dis_commodity  = $this->n_goods_model->getList(['where' => $where]);
        $comm_parent_list = $this->comm_type_model->getCommodityByParentId(0);
        $n_discount_comm  = $this->n_goods_model->getNDiscountGoods(['a.n_id' => $id]);
        $row_comm         = [];
        $comm_id          = [];
        $comm_set_id_arr  = [];
        foreach ($n_discount_comm as $key => $val) {
            $row_comm[]        = [
                'commodity_name'   => $val['commodity_name'],
                'cover_image'      => $val['cover_image'],
                'commodity_id'     => $val['commodity_id'],
                'commodity_set_id' => $val['commodity_set_id'],
            ];
            $comm_set_id_arr[] = $val['commodity_set_id'];
            $comm_id[]         = $val['commodity_id'];
        }
//         var_dump($row_comm);die();
        $dis_info = [];
        $pn_arr   = [];
        foreach ($n_dis_info as $k => $v) {
            if (!in_array($v['p_number'], $pn_arr)) {
                $dis_info[$v['p_number']]['p_number'] = $v['p_number'];
                $pn_arr[]                             = $v['p_number'];
            }

            $dis_info[$v['p_number']]['list'][] = [
                'piece'    => $v['piece'],
                'discount' => $v['discount']
            ];
        }
        $dis_info           = array_merge($dis_info);
        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);
        $this->assign('comm_set_id_arr', json_encode_cn($comm_set_id_arr));
        $this->assign('comm_id', json_encode_cn($comm_id));
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('row_comm', $row_comm);
        $this->assign('row', $n_dis);
        $this->assign('dis_info', $dis_info);
        $this->assign('n_dis_info', $n_dis_info);
        $this->assign('n_dis_commodity', $n_dis_commodity);
        $this->assign('set_type', 3);
        return $this->fetch('update');
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetLiveCommodityList()
    {
        $commodity_name        = input('commodity_name');
        $top_type              = input('comm_parent_id');
        $second_type           = input('sub_comm_type_id');
        $third_type            = input('three_comm_type_id');
        $active_id             = input('active_id');
        $commodity_class       = input('commodity_class');
        $commodity_dlr_type_id = input('commodity_dlr_type_id');
        $start_time            = input('start_time');
        $end_time              = input('end_time');
        $user_segment          = input('user_segment');

        $live_type = input('live_type');
        $where = [
            'b.listing_type'    => 1,
        ];

        $brand = 1;
        if (empty($live_type)) {
            $where['b.shelves_type']        = 5;
            $where['b.qsc_group'] =  '';  // 取送车服务包不能参加
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($live_type == 2) {
            $brand = 2;
            $where['b.shelves_type'] = 7;
            $where[]                 = [['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], 'or'];
        } else {
            $brand = 3;
            $where['b.shelves_type'] = 6;
            $where[]                 = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if (!empty($commodity_name)) {
            $where['commodity_name'] = ['like', "%$commodity_name%"];
        }

        $type_id = 0;
        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

        if (!empty($commodity_class)) {
            $where['commodity_class'] = $commodity_class;
        } else {
            $where['commodity_class'] = ['neq', DbCommodity::COMMODITY_CLASS_KEY9]; // 延保服务包不能参加

        }

        if (!empty($commodity_dlr_type_id)) {
            $where['b.commodity_dlr_type_id'] = $commodity_dlr_type_id;
        }

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }


        $field  = "b.commodity_dlr_type_id,a.commodity_class,comm_type_id,a.up_down_channel_name,a.up_down_channel_dlr,a.commodity_id,a.commodity_set_id,commodity_name,cover_image,a.price,a.count_stock";
        $params = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );

        $flat = new DbCommodityFlat();
        $res  = [];
        $list = $flat->getCommodityList($params);
        $date = date('Y-m-d H:i:s');

        //如果是快递到家#
        $homeArr = $this->_dbCommodityDlrTypeObj->getExpressHomeType();
        foreach ($list as $key => $val) {
            $list[$key]['home'] = 0;
            if (isset($val['commodity_dlr_type_id']) && in_array($val['commodity_dlr_type_id'], $homeArr)) {
                $list[$key]['home'] = 1;
            }
        }
        if (empty($start_time)) {
            $start_time = $date;
        }
        if (empty($end_time)) {
            $end_time = $date;
        }

        if (!empty($start_time)) $where_start_time = ['<', $end_time];
        if (!empty($end_time)) $where_end_time = ['>', $start_time];
        $where_time = [
            'start_time' => $start_time,
            'end_time' => $end_time,
        ];

        //是否有参加预售
        $list = (new DbPreSale())->getIsPreProduct($list, [
            'a.is_enable'      => ['=', 1],
            'a.front_s_time'   => $where_start_time ?? ['<', $date],
            'a.balance_e_time' => $where_end_time ?? ['>', $date],
            'a.set_type'       => $where['b.shelves_type'],
        ]);

        //是否NN
        $map = [
            'a.id'         => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in',[1,2]],
        ];
        $list = (new DbNDiscount())->getIsNDiscount($list, $map, $where_time);

        //是否秒杀
        $map = [
            'a.id'         => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1,2]],
        ];
        $list = (new model\db\DbSeckill())->getIsSeckillProduct($list, $map, $where_time);

        $segment_discount_type_model = new model\db\DbCommodityTypeSegmentDiscount();
        if (!empty($user_segment)) {
            // 判断商品分类的定向人群优惠
            foreach ($list as $key => $item) {

                $re = $segment_discount_type_model->getSegmentByCommTypeId($item->comm_type_id,$brand);
                $list[$key]['is_user_segment'] = 0; // 商品没有设置定向人群

                if (!is_null($re)) {
                    if ($re['user_segment'] == $user_segment) {
                        $list[$key]['is_user_segment'] = 1; // 定向人群一致
                    } else {
                        $list[$key]['is_user_segment'] = 2; // 定向人群不一致
                    }
                }

            }

        }

        $res['list'] = $list;
        print_json(0, '', $res);
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetCommodityList()
    {
        $commodity_name  = input('commodity_name');
        $comm_type_id    = input('comm_type_id');
        $comm_parent_id  = input('comm_parent_id');
        $all_s           = input('all_s', 0);
        $params['where'] = ['a.commodity_class' => 1];
        $params['query'] = [];
        $set_type        = input('get.set_type', 3);
        if (!empty($commodity_name)) {
            $params['where']['a.commodity_name'] = ['like', "%$commodity_name%"];
        }
        if (!empty($comm_type_id)) {
            $params['where']['a.comm_type_id'] = $comm_type_id;
        } else {
            if (!empty($comm_parent_id)) {
                $comm_type_column                  = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            }
        }
        $params['where']['e.shelves_type']    = 3;//官微上架
        $params['where']['a.commodity_class'] = 1;//实物商品
        $params['order']                      = 'a.id desc';
        $params['query']                      = input('get.');

        $params['pagesize'] = input('pagesize');
        if ($all_s == 1) {
            $params['pagesize'] = 2000;
        }
        $params['shelves_type'] = 3;
        $dlr_code               = $this->admin_info['dlr_code'];
        $set_type               = $this->admin_info['type'];
        $list                   = $this->dbCommodity->getShelvesListByDlr($set_type, $dlr_code, $params);
//        $sql =  $list['sql'];
//        $list =$list['list'];
        $commodity_set_id_list = [];
        foreach ($list as $key => $val) {
            $commodity_set_id_list[] = $val['commodity_set_id'];
            $list[$key]['is_limit']  = 0;
            if (!empty($val['highest_price'])) $list[$key]['original_price_range_end'] = $val['highest_price'];
        }
        //处理是否已经折扣
//        $limit_dlr_list=$this->dbLimitDiscountCommodity->getDlrByCommodity(['is_enable'=>1,'commodity_set_id'=>['in',$commodity_set_id_list]]);
        $comm_dl_model = new DbCommodityDlr();
        $dlr_list      = $comm_dl_model->getDlrBySetId($commodity_set_id_list);
        $is_limit_id   = [];

        foreach ($list as $key => $val) {
            if (in_array($val['commodity_set_id'], $is_limit_id)) $list[$key]['is_limit'] = 1;
        }

        $comm_parent_list2        = $this->comm_type_model->getCommTypeName();
        $res                      = [];
        $res['list']              = $list;
        $res['comm_parent_list2'] = $comm_parent_list2;
        print_json(0, '', $res);
    }

    public function getSku()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $comm_set_model   = new DbCommoditySet();
        $com_set_row      = $comm_set_model->getOne(['where' => ['id' => $commodity_set_id], 'field' => 'commodity_id,dlr_code,set_type']);
        // var_dump($commodity_set_id);
        $sku           = $this->comm_service->getSetSku($commodity_set_id);
        $sku_list      = $sku['sku_list'];
        $commodity_row = $this->dbCommodity->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image']);
        // var_dump($sku);
        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (!empty($val['sp_value_arr'])) {
                foreach ($val['sp_value_arr'] as $key1 => $val1) {
                    $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
                }
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    /**
     * 保存数据--车生活
     */
    public function saveLive()
    {
        $post = input('post.');
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '结束时间不能为空');
        $des             = input('post.des');
        $is_enable       = input('post.is_enable', '1');
        $id              = input('post.id');
        $home            = input('post.home');
        $commodity_class = input('post.commodity_class');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');
        $ids                 = input('post.s_ids');
        $goods               = input('post.goods');//商品ID列表
        $set_type            = input('post.set_type');
        $n_info              = $post['n_info'];
        $card_available      = input('post.card_available', 0);
        $up_down_channel_arr = $post['up_down_channel'] ?? [];
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $up_down_channel_arr),
            input('post.dlr_code', '')
        );
        $user_segment = input('user_segment',0);
        $user_segment_options = input('user_segment_options');
        $sku_list            = [];
        if (!is_array($n_info)) {
            $n_info              = json_decode($n_info);
        }
        $n_info_arr          = [];
        $nvi_code = 'N';
        $setType = input('set_type', 5);
        $gather_id = input('gather_id',0);
        $is_pv_subsidy = input('is_pv_subsidy',0);
        $theme_name = input('theme_name');

        if($setType == 5){
            $nvi_code = 'N';
        }
        if($setType == 7){
            $nvi_code = 'V';
        }
        $user_segment_options_arr = [];
        foreach ($n_info as $k => $v) {
            $n_info_arr[$k]['p_number'] = 0;
            $pp_dis                     = [];
            if ($user_segment == 0) {
                foreach ($v[1] as $kk => $vv) {
                    $kk_data  = [
                        'piece'         => $vv[0],
                        'pdiscountiece' => $vv[1],
                    ];
                    $pp_dis[] = $kk_data;
                }

            } elseif($user_segment == 1) {
                // 会员
                foreach ($v->zhe as $key => $val) {
                    $pp_dis[] = [
                        'piece' => $v->jian,
                        'pdiscountiece' => $val,
                        'segment_label' => $key
                    ];
                }

                // 会员级别处理
                $user_level = (new DbSystemValue())->getNameListByCode(25,$nvi_code);

                $user_segment_order_no = '';

                // 非会员
                if ($user_segment_options == 'NONE') {
                    $user_segment_order_no = 0;
                    $user_segment_options_arr = ['NONE'];
                }
                foreach ($user_level as $key => $val) {
                    if ($val['value_code'] == $user_segment_options) {
                        $user_segment_order_no = $val['order_no'];
                    }
                    if ((is_numeric($user_segment_order_no)) && ($user_segment_order_no <= $val['order_no'])) {
                        $user_segment_options_arr[] = $val['value_code'];
                    }
                }


            } else {
                // 车主
                $pp_dis = [
                    // 车主
                    [
                        'piece' => $v->jian,
                        'pdiscountiece' => $v->zhe->car,
                        'segment_label' => $nvi_code
                    ],
                ];
                // 日产
                if ($set_type == 5) {
                    $user_segment_options_arr = ['N'];
                }
                // 启辰
                if ($set_type == 7) {
                    $user_segment_options_arr = ['V'];
                }

                // pz1a
                if ($set_type == 6) {
                    $pz1a = [
                        'piece' => $v->jian,
                        'pdiscountiece' => $v->zhe->a_car,
                        'segment_label' => 'P'
                    ];
                    array_push($pp_dis,$pz1a);
                    $user_segment_options_arr = ['N','P'];
                }
                if (!empty($v->zhe->no_car)) {
                    array_unshift($user_segment_options_arr,'NONE');
                    $none_arr = [
                        'piece' => $v->jian,
                        'pdiscountiece' => $v->zhe->no_car,
                        'segment_label' => 'NONE'
                    ];
                    array_unshift($pp_dis, $none_arr);
                }
            }
            $n_info_arr[$k]['s_cat'] = $pp_dis;
        }

        if ($ids) {
            $ids   = explode(",", $ids);
            $goods = explode(",", $goods);
            if ($ids) {
                foreach ($ids as $k => $v) {
                    $sku_list[$k]['commodity_set_id'] = $v;
                    $sku_list[$k]['commodity_id']     = $goods[$k];
                    $sku_list[$k]['commodity_name']   = DbCommodity::where('id',$goods[$k])->value('commodity_name');
                }
            }
        }
        $act_status = $this->n_model->getActStatus($start_time, $end_time);

        $data = [
            'title'                 => $title,
            'start_time'            => $start_time,
            'end_time'              => $end_time,
            'tag'                   => $post['tag'],
            'act_status'            => $act_status,
            'is_enable'             => $is_enable,
            'des'                   => $des,
            'creator'               => $this->admin_info['username'],
            'created_dlr_code'      => $this->admin_info['dlr_code'],
            'card_available'        => $card_available,
            'set_type'              => $set_type,
            'up_down_channel_name'  => implode(',', $post['up_down_channel_name'] ?? []),
            'up_down_channel_dlr'   => $up_down_channel_dlr,
            'e3s_activity_id'       => $post['e3s_activity_id'] ?? 0,
            'activity_type'         => $post['activity_type'] ?? 0,
            'settlement_rule_id'    => $post['settlement_rule_id'],
            'settlement_rule_name'  => $post['settlement_rule'],
            'settlement_rule_type'  => $post['settlement_rule_type'],
            'settlement_rule_value' => $post['settlement_rule_value'],
            'act_sett_standard'     => $post['act_sett_standard'],
            'rel_card_ids'          => $post['rel_card_ids'] ?? '',
            'gather_id'             => $gather_id,
            'is_pv_subsidy'         => $is_pv_subsidy,
            'theme_name'            => $theme_name,
        ];
        // 有定向人群
        if ($user_segment) {
            $data['user_segment'] = $user_segment;
            $data['user_segment_options'] = implode(',',$user_segment_options_arr);
        }
        if (!empty($data['e3s_activity_id'])){
            if (empty($data['activity_type'])) print_json(1, '存在e3s活动时活动设置类型必选');
        }else{
            $data['activity_type'] = 0;
        }
        $e3s_activity = input('e3s_activity', '');
        if (!empty($e3s_activity)){
            $data['e3s_activity_name'] = explode(' | ', $e3s_activity)[1];
        }


        //开启事物
        $this->n_model->startTrans();

        $rm_commodity_id_arr = [];

        if (empty($id)) {  //插入
            $last_id = $this->n_model->insertGetId($data);
            $res     = $this->insertCommodiySkuDataLive($sku_list, $last_id, 'add', $set_type, $home, $commodity_class);
            if (!$res) {
                print_json(1, '保存失败');
            }
            //保存折扣
            $res = $this->insertDisInfo($n_info_arr, $last_id);
            if (!$res) {
                print_json(1, '保存折扣失败');
            }
        } else {    //修改
            if ($is_enable == 1) {
                if (empty($ids)) {
                    print_json(1, '已选商品不能为空');
                }
                $date = date('Y-m-d H:i:s');
                $where_time = ['start_time'=>$start_time, 'end_time'=>$end_time];
                //是否有参加预售
                $list = (new DbPreSale())->getIsPreProduct(
                    $sku_list, [
                    'a.is_enable'      => ['=', 1],
                    'a.front_s_time'   => ['<', $date],
                    'a.balance_e_time' => ['>', $date],
                    'a.set_type'       => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_pre'] == 1) print_json(1, $v['commodity_name'] . '已参加预售活动');
                }
                //是否NN
                $map = [
                    'a.id'         => ['<>', $id],
                    'a.is_enable'  => ['=', 1],
                    'a.set_type'   => $set_type,
                    'a.act_status' => ['in', [1,2]]
                ];
                $list = (new DbNDiscount())->getIsNDiscount($sku_list, $map, $where_time);
                foreach ($list as $v) {
                    if ($v['is_n_discount'] == 1) print_json(1, $v['commodity_name'] . '已参加其它N件N折活动');
                }
                // 是否是秒杀
                $map = [
                    'a.id'         => ['<>', $id],
                    'a.is_enable'  => ['=', 1],
                    'a.set_type'   => $set_type,
                    'a.act_status' => ['in', [1, 2]],
                ];

                $list = (new model\db\DbSeckill())->getIsSeckillProduct($sku_list,$map,$where_time);
                foreach ($list as $v) {
                    if ($v['is_seckill'] == 1) print_json(1, $v['commodity_name'] . '已参加其它秒杀活动');
                }
            }
            $row     = $this->n_model->getOneByPk($id);
            $last_id = $id;
            if ($row && $row['created_dlr_code'] == $this->admin_info['dlr_code']) {
                $data['modifier']          = $this->admin_info['username'];
                $data['last_updated_date'] = date('Y-m-d H:i:s');
                $res                       = $this->n_model->isUpdate(true)->saveData($data, ['id' => $id]);
                if ($res) {

                    $old_commodity_id_arr = $this->n_goods_model->where(['n_id' => $id])->column('commodity_id');
                    $new_commodity_id_arr = array_column($sku_list, 'commodity_id');
                    $rm_commodity_id_arr  = array_diff($old_commodity_id_arr, $new_commodity_id_arr);

                    $this->n_goods_model->where(['n_id' => $id])->delete();
                    $res_sku = $this->insertCommodiySkuDataLive($sku_list, $id, 'update', $set_type, $home, $commodity_class);
                    if (!$res_sku) {
                        print_json(1, '活动商品保存失败');
                    }
                    //保存折扣
                    $this->n_info_model->where(['n_id' => $id])->delete();
                    $res = $this->insertDisInfo($n_info_arr, $id);
                    if (!$res) {
                        print_json(1, '保存折扣失败');
                    }
                }
            }
        }
        //提交
        $this->n_model->commit();
        if (($act_status == 2) && !empty($is_enable)) {
            $n_commodity_arr = $this->n_model->getAllNDiscount([
                'where' => ['a.id' => $last_id],
                'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
            ]);
            $this->doHook('add', $n_commodity_arr, $last_id);
        } else if (empty($is_enable)) {
            $n_commodity_arr = $this->n_model->getAllNDiscount([
                'where' => ['a.id' => $last_id],
                'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
            ]);
            $this->doHook('delete', $n_commodity_arr, $last_id);
        }

        if (!empty($rm_commodity_id_arr)) {
            $this->doHook('delete', $rm_commodity_id_arr, $last_id);
        }

        print_json(0, '保存成功');
    }

    /**
     * 保存数据
     */
    public function save()
    {
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '结束时间不能为空');
//        $discount = input('post.discount') or  print_json(1,'折扣不能为空');
        $des       = input('post.des');
        $is_enable = input('post.is_enable', '1');
        $id        = input('post.id');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');
//        if (empty($_POST['sku_list'])) print_json(1,'商品规格不能为空');
//        $sku_list        = $_POST['sku_list'];
        $ids        = input('post.s_ids');
        $goods      = input('post.goods');//商品ID列表
        $set_type   = input('post.set_type');
        $n_info     = input('post.n_info');
        $sku_list   = [];
        $n_info     = json_decode($n_info);
        $n_info_arr = [];

        foreach ($n_info as $k => $v) {
            $n_info_arr[$k]['p_number'] = $v[0];
            $pp_dis                     = [];
            foreach ($v[1] as $kk => $vv) {
                $kk_data  = [
                    'piece'         => $vv[0],
                    'pdiscountiece' => $vv[1],
                ];
                $pp_dis[] = $kk_data;
            }
            $n_info_arr[$k]['s_cat'] = $pp_dis;
        }
        if ($ids) {
            $ids   = explode(",", $ids);
            $goods = explode(",", $goods);
            if ($ids) {
                foreach ($ids as $k => $v) {
                    $sku_list[$k]['commodity_set_id'] = $v;
                    $sku_list[$k]['commodity_id']     = $goods[$k];
                }
            }

        }
        $data = [
            'title'            => $title,
            'start_time'       => $start_time,
            'end_time'         => $end_time,
            'is_enable'        => $is_enable,
            'des'              => $des,
            'creator'          => $this->admin_info['username'],
            'created_dlr_code' => $this->admin_info['dlr_code'],
        ];
        //开启事物
        $this->n_model->startTrans();
        if (empty($id)) {  //插入
            $last_id = $this->n_model->insertGetId($data);

            $res = $this->insertCommodiySkuData($sku_list, $last_id, 'add', $set_type);
            if (!$res) {
                print_json(1, '保存失败');
            }
            //保存折扣
            $res = $this->insertDisInfo($n_info_arr, $last_id);
            if (!$res) {
                print_json(1, '保存折扣失败');
            }

        } else {    //修改
            $row = $this->n_model->getOneByPk($id);
            if ($row && $row['created_dlr_code'] == $this->admin_info['dlr_code']) {
                $data['modifier']          = $this->admin_info['username'];
                $data['last_updated_date'] = date('Y-m-d H:i:s');
                $res                       = $this->n_model->isUpdate(true)->saveData($data, ['id' => $id]);
                if ($res) {
                    $this->n_goods_model->where(['n_id' => $id])->delete();
                    $res_sku = $this->insertCommodiySkuData($sku_list, $id, 'update', $set_type);
                    if (!$res_sku) {
                        print_json(1, '活动商品保存失败');
                    }
                    //保存折扣
                    $this->n_info_model->where(['n_id' => $id])->delete();
                    $res = $this->insertDisInfo($n_info_arr, $id);
                    if (!$res) {
                        print_json(1, '保存折扣失败');
                    }
                }
            }
        }
        //提交
        $this->n_model->commit();
        print_json(0, '保存成功');
    }

    public function delete()
    {
        $this->_checkAjax();
        $id  = input('post.id');
        $row = $this->n_model->getOneByPk($id);
        //创建专营店==管理专营店，也就是由这个店创建的才可以删除
        if ($row['created_dlr_code'] == $this->admin_info['dlr_code']) {
            $this->n_info_model->where(['n_id' => $id])->delete();
            $this->n_goods_model->where(['n_id' => $id])->delete();
            $this->n_model->where(['id' => $id])->delete();

            if ($row['set_type'] == 5) {
                $n_commodity_arr = $this->n_model->getAllNDiscount([
                    'where' => ['a.id' => $id],
                    'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                ]);
                $this->doHook('delete', $n_commodity_arr, $id);
            }

            print_json(0, '删除成功');
        }
    }

    /**
     * 获取商品分类
     */

    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);

    }

    //插入N件N折方案
    public function insertDisInfo($info_arr, $n_id)
    {
        $in_data = [];
        foreach ($info_arr as $k => $v) {
            foreach ($v['s_cat'] as $vv) {
                $data_one  = [
                    'n_id'     => $n_id,
                    'p_number' => $v['p_number'],
                    'piece'    => $vv['piece'],
                    'discount' => $vv['pdiscountiece'],
                    'segment_label' => $vv['segment_label'] ?? 0,
                ];
                $in_data[] = $data_one;
            }
        }
        $lastid = $this->n_info_model->insertAll($in_data);
        return $lastid;
    }

    /**
     * 插入商品及规格
     * @param $sku_list
     * @param $fight_id
     * @param $action
     * @param $set_type
     * @param $home
     * @return bool|int|string
     */
    public function insertCommodiySkuDataLive($sku_list, $fight_id, $action, $set_type, $home = 0, $commodity_class = 1)
    {
        if (empty($sku_list)) {
            return false;
        }
        $data              = [];
        $commodity_set_ids = [];
        foreach ($sku_list as $key => $val) {
            $data[]              = [
                'commodity_id'     => $val['commodity_id'],
                'commodity_set_id' => $val['commodity_set_id'],
                'n_id'             => $fight_id,
                'dlr_code'         => $this->admin_info['dlr_code'],
                'creator'          => $this->admin_info['username'],
                'is_home'          => $home,
                'commodity_class'  => $commodity_class,
            ];
            $commodity_set_ids[] = $val['commodity_set_id'];
        }

        //删除已经添加的商品
        if ($this->admin_info['type'] == 1) {
            $where = ['n_id' => ['in', [$fight_id]]];
        } else {
            $where = ['n_id' => ['in', [$fight_id]], 'dlr_code' => $this->admin_info['dlr_code']];
        }
        $this->n_goods_model->where($where)->delete();
        $lastid = $this->n_goods_model->insertAll($data);

        // 删除推荐活动不存在的商品
        $service = new RecommendActivityService();
        $service->delNotInActivityCommodity(4, $fight_id);

        return $lastid;
    }

    /**
     * 插入商品及规格
     * @param $sku_list
     * @param $fight_id
     * @param $action
     * @return bool|int|string
     */
    public function insertCommodiySkuData($sku_list, $fight_id, $action, $set_type)
    {
        if (empty($sku_list)) {
            return false;
        }
        $data = [];

        if ($set_type == 2) {
            $dlr_code = $this->admin_info['dlr_code'];
        } elseif ($set_type == 3) {
            $dlr_code = 'GWSC';
        }
        $commodity_set_ids = [];
        foreach ($sku_list as $key => $val) {
            $data[]              = [
                'commodity_id'     => $val['commodity_id'],
                'commodity_set_id' => $val['commodity_set_id'],
                'n_id'             => $fight_id,
                'dlr_code'         => $this->admin_info['dlr_code'],

                'creator' => $this->admin_info['username'],
            ];
            $commodity_set_ids[] = $val['commodity_set_id'];
            // var_dump($data);
        }

        //删除已经添加的商品
        if ($this->admin_info['type'] == 1) {
            $where = ['commodity_set_id' => ['in', $commodity_set_ids]];
        } else {
            $where = ['commodity_set_id' => ['in', $commodity_set_ids], 'dlr_code' => $this->admin_info['dlr_code']];
        }
        $this->n_goods_model->where($where)->delete();
        $lastid = $this->n_goods_model->insertAll($data);
        return $lastid;
    }

    private function doHook($type = 'delete', $commodity_arr = [], $act_id = 0)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;

        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        $activity = DbNDiscount::where('id', $act_id)->find()->toArray();
        if (!empty($activity['up_down_channel_dlr'])) {
            $up_down_arr = explode(',', $activity['up_down_channel_dlr']);
            $shelves_ni  = array_intersect(DbDlr::$ni_arr, $up_down_arr);
            $shelves_pz  = array_intersect(DbDlr::$pz1a_arr, $up_down_arr);
            $shelves_qc  = array_intersect(DbDlr::$qc_arr, $up_down_arr);

            if (!empty($shelves_ni)) {
                $shelves_type = DbDlr::$ni_shelves;
            } else if (!empty($shelves_pz)) {
                $shelves_type = DbDlr::$pz1a_shelves;
            } else if (!empty($shelves_qc)) {
                $shelves_type = DbDlr::$qc_shelves;
            } else {
                return false;
            }

            switch ($type) {
                case 'add':
                case 'update':
                    # 添加商品不需要刷活动信息
                    foreach ($commodity_arr as $item_one) {
                        $item_one['shelves_type'] = $shelves_type;
                        Hook::listen('flat_n_discount', $item_one);
                    }
                    break;
                case 'delete':
                    # 删除就只需要将数据删除就ok
                    foreach ($commodity_arr as $item_one) {
                        $del_params = [
                            'shelves_type' => $shelves_type,
                            'del_dis'      => true,
                            'act_id'       => $act_id,
                            'commodity_id' => empty($item_one['commodity_id']) ? $item_one : $item_one['commodity_id']
                        ];
                        Hook::listen('flat_n_discount', $del_params);
                    }
                    break;
            }

            $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
            Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);

            return $result;
        }
        return false;
    }

    /**
     * 获取卡券
     */
    public function ajaxGetCard(){
        $this->_checkAjax();
        $model = new DbCard();
        $card_name = input('get.card_name');
        $set_type = input('set_type', 0);
        $where = [];
        $shelves_type = 5;
        if($set_type == 5){
            $where['up_down_channel_dlr'] =[['notlike','%PZ1ASM%'],['notlike','%PZ1AAPP%'],['notlike','%QCSM%'],['notlike','%QCAPP%'],'and'];
        }elseif($set_type == 6){
            $shelves_type = 6;
            $where[] =[['exp','FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'],['exp','FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'],'or'];
        }elseif($set_type == 7){
            $shelves_type = 7;
            $where[] =[['exp','FIND_IN_SET("QCSM",up_down_channel_dlr)'],['exp','FIND_IN_SET("QCAPP",up_down_channel_dlr)'],'or'];
        }
        if(!empty($card_name))
            $where['card_name'] = ['like','%'.$card_name.'%'];

        $where['shelves_type'] = $shelves_type;
        $where['is_enable'] = 1;
        $where['type'] = 2;
        $where['act_status'] = ['in', [1, 2, 3]];
        $params = [
            'where'=>$where,
            'order'=>'id desc',
            'field'=>'id,is_enable,act_status,card_type,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to,available_count,up_down_channel_name',
            'pagesize'=>input('get.pagesize'),
            'query'=>input('get.')
        ];
        $list = $model->getListPaginate($params);
        $card_type_arr = $model->cardType();


        foreach ($list as $key=>$value){
            $list[$key]['id'] = (string)$value['id'];
            $list[$key]['card_type_name'] = $card_type_arr[$value['card_type']] ?? '';

            switch ($value['date_type']){
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'].'至'.$value['validity_date_end'];
                    break;
                case 2:
                    if($value['fixed_begin_term']==0){
                        $list[$key]['validity_date'] = "自领取当天有效，有效期".$value['fixed_term'].'天';
                    }elseif ($value['fixed_begin_term']==1){
                        $list[$key]['validity_date'] = "领取后".$value['fixed_term'].'天后有效';
                    }
                    break;
                default:
                    break;
            }
            $value['status_name'] = empty($value['is_enable']) ? '已关闭' : $model->cardStatus()[$value['act_status']];
        }
        print_json(0,'',$list);
    }

}
