<?php
/**
 * 订单统计
 * @author: lian<PERSON>ijian
 * @time: 2017-04-15
 */

namespace app\admin_v2\controller;


use app\common\model\act\AcHaveTradeList;
use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;

use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderDetailed;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCrowdfund;
use app\common\model\db\DbExports;
use app\common\model\db\DbHomeTypeCommodity;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbOrderInvoice;
use app\common\model\db\DbSystemValue;
use think\Model;
use think\Queue;
use tool\Logger;
use tool\PhpExcel;

class OrderStatic extends Common
{

    protected $comm_type_model;

    public function __construct()
    {
        parent::__construct();
        $this->comm_type_model = new DbCommodityType();
    }

    public function index($set_channel = '1,2,3')
    {
        set_time_limit(300);
        $api_start_at = microtime(true);

        $buOrder            = new BuOrder();
        $order_commodity_model = new BuOrderCommodity();

        $order_code         = trim(input('get.order_code'));
        $dlr_code           = trim(input('get.dlr_name'));
        $pay_order_code     = trim(input('get.pay_order_code'));
        $commodity_name     = trim(input('get.commodity_name'));
        $order_status       = input('get.order_status');
        $created_date       = urldecode(input('get.created_date', date("Y-m-d", (time() - 86400 * 2)) . ' ~ ' . date("Y-m-d")));
        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $logistics_mode     = input('get.logistics_mode');
        $order_source       = input('get.order_source');
        $set_type           = input('get.set_type');
        $name               = trim(input('get.name'));
        $phone              = trim(input('get.phone'));
//        $sales_source           = input('get.sales_source');
        $channel      = input('get.channel') ?? $set_channel;
        $is_down      = input('get.is_down');//是否下载
        $is_ajax_down = input('get.is_ajax_down');//是否异步下载
        $pay_time     = urldecode(input('get.pay_time'));
        $vin          = input('get.vin');
        $is_jd        = input('get.js_jd', '');
        $user_id      = input('get.user_id', '');
        $is_cc_ok     = input('get.is_cc_ok');
        $parent_order_code     = input('get.parent_order_code');
        $third_order_id     = input('get.third_order_id');
        $invoiceTime   = input('get.invoice_time');
        $invoiceStatus = input('get.invoice_status');

//        $brand = input('brand', 1);
//        $where['a.brand'] = $brand;
        $query = [
            'dlr_name'           => $dlr_code,
            'commodity_name'     => $commodity_name,
            'order_status_array' => [],
            'order_status'       => $order_status,
            //            'created_date'   =>'',
            // 'comm_parent_type_id'=>$comm_parent_type_id,
            'comm_parent_id'     => $comm_parent_id,
            'logistics_mode'     => $logistics_mode,
            'order_code'         => $order_code,
            'pay_order_code'     => $pay_order_code,
            'order_source'       => $order_source,
            'set_type'           => $set_type,
            'name'               => $name,
            'phone'              => $phone,
            //            'sales_source'=>$sales_source,
            'channel_array'      => explode(',', $channel),
            'channel'            => $channel,
            'is_down'            => $is_down,
            'pay_time'           => $pay_time,
            'created_date'       => $created_date,
            'vin'                => $vin,
            'is_jd'              => $is_jd,
            'user_id'            => $user_id,
            'is_cc_ok'           => $is_cc_ok,
            'parent_order_code'  => $parent_order_code,
            'third_order_id'     => $third_order_id,
            'invoice_time'       => $invoiceTime,
            'invoice_status'     => $invoiceStatus,
            'order_source_array' => [],
        ];

        if (!empty($is_cc_ok)) {
            if ($is_cc_ok == 10) {
                $where['a.is_cc_ok'] = 0;
            } else {
                $where['a.is_cc_ok'] = $is_cc_ok;
            }
        }

        if (empty($query['sales_source'])) {
            $query['sales_source'] = 0;
        }


        if (!empty($dlr_code)) {
            $where['a.dd_dlr_code'] = $dlr_code;

        }
        if (!empty($vin)) {
            $where['a.vin'] = $vin;

        }
        if (!empty($is_jd)) {
            $where['b.supplier'] = $is_jd;

        }

        if (!empty($commodity_name)) {
//            $where['b.commodity_name'] = ['like', "%{$commodity_name}%"];
            $where['b.commodity_name'] = $commodity_name;
        }
        $comm_type_list = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }

        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['in', $order_code];
        }
        if (!empty($parent_order_code)) {
            $where['a.parent_order_code'] = ['in', $parent_order_code];
        }

        if (!empty($three_comm_type_id)) {
            $where['c.comm_type_id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column        = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);
                $where['c.comm_type_id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column         = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column       = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $where['c.comm_type_id'] = ['in', $three_type_column];
                }
            }
        }
        if (!empty($order_status)) {
            $where['a.order_status'] = ['in', explode(",", input('get.order_status'))];
            $query['order_status_array'] = explode(",", input('get.order_status'));
        }

        if (!empty($logistics_mode)) {
            $where['a.logistics_mode'] = $logistics_mode;
        }
        if (!empty($pay_order_code)) {
            $where[] = ['exp', sprintf("cashier_trade_no2='%s' || cashier_trade_no='%s'", $pay_order_code, $pay_order_code)];
//            $where['a.pay_order_code'] = ['like', '%' . $pay_order_code . '%'];
        }


        if (!empty($name)) {
//            $where['a.name'] = ['like', "%$name%"];
            $where['a.name'] = $name;
        }
        if (!empty($phone)) {
            $where['a.phone'] = $phone;//模糊查询效率太低了
//            $where['a.phone'] = ['like', "%$phone%"];
        }
        if ($this->admin_info['type'] == 2) {
            $where['a.dd_dlr_code'] = $this->admin_info['dlr_code'];
        }
        if (!empty($created_date)) {
            list($created_date_start, $created_date_end) = explode(' ~ ', $created_date);
            $where['a.created_date'] = ['between', [date('Y-m-d H:i:s', strtotime($created_date_start)), date('Y-m-d H:i:s', strtotime($created_date_end) + 86400 - 1)]];

        }
        if (!empty($pay_time)) {
            list($pay_time_start, $pay_time_end) = explode(' ~ ', $pay_time);
//            $where['i.created_date|a.pay_time'] = ['between', [date('Y-m-d H:i:s', strtotime($pay_time_start)), date('Y-m-d H:i:s', strtotime($pay_time_end) + 86400)]];//后续再改成a.pay_time
            $where['a.pay_time'] = ['between', [date('Y-m-d H:i:s', strtotime($pay_time_start)), date('Y-m-d H:i:s',
                strtotime($pay_time_end) + 86400 - 1)]];
        }

//        if(!empty($sales_source)){
//            $where['b.sales_source'] = $sales_source;
//        }
        if (!empty($channel)) {
//            $where['a.channel'] = $channel;
            $where['a.channel'] = ['in', explode(",", $channel)];
        }


        if (!empty($set_type)) {
            if ($is_down == 1) {
                $where['h.type'] = $set_type;

            } else {
                $where['f.type'] = $set_type;

            }
        }
        if (!empty($user_id)) {
            $where['a.user_id'] = $user_id;
        }

        if (!empty($invoiceTime)) {
            list($invoiceTimeStart, $invoiceTimeEnd) = explode(' ~ ', $invoiceTime);
            $where['invoice.invoice_time'] = ['between', [$invoiceTimeStart . " 00:00:00", $invoiceTimeEnd . " 23:59:59"]];
        }

        if (!empty($invoiceStatus) ) {
            if($invoiceStatus == 9999){
                $where['a.dd_dlr_code'] = ['<>', ""];
            }else{
                $where['invoice.invoice_status'] = ['in', $invoiceStatus];
            }
        }

       // dd($where);
        if (!empty($order_source)) {
            $where['a.order_source'] = ['in', explode(",", $order_source)];
            $query['order_source_array'] = explode(",", $order_source);
        } else {
            if (empty($where)) {
                $where['a.created_date|a.pay_time'] = ['between', [date('Y-m-d') . " 00:00:00", date('Y-m-d') . " 23:59:59"]];
            }
            $where['a.order_source'] = ['neq', 5];
        }
        $where['b.mo_id'] = 0;

        // 渠道
        $channel_list = $buOrder::$channel;
        if (empty(input('get.'))) {
            unset($where);
            $where['a.id'] = 0;
        }
        if(!empty($third_order_id)){
            $where['b.third_order_id'] = $third_order_id;
        }

        $invoiceStatusArr = [
            1001=>'已申请',
            1004=>'已开票',
            1007=>'开票失败',
            1011=>'换开待审核',
            1014=>'冲红中',
            1017=>'已冲红',
            1021=>'冲红失败',
            101=>'待审核',
            107=>'开票驳回',
            9999=>'无需开票',
        ];
        if($invoiceStatus == 9999){
            $invoice_status_str = "9999 as invoice_status";
        }else{
           $invoice_status_str = "invoice.invoice_status";
        }
        $params = [
            'where' => $where,
            'query' => $query,
            'order' => 'a.id desc',
            #'field' => "a.id,a.payment_method,a.parent_order_code,a.user_id,a.gift_score,a.refund_money,a
            #.shop_integral,a.payment_method,a.order_code,a.channel,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,a.logistics_mode,a.order_source,a.cashier_trade_no2,a.cashier_trade_no,a.front_money,a.front_pay_time,a.pre_use_money,b.car_info,b.commodity_name,b.pre_sale_id,b.actual_price,e.dlr_code,e.dlr_name,(case f.type when 1 then '平台' when 2 then '专营店' when 3 then '官微' end) as type,(case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' when 4 then '积分商城'  end) as sales_source,a.receipt_address,if(a.pay_time>0,a.pay_time,i.created_date) as pay_time,a.mail_price,k.money hmoney,k.preferential_money,a.is_cc_ok,d.price old_price,b.b_act_price,b.count,b.all_dis,b.price,b.count,b.price*b.count as total_price,b.id bid,card_money,b.mail_price goods_mail_price,a.pay_order_code2,a.cashier_trade_no,a.cashier_trade_no2,b.third_sku_code,b.third_order_id,b.tax_code,b.tax,b.supplier,b.cost_price,b.third_mail_price",
            'field' => "a.id,a.payment_method,a.parent_order_code,a.user_id,a.gift_score,a.refund_money,a.shop_integral,a.payment_method,a.order_code,a.channel,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,a.order_vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,a.logistics_mode,a.order_source,a.cashier_trade_no2,a.cashier_trade_no,a.front_money,a.front_pay_time,a.pre_use_money,b.car_info,b.commodity_name,b.pre_sale_id,b.actual_price,e.dlr_code,e.dlr_name,(case f.type when 1 then '平台' when 2 then '专营店' when 3 then '官微' end) as type,(case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' when 4 then '积分商城'  end) as sales_source,a.receipt_address,a.pay_time,a.mail_price,k.money hmoney,k.preferential_money,k.activity_title full_title,a.is_cc_ok,b.price old_price,b.b_act_price,b.count,b.all_dis,b.price,b.count,b.price*b.count as total_price,b.id bid,card_money,b.mail_price goods_mail_price,a.pay_order_code2,a.cashier_trade_no,a.cashier_trade_no2,b.third_sku_code,b.third_order_id,b.tax_code,b.tax,b.supplier,b.cost_price,b.third_mail_price,a.ic_card_no,b.full_id,b.full_dis_money,b.limit_dis_money
                        ,a.ms_order_code,a.ms_order_code2,b.mo_sub_id,b.work_time_dis
                        ,(a.b_act_goods_price+a.b_work_time_price+a.mail_price) as total_order_price,a.pre_point
                        ,(a.money+a.pre_use_money) as pay_money
                        ,(a.integral+a.pre_point) as total_integral,b.first_order_price,b.gift_act_id,b.is_gift,g.title as gift_title,se.title as seckill_name,yd.point as yd_point
                        ,b.waybill_number,a.common_carrier,a.delivery_time,b.order_commodity_status,b.id order_commodity_id
                        ,ps.title as presale,b.qsc_num,a.current_points,b.source_special,j.mid_phone
                         ,{$invoice_status_str},invoice.invoice_time 
                        ",//订单表：integral+pre_point

    //        'field' => "a.id,a.payment_method,a.parent_order_code,a.user_id,a.gift_score,a.refund_money,a.shop_integral,a.payment_method,a.order_code,a.channel,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,a.logistics_mode,a.order_source,a.cashier_trade_no2,a.cashier_trade_no,a.front_money,a.front_pay_time,a.pre_use_money,b.car_info,b.commodity_name,b.pre_sale_id,b.actual_price,e.dlr_code,e.dlr_name,(case f.type when 1 then '平台' when 2 then '专营店' when 3 then '官微' end) as type,(case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' when 4 then '积分商城'  end) as sales_source,a.receipt_address,a.pay_time,a.mail_price,k.money hmoney,k.preferential_money,k.activity_title full_title,a.is_cc_ok,d.price old_price,b.b_act_price,b.count,b.all_dis,b.price,b.count,b.price*b.count as total_price,b.id bid,card_money,b.mail_price goods_mail_price,a.pay_order_code2,a.cashier_trade_no,a.cashier_trade_no2,b.third_sku_code,b.third_order_id,b.tax_code,b.tax,b.supplier,b.cost_price,b.third_mail_price,a.ic_card_no",

            'group' => 'b.id'  //2020-12-21 10:12:23 用id才能显示同时买多个商品的
        ];
        //,m.emp_name,m.mobile,m.emp_no  2020-09-15 10:15:23 添加积分商城员工信息  产品lty
//        print_json(1,'',$params);

        $dbOrderInvoice = new DbOrderInvoice();
        //判断是否是异步下载,是的话,传入队列
        if ($is_ajax_down == 1) {
            //异步下载进行下单时间+支付时间的传参判断
            if (empty($created_date) && empty($pay_time)) {
                print_json(1, '下单/支付时间必选其一');
            }

            $days = 0;

            $time_field = "a.created_date";
            if (!empty($created_date)) {
                list($created_date_start, $created_date_end) = explode(' ~ ', $created_date);
                $days = date_diff(date_create($created_date_start), date_create($created_date_end))->days;
            } else if (!empty($pay_time)) {
                list($pay_time_start, $pay_time_end) = explode(' ~ ', $pay_time);
                $days         = date_diff(date_create($pay_time_start), date_create($pay_time_end))->days;
                $time_field   = "a.pay_time";
                $created_date = $pay_time;
            }

            if ($days > 31) {
                print_json(1, '下单/支付时间范围请小于1个月');
            }

//            if (!getRedisLock('order_is_ajax_down', 300)) {
//                print_json(1, '限制5分钟一次');
//            }

//            $params['field'] = "a.payment_method,b.full_id,b.full_dis_money,a.parent_order_code,a.user_id, channel,a.gift_score,a.refund_money,
//            a.shop_integral,c.comm_type_id,c.is_pure,a.id,a.payment_method,a.cashier_trade_no,a.front_money,a.pre_use_money,
//            a.front_pay_time,a.cashier_trade_no2,a.order_code,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,
//            a.order_vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,
//            a.logistics_mode,a.order_source,f.dlr_code,f.dlr_name,b.spr_id,b.b_act_price,b.count,b.all_dis,b.card_all_dis,
//            b.pre_sale_id,b.commodity_pic,b.commodity_name,b.car_info,b.sku_info,b.price,b.count,b.price*b.count as total_price,
//            b.actual_price,b.actual_point,b.actual_use_money,c.commodity_code,b.third_sku_code sku_code,(case h.type when 1 then '平台' when 2 then '专营店' when 3 then '官微销售' end) as type,
//            (case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' end) as sales_source,
//            a.receipt_address,a.pay_time,a.mail_price,a.is_cc_ok,d.price old_price,a.delivery_time,b.limit_id,b.n_dis_id,
//            card_money,b.mail_price goods_mail_price,a.pay_order_code2,a.cashier_trade_no,a.cashier_trade_no2,b.third_sku_code,
//            b.third_order_id,b.tax_code,b.tax,b.supplier,b.cost_price,b.third_mail_price,a.ic_card_no,a.ms_order_code,a.ms_order_code2,
//            b.mo_sub_id,b.work_time_json,b.act_sett_money,b.card_sett_money,b.work_time_money,b.work_time_actual_money,
//            b.work_time_dis,b.mo_id,b.first_order_price,b.gift_act_id,b.is_gift,g.title as gift_title,se.title as seckill_name,
//            yd.point as yd_point,source,b.common_carrier,b.waybill_number,b.delivery_time,b.order_commodity_status,
//            b.id order_commodity_id,b.suit_id,a.pre_point,ps.title as presale"
//            ;
//            if ($order_source == 6) {
//                $params['field'] .= ",m.mobile ca_phone,m.emp_no,m.emp_name,o.service_charge,(case o.uses when 1 then '自己用' when 2 then '转赠' end) as uses";
//            }
//            $params['field'] .=",point.point_order_code,b.commodity_id,b.give_integral,a.card_degree_code,b.commodity_segment_dis_money,b.qsc_num";
            $params_key = md5(json_encode($params));
            //查询如果过滤数据存在,就直接跳转到对应的数据列表

//            $exist = DbExports::where('export_key', $params_key)->find();
            $exist = false;
            if (!empty($exist)) {
                //直接跳转到已存在的数据列表
            } else {
                $export = DbExports::create([
                    'export_type'   => 'order',
                    'filter_params' => json_encode($params['where']),
                    'export_key'    => $params_key,
                    'creator'       => $this->admin_info['username']
                ]);

                Queue::push('app\admin_v2\queue\OrderExport', json_encode([
                    'params'       => $params,
                    'order_source' => $order_source,
                    'created_date' => $created_date,
                    'time_field'   => $time_field,
                    'id'           => $export->id,
                    'brand'        => 1, // 日产
                ]), config('queue_type.export'));
                print_json(0, '已加入异步下载列表');
            }
        }


        if ($is_down == 1) {

//            if (!getRedisLock('order_static_index_down_csv', 30)) {
//                die("请稍后再导出!");
//            }

            ini_set('memory_limit', '2200M');


            $params['field'] = "a.id,a.payment_method,a.parent_order_code,a.user_id, channel,a.gift_score,a.refund_money,a.shop_integral,t1.comm_type_name,t2.comm_type_name as parent_comm_type_name,t3.comm_type_name as three_comm_type_name,c.is_pure,a.id,a.payment_method,a.cashier_trade_no,a.front_money,a.pre_use_money,a.front_pay_time,a.cashier_trade_no2,a.order_code,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,a.order_vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,a.logistics_mode,a.order_source,f.dlr_code,f.dlr_name,b.b_act_price,b.count,b.all_dis,b.card_all_dis,b.pre_sale_id,b.commodity_pic,b.commodity_name,b.car_info,b.sku_info,b.price,b.count,b.price*b.count as total_price,b.actual_price,b.actual_point,b.actual_use_money,c.commodity_code,b.third_sku_code sku_code,(case h.type when 1 then '平台' when 2 then '专营店' when 3 then '官微销售' end) as type,(case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' end) as sales_source,a.receipt_address,a.pay_time,a.mail_price,k.money hmoney,k.preferential_money,k.activity_title full_title,a.is_cc_ok,b.price as old_price,b.limit_id,b.n_dis_id,card_money,n.rule_info,ps.title as presale,b.mail_price goods_mail_price,a.pay_order_code2,a.cashier_trade_no,a.cashier_trade_no2,b.third_sku_code,b.third_order_id,b.tax_code,b.tax,b.supplier,b.cost_price,b.third_mail_price,a.ic_card_no,b.full_id,b.full_dis_money,b.gift_act_id,b.is_gift,g.title as gift_title,se.title as seckill_name";

            $params['field'] .= ',a.ms_order_code,a.ms_order_code2,b.mo_sub_id,a.pre_point,b.work_time_dis,b.work_time_json,b.work_time_money,b.act_sett_money,b.card_sett_money,b.work_time_actual_money,b.mo_id
                        ,(a.b_act_goods_price+a.b_work_time_price+a.mail_price) as total_order_price
                        ,(a.money+a.pre_use_money) as pay_money
                        ,(a.integral+a.pre_point) as total_integral
                        ,point.point_order_code,b.first_order_price,yd.point as yd_point
                        ,b.common_carrier,b.waybill_number,b.delivery_time,b.order_commodity_status,b.id order_commodity_id
                        ,a.card_degree_code,b.commodity_segment_dis_money,b.qsc_num,c.comm_type_id,b.commodity_id,b.crowd_id,a.current_points,b.source_special
                        ,b.card_sett_rule,b.act_sett_rule,b.card_sett_standard,b.act_sett_standard
                         ,invoice.invoice_status,invoice.invoice_time
                        ';
            $params['field'] .= ',b.limit_dis_money,a.delivery_time as verification_time';
            // 多选不处理
            if ($order_source == 6) {
                $params['order_s_q'] = 1;
                $params['field']     .= ",m.emp_name,m.mobile ca_phone,m.emp_no,o.service_charge,(case o.uses when 1 then '自己用' when 2 then '转赠' end) as uses";
            }
            //$list     = $buOrder->getOrderLimit($params, false);
            $list = $buOrder->getOrdersm($params, false);

            // 批量收集活动ID，避免循环内重复查询
            $limit_id = [];
            $n_dis_id = [];
            $crowd_id = [];
            $source_special = [];

            foreach ($list as $item) {
                if (!empty($item['limit_id']) && $item['limit_dis_money'] != '0.00') {
                    $limit_id[] = $item['limit_id'];
                }
                if (!empty($item['n_dis_id'])) {
                    $n_dis_id[] = $item['n_dis_id'];
                }
                if (!empty($item['crowd_id'])) {
                    $crowd_id[] = $item['crowd_id'];
                }
                if (!empty($item['source_special'])) {
                    $source_special[] = $item['source_special'];
                }
            }

            // 批量查询限时优惠
            $limit = [];
            if ($limit_id) {
                $limit_model = new DbLimitDiscount();
                $limit = $limit_model->whereIn('id', array_unique($limit_id))->column('title', 'id');
            }

            // 批量查询N件N折活动
            $n_dis_arr = [];
            if ($n_dis_id) {
                $n_dis_model = new DbNDiscount();
                $n_dis_list = $n_dis_model->whereIn('id', array_unique($n_dis_id))->select();
                foreach ($n_dis_list as $n_dis_item) {
                    $n_dis_arr[$n_dis_item['id']] = "N件N折-" . $n_dis_item['title'];
                }
            }

            // 批量查询众筹活动
            $crowd_title = [];
            if ($crowd_id) {
                $crowdfund_model = new DbCrowdfund();
                $crowd_title = $crowdfund_model->whereIn('id', array_unique($crowd_id))->column('title', 'id');
            }

            // 批量查询工会手机号
            $trade_list = [];
            if ($source_special) {
                $trade_model = new AcHaveTradeList();
                $trade_list = $trade_model->whereIn('sp_id', array_unique($source_special))
                    ->where('is_enable', 1)
                    ->field('id,phone,sp_id,user_id')
                    ->select();
            }

            // 遍历订单列表，填充活动信息，避免循环内数据库查询
            foreach ($list as &$item) {
                $item['limit'] = $limit[$item['limit_id']] ?? '';
                $item['n_dis'] = $n_dis_arr[$item['n_dis_id']] ?? '';
                $item['crowd_title'] = $crowd_title[$item['crowd_id']] ?? '';
                $item['trade_phone'] = '';
                if (!empty($item['source_special'])) {
                    foreach ($trade_list as $trade) {
                        if ($trade['sp_id'] == $item['source_special'] && $trade['user_id'] == $item['user_id']) {
                            $item['trade_phone'] = $trade['phone'];
                            break;
                        }
                    }
                }
            }
            //ai in end

           // ,invoice.invoice_status,invoice.invoice_time
            $limit_id     = [];
            $n_dis_id     = [];
            $n_dis_id_arr = [];
            $limit_id_arr = [];
            $user_level_arr = [];
            $limit        = [];
            $source_special = []; // 工会专题id
            $trade_list = [];

            $crowd_id      = [];
            $crowd_id_arr      = []; // 众筹活动
            $crowd_title = [];





            $map = ['is_enable'=>1,'value_type'=>25, 'parent_value_code'=>'N'];
            if ($set_channel = '6,7') {
                $map['parent_value_code'] = 'V';
            }
            $time = number_format(microtime(true) - $api_start_at, 10, '.', '');
            Logger::error('orderstatictime::'.$time);
            $user_level = DbSystemValue::where($map)->field('value_code,county_name')->order('order_no')->select();
            foreach ($user_level as $key => $val) {
                $user_level_arr[$val['value_code']] = $val['county_name'];
            }
            if (input('test') == 1) {
                echo $buOrder->getLastSql();
                die();
            }
            if ($list) {

                $home_type = new DbHomeTypeCommodity();
                foreach ($list as $k=>$vvv){
                    if ($vvv['n_dis_id'] && !in_array($vvv['n_dis_id'], $n_dis_id_arr)) {
                        $n_dis_id[]     = $vvv['n_dis_id'];
                        $n_dis_id_arr[] = $vvv['n_dis_id'];
                    }
                    if ($vvv['limit_id'] && !in_array($vvv['limit_id'], $limit_id_arr) && $vvv['limit_dis_money'] != '0.00') {
                        $limit_id[]     = $vvv['limit_id'];
                        $limit_id_arr[] = $vvv['limit_id'];
                    }
                    if (!empty($vvv['crowd_id']) && !in_array($vvv['crowd_id'], $crowd_id_arr)) {
                        $crowd_id[] = $vvv['crowd_id'];
                        $crowd_id_arr[] = $vvv['crowd_id'];
                    }

                    // 工会手机号
                    if (!empty($vvv['source_special'])) {
                        $source_special[] = $vvv['source_special'];
                    }





                }

                if($limit_id){
                    $limit_model = new DbLimitDiscount();
                    $limit       = $limit_model->whereIn('id', $limit_id)->column('title', 'id');
                }

                if ($n_dis_id) {
                    $n_dis_model = new DbNDiscount();
                    $n_dis_list  = $n_dis_model->whereIn('id',$n_dis_id)->select();
                    if ($n_dis_list) {
                        foreach ($n_dis_list as $nnn) {
                            $n_dis_arr[$nnn['id']] = "N件N折-" . $nnn['title'];
                        }
                    }
                }

                if (!empty($crowd_id)) {
                    $crowdfund_model = new DbCrowdfund();
                    $crowd_title = $crowdfund_model->whereIn('id', $crowd_id)->column('title','id');
                }

                // 工会手机号
                if (!empty($source_special)) {
                    $trade_model = new AcHaveTradeList();
                    $map = [
                        'sp_id' => ['in',array_unique($source_special)],
                        'is_enable' => 1,
                    ];
                    $field = 'id,phone,sp_id,user_id';
                    $trade_list = $trade_model->where($map)->field($field)->select();
                }







                foreach ($list as $kkk => $vvv) {
                    $list[$kkk]['limit'] = '';
                    $list[$kkk]['n_dis'] = '';
                    $list[$kkk]['crowd_title'] = '';
                    $list[$kkk]['trade_phone'] = '';


                    if ($vvv['full_title']) {

//                $list[$key]['mj_info'] = sprintf("满%s-%s", $vvv['hmoney'], $vvv['preferential_money']);

                        $list[$kkk]['mj_info'] = '满减活动-'.$vvv['full_title'];
                    } else {
                        $list[$kkk]['mj_info'] = '';
                    }
                    $list[$kkk]['is_gift_name'] = '';
                    if($vvv['gift_act_id'] != 0){
                        if($vvv['is_gift'] == 0){
                            $list[$kkk]['is_gift_name'] = '否';
                        }elseif ($vvv['is_gift'] == 1){
                            $list[$kkk]['is_gift_name'] = '是';
                        }
                    }
                    $list[$kkk]['user_level_price'] = $vvv['price'] - $vvv['commodity_segment_dis_money'];
                    $list[$kkk]['card_degree_code'] = $user_level_arr[$vvv['card_degree_code']] ?? '';


                    // 判断众筹活动
                    if (!empty($vvv['crowd_id'])) {
                        $list[$kkk]['crowd_title'] = $crowd_title[$vvv['crowd_id']] ?? '';
                    }

                    // 工会手机号
                    if(!empty($vvv['source_special'])) {
                        foreach ($trade_list as $datum) {
                            if ($datum['sp_id'] == $vvv['source_special'] && $vvv['user_id'] == $datum['user_id']) {
                                $list[$kkk]['trade_phone'] = $datum['phone'];
                            }
                        }
                    }

                    // 限时优惠
                    if ($vvv['limit_id'] && $vvv['limit_dis_money'] != '0.00') {
                        $list[$kkk]['limit'] = $limit[$vvv['limit_id']] ?? "活动未明";
                    }

                    // N件N折
                    if (!empty($vvv['n_dis_id'])) {
                        $list[$kkk]['n_dis'] = $n_dis_arr[$vvv['n_dis_id']] ?? "活动未明";
                    }







                    // 前台分类
                    $web_home_type_key = 'tmp_que_export_type_key_' . $vvv['comm_type_id'] . '_' . $vvv['commodity_id'];
                    $web_home_type     = redis($web_home_type_key);
                    //暂时去掉
//                    if (empty($web_home_type)) {
//                        $type_params['where']['a.old_comm_type_id'] = $vvv['comm_type_id'];
//                        $type_params['where_or']['a.comm_id']       = $vvv['commodity_id'];
//                        $type_params['field']                       = "b.comm_type_name as pparent_type_name,c.comm_type_name as parent_type_name,d.comm_type_name";
//                        $type_params['group']                       = 'a.new_comm_type_id';
//                        $home_type_list                             = $home_type->getHomeType($type_params);
//                        $comm_type_name                             = [];
//                        $parent_type_name                           = [];
//                        $pparent_type_name                          = [];
//                        foreach ($home_type_list as $item) {
//                            $comm_type_name[]    = $item['comm_type_name'];
//                            $parent_type_name[]  = $item['parent_type_name'];
//                            $pparent_type_name[] = $item['pparent_type_name'];
//                        }
//                        $web_home_type = [
//                            'comm_type_name'    => implode(',', $comm_type_name),
//                            'parent_type_name'  => implode(',', $parent_type_name),
//                            'pparent_type_name' => implode(',', $pparent_type_name),
//                        ];
//                        redis($web_home_type_key, $web_home_type, 1000);
//                    }
                    if (empty($web_home_type)) {
                        $list[$kkk]['web_comm_type_name']        = '';
                        $list[$kkk]['web_parent_comm_type_name'] = '';
                        $list[$kkk]['web_top_comm_type_name']    = '';
                    } else {
                        $list[$kkk]['web_comm_type_name']        = $web_home_type['comm_type_name'];
                        $list[$kkk]['web_parent_comm_type_name'] = $web_home_type['parent_type_name'];
                        $list[$kkk]['web_top_comm_type_name']    = $web_home_type['pparent_type_name'];
                    }
                }

            }
            $time = number_format(microtime(true) - $api_start_at, 10, '.', '');
            Logger::error('orderstatictime2:'.$time);

        } else {
            // 多选不处理
            if ($order_source == 6) {
                $params['order_s_q'] = 1;
                $params['field']     .= ",m.emp_name,m.mobile ca_phone,m.emp_no";
            }
           
            $params['group'] = 'b.order_code';
            $list = $buOrder->getOrderPaginate($params);

            if (input('test') == 1) {
                echo $buOrder->getLastSql();
                exit;
            }

        }
        $order_source_list = BuOrder::orderSource();

        unset($order_source_list['5']);



        $order_codes = [];
        $sett_arr = [];

        $card_id_arr = [];
        $card_list = [];
        $act_ids = [];
        $act_user_id = [];
        $card_receive_arr = [];
        foreach ($list as $key => $val) {

            // 正常订单 此功能已没用
            /*if ($val['pre_sale_id'] == 0) {
                $orderSettlements                    = $val->orderSettlements;
                $cashier_settlement_no               = array_column($orderSettlements, 'cashier_settlement_no');
                $list[$key]['cashier_settlement_no'] = implode(',', $cashier_settlement_no);
            } else {
                // 预售订单
                if (!empty($val['cashier_trade_no2'])) {
                    $orderSettlement2                     = $val->orderSettlementByTradeNo2;
                    $list[$key]['cashier_settlement_no2'] = $orderSettlement2['cashier_settlement_no'] ?? '';
                } else {
                    $list[$key]['cashier_settlement_no2'] = '';
                }

                // 尾款
                if (!empty($val['cashier_trade_no'])) {
                    $orderSettlement                     = $val->orderSettlementByTradeNo;
                    $list[$key]['cashier_settlement_no'] = $orderSettlement['cashier_settlement_no'] ?? '';
                } else {
                    $list[$key]['cashier_settlement_no'] = '';
                }

            }*/
            $list[$key]['cashier_settlement_no'] = '';
            $list[$key]['cashier_settlement_no2'] = '';
            $list[$key]['card_name'] = '';
            $list[$key]['card_status'] = '';


            // 使用卡券
            if (!empty($val['card_id'])) {
                $cardIdArr = explode(',', $val['card_id']);
                $card_id_arr = array_merge($card_id_arr, $cardIdArr);
            }

            // 取送车券
            if (in_array($val['order_status'], [2, 7]) && in_array($val['order_source'], [24])){
                $act_ids[] = $val['id'];
                $act_user_id[] = $val['user_id'];
            }



            // 是否首单立减
            $is_first_order = '否';
            if ($val['first_order_price'] != 0) {
                $is_first_order = '是';
            }
            $list[$key]['is_first_order']              = $is_first_order;
            $list[$key]['dlr']                         = $val['dlr_code'] . ' - ' . $val['dlr_name'];
            $list[$key]['order_status_name']           = $buOrder::orderStatus($val['order_status']);
            $list[$key]['order_commodity_status_name'] = BuOrderCommodity::$order_commodity_status[$val['order_commodity_status']] ?? $list[$key]['order_status_name'];
            $list[$key]['logistics_mode_name']         = $val['logistics_mode'] == 1 ? '自提' : '快递';
            $list[$key]['order_source']                = $order_source_list[$val['order_source']] ?? '';
            $list[$key]['is_pure']                     = empty($val['is_pure']) ? '否' : '是';
//            $list[$key]['yh_money']            = $val['old_price'] - $val['price'];//优惠金额

            //组合商品子ID，主ID为mo_id非组合商品为0
            $workTimeDis =  ($val['mo_sub_id'] != 0) ? $val['work_time_dis'] : ($val['work_time_dis'] * $val['count']);
            $list[$key]['yh_total_money'] = $val['all_dis'] - $workTimeDis;

            if (in_array($val['order_status'], [1, 3, 5, 6, 8])) {
                $list[$key]['money'] = '0.00';
            }





            if ($val['payment_method'] == 1) {
                $list[$key]['payment_method'] = '现金';
            } elseif ($val['payment_method'] == 2) {
                $list[$key]['payment_method'] = '积分';
            } elseif ($val['payment_method'] == 3) {
                $list[$key]['payment_method'] = '卡劵';
            } elseif ($val['payment_method'] == 4) {
                $list[$key]['payment_method'] = '现金+积分';
            } elseif ($val['payment_method'] == 5) {
                $list[$key]['payment_method'] = '现金+优惠券';
            } elseif ($val['payment_method'] == 6) {
                $list[$key]['payment_method'] = '积分+优惠券';
            } elseif ($val['payment_method'] == 7) {
                $list[$key]['payment_method'] = '现金+积分+卡劵';
            }
            if ($val['full_title']) {

//                $list[$key]['mj_info'] = sprintf("满%s-%s", $val['hmoney'], $val['preferential_money']);

                $list[$key]['mj_info'] = '满减活动-'.$val['full_title'];
            } else {
                $list[$key]['mj_info'] = '';
            }
            $list[$key]['is_gift_name'] = '';
            if($val['gift_act_id'] != 0){
                if($val['is_gift'] == 0){
                    $list[$key]['is_gift_name'] = '否';
                }elseif ($val['is_gift'] == 1){
                    $list[$key]['is_gift_name'] = '是';
                }
            }

            if ($val['order_status'] == 2 && $val['logistics_mode'] == 1) {
                $list[$key]['dq_time'] = date('Y-m-d H:i:s', strtotime(sprintf("%s +1 month", $val['pay_time'])));
            } else {
                $list[$key]['dq_time'] = '';
            }

            // 分销规则
            if (isset($val['rule_info']) && !is_null($val['rule_info'])) {
                $rule_info               = json_decode($val['rule_info'], true);
                $list[$key]['rule_info'] = "平台:" . $rule_info['platform'] . '%' . ',' . "经销商:" . $rule_info['dlr'] . '%';
            } else {
                $list[$key]['rule_info'] = "";
            }

            $orderCommodities = $val->orderCommodities;

            $order_total_money = 0;  // 订单总价
            $order_all_dis     = 0; // 订单活动总优惠金额
            foreach ($orderCommodities as $item) {
                // 订单总价 = 订单商品原价 * 数量
                if ($item['mo_id'] > 0) continue;
                $order_total_money += $item['b_act_price'] * $item['count'];
                $order_all_dis     += $item['all_dis'];
                /**** 旧订单商品状态处理-begin ****/
                if ($item['mo_id'] == 0 && empty($item['order_commodity_status'])){
                    $order_commodity_status = $order_commodity_model->orderToCommodityStatus($val['order_status']);
                    $update_data = ['order_commodity_status' => $order_commodity_status, 'common_carrier' => $val['common_carrier'], 'waybill_number' => $val['waybill_number'], 'delivery_time' => $val['delivery_time']];
                    if ($val['order_status'] == 4){
                        $update_data['operate_list'] = json_encode_cn($update_data);
                    }
                    if (!empty($order_commodity_status)) {
                        $order_commodity_model->saveData($update_data, ['id' => $item['id']]);
                        if ($val['order_commodity_id'] == $item['id']) $val['order_commodity_status'] = $update_data['order_commodity_status'];
                    }
                }
                /**** 旧订单商品状态处理-end ****/

                // 结算
                $sett_arr[$item['order_code']][] = $item;
            }

            $list[$key]['order_total_money'] = $order_total_money;
            $list[$key]['order_all_dis']     = $order_all_dis;

            // 订单金额 =订单总价 + 订单运费 - 订单卡券总优惠金额 - 订单活动总优惠金额
//            $list[$key]['order_money'] = $order_total_money
//                + $val['mail_price']
//                - $val['card_money']
//                - $order_all_dis;

            //订单表：money + integral/10 + pre_point/10 + pre_use_money
            $list[$key]['order_money'] = $val['money'] + $val['integral']/10 + $val['pre_point']/10 + $val['pre_use_money'];
            $list[$key]['pre_order_money'] = $val['pre_point']/10 + $val['pre_use_money'];

            $list[$key]['order_money'] = round($list[$key]['order_money'], 2);
            $list[$key]['pre_order_money'] = round($list[$key]['pre_order_money'], 2);


            $order_codes[] = $val['order_code'];
        }

        // 卡券
        if (!empty($card_id_arr)) {
            $c_model           = new DbCard();
            $card_list       = $c_model->where(['id' => ['in', $card_id_arr]])->column('card_name,card_quota', 'id');
        }

        // 取送车券
        if (!empty($act_ids)){
            $card_r_r = new BuCardReceiveRecord();
            $act_map = ['act_id' => ['in', $act_ids], 'user_id' => ['in', $act_user_id]];
            $field = 'act_id,status';
            $card_receive_list = $card_r_r->where($act_map)->field($field)->select();
            $statusData = BuCardReceiveRecord::STATUS_LIST;
            foreach ($card_receive_list as $key => $item) {
                $card_receive_arr[$item['act_id']]['card_status'] = $statusData[$item['status']] ?? '';
            }
        }

        foreach ($list as $key => $item) {
            $list[$key]['card_name'] = '';
            $list[$key]['card_status'] = '';
            $card_mon                = 0;
            if (!empty($item['card_id'])) {
                $card_named_list = '';
                $cardIds = explode(',', $item['card_id']);
                foreach ($cardIds as $cardId) {
                    $card_name = $card_list[$cardId]['card_name'] ?? '';
                    $card_named_list .= $card_name . ',';
                    $card_quota = $card_list[$cardId]['card_quota'] ?? 0;
                    $card_mon        += $card_quota;
                }

                $list[$key]['card_name'] = rtrim($card_named_list, ',');
            }

            if ($item['is_cc_ok'] == 0) {
                if ($item['card_money'] <> $card_mon && $item['card_money'] > 0) {
//                    $list[$key]['is_cc_ok'] = '卡券使用有点异常';
                } else {
                    $list[$key]['is_cc_ok'] = '正常';
                }
            } elseif ($item['is_cc_ok'] == 1) {
                $list[$key]['is_cc_ok'] = '积分扣除失败';
            } elseif ($item['is_cc_ok'] == 2) {
                $list[$key]['is_cc_ok'] = '卡券核销失败';
            }

            // 取送车券
            if (in_array($item['order_status'], [2, 7]) && in_array($item['order_source'], [24])) {
                $list[$key]['card_status'] = $card_receive_arr[$item['id']] ?? '';
            }
        }




        if ($is_down == 1) {
            $time = number_format(microtime(true) - $api_start_at, 10, '.', '');
            Logger::error('orderstatictime3:'.$time);
            $fields = 'id,order_code,parent_order_code,cashier_trade_no,ms_order_code,cashier_settlement_no,pay_order_code,order_money,order_total_money,pay_money,total_integral,mail_price,card_money,order_all_dis,refund_money,dlr,name,phone,vin,order_vin,license_plate,dlr_integral,shop_integral,card_name,card_code,created_date,pay_time,verification_time,dq_time,order_status_name,card_status,logistics_mode_name,commodity_pic,mo_commodity_name,commodity_name,three_comm_type_name,parent_comm_type_name,comm_type_name,is_pure,car_info,sku_info,commodity_code,sku_code,type,sales_source,channel_source,order_source,receipt_address,gift_score,payment_method,is_cc_ok,old_price,actual_price,count,total_price,yh_total_money,first_order_price,card_all_dis,actual_use_money,actual_point,goods_mail_price,is_work_time,work_time_code,work_time_money,work_time_actual_money,work_time_dis,third_order_id,third_mail_price,supplier,tax_code,tax,cost_price,rule_info,presale,limit,n_dis,mj_info,gift_title,is_gift_name,seckill_name,user_id,point_order_code,is_first_order,yd_point,order_commodity_status_name,common_carrier,waybill_number,delivery_time,web_comm_type_name,web_parent_comm_type_name,web_top_comm_type_name,user_level_price,card_degree_code,qsc_num,crowd_title,current_points,trade_phone,card_sett_rule,act_sett_rule,card_sett_standard,act_sett_standard,order_card_subsidy_money,commodity_card_subsidy_money,order_act_subsidy_money,commodity_act_subsidy_money,order_total_subsidy_money,commodity_total_subsidy_money,invoice_status,invoice_time';
            $titles = '序号,订单编码,总订单号,老支付单号,新支付单号,结算单号,订单商户号,订单金额,订单总价,实付金额,订单实付积分,订单运费,卡券总优惠金额,活动总优惠金额,退款金额,专营店,姓名,手机号码,vin,下单vin,车牌,专营店积分,商城积分,卡券名称,卡劵code,下单时间,支付时间,核销时间,到期时间,订单状态,购买卡券状态,物流方式,商品图片,组合商品名称,商品名称,一级分类,二级分类,三级分类,是否纯正,车辆信息,sku信息,商品编码,规格编码,商品属性,销售来源,渠道来源,订单来源,收货地址,赠送积分,支付方式,积分卡券状态,原价,活动价,数量,总价,活动优惠总金额,商品活动优惠金额,商品卡券优惠金额,商品实付金额,商品实付积分,商品运费,是否线上支付工时,商品工时编码,商品工时原价,商品工时实付金额,商品工时优惠金额,供应商订单号,供应商运费,供应商,税务编码,税率,成本价,分账比例,预售活动,限时优惠,N件N折,满减,买赠活动,是否赠品,秒杀活动,用户ID,扣积分单号,首单立减,移动积分,商品状态,承运公司,运单编号,发货时间,前台一级分类,前台二级分类,前台三级分类,会员价,会员等级,取送车数量,众筹活动,支付前积分余额,工会手机号,卡券结算规则,活动结算规则,卡券结算价格标准值,活动结算价格标准值,订单厂家卡券补贴金额,商品厂家卡券补贴金额,订单厂家活动补贴金额,商品厂家活动补贴金额,订单厂家总补贴金额,商品厂家总补贴金额,开票状态,开票时间';
            // 多选不处理
            if ($order_source == 6) {
                $fields .= ",ca_phone,emp_no,emp_name,service_charge,uses";
                $titles .= ",ca手机号,工号,姓名,服务费,用途";
            }

            $newList = [];

            foreach ($list as $k=> $item) {
                $list[$k]['invoice_status'] = $invoiceStatusArr[$val['invoice_status']] ?? "";
                $item['channel_source'] = $buOrder::$channel[$item['channel']];
                //组合商品子ID，主ID为mo_id非组合商品为0
                if ($item['mo_sub_id'] != 0) {
                    // 组合商品 todo
                    $map = ['order_code'=>$item['order_code'], 'mo_id'=>$item['mo_sub_id']];
                    $zCommodity = $order_commodity_model->where($map)->field('id,order_code,count,commodity_name')->find();
                    $item['count'] = bcmul($zCommodity['count'], $item['count']); // 主商品数量*子商品数量
                    $item['mo_commodity_name'] = $zCommodity['commodity_name'];
                    $item['total_price']       = $item['price'] * $item['count'];
                }else{
                    //商品工时原价
                    $item['work_time_money'] *= $item['count'];
                    //商品工时实付金额
                    $item['work_time_actual_money'] *= $item['count'];
                    $item['work_time_actual_money'] < 0 ? $item['work_time_actual_money'] = 0 : '';

                    //商品工时优惠金额
                    $item['work_time_dis'] *= $item['count'];
                    $item['mo_commodity_name'] = '';

                }




                //工时信息
                $workTimeJson = !empty($item['work_time_json']) ? json_decode($item['work_time_json'], true) : [];
                //商品工时
                $item['work_time_number'] = $workTimeJson['work_time_number'] ?? 0;
                //商品工时编码
                $item['work_time_code'] = $workTimeJson['work_time_code'] ?? 0;
                //是否线上支付工时：1-是，2-否
                $item['is_work_time'] = !empty($item['work_time_number']) ? '是' : '否';
                //商品厂家补贴金额
//                $item['work_time_subsidy_money'] = $item['act_sett_money'] + $item['card_sett_money'];

                // 卡券订单状态拼接卡券使用状态
//                $item['order_status_name'] = $item['order_status_name'] . $item['card_status'];

                // 结算信息
                foreach ($sett_arr as $order_code_index => $datum) {

                    if ($item['order_code'] == $order_code_index) {
                        $act_sett_money_arr = array_column($datum, 'act_sett_money','commodity_id');
                        $card_sett_money_arr = array_column($datum, 'card_sett_money','commodity_id');

                        // 订单厂家卡券补贴金额  card_sett_money 订单下所有商品
                        $order_card_subsidy_money = array_sum($card_sett_money_arr);
                        // 订单厂家活动补贴金额  act_sett_money  订单下所有商品
                        $order_act_subsidy_money = array_sum($act_sett_money_arr);
                        // 商品厂家卡券补贴金额  单个商品  card_sett_money
                        $commodity_card_subsidy_money = $card_sett_money_arr[$item['commodity_id']] ?? 0;
                        // 商品厂家活动补贴金额  单个商品  act_sett_money
                        $commodity_act_subsidy_money = $act_sett_money_arr[$item['commodity_id']] ?? 0;
                        // 订单厂家总补贴金额   订单下所有商品 (card_sett_money + act_sett_money)
                        $order_total_subsidy_money = $order_card_subsidy_money + $order_act_subsidy_money;
                        // 商品厂家总补贴金额  单个商品  (card_sett_money + act_sett_money)
                        $commodity_total_subsidy_money = $commodity_card_subsidy_money + $commodity_act_subsidy_money;


                        $card_sett_rule = [];
                        if (!empty($item['card_sett_rule'])) {
                            $card_sett_rule_arr = json_decode($item['card_sett_rule'], true);
                            foreach ($card_sett_rule_arr as $key => $value) {
                                $str = '';
                                foreach ($value as $k => $v) {
                                    if ($k == 1) {
                                        $str = '固定金额-'.$v.'元';
                                    } else {
                                        $str = '比例结算-'.$v.'%';
                                    }

                                }
                                $card_sett_rule[] = $str;
                            }
                        }

                        $act_sett_rule = [];
                        if (!empty($item['act_sett_rule'])) {
                            $card_sett_rule_arr = json_decode($item['act_sett_rule'], true) ?? [];
                            foreach ($card_sett_rule_arr as $key => $value) {
                                if ($key == 1) {
                                    $str = '固定金额-'.$value.'元';
                                } else {
                                    $str = '比例结算-'.$value.'%';
                                }
                                $act_sett_rule[] = $str;
                            }
                        }
                        $item['card_sett_rule'] = implode(',', $card_sett_rule);  // 卡券结算规则
                        $item['act_sett_rule'] = implode(',', $act_sett_rule);  // 活动结算规则
                        $card_sett_standard_arr = json_decode($item['card_sett_standard'], true) ?? [];
                        $sett_standard = [];
                        foreach ($card_sett_standard_arr as $key => $val) {
                            $sett_standard[] = DbCard::sett_standard($val);
                        }
                        $item['card_sett_standard'] = implode(',', $sett_standard);  // 卡券结算价格标准值
                        $item['act_sett_standard'] = DbCard::sett_standard($item['act_sett_standard']);;  // 活动结算价格标准值
                        $item['order_card_subsidy_money'] = $order_card_subsidy_money;
                        $item['commodity_card_subsidy_money'] = $commodity_card_subsidy_money;
                        $item['order_act_subsidy_money'] = $order_act_subsidy_money;
                        $item['commodity_act_subsidy_money'] = $commodity_act_subsidy_money;
                        $item['order_total_subsidy_money'] =  $order_total_subsidy_money;
                        $item['commodity_total_subsidy_money'] = $commodity_total_subsidy_money;

                    }
                }

                $time = number_format(microtime(true) - $api_start_at, 10, '.', '');
                Logger::error('orderstatictime4:'.$time);

                if ($item['pre_sale_id'] > 0) {
                    $finalAdd                   = $preAdd = $item->toArray();  // 预售订单信息  尾款订单信息
                    $preAdd['pay_order_code']   = $item['cashier_trade_no2']; // 支付单号
                    $finalAdd['pay_order_code'] = $item['cashier_trade_no']; // 支付单号
//
                    $preAdd['cashier_settlement_no']   = $item['cashier_settlement_no2']; // 结算单号
                    $finalAdd['cashier_settlement_no'] = $item['cashier_settlement_no']; // 结算单号
//
//                    // 旧的支付单号
                    $preAdd['cashier_trade_no']   = empty($item['ms_order_code2'])?$item['cashier_trade_no2']:''; // 财务中心支付单号
                    $finalAdd['cashier_trade_no'] = empty($item['ms_order_code'])?$item['cashier_trade_no']:''; // 财务中心支付单号
//
//                    // 新的支付单号
                    $preAdd['ms_order_code']   = $item['ms_order_code2']; // 联友支付预付单号
                    $finalAdd['ms_order_code'] = $item['ms_order_code']; // 联友支付尾款单号
//
//                    $preAdd['total_money']   = $item['front_money']; // 订单金额
//                    $finalAdd['total_money'] = $item['total_money']; // 订单金额
//
                    $preAdd['pay_money'] = $item['pre_use_money'];  // 预付金额
                    $finalAdd['pay_money'] = $item['money'];  // 尾款金额

                    $preAdd['total_integral'] = $item['pre_point']; // 预付积分
                    $finalAdd['total_integral'] = $item['integral']; // 尾款积分
//
                    $preAdd['pay_time']   = $item['front_pay_time']; // 预付款支付时间
                    $finalAdd['pay_time'] = $item['pay_time']; // 支付时间

                    $preAdd['count'] = 0;
                    array_push($newList, $preAdd);  // 预售订单信息
                    if ($item['order_status'] != 15) {
                        array_push($newList, $finalAdd);  // 尾款订单信息
                    }
                } else {
                    // 新单号存在  旧单号置空
                    $item['cashier_trade_no'] = empty($item['ms_order_code'])?$item['cashier_trade_no']:''; // 财务中心支付单号
                    array_push($newList, $item);
                }
            }
            $time = number_format(microtime(true) - $api_start_at, 10, '.', '');
            Logger::error('orderstatictime5:'.$time);
            // 订单核销有效期 t_bu_order.dq_time
            // 订单总补贴金额 t_bu_order.all_act_yh + all_card_yh +
            // 订单面值金额  t_db_card.card_quota  t_bu_order_commodity.goods_card_ids
            // 商品工时费原价 t_bu_order_commodity.work_time_money*num
            // 商品工时费活动价 t_bu_order_commodity.work_time_actual_money*num
            // 商品活动关联E3S活动  t_bu_order_commodity.e3s_activity_id
            // 商品活动结算规则
            // 商品优惠券关联E3S活动
            // 商品优惠券结算规则
            // 商品工时活动关联E3S活动
            // 商品工时活动结算规则
            // 商品工时活动补贴金额
            // 商品优惠券补贴金额
            // 商品活动补贴金额



            PhpExcel::export_csv2($newList, $fields, $titles, '订单统计.csv');
        } else {
            $waybill_where['a.order_code']     = ['in', $order_codes];
            $waybill_where['a.waybill_number'] = ['neq', ''];
            $waybill_where['a.mo_id']          = 0;
            if(!empty($where['b.supplier'])) {
                $waybill_where['a.supplier'] = $where['b.supplier'];
            }
            $waybill_params = [
                'where' => $waybill_where,
                'group' => 'a.waybill_number',
            ];
            $waybill_list = $order_commodity_model->getOrderCommodityWaybill($waybill_params);

            foreach ($list as $k => $val){
                $list[$k]['waybill_msg']    = '';
                $list[$k]['is_all_waybill'] = 0;
                if (empty($val['waybill_number'])) continue;
                $waybill_msg = $val['common_carrier'] . ' ' . $val['waybill_number'];
                if (!empty($waybill_list[$val['order_code']])) {
                    $waybill = $waybill_list[$val['order_code']];
                    if (count($waybill) == 1) {
                        $waybill_msg = end($waybill)['common_carrier'] . ' ' . end($waybill)['waybill_number'];
                    } else {
                        $waybill_msg = '共' . count($waybill) . '个包裹';
                    }
                    $count = $order_commodity_model->where(['order_code' => $val['order_code'], 'mo_id' => 0])->group('waybill_number')->count();
                    if (count($waybill) == $count){
                        $list[$k]['is_all_waybill'] = 1;
                    }
                }
                $list[$k]['waybill_msg'] = $waybill_msg;




            }
            foreach ($list as $k => $val) {
                $list[$k]['invoice_status'] = $invoiceStatusArr[$val['invoice_status']] ?? "";
            }


            $sys_model = new DbSystemValue();
            $sups      = $sys_model->getList(['where' => ['value_type' => 22], 'field' => 'value_code,county_name']);
            $page = $list->render();
            $this->assign('list', $list);
            $this->assign('sups', $sups);
            $this->assign('page', $page);
            $this->assign('role', $this->admin_info['type']);
            $this->assign('query', $query);
            $this->assign('comm_type_list', $comm_type_list);
            $this->assign('comm_three_type_list', $comm_three_type_list);
            $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
            $this->assign('comm_parent_id', $comm_parent_id);
            $this->assign('comm_three_parent_id', $three_comm_type_id);
            $this->assign('sub_comm_type_id', $sub_comm_type_id);
            $this->assign('order_status_list', $buOrder::orderStatus());
            $this->assign('role', $this->admin_info['type']);
            $this->assign('order_source_list', $order_source_list);
            $this->assign('channel', $channel_list);
            $this->assign('channel', $channel_list);
            return $this->fetch('order_static/index');
        }
    }

    /**
     * 获取商品分类
     */

    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);

    }

    private function _commodityTypeList($comm_parent_id = 0)
    {
        $dbCommType     = new DbCommodityType();
        $params         = ['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'field' => 'id,comm_type_name', 'order' => 'sort'];
        $commodity_list = $dbCommType->getList($params);
        return $commodity_list;
    }

    public function ajaxGetCommType()
    {
        $dbCommType          = new DbCommodityType();
        $comm_parent_type_id = input('get.comm_parent_type_id');
        if (empty($comm_parent_type_id))
            print_json(0, '', []);
        $params         = ['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_type_id], 'field' => 'id,comm_type_name', 'order' => 'sort'];
        $commodity_list = $dbCommType->getList($params);
        print_json(0, '', $commodity_list);
    }

    public function ajaxUpdateStatus()
    {
        if (request()->isAjax()) {
            $id            = input('post.id');
            $this->buOrder = new \app\common\model\bu\BuOrder();
            $row           = $this->buOrder->getOneByPk($id);
            if ($row['order_status'] != 4) print_json(1, '状态异常');
            $res = $this->buOrder->where('id', $id)->update(['order_status' => 7, 'modifier' => $this->adminInfo['username'], 'last_updated_date' => date('Y-m-d H:i:s')]);
            //结算成功数据;
            if ($res) {
//                $row    =$this->buOrder->getOneByPk($id);
//                $row    =$row->toArray();
//                unset($row['id']);
//                unset($row['created_date']);
//                unset($row['last_updated_date']);
//                $d_model=new BuOrderDetailed();
//                $last_id=$d_model->insertGetId($row);
                print_json(0, '结算成功');
            }
        }

    }

    public function ajaxGetOrderCommodity()
    {
        $order_code = input('get.order_code');
        if (empty($order_code))
            print_json(1, '订单编号不能为空');

        $buOrder = new BuOrder();
        $order_commodity_model = new BuOrderCommodity();

        $params  = [
            'field' => 'a.dlr_code as adlrcode,c.id as commodity_id,c.create_dlr_code as dlr_code,b.commodity_name,
            c.commodity_code,b.order_code,b.car_info,b.sku_info,b.price,b.count,b.commodity_pic as cover_image,b.third_sku_code sku_code,
            b.limit_id,b.third_sku_code,b.third_order_id,b.tax_code,b.tax,b.supplier,
            b.cost_price,b.third_mail_price,b.actual_price,b.actual_point,b.actual_use_money,b.mo_id,b.mo_sub_id',
            'where' => ['a.order_code' => $order_code, 'b.mo_id'=>0]
        ];//,f.rule_id,f.rule_name,f.rule_info

        $detail = $buOrder->getOrderCommodityInfoNoSku($params);

        foreach ($detail as $k => $detaili) {

            //组合商品子ID，主ID为mo_id非组合商品为0
            if ($detaili['mo_sub_id'] != 0) {
                $map = ['order_code'=>$detaili['order_code'], 'mo_id'=>$detaili['mo_sub_id']];
                $zCommodity = $order_commodity_model->where($map)->field('id,order_code,count,commodity_name')->find();
                $detaili['count'] = bcmul($zCommodity['count'], $detaili['count']); // 主商品数量*子商品数量

                $detail[$k]['commodity_name'] = '['.$zCommodity['commodity_name'].']      '   .$detaili['commodity_name'];
            }


            $detail[$k]['limit'] = '';
            if ($detaili['limit_id']) {
                $limit_model = new DbLimitDiscount();
                $limit_info  = $limit_model->getOneByPk($detaili['limit_id']);
                if ($limit_info) {
                    $detail[$k]['limit'] = sprintf("限时优惠：" . $limit_info['title']);
                }
            }

//            if (!is_null($detaili['rule_info'])) {
//                $rule_info               = json_decode($detaili['rule_info'], true);
//                $detail[$k]['rule_info'] = "平台:" . $rule_info['platform'] . '%' . ',' . "经销商:" . $rule_info['dlr'] . '%';
//            } else {
//                $detail[$k]['rule_info'] = "";
//            }
        }

        $detail = array_values($detail);
        print_json(0, '', $detail);
    }

    public function ajaxGetOrderDetail()
    {
        $order_code = input('get.order_code');
        if (empty($order_code))
            print_json(1, '订单编号不能为空');
        $dbOrderDetail  = new BuOrderDetailed();
        $params         = [
            'where' => ['order_code' => $order_code],
            'field' => 'order_status,created_date,modifier'
        ];
        $order_log_list = $dbOrderDetail->getList($params);
        if (!empty($order_log_list)) {
            foreach ($order_log_list as $key => $value) {
                $order_log_list[$key]['order_status'] = BuOrder::orderStatus($value['order_status']);
            }
        }
        print_json(0, '', $order_log_list);
    }

    public function ajaxUpdateIsCC()
    {
        if (request()->isAjax()) {
            $id          = input('post.id');
            $order_model = new BuOrder();
            if ($order_model->saveData(['is_cc_ok' => 0,'modifier'=>'ajaxUpdateIsCC'], ['id' => $id])) {
                print_json(0, '处理成功');
            } else {
                print_json(1, '处理失败');
            }
        }
    }
}
