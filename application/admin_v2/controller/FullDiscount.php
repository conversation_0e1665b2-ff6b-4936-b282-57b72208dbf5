<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: www
 * Date: 17/11/23
 * Time: 16:04
 */

namespace app\admin_v2\controller;

use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlr;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCommodityTypeSegmentDiscount;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFightGroupCommodity;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbFullDiscountCommDlr;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbLimitDiscountCommodity;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSystemValue;
use app\common\service\BaseDataService;
use app\common\service\CommodityService;
use think\Hook;
use think\Model;
use function PHPSTORM_META\type;
use think\Exception;

class FullDiscount extends Common
{
    private $comm_type_model;
    private $_dbCommodityDlrTypeObj;
    private $_dbCommodityFlatObj;
    private $gather_list;

    public function __construct()
    {
        parent::__construct();
        $this->comm_type_model        = new DbCommodityType();
        $this->_dbCommodityDlrTypeObj = new DbCommodityDlrType();
        $this->_dbCommodityFlatObj    = new DbCommodityFlat();
        $model = new DbSystemValue();
        $this->gather_list = $model->where(['value_type'=>26,'is_enable'=>1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('gather_list', $this->gather_list);
    }

    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);

    }

    public function live()
    {
        $activity_title  = input('get.activity_title');
        $activity_status = input('get.activity_status');
        $created_date    = input('get.created_date');
        $set_type  = input('get.set_type');
        $gather_id = input('get.gather_id',0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name = input('get.theme_name');
        $this->assign('set_type', $set_type);
        $where           = [];
        $live_type       = input('get.live_type');
        $set_type        = input('get.set_type');
        if (empty($live_type) || ($set_type == 5)) {
            $set_type                       = 5;
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($set_type == 6) {
            $set_type = 6;
            $where[]  = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        } elseif ($set_type == 7) {
            $set_type = 7;
            $where[]  = [['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if (!empty($activity_status)) {
            switch ($activity_status) {
                case 1://未开始，活动开始时间比当前时间往后
                    $where['a.start_time'] = ['>', date('Y-m-d H:i:s')];
                    break;
                case 2://进行中，活动开始时间比当前时间往前，结束时间比当前时间往后
                    $where['a.start_time'] = ['<=', date('Y-m-d H:i:s')];
                    $where['a.end_time']   = ['>', date('Y-m-d H:i:s')];
                    break;
                case 3://已结束，活动结束始时间比当前时间往后
                    $where['a.end_time'] = ['<', date('Y-m-d H:i:s')];
                    break;
                case 4://已结束，活动结束始时间比当前时间往后
                    $where['a.is_enable'] = ['=', 0];
                    break;
            }
            if (in_array($activity_status, [1, 2, 3])) {
                $where['a.is_enable'] = ['=', 1];
            }
        }

        if (!empty($created_date)) {
            list($created_date_start, $created_date_end) = explode('~', $created_date);
            $where['a.start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($created_date_start)), date('Y-m-d H:i:s', strtotime($created_date_end) + 86400)]];
        }

        if(!empty($gather_id)) {
            $where['a.gather_id'] = $gather_id;
        }
        if(!empty($is_pv_subsidy)) {
            if($is_pv_subsidy != 2){
                $where['a.is_pv_subsidy'] = $is_pv_subsidy;
            }else{
                $where['a.is_pv_subsidy'] = 0;
            }
        }
        if(!empty($theme_name)) {
            $where['a.theme_name'] = array('like','%'.$theme_name.'%');
        }

        $model               = new DbFullDiscount();
        $where['a.set_type'] = $set_type;
        $params              = [
            'where' => $where,
            'field' => "a.*",
            'order' => 'a.id desc',
            'query' => input('get.'),
            'group' => 'a.id'
        ];
        $gather_array = [];
        foreach ($this->gather_list as $value){
            $gather_array[$value['id']] = $value['name'];
        }
        $list                = $model->getListJoin($params);
        $page                = $list->render();
        foreach ($list as $key => $value) {
            $in = empty($value['is_enable']) || $activity_status == 4;
            if (strtotime($value['start_time']) > time()) {
                $list[$key]['status'] = $in ? '已关闭' : '未开始';
            } else if (strtotime($value['start_time']) <= time() && strtotime($value['end_time']) >= time()) {
                $list[$key]['status'] = $in ? '已关闭' : '进行中';
            } else if (strtotime($value['end_time']) < time()) {
                $list[$key]['status'] = $in ? '已关闭' : '已结束';
            }
            if($value['gather_id'] == 0 || empty($value['gather_id'])){
                $list[$key]['gather_name'] = '-';
            }else{
                if(isset($gather_array[$value['gather_id']])){
                    $list[$key]['gather_name'] = $gather_array[$value['gather_id']];
                }else{
                    $list[$key]['gather_name'] = '-';
                }
            }
            $list[$key]['is_pv_subsidy_status'] = $value['is_pv_subsidy'] == 1 ?'是': '否';
        }
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('set_type', $set_type);
        $this->assign('live_type', $live_type);
        return $this->fetch('live');
    }

    public function index()
    {
        $activity_title    = input('get.activity_title');
        $activity_status   = input('get.activity_status');
        $activity_set_type = input('get.activity_set_type');
        $created_date      = input('get.created_date');
        $dlr_code          = input('get.dlr_code');
        $set_type          = input('get.set_type', 1);
        $where             = [];
        $admin_dlr_code    = $this->admin_info['dlr_code'];
        if (!empty($activity_title)) {
            $where['a.activity_title'] = ['like', "%$activity_title%"];
        }
        if (!empty($activity_status)) {
            switch ($activity_status) {
                case 1://未开始，活动开始时间比当前时间往后
                    $where['a.start_time'] = ['>', date('Y-m-d H:i:s')];
                    break;
                case 2://进行中，活动开始时间比当前时间往前，结束时间比当前时间往后
                    $where['a.start_time'] = ['<=', date('Y-m-d H:i:s')];
                    $where['a.end_time']   = ['>', date('Y-m-d H:i:s')];
                    break;
                case 3://已结束，活动结束始时间比当前时间往后
                    $where['a.end_time'] = ['<', date('Y-m-d H:i:s')];
                    break;
            }
        }
        if ($activity_set_type == 1) {
            $where['a.created_dlr_code'] = 'NISSAN';
        } elseif ($activity_set_type == 2) {
            $where['a.created_dlr_code'] = ['<>', 'NISSAN'];
        }
        if (!empty($dlr_code)) {
            $where['a.created_dlr_code'] = ['like', "%$dlr_code%"];
        }
        if (!empty($created_date)) {
            list($created_date_start, $created_date_end) = explode('~', $created_date);
            $where['a.start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($created_date_start)), date('Y-m-d H:i:s', strtotime($created_date_end) + 86400)]];
        }


        $model      = new DbFullDiscount();
        $admin_type = $this->admin_info['type'];
        if ($admin_type == 1) {
            $where['a.set_type'] = $set_type == 3 ? $set_type : ['<>', 3];
        } else {
            $set_type = 2;
            $where[]  = ['exp', "(find_in_set('{$admin_dlr_code}',b.dlr_code) AND set_type = 1 ) OR a.created_dlr_code='{$this->admin_info['dlr_code']}'"];
        }
        $params = [
            'where' => $where,
            'field' => "a.*,(case set_type when $admin_type then 1 else 0 end) as auth_pass,(case b.dlr_code when 'NISSAN' then '-' else CONCAT(b.dlr_code,'-',b.dlr_name) end) as dlr,(case a.set_type when 1 then '平台' when 2 then '自建' end) as set_type",
            'order' => 'a.id desc',
            'query' => input('get.'),
            'group' => 'a.id'
        ];
        $list   = $model->getListJoin($params);
        $page   = $list->render();
        foreach ($list as $key => $value) {
            if (($value['created_dlr_code'] == 'NISSAN' && $admin_type == 1) or ($admin_type == 2 && $value['created_dlr_code'] == $admin_dlr_code)) {
                $list[$key]['edit'] = true;
            }
            if (strtotime($value['start_time']) > time()) {
                $list[$key]['status'] = '未开始';
            }
            if (strtotime($value['start_time']) <= time() && strtotime($value['end_time']) >= time()) {
                $list[$key]['status'] = '进行中';
            }
            if (strtotime($value['end_time']) < time()) {
                $list[$key]['status'] = '已结束';
            }
        }
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('set_type', $set_type);
        return $this->fetch('index');
    }

    public function addPageLive()
    {

        //角色类型
        $adminType = $this->admin_info['type'];
        $setType   = input('set_type', 5);

        $nvi_code = '';
        if($setType == 5){
            $nvi_code = 'N';
        }
        if($setType == 6){
            $nvi_code = 'P';
        } if($setType == 7){
            $nvi_code = 'V';
        }
        $user_level = [];
        if($setType == 5 || $setType == 7){
            $sysvalueobj = new DbSystemValue();
            $user_level = $sysvalueobj->getListByCode(25,$nvi_code);
//            $user_level = (new DbSystemValue())->getNameListByCode(25,$nvi_code);

        }
        $this->assign('user_level',$user_level);
        $this->assign('user_level_json',json_encode($user_level));
        $this->assign('nvi_code','');

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');

        $commodity_class = DbCommodity::commodityClass();
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY9]); // 延保服务包不参加活动

        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('set_type', $setType);
        $this->assign('admin_type', $adminType);
        $this->assign('set_type', input('set_type', 5));
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', (new DbSystemValue())->getNameList(20));
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));
        $this->assign('live_type', input('live_type'));
        return $this->fetch('add_page_live');
    }

    public function addPage()
    {
        $model  = new DbCommodityType();
        $params = [
            'where' => [
                'is_enable'      => 1,
                'comm_parent_id' => 0
            ],
            'field' => 'id,comm_type_name'
        ];
        $list   = $model->getList($params);


        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);

        $this->assign('comm_type_list', $list);
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('action', 'add');
        $this->assign('set_type', input('get.set_type'));
        return $this->fetch('add_page');
    }

    public function editPageLive()
    {
        $id                   = intval(input('get.id'));
        $model                = new DbFullDiscount();
        $data                 = $model->getActivityDetailLive($id);

        $user_segment_options = '';
        $user_segment_options_arr = [];
        if(!empty($data['info']['user_segment_options'])){
            $user_segment_options_arr = explode(',',$data['info']['user_segment_options']);
            $user_segment_options = $user_segment_options_arr[0];
        }

        $this->assign('user_segment_options_arr',$user_segment_options_arr);
        $this->assign('user_segment_options',$user_segment_options);
        $full_discount_rules = [];
        $car_dis_rule = [];
        $car_title = [];
        $car_full_dis = [];
        $new_car_dis_rule = [];
        if($data['info']['user_segment'] > 0){
            if($data['info']['user_segment'] == 1){
                $full_discount_rules = json_decode($data['info']['full_discount_rules'],true);
                $dis_tmp = [];
                foreach($full_discount_rules as $k =>$item_list){
                    foreach($item_list as $sub=>$item){
                        $dis_tmp[$sub][] = $item;
                    }
                }
                $full_discount_rules = $dis_tmp;
            }else{
                $car_dis_rule = json_decode($data['info']['full_discount_rules'],true);
                foreach($car_dis_rule as $k=>$car_dis_item){
                    $car_title[] = $k;
                }

                foreach($car_title as $item){
                    foreach($car_dis_rule[$item]  as $k=>$car_dis_item){
                        $car_full_dis[] = $car_dis_item[0];
                        $new_car_dis_rule[$item][] = $car_dis_item[1];
                    }
                }
                $car_dis_rule = $new_car_dis_rule;
            }
        }

        //dd($car_dis_rule);
        $car_dis_rule['dis'] = array_unique($car_full_dis);
        $this->assign('full_discount_rules',$full_discount_rules);
        $this->assign('car_dis_rule',$car_dis_rule);

        $up_down_channel_info = (new DbSystemValue())->getNameList(20);
        $info_str             = implode(',', array_keys($up_down_channel_info));

        //角色类型
        $adminType = $this->admin_info['type'];
        $setType   = $data['info']['set_type'];

        $nvi_code = '';
        if($setType == 5){
            $nvi_code = 'N';
        }
        if($setType == 7){
            $nvi_code = 'V';
        }
        if($setType == 6){
            $nvi_code = 'P';
        }
        $user_level = [];
        if($setType == 5 || $setType == 7){
            $sysvalueobj = new DbSystemValue();
            $user_level = $sysvalueobj->getListByCode(25,$nvi_code);
//            $user_level = (new DbSystemValue())->getNameListByCode(25,$nvi_code);
        }

        $this->assign('user_level',$user_level);
        $this->assign('user_level_json',json_encode($user_level));
        $this->assign('nvi_code','');

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');


        //优惠券
        $card_arr = [];
        $selected_data = [];
        $commodity_card_name = [];
        if(!empty($data['info']['rel_card_ids'])) {
            $commodity_card_name = (new DbCard())->getColumn(['where' => ['id' => ['in', $data['info']['rel_card_ids']]], 'column' => 'card_name']);
            $selected_data = (new DbCard())->getColumn(['where' => ['id' => ['in', $data['info']['rel_card_ids']]], 'column' => 'id,card_name']);
            $card_arr = explode(',' ,$data['info']['rel_card_ids']);
        }
        $data['info']['commodity_card_name'] =  implode(',', $commodity_card_name);
        $data['info']['num_card'] = empty( $data['info']['rel_card_ids']) ? 0 : count(explode(',',  $data['info']['rel_card_ids']));
        $this->assign('selected',json_encode($card_arr));
        $this->assign('selected_data', $selected_data);
        $this->assign('selected_card_name',json_encode($commodity_card_name));

        $commodity_class       = DbCommodity::commodityClass();
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY9]); // 延保服务包不参加活动

        $oneSkuData            = $data['sku_list'][0] ?? [];
        $oneCommodityClass     = $oneSkuData['commodity_class'] ?? 0;
        $oneCommodityDlrTypeId = $oneSkuData['commodity_dlr_type_id'] ?? 0;

        $this->assign('oneCommodityClass', $oneCommodityClass);
        $this->assign('oneCommodityDlrTypeId', $oneCommodityDlrTypeId);
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));

        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', $up_down_channel_info);
        $this->assign('dlr_hidden', (strpos($data['info']['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
        $this->assign('dlr_str', $this->getDlrInInfo($data['info']['up_down_channel_dlr']));
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('info', $data['info']);//活动是否已开始
        $this->assign('sku_list', $data['sku_list']);//活动是否已开始
        $this->assign('commodity_set_ids', $data['commodity_set_ids']);//活动是否已开始
        $this->assign('action', 'edit');//活动是否已开始
        $this->assign('set_type', $setType);
        $this->assign('live_type', input('live_type'));
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));
        return $this->fetch('edit_page_live');
    }

    public function editPage()
    {
        $id     = intval(input('get.id'));
        $model  = new DbFullDiscount();
        $data   = $model->getActivityDetail($id);
        $model  = new DbCommodityType();
        $params = [
            'where' => [
                'is_enable'      => 1,
                'comm_parent_id' => 0
            ],
            'field' => 'id,comm_type_name'
        ];
        $list   = $model->getList($params);
        $this->assign('comm_type_list', $list);

        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);

        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('info', $data['info']);//活动是否已开始
        $this->assign('sku_list', $data['sku_list']);//活动是否已开始
        $this->assign('commodity_set_ids', $data['commodity_set_ids']);//活动是否已开始
        $this->assign('action', 'edit');//活动是否已开始
        $this->assign('set_type', $data['info']['set_type']);
        return $this->fetch('edit_page');
    }

    public function viewPage()
    {
        $id     = intval(input('get.id'));
        $model  = new DbFullDiscount();
        $data   = $model->getActivityDetail($id);
        $model  = new DbCommodityType();
        $params = [
            'where' => [
                'is_enable'      => 1,
                'comm_parent_id' => 0
            ],
            'field' => 'id,comm_type_name'
        ];
        $list   = $model->getList($params);
        $this->assign('comm_type_list', $list);
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('info', $data['info']);//活动是否已开始
//        print_json(1,'',$data['info']);
        $this->assign('sku_list', $data['sku_list']);//活动是否已开始
        $this->assign('commodity_set_ids', $data['commodity_set_ids']);//活动是否已开始
        $this->assign('action', 'view');//活动是否已开始
        return $this->fetch('view_page');
    }

    /**
     * 获取商品分类
     */
    public function ajaxGetCt()
    {
        $ct_parent_id = input('get.ct_parent_id');
        if (empty($ct_parent_id)) {
            print_json(0, '', []);
        }
        $model_ct = new DbCommodityType();
        $where    = [
            'comm_parent_id' => $ct_parent_id,
            'is_enable'      => 1,
            'is_display'     => 1
        ];
        $list     = $model_ct->where($where)->select();
        print_json(0, '', $list);
    }

    /**
     * 车生活
     */
    public function ajaxGetLiveCommodityList()
    {
        $commodity_name        = input('commodity_name');
        $top_type              = input('comm_parent_id');
        $second_type           = input('sub_comm_type_id');
        $third_type            = input('three_comm_type_id');
        $active_id             = input('active_id');
        $commodity_class       = input('commodity_class');
        $live_type             = input('live_type');
        $commodity_dlr_type_id = input('commodity_dlr_type_id');
        $start_time            = input('start_time');
        $end_time              = input('end_time');
        $discount_type         = input('discount_type');
        $user_segment          = input('user_segment') ?? 0;
        $set_type  = input('set_type') ?? 0;
        $where = [
            'b.listing_type'    => 1,
        ];

        $where_time = ['start_time'=>$start_time, 'end_time'=>$end_time];
        if ($set_type == 5) {
            $brand_id = 1;
            $where['b.shelves_type']        = 5;
            $where['b.qsc_group'] =  '';  // 取送车服务包不能参加
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];

        } elseif ($set_type == 6) {
            $brand_id = 3;
            $where['b.shelves_type'] = 6;
            $where[]                 = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        } elseif ($set_type == 7){
            $brand_id = 2;
            $where['b.shelves_type'] = 7;
            $where[]                 = [['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], 'or'];
        }


        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        if (!empty($commodity_dlr_type_id)) {
            $where['b.commodity_dlr_type_id'] = $commodity_dlr_type_id;
        }

        $type_id = 0;
        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

        if (!empty($commodity_class)) {
            $where['commodity_class'] = $commodity_class;
        } else {
            $where['commodity_class'] = ['neq', DbCommodity::COMMODITY_CLASS_KEY9]; // 延保服务包不能参加

        }

//        $where['commodity_class'] = ['in', [1, 2]];//只能选择实物商品--增加虚拟商品

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }

        $field  = "b.commodity_dlr_type_id,a.commodity_class,comm_type_id,a.up_down_channel_name,a.commodity_id,a.commodity_set_id,commodity_name,cover_image,a.price,a.count_stock";
        $params = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );

        $flat = new DbCommodityFlat();
        $list = $flat->getCommodityList($params);
        $date = date('Y-m-d H:i:s');
        //是否 定向人群不一至
        $dbcommoidtytypesegmentObj = new DbCommodityTypeSegmentDiscount();
        foreach($list as $k =>$v){
            $list[$k]['is_segment'] = 0;
            if($user_segment > 0){
                $segment_info = $dbcommoidtytypesegmentObj->alias("a")->join("t_db_commodity_segment_discount b","b.id=a.segment_discount_id")->where(['a.commodity_type_id'=>$v['comm_type_id'],'a.is_enable'=>1,'a.brand'=>$brand_id])->field('b.*')->find();
                if(!empty($segment_info)){
                    if($segment_info['user_segment'] != $user_segment){
                        $list[$k]['is_segment'] = 1;
                    }
                }
            }
        }

        //判断是否已经参加了预售
        $list = (new DbPreSale())->getIsPreProduct($list, [
            'a.is_enable'      => ['=', 1],
            'a.front_s_time'   => ['<', $date],
            'a.balance_e_time' => ['>', $date],
            'a.set_type'       => $where['b.shelves_type'],
        ]);

        $disQuery = [
            'a.id'         => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]],
        ];
        if (!empty($end_time)) $disQuery['a.start_time'] = ['<', $end_time];
        if (!empty($start_time)) $disQuery['a.end_time'] = ['>', $start_time];

        if ($discount_type == 1) {
            $disQuery['a.discount_type'] = ['<>', 2];
        }
        if (in_array($discount_type, [2, 3])) {
            $limit_where = $disQuery;
            unset($limit_where['a.id']);
            $limit_where['a.discount_type'] = ['<>', 1];
            $list                           = (new DbLimitDiscount())->getIsLimitProduct(
                $list, $limit_where, $where_time
            );
            $disQuery['a.discount_type']    = ['<>', 1];
        }
        //是否满优惠
        $list = (new DbFullDiscount())->getIsFullDiscount($list, $disQuery, $where_time);

        //如果是快递到家
        $homeArr = $this->_dbCommodityDlrTypeObj->getExpressHomeType();
        foreach ($list as $key => $val) {
            $list[$key]['home'] = 0;
            if (isset($val['commodity_dlr_type_id']) && in_array($val['commodity_dlr_type_id'], $homeArr)) {
                $list[$key]['home'] = 1;
            }
        }

        //判断是否已经参加秒杀
        $list        = (new DbSeckill())->getIsSeckillProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]],
        ],$where_time);
        $res         = [];
        $res['list'] = $list;
        print_json(0, '', $res);
    }

    /**
     * 获取商品列表：
     * 1.专营店可以设置平台发布的专营店商品和专营店自己的商品（可卖商品）；平台只能设置平台发布的平台商品；
     * 2.活动商品里面的原价格显示所有规格的最高价；
     * 3.如果商品下架，将从活动中移除；
     * 4.只能选择实物商品；
     *
     * 同一个商品在多个活动中可以选择同一个经销商
     */
    public function ajaxGetCommodityList()
    {
        $model    = new DbCommodity();
        $newModel = new DbCommodityDlrType();
        $where    = [];

        $comm_parent_id     = input('comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_name          = input('get.commodity_name');
        $set_type           = input('get.set_type');

//        if (!empty($comm_type_id)) {
//            $where['a.comm_type_id'] = intval($comm_type_id);
//        } else {
//            if (!empty($comm_type_pid)) {
//                $where['c.id'] = intval($comm_type_pid);
//            }
//        }

        if (!empty($three_comm_type_id)) {
            $where['a.comm_type_id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column        = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);
                $where['a.comm_type_id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column         = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column       = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $where['a.comm_type_id'] = ['in', $three_type_column];
                }
            }
        }

        if (!empty($comm_name)) {
            $where['commodity_name'] = ['like', "%$comm_name%"];
        }
        $where['a.commodity_class'] = ['in', [1, 2]];//只能选择实物商品--增加虚拟商品
        $commodity_dlr_type_list    = $newModel->getExpressHomeType();//获取快递到家的commodity_dlr_type_id
        $params                     = [
            'where'    => $where,
            'field'    => 'a.id,a.commodity_name,a.cover_image,ifnull(highest_price,e.original_price_range_end) as highest_price,e.count_stock,e.id as commodity_set_id,e.dlr_code,e.commodity_dlr_type_id',
            'pagesize' => input('get.pagesize')
        ];
        $params['shelves_type']     = $set_type;
        $list                       = $model->getShelvesListByDlr($this->admin_info['type'], $this->admin_info['dlr_code'], $params);
        foreach ($list as $value) {
            $value['commodity_dlr_type'] = 2;
            if (in_array($value['commodity_dlr_type_id'], $commodity_dlr_type_list)) {
                $value['commodity_dlr_type'] = 1;
            }
        }

        $res['list'] = $list;
        print_json(0, '', $res);
    }

    public function getSkuLive()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $comm_service     = new CommodityService();

        $sku              = $comm_service->getSetSku($commodity_set_id);
        $sku_list         = $sku['sku_list'];
        $commodity_row    = $this->_dbCommodityFlatObj->getOne([
            'where'        => ['commodity_id' => $commodity_id, 'commodity_set_id' => $commodity_set_id],
            'field'        => 'commodity_name,cover_image,comm_type_id',
            'shelves_type' => 5
        ]);

        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (empty($val['sp_value_arr'])) {
                continue;
            }
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    /**
     * 获取商品规格列表
     * 1、专营店不能卖该商品不能选择该专营店；
     * 2、如果该商品规格有限时折扣价格，原价显示限时折扣价格；
     */
    public function getSku()
    {
        $commodity_id                   = input('get.commodity_id');
        $commodity_set_id               = input('get.commodity_set_id');
        $comm_set_model                 = new DbCommoditySet();
        $comm_service                   = new CommodityService();
        $commodity                      = new DbCommodity();
        $limit_discount_commodity_model = new DbLimitDiscountCommodity();
        $params                         = [
            'where' => [
                'id' => $commodity_set_id
            ],
            'field' => 'commodity_id,dlr_code,set_type'
        ];
        $com_set_row                    = $comm_set_model->getOne($params);
        $params                         = [
            'where' => [
                'id' => $commodity_id
            ],
            'field' => 'commodity_name,cover_image'
        ];
        $commodity_row                  = $commodity->getOne($params);
        $params                         = [
            'where' => [
                'commodity_id'     => $commodity_id,
                'commodity_set_id' => $commodity_set_id
            ],
            'field' => 'sku_price'
        ];
        $com_sku_discount_row           = $limit_discount_commodity_model->getOne($params);
        if (!empty($com_sku_discount_row)) {
            $com_sku_discount_row = json_decode($com_sku_discount_row['sku_price'], true);
//            print_r($com_sku_discount_row);exit();
        }

        //参加了折扣活动
        $sku      = $comm_service->getSetSku($commodity_set_id);
        $sku_list = $sku['sku_list'];
//        print_json(1,'',$sku_list);exit();
        foreach ($sku_list as $key => $val) {
            if (!empty($com_sku_discount_row)) {
                if (isset($com_sku_discount_row[$val['id']])) {
                    $sku_list[$key]['price'] = $com_sku_discount_row[$val['id']];
                }
            }
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    /**
     * 获取优惠券：
     * 平台端：只能选平台的所有类型优惠券：type = 1|2
     * 专营店：只能选专营店的所有类型优惠券：type = 1|2
     */
    public function ajaxGetCard()
    {
        $this->_checkAjax();
        $model     = new DbCard();
        $card_name = input('get.card_name');
        $where     = [];
        if (!empty($card_name)) {
            $where['card_name'] = ['like', '%' . $card_name . '%'];
        }
        $where['is_enable'] = 1;
        $where['dlr_code']  = $this->admin_info['dlr_code'];
        $params             = [
            'where'    => $where,
            'order'    => 'id desc',
            'field'    => 'id,card_type,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to',
            'pagesize' => input('get.pagesize'),
            'query'    => input('get.')
        ];
        $list = $model->getListPaginate($params);
        $card_type_arr = $model->cardType();

        foreach ($list as $key => $value) {
            $list[$key]['id'] = (string)$value['id'];
            $list[$key]['card_type_name'] = $card_type_arr[$value['card_type']] ?? '';

            switch ($value['date_type']) {
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'] . '至' . $value['validity_date_end'];
                    break;
                case 2:
                    if ($value['fixed_begin_term'] == 0) {
                        $list[$key]['validity_date'] = "自领取当天有效，有效期" . $value['fixed_term'] . '天';
                    } elseif ($value['fixed_begin_term'] == 1) {
                        $list[$key]['validity_date'] = "领取后" . $value['fixed_term'] . '天后有效';
                    }
                    break;
                default:
                    break;
            }
        }
        print_json(0, '', $list);
    }

    /**
     * 通过上架id 获取可以选专营店
     */
    public function ajaxGetDlr()
    {
        $commodity_set_id = input('get.commodity_set_id') or print_json(1, '无法获取已上架商品的编号', []);
        $baseService = new BaseDataService();
        $dlr_list    = $baseService->getDlrList($commodity_set_id);
        print_json(0, '', $dlr_list);
    }

    public function saveLive()
    {
        $post = input('post.');
        $activity_title = input('post.activity_title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '活动起始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '活动结束时间不能为空');
        $set_type            = input('post.set_type', 5);
        $card_available      = input('post.card_available', 0);
        $home                = input('post.home');
        $is_enable           = input('post.is_enable');
        $user_segment_options= input('post.user_segment_options') ?? '';
        $user_segment        = input('post.user_segment',0);
        $up_down_channel_arr = $post['up_down_channel'] ?? [];
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $up_down_channel_arr),
            input('post.dlr_code', '')
        );
        $gather_id = input('gather_id',0);
        $is_pv_subsidy = input('is_pv_subsidy',0);
        $theme_name = input('theme_name');
        $new_user_segment_options = '';

        if($user_segment == 0)
        {
//            $full_discount_rules = input('post.full_discount_rules');
            $full_discount_rules = $post['full_discount_rules'];
            if (!is_array($full_discount_rules)) {
                $rules_info          = json_decode($full_discount_rules);
            } else {
                $rules_info          = $post['full_discount_rules'];
            }
            $key_info            = array_column($rules_info, 0);
            rsort($key_info);
            $final_info = [];
            foreach ($key_info as $item) {
                foreach ($rules_info as $rule_item) {
                    if ($rule_item[0] == $item) {
                        $final_info[] = $rule_item;
                    }
                }
            }
        }else if($user_segment == 1){ //有定会员
            if($set_type == 5){
                $nvi_code = 'N';
            }
            if($set_type == 7){
                $nvi_code = 'V';
            }if($set_type == 6){
                $nvi_code = 'P';
            }
            if($set_type == 5 || $set_type == 7){
                $sysvalueobj = new DbSystemValue();

                $user_level = $sysvalueobj->getListByCode(25,$nvi_code);
                foreach($user_level as $user_level_item){
                    $user_level_arr[] = $user_level_item['value_code'];
                }
                if(!empty($user_level_arr)){
                    $user_level_str = implode(",",$user_level_arr);
                    $level_arr = explode($user_segment_options,$user_level_str);
                    $new_user_segment_options = $user_segment_options.$level_arr[1];
                }

                $final_info=[];
//                $m_level_arr  = input('m_level');
                $m_level_arr  = $post['m_level'];
                foreach($user_level_arr as $code){//会员编码
                   // $final_info[$code] = [];
                    $m_level_item_tmp = [];
                    $user_level_list = $_REQUEST[$code];
                    foreach($user_level_list as $k=>$user_level_item){//每会员编码的优惠
                        if($user_level_item > 0){
                            $m_level_item_tmp[] = [$m_level_arr[$k],$user_level_item];
                        }
                    }
                    if(!empty($m_level_item_tmp)){
                        $final_info[$code] = array_reverse($m_level_item_tmp);
                    }
                }
            }
        }else if($user_segment == 2){ //有定向车主
//            $cm_level  = input('cm_level');
            $cm_level  = $post['cm_level'];
//            $none_car  = input('none_car');
            $none_car  = $post['none_car'];
//            $nissan_car = input('nissan_car');
            $nissan_car = $post['nissan_car'];
            $none_car_tmp = [];
            $nissan_car_tmp = [];
            $ariya_car_tmp = [];
            foreach($cm_level as $ck=>$m_level_item){
                if(!empty($none_car[$ck])){
                    $none_car_tmp[] = [$m_level_item,$none_car[$ck]];
                }
                if(!empty($nissan_car[$ck])) {
                    $nissan_car_tmp[] = [$m_level_item, $nissan_car[$ck]];
                }
                if($set_type == 6){
//                    $ariya_car  = input('ariya_car');
                    $ariya_car  = $post['ariya_car'];
                    $ariya_car_tmp[] =[$m_level_item,$ariya_car[$ck]];
                }
            }
            $new_user_segment_options = [];
            if(!empty($none_car_tmp)) $final_info['NONE'] = array_reverse($none_car_tmp);
            if(!empty(array_sum($none_car))) $new_user_segment_options[] = "NONE";

            if(!empty($nissan_car_tmp)){
                if($set_type == 5){
                    $new_level_arr = 'N';
                    $final_info[$new_level_arr] = array_reverse($nissan_car_tmp);

                }
                if($set_type == 7){
                    $new_level_arr = 'V';
                    $final_info[$new_level_arr] = array_reverse($nissan_car_tmp);
                }
                if($set_type == 6){
                    $new_level_arr = 'N';
                    $final_info[$new_level_arr] = array_reverse($nissan_car_tmp);
                }
                $new_user_segment_options[] = $new_level_arr;
            }

            if($set_type ==6){
                $final_info['P'] = array_reverse($ariya_car_tmp);
//                $ariya_car = input('ariya_car');
                $ariya_car = $post['ariya_car'] ?? [];
                if(!empty(array_sum($ariya_car))){
                    $new_user_segment_options[] ="P";
                }
            }
            $new_user_segment_options = implode(',',$new_user_segment_options);
        }

        if (empty($_POST['sku_list'])) {
            print_json(1, '商品不能为空');
        }
        $sku_list                      = $post['sku_list'];
        $full_discount_model           = new DbFullDiscount();
        $act_status                    = $full_discount_model->getActStatus($start_time, $end_time);
        $data['user_segment']          = $user_segment;
        $data['user_segment_options']  = $new_user_segment_options;
        $data['activity_title']        = $activity_title;
        $data['start_time']            = $start_time;
        $data['end_time']              = $end_time;
        $data['tag']                   = $post['tag'];
        $data['act_status']            = $act_status;
        $data['set_type']              = $set_type;
        $data['is_enable']             = $is_enable;
        $data['created_dlr_code']      = $this->admin_info['dlr_code'];
        $data['sku_list']              = $sku_list;
        $data['card_available']        = $card_available;
        $data['full_discount_rules']   = json_encode($final_info);
        $data['up_down_channel_dlr']   = $up_down_channel_dlr;
        $data['up_down_channel_name']  = implode(',', $_POST['up_down_channel_name'] ?? []);
        $data['discount_type']         = $post['discount_type'] ?? 1;
        $data['e3s_activity_id']       = $post['e3s_activity_id'] ?? 0;
        $data['activity_type']         = $post['activity_type'] ?? 0;
        $data['settlement_rule_id']    = $post['settlement_rule_id'];
        $data['settlement_rule_name']  = $post['settlement_rule'];
        $data['settlement_rule_type']  = $post['settlement_rule_type'];
        $data['settlement_rule_value'] = $post['settlement_rule_value'];
        $data['act_sett_standard']     = $post['act_sett_standard'];
        $data['purchase_number']       = $post['purchase_number'] ?? 0;
        $data['rel_card_ids']          = $post['rel_card_ids'] ?? '';
        $data['creator']               = $this->admin_info['username'];
        $data['gather_id']             = $gather_id;
        $data['is_pv_subsidy']         = $is_pv_subsidy;
        $data['theme_name']            = $theme_name;
        if (!empty($data['e3s_activity_id'])){
            if (empty($data['activity_type'])) print_json(1, '存在e3s活动时活动设置类型必选');
        } else {
            $data['activity_type'] = 0;
        }
        $e3s_activity = input('e3s_activity', '');
        if (!empty($e3s_activity)) {
            $data['e3s_activity_name'] = explode(' | ', $e3s_activity)[1];
        }

        $where_time = ['start_time'=>$start_time, 'end_time'=>$end_time];
        if ($is_enable == 1) {
            $date = date('Y-m-d H:i:s');
            //判断是否已经参加了预售
            $list = (new DbPreSale())->getIsPreProduct(
                $sku_list, [
                    'a.is_enable'      => ['=', 1],
                    'a.front_s_time'   => ['<', $date],
                    'a.balance_e_time' => ['>', $date],
                    'a.set_type'       => $set_type,
                ]
            );
            foreach ($list as $v) {
                if ($v['is_pre'] == 1) print_json(1, $v['commodity_name'] . '已参加预售活动');
            }
            //判断是否已经参加秒杀
            $list = (new DbSeckill())->getIsSeckillProduct($sku_list, [
                'a.is_enable'  => ['=', 1],
//                'a.start_time' => ['<', $end_time],
//                'a.end_time'   => ['>', $start_time],
                'a.set_type'   => $set_type,
                'a.act_status' =>['in',[1,2]]
            ],$where_time);
            foreach ($list as $v) {
                if ($v['is_seckill'] == 1) print_json(1, $v['commodity_name'] . '已参加秒杀活动');
            }

            $disQuery = [
                'a.is_enable'  => ['=', 1],
//                'a.start_time' => ['<', $end_time],
//                'a.end_time'   => ['>', $start_time],
                'a.set_type'   => $set_type,
                'a.act_status' => ['in', [1, 2]],
            ];
            if ($data['discount_type'] == 1) {
                $disQuery['a.discount_type'] = ['<>', 2];
            }
            if (in_array($data['discount_type'], [2, 3])) {
                $limit_where = $disQuery;
                unset($limit_where['a.id']);
                $list = (new DbLimitDiscount())->getIsLimitProduct(
                    $list, $limit_where,$where_time
                );
                foreach ($list as $v) {
                    if ($v['is_limit'] == 1) print_json(1, $v['commodity_name'] . '工时已参加限时折扣活动');
                }
            }
            //是否满优惠
            $list = (new DbFullDiscount())->getIsFullDiscount(
                $sku_list, $disQuery,$where_time
            );

            foreach ($list as $v) {
                if ($v['is_full'] == 1) print_json(1, $v['commodity_name'] . '已参加其它满减活动');
            }
        }

        $result = $full_discount_model->initFullDiscountActivityLive($data, $set_type, $home);

        if (empty($result['status'])) {
            if (($act_status == 2) && !empty($is_enable)) {
                $full_commodity_arr = $full_discount_model->getAllFullDiscount([
                    'where' => ['a.id' => $result['id']],
                    'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                ]);
                $this->doHook('add', $full_commodity_arr, $result['id']);
            }
        }
        print_json($result['status'], $result['msg']);
    }

    public function save()
    {
        $activity_title = input('post.activity_title') or print_json(1, '活动名称不能为空');
        $money = input('post.money') or print_json(1, '活动金额不能为空');
        $start_time = input('post.start_time') or print_json(1, '活动起始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '活动结束时间不能为空');
        $is_preferential_money = input('post.is_preferential_money');
        $preferential_money    = input('post.preferential_money');
        $is_preferential_card  = input('post.is_preferential_card');
        $preferential_card_id  = input('post.preferential_card_id');
        $set_type              = input('post.set_type');
        if (empty($_POST['sku_list'])) {
            print_json(1, '商品不能为空');
        }
        $sku_list                      = $_POST['sku_list'];
        $full_discount_model           = new DbFullDiscount();
        $data['activity_title']        = $activity_title;
        $data['money']                 = floatval($money);
        $data['start_time']            = $start_time;
        $data['end_time']              = $end_time;
        $data['is_preferential_money'] = intval($is_preferential_money);
        $data['is_preferential_card']  = intval($is_preferential_card);
        $data['preferential_money']    = floatval($preferential_money);
        $data['preferential_card_id']  = intval($preferential_card_id);
        $data['set_type']              = $set_type;
        $data['created_dlr_code']      = $this->admin_info['dlr_code'];
        $data['sku_list']              = $sku_list;
        $result                        = $full_discount_model->initFullDiscountActivity($data, $set_type);

        print_json($result['status'], $result['msg']);
    }

    public function editLive()
    {
        $post = input('post.');
        $id   = input('post.id');
        $home = input('post.home');
        $activity_title = input('post.activity_title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '活动起始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '活动结束时间不能为空');
        $set_type            = input('post.set_type');
        $is_enable           = input('post.is_enable');
        $card_available      = input('post.card_available', 0);
        $user_segment_options= input('post.user_segment_options') ?? '';
        $user_segment        = input('post.user_segment',0);
        $gather_id = input('gather_id',0);
        $is_pv_subsidy = input('is_pv_subsidy',0);
        $theme_name = input('theme_name');
        $up_down_channel_arr = $post['up_down_channel'] ?? [];
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $up_down_channel_arr),
            input('post.dlr_code', '')
        );
        $full_discount_rules = $post['full_discount_rules'];
        if (empty($_POST['sku_list'])) {
            print_json(1, '商品不能为空');
        }
        $sku_list = $post['sku_list'];

        $new_user_segment_options = '';
       // dd($user_segment);
        if($user_segment == 0)
        {
            $full_discount_rules = $post['full_discount_rules'];
        }else if($user_segment == 1){ //有定会员
            if($set_type == 5){
                $nvi_code = 'N';
            }
            if($set_type == 7){
                $nvi_code = 'V';
            }
            if($set_type == 5 || $set_type == 7){
                $sysvalueobj = new DbSystemValue();

                $user_level = $sysvalueobj->getListByCode(25,$nvi_code);
                foreach($user_level as $user_level_item){
                    $user_level_arr[] = $user_level_item['value_code'];
                }
                if(!empty($user_level_arr)){
                    $user_level_str = implode(",",$user_level_arr);
                    $level_arr = explode($user_segment_options,$user_level_str);
                    $new_user_segment_options = $user_segment_options.$level_arr[1];
                }

                $final_info=[];
//                $m_level_arr  = input('m_level');
                $m_level_arr  = $post['m_level'];
                foreach($user_level_arr as $code){//会员编码
                    // $final_info[$code] = [];
                    $m_level_item_tmp = [];
                    $user_level_list = $_REQUEST[$code] ?? [];
                    foreach($user_level_list as $k=>$user_level_item){//每会员编码的优惠
                        if($user_level_item > 0){
                            $m_level_item_tmp[] = [$m_level_arr[$k],$user_level_item];
                        }
                    }
                    if(!empty($m_level_item_tmp)){
                        $final_info[$code] = array_reverse($m_level_item_tmp);
                    }
                }
            }
            $full_discount_rules = json_encode($final_info);
            //dd($final_info);
        }else if($user_segment == 2){ //有定向车主
//            $cm_level  = input('cm_level');
//            $none_car  = input('none_car');
//            $nissan_car = input('nissan_car');
//
//
//            $none_car_tmp = [];
//            $nissan_car_tmp = [];
//            $ariya_car_tmp = [];
//            foreach($cm_level as $ck=>$m_level_item){
//                if(!empty($none_car[$ck])){
//                    $none_car_tmp[] = [$m_level_item,$none_car[$ck]];
//                }
//                if(!empty($nissan_car[$ck])) {
//                    $nissan_car_tmp[] = [$m_level_item, $nissan_car[$ck]];
//                }
//                if($set_type == 6){
//                    $ariya_car  = input('ariya_car');
//                    $ariya_car_tmp[] =[$m_level_item,$ariya_car[$ck]];
//                }
//            }
//            if(!empty($none_car_tmp)) $final_info['NONE'] = $none_car_tmp;
//
//            $new_user_segment_options = [];
//            if(!empty(array_sum($none_car))){
//                $new_user_segment_options[] = "NONE";
//            }
//            if(!empty($nissan_car_tmp)){
//                if($set_type == 5){
//                    $new_level_arr = 'N';
//                }
//                if($set_type == 7){
//                    $new_level_arr = 'V';
//                }
//                $final_info[$new_level_arr] = $nissan_car_tmp;
//            }
//            if(!empty(array_sum($nissan_car))){
//                if($set_type == 5){
//                    $new_level_arr = 'N';
//                }
//                if($set_type == 7){
//                    $new_level_arr = 'V';
//                }
//                $new_user_segment_options[] = $new_level_arr;
//            }
//            if($set_type ==6){
//                $final_info['A'] = $ariya_car_tmp;
//                $ariya_car = input('ariya_car');
//                if(!empty(array_sum($ariya_car))){
//                    $new_user_segment_options[] ="P";
//                }
//            }
//            $new_user_segment_options = implode(',',$new_user_segment_options);
//
//            $full_discount_rules = json_encode($final_info);
//            $cm_level  = input('cm_level');
//            $none_car  = input('none_car');
//            $nissan_car = input('nissan_car');
            $cm_level  = $post['cm_level'];
            $none_car  = $post['none_car'];
            $nissan_car  = $post['nissan_car'];
            $none_car_tmp = [];
            $nissan_car_tmp = [];
            $ariya_car_tmp = [];
            foreach($cm_level as $ck=>$m_level_item){
                if(!empty($none_car[$ck])){
                    $none_car_tmp[] = [$m_level_item,$none_car[$ck]];
                }
                if(!empty($nissan_car[$ck])) {
                    $nissan_car_tmp[] = [$m_level_item, $nissan_car[$ck]];
                }
                if($set_type == 6){
                    $ariya_car  = input('ariya_car');
                    $ariya_car_tmp[] =[$m_level_item,$ariya_car[$ck]];
                }
            }
            $new_user_segment_options = [];
            if(!empty($none_car_tmp)) $final_info['NONE'] = array_reverse($none_car_tmp);
            if(!empty(array_sum($none_car))) $new_user_segment_options[] = "NONE";

            if(!empty($nissan_car_tmp)){
                if($set_type == 5){
                    $new_level_arr = 'N';
                    $final_info[$new_level_arr] = array_reverse($nissan_car_tmp);

                }
                if($set_type == 7){
                    $new_level_arr = 'V';
                    $final_info[$new_level_arr] = array_reverse($nissan_car_tmp);
                }
                if($set_type == 6){
                    $new_level_arr = 'N';
                    $final_info[$new_level_arr] = array_reverse($nissan_car_tmp);
                }
                $new_user_segment_options[] = $new_level_arr;
            }

            if($set_type ==6){
                $final_info['P'] = array_reverse($ariya_car_tmp);
//                $ariya_car = input('ariya_car');
                $ariya_car = $post['ariya_car'];
                if(!empty(array_sum($ariya_car))){
                    $new_user_segment_options[] ="P";
                }
            }
            $new_user_segment_options = implode(',',$new_user_segment_options);

            $full_discount_rules = json_encode($final_info);
        }

        $full_discount_model  = new DbFullDiscount();
        $old_commodity_id_arr = (new DbFullDiscountCommDlr())->where(['discount_activity_id' => $id])->column('commodity_id');
        $new_commodity_id_arr = array_column($sku_list, 'commodity_id');
        $rm_commodity_id_arr  = array_diff($old_commodity_id_arr, $new_commodity_id_arr);

        $act_status                   = $full_discount_model->getActStatus($start_time, $end_time);
        $data['id']                   = $id;
        $data['user_segment']         = $user_segment;
        $data['user_segment_options'] = $new_user_segment_options;
        $data['activity_title']       = $activity_title;
        $data['start_time']           = $start_time;
        $data['end_time']             = $end_time;
        $data['tag']                  = $post['tag'];
        $data['act_status']           = $act_status;
        $data['set_type']             = $set_type;
        $data['sku_list']             = $sku_list;
        $data['is_enable']            = $is_enable;
        $data['card_available']       = $card_available;
        $data['full_discount_rules']  = $full_discount_rules;
        $data['up_down_channel_dlr']  = $up_down_channel_dlr;
        $data['up_down_channel_name'] = implode(',', $post['up_down_channel_name'] ?? []);

        $data['e3s_activity_id']       = $post['e3s_activity_id'] ?? 0;
        $data['settlement_rule_id']    = $post['settlement_rule_id'];
        $data['settlement_rule_name']  = $post['settlement_rule'];
        $data['settlement_rule_type']  = $post['settlement_rule_type'];
        $data['settlement_rule_value'] = $post['settlement_rule_value'];
        $data['act_sett_standard']     = $post['act_sett_standard'];
        $data['discount_type']         = $post['discount_type'] ?? 1;
        $data['purchase_number']       = $post['purchase_number'] ?? 0;
        $data['rel_card_ids']          = $post['rel_card_ids'] ?? '';
        $data['modifier']             = $this->admin_info['username'];
        $data['gather_id']            = $gather_id;
        $data['is_pv_subsidy']        = $is_pv_subsidy;
        $data['theme_name']           = $theme_name;
        $e3s_activity = input('e3s_activity', '');
        if (!empty($e3s_activity)){
            $data['e3s_activity_name'] = explode(' | ', $e3s_activity)[1];
        }


        $data['created_dlr_code'] = $this->admin_info['dlr_code'];
        if ($is_enable == 1) {
            $date = date('Y-m-d H:i:s');
            //判断是否已经参加了预售
            $list = (new DbPreSale())->getIsPreProduct(
                $sku_list, [
                    'a.is_enable'      => ['=', 1],
                    'a.front_s_time'   => ['<', $date],
                    'a.balance_e_time' => ['>', $date],
                    'a.set_type'       => $set_type,
                ]
            );
            foreach ($list as $v) {
                if ($v['is_pre'] == 1) print_json(1, $v['commodity_name'] . '已参加预售活动');
            }
            $where_time = ['start_time'=>$start_time, 'end_time'=>$end_time];
            $disQuery = [
                'a.id'         => ['<>', $id],
                'a.is_enable'  => ['=', 1],
//                'a.start_time' => ['<', $end_time],
//                'a.end_time'   => ['>', $start_time],
                'a.set_type'   => $set_type,
                'a.act_status' => ['in', [1, 2]],
            ];
            if ($data['discount_type'] == 1) {
                $disQuery['a.discount_type'] = ['<>', 2];
            }
            if ($data['discount_type'] == 2) {
                $disQuery['a.discount_type'] = ['<>', 1];
                $list                        = (new DbLimitDiscount())->getIsLimitProduct(
                    $sku_list, $disQuery, $where_time
                );
                foreach ($list as $v) {
                    if ($v['is_limit'] == 1) print_json(1, $v['commodity_name'] . '工时类型已参加限时折扣活动');
                }
            }
            //是否满优惠
            $list = (new DbFullDiscount())->getIsFullDiscount(
                $sku_list, $disQuery,$where_time
            );

            foreach ($list as $v) {
                if ($v['is_full'] == 1) print_json(1, $v['commodity_name'] . '已参加其它满减活动');
            }


            //判断是否已经参加秒杀
            $list = (new DbSeckill())->getIsSeckillProduct($sku_list, [
                'a.is_enable'  => ['=', 1],
//                'a.start_time' => ['<', $end_time],
//                'a.end_time'   => ['>', $start_time],
                'a.set_type'   => $set_type,
                'a.act_status' =>['in',[1,2]]
            ],$where_time);
            foreach ($list as $v) {
                if ($v['is_seckill'] == 1) print_json(1, $v['commodity_name'] . '已参加秒杀活动');
            }

        }

        $result = $full_discount_model->editFullDiscountActivityLive($data, $set_type, $home);
        if (empty($result['status'])) {
            if (($act_status == 2) && !empty($is_enable)) {
                $full_commodity_arr = $full_discount_model->getAllFullDiscount([
                    'where' => ['a.id' => $id],
                    'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                ]);
                $this->doHook('update', $full_commodity_arr, $id);
            } else if (empty($is_enable)) {
                $full_commodity_arr = $full_discount_model->getAllFullDiscount([
                    'where' => ['a.id' => $id],
                    'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                ]);
                $this->doHook('delete', $full_commodity_arr, $id);
            }
            if (!empty($rm_commodity_id_arr)) {
                $this->doHook('delete', $rm_commodity_id_arr, $id);
            }
        }
        print_json($result['status'], $result['msg']);
    }

    public function edit()
    {
        $id = input('post.id');
        $activity_title = input('post.activity_title') or print_json(1, '活动名称不能为空');
        $money = input('post.money') or print_json(1, '活动金额不能为空');
        $start_time = input('post.start_time') or print_json(1, '活动起始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '活动结束时间不能为空');
        $is_preferential_money = input('post.is_preferential_money');
        $preferential_money    = input('post.preferential_money');
        $is_preferential_card  = input('post.is_preferential_card');
        $preferential_card_id  = input('post.preferential_card_id');
        $set_type              = input('post.set_type');
        $purchase_number       = input('post.purchase_number', 0);
        if (empty($_POST['sku_list'])) {
            print_json(1, '商品不能为空');
        }
        $sku_list                      = $_POST['sku_list'];
        $full_discount_model           = new DbFullDiscount();
        $data['id']                    = $id;
        $data['activity_title']        = $activity_title;
        $data['money']                 = floatval($money);
        $data['start_time']            = $start_time;
        $data['end_time']              = $end_time;
        $data['is_preferential_money'] = intval($is_preferential_money);
        $data['is_preferential_card']  = intval($is_preferential_card);
        $data['preferential_money']    = floatval($preferential_money);
        $data['preferential_card_id']  = intval($preferential_card_id);
        $data['set_type']              = $set_type;
        $data['created_dlr_code']      = $set_type == 3 ? 'NISSAN' : $this->admin_info['dlr_code'];
        $data['sku_list']              = $sku_list;
        $data['purchase_number']       = $purchase_number;
        $result                        = $full_discount_model->editFullDiscountActivity($data, $set_type);
        print_json($result['status'], $result['msg']);
    }

    public function delete()
    {
        $id    = input('post.id');
        $model = new DbFullDiscount();
        $where = [
            'id'               => intval($id),
            'set_type'         => $this->admin_info['type'],
            'created_dlr_code' => $this->admin_info['dlr_code']
        ];
        try {
            $model->startTrans();
            $row    = $model->getOneByPk($id);
            $result = $model->where($where)->delete();
            $where  = [
                'discount_activity_id' => intval($id)
            ];
            $model->table('t_db_full_discount_comm_dlr')->where($where)->delete();
            $model->commit();
            if ($row['set_type'] == 5) {
                $full_commodity_arr = $model->getAllFullDiscount([
                    'where' => ['a.id' => $id],
                    'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                ]);
                $this->doHook('delete', $full_commodity_arr, $id);
            }
            print_json(0, '删除成功。');
        } catch (Exception $exception) {
            $model->rollback();
            print_json(1, $exception->getMessage());
        }
    }

    private function doHook($type = 'delete', $commodity_arr = [], $act_id = 0)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;

        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        $activity = DbFullDiscount::where('id', $act_id)->find();

        if (!empty($activity['up_down_channel_dlr'])) {

            $up_down_arr = explode(',', $activity['up_down_channel_dlr']);
            $shelves_ni  = array_intersect(DbDlr::$ni_arr, $up_down_arr);
            $shelves_pz  = array_intersect(DbDlr::$pz1a_arr, $up_down_arr);
            $shelves_qc  = array_intersect(DbDlr::$qc_arr, $up_down_arr);

            if (!empty($shelves_ni)) {
                $shelves_type = DbDlr::$ni_shelves;
            } else if (!empty($shelves_pz)) {
                $shelves_type = DbDlr::$pz1a_shelves;
            } else if (!empty($shelves_qc)) {
                $shelves_type = DbDlr::$qc_shelves;
            } else {
                return false;
            }

            switch ($type) {
                case 'add':
                case 'update':
                    # 添加商品不需要刷活动信息
                    foreach ($commodity_arr as $item_one) {
                        $item_one['shelves_type'] = $shelves_type;
                        Hook::listen('flat_full_discount', $item_one);
                    }
                    break;
                case 'delete':
                    # 删除就只需要将数据删除就ok
                    foreach ($commodity_arr as $item_one) {
                        $del_params = [
                            'shelves_type' => $shelves_type,
                            'del_dis'      => true,
                            'act_id'       => $act_id,
                            'commodity_id' => empty($item_one['commodity_id']) ? $item_one : $item_one['commodity_id']
                        ];
                        Hook::listen('flat_full_discount', $del_params);
                    }
                    break;
            }

            $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
            Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);

            return $result;
        }

        return false;
    }

    /**
     * 获取卡券
     */
    public function ajaxGetCard2(){
        $this->_checkAjax();
        $model = new DbCard();
        $card_name = input('get.card_name');
        $set_type = input('set_type', 0);
        $where = [];
        $shelves_type = 5;
        if($set_type == 5){
            $where['up_down_channel_dlr'] =[['notlike','%PZ1ASM%'],['notlike','%PZ1AAPP%'],['notlike','%QCSM%'],['notlike','%QCAPP%'],'and'];
        }elseif($set_type == 6){
            $shelves_type = 6;
            $where[] =[['exp','FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'],['exp','FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'],'or'];
        }elseif($set_type == 7){
            $shelves_type = 7;
            $where[] =[['exp','FIND_IN_SET("QCSM",up_down_channel_dlr)'],['exp','FIND_IN_SET("QCAPP",up_down_channel_dlr)'],'or'];
        }
        if(!empty($card_name))
            $where['card_name'] = ['like','%'.$card_name.'%'];

        $where['shelves_type'] = $shelves_type;
        $where['is_enable'] = 1;
        $where['type'] = 2;
        $where['act_status'] = ['in', [1, 2, 3]];
        $params = [
            'where'=>$where,
            'order'=>'id desc',
            'field'=>'id,is_enable,act_status,(case card_type when 1 then "代金券" when 2 then "折购券" when 3 then "兑换券" when 4 then "满减券" when 5 then "优惠券" end) as card_type_name,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to,available_count,up_down_channel_name',
            'pagesize'=>input('get.pagesize'),
            'query'=>input('get.')
        ];
        $list = $model->getListPaginate($params);
        foreach ($list as $key=>$value){
            switch ($value['date_type']){
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'].'至'.$value['validity_date_end'];
                    break;
                case 2:
                    if($value['fixed_begin_term']==0){
                        $list[$key]['validity_date'] = "自领取当天有效，有效期".$value['fixed_term'].'天';
                    }elseif ($value['fixed_begin_term']==1){
                        $list[$key]['validity_date'] = "领取后".$value['fixed_term'].'天后有效';
                    }
                    break;
                default:
                    break;
            }
            $value['status_name'] = empty($value['is_enable']) ? '已关闭' : $model->cardStatus()[$value['act_status']];
        }
        print_json(0,'',$list);
    }

}
