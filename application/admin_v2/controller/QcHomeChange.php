<?php
/**
 * Created by <PERSON>pstorm
 * User: caiweiwei
 * Date: 2020/4/27
 */

namespace app\admin_v2\controller;

use app\common\model\bu\BuCheapSuitCommodity;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCrowdfund;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDraw;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbHomeSmallPage;
use app\common\model\db\DbHomeSmallPageGroup;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSmExpand;
use app\common\net_service\NetGoods;
use app\common\port\connectors\Tag;
use app\common\service\BaseDataService;
use app\common\model\db\DbHomeSm;
use app\common\model\db\DbSpecialSm;
use think\Collection;
use think\Controller;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\Hook;
use think\Log;
use think\Request;

/**
 * 自定义首页接口#test
 * Class HomeChange
 * @package app\admin_v2\controller
 */
class QcHomeChange extends Common
{
    private $homeSmallPageGroup;
    private $homeSmallPage;
    private $dlr;
    private $commodity;
    private $operation_type;
    protected $admin_info;
    private $base_service;
    private $dlr_code;
    private $set_type;
    private $commodityType;
    protected $pageType;    //页面类型

    function __construct()
    {
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization");
        header('Access-Control-Allow-Methods: GET,POST,PUT,DELETE,OPTIONS,PATCH');
//
//        $admin = [
//            'id'=>8, 'dlr_code'=>'NISSAN', 'username'=>'admin',
//            'password'=>'e10adc3949ba59abbe56e057f20f883e', 'gid'=>1, 'is_enable'=>1,
//            'created_date'=>'2017-06-28 15:30:15','creator'=>null, 'last_updated_date'=>'2017-09-14 15:57:22',
//            'modifier'=>'admin','type'=>'1', 'dlr_name'=>'东风日产车数据营销'
//        ];
//        session('admin_info',$admin);

        $this->admin_info = session('admin_info');
        parent::__construct();
        $this->admin_info         = session('admin_info');
        $this->homeSmallPageGroup = new DbHomeSmallPageGroup();
        $this->homeSmallPage      = new DbHomeSmallPage();
        $this->operation_type     = DbHomeSmallPageGroup::operationType();
        $this->base_service       = new BaseDataService();
        $this->commodity          = new DbCommodity();
        $this->commodityType      = new DbCommodityType();
        $this->dlr                = new DbDlr();
        $this->dlr_code           = $this->admin_info['dlr_code'];
        $this->set_type           = $this->admin_info['type'];
        $this->pageType           = input('page_type', DbHomeSm::DEFAULT_PAGE); #NI+ or 智趣
        $this->shelves_type       = $this->pageType == DbHomeSm::DEFAULT_PAGE ? 5 : 7;
        $this->tag                = $this->pageType == DbHomeSm::DEFAULT_PAGE ? 'tag' : 'tag_qcsm'; # oneapp or 启辰app
        $this->channel_type       = $this->pageType == DbHomeSm::DEFAULT_PAGE ? 'GWSM' : 'QCSM';
    }

    /**
     * 显示专营店设置模板列表
     * Created 2020/6/11 by Phpstorm
     * author: caiweiwei
     * @return \think\response\View
     */
    public function list_list()
    {
        $this->assign('admin_type', $this->admin_info['type']);
        return view('index');
    }

    /**
     * 获取模板接口
     * Created 2020/5/7 by Phpstorm
     * author: caiweiwei
     */
    public function home_lists()
    {
        $search   = input('search');      //接收前端传过来的搜索条件
        $page     = input('page', 1);     //页码 默认第一页
        $paginate = input('paginate', 10);//一页显示多少条数据 默认显示10条数据
        $params   = [
            'field'    => 'id,name,apply_dlr_code,created_at',
            'where'    => [],
            'order'    => 'created_at desc',
            'page'     => $page,
            'paginate' => $paginate
        ];
        //查询条件
        if (!empty($search)) {
            $params['where']['apply_dlr_code'] = ['like', "%$search%"];
        }
        $list = $this->homeSmallPageGroup->getListPaginate($params);

        foreach ($list as $key => $value) {
            $value->apply_dlr_code = $this->formatDlrOut($value['apply_dlr_code']);
            unset($value->created_at);
        }

        return print_json('0', 'ok', $list);
    }

    /**
     * 获取经销商分类和经销商列表
     * Created 2020/5/7 by Phpstorm
     * author: caiweiwei
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function get_dlr_list()
    {
        $all_list     = $this->homeSmallPageGroup->where(null)->field('apply_dlr_code')->select();
        $selected_dlr = [];
        foreach ($all_list as $key => $val) {
            $apply_dlr_code = explode(',', $this->formatDlrOut($val['apply_dlr_code']));
            $selected_dlr   = array_merge($selected_dlr, $apply_dlr_code);     //合并数组
        }
        //专营店类型的专营店列表
        $where_column                      = ['where' => ['is_enable' => 1, 'type' => 2], 'column' => 'dlr_code'];
        $where_column['where']['dlr_code'] = ["NOT IN", $selected_dlr];
        $dlr_code                          = $this->dlr->getColumn($where_column);

        if (!empty($dlr_code)) {
            $baseService = new BaseDataService();
            $dlr_data    = json_decode($baseService->getDlrList(0, $dlr_code));
        } else {
            $dlr_data = ['dlr_list' => [], 'label_list' => []];
        }
        print_json(0, 'ok', $dlr_data);
    }

    /**
     * 过滤选择过的专营店
     * Created 2020/6/11 by Phpstorm
     * author: caiweiwei
     */
    public function getDlrList()
    {
        //一家店只能在一个设置里
        $all_list     = $this->homePageGroup->where(null)->field('apply_dlr_code')->select();
        $selected_dlr = [];
        foreach ($all_list as $key => $val) {
            $apply_dlr_code = explode(',', $this->formatDlrOut($val['apply_dlr_code']));
            $selected_dlr   = array_merge($selected_dlr, $apply_dlr_code);     //合并数组
        }
        //专营店类型的专营店列表
        $where_column                      = ['where' => ['is_enable' => 1, 'type' => 2], 'column' => 'dlr_code'];
        $where_column['where']['dlr_code'] = ["NOT IN", $selected_dlr];
        $dlr_code                          = $this->dlr->getColumn($where_column);
        if (!empty($dlr_code)) {
            $baseService = new BaseDataService();
            $dlr_data    = $baseService->getDlrList(0, $dlr_code);
        } else {
            $dlr_data = json_encode(['dlr_list' => [], 'label_list' => []]);
        }
        print_json(0, 'ok', $dlr_data);
    }

    /**
     * 保存模板 或者 复制模板
     * Created 2020/5/7 by Phpstorm
     * author: caiweiwei
     * @throws \think\exception\PDOException
     */
    public function group_save()
    {
        $input_data = input();
        $action     = input('action', '');   //操作：copy复制
        $copy_id    = null;
        if ($this->admin_info['type'] != 1) print_json(1, '非法操作');

        if (!empty($action)) {
            if ($action == 'copy') {
                $copy_id = input('copy_id') or print_json(1, '复制参数丢失');
            }
        }

        if (empty($input_data['name'])) {
            print_json(1, '请输入首页名称', []);
        }

        if (empty($input_data['apply_dlr_code'])) {
            print_json(1, '请选择应用专营店');
        } else {
            $input_data['apply_dlr_code'] = ',' . $input_data['apply_dlr_code'] . ',';
        }

        $input_data['creator'] = $this->admin_info['username'];
        $model_group           = new DbHomeSmallPageGroup();
        try {
            //开启事物
            $model_group->startTrans();
            if ($this->homeSmallPageGroup->getOne(['where' => ['name' => $input_data['name']]])) {
                print_json(1, '已存在名称相同的首页');
            }

            //保存首页模板
            $res = $this->homeSmallPageGroup->allowField(true)->isUpdate(false)->save($input_data);
            if (!$res) {
                print_json(1, '保存失败');
            }
            //新增后的模板id
            $new_group_id = $this->homeSmallPageGroup->getLastInsID();
            //复制首页
            if ($action == 'copy') {
                $field     = 'dlr_sync,group_id,dlr_sync,data_json,preview_data_json,sort,is_enable';
                $page_list = $this->homeSmallPage->where(['group_id' => $copy_id])->field($field)->select();

                if (!empty($page_list)) {
                    foreach ($page_list as $key => $val) {
                        $copy_val        = [
                            'group_id'          => $new_group_id,
                            'dlr_sync'          => $val['dlr_sync'],
                            'data_json'         => $val['data_json'],
                            'preview_data_json' => $val['preview_data_json'],
                            'sort'              => $val['sort'],
                            'is_enable'         => $val['is_enable'],
                        ];
                        $page_list[$key] = $copy_val;
                    }

                    $res = $this->homeSmallPage->insertAll($page_list);
                    if (!$res) {
                        print_json(1, '保存失败');
                    }
                }

            }
            //提交事物
            $model_group->commit();

        } catch (\Exception $e) {
            //回滚事物
            $model_group->rollback();
            print_json(1, $e->getMessage());
        }

        //缓存清理行为
        $params = 'z_xt_snow_home_index';
        Hook::exec('app\\admin_v2\\behavior\\CacheClear', 'run', $params);

        print_json(0, '保存成功');
    }

    /**
     * 获取模板信息
     * Created 2020/5/7 by Phpstorm
     * author: caiweiwei
     */
    public function get_group()
    {
        $id = input('id') or print_json(1, '参数丢失');
        $action = input('action');
        if ($this->admin_info['type'] != 1) print_json(1, '非法操作');

        $group_info = $this->homeSmallPageGroup->getOneByPk($id);

        if (empty($group_info)) {
            print_json(1, '数据不存在');
        }
        $group_info['apply_dlr_code'] = $this->formatDlrOut($group_info['apply_dlr_code']);
        $dlr_name                     = $this->dlr->getColumn(['where' => ['dlr_code' => ['in', $group_info['apply_dlr_code']], 'is_enable' => 1], 'column' => 'dlr_name']);
        $group_info['apply_dlr_name'] = implode(',', $dlr_name);
        unset($group_info['sort_order'], $group_info['is_enable'], $group_info['created_at'],
            $group_info['creator'], $group_info['last_updated_date'], $group_info['modifier']);
        print_json(0, 'ok', $group_info);
    }

    /**
     * 修改模板
     * Created 2020/5/7 by Phpstorm
     * author: caiweiwei
     */
    public function group_set()
    {
        $input_data = input();
        if (empty($input_data['id'])) {
            print_json(1, '参数丢失');
        }
        if (empty($input_data['apply_dlr_code'])) {
            print_json(1, '请选择应用专营店');
        } else {
            $input_data['apply_dlr_code'] = ',' . $input_data['apply_dlr_code'] . ',';
        }
        if (empty($input_data['name'])) {
            print_json(1, '请输入首页名称');
        }
        if ($this->admin_info['type'] != 1) print_json(1, '非法操作');

        $input_data['modifier'] = $this->admin_info['username'];
        $is_eq                  = $this->homeSmallPageGroup->getOne(['where' => ['name' => $input_data['name']]]);
        if (!empty($is_eq) && $is_eq['id'] != $input_data['id']) {
            print_json(1, '已存在名称相同的首页模板');
        }
        //保存首页模板
        $res = $this->homeSmallPageGroup->allowField(true)->isUpdate(true)->save($input_data, $input_data['id']);
        if (!$res) {
            print_json(1, '设置权限失败：' . $this->homeSmallPageGroup->getError());
        }
        print_json(0, '设置权限成功');
    }

    /**
     * 删除模板
     * Created 2020/5/8 by Phpstorm
     * author: caiweiwei
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function group_delete()
    {
        if (\request()->method() != 'DELETE') {
            print_json(1, '非法请求');
        }

        $id = input('id') or print_json(1, '参数丢失');
        if ($this->admin_info['type'] != 1) print_json(1, '非法操作');

        //删除首页数据
        $home_list = $this->homeSmallPage->where(['group_id' => $id])->field('id')->select();
        foreach ($home_list as $key => $val) {
            $this->homeSmallPage->where(['id' => $val['id']])->delete();
        }
        //删除首页组
        $res = $this->homeSmallPageGroup->where(['id' => $id])->delete($id);
        if (!$res) print_json(1, '删除失败：' . $this->homeSmallPageGroup->getError());
        print_json(0, '删除成功');
    }

    /**
     * 编辑模板
     * Created 2020/6/11 by Phpstorm
     * author: caiweiwei
     */
    public function group_edit()
    {

        $id = input('id') or print_json(1, '参数丢失');
        if ($this->admin_info['type'] != 1) print_json(1, '非法操作');
        $list = $this->homeSmallPageGroup->getOne(['where' => ['id' => $id]]);

        if (!$list) {
            print_json(1, '非法模板');
        }
        $list_group             = $this->homeSmallPage->getOne(['where' => ['group_id' => $id]]);
        $data['list']           = json_decode($list_group['data_json'], true);//json数据
        $data['operation_type'] = $this->operation_type;                      //类型
        //缓存清理行为
        $params = 'z_xt_snow_home_index';
        Hook::exec('app\\admin_v2\\behavior\\CacheClear', 'run', $params);

        $data['url'] = $_SERVER['HTTP_HOST'] . url('admin_v2/preview/preview_list?id=') . $id;
        print_json(0, '获取成功', $data);
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetCommodityList()
    {
        $group_id             = input('group_id');
        $commodity_name       = input('commodity_name');
        $comm_parent_id       = input('comm_parent_id');
        $sub_comm_parent_id   = input('sub_comm_parent_id');
        $three_comm_parent_id = input('three_comm_parent_id');
        $params['where']      = [];
        $params['query']      = [];
        if (!empty($commodity_name)) {
            $params['where']['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        if (!empty($comm_parent_id)) {
            if (empty($sub_comm_parent_id)) {
                $comm_type_column = $this->commodityType->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                $array            = [];
                foreach ($comm_type_column as $key => $value) {
                    $comm_type_two = $this->commodityType->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $value], 'column' => 'id']);
                    $array         = array_merge($array, $comm_type_two);
                }
            } else {
                if (empty($three_comm_parent_id)) {
                    $array = $this->commodityType->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_parent_id], 'column' => 'id']);
                } else {
                    $array = $three_comm_parent_id;
                }
            }
            $params['where']['a.comm_type_id'] = ['in', $array];
        }

        $params['field']    = 'a.id,a.commodity_name,a.comm_type_id,a.set_type,a.create_dlr_code,
                c.comm_type_name,c.comm_parent_id,
                b.shelves_type';
        $params['order']    = 'a.id desc';
        $params['query']    = input('get.');
        $params['pagesize'] = input('pagesize');
        $params['where_1']  = '';
        $set_type           = $this->admin_info['type'];
        $dlr_code           = '';
        if ($set_type == 2) {
            $dlr_code = $this->admin_info['dlr_code'];
        } else if ($set_type == 1) {
            $home    = $this->homeSmallPageGroup->getOneByPk($group_id);
            $arr_dlr = explode(',', $this->formatDlrOut($home['apply_dlr_code']));
            foreach ($arr_dlr as $key => $val) {
                if ($key == count($arr_dlr) - 1) {
                    $dlr_code .= "'$val'";
                } else {
                    $dlr_code .= "'$val',";
                }
            }
        }
        $list            = $this->commodity->getHomePageShelvesListByDlr($set_type, $dlr_code, 3, $params);
        $comm_type_modal = new  DbCommodityType();
        foreach ($list as $key => $value) {
            $list[$key]['comm_type_one_name'] = $comm_type_modal->getOneByPk($comm_type_modal->getOneByPk($value['comm_parent_id'])['comm_parent_id'])['comm_type_name'];
            $list[$key]['comm_type_two_name'] = $comm_type_modal->getOneByPk($value['comm_parent_id'])['comm_type_name'];
            $list[$key]['url']                = url('index_v5/goods/detail', array('id' => $value['id'], 's_type' => 3));
        }
        $comm_parent_list2 = $this->commodityType->getCommTypeName();

        $res                      = [];
        $res['list']              = $list;
        $res['comm_parent_list2'] = $comm_parent_list2;
        print_json(0, '', $res);
    }

    /**
     * 获取选择选项数据
     * Created 2020/6/11 by Phpstorm
     * author: caiweiwei
     */
    public function get_pejoration_list()
    {
        $operation_type        = input('get.operation_type'); //类型
        $condition             = input('get.');
        $params['where']['id'] = $condition['group_id'];
        $official_micro        = $this->homeSmallPageGroup->getOne($params);
        $buying                = $this->set_type;
        if (strpos($official_micro['apply_dlr_code'], 'GWSC')) {
            //存在官微
            $buying = 3;
        }

        if ($operation_type == 1) {
            $res = $this->ajaxGetCommodityList();
        } elseif ($operation_type == 6) {
            $res = $this->getGroupBuying($this->set_type, $this->dlr_code, 3, $buying);
        } elseif ($operation_type == 7) {
            $res = $this->GetTimeLimit($this->set_type, $this->dlr_code, 3, $buying);
        } elseif ($operation_type == 8) {
            $res = $this->GetDiscount($this->set_type, $this->dlr_code, 3, $buying);
        } else {
            $res = $this->base_service->getHomePageSelect($operation_type, $this->dlr_code, $this->set_type, 3);
        }
        if (!isset($res['list'])) {
            $data['list'] = $res;
            if (isset($res['comm_parent_list1'])) {
                $data['comm_parent_list1'] = $res['comm_parent_list1'];
            }
            if (isset($res['comm_parent_list2'])) {
                $data['comm_parent_list2'] = $res['comm_parent_list2'];
            }
        } else {
            $data = $res;
        }
        print_json(0, '', $data);
    }

    /**
     * 保存模板组件
     * Created 2020/6/11 by Phpstorm
     * author: caiweiwei
     * @throws \think\exception\PDOException
     */
    public function group_add()
    {
        $data_post = input('post.');
        $id = input('post.id') or print_json(1, '参数丢失');//模板编号;
        $home_page               = $data_post['list'];
        $input_data['data_json'] = json_encode($home_page);
        $input_data['group_id']  = $id;
        $input_data['creator']   = $this->admin_info['username'];
        if (!$this->homeSmallPageGroup->getOne(['where' => ['id' => $id]])) {
            print_json(1, '非法模板');
        }
        $model_group = new DbHomeSmallPage();
        try {
            //开启事物
            $model_group->startTrans();
            //保存首页模板
            if (!$this->homeSmallPage->getOne(['where' => ['group_id' => $id]])) {
                $res = $this->homeSmallPage->allowField(true)->isUpdate(false)->save($input_data);
            } else {
                $where['group_id'] = $id;
                $res               = $this->homeSmallPage->allowField(true)->isUpdate(true)->update($input_data, $where);
            }
            if (!$res) {
                print_json(1, '保存失败');
            }
            //提交事物
            $model_group->commit();

        } catch (\Exception $e) {
            //回滚事物
            $model_group->rollback();
            print_json(1, $e->getMessage());
        }

        //缓存清理行为
        $params = 'z_xt_snow_home_index';
        Hook::exec('app\\admin_v2\\behavior\\CacheClear', 'run', $params);

        print_json(0, '保存成功');
    }

    /**
     * 预览保存
     * Created 2020/6/11 by Phpstorm
     * author: caiweiwei
     * @throws \think\exception\PDOException
     */
    public function group_preview_add()
    {
        $data_post = input('post.');
        $id = input('post.id') or print_json(1, '参数丢失');//模板编号;
        $home_page                       = $data_post['list'];
        $input_data['preview_data_json'] = json_encode($home_page);
        $input_data['group_id']          = $id;
        $input_data['creator']           = $this->admin_info['username'];
        if (!$this->homeSmallPageGroup->getOne(['where' => ['id' => $id]])) {
            print_json(1, '非法模板');
        }
        $model_group = new DbHomeSmallPage();
        try {
            //开启事物
            $model_group->startTrans();
            //保存首页模板
            if (!$this->homeSmallPage->getOne(['where' => ['group_id' => $id]])) {
                $res = $this->homeSmallPage->allowField(true)->isUpdate(false)->save($input_data);
            } else {
                $where['group_id'] = $id;
                $res               = $this->homeSmallPage->allowField(true)->isUpdate(true)->update($input_data, $where);
            }
            if (!$res) {
                print_json(1, '保存失败');
            }
            //提交事物
            $model_group->commit();

        } catch (\Exception $e) {
            //回滚事物
            $model_group->rollback();
            print_json(1, $e->getMessage());
        }
        print_json(0, '保存成功');
    }

    /**
     * 根据父级id获取 商品分类
     */
    public function get_comm_by_parent_id()
    {
        $comm_parent_id = input('comm_parent_id');
        print_json(0, '', $this->commodityType->getCommodityByParentId($comm_parent_id));
    }

    /**
     * 格式化专营店编码，去除分隔符号：，
     * Created 2020/5/5 by Phpstorm
     * author: caiweiwei
     * @param $str
     * @return false|string
     */
    private function formatDlrOut($str)
    {
        if (!empty($str)) {
            $str = substr($str, 1, strlen($str) - 2);
        }
        return $str;
    }

    /**
     * 查询多人拼团
     * Created 2020/6/11 by Phpstorm
     * author: caiweiwei
     * @param $set_type
     * @param $dlr_code
     * @param $home_set_type
     * @param $buying 3 官微
     * @return \think\Paginator
     */
    public function getGroupBuying($set_type, $dlr_code, $home_set_type, $buying)
    {
        $page_size            = 5;
        $commodity_name       = input('commodity_name');
        $title                = input('title');
        $where['a.is_enable'] = 1;
        if (!empty($commodity_name)) {
            $where['c.commodity_name'] = ['like', "%$commodity_name%"];
        }
        if (!empty($title)) {
            $where['a.title'] = ['like', "%$title%"];
        }

        if ($set_type == 2) {
            $where['a.created_dlr_code'] = $dlr_code;
        } elseif ($set_type == 2) {
            $where['a.created_dlr_code'] = 'NISSAN';
        }

        //增加这个set_type  created_dlr_code改为2查询专营店，其他的都为NISSAN lzx 20180814
        $where['a.set_type'] = $buying;

        $where['a.end_time'] = ['>', date('Y-m-d H:i:s')];
        $fight_modal         = new DbFightGroup();
        $list                = $fight_modal->getFightCommodityList(['where' => $where, 'pagesize' => $page_size, 'field' => 'a.id,title,a.start_time,a.end_time,c.commodity_name,b.commodity_id,b.commodity_set_id,d.shelves_type']);
        foreach ($list as $key => $val) {
            $list[$key]['valid_time'] = date('Y-m-d', strtotime($val['start_time'])) . '-' . date('Y-m-d', strtotime($val['end_time']));
            if ($home_set_type == 3) {
                $list[$key]['url'] = url('index_v5/goods/detail', array('id' => $val['commodity_id'], 'group_id' => $val['id'], 's_type' => 3));
            }
        }
        return $list;
    }

    /**
     * 区分官微和专营店限时折扣
     * Created 2020/6/11 by Phpstorm
     * author: caiweiwei
     * @param $set_type
     * @param $dlr_code
     * @param $home_set_type
     * @param $buying
     * @return \think\Paginator
     */
    public function GetTimeLimit($set_type, $dlr_code, $home_set_type, $buying)
    {
        $page_size            = 5;
        $commodity_name       = input('commodity_name');
        $title                = input('title');
        $where['a.is_enable'] = 1;
        if (!empty($commodity_name)) {
            $where['c.commodity_name'] = ['like', "%$commodity_name%"];
        }
        if (!empty($title)) {
            $where['a.title'] = ['like', "%$title%"];
        }
        if ($set_type == 2) {
            $where['a.created_dlr_code'] = $dlr_code;
        } else {
            $where['a.created_dlr_code'] = 'NISSAN';
        }
        //增加这个set_type  created_dlr_code改为2查询专营店，其他的都为NISSAN lzx 20180814
        $where['a.set_type'] = $buying;

        $where['a.end_time'] = ['>', date('Y-m-d H:i:s')];
        $limit_modal         = new DbLimitDiscount();
        $list                = $limit_modal->getLimitCommodityList(['where' => $where, 'pagesize' => $page_size, 'field' => 'a.id,title,a.start_time,a.end_time,c.commodity_name,b.commodity_id,b.commodity_set_id,d.shelves_type']);
        foreach ($list as $key => $val) {
            $list[$key]['valid_time'] = date('Y-m-d', strtotime($val['start_time'])) . '-' . date('Y-m-d', strtotime($val['end_time']));
            if ($home_set_type == 3) {
                $list[$key]['url'] = url('index_v5/goods/detail', array('id' => $val['commodity_id'], 's_type' => 3));
            }
        }
        return $list;
    }

    /**
     * 区分官微和专营店满优惠
     * Created 2020/6/11 by Phpstorm
     * author: caiweiwei
     * @param $set_type
     * @param $dlr_code
     * @param $home_set_type
     * @param $buying
     * @return \think\Paginator
     */
    public function GetDiscount($set_type, $dlr_code, $home_set_type, $buying)
    {
        $page_size            = 5;
        $commodity_name       = input('commodity_name');
        $title                = input('title');
        $where['a.is_enable'] = 1;
        if (!empty($commodity_name)) {
            $where['c.commodity_name'] = ['like', "%$commodity_name%"];
        }
        if (!empty($title)) {
            $where['a.activity_title'] = ['like', "%$title%"];
        }
        if ($set_type == 1) {
            $where['a.created_dlr_code'] = $dlr_code;
        } else {
            $where['a.created_dlr_code'] = 'NISSAN';
        }
        //增加这个set_type  created_dlr_code改为2查询专营店，其他的都为NISSAN lzx 20180814
        $where['a.set_type'] = $buying;

        $where['a.end_time'] = ['>', date('Y-m-d H:i:s')];
        $limit_modal         = new DbFullDiscount();
        $list                = $limit_modal->getFullCommodityList(['where' => $where, 'pagesize' => $page_size, 'field' => 'a.id,activity_title as title,a.start_time,a.end_time,c.commodity_name,b.commodity_id,b.commodity_set_id,d.shelves_type']);
        foreach ($list as $key => $val) {
            $list[$key]['valid_time'] = date('Y-m-d', strtotime($val['start_time'])) . '-' . date('Y-m-d', strtotime($val['end_time']));
            if ($home_set_type == 3) {
                $list[$key]['url'] = url('index_v5/goods/detail', array('id' => $val['commodity_id'], 's_type' => 3));
            }
        }
        return $list;
    }

    /**
     * 车生活自定义小程序首页
     * @return \think\response\View
     */
    public function live()
    {
        return view('live');
    }

    public function get_find()
    {
        $home_info = DbHomeSm::field('id,data_json,draft_data_json,preview_data_json')->find($this->pageType);
        $data_json = json_decode($home_info['data_json'], true);
        foreach ($data_json as $key => $value) {
            if (in_array($value['type'], ['cAdvertising', 'cAdvertising1', 'cAdvertising2'])) {
                foreach ($value['attribute']['imgs'] as $k => $val) {
                    if (!isset($val['show_time'])) {
                        $data_json[$key]['attribute']['imgs'][$k]['show_time']['start_time'] = '';
                        $data_json[$key]['attribute']['imgs'][$k]['show_time']['end_time']   = '';
                    }
                }
            }
            if ($value['type'] == "cGoods") {
                foreach ($value['attribute'] as $val) {
                    if (!isset($val['show_time'])) {
                        $data_json[$key]['attribute']['show_time']['start_time'] = '';
                        $data_json[$key]['attribute']['show_time']['end_time']   = '';
                    }
                }
            }
        }
        $list['data_json']         = $data_json;
        $list['draft_data_json']   = json_decode($home_info['draft_data_json'], true);
        $list['preview_data_json'] = json_decode($home_info['preview_data_json'], true);
        if (!empty($list)) {
            print_json(0, '获取成功', $list);
        } else {
            print_json(1, '获取失败');
        }
    }

    /**
     * 获取全部分类
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function ajax_get_class()
    {
        $list = Db::name('db_commodity_type')
            ->where(['is_enable' => 1, 'level' => 1])
            ->field('id,comm_parent_id as parent_id,comm_type_name as class_name')
            ->select();

        foreach ($list as $key => $value) {
            $list[$key]['subclass'] = Db::name('db_commodity_type')
                ->where(['is_enable' => 1, 'level' => 2])
                ->where('comm_parent_id', $value['id'])
                ->field('id,comm_parent_id as parent_id,comm_type_name as class_name')
                ->select();
            foreach ($list[$key]['subclass'] as $k => $val) {
                $list[$key]['subclass'][$k]['subclass'] = Db::name('db_commodity_type')
                    ->where(['is_enable' => 1, 'level' => 3])
                    ->where('comm_parent_id', $val['id'])
                    ->field('id,comm_parent_id as parent_id,comm_type_name as class_name')
                    ->select();
            }
        }

        return print_json('0', 'ok', $list);
    }

    public function get_product()
    {
        $flat = new DbCommodityFlat();
        $list = $flat->getProduct($this->shelves_type, $this->channel_type, $this->tag);

        print_json(0, 'ok', $list);
    }

    /**
     *
     */
    public function edit()
    {
        $save  = [];
        $input = input();
        if (isset($input['data_json'])) {
            $data_json = $input['data_json'];
            Db::table('t_db_card_sm_log')->where(['log_type' => 1, 'page_type' => $this->pageType])->delete();
            foreach ($data_json as $key => $value) {
                if ($value['type'] == 'cCarousel') {
                    foreach ($value['attribute']['imgs'] as $k => $val) {
                        if (isset($val['tags'])) {
                            if (!empty($val['tags'])) {
                                $data                      = [];
                                $data['modelCode']         = $this->brandModelCode($input['page_type']);
                                $data['contentCreateTime'] = date("Y-m-d H:i:s");
                                $data['contentCreator']    = $this->admin_info['username'];
                                $data['contentDetail']     = $value['name'];
                                if ($val['contentId'] == 0) {
                                    $contentId                                             = date("YmH") . rand(1, 99);
                                    $data['contentId']                                     = $contentId;
                                    $data_json[$key]['attribute']['imgs'][$k]['contentId'] = $contentId;
                                } else {
                                    $data['contentId'] = $val['contentId'];
                                }
                                $data['contentPublishTime'] = date("Y-m-d H:i:s");
                                $data['contentType']        = '广告活动';
                                $data['departmentId']       = $val['department'][1];
                                $data['departmentName']     = empty($val['departmentLabel']) ? '' : $val['departmentLabel'][1];
                                $data['title']              = '广告活动';
                                $data['url']                = $val['src'];
                                $tags                       = [];
                                foreach ($val['tags'] as $v) {
                                    $tags[] = [
                                        'tagId'   => $v['id'],
                                        'tagName' => $v['name'],
                                    ];
                                }
                                $data['selectedTagList'] = $tags;
                                $res                     = Tag::create('tag')->addContentTag($data);
                                if ($res['code'] == 1) {
                                    Log::error('index tags error' . json_encode($res));
                                }
                            }
                        }
                    }
                }
                if ($value['type'] == 'cCoupon') {
                    foreach ($value['attribute']['cards_data'] as $v) {
                        $card_id = $v['card_data']['card_id'] ?? 0;
                        if ($card_id > 0) {
                            $data = [
                                'card_id'   => $card_id,
                                'act_code'  => $v['card_data']['act_code'] ?? '',
                                'log_type'  => 1,
                                'page_type' => $this->pageType,
                                'creator'   => $this->admin_info['username'],
                            ];
                            Db::table('t_db_card_sm_log')->insert($data);
                        }
                    }
                }
                if (in_array($value['type'], ['cAdvertising', 'cAdvertising1', 'cAdvertising2'])) {
                    foreach ($value['attribute']['imgs'] as $k => $val) {
                        if ($val['savePath']['type'] == 8) { // 卡券抽奖
                            $act_code = $val['couponList'][0]['act_code'] ?? '';
                            $data_json[$key]['attribute']['imgs'][$k]['act_code'] = $act_code;
                        }
                    }
                }
                if ($value['type'] == 'cCarousel') {
                    // 轮播广告
                    foreach ($value['attribute']['imgs'] as $k => $val) {
                        foreach ($val as $index => $datum) {
                            // 抽奖链接
                            if ($datum['savePath']['type'] == 8) {
                                $act_code = $datum['couponList'][0]['act_code'] ?? '';
                                $data_json[$key]['attribute']['imgs'][$k][$index]['act_code'] = $act_code;
                            }
                        }
                    }
                }
            }
            $save['data_json'] = json_encode($data_json);
        }
        if (isset($input['preview_data_json'])) {
            $save['preview_data_json'] = json_encode($input['preview_data_json']);
        }
        if (isset($input['draft_data_json'])) {
            $save['draft_data_json'] = json_encode($input['draft_data_json']);
        }

        if (empty($save)) {
            print_json('1', 'error info');
        }
        $commodity_ids = $this->getCommodityIds($this->pageType);
        if (!empty($commodity_ids)) {
            $save['commodity_id_str'] = $commodity_ids;
        }

        DbHomeSm::where('id', $this->pageType)->update($save);
        $params = [
            'key' => 'cache_prefix.home', 'suffix' => $this->pageType,
            'set' => 'cache_prefix.more_people_more_hm_set', 'set_suffix' => $this->pageType
        ];
        Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $params);

        print_json(0, '保存成功');
    }


    public function get_activity()
    {
        //type 1限时优惠 2NN 3满优惠 4预售 5多人拼团 6优惠套装 7秒杀 8抽奖
        $type = input('type') or print_json(1, '参数丢失');
        $search = input('title');//搜索名称

        $params['where']['is_enable'] = 1;
        if ($type != 7 && $type != 9) {
            $params['where']['act_status'] = 2;#处于活动中
        } elseif ($type == 9) {
            $params['where']['act_status'] = array('in', [1, 2]);
        }
        $params['where']['up_down_channel_dlr'] = array('like', '%' . $this->channel_type . '%');

        switch ($type) {
            case 1:
                $params['field']                  = 'id,title,start_time,end_time';
                $params['where']['set_type']      = $this->shelves_type;
                $params['where']['title']         = array('like', '%' . $search . '%');
                $params['where']['discount_type'] = ['<>', 2];
                $list                             = (new DbLimitDiscount())->getListPaginate($params);
                break;
            case 2:
                $params['field']             = 'id,title,start_time,end_time';
                $params['where']['set_type'] = $this->shelves_type;
                $params['where']['title']    = array('like', '%' . $search . '%');
                $list                        = (new DbNDiscount())->getListPaginate($params);
                break;
            case 3:
                $params['field']                   = 'id,activity_title as title,start_time,end_time';
                $params['where']['set_type']       = $this->shelves_type;
                $params['where']['activity_title'] = array('like', '%' . $search . '%');
                $params['where']['discount_type']  = ['<>', 2];
                $list                              = (new DbFullDiscount())->getListPaginate($params);
                break;
            case 4:
                $params['field']             = 'id,title,front_s_time as start_time,balance_e_time as end_time';
                $params['where']['set_type'] = $this->shelves_type;
                $params['where']['title']    = array('like', '%' . $search . '%');
                $list                        = (new DbPreSale())->getListPaginate($params);
                break;
            case 5:
                $params['field']             = 'id,title,start_time,end_time';
                $params['where']['set_type'] = $this->shelves_type;
                $params['where']['title']    = array('like', '%' . $search . '%');
                $list                        = (new DbFightGroup())->getListPaginate($params);
                break;
            case 6:
                $params['field']         = 'id,name as title,s_time as start_time,e_time as end_time';
                $params['where']['type'] = $this->shelves_type;
                $params['where']['name'] = array('like', '%' . $search . '%');
                $list                    = (new BuCheapSuitIndex())->getListPaginate($params);

                if (!empty($list)) {
                    foreach ($list as $one) {
                        $one['start_time'] = date("Y-m-d H:i:s", $one['start_time']);
                        $one['end_time']   = date("Y-m-d H:i:s", $one['end_time']);
                    }
                }

                break;
            case 8:
                $params['field']               = 'id,title,start_time,end_time,draw_url,game_id';
                $params['where']['set_type']   = $this->shelves_type;
                $params['where']['title']      = array('like', '%' . $search . '%');
                $params['where']['act_status'] = ['in', [1, 2]];
                $params['order']               = 'id desc';
                $list                          = (new DbDraw())->getListPaginate($params);
                break;
            case 7:
                $params['field']             = 'id,title,start_time,end_time,seckill_type,day_start_time,day_end_time';
                $params['where']['set_type'] = $this->shelves_type;
                $params['where']['title']    = array('like', '%' . $search . '%');
                $params['order']             = 'id desc';
                $list                        = (new DbSeckill())->getListPaginate($params);
                foreach ($list as $key => $item) {
                    if ($item['seckill_type'] == 2) {
                        $item['start_time'] = date('Y-m-d ', strtotime($item['start_time'])) . $item['day_start_time'];
                        $item['end_time']   = date('Y-m-d ', strtotime($item['end_time'])) . $item['day_end_time'];
                    }
                }
                break;
            case 9:
                $status = input('status');
                if ($this->shelves_type == 5) {
                    $params['where']['brand'] = 1;
                } elseif ($this->shelves_type == 6) {
                    $params['where']['brand'] = 3;
                } elseif ($this->shelves_type == 7) {
                    $params['where']['brand'] = 2;
                }
                $params['field'] = 'id,title,start_time,end_time,up_down_channel as channel';
                if (!empty($search)) {
                    $params['where']['title'] = array('like', '%' . $search . '%');
                }
                if (!empty($status)) {
                    $params['where']['act_status'] = $status;
                }
                $params['order']                    = 'id desc';
                $params['where']['up_down_channel'] = $params['where']['up_down_channel_dlr'];
                unset($params['where']['up_down_channel_dlr']);
                $list = (new DbCrowdfund())->getListPaginate($params);
                break;
        }
        print_json('0', 'ok', $list);
    }

    public function get_card()
    {
        $input = input('get.');

        $where = [
            'a.is_enable'           => 1,
            'a.act_status'          => ['in', [1, 2]],
            'a.shelves_type'        => $this->shelves_type,
            'a.up_down_channel_dlr' => ['like', '%' . $this->channel_type . '%'],
            'a.card_type'           => ['neq', 6],
            'a.activity_id'         => ['neq', 0],
            ['exp', 'FIND_IN_SET("LQDW-ShangChengYouHuiQuanZuJian",receive_coupon_points)']

        ];
        if (!empty($input['card_name'])) {
            $where['a.card_name'] = ['like', '%' . $input['card_name'] . '%'];
        }
        if (!empty($input['card_type'])) {
            $where['a.card_type'] = $input['card_type'];
        }

        $params['field'] = 'a.id as card_id,a.card_type as card_type_name,a.card_name,a.act_status';
        $params['where'] = $where;
        $params['order'] = 'a.last_updated_date desc';


//        $list = (new DbCard())->getListPaginate($params);
        $card_model  = new DbCard();
        $type_list   = DbCard::CARD_TYPE_LIST;
        $card_status = DbCard::cardStatus();
        $list        = $card_model->getActivityCardPage($params);
        foreach ($list as $key => $item) {
            $list[$key]['card_id']        = (string)$item['card_id'];
            $list[$key]['card_type_name'] = $type_list[$item['card_type_name']] ?? '';
            $list[$key]['card_status']    = $card_status[$item['act_status']] ?? '';
        }

        print_json('0', 'ok', $list);
    }

    public function get_special()
    {
        $title = input('title', '');

        if ($this->pageType == DbHomeSm::DEFAULT_PAGE) {
            $page_type = DbSpecialSm::DEFAULT_PAGE_TYPE;
        } else {
            $page_type = DbSpecialSm::V_DEFAULT_PAGE_TYPE;
        }

        $params['field']              = 'id,title';
        $params['where']['is_enable'] = 1;
        $params['where']['page_type'] = $page_type;

        if (!empty($title)) {
            $params['where']['title'] = array('like', '%' . $title . '%');
        }

        $list = (new DbSpecialSm())->getListPaginate($params);

        print_json('0', 'ok', $list);
    }

    public function adsType()
    {
        (new Special())->adsType();
    }

    //套装列表
    public function get_suit()
    {
        $title = input('title', '');
        if (!empty($title)) {
            $param['where']['name'] = array('like', '%' . $title . "%");
        }
        $model                                 = new BuCheapSuitIndex();
        $model_commodity                       = new BuCheapSuitCommodity();
        $param['where']['act_status']          = ['in', [1, 2]];
        $param['where']['up_down_channel_dlr'] = array('like', '%' . $this->channel_type . '%');
        $param['field']                        = "id,name as title,s_time,e_time";
        $param['order']                        = "id desc";
        $list                                  = $model->getIndexList($param);
        foreach ($list as $key => $value) {
            $value->time                  = date("Y-m-d", $value->s_time) . '~' . date('Y-m-d', $value->e_time);
            $params['where']['a.suit_id'] = $value->id;
            $params['field']              = 'c.cover_image';
            $img                          = $model_commodity->getCheapCommodity($params);
            $imgs                         = [];
            foreach ($img as $val) {
                $imgs[] = $val['cover_image'];
            }
            $value->imgs  = $imgs;
            $value->total = count($img);
            unset($value->s_time, $value->e_time);
        }
        print_json('0', 'ok', $list);
    }

}
