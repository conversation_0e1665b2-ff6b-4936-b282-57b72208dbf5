<?php

namespace app\admin_v2\controller;

use app\admin_v2\queue\InvoiceImageExport;
use app\admin_v2\queue\InvoiceRecordExportQueue;
use app\admin_v2\service\InvoiceRecordService;
use app\common\model\db\DbExports;
use app\common\model\db\InvoiceRecordModel;
use app\common\model\sys\SysMenu;
use app\common\port\connectors\InvoiceHX;
use ForkModules\Traits\ResponseTrait;
use think\Env;
use think\Model;
use think\Queue;
use think\Response;

/*** Created by demo.
 * PROJECT：php-wxmp-dealers
 * User: <EMAIL>
 * Date: 2025/1/14
 * Time: 13:56
 * remark:
 */
class InvoiceRecord extends Common
{
    use ResponseTrait;

    //已申请、已开票、开票失败、换开待审核、冲红中、已冲红、冲红失败
    //开票状态：0 已申请 1开票申请成功等待反馈 2开票成功 3开票失败 4已删除(开票失败后可操作) 5 发票作废
    //10开票申请、20开票申请成功、30开票失败、40冲红请求成功、50冲红失败、60冲红成功、70已申请换开、80换开成功

    /**
     * 跳转到前端项目页面
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function index()
    {
        $m_model = new SysMenu();
        $list = $m_model->alias('a')
            ->join('t_sys_menu b', 'a.menu_pid = b.id')
            ->where(['a.controller' => request()->controller(), 'a.left_view' => 1, 'b.left_view' => 1])
            ->field('a.web_menu_url,b.menu_url')
            ->find();
        if (!empty($list['web_menu_url'])) {
            $username['username'] = $this->admin_info['username'];
            $username['timestamp'] = time();
            $token = http_build_query($username);
            $this->redirect(Env::get('TOB_URL') . '/' . $list['web_menu_url'] . '?sign=' . base64_encode($token));
        }
    }

    /**
     * @return Response
     */
    public function condition(): Response
    {
        $list = [
            'invoice_status_list' => InvoiceRecordModel::INVOICE_STATUS,
            'invoice_type_str_list' => [
                'bs' => '数电专票（电子）',
                'pc' => '数电普票（电子）',
            ],
        ];
        return $this->setResponseData($list)->send();
    }

    /**
     * 获取开票记录列表
     */
    public function InvoiceRecordList()
    {
        $list = InvoiceRecordService::getInstance()->getRecordList(input());
        return $this->setResponseData($list)->send();
    }

    /**
     * 冲红
     * @param $id
     * @return Response
     */
    public function doInvoiceRed($id)
    {
        try {
            $array = InvoiceRecordService::getInstance()->redInoviceById($id);
            if (!isset($array['code'])) {
                return $this->setResponseError($array['message'] ?? '未知错误，请重试')->send();
            }
            if ($array['code'] != 200) {
                return $this->setResponseError($array['message'] ?? '未知错误，请重试')->send();
            }
            return $this->setResponseData("冲红成功")->send();
        } catch (\Exception $e) {
            return $this->setResponseError($e->getMessage())->send();
        }
    }

    /**
     *重开发票
     */
    public function reTryInvoice(): Response
    {
        try {

            $array = InvoiceRecordService::getInstance()->reTryInvoice(input());
            if (!isset($array['code'])) {
                return $this->setResponseError($array['message'] ?? '未知错误，请重试')->send();
            }
            if ($array['code'] != 200) {
                return $this->setResponseError($array['message'] ?? '未知错误，请重试')->send();
            }
            return $this->setResponseData("操作成功")->send();
        } catch (\Exception $e) {
            return $this->setResponseError($e->getMessage())->send();
        }
    }

    /**
     * 换开
     * @return Response
     */
    public function redoInvoice(): Response
    {
        try {
            if (!InvoiceRecordService::getInstance()->redoInvoice(input(), $message)) {
                return $this->setResponseError($message)->send();
            }
            return $this->setResponseData("操作成功")->send();
        } catch (\Exception $e) {
            return $this->setResponseError($e->getMessage())->send();
        }
    }

    public function cancelRedoInvoice($id)
    {
        try {
            if (!InvoiceRecordService::getInstance()->cancelRedoInvoice($id, $message)) {
                return $this->setResponseError($message)->send();
            }
            return $this->setResponseData("操作成功")->send();
        } catch (\Exception $e) {
            return $this->setResponseError($e->getMessage())->send();
        }
    }

    public function pushInvoice(): Response
    {
        try {
            $param = input();
            if (empty($param['id'])) {
                return $this->setResponseData("记录ID 不能同时为空")->send();
            }

            if (empty($param['notify_email']) && empty($param['notify_phone'])) {
                return $this->setResponseData("手机号和邮箱不能同时为空")->send();
            }

            $model = InvoiceRecordService::getInstance()->getDetailById($param['id']);
            if (empty($model)) {
                return $this->setResponseData("开票记录不存在")->send();
            }

            if ($model->invoice_status != InvoiceRecordModel::INVOICE_SUCCESS) {
                return $this->setResponseData("开票状态不是成功状态")->send();
            }

            $send_params = [
                //销方税号和销方组织编码其一必填
                'sellerTaxnum' => $model->seller_tax_num,
                //销方组织编码；销方税号和销方组织编码其一必填
                'sellerCompanyCode' => '',
                //发票代码12或10位
                'invoiceCode' => $model->invoice_code,
                //发票号码；8或20位
                'invoiceNumber' => $model->invoice_number,
                //交付手机号码；交付电话号码和交付邮箱至少有一个不为空，仅支持一个
                'notifyPhone' => $model->notify_phone,
                //交付邮箱地址；交付电话号码和交付邮箱至少有一个不为空，仅支持一个
                'notifyEmail' => $model->notify_email,
                //抄送手机号，当推送手机notifyPhone有值时，才允许填写，多个中间用英文逗号隔开，最多可填写5个
                'ccPhone' => '',
                //抄送邮箱，当推送邮箱notifyEmail有值时，才允许填写，多个中间用英文逗号隔开，最多可填写5个
                'ccEmail' => '',
            ];
            if (!empty($param['notify_phone'])) {
                if ($param['notify_phone'] != $model->notify_phone) {
                    $send_params['notifyPhone'] = $param['notify_phone'];
                }
            }
            if (!empty($param['notify_email'])) {
                if ($param['notify_email'] != $model->notify_email) {
                    $send_params['notifyEmail'] = $param['notify_email'];
                }
            }

            $result_data = InvoiceHX::create('invoice_hx')->delivery($send_params);
            if (isset($result_data['code']) && $result_data['code'] == 200) {
                return $this->setResponseData("操作成功")->send();
            } else {
                return $this->setResponseData($result_data['message'] ?? '未知错误，请重试')->send();
            }

        } catch (\Exception $e) {
            return $this->setResponseError($e->getMessage())->send();
        }
    }

    //开票记录信息
    public function recordDetail($id): Response
    {
        try {
            $list = InvoiceRecordService::getInstance()->getRecord($id);
            return $this->setResponseData($list)->send();
        } catch (\Exception $e) {
            return $this->setResponseError($e->getMessage())->send();
        }
    }

    /**
     * 失败数量
     * @return Response
     */
    public function failCount()
    {
        return $this->setResponseData(['count' => InvoiceRecordService::getInstance()->failCount()])->send();
    }

    /**
     * //     * @param $type image,pdf
     * @return Response
     */
    public function download()
    {
        $type = input('type');
        $ids = input('ids');

        $params = [
            'invoice_ids' => $ids,
            'type' => $type,
        ];

        if (!in_array($type, ['image', 'pdf'])) {
            return $this->setResponseError('格式不正确')->send();
        }


        $export = DbExports::create([
            'export_type' => 'invoice_image',
            'filter_params' => json_encode($params),
            'export_key' => md5(json_encode($params)),
            'creator' => $this->admin_info['username']
        ]);

        Queue::push('app\admin_v2\queue\InvoiceImageExport', json_encode_cn([
            'invoice_ids' => $ids,
            'type' => $type,
            'id' => $export->id,
        ]), config('queue_type.export'));

        return $this->setResponseData("下载进行中")->send();
    }


    public function exportList()
    {
        $params = input();
        if (empty($params['relation_good_order_no'])) {
            if(empty($params['invoice_start_date']) || empty($params['invoice_end_date'])
                || empty($params['red_invoice_start_date']) || empty($params['red_invoice_end_date'])) {
                print_json(1,'订单号为空时,开票时间和冲红时间不能为空');
            }
        }
        $export = DbExports::create([
            'export_type' => 'invoice_excel',
            'filter_params' => json_encode($params),
            'export_key' => md5(json_encode($params)),
            'creator' => $this->admin_info['username']
        ]);
        $params['export_id'] = $export->id;

//        Queue::push('app\admin_v2\queue\InvoiceRecordExportQueue', json_encode($params), config('queue_type.export'));
        Queue::push('app\admin_v2\queue\NewInvoiceRecord', json_encode($params), config('queue_type.export'));
        return $this->setResponseData("下载进行中")->send();
    }
}