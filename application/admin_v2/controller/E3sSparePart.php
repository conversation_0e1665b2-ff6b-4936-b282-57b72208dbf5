<?php

namespace app\admin_v2\controller;

use app\common\model\db\DbCommoditySku;
use app\common\model\e3s\E3sMaintenanceProductCarSeries;
use think\Db;
use think\Model;
use app\common\model\e3s\E3sSparePart as SparePartModel;
use app\common\model\e3s\E3sPartCarSeries as PartCarSeriesModel;

class E3sSparePart extends Comment
{

    private $spare_part;
    private $part_car_series;

    public function __construct()
    {
        parent::__construct();
        $this->spare_part = new SparePartModel();
        $this->part_car_series = new PartCarSeriesModel();
    }

    public function index()
    {
        $query = input('get.');
        $params['query'] = input('get.');
        if(isset($query['part_brand_code']) && !empty($query['part_brand_code'])){
            $params['where']['a.part_brand_code'] = $query['part_brand_code'];
        }
        if(isset($query['variety_code_mid_code']) && !empty($query['variety_code_mid_code'])){
            $params['where']['a.variety_code_mid_code'] = $query['variety_code_mid_code'];
        }
        if(isset($query['variety_code_big_code']) && !empty($query['variety_code_big_code'])){
            $params['where']['a.variety_code_big_code'] = $query['variety_code_big_code'];
        }
        if(isset($query['variety_code_small_code']) && !empty($query['variety_code_small_code'])){
            $params['where']['a.variety_code_small_code'] = $query['variety_code_small_code'];
        }
        if(isset($query['part_no']) && !empty($query['part_no'])){
            $params['where']['a.part_no'] = $query['part_no'];
        }
        if(isset($query['part_name']) && !empty($query['part_name'])){
            $params['where']['a.part_name'] = array("like","%" . $query['part_name'] . "%");
        }
        if(isset($query['other_attr']) && !empty($query['other_attr'])) {
            $params['where']['a.other_attr'] = $query['other_attr'];
        }
        $params['field'] = "a.id,a.variety_code_mid_name,a.part_no,a.part_name,a.part_brand_code_cn,a.variety_code_big_name,
	a.variety_code_small_name,a.sale_price,a.dlr_price,a.rep_part_no,a.other_attr,a.modified_date,a.part_status_cn,
	a.dlr_order_switch_cn,a.dlr_price,sr.spec_part_group,sr.spec_part_group_id";
        $brad = $this->spare_part->partBrand();
        $list = $this->spare_part->partList($params);
        foreach ($list as $value){
            $value->part_brand_code_cn = $this->spare_part->return_brand_cn($value['part_brand_code_cn']);
            $value->oil_supplier = $this->spare_part->oil_supplier(substr($value['part_no'],-4));
            $value->is_goods = $this->spare_part->is_goods($value['part_no']);
            $value->is_part_car_series = $this->part_car_series->is_part_car_series($value['part_no']);
        }
        $variety_big = $this->spare_part->varietyBig();
        $variety_small = $this->spare_part->varietySmall();
        $variety_categories = $this->spare_part->categories();
        $other_attr = $this->spare_part->otherAttr();
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        $this->assign('part_brand', $brad);
        $this->assign('variety_big', $variety_big);
        $this->assign('variety_small', $variety_small);
        $this->assign('variety_categories',$variety_categories);
        $this->assign('other_attr',$other_attr);
        return $this->fetch('e3s/e3s_spare_part/index');
    }

    /**
     * 查看车系车型详情
     * @return mixed
     */
    public function select_car_series(){
        $query = input('get.');
        $params['query'] = $query;
        $type = $query['type'] ?? 1;
        if($type == 1){
            $params['where']['part_no'] = $query['part_no'];
            $part_name = $this->spare_part->getOne($params);
            $params['field'] = "part_series_cn,part_car_type_name,car_config_code";
            $list = $this->part_car_series->getListPaginate($params);
            foreach ($list as $value){
                $value->part_name = $part_name['part_name'];
            }
            $page = $list->render();
            $this->assign('list',$list);
            $this->assign('page',$page);//分页
            return $this->fetch('e3s/e3s_spare_part/select_car_series');
        }elseif ($type == 2){
            $params['pagesize'] = 10;
            $product_car_series = new E3sMaintenanceProductCarSeries();
            $list = $product_car_series->alias('a')
                ->join('t_e3s_car_series b','a.service_car_type = b.service_car_type')
                ->join('t_e3s_maintenance_package d','a.product_type_id = d.product_type_id')
                ->where(['a.product_type_id' => $query['product_type_id']])
                ->field('d.maintain_group_name,d.maintain_group_code,a.service_car_type,b.car_config_code,b.base_series_code,b.base_car_series_cn')
                ->group('a.service_car_type,b.car_config_code')
                ->paginate($params['pagesize'],false,array('query'=>$params['query']));
            $page = $list->render();
            $this->assign('list',$list);
            $this->assign('page',$page);//分页
            return $this->fetch('e3s/e3s_set_meal/select_car_series');
        }

    }

    /**
     * 查看关联商品
     * @return mixed
     */
    public function select_win_goods()
    {
        $query = input('get.');
        $params['query'] = $query;
        $params['where'] = [];
        $params['order'] = 'modified_date desc';
        $params['pagesize'] = 10;
        $params['where']['a.sku_code'] = $query['part_no'];
        $params['where']['a.is_enable'] = 1;
        $params['where']['b.is_enable'] = 1;
        $list = Db::name('db_commodity_sku')->alias('a')
            ->join('t_db_commodity b','a.commodity_id = b.id','left')
            ->where($params['where'])
            ->field('b.commodity_name,a.id sku_id,a.sku_code,a.price,a.commodity_id')
            ->group('b.id')
            ->paginate($params['pagesize'],false,array('query'=>$params['query']));
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        return $this->fetch('e3s/e3s_spare_part/select_win_goods');
    }

    /**
     * 返回备件数据
     * @return void
     */
    public function ajaxGetBj()
    {
        $query = input('get.');
        $params['query'] = input('get.');
        $params['where']['a.is_enable'] = 1;
        if(isset($query['part_brand_code']) && !empty($query['part_brand_code'])){
            $params['where']['a.part_brand_code'] = $query['part_brand_code'];
        }
        if(isset($query['variety_code_mid_code']) && !empty($query['variety_code_mid_code'])){
            $params['where']['a.variety_code_mid_code'] = $query['variety_code_mid_code'];
        }
        if(isset($query['variety_code_big_code']) && !empty($query['variety_code_big_code'])){
            $params['where']['a.variety_code_big_code'] = $query['variety_code_big_code'];
        }
        if(isset($query['variety_code_small_name']) && !empty($query['variety_code_small_name'])){
            $params['where']['a.variety_code_small_name'] = $query['variety_code_small_name'];
        }
        if(isset($query['part_no']) && !empty($query['part_no'])){
            $params['where']['a.part_no'] = $query['part_no'];
        }
        if(isset($query['part_name']) && !empty($query['part_name'])){
            $params['where']['a.part_name|other_attr'] = array('like','%' . $query['part_name'] . '%');
        }
        if(isset($query['part_status']) && !empty($query['part_status'])){
            if($query['part_status'] == 1){
                $params['where']['a.part_status'] = array('in',[1,5]);
            }else{
                $params['where']['a.part_status'] = array('in',[0,2,4]);
            }
        }
        if(isset($query['sku_code']) && !empty($query['sku_code'])){
            if(!empty($query['sku_code'])){
                $params['where']['a.part_no'] = array('in',$query['sku_code']);
            }
        }
        if(isset($query['other_attr']) && !empty($query['other_attr'])){
            $params['where']['a.other_attr'] = $query['other_attr'];
        }
        if(isset($query['spec_part_group_id']) && !empty($query['spec_part_group_id'])){
            $params['where']['sr.spec_part_group_id'] = array('in',explode(',',$query['spec_part_group_id']));
        }
        $params['field'] = "a.*,sr.spec_part_group,sr.spec_part_group_id";
//        $list = $this->spare_part->getListPaginate($params);
        $list = $this->spare_part->partList($params);
        foreach ($list as $value){
            $value->part_brand_code_cn = $this->spare_part->return_brand_cn($value['part_brand_code_cn']);
        }

        print_json(0, '', $list);
    }

    /**
     * 根据备件返回车型数据
     * @return void
     */
    public function ajaxGetBjCar()
    {
        $query = input('get.');
        $params['query'] = $query;
        $params['where']['part_no'] = $query['part_no'];
        $part_name = $this->spare_part->getOne($params);
        $list = $this->part_car_series->getListPaginate($params);
        foreach ($list as $value){
            $value->part_name = $part_name['part_name'];
        }
        print_json(0, '', $list);
    }

}