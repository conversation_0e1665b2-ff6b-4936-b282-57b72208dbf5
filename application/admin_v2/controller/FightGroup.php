<?php
/**
 *
 * 拼团
 * Created by PhpStorm.
 * User: lyj
 * Date: 2017/10/27
 * Time: 下午4:23
 */

namespace app\admin_v2\controller;


use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlr;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFightGroupCommodity;
use app\common\model\db\DbGift;
use app\common\model\db\DbLimitDiscountCommodity;
use app\common\model\db\DbSeckill;
use app\common\service\BaseDataService;
use app\common\service\CommodityService;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSystemValue;
use think\Hook;
use app\common\model\db\DbCard;

class FightGroup extends Common
{

    private $comm_type_model;
    private $commodity;
    private $fight_group;
    private $fight_group_comm;
    private $dlr;
    private $comm_service;
    private $_dbCommodityDlrTypeObj;
    private $gather_list;

    public function __construct()
    {
        parent::__construct();
        $this->comm_type_model        = new DbCommodityType();
        $this->commodity              = new DbCommodity();
        $this->fight_group            = new DbFightGroup();
        $this->fight_group_comm       = new DbFightGroupCommodity();
        $this->dlr                    = new DbDlr();
        $this->comm_service           = new CommodityService();
        $this->_dbCommodityDlrTypeObj = new DbCommodityDlrType();
        $model = new DbSystemValue();
        $this->gather_list = $model->where(['value_type'=>26,'is_enable'=>1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('gather_list', $this->gather_list);
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('dlr_code', $this->admin_info['dlr_code']);
    }

    public function live()
    {
        $status         = input('get.status');  //1未开始 2 进行中 3已结束
        $title          = input('get.title');
        $date           = input('get.date');
        $admin_type     = $this->admin_info['type'];
        $admin_dlr_code = $this->admin_info['dlr_code'];
        $gather_id = input('get.gather_id',0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name = input('get.theme_name');
        $where     = [];
        $live_type = input('get.live_type');
        if (empty($live_type)) {
            $set_type                       = 5;
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif($live_type == 2) {
            $set_type = 7;
            $where[]  = [['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], 'or'];
        } else {
            $set_type = 6;
            $where[]  = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if (!empty($date)) {
            $date = explode(' ~ ', $date);
            if (count($date) == 2) {
                $where['a.start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($date[0])), date('Y-m-d H:i:s', strtotime($date[1]) + 86400)]];
            }
        }

        if (!empty($title)) {
            $where['a.title'] = ['like', "%{$title}%"];
        }
        $date = date('Y-m-d H:i:s');
        if ($status == 1) {
            $where['a.start_time'] = ['>', $date];
        } else if ($status == 2) {
            $where['a.start_time'] = ['<', $date];
            $where['a.end_time']   = ['>', $date];
        } else if ($status == 3) {
            $where['a.end_time'] = ['<', $date];
        } else if ($status == 4) {
            $where['a.is_enable'] = 0;
        }
        if (in_array($status, [1, 2, 3])) {
            $where['a.is_enable'] = 1;
        }
        if(!empty($gather_id)) {
            $where['a.gather_id'] = $gather_id;
        }
        if(!empty($is_pv_subsidy)) {
            if($is_pv_subsidy != 2){
                $where['a.is_pv_subsidy'] = $is_pv_subsidy;
            }else{
                $where['a.is_pv_subsidy'] = 0;
            }
        }
        if(!empty($theme_name)) {
            $where['a.theme_name'] = array('like','%'.$theme_name.'%');
        }
        $field               = 'a.id,a.title,a.start_time,a.end_time,a.is_enable,a.up_down_channel_name,a.gather_id,a.is_pv_subsidy,a.theme_name';
        $where['a.set_type'] = $set_type;
        $params              = [
            'where' => $where,
            'query' => input('get.'),
            'order' => 'a.id DESC',
            'field' => $field,
        ];
        $gather_array = [];
        foreach ($this->gather_list as $value){
            $gather_array[$value['id']] = $value['name'];
        }
        $list                = $this->fight_group->getFightGroupList($admin_type, $params);
        foreach ($list as $key => $val) {
            $in = empty($val['is_enable']) || $status == 4;
            if ($val['start_time'] > $date) {
                $list[$key]['status_name'] = $in ? '已关闭' : '未开始';
            } else if ($val['start_time'] < $date && $val['end_time'] > $date) {
                $list[$key]['status_name'] = $in ? '已关闭' : '进行中';
            } else {
                $list[$key]['status_name'] = $in ? '已关闭' : '已结束';
            }
            if($val['gather_id'] == 0 || empty($val['gather_id'])){
                $list[$key]['gather_name'] = '-';
            }else{
                if(isset($gather_array[$val['gather_id']])){
                    $list[$key]['gather_name'] = $gather_array[$val['gather_id']];
                }else{
                    $list[$key]['gather_name'] = '-';
                }
            }
            $list[$key]['is_pv_subsidy_status'] = $val['is_pv_subsidy'] == 1 ?'是': '否';
        }

        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('admin_type', $admin_type);
        $this->assign('status', $status);
        $this->assign('set_type', $set_type);
        $this->assign('live_type', $live_type);
        return $this->fetch('live');
    }

    public function index()
    {
        $status         = input('get.status');  //1未开始 2 进行中 3已结束
        $belong         = input('get.belong');  //平台 2 专营店
        $title          = input('get.title');
        $date           = input('get.date');
        $dlr_code       = input('get.dlr_code');
        $admin_type     = $this->admin_info['type'];
        $admin_dlr_code = $this->admin_info['dlr_code'];
        $set_type       = input('get.set_type', 1);

        $where = [];
        if (!empty($date)) {
            $date = explode(' ~ ', $date);

            if (count($date) == 2) {
                $where['a.start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($date[0])), date('Y-m-d H:i:s', strtotime($date[1]) + 86400)]];
            }
        }

        if ($belong == 1) {
            $where['a.created_dlr_code'] = 'NISSAN';
        } else if ($belong == 2) {
            $where['a.created_dlr_code'] = ['<>', 'NISSAN'];
        }
        if (!empty($title)) {
            $where['a.title'] = ['like', "%{$title}%"];
        }
        if (!empty($dlr_code)) {
            $where['a.created_dlr_code'] = $dlr_code;
        }
        $date = date('Y-m-d H:i:s');
        if ($status == 1) {
            $where['a.start_time'] = ['>', $date];
        } else if ($status == 2) {
            $where['a.start_time'] = ['<', $date];
            $where['a.end_time']   = ['>', $date];
        } else if ($status == 3) {
            $where['a.end_time'] = ['<', $date];
        }
        $field = 'a.id,a.title,a.created_dlr_code,a.start_time,a.end_time,a.is_enable';
        if ($admin_type == 1) {
            $field .= ", c.dlr_name";

            $where['a.set_type'] = $set_type == 3 ? $set_type : ['<>', 3];

        } else {
            $set_type = 2;
            $where[]  = ['exp', "(find_in_set('{$admin_dlr_code}',b.dlr_code) AND set_type = 1 )OR a.created_dlr_code='{$this->admin_info['dlr_code']}'"];
        }
        $params = [
            'where' => $where,
            'query' => input('get.'),
            'order' => 'a.id DESC',
            'field' => $field,
        ];
        $list   = $this->fight_group->getFightGroupList($admin_type, $params);
        foreach ($list as $key => $val) {
            $list[$key]['edit'] = false;
            if (($val['created_dlr_code'] == 'NISSAN' && $admin_type == 1) or ($admin_type == 2 && $val['created_dlr_code'] == $admin_dlr_code)) {
                $list[$key]['edit'] = true;
            }

            if ($val['start_time'] > $date) {
                $list[$key]['status_name'] = '未开始';
            } else if ($val['start_time'] < $date && $val['end_time'] > $date) {
                $list[$key]['status_name'] = '进行中';
            } else {
                $list[$key]['status_name'] = '已结束';
            }
            if ($val['created_dlr_code'] == 'NISSAN') {
                $list[$key]['belong'] = '平台';
            } else {
                $list[$key]['belong'] = $admin_type == 1 ? '专营店' : '自建';
            }
        }

        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('admin_type', $admin_type);
        $this->assign('belong', $belong);
        $this->assign('status', $status);
        $this->assign('set_type', $set_type);
        return $this->fetch('index');


    }


    public function getSkuList()
    {
        $fight_commodity_id = input('get.fight_commodity_id');
        $row                = $this->fight_group->getGroupInfo(['where' => ['b.id' => $fight_commodity_id], 'field' => 'b.sku_price,b.commodity_id,b.commodity_set_id,a.created_dlr_code']);
        if (!$row) print_json(1, '', []);
        $sku_price    = json_decode($row['sku_price'], true);
        $commodity_id = input('commodity_id');
        // $list=$this->sku_model->getList(['where'=>['commodity_id'=>$commodity_id,'is_enable'=>1]]);
        $res = $this->comm_service->getSetSku($row['commodity_set_id']);
        foreach ($res['sku_list'] as $key => $val) {
            if (empty($sku_price[$val['id']])) {
                unset($res['sku_list'][$key]);
            } else {
                $res['sku_list'][$key]['fight_price'] = $sku_price[$val['id']];
            }

        }
        $this->assign('sku_list', $res['sku_list']);
        $this->assign('sp_title', $res['sp_title']);
        $this->assign('sp_list', $res['sp_list']);
        return $this->fetch('get_index_sku');


    }

    /**
     * 添加数据
     * @return mixed
     */
    public function addLive()
    {
        $this->assign('set_type', input('set_type', 5));
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', (new DbSystemValue())->getNameList(20));
        $this->assign('commodity_class', DbCommodity::commodityClass());
        $this->assign('live_type', input('live_type'));
        return $this->fetch('add_live');
    }


    /**
     * 添加数据
     * @return mixed
     */
    public function add()
    {
        $set_type = input('get.set_type') or print_json(1, '');


        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);


        $this->assign('set_type', $set_type);
        return $this->fetch('add');
    }

    /**
     * 更新数据
     * @return mixed
     */
    public function updateLive()
    {
        $id          = input('get.id');
        $fight_group = $this->fight_group->getOneByPk($id);
        if ($fight_group['created_dlr_code'] != $this->admin_info['dlr_code']) print_json(1, '');
        $fight_group_comm = $this->fight_group_comm->getFightGroupCommodity(['a.fight_group_id' => $id]);
        $fight_group_comm = collection($fight_group_comm)->toArray();
        $row_comm         = [];
        //过滤专营店
        $commodity_set_ids = [];
        foreach ($fight_group_comm as $key => $val) {
            $commodity_set_ids[] = $val['commodity_set_id'];
        }

        $comm_dl_model = new DbCommodityDlr();
        $dlr_list      = $comm_dl_model->getDlrBySetId($commodity_set_ids);

        foreach ($fight_group_comm as $key => $val) {
            $val['dlr_name'] = [];
            foreach ($dlr_list as $key_d => $val_d) {
                if ($val_d['commodity_set_id'] == $val['commodity_set_id']) {
                    $dlr_code        = array_intersect(explode(',', $val['dlr_code']), explode(',', $val_d['dlr_code']));
                    $dlr_name        = $this->dlr->getColumn(['where' => ['dlr_code' => ['in', $dlr_code]], 'column' => 'dlr_name']);
                    $val['dlr_name'] = implode(',', $dlr_name);
                }
            }
            $val['sku_list'] = json_decode($val['sku_price'], true);
            unset($val['sku_price']);
            unset($val['cover_image']);
            $row_comm[] = [
                'commodity_name' => $val['commodity_name'],
                'cover_image'    => $fight_group_comm[$key]['cover_image'],
                'commodity_id'   => $val['commodity_id'],
                'sku_list'       => json_encode($val),
            ];
        }

        $up_down_channel_info = (new DbSystemValue())->getNameList(20);
        $info_str             = implode(',', array_keys($up_down_channel_info));


        $setType = input('set_type', 5);

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');

        //优惠券
        $card_arr = [];
        $selected_data = [];
        $commodity_card_name = [];
        if(!empty($fight_group['rel_card_ids'])) {
            $commodity_card_name = (new DbCard())->getColumn(['where' => ['id' => ['in', $fight_group['rel_card_ids']]], 'column' => 'card_name']);
            $selected_data = (new DbCard())->getColumn(['where' => ['id' => ['in', $fight_group['rel_card_ids']]], 'column' => 'id,card_name']);
            $card_arr = explode(',', $fight_group['rel_card_ids']);
        }
        $fight_group['commodity_card_name'] = implode(',', $commodity_card_name);
        $fight_group['num_card'] = empty($fight_group['rel_card_ids']) ? 0 : count(explode(',', $fight_group['rel_card_ids']));
        $this->assign('selected',json_encode($card_arr));
        $this->assign('selected_data', $selected_data);
        $this->assign('selected_card_name',json_encode($commodity_card_name));


        $commodity_class = DbCommodity::commodityClass();
        $oneSkuData = $data['sku_list'][0] ?? [];
        $oneCommodityClass = $oneSkuData['commodity_class'] ?? 0;
        $oneCommodityDlrTypeId = $oneSkuData['commodity_dlr_type_id'] ?? 0;
        $this->assign('oneCommodityClass', $oneCommodityClass);
        $this->assign('oneCommodityDlrTypeId', $oneCommodityDlrTypeId);
        $this->assign('comm_set_id_arr', json_encode_cn($commodity_set_ids));


        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));

        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('row', $fight_group);
        $this->assign('row_comm', $row_comm);
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', $up_down_channel_info);
        $this->assign('dlr_hidden', (strpos($fight_group['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
        $this->assign('dlr_str', $this->getDlrInInfo($fight_group['up_down_channel_dlr']));
        $comm_parent_list = $this->comm_type_model->getCommodityByParentId(0);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('commodity_class', DbCommodity::commodityClass());
        $this->assign('live_type', input('live_type'));

        return $this->fetch('update_live');
    }

    /**
     * 更新数据
     * @return mixed
     */
    public function update()
    {
        $id          = input('get.id');
        $fight_group = $this->fight_group->getOneByPk($id);
        if ($fight_group['created_dlr_code'] != $this->admin_info['dlr_code']) print_json(1, '');
        $fight_group_comm = $this->fight_group_comm->getFightGroupCommodity(['a.fight_group_id' => $id]);
        $fight_group_comm = collection($fight_group_comm)->toArray();
//        echo $this->fight_group_comm->getLastSql();
        $row_comm = [];
        //过滤专营店
        $commodity_set_ids = [];
        foreach ($fight_group_comm as $key => $val) {
            $commodity_set_ids[] = $val['commodity_set_id'];
        }

        $comm_dl_model = new DbCommodityDlr();
        $dlr_list      = $comm_dl_model->getDlrBySetId($commodity_set_ids);

        foreach ($fight_group_comm as $key => $val) {
            $val['dlr_name'] = [];
            foreach ($dlr_list as $key_d => $val_d) {
                if ($val_d['commodity_set_id'] == $val['commodity_set_id']) {
                    $dlr_code        = array_intersect(explode(',', $val['dlr_code']), explode(',', $val_d['dlr_code']));
                    $dlr_name        = $this->dlr->getColumn(['where' => ['dlr_code' => ['in', $dlr_code]], 'column' => 'dlr_name']);
                    $val['dlr_name'] = implode(',', $dlr_name);
                }

            }
            //  $dlr_name=$this->dlr->getColumn(['where'=>['dlr_code'=>['in',explode(',',$val['dlr_code'])]],'column'=>'dlr_name']);
            //$val['dlr_name'] = implode(',',$dlr_name);
            $val['sku_list'] = json_decode($val['sku_price'], true);
            unset($val['sku_price']);
            unset($val['cover_image']);
//            var_dump($val);
            $row_comm[] = [
                'commodity_name' => $val['commodity_name'],
                'cover_image'    => $fight_group_comm[$key]['cover_image'],
                'commodity_id'   => $val['commodity_id'],
                'sku_list'       => json_encode($val),
            ];


        }


        $comm_parent_id     = input('get.comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $comm_parent_list   = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list     = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        if (!empty($order_code)) {
            $where['a.order_code'] = ['like', '%' . $order_code . '%'];
        }

        $this->assign('comm_parent_id', $comm_parent_id);
        $this->assign('sub_comm_type_id', $sub_comm_type_id);
        $this->assign('three_comm_type_id', $three_comm_type_id);
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);


        $this->assign('row', $fight_group);
        $this->assign('row_comm', $row_comm);
        $comm_parent_list = $this->comm_type_model->getCommodityByParentId(0);
        $this->assign('comm_parent_list', $comm_parent_list);
        return $this->fetch('update');

    }

    /**
     * 查看
     */

    public function view()
    {
        $id                    = input('get.id');
        $fight_group           = $this->fight_group->getOneByPk($id);
        $fight_group_comm_list = $this->fight_group_comm->getFightGroupCommodity(['a.fight_group_id' => $id]);

        if ($fight_group !== 'NISSAN') {
            $dlr      = $this->dlr->getOne(['where' => ['dlr_code' => $fight_group['created_dlr_code']], 'field' => 'dlr_name']);
            $dlr_name = $dlr['dlr_name'];
        } else {
            $dlr_name = '-';
        }
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('row', $fight_group);
        $this->assign('fight_group_comm_list', $fight_group_comm_list);
        $this->assign('dlr_name', $dlr_name);
        return $this->fetch('view');


    }

    /**
     * 保存数据--车生活
     */
    public function saveLive()
    {
        $post = input('post.');
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '结束时间不能为空');
        $people_number   = input('post.people_number');
        $purchase_number = input('post.purchase_number');
        $rule = input('post.rule') or print_json(1, '参团规则不能为空');
        $buy_hour  = input('post.buy_hour');
        $id        = input('post.id');
        $is_enable = input('post.is_enable');
        $set_type  = input('post.set_type');
        $gather_id = input('gather_id',0);
        $is_pv_subsidy = input('is_pv_subsidy',0);
        $theme_name = input('theme_name');
        if (empty($buy_hour)) print_json(1, '参团时间不能为空');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');
        if (empty($post['sku_list'])) print_json(1, '商品规格不能为空');
        $sku_list            = $post['sku_list'];
        $card_available      = input('post.card_available', 0);
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $post['up_down_channel'] ?? []),
            input('post.dlr_code', '')
        );

        $act_status = $this->fight_group->getActStatus($start_time, $end_time);
        $data = [
            'title'                 => $title,
            'start_time'            => $start_time,
            'end_time'              => $end_time,
            'tag'                   => $post['tag'],
            'act_status'            => $act_status,
            'people_number'         => $people_number,
            'purchase_number'       => $purchase_number,
            'buy_hour'              => $buy_hour,
            'rule'                  => $rule,
            'creator'               => $this->admin_info['username'],
            'created_dlr_code'      => $this->admin_info['dlr_code'],
            'set_type'              => $set_type,
            'card_available'        => $card_available,
            'up_down_channel_name'  => implode(',', $post['up_down_channel_name'] ?? []),
            'up_down_channel_dlr'   => $up_down_channel_dlr,
            'is_enable'             => $is_enable,
            'e3s_activity_id'       => $post['e3s_activity_id'] ?? 0,
            'activity_type'         => $post['post.activity_type'] ?? 0,
            'settlement_rule_id'    => $post['settlement_rule_id'],
            'settlement_rule_name'  => $post['settlement_rule'],
            'settlement_rule_type'  => $post['settlement_rule_type'],
            'settlement_rule_value' => $post['settlement_rule_value'],
            'rel_card_ids'          => $post['rel_card_ids'],
            'gather_id'             => $gather_id,
            'is_pv_subsidy'         => $is_pv_subsidy,
            'theme_name'            => $theme_name,
        ];
        if (!empty($data['e3s_activity_id'])){
            if (empty($data['activity_type'])) print_json(1, '存在e3s活动时活动设置类型必选');
        }else{
            $data['activity_type'] = 0;
        }
        $e3s_activity = input('e3s_activity', '');
        if (!empty($e3s_activity)){
            $data['e3s_activity_name'] = explode(' | ', $e3s_activity)[1];
        }

        //开启事物
        $this->fight_group->startTrans();
        if (empty($id)) {  //插入
            $last_id = $this->fight_group->insertGetId($data);
            $res     = $this->insertCommodiySkuData($sku_list, $last_id, 'add', $set_type);
            //提交
            $this->fight_group->commit();
            if (($act_status == 2) && !empty($is_enable)) {
                $params              = [
                    'where' => ['a.id' => $last_id],
                    'field' => 'a.id,a.title,a.act_status,a.up_down_channel_name,b.commodity_id',
                ];
                $fight_commodity_arr = $this->fight_group->getAllFight($params);
                $this->doHook('add', $fight_commodity_arr, $last_id);
            }
            if ($res) print_json(0, '保存成功');
        } else {    //修改
            if ($is_enable == 1) {
                $date = date('Y-m-d H:i:s');
                //是否 限时折扣
                $list = (new dbLimitDiscount())->getIsLimitProduct(
                    $sku_list, [
                    'a.is_enable'  => ['=', 1],
                    'a.start_time' => ['<', $date],
                    'a.end_time'   => ['>', $date],
                    'a.set_type'   => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_limit'] == 1) print_json(1, $v['commodity_name'] . '已参加限时折扣活动');
                }
                //判断是否已经参加了预售
                $list = (new DbPreSale())->getIsPreProduct(
                    $sku_list, [
                    'a.is_enable'      => ['=', 1],
                    'a.front_s_time'   => ['<', $date],
                    'a.balance_e_time' => ['>', $date],
                    'a.set_type'   => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_pre'] == 1) print_json(1, $v['commodity_name'] . '已参加预售活动');
                }
                //判断是否已经参加了拼团
                $list = (new DbFightGroup())->getIsFightGroupProduct(
                    $sku_list, [
                    'a.id'         => ['<>', $id],
                    'a.is_enable'  => ['=', 1],
                    'a.start_time' => ['<', $date],
                    'a.end_time'   => ['>', $date],
                    'a.set_type'   => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_fight'] == 1) print_json(1, $v['commodity_name'] . '已参加其它拼团活动');
                }

                $list = (new DbGift())->getIsGiftProduct($list,[
                    'a.is_enable'  => ['=', 1],
                    'a.set_type'   => $set_type,
                    'a.act_status' => ['in', [1, 2]]
                ]);
                foreach ($list as $v) {
                    if ($v['is_gift'] == 1) print_json(1, $v['commodity_name'] . '已参加买赠活动');
                }
            }
            $row     = $this->fight_group->getOneByPk($id);
            $last_id = $id;
            if ($row && $row['created_dlr_code'] == $this->admin_info['dlr_code']) {
                $data['modifier']          = $this->admin_info['username'];
                $data['last_updated_date'] = date('Y-m-d H:i:s');
                $res                       = $this->fight_group->where(['id' => $id])->update($data);
                if ($res) {

                    $old_commodity_id_arr = $this->fight_group_comm->where(['fight_group_id' => $id])->column('commodity_id');
                    $new_commodity_id_arr = array_column($sku_list, 'commodity_id');
                    $rm_commodity_id_arr  = array_diff($old_commodity_id_arr, $new_commodity_id_arr);

                    $this->fight_group_comm->where(['fight_group_id' => $id])->delete();
                    $this->insertCommodiySkuData($sku_list, $id, 'update', $row['set_type']);
                    $this->fight_group->commit();

                    if (($act_status == 2) && !empty($is_enable)) {
                        $params              = [
                            'where' => ['a.id' => $last_id],
                            'field' => 'a.id,a.title,a.act_status,a.up_down_channel_name,b.commodity_id',
                        ];
                        $fight_commodity_arr = $this->fight_group->getAllFight($params);
                        $this->doHook('update', $fight_commodity_arr, $last_id);
                    } else if (empty($is_enable)) {
                        $params              = [
                            'where' => ['a.id' => $last_id],
                            'field' => 'a.id,a.title,a.act_status,a.up_down_channel_name,b.commodity_id',
                        ];
                        $fight_commodity_arr = $this->fight_group->getAllFight($params);
                        $this->doHook('delete', $fight_commodity_arr, $last_id);
                    }

                    if (!empty($rm_commodity_id_arr)) {
                        $this->doHook('delete', $rm_commodity_id_arr, $last_id);
                    }

                    print_json(0, '保存成功');
                }
            }
        }

        print_json(1, '保存失败');
    }

    /**
     * 保存数据
     */
    public function save()
    {
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '结束时间不能为空');
        $people_number   = input('post.people_number');
        $purchase_number = input('post.purchase_number');
        $rule = input('post.rule') or print_json(1, '参团规则不能为空');
        $buy_hour = input('post.buy_hour', 0);
        $id       = input('post.id');
        $set_type = input('post.set_type');
        // $pic             = input('post.pic') //or print_json(1,'素材图片不能为空');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');
        if (empty($_POST['sku_list'])) print_json(1, '商品规格不能为空');
        $sku_list = $_POST['sku_list'];


        $data = [
            'title'            => $title,
            'start_time'       => $start_time,
            'end_time'         => $end_time,
            'people_number'    => $people_number,
            'purchase_number'  => $purchase_number,
            'buy_hour'         => $buy_hour,
            'rule'             => $rule,
            'creator'          => $this->admin_info['username'],
            'created_dlr_code' => $this->admin_info['dlr_code'],
            'set_type'         => $set_type,
            //'pic'           =>$pic,
        ];
        //开启事物
        $this->fight_group->startTrans();
        if (empty($id)) {  //插入
            $last_id = $this->fight_group->insertGetId($data);
            $res     = $this->insertCommodiySkuData($sku_list, $last_id, 'add', $set_type);
            //提交
            $this->fight_group->commit();
            if ($res) print_json(0, '保存成功');

        } else {    //修改

            $row = $this->fight_group->getOneByPk($id);
            if ($row && $row['created_dlr_code'] == $this->admin_info['dlr_code']) {
                $data['modifier']          = $this->admin_info['username'];
                $data['last_updated_date'] = date('Y-m-d H:i:s');
                $res                       = $this->fight_group->where(['id' => $id])->update($data);
                if ($res) {
                    $this->fight_group_comm->where(['fight_group_id' => $id])->delete();
                    $this->insertCommodiySkuData($sku_list, $id, 'update', $row['set_type']);
                    $this->fight_group->commit();
                    print_json(0, '保存成功');
                }
            }

        }

        print_json(1, '保存失败');

    }

    public function delete()
    {
        $this->_checkAjax();
        $id  = input('post.id');
        $row = $this->fight_group->getOneByPk($id);

        if ($row['set_type'] == 5) {
            $params              = [
                'where' => ['a.id' => $id],
                'field' => 'a.id,a.title,a.act_status,a.up_down_channel_name,b.commodity_id',
            ];
            $fight_commodity_arr = $this->fight_group->getAllFight($params);
            $this->doHook('delete', $fight_commodity_arr, $id);
        }

        if ($row['created_dlr_code'] == $this->admin_info['dlr_code']) {
            $this->fight_group_comm->where(['fight_group_id' => $id])->delete();
            $this->fight_group->where(['id' => $id])->delete();
            print_json(0, '删除成功');
        }
    }

    /**
     * 插入商品及规格
     * @param $sku_list
     * @param $fight_id
     * @param $action
     * @return bool|int|string
     */

    public function insertCommodiySkuData($sku_list, $fight_id, $action, $set_type)
    {
        if (empty($sku_list)) {
            return false;
        }
        $data     = [];
        $dlr_code = '';
        if ($set_type == 2) {
            $dlr_code = $this->admin_info['dlr_code'];
        } elseif ($set_type == 3) {
            $dlr_code = 'GWSC';
        }
        foreach ($sku_list as $key => $val) {

            $sku_price = $val['sku_list'];
            rsort($sku_price);
            $lowest_price        = end($sku_price);
            $data[]              = [
                'commodity_id'     => $val['commodity_id'],
                'commodity_set_id' => $val['commodity_set_id'],
                'dlr_code'         => $set_type == 1 ? $val['dlr_code'] : $dlr_code,
                'fight_group_id'   => $fight_id,
                'creator'          => $this->admin_info['username'],
                'sku_price'        => json_encode($val['sku_list']),
                'lowest_price'     => $lowest_price,
            ];
            $commodity_set_ids[] = $val['commodity_set_id'];
            // var_dump($data);
        }
        //删除已经添加的商品
        if ($this->admin_info['type'] == 1) {
            $where = ['commodity_set_id' => ['in', $commodity_set_ids]];
        } else {
            $where = ['commodity_set_id' => ['in', $commodity_set_ids], 'dlr_code' => $this->admin_info['dlr_code']];
        }
        $this->fight_group_comm->where($where)->delete();
        $lastid = $this->fight_group_comm->insertAll($data);
        return $lastid;


    }

    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetLiveCommodityList()
    {
        $commodity_name  = input('commodity_name');
        $top_type        = input('comm_parent_id');
        $second_type     = input('sub_comm_type_id');
        $third_type      = input('three_comm_type_id');
        $active_id       = input('active_id');
        $commodity_class = input('commodity_class');

        $live_type = input('live_type');
        $where = ['b.listing_type' => 1, 'c.dd_commodity_type' => ['neq', 12]];
        if (empty($live_type)) {
            $where['b.shelves_type']        = 5;
            $where['b.qsc_group'] =  '';  // 取送车服务包不能参加
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($live_type == 2) {
            $where['b.shelves_type'] = 7;
            $where[]                 = [['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], 'or'];
        } else {
            $where['b.shelves_type'] = 6;
            $where[]                 = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        $type_id = 0;
        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

        if (!empty($commodity_class)) {
            $where['a.commodity_class'] = $commodity_class;
        }

//        $where['commodity_class'] = 1;

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},a.comm_type_id_str) ) "];
        }

        $where['is_store'] = 0;
        $field  = "b.commodity_dlr_type_id,a.commodity_class,a.comm_type_id,a.up_down_channel_name,a.commodity_id,
        a.commodity_set_id,a.commodity_name,a.cover_image,a.price,a.count_stock,c.dd_commodity_type";
        $params = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );

        $flat = new DbCommodityFlat();
        $list = $flat->getCommodityList($params);
        $date = date('Y-m-d H:i:s');

        //是否已团购
        $list = $this->fight_group->getIsFightGroupProduct($list, [
            'a.id'         => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'  => ['=', 1],
            'a.start_time' => ['<', $date],
            'a.end_time'   => ['>', $date],
            'a.set_type'   => $where['b.shelves_type'],
        ]);

        //是否有参加预售
        $list = (new DbPreSale())->getIsPreProduct($list, [
            'a.is_enable'      => ['=', 1],
            'a.front_s_time'   => ['<', $date],
            'a.balance_e_time' => ['>', $date],
            'a.set_type'       => $where['b.shelves_type'],
        ]);

        //是否有参加限时优惠
        $list = (new DbLimitDiscount())->getIsLimitProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.start_time' => ['<', $date],
            'a.end_time'   => ['>', $date],
            'a.set_type'   => $where['b.shelves_type'],
        ]);

        //判断是否已经参加秒杀
        $list = (new DbSeckill())->getIsSeckillProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]],
        ]);

        //判断是否参加买赠
        $list = (new DbGift())->getIsGiftProduct($list,[
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);

        $res         = [];
        $res['list'] = $list;
        print_json(0, '', $res);
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetCommodityList()
    {
        $commodity_name                       = input('commodity_name');
        $comm_type_id                         = input('comm_type_id');
        $comm_parent_id                       = input('comm_parent_id');
        $sub_comm_type_id                     = input('get.sub_comm_type_id');
        $three_comm_type_id                   = input('get.three_comm_type_id');
        $set_type                             = input('set_type');
        $params['where']['a.commodity_class'] = 1;
        $params['query']                      = [];

        $params['where']['is_grouped']    = 0;
        if (!empty($commodity_name)) {
            $params['where']['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        if (!empty($three_comm_type_id)) {
            $params['where']['a.comm_type_id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);

                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column                   = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column                 = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $params['where']['a.comm_type_id'] = ['in', $three_type_column];
                }
            }
        }

        //$params['field']='a.id,a.commodity_name,a.cover_image,a.comm_type_id,a.set_type,a.create_dlr_code, b.comm_type_name,b.comm_parent_id,e.id as commodity_set_id,e.dlr_code,e.original_price_range_end,e.count_stock';
        $params['order']        = 'a.id desc';
        $params['query']        = input('get.');
        $params['pagesize']     = input('pagesize');
        $params['shelves_type'] = $set_type;
        $dlr_code               = $this->admin_info['dlr_code'];
        $set_type               = $this->admin_info['type'];
        $list                   = $this->commodity->getShelvesListByDlr($set_type, $dlr_code, $params);
        //   echo $this->commodity->getLastSql();
        //  exit();
        $commodity_set_id_list = [];
        foreach ($list as $key => $val) {
            $commodity_set_id_list[] = $val['commodity_set_id'];
            $list[$key]['is_fight']  = 0;
            if (!empty($val['highest_price'])) $list[$key]['original_price_range_end'] = $val['highest_price'];
        }
        //处理是否已经拼团
        //$fight_dlr_list=$this->fight_group_comm->getDlrByCommodity(['is_enable'=>1,'commodity_set_id'=>['in',$commodity_set_id_list]]);
        $comm_dl_model = new DbCommodityDlr();
        $dlr_list      = $comm_dl_model->getDlrBySetId($commodity_set_id_list);
        $is_fight_id   = [];
        foreach ($list as $key => $val) {
            if (in_array($val['commodity_set_id'], $is_fight_id)) $list[$key]['is_fight'] = 1;
        }
        $comm_parent_list2        = $this->comm_type_model->getCommTypeName();
        $res                      = [];
        $res['list']              = $list;
        $res['comm_parent_list2'] = $comm_parent_list2;
        print_json(0, '', $res);
    }


    public function getSku()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $comm_set_model   = new DbCommoditySet();
        $com_set_row      = $comm_set_model->getOne(['where' => ['id' => $commodity_set_id], 'field' => 'commodity_id,dlr_code,set_type']);
        // var_dump($commodity_set_id);
        // $sku         = $this->comm_service->getSetSku($commodity_set_id);
        $sku           = $this->comm_service->getLimitDiscountSku($commodity_set_id, $this->admin_info['dlr_code']);
        $sku_list      = $sku['sku_list'];
        $commodity_row = $this->commodity->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image']);
        // var_dump($sku);
        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'] . '/';
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);

    }


    /**
     * 通过上架id 获取可以选专营店
     */

    public function ajaxGetDlr()
    {
        $commodity_id = input('get.commodity_id', 0);
        $commodity_set_id = input('get.commodity_set_id') or print_json(1, '', []);

        $fight_group_id = input('get.fight_group_id');
        //$where = ['is_enable'=>1,'commodity_set_id'=>$commodity_set_id];
        // if (!empty($fight_group_id)) $where['fight_group_id']=['<>',$fight_group_id];

        //   $figth_dlr_list=$this->fight_group_comm->getDlrByCommodity($where);
        // $comm_dl_model     =new DbCommodityDlr();
        // $comm_dlr_list     =$comm_dl_model->getDlrBySetId([$commodity_set_id]);
        // if ( !empty($comm_dlr_list[0])){
        // $dlr_code          =  explode(',',$comm_dlr_list[0]['dlr_code']) ;
        // }
        /*  if (empty($figth_dlr_list[0]) && !empty($comm_dlr_list[0])){
              $dlr_code=explode(',',$comm_dlr_list[0]['dlr_code']);
          }else if (!empty($comm_dlr_list[0])){
              $check_dlr=array_diff(explode(',',$comm_dlr_list[0]['dlr_code']),explode(',',$figth_dlr_list[0]['dlr_code']));
              $dlr_code=$check_dlr;
          }*/
        // $dlr_list = $this->dlr->getList(['where'=>['dlr_code'=>['in',$dlr_code]],'field'=>'dlr_code,dlr_name']);
        $base     = new BaseDataService();
        $dlr_list = $base->getDlrList($commodity_set_id);
        print_json(0, '', $dlr_list);


    }


    public function test()
    {
        $dlr_list = ['cx222', '343', '3ooii', '34333'];
        $kk       = ['cx222', '343', '3ooii'];
        $ls       = array_intersect($kk, $dlr_list);
        var_dump($ls);
    }

    public function test2()
    {

        $dd = $this->comm_service->getCommodityInfo(341, 'TEST', 0, '', 28);
        var_dump($dd);

    }

    public function test3()
    {

        $dd = $this->comm_service->getOneSku(186, 2);
        var_dump($dd);
    }

    private function doHook($type = 'delete', $commodity_arr = [], $act_id = 0)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;

        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        $activity = DbFightGroup::where('id', $act_id)->find();

        if (!empty($activity['up_down_channel_dlr'])) {

            $up_down_arr = explode(',', $activity['up_down_channel_dlr']);
            $shelves_ni  = array_intersect(DbDlr::$ni_arr, $up_down_arr);
            $shelves_pz  = array_intersect(DbDlr::$pz1a_arr, $up_down_arr);
            $shelves_qc  = array_intersect(DbDlr::$qc_arr, $up_down_arr);

            if (!empty($shelves_ni)) {
                $shelves_type = DbDlr::$ni_shelves;
            } else if (!empty($shelves_pz)) {
                $shelves_type = DbDlr::$pz1a_shelves;
            } else if (!empty($shelves_qc)) {
                $shelves_type = DbDlr::$qc_shelves;
            } else {
                return false;
            }

            switch ($type) {
                case 'add':
                case 'update':
                    # 添加商品不需要刷活动信息
                    foreach ($commodity_arr as $item_one) {
                        $item_one['shelves_type'] = $shelves_type;
                        Hook::listen('flat_fight_group', $item_one);
                    }
                    break;
                case 'delete':
                    # 删除就只需要将数据删除就ok
                    foreach ($commodity_arr as $item_one) {
                        $del_params = [
                            'shelves_type' => $shelves_type,
                            'del_dis'      => true,
                            'act_id'       => $act_id,
                            'commodity_id' => empty($item_one['commodity_id']) ? $item_one : $item_one['commodity_id']
                        ];
                        Hook::listen('flat_fight_group', $del_params);
                    }
                    break;
            }

            $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
            Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);

            return $result;
        }

        return false;
    }

    /**
     * 获取卡券
     */
    public function ajaxGetCard(){
        $this->_checkAjax();
        $model = new DbCard();
        $card_name = input('get.card_name');
        $set_type = input('set_type', 0);
        $where = [];
        $shelves_type = 5;
        if($set_type == 5){
            $where['up_down_channel_dlr'] =[['notlike','%PZ1ASM%'],['notlike','%PZ1AAPP%'],['notlike','%QCSM%'],['notlike','%QCAPP%'],'and'];
        }elseif($set_type == 6){
            $shelves_type = 6;
            $where[] =[['exp','FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'],['exp','FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'],'or'];
        }elseif($set_type == 7){
            $shelves_type = 7;
            $where[] =[['exp','FIND_IN_SET("QCSM",up_down_channel_dlr)'],['exp','FIND_IN_SET("QCAPP",up_down_channel_dlr)'],'or'];
        }
        if(!empty($card_name))
            $where['card_name'] = ['like','%'.$card_name.'%'];

        $where['shelves_type'] = $shelves_type;
        $where['is_enable'] = 1;
        $where['type'] = 2;
        $where['act_status'] = ['in', [1, 2, 3]];
        $params = [
            'where'=>$where,
            'order'=>'id desc',
            'field'=>'id,is_enable,act_status,card_type,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to,available_count,up_down_channel_name',
            'pagesize'=>input('get.pagesize'),
            'query'=>input('get.')
        ];
        $list = $model->getListPaginate($params);
        $card_type_arr = $model->cardType();

        foreach ($list as $key=>$value){
            $list[$key]['id'] = (string)$value['id'];
            $list[$key]['card_type_name'] =  $card_type_arr[$value['card_type']] ?? '';

            switch ($value['date_type']){
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'].'至'.$value['validity_date_end'];
                    break;
                case 2:
                    if($value['fixed_begin_term']==0){
                        $list[$key]['validity_date'] = "自领取当天有效，有效期".$value['fixed_term'].'天';
                    }elseif ($value['fixed_begin_term']==1){
                        $list[$key]['validity_date'] = "领取后".$value['fixed_term'].'天后有效';
                    }
                    break;
                default:
                    break;
            }
            $value['status_name'] = empty($value['is_enable']) ? '已关闭' : $model->cardStatus()[$value['act_status']];
        }
        print_json(0,'',$list);
    }
}
