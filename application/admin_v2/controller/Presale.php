<?php


namespace app\admin_v2\controller;

use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlr;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbGift;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbPreSaleCommodity;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSystemValue;
use app\common\service\BaseDataService;
use app\common\service\CommodityService;
use app\common\model\db\DbCommodityFlat;
use think\Db;
use think\Hook;

/**
 * 预售控制器
 * Class Presale
 * @package app\admin_v2\controller
 */
class Presale extends Common
{
    private $comm_type_model;
    private $commodity;
    private $card_model;
    private $comm_service;
    private $pre_sale;
    private $pre_sale_commodity;
    private $_dbCommodityDlrTypeObj;
    private $gather_list;

    public function __construct()
    {
        parent::__construct();
        $this->comm_type_model        = new DbCommodityType();
        $this->commodity              = new DbCommodity();
        $this->card_model             = new DbCard();
        $this->comm_service           = new CommodityService();
        $this->pre_sale               = new DbPreSale();
        $this->pre_sale_commodity     = new DbPreSaleCommodity();
        $this->_dbCommodityDlrTypeObj = new DbCommodityDlrType();
        $model = new DbSystemValue();
        $this->gather_list = $model->where(['value_type'=>26,'is_enable'=>1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('gather_list', $this->gather_list);
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('dlr_code', $this->admin_info['dlr_code']);
    }

    /**
     * 车生活
     * @return mixed
     */
    public function live()
    {
        $status         = input('get.status', '');  //1未开始 2 进行中 3已结束
        $title          = input('get.title');
        $gather_id = input('get.gather_id',0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name = input('get.theme_name');
        $admin_type     = $this->admin_info['type'];
        $admin_dlr_code = $this->admin_info['dlr_code'];
        $where          = [];
        $live_type      = input('get.live_type');
        if (empty($live_type)) {
            $set_type                       = 5;
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($live_type == 2) {
            $set_type = 7;
            $where[]  = [['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'],  'or'];
        } else {
            $set_type = 6;
            $where[]  = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if(!empty($gather_id)) {
            $where['a.gather_id'] = $gather_id;
        }
        if(!empty($is_pv_subsidy)) {
            if($is_pv_subsidy != 2){
                $where['a.is_pv_subsidy'] = $is_pv_subsidy;
            }else{
                $where['a.is_pv_subsidy'] = 0;
            }
        }
        if(!empty($theme_name)) {
            $where['a.theme_name'] = array('like','%'.$theme_name.'%');
        }
        $field = 'a.id,a.title,a.front_s_time,a.front_e_time,a.balance_s_time,a.balance_e_time,a.up_down_channel_name,a.is_enable,a.gather_id,a.is_pv_subsidy,a.theme_name';
        $date  = date('Y-m-d H:i:s');
        if (!empty($title)) {
            $where['a.title'] = ['like', "%{$title}%"];
        }
        if (!empty($status)) {
            if ($status == 1) {
                $where['a.front_s_time'] = ['>', $date];
            } else if ($status == 2) {
                $where['a.front_s_time']   = ['<', $date];
                $where['a.balance_e_time'] = ['>', $date];
            } else if ($status == 3) {
                $where['a.balance_e_time'] = ['<', $date];
            } else if ($status == 4) {
                $where['a.is_enable'] = 0;
            }
            if (in_array($status, [1, 2, 3])) {
                $where['a.is_enable'] = 1;
            }
        }

        $where['set_type'] = $set_type;
        $params            = [
            'where' => $where,
            'query' => input('get.'),
            'order' => 'a.id DESC',
            'field' => $field,
        ];
        $list              = $this->pre_sale->getPreSaleList($params);
        $time              = time();
        $gather_array = [];
        foreach ($this->gather_list as $value){
            $gather_array[$value['id']] = $value['name'];
        }
        foreach ($list as $key => $val) {
            $in = empty($val['is_enable']) || $status == 4;
            if (strtotime($val['front_s_time']) > $time) {
                $list[$key]['status'] = $in ? '已关闭' : '未开始';
            } else if (strtotime($val['front_s_time']) <= $time && strtotime($val['balance_e_time']) >= $time) {
                $list[$key]['status'] = $in ? '已关闭' : '进行中';
            } else if (strtotime($val['balance_e_time']) < $time) {
                $list[$key]['status'] = $in ? '已关闭' : '已结束';
            }
            if($val['gather_id'] == 0 || empty($val['gather_id'])){
                $list[$key]['gather_name'] = '-';
            }else{
                if(isset($gather_array[$val['gather_id']])){
                    $list[$key]['gather_name'] = $gather_array[$val['gather_id']];
                }else{
                    $list[$key]['gather_name'] = '-';
                }
            }
            $list[$key]['is_pv_subsidy_status'] = $val['is_pv_subsidy'] == 1 ?'是': '否';
        }

        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('status', $status);
        $this->assign('admin_type', $admin_type);
        $this->assign('live_type', $live_type);
        $this->assign('set_type', $set_type);
        return $this->fetch();
    }

    /**
     * 预售列表
     * <AUTHOR>
     * Date: 2020/10/22
     * Time: 15:30
     */
    public function index()
    {
        $status         = input('get.status');  //1未开始 2 进行中 3已结束
        $title          = input('get.title');
        $admin_type     = $this->admin_info['type'];
        $admin_dlr_code = $this->admin_info['dlr_code'];
        $where          = [];
        $field          = 'a.id,a.title,a.front_s_time,a.front_e_time,a.balance_s_time,a.balance_e_time';
        $date           = date('Y-m-d H:i:s');
        if (!empty($title)) {
            $where['a.title'] = ['like', "%{$title}%"];
        }
        if ($status == 1) {
            $where['a.front_s_time'] = ['>', $date];
        } else if ($status == 2) {
            $where['a.front_s_time']   = ['<', $date];
            $where['a.balance_e_time'] = ['>', $date];
        } else if ($status == 3) {
            $where['a.balance_e_time'] = ['<', $date];
        }
        $params = [
            'where' => $where,
            'query' => input('get.'),
            'order' => 'a.id DESC',
            'field' => $field,
        ];
        $list   = $this->pre_sale->getPreSaleList($params);
        foreach ($list as $key => $val) {
            if ($val['front_s_time'] > $date) {
                $list[$key]['status_name'] = '未开始';
            } else if ($val['front_s_time'] < $date && $val['balance_e_time'] > $date) {
                $list[$key]['status_name'] = '进行中';
            } else {
                $list[$key]['status_name'] = '已结束';
            }
        }
        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('status', $status);
        return $this->fetch();
    }

    public function addLive()
    {
        //角色类型
        $adminType  = $this->admin_info['type'];
        $setType = input('set_type', 5);

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');

        $commodity_class = DbCommodity::commodityClass();
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY9]); // 延保服务包不参加活动

        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('set_type', $setType);
        $this->assign('admin_type', $adminType);
        $this->assign('set_type', input('set_type', 5));
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', (new DbSystemValue())->getNameList(20));
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));
        $this->assign('live_type', input('live_type'));
        return $this->fetch('add_live');
    }

    /**
     * 显示预售添加页面数据
     * @return mixed
     * <AUTHOR>
     * Date: 2020/10/28
     * Time: 15:32
     */
    public function add()
    {
        $comm_parent_list = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list   = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }
        $this->assign('comm_parent_id', input('get.comm_parent_id'));
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);
        return $this->fetch();
    }

    /**
     * 获取优惠券列表
     * <AUTHOR>
     * Date: 2020/10/28
     * Time: 15:32
     */
    public function ajaxGetCouponList()
    {
        $params['query']              = [];
        $params['where']['type']      = 1;
        $params['where']['is_enable'] = 1;
        $date                         = date('Y-m-d');
        $params['where'][]            = ['exp', " (date_type=1 AND validity_date_end>='{$date}') OR date_type=2 "];
        $params['where']['dlr_code']  = $this->admin_info['dlr_code'];
        $params['order']              = 'id desc';
        $params['field']              = 'id,card_name,card_id,card_type,validity_date_start,validity_date_end,fixed_term';
        $params['pagesize']           = input('pagesize');
        $list                         = $this->card_model->getListPaginate($params);
        foreach ($list as $key => $val) {
            $val['card_type_name'] = $this->card_model->cardType()[$val['card_type']];
            $val['validity_date']  = $val['validity_date_start'] . ' ~ ' . $val['validity_date_end'];
            if ($val['validity_date'] == ' ~ ') {
                $validity_date_test   = '领取后';
                $validity_date_test   .= $val['fixed_term'] == 0 ? '当' : $val['fixed_term'];
                $val['validity_date'] = $validity_date_test . '天有效';
            }
        }
        print_json(0, '', $list);
    }

    //获取下级分类
    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);
    }


    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetLiveCommodityList()
    {
        $commodity_name  = input('commodity_name');
        $top_type        = input('comm_parent_id');
        $second_type     = input('sub_comm_type_id');
        $third_type      = input('three_comm_type_id');
        $active_id       = input('active_id');
        $commodity_class = input('commodity_class');
        $live_type       = input('live_type');
        $commodity_dlr_type_id       = input('commodity_dlr_type_id');

        $where = [
            'b.listing_type' => 1,
            'c.dd_commodity_type' => ['neq', 12],
        ];

        if (empty($live_type)) {
            $where['b.shelves_type']        = 5;
            $where['b.qsc_group'] =  '';  // 取送车服务包不能参加
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif($live_type == 2) {
            $where['b.shelves_type'] = 7;
            $where[]                 = [['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], 'or'];
        } else {
            $where['b.shelves_type'] = 6;
            $where[]                 = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        if (!empty($commodity_dlr_type_id)) {
            $where['b.commodity_dlr_type_id'] = $commodity_dlr_type_id;
        }

        $type_id = 0;
        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

        if (!empty($commodity_class)) {
            $where['a.commodity_class'] = $commodity_class;
        } else {
            $where['a.commodity_class']   = ['neq', DbCommodity::COMMODITY_CLASS_KEY9]; // 延保服务包不能参加

        }

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }


        $field  = "b.commodity_dlr_type_id,a.commodity_class,a.comm_type_id,a.up_down_channel_name,a.commodity_id,
        a.commodity_set_id,a.commodity_name,a.cover_image,a.price,a.count_stock,c.dd_commodity_type";
        $params = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );

        $flat = new DbCommodityFlat();
        $list = $flat->getCommodityList($params);
        $date = date('Y-m-d H:i:s');

        //判断是否已经参与其他预售了
        $list = $this->pre_sale->getIsPreProduct($list, [
            'a.id'             => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'      => ['=', 1],
            'a.front_s_time'   => ['<', $date],
            'a.balance_e_time' => ['>', $date]
        ]);

        //是否有参加限时优惠
        $list = (new DbLimitDiscount())->getIsLimitProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.start_time' => ['<', $date],
            'a.end_time'   => ['>', $date],
        ]);

        //是否已团购
        $list = (new DbFightGroup())->getIsFightGroupProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.start_time' => ['<', $date],
            'a.end_time'   => ['>', $date],
        ]);

        //是否已套装
        $list = (new BuCheapSuitIndex())->getIsCheap($list, [
            'a.is_enable' => ['=', 1],
            'a.s_time'    => ['<', $date],
            'a.e_time'    => ['>', $date],
        ]);

        //是否满优惠
        $list = (new DbFullDiscount())->getIsFullDiscount($list, [
            'a.is_enable'  => ['=', 1],
            'a.start_time' => ['<', $date],
            'a.end_time'   => ['>', $date],
        ]);

        //是否NN
        $list = (new DbNDiscount())->getIsNDiscount($list, [
            'a.is_enable'  => ['=', 1],
            'a.start_time' => ['<', $date],
            'a.end_time'   => ['>', $date],
        ]);

        //是否秒杀
        $list = (new DbSeckill())->getIsSeckillProduct($list, [
            'a.id'         => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]],
        ]);

        //是否参加买赠
        $list = (new DbGift())->getIsGiftProduct($list,[
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);

        $res['list'] = $list;
        print_json(0, '', $res);
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetCommodityList()
    {
        $commodity_name                       = input('commodity_name');
        $comm_parent_id                       = input('comm_parent_id');
        $sub_comm_type_id                     = input('get.sub_comm_type_id');
        $three_comm_type_id                   = input('get.three_comm_type_id');
        $set_type                             = input('set_type');
        $params['where']['a.commodity_class'] = 1;
        $params['query']                      = [];

        $params['where']['is_grouped']    = 0;
        if (!empty($commodity_name)) {
            $params['where']['a.commodity_name'] = ['like', "%$commodity_name%"];
        }
        if (!empty($three_comm_type_id)) {
            $params['where']['a.comm_type_id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);

                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column                   = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column                 = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $params['where']['a.comm_type_id'] = ['in', $three_type_column];
                }
            }
        }
        //$params['field']='a.id,a.commodity_name,a.cover_image,a.comm_type_id,a.set_type,a.create_dlr_code, b.comm_type_name,b.comm_parent_id,e.id as commodity_set_id,e.dlr_code,e.original_price_range_end,e.count_stock';
        $params['order']        = 'a.id desc';
        $params['query']        = input('get.');
        $params['pagesize']     = input('pagesize');
        $params['shelves_type'] = $set_type;
        $dlr_code               = $this->admin_info['dlr_code'];
        $list                   = $this->commodity->getShelvesListByDlr(1, $dlr_code, $params);
        //   echo $this->commodity->getLastSql();
        //  exit();
        $commodity_set_id_list = [];
        foreach ($list as $key => $val) {
            $commodity_set_id_list[] = $val['commodity_set_id'];
            $list[$key]['is_fight']  = 0;
            if (!empty($val['highest_price'])) $list[$key]['original_price_range_end'] = $val['highest_price'];
        }
        //处理是否已经拼团
        $pre_sale_list = $this->pre_sale_commodity->getDlrByCommodity(['is_enable' => 1, 'commodity_set_id' => ['in', $commodity_set_id_list]]);
        //$fight_dlr_list=$this->fight_group_comm->getDlrByCommodity(['is_enable'=>1,'commodity_set_id'=>['in',$commodity_set_id_list]]);
//        $comm_dl_model= new DbCommodityDlr();
//        $dlr_list     = $comm_dl_model->getDlrBySetId($commodity_set_id_list);
        $ids = [];
        foreach ($pre_sale_list as $key => $value) {
            $ids[] = $value['commodity_set_id'];
        }
        foreach ($list as $key => $val) {
            if (in_array($val['commodity_set_id'], $ids)) $list[$key]['is_fight'] = 1;
        }

        $comm_parent_list2        = $this->comm_type_model->getCommTypeName();
        $res                      = [];
        $res['list']              = $list;
        $res['comm_parent_list2'] = $comm_parent_list2;
        print_json(0, '', $res);
    }

    /**
     * 获取sku列表
     * <AUTHOR>
     * Date: 2020/10/27
     * Time: 14:17
     */
    public function getSku()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $comm_set_model   = new DbCommoditySet();
        $sku              = $this->comm_service->getLimitDiscountSku($commodity_set_id, $this->admin_info['dlr_code']);
        $sku_list         = $sku['sku_list'];
        $commodity_row    = $this->commodity->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image']);
        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'] . '/';
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);

    }

    /**
     * 车生活
     * @throws \think\exception\PDOException
     */
    public function saveLive()
    {
        $post = input('post.');
        $id              = input('post.id');
        $purchase_number = input('post.purchase_number');
        $is_coupon       = input('post.is_coupon');
        $coupon_id       = input('post.coupon_id');
        $set_type        = input('post.set_type', 5);
        $gather_id = input('gather_id',0);
        $is_pv_subsidy = input('is_pv_subsidy',0);
        $theme_name = input('theme_name');
        $sku_list        = $post['sku_list'] ?? [];
        if (empty($sku_list)) {
            print_json(1, '商品数据不能为空');
        }
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $dec = input('post.dec') or print_json(1, '活动规则不能为空');
        $deposit = input('post.deposit') or print_json(1, '定金不能为空');
        $deduction = input('post.deduction') or print_json(1, '抵扣不能为空');
        $card_available      = input('post.card_available', 0);
        $is_enable           = input('post.is_enable');
        $up_down_channel_arr = $post['up_down_channel'] ?? [];
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $up_down_channel_arr),
            input('post.dlr_code', '')
        );

        if (!empty($id)) {
            $res = $this->pre_sale->getOneByPk($id);
            if (time() < strtotime($res['front_s_time'])) {
                $deposit_start_time = input('post.deposit_start_time') or print_json(1, '定金支付开始时间不能为空');
                $deposit_end_time = input('post.deposit_end_time') or print_json(1, '定金支付结束时间不能为空');
                if ($deposit_start_time > $deposit_end_time) {
                    print_json(1, '定金支付开始时间不能大于结束时间');
                }
                $balance_start_time = input('post.balance_start_time') or print_json(1, '尾款支付开始时间不能为空');
                $balance_end_time = input('post.balance_end_time') or print_json(1, '尾款支付结束时间不能为空');
                if ($balance_start_time > $balance_end_time) {
                    print_json(1, '尾款支付开始时间不能大于结束时间');
                }
                if ($balance_start_time < $deposit_start_time || $balance_start_time < $deposit_end_time) {
                    print_json(1, '尾款支付开始时间不能大于定金开始结束时间');
                }
            } else {
                $deposit_start_time = $res['front_s_time'];
                $deposit_end_time   = $res['front_e_time'];
                $balance_start_time = $res['balance_s_time'];
                $balance_end_time   = $res['balance_e_time'];
            }
        } else {
            if ($is_enable == 1) {
                $date = date('Y-m-d H:i:s');
                //判断是否已经参与其他预售了
                $list = $this->pre_sale->getIsPreProduct(
                    $sku_list, [
                    'a.id'             => ['<>', $id],
                    'a.is_enable'      => ['=', 1],
                    'a.front_s_time'   => ['<', $date],
                    'a.balance_e_time' => ['>', $date],
                    'a.set_type'       => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_pre'] == 1) print_json(1, $v['commodity_name'] . '已参加其它预售活动');
                }
                //是否有参加限时优惠
                $list = (new DbLimitDiscount())->getIsLimitProduct(
                    $sku_list, [
                    'a.is_enable'  => ['=', 1],
                    'a.start_time' => ['<', $date],
                    'a.end_time'   => ['>', $date],
                    'a.set_type'   => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_limit'] == 1) print_json(1, $v['commodity_name'] . '已参加限时优惠活动');
                }
                //是否已团购
                $list = (new DbFightGroup())->getIsFightGroupProduct(
                    $sku_list, [
                    'a.is_enable'  => ['=', 1],
                    'a.start_time' => ['<', $date],
                    'a.end_time'   => ['>', $date],
                    'a.set_type'   => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_fight'] == 1) print_json(1, $v['commodity_name'] . '已参加团购活动');
                }
                //是否已套装
                $list = (new BuCheapSuitIndex())->getIsCheap(
                    $sku_list, [
                    'a.is_enable' => ['=', 1],
                    'a.s_time'    => ['<', $date],
                    'a.e_time'    => ['>', $date],
                    'a.type'  => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_cheap'] == 1) print_json(1, $v['commodity_name'] . '已参加套装活动');
                }
                //是否满优惠
                $list = (new DbFullDiscount())->getIsFullDiscount(
                    $sku_list, [
                    'a.is_enable'  => ['=', 1],
                    'a.start_time' => ['<', $date],
                    'a.end_time'   => ['>', $date],
                    'a.set_type'   => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_full'] == 1) print_json(1, $v['commodity_name'] . '已参加满优惠活动');
                }
                //是否NN
                $list = (new DbNDiscount())->getIsNDiscount(
                    $sku_list, [
                    'a.is_enable'  => ['=', 1],
                    'a.start_time' => ['<', $date],
                    'a.end_time'   => ['>', $date],
                    'a.set_type'   => $set_type,
                ]
                );
                foreach ($list as $v) {
                    if ($v['is_n_discount'] == 1) print_json(1, $v['commodity_name'] . '已参加N件N折活动');
                }

                $list = (new DbGift())->getIsGiftProduct($list,[
                    'a.is_enable'  => ['=', 1],
                    'a.set_type'   => $set_type,
                    'a.act_status' => ['in', [1, 2]]
                ]);
                foreach ($list as $v) {
                    if ($v['is_gift'] == 1) print_json(1, $v['commodity_name'] . '已参加买赠活动');
                }
            }
            $deposit_start_time = input('post.deposit_start_time') or print_json(1, '定金支付开始时间不能为空');
            $deposit_end_time = input('post.deposit_end_time') or print_json(1, '定金支付结束时间不能为空');
            if ($deposit_start_time > $deposit_end_time) {
                print_json(1, '定金支付开始时间不能大于结束时间');
            }
            $balance_start_time = input('post.balance_start_time') or print_json(1, '尾款支付开始时间不能为空');
            $balance_end_time = input('post.balance_end_time') or print_json(1, '尾款支付结束时间不能为空');
            if ($balance_start_time > $balance_end_time) {
                print_json(1, '尾款支付开始时间不能大于结束时间');
            }
            if ($balance_start_time < $deposit_start_time || $balance_start_time < $deposit_end_time) {
                print_json(1, '尾款支付开始时间不能大于定金开始结束时间');
            }
        }

        $this->pre_sale->startTrans();

        $act_status = $this->pre_sale->getActStatus($deposit_start_time, $balance_end_time);
        $datas = [
            'title'                 => $title,
            'tag'                   => $post['tag'],
            'dec'                   => $dec,
            'front_money'           => $deposit,
            'dedu_money'            => $deduction,
            'front_s_time'          => $deposit_start_time,
            'front_e_time'          => $deposit_end_time,
            'balance_s_time'        => $balance_start_time,
            'balance_e_time'        => $balance_end_time,
            'act_status'            => $act_status,
            'purchase_number'       => $purchase_number,
            'can_use_card'          => $is_coupon,
            'can_use_card_id'       => $coupon_id,
            'set_type'              => $set_type,
            'up_down_channel_name'  => implode(',', $post['up_down_channel_name'] ?? []),
            'up_down_channel_dlr'   => $up_down_channel_dlr,
            'is_enable'             => $is_enable,
            'card_available'        => $card_available,
            'e3s_activity_id'       => $post['e3s_activity_id'] ?? 0,
            'activity_type'         => $post['activity_type'] ?? 0,
            'settlement_rule_id'    => $post['settlement_rule_id'],
            'settlement_rule_name'  => $post['settlement_rule'],
            'settlement_rule_type'  => $post['settlement_rule_type'],
            'settlement_rule_value' => $post['settlement_rule_value'],
            'act_sett_standard'     => $post['act_sett_standard'],
            'rel_card_ids'          => $post['rel_card_ids'] ?? '',
            'gather_id'             => $gather_id,
            'is_pv_subsidy'         => $is_pv_subsidy,
            'theme_name'            => $theme_name,
        ];
        if (!empty($datas['e3s_activity_id'])){
            if (empty($datas['activity_type'])) print_json(1, '存在e3s活动时活动设置类型必选');
        }else{
            $datas['activity_type'] = 0;
        }
        $e3s_activity = input('e3s_activity', '');
        if (!empty($e3s_activity)){
            $datas['e3s_activity_name'] = explode(' | ', $e3s_activity)[1];
        }

        if (empty($id)) {  //插入
            $datas['creator'] = $this->admin_info['username'];
            $last_id    = $this->pre_sale->insertGetId($datas);
            $data       = [];
            foreach ($sku_list as $key => $value) {
                $data[] = [
                    'pre_sale_id'      => $last_id,
                    'commodity_id'     => $value['commodity_id'],
                    'commodity_set_id' => $value['commodity_set_id'],
                    'sku_price'        => json_encode($value['sku_list'] ?? []),
                    'creator'          => $this->admin_info['username'],
                    'dlr_code'         => 'NISSAN',
                    'is_enable'        => 1,
                ];
            }
            $res = $this->pre_sale_commodity->insertAll($data);
            $this->pre_sale->commit();
            if (($act_status == 2) && !empty($is_enable)) {
                $pre_commodity_arr = $this->pre_sale->getAllPreSale([
                    'where' => ['a.id' => $last_id],
                    'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                ]);
                $this->doHook('add', $pre_commodity_arr, $last_id);
            }
            if ($res) print_json(0, '保存成功');
        } else {
            $datas['modifier'] = $this->admin_info['username'];
            $last_id    = $id;

            $old_commodity_id_arr = $this->pre_sale_commodity->where(['pre_sale_id' => $id])->column('commodity_id');
            $new_commodity_id_arr = array_column($sku_list, 'commodity_id');
            $rm_commodity_id_arr  = array_diff($old_commodity_id_arr, $new_commodity_id_arr);

            $this->pre_sale->where(['id' => $id])->update($datas);
            $this->pre_sale_commodity->where(['pre_sale_id' => $id])->delete();

            $data = [];
            foreach ($sku_list as $key => $value) {
                $data[] = [
                    'pre_sale_id'       => $id,
                    'commodity_id'      => $value['commodity_id'],
                    'commodity_set_id'  => $value['commodity_set_id'],
                    'sku_price'         => json_encode($value['sku_list'] ?? []),
                    'creator'           => $this->admin_info['username'],
                    'modifier'          => $this->admin_info['username'],
                    'dlr_code'          => 'NISSAN',
                    'is_enable'         => 1,
                    'last_updated_date' => date('Y-m-d H:i:s')
                ];
            }
            $res = $this->pre_sale_commodity->insertAll($data);
            $this->pre_sale->commit();
            if (($act_status == 2) && !empty($is_enable)) {
                $pre_commodity_arr = $this->pre_sale->getAllPreSale([
                    'where' => ['a.id' => $last_id],
                    'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                ]);
                $this->doHook('update', $pre_commodity_arr, $last_id);
            } else if (empty($is_enable)) {
                $pre_commodity_arr = $this->pre_sale->getAllPreSale([
                    'where' => ['a.id' => $last_id],
                    'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
                ]);
                $this->doHook('delete', $pre_commodity_arr, $last_id);
            }

            if (!empty($rm_commodity_id_arr)) {
                $this->doHook('delete', $rm_commodity_id_arr, $last_id);
            }

            if ($res) print_json(0, '修改成功');
        }

        print_json(1, '保存失败');
    }

    /**
     * 新增和修改预售活动
     * @throws \think\exception\PDOException
     * <AUTHOR>
     * Date: 2020/10/28
     * Time: 9:52
     */
    public function save()
    {
        $id              = input('post.id');
        $purchase_number = input('post.purchase_number');
        $is_coupon       = input('post.is_coupon');
        $coupon_id       = input('post.coupon_id');
        $sku_list        = $_POST['sku_list'];
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $dec = input('post.dec') or print_json(1, '活动规则不能为空');
        $deposit = input('post.deposit') or print_json(1, '定金不能为空');
        $deduction = input('post.deduction') or print_json(1, '抵扣不能为空');

        if (!empty($id)) {
            $res = $this->pre_sale->getOneByPk($id);
            if (time() < strtotime($res['front_s_time'])) {
                $deposit_start_time = input('post.deposit_start_time') or print_json(1, '定金支付开始时间不能为空');
                $deposit_end_time = input('post.deposit_end_time') or print_json(1, '定金支付结束时间不能为空');
                if ($deposit_start_time > $deposit_end_time) {
                    print_json(1, '定金支付开始时间不能大于结束时间');
                }
                $balance_start_time = input('post.balance_start_time') or print_json(1, '尾款支付开始时间不能为空');
                $balance_end_time = input('post.balance_end_time') or print_json(1, '尾款支付结束时间不能为空');
                if ($balance_start_time > $balance_end_time) {
                    print_json(1, '尾款支付开始时间不能大于结束时间');
                }
                if ($balance_start_time < $deposit_start_time || $balance_start_time < $deposit_end_time) {
                    print_json(1, '尾款支付开始时间不能大于定金开始结束时间');
                }
            } else {
                $deposit_start_time = $res['front_s_time'];
                $deposit_end_time   = $res['front_e_time'];
                $balance_start_time = $res['balance_s_time'];
                $balance_end_time   = $res['balance_e_time'];
            }
        } else {
            $deposit_start_time = input('post.deposit_start_time') or print_json(1, '定金支付开始时间不能为空');
            $deposit_end_time = input('post.deposit_end_time') or print_json(1, '定金支付结束时间不能为空');
            if ($deposit_start_time > $deposit_end_time) {
                print_json(1, '定金支付开始时间不能大于结束时间');
            }
            $balance_start_time = input('post.balance_start_time') or print_json(1, '尾款支付开始时间不能为空');
            $balance_end_time = input('post.balance_end_time') or print_json(1, '尾款支付结束时间不能为空');
            if ($balance_start_time > $balance_end_time) {
                print_json(1, '尾款支付开始时间不能大于结束时间');
            }
            if ($balance_start_time < $deposit_start_time || $balance_start_time < $deposit_end_time) {
                print_json(1, '尾款支付开始时间不能大于定金开始结束时间');
            }
        }


        $this->pre_sale->startTrans();
        if (empty($id)) {  //插入
            $datas   = [
                'title'           => $title,
                'dec'             => $dec,
                'front_money'     => $deposit,
                'dedu_money'      => $deduction,
                'front_s_time'    => $deposit_start_time,
                'front_e_time'    => $deposit_end_time,
                'balance_s_time'  => $balance_start_time,
                'balance_e_time'  => $balance_end_time,
                'purchase_number' => $purchase_number,
                'can_use_card'    => $is_coupon,
                'can_use_card_id' => $coupon_id,
                'creator'         => $this->admin_info['username'],
            ];
            $last_id = $this->pre_sale->insertGetId($datas);
            $data    = [];
            foreach ($sku_list as $key => $value) {
                $data[] = [
                    'pre_sale_id'      => $last_id,
                    'commodity_id'     => $value['commodity_id'],
                    'commodity_set_id' => $value['commodity_set_id'],
                    'sku_price'        => json_encode($value['sku_list'] ?? []),
                    'creator'          => $this->admin_info['username'],
                    'dlr_code'         => 'NISSAN',
                    'is_enable'        => 1,
                ];
            }
            $res = $this->pre_sale_commodity->insertAll($data);
            $this->pre_sale->commit();
            if ($res) print_json(0, '保存成功');
        } else {
            $datas = [
                'title'             => $title,
                'dec'               => $dec,
                'front_money'       => $deposit,
                'dedu_money'        => $deduction,
                'front_s_time'      => $deposit_start_time,
                'front_e_time'      => $deposit_end_time,
                'balance_s_time'    => $balance_start_time,
                'balance_e_time'    => $balance_end_time,
                'purchase_number'   => $purchase_number,
                'can_use_card'      => $is_coupon,
                'can_use_card_id'   => $coupon_id,
                'modifier'          => $this->admin_info['username'],
                'last_updated_date' => date('Y-m-d H:i:s')
            ];
            $this->pre_sale->where(['id' => $id])->update($datas);
            $this->pre_sale_commodity->where(['pre_sale_id' => $id])->delete();
            $data = [];
            foreach ($sku_list as $key => $value) {
                $data[] = [
                    'pre_sale_id'       => $id,
                    'commodity_id'      => $value['commodity_id'],
                    'commodity_set_id'  => $value['commodity_set_id'],
                    'sku_price'         => json_encode($value['sku_list'] ?? []),
                    'creator'           => $this->admin_info['username'],
                    'modifier'          => $this->admin_info['username'],
                    'dlr_code'          => 'NISSAN',
                    'is_enable'         => 1,
                    'last_updated_date' => date('Y-m-d H:i:s')
                ];
            }
            $res = $this->pre_sale_commodity->insertAll($data);
            $this->pre_sale->commit();
            if ($res) print_json(0, '修改成功');
        }
        print_json(1, '保存失败');
    }

    public function updateLive()
    {
        $id                   = input('get.id');
        $pre_sale             = $this->pre_sale->getOneByPk($id);
        $pre_sale_commodity   = $this->pre_sale_commodity->getSalePreCommodity(['a.pre_sale_id' => $id]);
        $pre_sale_commodity   = collection($pre_sale_commodity)->toArray();
        $row_comm             = [];
        $commodity_set_id_arr = [];

        //角色类型
        $adminType  = $this->admin_info['type'];
        $setType = $pre_sale['set_type'];

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');

        $commodity_class = DbCommodity::commodityClass();
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY9]); // 延保服务包不参加活动

        foreach ($pre_sale_commodity as $key => $val) {
            $oneCommodityClass = $val['commodity_class'] ?? 0;
            $oneCommodityDlrTypeId = $val['commodity_dlr_type_id'] ?? 0;

            $val['sku_list'] = json_decode($val['sku_price'], true);
            unset($val['sku_price']);
            unset($val['cover_image']);
            $row_comm[]             = [
                'commodity_name'   => $val['commodity_name'],
                'commodity_set_id' => $val['commodity_set_id'],
                'commodity_id'     => $val['commodity_id'],
                'cover_image'      => $pre_sale_commodity[$key]['cover_image'],
                'sku_list'         => json_encode($val),
            ];
            $commodity_set_id_arr[] = $val['commodity_set_id'];
        }

        $up_down_channel_info = (new DbSystemValue())->getNameList(20);
        $info_str             = implode(',', array_keys($up_down_channel_info));

        //优惠券
        $card_arr = [];
        $selected_data = [];
        $commodity_card_name = [];
        if(!empty($pre_sale['rel_card_ids'])) {
            $commodity_card_name = (new DbCard())->getColumn(['where' => ['id' => ['in', $pre_sale['rel_card_ids']]], 'column' => 'card_name']);
            $selected_data = (new DbCard())->getColumn(['where' => ['id' => ['in', $pre_sale['rel_card_ids']]], 'column' => 'id,card_name']);
            $card_arr = explode(',', $pre_sale['rel_card_ids']);
        }
        $pre_sale['commodity_card_name'] = implode(',', $commodity_card_name);
        $pre_sale['num_card'] = empty($pre_sale['rel_card_ids']) ? 0 : count(explode(',', $pre_sale['rel_card_ids']));
        $this->assign('selected',json_encode($card_arr));
        $this->assign('selected_data', $selected_data);
        $this->assign('selected_card_name',json_encode($commodity_card_name));

        $this->assign('oneCommodityClass', $oneCommodityClass);
        $this->assign('oneCommodityDlrTypeId', $oneCommodityDlrTypeId);
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('up_down_channel', $up_down_channel_info);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('dlr_hidden', (strpos($pre_sale['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
        $this->assign('dlr_str', $this->getDlrInInfo($pre_sale['up_down_channel_dlr']));
        $this->assign('comm_set_id_arr', json_encode_cn($commodity_set_id_arr));
        $this->assign('row_comm', $row_comm);
        $this->assign('row', $pre_sale);
        $this->assign('set_type', $setType);
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));
        $this->assign('live_type', input('live_type'));
        return $this->fetch('update_live');

    }

    /**
     * 显示编辑页面
     * @return mixed
     * <AUTHOR>
     * Date: 2020/10/28
     * Time: 15:33
     */
    public function update()
    {
        $id                 = input('get.id');
        $pre_sale           = $this->pre_sale->getOneByPk($id);
        $pre_sale_commodity = $this->pre_sale_commodity->getSalePreCommodity(['a.pre_sale_id' => $id]);
        $pre_sale_commodity = collection($pre_sale_commodity)->toArray();
        $row_comm           = [];

        $commodity_set_ids = [];
        foreach ($pre_sale_commodity as $key => $val) {
            $commodity_set_ids[] = $val['commodity_set_id'];
        }
        $comm_dl_model = new DbCommodityDlr();
        $dlr_list      = $comm_dl_model->getDlrBySetId($commodity_set_ids);
        foreach ($pre_sale_commodity as $key => $val) {
            $val['sku_list'] = json_decode($val['sku_price'], true);
            unset($val['sku_price']);
            unset($val['cover_image']);
            $row_comm[] = [
                'commodity_name' => $val['commodity_name'],
                'cover_image'    => $pre_sale_commodity[$key]['cover_image'],
                'commodity_id'   => $val['commodity_id'],
                'sku_list'       => json_encode($val),
            ];
        }
        $comm_parent_list = $this->comm_type_model->getCommodityByParentId(0);
        $comm_type_list   = [];
        if (!empty($comm_parent_id)) {
            $comm_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        }
        $comm_three_type_list = [];
        if (!empty($sub_comm_type_id)) {
            $comm_three_type_list = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id]]);
        }

        if ($pre_sale['can_use_card'] == 1) {
            $where['where']['id'] = $pre_sale['can_use_card_id'];
            $card_name            = $this->card_model->getOne($where);
            $this->assign('card_name', $card_name['card_name']);
        } else {
            $this->assign('card_name', '');
        }
        $this->assign('row', $pre_sale);
        $this->assign('comm_parent_id', input('get.comm_parent_id'));
        $this->assign('comm_parent_list', $comm_parent_list);
        $this->assign('comm_type_list', $comm_type_list);
        $this->assign('comm_three_type_list', $comm_three_type_list);
        $this->assign('row_comm', $row_comm);
        $comm_parent_list = $this->comm_type_model->getCommodityByParentId(0);
        $this->assign('comm_parent_list', $comm_parent_list);
        return $this->fetch();
    }

    /**
     * 删除预售活动
     * @title
     * @url
     * <AUTHOR>
     * Date: 2020/10/28
     * Time: 15:32
     */
    public function delete()
    {
        $this->_checkAjax();
        $id  = input('post.id');
        $row = $this->pre_sale->getOneByPk($id);
        $this->pre_sale_commodity->where(['pre_sale_id' => $id])->delete();
        $this->pre_sale->where(['id' => $id])->delete();
        if ($row['set_type'] == 5) {
            $full_commodity_arr = $this->pre_sale->getAllPreSale([
                'where' => ['a.id' => $id],
                'field' => 'a.id,a.up_down_channel_dlr,a.act_status,b.commodity_id'
            ]);
            $this->doHook('delete', $full_commodity_arr, $id);
        }
        print_json(0, '删除成功');
    }

    private function doHook($type = 'delete', $commodity_arr = [], $act_id = 0)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;

        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        $activity = DbPreSale::where('id', $act_id)->find();

        if (!empty($activity['up_down_channel_dlr'])) {

            $up_down_arr = explode(',', $activity['up_down_channel_dlr']);
            $shelves_ni  = array_intersect(DbDlr::$ni_arr, $up_down_arr);
            $shelves_pz  = array_intersect(DbDlr::$pz1a_arr, $up_down_arr);
            $shelves_qc  = array_intersect(DbDlr::$qc_arr, $up_down_arr);

            if (!empty($shelves_ni)) {
                $shelves_type = DbDlr::$ni_shelves;
            } else if (!empty($shelves_pz)) {
                $shelves_type = DbDlr::$pz1a_shelves;
            } else if (!empty($shelves_qc)) {
                $shelves_type = DbDlr::$qc_shelves;
            } else {
                return false;
            }

            switch ($type) {
                case 'add':
                case 'update':
                    # 添加商品不需要刷活动信息
                    foreach ($commodity_arr as $item_one) {
                        $item_one['shelves_type'] = $shelves_type;
                        Hook::listen('flat_presale', $item_one);
                    }
                    break;
                case 'delete':
                    # 删除就只需要将数据删除就ok
                    foreach ($commodity_arr as $item_one) {
                        $del_params = [
                            'shelves_type' => $shelves_type,
                            'del_dis'      => true,
                            'act_id'       => $act_id,
                            'commodity_id' => empty($item_one['commodity_id']) ? $item_one : $item_one['commodity_id']
                        ];
                        Hook::listen('flat_presale', $del_params);
                    }
                    break;
            }

            $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
            Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);

            return $result;
        }
        return false;
    }

    /**
     * 获取卡券
     */
    public function ajaxGetCard(){
        $this->_checkAjax();
        $model = new DbCard();
        $card_name = input('get.card_name');
        $set_type = input('set_type', 0);
        $where = [];
        $shelves_type = 5;
        if($set_type == 5){
            $where['up_down_channel_dlr'] =[['notlike','%PZ1ASM%'],['notlike','%PZ1AAPP%'],['notlike','%QCSM%'],['notlike','%QCAPP%'],'and'];
        }elseif($set_type == 6){
            $shelves_type = 6;
            $where[] =[['exp','FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'],['exp','FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'],'or'];
        }elseif($set_type == 7){
            $shelves_type = 7;
            $where[] =[['exp','FIND_IN_SET("QCSM",up_down_channel_dlr)'],['exp','FIND_IN_SET("QCAPP",up_down_channel_dlr)'],'or'];
        }
        if(!empty($card_name))
            $where['card_name'] = ['like','%'.$card_name.'%'];

        $where['shelves_type'] = $shelves_type;
        $where['is_enable'] = 1;
        $where['type'] = 2;
        $where['act_status'] = ['in', [1, 2, 3]];
        $params = [
            'where'=>$where,
            'order'=>'id desc',
            'field'=>'id,is_enable,act_status,card_type,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to,available_count,up_down_channel_name',
            'pagesize'=>input('get.pagesize'),
            'query'=>input('get.')
        ];
        $list = $model->getListPaginate($params);
        $card_type_arr = $model->cardType();


        foreach ($list as $key=>$value){
            $list[$key]['id'] = (string)$value['id'];
            $list[$key]['card_type_name'] = $card_type_arr[$value['card_type']] ?? '';

            switch ($value['date_type']){
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'].'至'.$value['validity_date_end'];
                    break;
                case 2:
                    if($value['fixed_begin_term']==0){
                        $list[$key]['validity_date'] = "自领取当天有效，有效期".$value['fixed_term'].'天';
                    }elseif ($value['fixed_begin_term']==1){
                        $list[$key]['validity_date'] = "领取后".$value['fixed_term'].'天后有效';
                    }
                    break;
                default:
                    break;
            }
            $value['status_name'] = empty($value['is_enable']) ? '已关闭' : $model->cardStatus()[$value['act_status']];
        }
        print_json(0,'',$list);
    }
}
