<?php
/**
 * 退款原因
 * User: www
 * Date: 17/8/1
 * Time: 11:29
 */

namespace app\admin_v2\controller;


use app\common\model\db\DbOrderRefundReason;
use think\Exception;

class OrderRefundReason extends Common
{

    private $dbOrderRefundReason;

    public function __construct()
    {
        parent::__construct();
        $this->dbOrderRefundReason = new DbOrderRefundReason();
    }

    public function index(){
        $msg = input('get.msg');

        $where = [];
        $query = [
            'msg'=>$msg
        ];
        if(!empty($msg)){
            $where['msg'] = ['like','%'.$msg.'%'];
        }
        $params = [
            'field'=>'*',
            'where'=>$where,
            'query'=>$query,
            'order'=>'sort asc,created_date desc'
        ];
        $list = $this->dbOrderRefundReason->getListPaginate($params);
        $page = $list->render();
        $this->assign('page',$page);
        $this->assign('list',$list);
        $this->assign('query',$query);
        return $this->fetch('order_refund_reason/index');
    }

    public function save(){
        $action = input('action');
        if($action=='add'){
            $result = $this->dbOrderRefundReason->allowField(true)->save(input('post.'));
            if($result)
                print_json(0,'新增成功');
                print_json(0,'新增失败');
        }elseif ($action=='update') {
            try{
                $this->dbOrderRefundReason->allowField(true)->isUpdate(true)->save(input('post.'));
                print_json(0,'编辑成功');
            }catch (Exception $exception){
                print_json(0,'编辑失败'.$exception->getMessage());
            }
        }
    }

    public function getOne(){
        $id = input('get.id');
        if (empty($id))
            print_json(1,'ID不能为空');
        $one = $this->dbOrderRefundReason->getOne(['where'=>['id'=>$id]]);
        print_json(0,'',$one);
    }

    public function delete(){
        $id = input('post.id');
        if(empty($id))
            print_json(1,'ID不能为空');
        $result = $this->dbOrderRefundReason->where(['id'=>$id])->delete();
        if($result)
            print_json(0,'删除成功');
        print_json(1,'删除失败');

    }
}