<?php


namespace app\admin_v2\controller;

use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbGift;
use app\common\model\db\DbGiftCommodity;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSystemValue;
use app\common\service\BaseDataService;
use app\common\service\CommodityService;
use think\Db;
use think\Hook;
use think\Log;
use think\Model;

/**
 * 买赠控制器
 * Class Gift
 * @package app\admin_v2\controller
 */
class Gift extends Common
{
    private $dbGift;
    private $dbGiftCommodity;
    private $comm_type_model;
    private $dbDlr;
    private $gather_list;

    public function __construct()
    {
        parent::__construct();
        $this->dbGift          = new DbGift();
        $this->dbGiftCommodity = new DbGiftCommodity();
        $this->comm_type_model = new DbCommodityType();
        $this->dbDlr           = new DbDlr();
        $model                 = new DbSystemValue();
        $this->gather_list     = $model->where(['value_type' => 26, 'is_enable' => 1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('gather_list', $this->gather_list);
    }

    //买赠活动列表
    public function live()
    {
        $status        = input('get.status');  //1未开始 2 进行中 3已结束
        $title         = input('get.title');
        $date          = input('get.date');
        $set_type      = input('get.set_type');
        $gather_id     = input('get.gather_id', 0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name    = input('get.theme_name');
        $where         = [];
        $live_type     = input('get.live_type');
        if (empty($live_type)) {
            $set_type                       = 5;
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($live_type == 2) {
            $set_type = 7;
            $where[]  = [['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], 'or'];
        } else {
            $set_type = 6;
            $where[]  = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if (!empty($date)) {
            $date = explode(' ~ ', $date);
            if (count($date) == 2) {
                $where['a.start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($date[0])), date('Y-m-d H:i:s', strtotime($date[1]) + 86400)]];
            }
        }


        if (!empty($title)) {
            $where['a.title'] = ['like', "%{$title}%"];
        }
        if (!empty($status)) {
            $where['a.act_status'] = $status;
        }
        if (!empty($gather_id)) {
            $where['a.gather_id'] = $gather_id;
        }
        if (!empty($is_pv_subsidy)) {
            if ($is_pv_subsidy != 2) {
                $where['a.is_pv_subsidy'] = $is_pv_subsidy;
            } else {
                $where['a.is_pv_subsidy'] = 0;
            }
        }
        if (!empty($theme_name)) {
            $where['a.theme_name'] = array('like', '%' . $theme_name . '%');
        }

        $field               = 'a.id,a.title,a.created_dlr_code,a.start_time,a.end_time,a.is_enable,a.up_down_channel_name,a.up_down_channel_dlr,a.gather_id,a.is_pv_subsidy,a.theme_name';
        $where['a.set_type'] = $set_type;
        $params              = [
            'where' => $where,
            'query' => input('get.'),
            'order' => 'a.id DESC',
            'field' => $field,
        ];

        $gather_array = [];
        foreach ($this->gather_list as $value) {
            $gather_array[$value['id']] = $value['name'];
        }
        $list = $this->dbGift->getGiftList($set_type, $params);
        if (input('test') == '123') {
            echo $this->dbGift->getLastSql();
            exit;
        }
        foreach ($list as $key => $val) {
            $in = empty($val['is_enable']) || $status == 4;
            if (strtotime($val['start_time']) > time()) {
                $list[$key]['status'] = $in ? '已关闭' : '未开始';
            } else if (strtotime($val['start_time']) <= time() && strtotime($val['end_time']) >= time()) {
                $list[$key]['status'] = $in ? '已关闭' : '进行中';
            } else if (strtotime($val['end_time']) < time()) {
                $list[$key]['status'] = $in ? '已关闭' : '已结束';
            }

            if ($val['gather_id'] == 0 || empty($val['gather_id'])) {
                $list[$key]['gather_name'] = '-';
            } else {
                if (isset($gather_array[$val['gather_id']])) {
                    $list[$key]['gather_name'] = $gather_array[$val['gather_id']];
                } else {
                    $list[$key]['gather_name'] = '-';
                }
            }
            $list[$key]['is_pv_subsidy_status'] = $val['is_pv_subsidy'] == 1 ? '是' : '否';
        }
        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('set_type', $set_type);
        $this->assign('status', $status);
        $this->assign('set_type', $set_type);
        $this->assign('live_type', $live_type);

        return $this->fetch('live');
    }

    public function addLive()
    {
        $setType = input('set_type', 5);

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type],
        ]);
        $setType         = input('get.set_type', 5);

        $nvi_code = '';
        if ($setType == 5) {
            $nvi_code = 'N';
        }
        if ($setType == 7) {
            $nvi_code = 'V';
        }
        $user_level = [];
        if ($setType == 5 || $setType == 7) {
            $sysvalueobj = new DbSystemValue();
            $user_level  = $sysvalueobj->getListByCode(25, $nvi_code);
        }

        $this->assign('user_level', $user_level);
        $this->assign('nvi_code', $nvi_code);

//        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', (new DbSystemValue())->getNameList(20));
        $this->assign('set_type', $setType);
        $commodity_class = DbCommodity::commodityClass();
        $this->assign('commodity_class', $commodity_class);
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY8]);
        $this->assign('gift_commodity_class', $commodity_class);
        $this->assign('live_type', input('live_type'));
        return $this->fetch('add_live');
    }

    /**
     * 获取主商品列表
     * @return mixed
     */
    public function ajaxGetLiveCommodityList()
    {
        $commodity_name  = input('commodity_name');
        $top_type        = input('comm_parent_id');
        $second_type     = input('sub_comm_type_id');
        $third_type      = input('three_comm_type_id');
        $gift_id         = input('gift_id');
        $commodity_class = input('commodity_class');
        $live_type       = input('live_type');
        $where           = [
            'c.commodity_class' => ['neq', DbCommodity::COMMODITY_CLASS_KEY9], // 延保服务包不能参加
        ];
        if (empty($live_type)) {
            $where['b.shelves_type']        = 5;
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($live_type == 2) {
            $where['b.shelves_type'] = 7;
            $where[]                 = [['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], 'or'];
        } else {
            $where['b.shelves_type'] = 6;
            $where[]                 = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        $type_id = 0;

        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

        if (!empty($commodity_class)) {
            $where['a.commodity_class'] = $commodity_class;
        }

//        $where['commodity_class'] = 1;

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }
        $where['a.count_stock'] = array('>', 0);
        $where['b.listing_type'] = 1;
        // $where['b.shelves_type'] = 5;//车生活id
        $field = "b.commodity_dlr_type_id,a.commodity_class,a.comm_type_id,a.up_down_channel_name,a.commodity_id,a.commodity_set_id,a.commodity_name,a.cover_image,a.price,b.count_stock,c.is_grouped";

        $where['c.dd_commodity_type'] = array('in', array_keys((new DbCommodity())->commodityDdType()));
        $params                       = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );
        $start_time                   = input('start_time');
        $end_time                     = input('end_time');
        $flat                         = new DbCommodityFlat();
        $list                         = $flat->getPz1aCommodityList($params);
        $date                         = date('Y-m-d H:i:s');
        if (!empty($start_time)) {
            $start_time = $start_time;
        } else {
            $start_time = $date;
        }
        if (!empty($end_time)) {
            $end_time = $end_time;
        } else {
            $end_time = $date;
        }
        //是否 限时折扣
//        $list = (new DbLimitDiscount())->getIsLimitProduct($list, [
//            'a.is_enable'  => ['=', 1],
////            'a.start_time'   => ['<',$end_time],
////            'a.end_time'   => ['>',$start_time],
//            'a.set_type'   => $where['b.shelves_type'],
//            'a.act_status' => ['in', [1, 2]]
//        ]);

        //判断是否已经参加了预售
        $list = (new DbPreSale())->getIsPreProduct($list, [
            'a.is_enable'  => ['=', 1],
            //            'a.front_s_time'   => ['<',$end_time],
            //            'a.balance_e_time'   => ['>',$start_time],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);

        //判断是否已经参加了满优惠
//        $list = (new DbFullDiscount())->getIsFullDiscount($list, [
//            'a.is_enable'  => ['=', 1],
////            'a.start_time' => ['<', $end_time],
////            'a.end_time'   => ['>', $start_time],
//            'a.act_status' => ['in', [1, 2]]
//        ]);
        $where_time = ['start_time' => $start_time, 'end_time' => $end_time];

        //判断是否已经参加了拼团
        $list = (new DbFightGroup())->getIsFightGroupProduct($list, [
            'a.is_enable'  => ['=', 1],
            //            'a.start_time'   => ['<',$end_time],
            //            'a.end_time'   => ['>',$start_time],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);

        //判断是否已经参加秒杀
        $list = (new DbSeckill())->getIsSeckillProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ], $where_time);

        //判断是否已经参加秒杀
        $list        = (new DbGift())->getIsGiftProduct($list, [
            'a.id'         => ['<>', empty($gift_id) ? 0 : $gift_id],
            'a.is_enable'  => ['=', 1],
            'b.is_gift'    => 0,
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ], $where_time);
        $res         = [];
        $res['list'] = $list;
        print_json(0, '', $res);
    }


    /**
     * 获取赠品商品列表
     * @return mixed
     */
    public function ajaxGetLiveCommodityGiftList()
    {
        $commodity_name        = input('commodity_name');
        $top_type              = input('comm_parent_id');
        $second_type           = input('sub_comm_type_id');
        $third_type            = input('three_comm_type_id');
        $seckill_id            = input('seckill_id');
        $commodity_class       = input('commodity_class');
        $live_type             = input('live_type');
        $commodity_dlr_type_id = input('commodity_dlr_type_id');
        $where                 = [];
        if (empty($live_type)) {
            $where['b.shelves_type']        = 5;
            $where['a.up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], 'and'];
        } elseif ($live_type == 2) {
            $where['b.shelves_type'] = 7;
            $where[]                 = [['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], 'or'];
        } else {
            $where['b.shelves_type'] = 6;
            $where[]                 = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }
        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        $type_id = 0;

        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

        if (!empty($commodity_class)) {
            $where['a.commodity_class'] = $commodity_class;
        } else {
            $where['a.commodity_class'] = ['neq', DbCommodity::COMMODITY_CLASS_KEY8];
        }

        if (!empty($commodity_dlr_type_id)) {
            $where['b.commodity_dlr_type_id'] = $commodity_dlr_type_id;
        }

        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }
        $where['a.count_stock'] = array('>', 0);
        $where['b.listing_type'] = 1;
        // $where['b.shelves_type'] = 5;//车生活id
        $field = "b.commodity_dlr_type_id,a.commodity_class,a.comm_type_id,a.up_down_channel_name,a.commodity_id,a.commodity_set_id,a.commodity_name,a.cover_image,a.price,b.count_stock,c.is_grouped";

        $params     = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );
        $start_time = input('start_time');
        $end_time   = input('end_time');
        $flat       = new DbCommodityFlat();
        $list       = $flat->getPz1aCommodityList($params);
        if (input('test') == 1) {
            print_json($flat->getLastSql());
        }
        $date       = date('Y-m-d H:i:s');
        if (!empty($start_time)) {
            $start_time = $start_time;
        } else {
            $start_time = $date;
        }
        if (!empty($end_time)) {
            $end_time = $end_time;
        } else {
            $end_time = $date;
        }
        //是否 限时折扣
//        $list = (new DbLimitDiscount())->getIsLimitProduct($list, [
//            'a.is_enable'  => ['=', 1],
////            'a.start_time'   => ['<',$end_time],
////            'a.end_time'   => ['>',$start_time],
//            'a.set_type'   => $where['b.shelves_type'],
//            'a.act_status' => ['in', [1, 2]]
//        ]);

        //判断是否已经参加了预售
        $list = (new DbPreSale())->getIsPreProduct($list, [
            'a.is_enable'  => ['=', 1],
            //            'a.front_s_time'   => ['<',$end_time],
            //            'a.balance_e_time'   => ['>',$start_time],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);

        //判断是否已经参加了满优惠
//        $list = (new DbFullDiscount())->getIsFullDiscount($list, [
//            'a.is_enable'  => ['=', 1],
////            'a.start_time' => ['<', $end_time],
////            'a.end_time'   => ['>', $start_time],
//            'a.act_status' => ['in', [1, 2]]
//        ]);

        //判断是否已经参加了拼团
        $list = (new DbFightGroup())->getIsFightGroupProduct($list, [
            'a.is_enable'  => ['=', 1],
            //            'a.start_time'   => ['<',$end_time],
            //            'a.end_time'   => ['>',$start_time],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);

        //判断是否已经参加秒杀
        $list        = (new DbSeckill())->getIsSeckillProduct($list, [
            'a.id'         => ['<>', empty($seckill_id) ? 0 : $seckill_id],
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);
        $res         = [];
        $res['list'] = $list;
        print_json(0, '', $res);
    }

    public function getSkuLive()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $shelves_type     = input('get.set_type', 5);
        $comm_service     = new CommodityService();
        $commodity_flat   = new DbCommodityFlat();
        $sku              = $comm_service->getSetSku($commodity_set_id);
        $sku_list         = $sku['sku_list'];
        $commodity_row    = $commodity_flat->getOne([
            'where'        => ['commodity_id' => $commodity_id, 'commodity_set_id' => $commodity_set_id],
            'field'        => 'commodity_name,cover_image,comm_type_id',
            'shelves_type' => $shelves_type
        ]);
//        $commodity_row = $commodity_flat->alias('a')->join('t_db_commodity b','a.commodity_id = b.id')
//            ->where(['a.commodity_id' => $commodity_id, 'a.commodity_set_id' => $commodity_set_id,'a.shelves_type'=>$shelves_type])
//            ->field('a.commodity_name,a.cover_image,a.comm_type_id,b.is_grouped')
//            ->find();

        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (empty($val['sp_value_arr'])) {
                continue;
            }
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    /**
     * 保存数据
     */
    public function saveLive()
    {
        $post = input('post.');
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '结束时间不能为空');
        $up_down_channel_arr = $post['up_down_channel'] ?? [];
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $up_down_channel_arr),
            input('post.dlr_code', '')
        );
        $purchase_number     = input('post.purchase_number');      //限购数量
        $optional_number     = input('post.optional_number');      //每次可选
        $des                 = input('post.des');
        $is_enable           = input('post.is_enable', '1');
        $user_segment        = input('post.user_segment', 0);
        $id                  = input('post.id');
        $gather_id           = input('gather_id', 0);
        $is_pv_subsidy       = input('is_pv_subsidy', 0);
        $theme_name          = input('theme_name');
        $card_available      = input('post.card_available', 0);

        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');
        $sku_list = [];
        if (empty($_POST['sku_list'])) print_json(1, '商品规格不能为空');
        if (!empty($_POST['sku_list'])) {
            $sku_list = json_decode($_POST['sku_list'], true);
        }
        $set_type             = input('post.set_type');
        $act_status           = $this->dbGift->getActStatus($start_time, $end_time);
        $user_segment_options = input('post.user_segment_options') ?? '';

        $new_user_segment_options = '';
        if ($user_segment == 1) { //有定会员
            if ($set_type == 5) {
                $nvi_code = 'N';
            }
            if ($set_type == 7) {
                $nvi_code = 'V';
            }
            if ($set_type == 6) {
                $nvi_code = 'P';
            }
            if ($set_type == 5 || $set_type == 7) {
                $sysvalueobj = new DbSystemValue();
                $user_level  = $sysvalueobj->getListByCode(25, $nvi_code);
                foreach ($user_level as $user_level_item) {
                    $user_level_arr[] = $user_level_item['value_code'];
                }

                if (!empty($user_level_arr)) {
                    $user_level_str           = implode(",", $user_level_arr);
                    $level_arr                = explode($user_segment_options, $user_level_str);
                    $new_user_segment_options = $user_segment_options . $level_arr[1];
                }

            }

        }
        if ($user_segment == 2) {
            if ($set_type == 5) {
                $new_user_segment_options = 'N';
            }
            if ($set_type == 7) {
                $new_user_segment_options = 'V';
            }
            if ($set_type == 6) {
                $new_user_segment_options = 'P';
            }
        }

        $data = [
            'user_segment'          => $user_segment,
            'user_segment_options'  => $new_user_segment_options,
            'title'                 => $title,
            'start_time'            => $start_time,
            'end_time'              => $end_time,
            'tag'                   => $post['tag'],
            'act_status'            => $act_status,
            'purchase_number'       => $purchase_number,
            'optional_number'       => $optional_number,
            'is_enable'             => $is_enable,
            'des'                   => $des,
            'creator'               => $this->admin_info['username'],
            'created_dlr_code'      => $this->admin_info['dlr_code'],
            'set_type'              => $set_type,
            'up_down_channel_name'  => implode(',', $_POST['up_down_channel_name'] ?? []),
            'up_down_channel_dlr'   => $up_down_channel_dlr,
            'gather_id'             => $gather_id,
            'is_pv_subsidy'         => $is_pv_subsidy,
            'theme_name'            => $theme_name,
            'card_available'        => $card_available,
            'rel_card_ids'          => $post['rel_card_ids'] ?? '',
            'settlement_rule_id'    => $post['settlement_rule_id'],
            'settlement_rule_name'  => $post['settlement_rule'],
            'settlement_rule_type'  => $post['settlement_rule_type'],
            'settlement_rule_value' => $post['settlement_rule_value'],
            'act_sett_standard'     => $post['act_sett_standard'],
        ];

        //开启事物
        $this->dbGift->startTrans();
        $rm_commodity_id_arr = [];
        if (empty($id)) {  //插入
            $last_id = $this->dbGift->insertGetId($data);
            $res     = $this->insertCommodiySkuDataLive($sku_list, $last_id, 'add', $set_type, 0, $data);
            if (!$res) {
                print_json(1, '保存失败');
            }
        } else {    //修改
            if ($is_enable == 1) {
                $where_time = ['start_time' => $start_time, 'end_time' => $end_time];

                $date = date('Y-m-d H:i:s');
                //是否 秒杀
                $list = (new DbSeckill())->getIsSeckillProduct(
                    $sku_list, [
                    'a.id'         => ['<>', $id],
                    'a.is_enable'  => ['=', 1],
                    'a.set_type'   => $set_type,
                    'a.act_status' => ['in', [1, 2]]
                ], $where_time
                );
                foreach ($list as $v) {
                    if ($v['is_seckill'] == 1) print_json(1, $v['commodity_name'] . '已参加其它秒杀活动');
                }
                //判断是否已经参加了预售
                $list = (new DbPreSale())->getIsPreProduct(
                    $sku_list, [
                        'a.is_enable'      => ['=', 1],
                        'a.front_s_time'   => ['<', $date],
                        'a.balance_e_time' => ['>', $date],
                        'a.set_type'       => $set_type,
                    ]
                );
                foreach ($list as $v) {
                    if ($v['is_pre'] == 1) print_json(1, $v['commodity_name'] . '已参加预售活动');
                }
                //判断是否已经参加了拼团
                $list = (new DbFightGroup())->getIsFightGroupProduct(
                    $sku_list, [
                        'a.is_enable'  => ['=', 1],
                        'a.start_time' => ['<', $date],
                        'a.end_time'   => ['>', $date],
                        'a.set_type'   => $set_type,
                    ]
                );
                foreach ($list as $v) {
                    if ($v['is_fight'] == 1) print_json(1, $v['commodity_name'] . '已参加拼团活动');
                }
            }
            $row     = $this->dbGift->getOneByPk($id);
            $last_id = $id;
            if ($row && $row['created_dlr_code'] == $this->admin_info['dlr_code']) {
                $data['modifier']          = $this->admin_info['username'];
                $data['last_updated_date'] = date('Y-m-d H:i:s');
                $res                       = $this->dbGift->isUpdate(true)->saveData($data, ['id' => $id]);
                if ($res) {

                    $old_commodity_id_arr = $this->dbGiftCommodity->where(['gift_id' => $id])->column('commodity_id');
                    $new_commodity_id_arr = array_column($sku_list, 'commodity_id');
                    $rm_commodity_id_arr  = array_diff($old_commodity_id_arr, $new_commodity_id_arr);

                    $this->dbGiftCommodity->where(['gift_id' => $id])->delete();
                    $res_sku = $this->insertCommodiySkuDataLive($sku_list, $id, 'update', $set_type, 0, $data);
                    if (!$res_sku) {
                        print_json(1, '活动商品保存失败');
                    }
                }
            }
        }
        //提交
        $this->dbGift->commit();
        if (in_array($act_status, [1, 2]) && !empty($is_enable)) {
            $limit_discount_comm = $this->dbGiftCommodity->getGiftCommodity(['a.gift_id' => $last_id, 'is_gift' => 0]);
            $this->doHook('add', $limit_discount_comm, $last_id);
        } else if (empty($is_enable)) {
            $limit_discount_comm = $this->dbGiftCommodity->getGiftCommodity(['a.gift_id' => $last_id, 'is_gift' => 0]);
            $this->doHook('delete', $limit_discount_comm, $last_id);
        }

        if (!empty($rm_commodity_id_arr)) {
            $this->doHook('delete', $rm_commodity_id_arr, $last_id);
        }

        print_json(0, '保存成功');
    }

    /**
     * 插入商品及规格
     * @param $sku_list
     * @param $fight_id
     * @param $action
     * @param $home
     * @return bool|int|string
     */
    public function insertCommodiySkuDataLive($sku_list, $fight_id, $action, $set_type = 5, $home = 0, $new_data = [])
    {
        if (empty($sku_list)) {
            return false;
        }
        $data     = [];
        $dlr_code = '';
        if ($set_type == 2) {
            $dlr_code = $this->admin_info['dlr_code'];
        } elseif ($set_type == 3) {
            $dlr_code = 'GWSC';
        }
        foreach ($sku_list as $key => $value) {
            if ($value['is_grouped'] == 0) {
                $sku_id = [];
                if (!empty($value['sku_list'])) {
                    foreach ($value['sku_list'] as $k => $val) {
                        $sku_id[] = $k;
                    }
                }
//                $db_sku_list = Db::name('db_commodity_set_sku')->alias('a')
//                    ->join('t_db_commodity_sku b','a.commodity_sku_id = b.id')
//                    ->where(['a.id'=>array('in',$sku_id)])->field('b.sp_value_list')
//                    ->group('sp_value_list')
//                    ->column('sp_value_list');
                $new_sku_list = Db::name('db_commodity_sku')->alias('a')
                    ->join('t_db_commodity_set_sku b', 'a.id = b.commodity_sku_id')
                    ->where([
                        'b.id'           => array('in', $sku_id),
                        'a.commodity_id' => $value['commodity_id'], 'b.commodity_set_id' => $value['commodity_set_id'],
                        'a.is_enable'    => 1, 'b.is_enable' => 1
                    ])->field('b.id,b.price')->select();
                $new_array    = [];
                foreach ($new_sku_list as $val) {
                    $new_array[$val['id']] = $val['price'];
                }
                $sku_list[$key]['sku_list'] = $new_array;
            } else {
                //组合商品
                $new_sku_list = Db::name('db_commodity_set_sku')->alias('a')
                    ->where([
                        'a.commodity_id' => $value['commodity_id'], 'a.commodity_set_id' => $value['commodity_set_id'],
                        'a.is_enable'    => 1
                    ])->field('id,price')->select();
                $new_array    = [];
                foreach ($new_sku_list as $val) {
                    $new_array[$val['id']] = $val['price'];
                }
                $sku_list[$key]['sku_list'] = $new_array;
            }
        }
        foreach ($sku_list as $key => $val) {
            if ($val['is_gift'] == 1) {
                $price = [];
                foreach ($val['sku_list'] as $k => $v) {
                    $price[$k] = 0;
                }
                $highest_price = 0;
                $lowest_price  = 0;
            } else {
                $price     = $val['sku_list'];
                $sku_price = $val['sku_list'];
                rsort($sku_price);
                $highest_price = reset($sku_price);
                $lowest_price  = end($sku_price);
            }
            $data[]              = [
                'commodity_id'     => $val['commodity_id'],
                'commodity_set_id' => $val['commodity_set_id'],
                'dlr_code'         => $set_type == 1 ? $val['dlr_code'] : $dlr_code,
                'gift_id'          => $fight_id,
                'is_gift'          => $val['is_gift'],
                'creator'          => $this->admin_info['username'],
                'sku_price'        => json_encode($price),
                'highest_price'    => $highest_price,
                'lowest_price'     => $lowest_price,
                //'is_home' => $home
            ];
            $commodity_set_ids[] = $val['commodity_set_id'];
            // var_dump($data);
        }
        //删除已经添加的商品
        $where = ['gift_id' => $fight_id];
        if (!$this->admin_info['type'] == 1) {
            $where['dlr_code'] = $this->admin_info['dlr_code'];
        }
        $this->dbGiftCommodity->where($where)->delete();
        $lastid = $this->dbGiftCommodity->insertAll($data);
        return $lastid;
    }

    private function doHook($type = 'delete', $commodity_arr = [], $act_id = 0)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;

        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        $model    = new DbGift();
        $activity = $model->where('id', $act_id)->find();
        if (!empty($activity['up_down_channel_dlr'])) {

            $up_down_arr = explode(',', $activity['up_down_channel_dlr']);
            $shelves_ni  = array_intersect(DbDlr::$ni_arr, $up_down_arr);
            $shelves_pz  = array_intersect(DbDlr::$pz1a_arr, $up_down_arr);
            $shelves_qc  = array_intersect(DbDlr::$qc_arr, $up_down_arr);

            if (!empty($shelves_ni)) {
                $shelves_type = DbDlr::$ni_shelves;
            } else if (!empty($shelves_pz)) {
                $shelves_type = DbDlr::$pz1a_shelves;
            } else if (!empty($shelves_qc)) {
                $shelves_type = DbDlr::$qc_shelves;
            } else {
                return false;
            }

            switch ($type) {
                case 'add':
                case 'update':
                    # 添加商品不需要刷活动信息
                    foreach ($commodity_arr as $item_one) {
                        $item_one['shelves_type'] = $shelves_type;
                        Hook::listen('flat_gift', $item_one);
                    }
                    break;
                case 'delete':
                    # 删除就只需要将数据删除就ok
                    foreach ($commodity_arr as $item_one) {
                        $del_params = [
                            'shelves_type' => $shelves_type,
                            'del_dis'      => true,
                            'act_id'       => $act_id,
                            'commodity_id' => empty($item_one['commodity_id']) ? $item_one : $item_one['commodity_id']
                        ];
                        Hook::listen('flat_gift', $del_params);
                    }
                    break;
            }

            $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
            Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);

            return $result;
        }

        return false;
    }

    /**
     * 更新数据
     * @return mixed
     */
    public function updateLive()
    {
        $id             = input('get.id');
        $limit_discount = $this->dbGift->getOneByPk($id);

        if ($limit_discount['created_dlr_code'] != $this->admin_info['dlr_code']) print_json(1, '');
        $limit_discount_comm      = $this->dbGiftCommodity->getGiftCommodity(['a.gift_id' => $id, 'is_gift' => 0]);
        $limit_discount_comm_gift = $this->dbGiftCommodity->getGiftCommodity(['a.gift_id' => $id, 'is_gift' => 1]);
        $limit_discount_comm      = collection($limit_discount_comm)->toArray();
        $limit_discount_comm_gift = collection($limit_discount_comm_gift)->toArray();
        $row_comm                 = [];
        $row_comm_gift            = [];
        foreach ($limit_discount_comm as $key => $val) {
            $dlr_name        = $this->dbDlr->getColumn(['where' => ['dlr_code' => ['in', explode(',', $val['dlr_code'])]], 'column' => 'dlr_name']);
            $val['dlr_name'] = implode(',', $dlr_name);
            $val['sku_list'] = json_decode($val['sku_price'], true);
            unset($val['sku_price']);
            unset($val['cover_image']);
            $row_comm[] = [
                'is_grouped'     => 0,
                'commodity_name' => $val['commodity_name'],
                'cover_image'    => $limit_discount_comm[$key]['cover_image'],
                'sku_list'       => json_encode($val),
            ];
        }
        foreach ($limit_discount_comm_gift as $key => $val) {
            $dlr_name        = $this->dbDlr->getColumn(['where' => ['dlr_code' => ['in', explode(',', $val['dlr_code'])]], 'column' => 'dlr_name']);
            $val['dlr_name'] = implode(',', $dlr_name);
            $val['sku_list'] = json_decode($val['sku_price'], true);
            unset($val['sku_price']);
            unset($val['cover_image']);
            $row_comm_gift[] = [
                'is_grouped'     => 1,
                'commodity_name' => $val['commodity_name'],
                'cover_image'    => $limit_discount_comm_gift[$key]['cover_image'],
                'sku_list'       => json_encode($val),
            ];
        }

        $setType              = input('get.set_type', 5);
        $user_segment_options = '';
        if (!empty($limit_discount['user_segment_options'])) {
            $user_segment_options_arr = explode(',', $limit_discount['user_segment_options']);
            $user_segment_options     = $user_segment_options_arr[0];
        }
        //dd($user_segment_options);
        $this->assign('user_segment_options', $user_segment_options);

        $nvi_code = '';
        if ($setType == 5) {
            $nvi_code = 'N';
        }
        if ($setType == 7) {
            $nvi_code = 'V';
        }
        $user_level = [];
        if ($setType == 5 || $setType == 7) {
            $sysvalueobj = new DbSystemValue();
            $user_level  = $sysvalueobj->getListByCode(25, $nvi_code);
        }

        $this->assign('user_level', $user_level);
        $this->assign('nvi_code', $nvi_code);

        //优惠券
        $card_arr = [];
        $selected_data = [];
        $commodity_card_name = [];
        if(!empty($limit_discount['rel_card_ids'])) {
            $commodity_card_name = (new DbCard())->getColumn(['where' => ['id' => ['in', $limit_discount['rel_card_ids']]], 'column' => 'card_name']);
            if (!empty($commodity_card_name)) {
                $selected_data = (new DbCard())->getColumn(['where' => ['id' => ['in', $limit_discount['rel_card_ids']]], 'column' => 'id,card_name']);
                $card_arr = explode(',', $limit_discount['rel_card_ids']);
            }
        }
        if (empty($card_arr)) {
            $limit_discount['rel_card_ids'] = '';
        }
        $limit_discount['commodity_card_name'] = implode(',', $commodity_card_name);
        $limit_discount['num_card'] = empty($limit_discount['rel_card_ids']) ? 0 : count(explode(',', $limit_discount['rel_card_ids']));
        $this->assign('selected',json_encode($card_arr));
        $this->assign('selected_data', $selected_data);
        $this->assign('selected_card_name',json_encode($commodity_card_name));

        $up_down_channel_info = (new DbSystemValue())->getNameList(20);
        $info_str             = implode(',', array_keys($up_down_channel_info));
        $this->assign('id', $id);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', $up_down_channel_info);
        $this->assign('dlr_hidden', (strpos($limit_discount['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
        $this->assign('dlr_str', $this->getDlrInInfo($limit_discount['up_down_channel_dlr']));
        if (empty($limit_discount['dis_type'])) {
            $limit_discount['dis_type'] = 0;
        }
        $this->assign('row', $limit_discount);
        $this->assign('row_comm', $row_comm);
        $this->assign('row_comm_gift', $row_comm_gift);
        $this->assign('set_type', $limit_discount['set_type']);
        $commodity_class = DbCommodity::commodityClass();
        $this->assign('commodity_class', $commodity_class);
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY8]);
        $this->assign('gift_commodity_class', $commodity_class);

        $this->assign('live_type', input('live_type'));
        $type            = DbCommodityDlrType::getType($limit_discount['set_type']);
        $commDlrTypeList = (new DbCommodityDlrType())->getList([
            'field' => 'id,inner_name',
            'where' => ['is_enable' => 1, 'type' => $type]
        ]);
        $this->assign('comm_dlr_type_list', $commDlrTypeList);

        return $this->fetch('update_live');
    }

    //获取下级分类
    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);
    }


    /**
     * 获取卡券
     */
    public function ajaxGetCard()
    {
        $this->_checkAjax();
        $model        = new DbCard();
        $card_name    = input('get.card_name');
        $set_type     = input('set_type', 0);
        $where        = [];
        $shelves_type = 5;
        if ($set_type == 5) {
            $where['up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], ['notlike', '%QCSM%'], ['notlike', '%QCAPP%'], 'and'];
        } elseif ($set_type == 6) {
            $shelves_type = 6;
            $where[]      = [['exp', 'FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'], 'or'];
        } elseif ($set_type == 7) {
            $shelves_type = 7;
            $where[]      = [['exp', 'FIND_IN_SET("QCSM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",up_down_channel_dlr)'], 'or'];
        }
        if (!empty($card_name))
            $where['card_name'] = ['like', '%' . $card_name . '%'];

        $where['shelves_type'] = $shelves_type;
        $where['is_enable']    = 1;
        $where['type']         = 2;
        $where['act_status']   = ['in', [1, 2, 3]];
        $params                = [
            'where'    => $where,
            'order'    => 'id desc',
            'field'    => 'id,is_enable,act_status,card_type,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to,available_count,up_down_channel_name',
            'pagesize' => input('get.pagesize'),
            'query'    => input('get.')
        ];
        $list = $model->getListPaginate($params);
        $card_type_arr = $model->cardType();

        foreach ($list as $key => $value) {
            $list[$key]['id'] = (string)$value['id'];
            $list[$key]['card_type_name'] = $card_type_arr[$value['card_type']] ?? '';
            switch ($value['date_type']) {
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'] . '至' . $value['validity_date_end'];
                    break;
                case 2:
                    if ($value['fixed_begin_term'] == 0) {
                        $list[$key]['validity_date'] = "自领取当天有效，有效期" . $value['fixed_term'] . '天';
                    } elseif ($value['fixed_begin_term'] == 1) {
                        $list[$key]['validity_date'] = "领取后" . $value['fixed_term'] . '天后有效';
                    }
                    break;
                default:
                    break;
            }
            $value['status_name'] = empty($value['is_enable']) ? '已关闭' : $model->cardStatus()[$value['act_status']];
        }
        print_json(0, '', $list);
    }
}
