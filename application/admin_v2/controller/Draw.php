<?php
/**
 * 抽奖活动
 * User: lilanjun
 * Date: 2022/10/13
 * Time: 上午11:00
 */

namespace app\admin_v2\controller;

use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlr;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommoditySet;
use app\common\service\BaseDataService;
use app\common\model;
use app\common\service\CommodityService;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSystemValue;
use think\Hook;

class Draw extends Common
{

    private $comm_type_model;
    private $dbCommodity;
    private $dbDraw;
    private $dbDrawCommodity;
    private $dbDlr;
    private $comm_service;
    private $gather_list;


    public function __construct()
    {
        parent::__construct();
        $this->comm_type_model = new model\db\DbCommodityType();
        $this->dbCommodity     = new model\db\DbCommodity();
        $this->dbDraw          = new model\db\DbDraw();
        $this->dbDrawCommodity = new model\db\DbDrawCommodity();
        $this->dbDlr           = new model\db\DbDlr();

        $this->comm_service           = new CommodityService();
        $this->_dbCommodityDlrTypeObj = new DbCommodityDlrType();
        $this->_dbCommodityFlatObj    = new DbCommodityFlat();
        $this->assign('admin_type', $this->admin_info['type']);
        $model             = new DbSystemValue();
        $this->gather_list = $model->where(['value_type' => 26, 'is_enable' => 1])->field('sys_value_id as id,county_name as name')->select();
        $this->assign('gather_list', $this->gather_list);

    }

    public function live()
    {
        $status        = input('get.status');  //1未开始 2 进行中 3已结束
        $title         = input('get.title');
        $date          = input('get.date');
        $gather_id     = input('get.gather_id', 0);
        $is_pv_subsidy = input('get.is_pv_subsidy');
        $theme_name    = input('get.theme_name');

        $where               = [];
        $set_type            = input('set_type', 5);
        $where['a.set_type'] = $set_type;

        if (!empty($date)) {
            $date = explode(' ~ ', $date);
            if (count($date) == 2) {
                $where['a.start_time'] = ['between', [date('Y-m-d H:i:s', strtotime($date[0])), date('Y-m-d H:i:s', strtotime($date[1]) + 86400)]];
            }
        }

        if (!empty($title)) {
            $where['a.title'] = ['like', "%{$title}%"];
        }

        if (in_array($status, [1, 2, 3])) {
            $where['a.act_status'] = $status;
            $where['a.is_enable']  = 1;
        } else if ($status == 4) {
            $where['a.is_enable'] = 0;
        }

        if(!empty($gather_id)) {
            $where['a.gather_id'] = $gather_id;
        }
        if(!empty($is_pv_subsidy)) {
            if($is_pv_subsidy != 2){
                $where['a.is_pv_subsidy'] = $is_pv_subsidy;
            }else{
                $where['a.is_pv_subsidy'] = 0;
            }
        }
        if(!empty($theme_name)) {
            $where['a.theme_name'] = array('like','%'.$theme_name.'%');
        }

        $field               = 'a.*';
        $where['a.set_type'] = $set_type;
        $params              = [
            'where' => $where,
            'query' => input('get.'),
            'order' => 'a.id DESC',
            'field' => $field,
        ];

        $gather_array = [];
        foreach ($this->gather_list as $value){
            $gather_array[$value['id']] = $value['name'];
        }

        $list = $this->dbDraw->getDrawList($set_type, $params);
        foreach ($list as $key => $val) {
            $in = empty($val['is_enable']) || $status == 4;
            if (strtotime($val['start_time']) > time()) {
                $list[$key]['status'] = $in ? '已关闭' : '未开始';
            } else if (strtotime($val['start_time']) <= time() && strtotime($val['end_time']) >= time()) {
                $list[$key]['status'] = $in ? '已关闭' : '进行中';
            } else if (strtotime($val['end_time']) < time()) {
                $list[$key]['status'] = $in ? '已关闭' : '已结束';
            }

            if($val['gather_id'] == 0 || empty($val['gather_id'])){
                $list[$key]['gather_name'] = '-';
            }else{
                if(isset($gather_array[$val['gather_id']])){
                    $list[$key]['gather_name'] = $gather_array[$val['gather_id']];
                }else{
                    $list[$key]['gather_name'] = '-';
                }
            }
            $list[$key]['is_pv_subsidy_status'] = $val['is_pv_subsidy'] == 1 ?'是': '否';

        }

        $page = $list->render();
        $this->assign('list', $list);
        $this->assign('page', $page);
        $this->assign('status', $status);
        $this->assign('set_type', $set_type);
        $this->assign('live_type', input('live_type'));
        return $this->fetch('live');
    }

    public function addLive()
    {
        //角色类型
        $adminType = $this->admin_info['type'];
        $setType   = input('set_type', 5);

        /**
         * 商品类型列表 积分商城和官微相同
         */
        $type            = DbCommodityDlrType::getType($setType);
        $commDlrTypeList = (new DbCommodityDlrType())->getList(
            [
                'field' => 'id,inner_name',
                'where' => ['is_enable' => 1, 'type' => $type],
            ]
        );
        $commDlrTypeData = array_column($commDlrTypeList, 'inner_name', 'id');

        $commodity_class = DbCommodity::commodityClass();
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY9]); // 延保服务包不参加活动

        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_dlr_type_json', json_encode($commDlrTypeData));
        $this->assign('set_type', $setType);
        $this->assign('admin_type', $adminType);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', (new DbSystemValue())->getNameList(20));
        $this->assign('commodity_class', $commodity_class);
        $this->assign('commodity_class_json', json_encode($commodity_class));
        $this->assign('live_type', input('live_type'));
        return $this->fetch('add_live');
    }

    /**
     * 更新数据
     * @return mixed
     */
    public function updateLive()
    {
        $id   = input('get.id');
        $draw = $this->dbDraw->getOneByPk($id);

        $is_change = 1;
        if ($draw['act_status'] == 2 && $draw['is_enable'] == 1) {
            $is_change = 0;
        }

        $draw_comm       = (new DbCommodity())->where(['id' => ['in', $draw['commodity_ids']]])->field('id commodity_id,commodity_name,cover_image')->select();
        $draw_comm       = collection($draw_comm)->toArray();
        $row_comm        = $draw_comm;
        $type            = DbCommodityDlrType::getType($draw['set_type']);
        $commDlrTypeList = (new DbCommodityDlrType())->getList(
            [
                'field' => 'id,inner_name',
                'where' => ['is_enable' => 1, 'type' => $type],
            ]
        );

        $up_down_channel_info = (new DbSystemValue())->getNameList(20);
//        $info_str             = implode(',', array_keys($up_down_channel_info));

        $commodity_class = DbCommodity::commodityClass();
        unset($commodity_class[DbCommodity::COMMODITY_CLASS_KEY9]); // 延保服务包不参加活动

        $this->assign('id', $id);
        $this->assign('comm_parent_list', $this->comm_type_model->getCommodityByParentId(0));
        $this->assign('dlr_data', (new BaseDataService())->getDlrList());
        $this->assign('up_down_channel', $up_down_channel_info);
        $this->assign('dlr_hidden', (strpos($draw['up_down_channel_dlr'], 'GWDLR') !== false) ? '' : 'hidden');
        $this->assign('dlr_str', $this->getDlrInInfo($draw['up_down_channel_dlr']));
        $this->assign('row', $draw);
        $this->assign('row_comm', $row_comm);
        $this->assign('set_type', $draw['set_type']);
        $this->assign('commodity_class', $commodity_class);
        $this->assign('live_type', input('live_type'));
        $this->assign('comm_dlr_type_list', $commDlrTypeList);
        $this->assign('comm_id', json_encode(explode(',', $draw['commodity_ids'])));
        $this->assign('is_change', $is_change);
        return $this->fetch('update_live');
    }

    /**
     * 查看
     */
    public function view()
    {
        $id                       = input('get.id');
        $limit_discount           = $this->dbDraw->getOneByPk($id);
        $limit_discount_comm_list = $this->dbDrawCommodity->getDrawCommodity(['a.limit_discount_id' => $id]);

        $dlr_name = '';
        if ($limit_discount !== 'NISSAN') {
            $dlr      = $this->dbDlr->getOne(['where' => ['dlr_code' => $limit_discount['created_dlr_code']], 'field' => 'dlr_name']);
            $dlr_name = $dlr['dlr_name'];
        }

        $date = date('Y-m-d H:i:s');
        if ($limit_discount['is_enable'] == 1) {
            if ($limit_discount['start_time'] > $date) {
                $limit_discount['status_name'] = '未开始';
            } else if ($limit_discount['start_time'] < $date && $limit_discount['end_time'] > $date) {
                $limit_discount['status_name'] = '进行中';
            } else {
                $limit_discount['status_name'] = '已结束';
            }
        } else {
            $limit_discount['status_name'] = '已关闭';
        }
        $this->assign('admin_type', $this->admin_info['type']);
        $this->assign('row', $limit_discount);
        $this->assign('limit_discount_comm_list', $limit_discount_comm_list);
        $this->assign('dlr_name', $dlr_name);
        return $this->fetch('view');
    }

    /**
     * 保存数据
     */
    public function saveLive()
    {
        $post = input('post.');
        $title = input('post.title') or print_json(1, '活动名称不能为空');
        $start_time = input('post.start_time') or print_json(1, '开始时间不能为空');
        $end_time = input('post.end_time') or print_json(1, '结束时间不能为空');
        $commodity_ids = input('post.goods') or print_json(1, '活动商品不能为空');

        $up_down_channel_arr = $post['up_down_channel'] ?? [];
        $up_down_channel_dlr = getUpDownChannel(
            implode(',', $up_down_channel_arr),
            input('post.dlr_code', '')
        );

        $is_enable = input('post.is_enable', '1');
        $id        = input('post.id', 0);
        $set_type  = input('post.set_type');
        if ($start_time > $end_time) print_json(1, '结束时间不能小于开始时间');
        $gather_id     = input('gather_id', 0);
        $is_pv_subsidy = input('is_pv_subsidy', 0);
        $theme_name    = input('theme_name');

        $act_status = (new model\db\DbLimitDiscount())->getActStatus($start_time, $end_time);
        $data       = [
            'title'                => $title,
            'start_time'           => $start_time,
            'end_time'             => $end_time,
            'act_status'           => $act_status,
            'commodity_ids'        => $commodity_ids,
            'draw_number'          => input('post.draw_number'),
            'draw_url'             => input('post.draw_url'),
            'game_id'              => input('post.game_id'),
            'is_enable'            => $is_enable,
            'creator'              => $this->admin_info['username'],
            'set_type'             => $set_type,
            'up_down_channel_name' => implode(',', $_POST['up_down_channel_name'] ?? []),
            'up_down_channel_dlr'  => $up_down_channel_dlr,
            'get_draw_set'         => input('post.get_draw_set'),
            'get_draw_type'        => input('post.get_draw_type'),
            'gather_id'            => $gather_id,
            'is_pv_subsidy'        => $is_pv_subsidy,
            'theme_name'           => $theme_name,
        ];

        if ($act_status == 2 && $id > 0) {
            unset($data['up_down_channel_name']);
            unset($data['up_down_channel_dlr']);
        }


        if ($is_enable == 1) {
            $date = date('Y-m-d H:i:s');
            //是否参加其它抽奖活动
            $field                   = "b.commodity_dlr_type_id,a.commodity_class,comm_type_id,a.up_down_channel_name,a.commodity_id,a.commodity_set_id,commodity_name,cover_image,price,a.count_stock";
            $where['b.commodity_id'] = ['in', $commodity_ids];
            $where['b.shelves_type'] = $set_type;
            $params                  = array(
                'qurey'    => input('get.'),
                'where'    => $where,
                'pagesize' => input('pagesize'),
                'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
                'field'    => $field,
            );

            $flat           = new DbCommodityFlat();
            $commodity_list = $flat->getCommodityList($params);
            $where_draw     = [
                'a.is_enable'    => ['=', 1],
                'a.start_time'   => ['<', $end_time],
                'a.end_time'     => ['>', $start_time],
                'a.set_type'     => $set_type,
                'c.shelves_type' => $set_type,
                'a.act_status'   => ['in', [1, 2]],
            ];
            if (!empty($id)) $where_draw['a.id'] = ['<>', $id];
            $list = $this->dbDraw->getIsDrawProduct(
                $commodity_list, $where_draw
            );
            foreach ($list as $v) {
                if ($v['is_draw'] == 1) print_json(1, $v['commodity_name'] . '已参加其它抽奖活动');
            }
        }

        //开启事物
        $this->dbDraw->startTrans();
        if (empty($id)) {  //插入
            $last_id = $this->dbDraw->insertGetId($data);
            if (!$last_id) {
                print_json(1, '保存失败');
            }
        } else {    //修改
            if (empty($data['get_draw_type'])) unset($data['get_draw_type']);
            $row = $this->dbDraw->getOneByPk($id);
            if ($row) {
                $data['modifier']          = $this->admin_info['username'];
                $data['last_updated_date'] = date('Y-m-d H:i:s');
                $res                       = $this->dbDraw->isUpdate(true)->saveData($data, ['id' => $id]);
                if (!$res) {
                    print_json(1, '保存失败');
                }
            }
        }
        //提交
        $this->dbDraw->commit();

        print_json(0, '保存成功');
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetLiveCommodityList()
    {
        $commodity_name  = input('commodity_name');
        $top_type        = input('comm_parent_id');
        $second_type     = input('sub_comm_type_id');
        $third_type      = input('three_comm_type_id');
        $active_id       = input('active_id');
        $commodity_class = input('commodity_class');
        $start_time      = input('start_time');
        $end_time        = input('end_time');
        $live_type       = input('live_type');
        $set_type        = input('set_type');

        $where = [
            'b.shelves_type'      => $set_type,
            'c.dd_commodity_type' => ['neq', 12],
        ];

        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        $type_id = 0;

        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }

        if (!empty($commodity_class)) {
            $where['a.commodity_class'] = $commodity_class;
        } else {
            $where['a.commodity_class'] = ['neq', DbCommodity::COMMODITY_CLASS_KEY9]; // 延保服务包不能参加
        }


        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }

        // $where['b.shelves_type'] = 5;//车生活id
        $field  = "b.commodity_dlr_type_id,a.commodity_class,a.comm_type_id,a.up_down_channel_name,a.commodity_id,
        a.commodity_set_id,a.commodity_name,a.cover_image,a.price,a.count_stock,c.dd_commodity_type";
        $params = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );

        $flat = new DbCommodityFlat();
        $list = $flat->getCommodityList($params);

        $where_draw = [
            'a.id'           => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'    => ['=', 1],
            'a.set_type'     => $set_type,
            'c.shelves_type' => $set_type,
            'a.act_status'   => ['in', [1, 2]],
        ];
        if (!empty($end_time)) $where_draw['a.start_time'] = ['<', $end_time];
        if (!empty($start_time)) $where_draw['a.end_time'] = ['>', $start_time];
        //是否参与了其它抽奖活动
        $list = $this->dbDraw->getIsDrawProduct(
            $list, $where_draw
        );

        $res         = [];
        $res['list'] = $list;
        print_json(0, '', $res);
    }

    /**
     * 获取商品列表
     * @return mixed
     */
    public function ajaxGetCommodityList()
    {
        $commodity_name     = input('commodity_name');
        $comm_parent_id     = input('comm_parent_id');
        $sub_comm_type_id   = input('get.sub_comm_type_id');
        $three_comm_type_id = input('get.three_comm_type_id');
        $params['where']    = ['a.commodity_class' => 1];
        $params['query']    = [];
        $set_type           = input('get.set_type');
        if (!empty($commodity_name)) {
            $params['where']['a.commodity_name'] = ['like', "%$commodity_name%"];
        }


        if (!empty($three_comm_type_id)) {
            $params['where']['a.comm_type_id'] = $three_comm_type_id;
        } else {
            if (!empty($sub_comm_type_id)) {
                $comm_type_column = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $sub_comm_type_id], 'column' => 'id']);

                $params['where']['a.comm_type_id'] = ['in', $comm_type_column];
            } else {
                if (!empty($comm_parent_id)) {
                    $two_type_column                   = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id], 'column' => 'id']);
                    $three_type_column                 = $this->comm_type_model->getColumn(['where' => ['is_enable' => 1, 'comm_parent_id' => ['in', $two_type_column]], 'column' => 'id']);
                    $params['where']['a.comm_type_id'] = ['in', $three_type_column];
                }
            }
        }

//        $params['field']='a.id,a.commodity_name,a.cover_image,a.comm_type_id,a.set_type,a.create_dlr_code, b.comm_type_name,b.comm_parent_id,e.id as commodity_set_id,e.dlr_code,e.original_price_range_end,e.count_stock';
        $params['order']        = 'a.id desc';
        $params['query']        = input('get.');
        $params['pagesize']     = input('pagesize');
        $params['shelves_type'] = $set_type;
        $dlr_code               = $this->admin_info['dlr_code'];
        $set_type               = $this->admin_info['type'];
        $list                   = $this->dbCommodity->getShelvesListByDlr($set_type, $dlr_code, $params);

        $commodity_set_id_list = [];
        foreach ($list as $key => $val) {
            $commodity_set_id_list[] = $val['commodity_set_id'];
            $list[$key]['is_limit']  = 0;
            if (!empty($val['highest_price'])) $list[$key]['original_price_range_end'] = $val['highest_price'];
        }
        //处理是否已经折扣
        $comm_dl_model = new DbCommodityDlr();
        $dlr_list      = $comm_dl_model->getDlrBySetId($commodity_set_id_list);
        $is_limit_id   = [];
        foreach ($list as $key => $val) {
            if (in_array($val['commodity_set_id'], $is_limit_id)) $list[$key]['is_limit'] = 1;
        }

        $comm_parent_list2        = $this->comm_type_model->getCommTypeName();
        $res                      = [];
        $res['list']              = $list;
        $res['comm_parent_list2'] = $comm_parent_list2;
        print_json(0, '', $res);
    }

    public function ajaxGetCommTypeId()
    {
        $comm_parent_id = input('get.comm_parent_id');
        $list           = $this->comm_type_model->getList(['where' => ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id]]);
        print_json(0, '', $list);

    }

    public function getSkuList()
    {
        $limit_commodity_id = input('get.limit_commodity_id');
        $commodity_set_id   = input('get.commodity_set_id');
        $row                = $this->dbDraw->getGroupInfo(['where' => ['b.id' => $limit_commodity_id], 'field' => 'b.sku_price,b.commodity_id,a.created_dlr_code']);
        if (!$row) print_json(1, '', []);
        $sku_price    = json_decode($row['sku_price'], true);
        $commodity_id = input('commodity_id');
        // $list=$this->sku_model->getList(['where'=>['commodity_id'=>$commodity_id,'is_enable'=>1]]);
        $res = $this->comm_service->getSetSku($commodity_set_id);
        foreach ($res['sku_list'] as $key => $val) {
            if (empty($sku_price[$val['id']])) {
                unset($res['sku_list'][$key]);
            } else {
                $res['sku_list'][$key]['fight_price'] = $sku_price[$val['id']];
            }
        }
        $this->assign('sku_list', $res['sku_list']);
        $this->assign('sp_title', $res['sp_title']);
        $this->assign('sp_list', $res['sp_list']);
        return $this->fetch('get_index_sku');
    }

    public function getSkuLive()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $comm_service     = new CommodityService();
        $sku              = $comm_service->getSetSku($commodity_set_id);
        $sku_list         = $sku['sku_list'];
        $commodity_row    = $this->_dbCommodityFlatObj->getOne(
            [
                'where'        => ['commodity_id' => $commodity_id, 'commodity_set_id' => $commodity_set_id],
                'field'        => 'commodity_name,cover_image,comm_type_id',
                'shelves_type' => 5
            ]
        );

        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (empty($val['sp_value_arr'])) {
                continue;
            }
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    public function getSku()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        $comm_set_model   = new DbCommoditySet();
        $com_set_row      = $comm_set_model->getOne(['where' => ['id' => $commodity_set_id], 'field' => 'commodity_id,dlr_code,set_type']);
        // var_dump($commodity_set_id);
        $sku           = $this->comm_service->getSetSku($commodity_set_id);
        $sku_list      = $sku['sku_list'];
        $commodity_row = $this->dbCommodity->getOne(['where' => ['id' => $commodity_id], 'field' => 'commodity_name,cover_image']);
        // var_dump($sku);
        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (!empty($val['sp_value_arr'])) {
                foreach ($val['sp_value_arr'] as $key1 => $val1) {
                    $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
                }
            }
        }

        //var_dump($sku_list);exit;
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }

    /**
     * 通过上架id 获取可以选专营店
     */
    public function ajaxGetDlr()
    {
        $commodity_set_id = input('get.commodity_set_id') or print_json(1, '', []);

        $baseService = new BaseDataService();
        $dlr_data    = $baseService->getDlrList($commodity_set_id);
        $this->assign('dlr_data', $dlr_data);
        print_json(0, '', $dlr_data);
    }


}
