<?php


namespace app\admin_v2\controller;


use app\common\model\db\DbCard;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbCrowdfund;
use app\common\model\db\DbCrowdfundCommodity;
use app\common\model\db\DbCrowdfundOrder;
use app\common\model\db\DbExports;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbFullDiscount;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbPreSale;
use app\common\model\db\DbSeckill;
use app\common\model\sys\SysMenu;
use app\common\service\CommodityService;
use app\common\validate\CrowdfundValidate;
use think\Env;
use think\Exception;
use think\Hook;
use think\Queue;
use think\Validate;
use tool\Logger;

class Crowdfund extends Common
{
    public function __construct()
    {
        parent::__construct();
    }

    // 菜单
    public function crowd_list()
    {
        $m_model = new SysMenu();
        $list    = $m_model->alias('a')
            ->join('t_sys_menu b', 'a.menu_pid = b.id')
            ->where(['a.controller' => request()->controller(), 'a.left_view' => 1, 'b.left_view' => 1])
            ->field('a.web_menu_url,b.menu_url')
            ->find();
        if (!empty($list['web_menu_url']) && $list['menu_url'] != "/") {
            $username['username']  = $this->admin_info['username'];
            $username['timestamp'] = time();
            $token                 = http_build_query($username);
            $this->redirect(Env::get('TOB_URL') . $list['menu_url'] . '/' . $list['web_menu_url'] . '?sign=' . base64_encode($token));
        }
    }


    /**
     * 列表接口
     * @throws \think\exception\DbException
     */
    public function index()
    {
        $search = input('get.');
        $map    = ['brand' => $search['brand'] ?? 1, 'is_enable' => 1];
        if (!empty($search['title'])) {
            $map['title'] = ['like', '%' . $search['title'] . '%'];
        }
        if (!empty($search['act_status'])) {
            $map['act_status'] = $search['act_status'];
        }
        if (!empty($search['time'])) {
            list($start_time, $end_time) = explode(' ~ ', $search['time']);
            $map['start_time'] = ['<=', $start_time];
            $map['end_time']   = ['>=', $end_time];
        }
        $pagesize = $search['pagesize'] ?? 10;
        $model    = new DbCrowdfund();
        $field    = 'id,title,start_time,end_time,gather_id,theme_name,is_pv_subsidy,up_down_channel,act_status';
        $list     = $model->where($map)->field($field)->order('id desc')->paginate($pagesize, false, ['query' => $search]);
        foreach ($list as $key => $item) {
            $item['gather_text']     = $item->gather_text;
            $item['act_status_text'] = DbCrowdfund::$act_status[$item['act_status']];
        }
        print_json(0, 'success', $list);
    }


    /**
     * 新增众筹
     * @param CrowdfundValidate $validate
     */
    public function save(CrowdfundValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            print_json(1, $validate->getError());
        }
        $requestData['start_time'] = date('Y-m-d H:i:00', strtotime($requestData['start_time']));
        $requestData['end_time']   = date('Y-m-d H:i:59', strtotime($requestData['end_time']));
        $commodity                 = $requestData['commodity'];
        foreach ($commodity as $item) {
            if (empty($item['commodity_id']) || empty($item['commodity_set_id']) || empty($item['commodity_name'])) {
                print_json(1, '商品参数不能为空');
            }
        }
        unset($requestData['commodity']);
        $crowdfundId = $requestData['id'] ?? 0;
        // 时间判断
        if (empty($crowdfundId)) {
            if (time() > strtotime($requestData['start_time'])) {
                print_json(1, '不能选择已过期时间');
            }

        }
        if (strtotime($requestData['start_time']) >= strtotime($requestData['end_time'])) {
            print_json(1, '结束时间应大于开始时间');
        }
        $days = date_diff(date_create($requestData['start_time']), date_create($requestData['end_time']))->days;
        if ($days > 100) {
            print_json(1, '时间范围不能超过100天');
        }

        // 添加众筹主表
        $crowdfund_model           = new DbCrowdfund();
        $crowdfund_commodity_model = new DbCrowdfundCommodity();

        $act_status                = $crowdfund_model->getActStatus($requestData['start_time'], $requestData['end_time']);
        $requestData['act_status'] = $act_status;
        $requestData['creator']    = $this->admin_info['username'] ?? '';


        $this->judgeJoinRestsActivity($requestData['start_time'], $requestData['end_time'], $commodity, $requestData['brand'], $crowdfundId);


        try {
            $crowdfund_model->startTrans();
            if (!isset($requestData['id']) && empty($requestData['id'])) {
                // 新增
                $requestData['creator'] = $this->admin_info['username'] ?? '';
                $crowdfundId            = $crowdfund_model->insertGetId($requestData);
            } else {
                // 修改
                $requestData['modifier']      = $this->admin_info['username'] ?? '';
                $requestData['modified_date'] = date('Y-m-d H:i:s');
                $crowdfund_model->where('id', $crowdfundId)->update($requestData);
            }

            $res = $this->insertCommoditySkuDataLive($commodity, $crowdfundId);
            if (!$res) {
                print_json(1, '活动商品保存失败');
            }
            $crowdfund_model->commit();
            // 未开始 进行中
            if (in_array($act_status, [1, 2])) {
                $crowdfund_com = $crowdfund_commodity_model->getCrowdfundCommodity(['a.crowdfund_id' => $crowdfundId]);
                $this->doHook('add', $crowdfund_com, $crowdfundId, $requestData['brand']);
            }
            print_json(0, '保存成功');

        } catch (Exception $e) {
            $crowdfund_model->rollback();
            $msg = $e->getMessage();
            Logger::error('save crowdfund:' . $msg);
            print_json(1, $msg);
        }


    }


    /**
     * 判断是否参加其他活动
     * @param $start_time
     * @param $end_time
     * @param $commodity
     * @param $brand
     * @param $active_id
     */
    private function judgeJoinRestsActivity($start_time, $end_time, $commodity, $brand, $active_id)
    {
        $shelves_type = DbCrowdfund::$shelves_type[$brand];

        $date       = date('Y-m-d H:i:s');
        $where_time = ['start_time' => $start_time, 'end_time' => $end_time];
        //是否有参加预售
        $list = (new DbPreSale())->getIsPreProduct(
            $commodity, [
                'a.is_enable'      => 1,
                'a.front_s_time'   => ['<', $date],
                'a.balance_e_time' => ['>', $date],
                'a.set_type'       => $shelves_type,
            ]
        );
        foreach ($list as $v) {
            if ($v['is_pre'] == 1) print_json(1, $v['commodity_name'] . '已参加预售活动');
        }

        //判断是否已经参加了满优惠
        $map  = [
            'a.is_enable' => 1,
            'a.set_type'  => $shelves_type,
            'act_status'  => ['in', [1, 2]]
        ];
        $list = (new DbFullDiscount())->getIsFullDiscount($list, $map, $where_time);
        foreach ($list as $v) {
            if ($v['is_full'] == 1) print_json(1, $v['commodity_name'] . '已参加其它满优惠活动');
        }

        //是否 限时折扣
        $list = (new DbLimitDiscount())->getIsLimitProduct($list, [
            'a.is_enable'  => 1,
            'a.set_type'   => $shelves_type,
            'a.act_status' => ['in', [1, 2]]
        ], $where_time);

        foreach ($list as $v) {
            if ($v['is_limit'] == 1) print_json(1, $v['commodity_name'] . '已参加其它限时折扣活动');
        }

        //判断是否已经参加了拼团
        $list = (new DbFightGroup())->getIsFightGroupProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $shelves_type,
            'a.act_status' => ['in', [1, 2]]
        ], $where_time);

        foreach ($list as $v) {
            if ($v['is_fight'] == 1) print_json(1, $v['commodity_name'] . '已参加其它拼团活动');
        }

        //是否NN
        $map  = [
            'a.is_enable'  => 1,
            'a.set_type'   => $shelves_type,
            'a.act_status' => ['in', [1, 2]]
        ];
        $list = (new DbNDiscount())->getIsNDiscount($commodity, $map, $where_time);
        foreach ($list as $v) {
            if ($v['is_n_discount'] == 1) print_json(1, $v['commodity_name'] . '已参加其它N件N折活动');
        }
        // 是否是秒杀
        $map = [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $shelves_type,
            'a.act_status' => ['in', [1, 2]],
        ];

        $list = (new DbSeckill())->getIsSeckillProduct($commodity, $map, $where_time);
        foreach ($list as $v) {
            if ($v['is_seckill'] == 1) print_json(1, $v['commodity_name'] . '已参加其它秒杀活动');
        }


        //是否是众筹
        $map  = [
            'a.id'         => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'  => ['=', 1],
            'a.brand'      => $brand,
            'a.act_status' => ['in', [1, 2]],
        ];
        $list = (new DbCrowdfund())->getIsCrowdfund($list, $map);
        foreach ($list as $v) {
            if ($v['is_crowdfund'] == 1) print_json(1, $v['commodity_name'] . '已参加其它众筹活动');
        }

    }


    /**
     * 插入活动商品表
     * @param $sku_list
     * @param $crowdfundId
     * @return DbCrowdfundCommodity|false|int|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function insertCommoditySkuDataLive($sku_list, $crowdfundId)
    {
        if (empty($sku_list)) {
            return false;
        }
        $crowdfund_commodity_model = new DbCrowdfundCommodity();
        $crowdfund_commodity_model->where('crowdfund_id', $crowdfundId)->update(['is_enable' => 0]);
        $re = 0;
        foreach ($sku_list as $key => $item) {
            $data = [
                'crowdfund_id'     => $crowdfundId,
                'commodity_id'     => $item['commodity_id'],
                'commodity_set_id' => $item['commodity_set_id'],
                'creator'          => $this->admin_info['username'] ?? '',
            ];
            $map  = ['crowdfund_id' => $crowdfundId, 'commodity_id' => $item['commodity_id']];
            $info = $crowdfund_commodity_model->where($map)->find();
            if (empty($info)) {
                $re = $crowdfund_commodity_model->insert($data);
            } else {
                $re = $crowdfund_commodity_model->where('id', $info['id'])->update(['is_enable' => 1]);
            }
        }
        return $re;

    }


    /**
     * 获取商品分类
     */
    public function getCommTypeById()
    {
        $comm_type_model = new DbCommodityType();
        $comm_parent_id  = input('get.comm_parent_id', 0);
        $map             = ['is_enable' => 1, 'comm_parent_id' => $comm_parent_id];
        $field           = 'id,comm_type_name';
        $params          = ['where' => $map, 'field' => $field, 'order' => 'sort'];
        $list            = $comm_type_model->getList($params);
        print_json(0, '', $list);
    }


    /**
     * 获取商品列表
     * @throws \think\exception\DbException
     */
    public function getLiveCommodityList()
    {
        $commodity_name = input('commodity_name');
        $top_type       = input('comm_parent_id');
        $second_type    = input('sub_comm_type_id');
        $third_type     = input('three_comm_type_id');
        $active_id      = input('active_id');
        $brand          = input('brand', 1);
        $start_time     = input('start_time');
        $end_time       = input('end_time');

        $where = ['b.listing_type' => 2, 'c.dd_commodity_type' => ['neq', 12]];
        if ($brand == 1) {
            $where['b.shelves_type'] = 5;
            $where['b.qsc_group']    = '';  // 取送车服务包不能参加
            $where[]                 = [['exp', 'FIND_IN_SET("GWSM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("GWAPP",a.up_down_channel_dlr)'], 'or'];
        } elseif ($brand == 2) {
            $where['b.shelves_type'] = 7;
            $where[]                 = [['exp', 'FIND_IN_SET("QCSM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",a.up_down_channel_dlr)'], 'or'];
        } else {
            $where['b.shelves_type'] = 6;
            $where[]                 = [['exp', 'FIND_IN_SET("PZ1ASM",a.up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",a.up_down_channel_dlr)'], 'or'];
        }

        if (!empty($commodity_name)) {
            $where['a.commodity_name'] = ['like', "%$commodity_name%"];
        }

        $type_id = 0;
        if (!empty($third_type)) {
            $type_id = $third_type;
        } else if (!empty($second_type)) {
            $type_id = $second_type;
        } else if (!empty($top_type)) {
            $type_id = $top_type;
        }
        if (!empty($type_id)) {
            $where[] = ['exp', " (find_in_set({$type_id},comm_type_id_str) ) "];
        }


        $field  = "b.commodity_dlr_type_id,a.commodity_class,a.comm_type_id,a.up_down_channel_name,a.up_down_channel_dlr,
        a.commodity_id,a.commodity_set_id,a.commodity_name,a.cover_image,a.price,a.count_stock,c.dd_commodity_type";
        $params = array(
            'qurey'    => input('get.'),
            'where'    => $where,
            'pagesize' => input('pagesize'),
            'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
            'field'    => $field,
        );

        $flat = new DbCommodityFlat();
        $res  = [];
        $list = $flat->getCommodityList($params);

        //如果是快递到家#
        $dbCommodityDlrTypeObj = new DbCommodityDlrType();
        $homeArr               = $dbCommodityDlrTypeObj->getExpressHomeType();
        foreach ($list as $key => $val) {
            $list[$key]['home'] = 0;
            if (isset($val['commodity_dlr_type_id']) && in_array($val['commodity_dlr_type_id'], $homeArr)) {
                $list[$key]['home'] = 1;
            }
        }


        $date = date('Y-m-d H:i:s');
        if (empty($start_time)) {
            $start_time = $date;
        }
        if (empty($end_time)) {
            $end_time = $date;
        }

        if (!empty($start_time)) $where_start_time = ['<', $end_time];
        if (!empty($end_time)) $where_end_time = ['>', $start_time];

        //是否有参加预售
        $list = (new DbPreSale())->getIsPreProduct($list, [
            'a.is_enable'      => ['=', 1],
            'a.front_s_time'   => $where_start_time ?? ['<', $date],
            'a.balance_e_time' => $where_end_time ?? ['>', $date],
            'a.set_type'       => $where['b.shelves_type'],
        ]);

        //判断是否已经参加了满优惠
        $map  = [
            'a.is_enable' => 1,
            'a.set_type'  => $where['b.shelves_type'],
            'act_status'  => ['in', [1, 2]]
        ];
        $list = (new DbFullDiscount())->getIsFullDiscount($list, $map);


        //是否 限时折扣
        $list = (new DbLimitDiscount())->getIsLimitProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);

        //判断是否已经参加了拼团
        $list = (new DbFightGroup())->getIsFightGroupProduct($list, [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]]
        ]);

        //是否NN
        $map  = [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]],
        ];
        $list = (new DbNDiscount())->getIsNDiscount($list, $map);


        //是否秒杀
        $map  = [
            'a.is_enable'  => ['=', 1],
            'a.set_type'   => $where['b.shelves_type'],
            'a.act_status' => ['in', [1, 2]],
        ];
        $list = (new DbSeckill())->getIsSeckillProduct($list, $map);


        //是否是众筹
        $map  = [
            'a.id'         => ['<>', empty($active_id) ? 0 : $active_id],
            'a.is_enable'  => ['=', 1],
            'a.brand'      => $brand,
            'a.act_status' => ['in', [1, 2]],
        ];
        $list = (new DbCrowdfund())->getIsCrowdfund($list, $map);

        $res['list'] = $list;
        print_json(0, '', $res);
    }


    /**
     * 获取商品sku
     */
    public function getSkuLive()
    {
        $commodity_id     = input('get.commodity_id');
        $commodity_set_id = input('get.commodity_set_id');
        if (empty($commodity_id) || empty($commodity_set_id)) {
            print_json(1, '缺少参数');
        }
        $comm_service   = new CommodityService();
        $commodity_flat = new DbCommodityFlat();
        $sku            = $comm_service->getSetSku($commodity_set_id);
        $sku_list       = $sku['sku_list'];
        $commodity_row  = $commodity_flat->getOne([
            'where'        => ['commodity_id' => $commodity_id, 'commodity_set_id' => $commodity_set_id],
            'field'        => 'commodity_name,cover_image,comm_type_id',
            'shelves_type' => 5
        ]);

        foreach ($sku_list as $key => $val) {
            $sku_list[$key]['sku_val'] = 'SKU:' . $val['id'];
            if (empty($val['sp_value_arr'])) {
                continue;
            }
            foreach ($val['sp_value_arr'] as $key1 => $val1) {
                $sku_list[$key]['sku_val'] .= '/' . $sku['sp_list'][$val1]['sp_name'] . ':' . $sku['sp_list'][$val1]['sp_value_name'] . ';';
            }
        }
        print_json(0, '', ['sku_list' => $sku_list, 'commodity_row' => $commodity_row]);
    }


    /**
     * @param $type
     * @param $commodity_arr
     * @param $act_id
     * @param $brand
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function doHook($type, $commodity_arr, $act_id, $brand)
    {
        Hook::import(require ROOT_PATH . 'config/tags.php');
        $result = true;

        //查询act_id对应的活动，查询up_down_channel_dlr ，做出判断 shelves_type
        $model    = new DbCrowdfund();
        $activity = $model->where('id', $act_id)->find();
        if (!empty($activity['up_down_channel'])) {
            switch ($type) {
                case 'add':
                    # 添加商品不需要刷活动信息
                    foreach ($commodity_arr as $item_one) {
                        $item_one['brand'] = $brand;
                        Hook::listen('flat_crowdfund', $item_one);
                    }
                    break;
                case 'delete':
                    # 删除就只需要将数据删除就ok
                    foreach ($commodity_arr as $item_one) {
                        $del_params = [
                            'brand'        => $brand,
                            'del_dis'      => true,
                            'act_id'       => $act_id,
                            'commodity_id' => empty($item_one['commodity_id']) ? $item_one : $item_one['commodity_id']
                        ];
                        Hook::listen('flat_crowdfund', $del_params);
                    }
                    break;
            }

            $detail_param = ['key' => 'cache_prefix.commodity_detail', 'suffix' => '', 'set' => 'cache_prefix.commodity_detail_set'];
            Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $detail_param);

            return $result;
        }

        return false;
    }


    /**
     * 获取渠道编码
     */
    public function getUpDownChannel()
    {
        $brand = input('brand');
        if (empty($brand) || !in_array($brand, [1, 2, 3])) {
            print_json(1, '参数错误');
        }
        $list = DbCrowdfund::$up_down_channel[$brand];
        print_json(0, 'success', $list);
    }


    /**
     * 删除
     */
    public function delete()
    {
        $id = input('id');
        if (empty($id)) {
            print_json(1, '缺少参数');
        }
        $crowdfund_model = new DbCrowdfund();
        $act_status      = $crowdfund_model->where('id', $id)->value('act_status');
        if ($act_status != 1) {
            print_json(1, '只有未开始的活动才可以删除');
        }
        $upd                       = ['is_enable' => 0, 'modifier' => $this->admin_info['username'], 'modified_date' => date('Y-m-d H:i:s')];
        $re1                       = $crowdfund_model->where('id', $id)->update($upd);
        $crowdfund_commodity_model = new DbCrowdfundCommodity();
        $re2                       = $crowdfund_commodity_model->where('crowdfund_id', $id)->update($upd);
        if ($re1 && $re2) {
            print_json(0, '删除成功');
        } else {
            print_json(1, '删除失败');
        }
    }


    /**
     * 进度列表
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function planList()
    {
        $crowdfund_id = input('crowdfund_id');
        if (empty($crowdfund_id)) {
            print_json(1, '缺少参数');
        }
        $crowdfund_model           = new DbCrowdfund();
        $crowdfund_commodity_model = new DbCrowdfundCommodity();
        $commodity_set_sku_model   = new DbCommoditySetSku();
        $crowdfund_order_model     = new DbCrowdfundOrder();

        $map       = ['id' => $crowdfund_id, 'is_enable' => 1];
        $field     = 'id,title,start_time,end_time,target,target_val,un_stand_standard';
        $crowdfund = $crowdfund_model->where($map)->field($field)->find();
        if (empty($crowdfund)) {
            print_json(1, '活动不存在');
        }
        $crowdfund['start_time'] = date('Y-m-d H:i', strtotime($crowdfund['start_time']));
        $crowdfund['end_time']   = date('Y-m-d H:i', strtotime($crowdfund['end_time']));
        $map                     = ['crowdfund_id' => $crowdfund_id, 'is_enable' => 1];
        $field                   = 'id,commodity_id,plan_status,meddle_price,meddle_count,meddle_order,meddle_people,commodity_set_id';
        $list                    = $crowdfund_commodity_model->where($map)->field($field)->select();
        foreach ($list as $key => $item) {
            $commodity = $item->commodity;
            unset($item['commodity']);
            $item['commodity_name'] = $commodity['commodity_name'];
            $map                    = ['commodity_set_id' => $item['commodity_set_id'], 'is_enable' => 1];
            $item['stock']          = $commodity_set_sku_model->where($map)->sum('stock');;
            $item['plan_status_text'] = DbCrowdfundCommodity::$plan_status[$item['plan_status']];
            $item['pay']              = $crowdfund_order_model->getPayOrder($crowdfund_id, $item['commodity_id'], 1);
            $item['already_refund']   = $crowdfund_order_model->getPayOrder($crowdfund_id, $item['commodity_id'], 2);
            $item['wait_refund']      = $crowdfund_order_model->getPayOrder($crowdfund_id, $item['commodity_id'], 3);
            $item['meddle_data']      = [
                'meddle_price'  => $item['meddle_price'],
                'meddle_count'  => $item['meddle_count'],
                'meddle_order'  => $item['meddle_order'],
                'meddle_people' => $item['meddle_people']
            ];
            unset($item['meddle_price']);
            unset($item['meddle_count']);
            unset($item['meddle_order']);
            unset($item['meddle_people']);
        }
        $data = [
            'crowdfund' => $crowdfund,
            'commodity' => $list
        ];

        print_json(0, 'success', $data);
    }

    /**
     * 获取活动进度补充数
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getPlanResidueNum()
    {
        $commodity_plan_id = input('commodity_plan_id');
        if (empty($commodity_plan_id)) {
            print_json(1, '缺少参数');
        }
        $result = $this->getReplenishNum($commodity_plan_id);
        print_json(0, 'success', $result);
    }


    /**
     * 获取补充数据
     * @param $commodity_plan_id
     * @param $is_get
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function getReplenishNum($commodity_plan_id, $is_get = 0)
    {
        $crowdfund_commodity_model = new DbCrowdfundCommodity();
        $crowdfund_model           = new DbCrowdfund();
        $commodity_set_sku_model   = new DbCommoditySetSku();
        $crowdfund_order_model     = new DbCrowdfundOrder();

        $map                 = ['id' => $commodity_plan_id, 'is_enable' => 1];
        $field               = 'crowdfund_id,commodity_id,commodity_set_id';
        $crowdfund_commodity = $crowdfund_commodity_model->where($map)->field($field)->find();

        // 查询sku最低价
        $map            = ['commodity_set_id' => $crowdfund_commodity['commodity_set_id'], 'is_enable' => 1];
        $price          = $commodity_set_sku_model->where($map)->min('price');
        $map            = ['id' => $crowdfund_commodity['crowdfund_id'], 'is_enable' => 1];
        $field          = 'id,target,target_val';
        $crowdfund_info = $crowdfund_model->where($map)->field($field)->find();

        $order_map = [
            'crowdfund_id' => $crowdfund_commodity['crowdfund_id'],
            'commodity_id' => $crowdfund_commodity['commodity_id'],
            'is_enable'    => 1,
            'pay_status'   => 1
        ];
        if ($crowdfund_info['target'] == 1) {
            // 众筹金额
            $money = $crowdfund_info['target_val'];
            // 查询已筹金额
            $price1          = $crowdfund_order_model->where($order_map)->sum('price'); // 已筹金额
            $replenish_price = $money - $price1;
            $replenish_num   = ceil($replenish_price / $price);
        } else {
            // 众筹数量
            $num = $crowdfund_info['target_val'];
            // 查询已筹金额
            $num1            = $crowdfund_order_model->where($order_map)->sum('num'); // 已筹数量
            $replenish_num   = $num - $num1;
            $replenish_price = $replenish_num * $price;
        }
        if ($replenish_num <= 0) {
            $replenish_num = 0;
            $order_num     = 0;
        } else {
            $order_num = ceil($replenish_price / $replenish_num);
        }
        $result = [
            'money'         => ($replenish_price < 0) ? 0 : $replenish_price,
            'commodity_num' => $replenish_num,
            'order_num'     => $order_num,
            'people_num'    => $order_num,
        ];
        if ($is_get) {
            $result['crowdfund_id'] = $crowdfund_commodity['crowdfund_id'];
            $result['commodity_id'] = $crowdfund_commodity['commodity_id'];
        }
        return $result;

    }


    /**
     * 补充数据修改
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function meddleCrowdCommodity()
    {
        $input = input('post.');
        $rules = [
            "commodity_plan_id" => 'require|number',
            "meddle_type"       => 'require|in:1,2,3',
            'meddle_price'      => 'require',
            'meddle_count'      => 'require|number',
            'meddle_order'      => 'require|number',
            'meddle_people'     => 'require|number',
        ];

        $validate = new Validate($rules);

        if (!$validate->check($input)) {
            print_json(1, $validate->getError());
        }
        // 判断进度状态和补充类型
        $crowdfund_commodity_model = new DbCrowdfundCommodity();
        $plan_status               = $crowdfund_commodity_model->where('id', $input['commodity_plan_id'])->value('plan_status');
        // 判断进度状态
        if ($input['meddle_type'] == 2 && in_array($plan_status, [2, 4, 5])) {
            print_json(1, '项目已达标，无法修改为项目结束');
        }

        if ($input['meddle_type'] == 1 && in_array($plan_status, [6, 7])) {
            print_json(1, '项目已结束，无法修改为项目成功');
        }

        if ($input['meddle_type'] == 1) {
            // 获取补充数据
            $data = $this->getReplenishNum($input['commodity_plan_id'], 1);
            if ($input['meddle_price'] < $data['money']) {
                print_json(1, '补充金额不能低于默认值');
            }
            if ($input['meddle_count'] < $data['commodity_num']) {
                print_json(1, '补充数量不能低于默认值');
            }
            if ($input['meddle_order'] < $data['order_num']) {
                print_json(1, '补充订单数不能低于默认值');
            }
            if ($input['meddle_people'] < $data['people_num']) {
                print_json(1, '补充人数不能低于默认值');
            }
            $plan_status = 5; // 项目成功-人为
        }
        if ($input['meddle_type'] == 2) {
            $plan_status = 7; // 项目失败-人为
        }
        $upd = [
            'plan_status'   => $plan_status, // 项目进度
            'meddle_type'   => $input['meddle_type'],
            'meddle_price'  => $input['meddle_price'],
            'meddle_count'  => $input['meddle_count'],
            'meddle_order'  => $input['meddle_order'],
            'meddle_people' => $input['meddle_people'],
            'modifier'      => $this->admin_info['username'] ?? '',
            'modified_date' => date('Y-m-d H:i:s'),
        ];

        $re = $crowdfund_commodity_model->where('id', $input['commodity_plan_id'])->update($upd);
        if ($re) {
            // 修改订单为已支付代发货
            if ($input['meddle_type'] == 1) {
                // 队列
                $crowdfund_order_model = new DbCrowdfundOrder();
                $map                   = ['crowdfund_id' => $data['crowdfund_id'], 'commodity_id' => $data['commodity_id'], 'pay_status' => ['in', [1, 3]], 'is_enable' => 1];
                $orderCodes            = $crowdfund_order_model->where($map)->column('order_code');
                if (!empty($orderCodes)) {
                    foreach ($orderCodes as $orderCode) {
                        Queue::push('app\admin_v2\queue\OrderChange', json_encode(['order_code' => $orderCode]), config('queue_type.order_change'));
                    }
                }
            }
            print_json(0, '补充成功');
        } else {
            print_json(1, '补充失败');
        }
    }


    /**
     * 卡券列表接口
     */
    public function getCard()
    {
        $card_name = input('get.card_name');
        $brand     = input('brand', 1);
        if ($brand == 1) {
            $shelves_type                 = 5;
            $where['up_down_channel_dlr'] = [['notlike', '%PZ1ASM%'], ['notlike', '%PZ1AAPP%'], ['notlike', '%QCSM%'], ['notlike', '%QCAPP%'], 'and'];
        } elseif ($brand == 2) {
            $shelves_type = 7;
            $where[]      = [['exp', 'FIND_IN_SET("QCSM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("QCAPP",up_down_channel_dlr)'], 'or'];
        } else {
            $shelves_type = 6;
            $where[]      = [['exp', 'FIND_IN_SET("PZ1ASM",up_down_channel_dlr)'], ['exp', 'FIND_IN_SET("PZ1AAPP",up_down_channel_dlr)'], 'or'];
        }
        if (!empty($card_name)) {
            $where['card_name'] = ['like', '%' . $card_name . '%'];
        }

        $where['shelves_type'] = $shelves_type;
        $where['is_enable']    = 1;
        $where['type']         = 2;
        $where['act_status']   = ['in', [1, 2, 3]];

        $params = [
            'where'    => $where,
            'order'    => 'id desc',
            'field'    => 'id,is_enable,act_status,(case card_type when 1 then "代金券" when 2 then "折购券" when 3 then "兑换券" when 4 then "满减券" when 5 then "优惠券" end) as card_type_name,card_name,date_type,validity_date_start,validity_date_end,fixed_term,fixed_begin_term,(case dlr_code when "NISSAN" then "平台" else "自建" end) as belong_to,available_count,up_down_channel_name',
            'pagesize' => input('get.pagesize'),
            'query'    => input('get.')
        ];
        $model  = new DbCard();
        $list   = $model->getListPaginate($params);
        foreach ($list as $key => $value) {
            switch ($value['date_type']) {
                case 1:
                    $list[$key]['validity_date'] = $value['validity_date_start'] . '至' . $value['validity_date_end'];
                    break;
                case 2:
                    if ($value['fixed_begin_term'] == 0) {
                        $list[$key]['validity_date'] = "自领取当天有效，有效期" . $value['fixed_term'] . '天';
                    } elseif ($value['fixed_begin_term'] == 1) {
                        $list[$key]['validity_date'] = "领取后" . $value['fixed_term'] . '天后有效';
                    }
                    break;
                default:
                    break;
            }
            $value['status_name'] = empty($value['is_enable']) ? '已关闭' : $model->cardStatus()[$value['act_status']];
        }
        print_json(0, 'success', $list);
    }


    /**
     * 详情
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function info()
    {
        $crowdfundId = input('crowdfund_id');
        if (empty($crowdfundId)) {
            print_json(1, '缺少参数');
        }
        $crowdfund_model = new DbCrowdfund();
        $map             = ['id' => $crowdfundId, 'is_enable' => 1];
        $field           = 'id,title,start_time,end_time,target,target_val,alr_crowd,purchase_num,is_use_ticket,ticket_ids,brand,
        up_down_channel,un_stand_standard,refund_set,gather_id,is_pv_subsidy,theme_name,act_status';
        $info            = $crowdfund_model->where($map)->field($field)->find();
        if (empty($info)) {
            print_json(1, '数据不存在');
        }
        $ticketIdArr = [];
        $cards       = [];
        if (!empty($info->ticket_ids)) {
            $ticketIdArr = explode(',', $info->ticket_ids);
        }
        $crowdfundCommodities = $info->crowdfundCommodities;
        unset($info['crowdfundCommodities']);
        $commodity = [];
        foreach ($crowdfundCommodities as $key => $item) {
            $commodity[] = [
                'commodity_id'     => $item['commodity_id'],
                'commodity_set_id' => $item['commodity_set_id'],
                'commodity_name'   => $item->commodity->commodity_name,
                'cover_image'      => $item->commodity->cover_image,
            ];
        }
        $info['commodity'] = $commodity;
        if (!empty($ticketIdArr)) {
            // 查询卡券
            $card_model = new DbCard();
            $map        = ['id' => ['in', $ticketIdArr]];
            $field      = 'id,card_name,card_type';
            $cards      = $card_model->where($map)->field($field)->select();
        }
        $info['cards'] = $cards;
        print_json(0, 'success', $info);

    }


    public function excel()
    {
        $crowdfund_commodity_id = input('crowdfund_commodity_id');
        if (empty($crowdfund_commodity_id)) {
            print_json(1, '参数缺失');
        }
        $crowdfund_commodity_model = new DbCrowdfundCommodity();
        $map                       = ['id' => $crowdfund_commodity_id];
        $field                     = 'id,commodity_id,crowdfund_id';
        $crowdfund_commodity_info  = $crowdfund_commodity_model->where($map)->field($field)->find();
        if (empty($crowdfund_commodity_info)) {
            print_json(1, '数据不存在');
        }
        $order_where           = [
            'crowdfund_id' => $crowdfund_commodity_info['crowdfund_id'],
            'commodity_id' => $crowdfund_commodity_info['commodity_id'],
            'pay_status'   => 1,
            'is_enable'    => 1,
            'order_code'   => ['neq', '']
        ];
        $crowdfund_order_model = new DbCrowdfundOrder();
        $orderCodeArr          = $crowdfund_order_model->where($order_where)->column('order_code');
        if (empty($orderCodeArr)) {
            print_json(1, '该商品订单为空');
        }
        // 添加点击手动拉表记录
        $params['where'] = ['a.order_code' => ['in', $orderCodeArr]];
        $brand           = DbCrowdfund::where('id', $crowdfund_commodity_info['crowdfund_id'])->value('brand');
        $field           = "a.payment_method,b.full_id,b.full_dis_money,a.parent_order_code,a.user_id, channel,a.gift_score,a.refund_money,
            a.shop_integral,c.comm_type_id,c.is_pure,a.id,a.payment_method,a.cashier_trade_no,a.front_money,a.pre_use_money,
            a.front_pay_time,a.cashier_trade_no2,a.order_code,a.pay_order_code,a.total_money,a.money,a.name,a.phone,a.vin,
            a.order_vin,a.license_plate,a.integral,a.dlr_integral,a.card_id,a.card_code,a.created_date,a.order_status,
            a.logistics_mode,a.order_source,f.dlr_code,f.dlr_name,b.spr_id,b.b_act_price,b.count,b.all_dis,b.card_all_dis,
            b.pre_sale_id,b.commodity_pic,b.commodity_name,b.car_info,b.sku_info,b.price,b.count,b.price*b.count as total_price,
            b.actual_price,b.actual_point,b.actual_use_money,c.commodity_code,b.third_sku_code sku_code,(case h.type when 1 then '平台' when 2 then '专营店' when 3 then '官微销售' end) as type,
            (case b.sales_source when 0 then '旧数据' when 1 then '平台自营销售' when 2 then '专营店销售' when 3 then '官微销售' end) as sales_source,
            a.receipt_address,a.pay_time,a.mail_price,a.is_cc_ok,d.price old_price,a.delivery_time,b.limit_id,b.n_dis_id,
            card_money,b.mail_price goods_mail_price,a.pay_order_code2,a.cashier_trade_no,a.cashier_trade_no2,b.third_sku_code,
            b.third_order_id,b.tax_code,b.tax,b.supplier,b.cost_price,b.third_mail_price,a.ic_card_no,a.ms_order_code,a.ms_order_code2,
            b.mo_sub_id,b.work_time_json,b.act_sett_money,b.card_sett_money,b.work_time_money,b.work_time_actual_money,b.work_time_dis,b.mo_id,
            b.order_commodity_status,b.common_carrier,b.waybill_number,b.delivery_time,b.id order_commodity_id,ps.title as presale,
            point.point_order_code,b.give_integral,a.pre_point,b.qsc_num,b.commodity_id,a.source,b.gift_act_id,b.is_gift,g.title as gift_title,se.title as seckill_name,
            b.suit_id,a.card_degree_code,b.commodity_segment_dis_money";
        if ($brand == 1) {
            $field .= ",b.first_order_price,yd.point as yd_point";
        }
        $params['field'] = $field;
        $model           = new DbExports();
        $exportId        = $model->insertGetId([
            'export_type'   => 'crowdfund',
            'filter_params' => json_encode($params['where']),
            'export_key'    => md5(json_encode(input('get.'))),
            'creator'       => $this->admin_info['username'] ?? '',
        ]);

        $data = [
            'params'       => $params,
            'id'           => $exportId,
            'crowdfund_id' => $crowdfund_commodity_info['crowdfund_id'],
            'brand'        => $brand
        ];
        Queue::push('app\admin_v2\queue\OrderExport', json_encode($data), config('queue_type.export'));
        print_json(0, '已加入异步下载列表');

    }

}