<?php

namespace app\admin_v2\controller;

use app\common\model\e3s\E3sPz1aMaintenancePackageLog;
use app\common\model\e3s_log\E3sCarSeriesLog;
use app\common\model\e3s_log\E3sMaintenanceProductLog;
use app\common\model\e3s_log\E3sPackageLog;
use app\common\model\e3s_log\E3sPartCarSeriesLog;
use app\common\model\e3s_log\E3sSparePartLog;
use app\common\model\e3s\E3sSparePartUpdate;

class E3sUpdateHandle extends Comment
{

    private $e3s_spare_part_update;

    public function __construct()
    {
        parent::__construct();
        $this->e3s_spare_part_update = new E3sSparePartUpdate();
    }

    public function index()
    {
        $spare_part = new \app\common\model\e3s\E3sSparePart();
        $query = input('get.');
        $params['query'] = $query;
        $params['order'] = 'created_date desc';
        if(isset($query['service_type']) && !empty($query['service_type'])){
            $params['where']['service_type'] = $query['service_type'];
        }
        if(isset($query['update_no']) && !empty($query['update_no'])){
            $params['where']['update_no'] = $query['update_no'];
        }
        $list = $this->e3s_spare_part_update->getListPaginate($params);
        foreach ($list as $value) {
            if($value['update_type'] == 1){
                $value->update_type_msg = "成功";
            }else{
                $value->update_type_msg = "失败";
            }
            if($value['update_duration'] < 60){
                $value->update_duration = round($value['update_duration'],2) . '秒';
            }
            $value->is_goods = $spare_part->is_goods_update_no($value['service_type'],$value['update_no']);
        }
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        return $this->fetch('e3s/e3s_update_handle/index');
    }

    //查看备件明细
    public function select_e3s_spare_part(){
        $spare_part = new \app\common\model\e3s\E3sSparePart();
        $spare_part_log = new E3sSparePartLog();
        $query = input('get.');
        $params['query'] = $query;
        $params['where']['e3s_update_no'] = input('get.update_no');
        $list = $spare_part_log->getListPaginate($params);
        foreach ($list as $value){
            $html = '';
            $value->goods_list = $spare_part->goods_name(1,$value['part_no']);
            $update = $spare_part_log->where(['part_no'=>$value['part_no'],'id'=>array('elt',$value['id'])])->order('id desc')->select();
            if (count($update) >=2) {
                if($update[1]['sale_price'] != $value['sale_price']){
                    $html .= '备件单价：￥'. $value['sale_price'].' (变更前：￥'.$update[1]['sale_price'].')<br />';
                }
                if($update[1]['dlr_price'] != $value['dlr_price']){
                    $html .= '备件成本价：￥'. $value['dlr_price'].' (变更前：￥'.$update[1]['dlr_price'].')<br />';
                }
                if($update[1]['rep_part_no'] != $value['rep_part_no']){
                    $html .= '被替换件编号：'. $value['rep_part_no'].' (变更前：'.$update[1]['rep_part_no'].')<br />';
                }
                if($update[1]['part_status'] != $value['part_status']){
                    $html .= '备件状态：'. $value['part_status_cn'].' (变更前：'.$update[1]['part_status_cn'].')<br />';
                }
                if($update[1]['dlr_order_switch'] != $value['dlr_order_switch']){
                    $html .= '订货状态：'. $value['dlr_order_switch_cn'].' (变更前：'.$update[1]['dlr_order_switch_cn'].')';
                }
            }
            $value->part_details = $html;
        }
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        return $this->fetch('e3s/e3s_update_handle/select_e3s_spare_part');
    }

    //查看备件车型车系明细
    public function select_e3s_part_car_series(){
        $part_car_series_log = new E3sPartCarSeriesLog();
        $car_series = new \app\common\model\e3s\E3sCarSeries();
        $part = new \app\common\model\e3s\E3sSparePart();
        $query = input('get.');
        $params['query'] = $query;
        $params['where']['e3s_update_no'] = input('get.update_no');
        $list = $part_car_series_log->getListPaginate($params);
        foreach ($list as $value) {
            $find = $car_series->getOne(['where'=>['car_config_code' => $value['car_config_code']]]);
            $part_name = $part->getOne(['where'=>['part_no' => $value['part_no']]]);
            $value->power_type = $find['power_type_cn'] ?? '';
            $value->base_series_code = $find['base_series_code'] ?? '';
            $value->base_car_series_cn = $find['base_car_series_cn'] ?? '';
            $value->supply_status = $find['supply_status_cn'] ?? '';
            $value->large_car_type_cn = $find['large_car_type_cn'] ?? '';
            $value->part_name = $part_name['part_name'];
        }
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        return $this->fetch('e3s/e3s_update_handle/select_e3s_part_car_series');
    }

    //查看备件车型车系明细
    public function select_e3s_car_series(){
        $part_car_series_log = new E3sCarSeriesLog();
        $query = input('get.');
        $params['query'] = $query;
        $params['where']['e3s_update_no'] = input('get.update_no');
        $list = $part_car_series_log->getListPaginate($params);
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        return $this->fetch('e3s/e3s_update_handle/select_e3s_car_series');
    }

    //查看保养套餐
    public function select_e3s_maintenance_package()
    {
        $spare_part = new \app\common\model\e3s\E3sSparePart();
        $part_car_series_log = new E3sPackageLog();
        $query = input('get.');
        $params['query'] = $query;
        $params['where']['e3s_update_no'] = input('get.update_no');
        $list = $part_car_series_log->getListPaginate($params);
        foreach ($list as $value){
            $update = $part_car_series_log->where(['maintain_group_id'=>$value['maintain_group_id'],'id'=>array('lt',$value['id'])])->order('id desc')->select();
            $html = '';
            if (count($update) >=2) {
                if($update[1]['upgrade_type'] != $value['upgrade_type']){
                    $html .= '套餐状态：'. $value['part_status_cn'].' (变更前：'.$update[1]['part_status_cn'].')<br />';
                }
                if($update[1]['saler_amount'] != $value['saler_amount']){
                    $html .= '套餐金额：'. $value['saler_amount'].' (变更前：'.$update[1]['saler_amount'].')<br />';
                }
            }
            $value->part_details = $html;
            if(empty($update)){
                $value->type_name = $value['upgrade_type'] == 1 ? '新增' : '删除';
            }else{
                $value->type_name = $value['upgrade_type'] == 1 ? '修改' : '删除';
            }
            $value->goods_list = $spare_part->goods_name(2,$value['maintain_group_code']);
        }
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        return $this->fetch('e3s/e3s_update_handle/select_e3s_maintenance_package');
    }

    public function select_e3s_maintenance_product(){
        $part_car_series_log = new E3sMaintenanceProductLog();
        $query = input('get.');
        $params['query'] = $query;
        $params['where']['e3s_update_no'] = input('get.update_no');
        $list = $part_car_series_log->getListPaginate($params);
        foreach ($list as $value){
            $value->type_name = $value['upgrade_type'] == 1 ? '新增/修改' : '删除';
        }
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        return $this->fetch('e3s/e3s_update_handle/select_e3s_maintenance_product');
    }

    //查看保养套餐
    public function select_e3s_pz1a_maintenance_package()
    {
        $spare_part = new \app\common\model\e3s\E3sSparePart();
        $pz1a_package = new E3sPz1aMaintenancePackageLog();
        $query = input('get.');
        $params['query'] = $query;
        $params['where']['e3s_update_no'] = input('get.update_no');
        $list = $pz1a_package->getListPaginate($params);
        foreach ($list as $value){
            $value->type_name = $value['upgrade_type'] == 1 ? '新增/修改' : '删除';
            $value->goods_list = $spare_part->goods_name(1,$value['sp_basic_code']);
        }
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        return $this->fetch('e3s/e3s_update_handle/select_e3s_pz1a_maintenance_package');
    }

    public function select_e3s_specific_relation()
    {
        $part_update = new E3sSparePartUpdate();
        $spare_part = new \app\common\model\e3s\E3sSparePart();
        $query = input('get.');
        $params['query'] = $query;
        $params['where']['a.update_no'] = input('get.update_no');
        $params['field'] = "a.update_type as upgrade_type,c.part_no,c.part_name,a.new_add,a.new_update,a.new_del";
        $list = $part_update->e3s_specific_relation_list($params);
        foreach ($list as $value){
            if($value['new_add'] != 0){
                $value->type_name =  '新增';
            }
            if($value['new_update'] != 0){
                $value->type_name =  '修改';
            }
            if($value['new_del'] != 0){
                $value->type_name =  '删除';
            }
            $value->goods_list = $spare_part->goods_name(1,$value['part_no']);
        }
        $page = $list->render();
        $this->assign('list',$list);
        $this->assign('page',$page);//分页
        return $this->fetch('e3s/e3s_update_handle/select_e3s_specific_relation');
    }
}