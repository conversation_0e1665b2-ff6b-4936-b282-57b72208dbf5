{extend name="public:base_layout" /}

{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<style>
    .file-drop-zone-title {
        color: #aaa;
        font-size: 20px;
        padding: 25px 15px;
    }
    .kv-file-zoom,.file-footer-buttons, .file-drag-handle, .file-upload-indicator  {
        display: none;
    }
    .progress{
        display: none;
    }
</style>
{/block}

{block name="content"/}

<div class="panel-body">
    {include file="commodity:commodity_nav" /}
    <div id="alert-danger" class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">商品基本信息</legend>
        <form id="add-step-2" class="form-horizontal form-bordered" data-parsley-trigger="change">
            <div class="form-group">
                <label class="control-label col-md-2">商品分类<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">


                    {$commodity_type['pparent_type_name']} > {$commodity_type['parent_type_name']} > {$commodity_type['comm_type_name']}
                    <a href="{:url('addStep1')}?commodity_id={$commodity_id}&is_grouped={$is_grouped}" class="btn btn-white btn-sm m-r-5 m-b-5">选择分类</a>
                    <input type="hidden" name="comm_type_id" value="{$commodity_type['id']}">

                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" name="commodity_name" placeholder="请输入商品单位" value="{$row['commodity_name']}" class="form-control " data-parsley-required="true" data-parsley-length="[3, 50]">
                    <p class="m-t-5">商品标题名称长度至少3个字符，最长50个汉字</p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">到店商品类型:</label>
                <div class="col-md-10">
                    <input type="hidden"class="is_show_change" name="dd_commodity_type" value="{$row['dd_commodity_type']}">
                    <!--到店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-五年双保升级权益套餐 6保养套餐-其他 7到店代金券-->
                    <label class="radio-inline">
                        <input type="radio" class="is_show_change" name="dd_commodity_type" <?php if($row['dd_commodity_type']==9) echo 'checked';?> value="9" data-parsley-required="true" data-parsley-multiple="radiorequired" /> 到店备件商品
                    </label>
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio" class="is_show_change" name="dd_commodity_type" --><?php //if($row['dd_commodity_type']==1) echo 'checked';?><!-- value="1" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-老友惠保养套餐-->
<!--                    </label>-->
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio" class="is_show_change" name="dd_commodity_type" --><?php //if($row['dd_commodity_type']==3) echo 'checked';?><!-- value="3" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-心悦保养套餐-->
<!--                    </label>-->
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio" class="is_show_change" name="dd_commodity_type" --><?php //if($row['dd_commodity_type']==4) echo 'checked';?><!-- value="4" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-五年双保升级权益套餐-->
<!--                    </label>-->
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio" class="is_show_change" name="dd_commodity_type" --><?php //if($row['dd_commodity_type']==41) echo 'checked';?><!-- value="41" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-五年双保升级权益套餐(全合成)-->
<!--                    </label>-->
                    <label class="radio-inline">
                        <input type="radio" class="is_show_change" name="dd_commodity_type" <?php if($row['dd_commodity_type']==6) echo 'checked';?> value="6" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-其他
                    </label>
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio" class="is_show_change" name="dd_commodity_type" --><?php //if($row['dd_commodity_type']==7) echo 'checked';?><!-- value="7" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 到店代金券-->
<!--                    </label>-->
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">收录推荐系统:</label>
                <div class="col-md-10">
                    <!--0否1是-->
                    <label class="radio-inline">
                        <input type="radio"  name="arrival_bdp" value="0" <?php if($row['arrival_bdp']==0) echo 'checked';?> data-parsley-required="true" data-parsley-multiple="radiorequired" /> 否
                    </label>
                    <label class="radio-inline">
                        <input type="radio"  name="arrival_bdp" value="1" <?php if($row['arrival_bdp']==1) echo 'checked';?> data-parsley-required="true" data-parsley-multiple="radiorequired"/> 是
                    </label>
                    <p class="m-t-5">部分商品不能推给推荐系统，因为是组合销售商品，如车联保养套餐</p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">品牌:</label>
                <div class="col-md-5">
                    <select name="brands_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id">
                        <option value="">
                            请选择
                        </option>
                        <?php foreach($brands_list as $key=>$val):?>
                            <option value="{$val['id']}" <?php if ($val['id'] == $row['brands_id']) echo 'selected'?>>{$val['brands_name']}</option>
                        <?php endforeach;?>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品编码:</label>
                <div class="col-md-5">
                    <input type="text" name="commodity_code" value="{$row['commodity_code']}" placeholder="请输入商品编码" class="form-control "  data-parsley-length="[1, 200]">

                </div>
            </div>
            <div class="form-group" style="display: none">
                <label class="control-label col-md-2">价格范围<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">

                    <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">原价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " value="{$row['original_price_range_start']}" name="original_price_range_start" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">

                    </div>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " value="{$row['original_price_range_end']}" name="original_price_range_end" placeholder="最高价格"  data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                    </div>

                    <label class="control-label col-md-2 m-r-0 text-right">现价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " value="{$row['discount_price_range_start']}" name="discount_price_range_start" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">

                    </div>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " value="{$row['discount_price_range_end']}" name="discount_price_range_end" placeholder="最高价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                    </div>

                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2 ">商品特性<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-5 p-l-0">
                        <label class="control-label col-md-4 width-100 m-r-0 p-l-0 text-left ">是否纯正商品<i class="m-r-3 text-danger">*</i>:</label>
                        <label class="radio-inline">
                            <input type="radio"   <?php if($row['is_pure']==1) echo 'checked';?> name="is_pure" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="radiorequired" class="is_show_change" /> 是
                        </label>

                        <label class="radio-inline">
                            <input type="radio"  <?php if($row['is_pure']==0) echo 'checked';?> name="is_pure" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="radiorequired" class="is_show_change"/> 否
                        </label>
                    </div>
                    <div class="col-md-5 p-l-0">
                        <label class="control-label col-md-4 width-100 m-r-0 p-l-0 text-left">是否商城商品<i class="m-r-3 text-danger">*</i>:</label>


                        <label class="radio-inline">
                            <input type="radio" <?php if($row['is_shop']==1) echo 'checked';?> name="is_shop" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="is_shop" class="is_show_change" /> 是
                        </label>


                        <label class="radio-inline">
                            <input type="radio" <?php if($row['is_shop']==0) echo 'checked';?> name="is_shop" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="is_shop" class="is_show_change"/> 否
                        </label>

                    </div>

                    <?php if ($admin_type==1):?>

                    <div class="col-md-7 p-l-0">
                        <label class="control-label col-md-4 width-80 m-r-0 p-l-0 text-left">上架源<i class="m-r-3 text-danger">*</i>:</label>
                        <?php foreach ($shelves_sources as $key=>$val):?>
                            <?php if (!in_array($key,[8])):?>
                            <label class="checkbox-inline">
                                <input type="checkbox" class="shelves_sources_check" name="shelves_sources[]" value="{$key}" <?php if (in_array($key,explode(',',$row['shelves_sources']))) echo 'checked';?>  class="is_show_change">{$val}
                            </label>
                            <?php endif;?>
                        <?php endforeach;?>
                    </div>

                    <?php endif;?>



                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">商品种类:</label>


                <div class="col-md-5 ">
<!--                    --><?php //foreach ($commodity_class as $key=>$val):?>

                       <label class="radio-inline">
                          <input type="radio" disabled="disabled"  name="commodity_class" value="1" <?php if($row['commodity_class']==1) echo 'checked';?> id="radio-required" data-parsley-required="true" data-parsley-multiple="commodtiy_class" /> {$commodity_class[1]}
                       </label>
                    <label class="radio-inline">
                        <input type="radio" disabled="disabled"  name="commodity_class" value="8" <?php if($row['commodity_class']==8) echo 'checked';?> id="radio-required" data-parsley-required="true" data-parsley-multiple="commodtiy_class" /> {$commodity_class[8]}
                    </label>
<!--                    --><?php //endforeach;?>

                    <input type="hidden" name="commodity_class" value="{$row['commodity_class']}">
                </div>


            </div>

            <div class="form-group">
                <label class="control-label col-md-2">订单提示:</label>
                <div class="col-md-5 ">
                    <input type="text" class="form-control " name="tips" id="tips" data-toggle="tooltip" title="字数不能超过50个。" maxlength="50" value="{$row['tips']}">
                </div>

            </div>




            <?php if ($row['commodity_class']==3) :?>
            <div class="form-group" id="commodity_card_ids" >
                <label class="control-label col-md-2">卡券:</label>
                <div class="col-md-5">

                    <input type="text" class="form-control is_show_change" value="{$row['commodity_card_name']}" onkeydown="onlyView();" name="commodity_card_ids_name" placeholder="选择优惠券" >


                    <input type="hidden" class="form-control hide_card_ids" name="commodity_card_ids" >
                </div>
            </div>
            <?php endif;?>
            <?php if ($row['commodity_class']==5) :?>
            <div class="form-group" id="mark_coupon_id">
                <label class="control-label col-md-2">平台卡券id<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control is_show_change" name="mark_coupon_id" placeholder="请输入平台卡券id" value="{$row['commodity_card_ids']}">
                </div>
            </div>
            <?php endif;?>
            <div class="form-group">
                <label class="control-label col-md-2">商品单位<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control "  value="{$row['unit']}" name="unit" placeholder="商品单位" data-parsley-required="true"  data-parsley-length="[0,4]">
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">供应商渠道:{$row.supplier}</label>
                <div class="col-md-5">
                    <select id="template_guid" class="form-control width-300" name="supplier">
                        <option value="">请选择</option>
                        <?php foreach ($supp_list as $k => $vo): ?>
                            <option value="{$vo['value_code']}"
                                    <?php if($row['supplier']===$vo['value_code']):?>
                                        selected="selected"
                                    <?php endif; ?>
                                    >
                                {$vo['county_name']}
                            </option>
                        <?php endforeach;?>
                    </select>
                </div>
            </div>



            <div class="form-group">
                <label class="control-label col-md-2">排序<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control " value="{$row['sort']}" name="sort" placeholder="请输入排序" data-parsley-required="true" data-parsley-range="[0,10000]">
                </div>
            </div>
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">适用车型:</label>-->
<!--                <div class="col-md-5">-->
<!--                    <input type="text" data-toggle="modal" value="{$row['sort']}" class="form-control " name="car_series_name" placeholder="选择车型" data-target="#car-modal">-->
<!--                </div>-->
<!--            </div>-->


            <div class="form-group">
                <label class="control-label col-md-2">商品图片<i class="m-r-3 text-danger">*</i>:<br><span>(用于商品详情的头图)</span></label>
                <div class="col-md-10  image_group">


                        <div class="dndc-upload-pic goods_pic" sp-id="0" data-image-id="0" id="image-id-0">
                            <label  class="sp-value m-t-10 m-l-10">默认</label>
                            <label>

                                <a href="javascript:;" class="btn-image btn btn-primary m-r-5">上传图片</a>
                            </label>

                            <p>5张,200K,jpg,png
                                <br/><a href="javascript:;" class="btn-px" id="btn-px">点击排序</a>
                            </p>
                            <ul>
                                <?php foreach($sku_image as $val1):?>
                                    <li class="move-item">
                                        <img image-value="{$val1}" src="{$Think.config.upload.url}{$val1}">
                                        <del></del><span>修改</span>
                                    </li>
                                <?php endforeach;?>
                            </ul>
                        </div>

                    <input type="file" name="image" class="hide"  id="goods-image-input">
                </div>


            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品视频</label>
                <div class="col-md-10 ">
                    <input id="file_video" name="file_video" type="file" class="file" data-overwrite-initial="false">
                    <input id="video_img" name="video_img" type="hidden" value="{$row['video']}">
                </div>


            </div>



<!--            <legend class="pull-left width-full">商品详情描述</legend>-->
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">商品详情<i class="m-r-3 text-danger">*</i>:</label>-->
<!--                <div class="col-md-9 ">-->
<!--                    <script id="content"  name="content" type="text/plain" style="width:100%;height:300px;"></script>-->
<!--                </div>-->
<!---->
<!--            </div>-->
            <input type="hidden" name="id" value="{$row['id']}">
            <input type="hidden" name="action" value="update">



            <div class="text-center m-t-20" style="position: sticky;bottom: 0px;height:80px;background-color: #fff;line-height: 80px;">
<!--                <button type="button" id="put-form-view" class="btn btn-primary m-r-5 m-b-5">预览</button>-->
                <button id="put-form" type="button" class="btn btn-primary m-r-5 m-b-5">下一步</button>
            </div>
        </form>





    </div>

    <!----begin 选择车系---->
    <div class="modal fade" id="car-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择车型</h4>
                </div>

                <div class="modal-body car-series ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <div class="form-group">
                        <div class="col-md-2 p-r-0 checkbox">
                            <label><input type="checkbox" id="check_all" class="car_checkbox ">全选</label>
                        </div>
                        {volist name="brand_s" id="v" key="k"}
                        <div class="col-md-2 p-r-0 checkbox">
                            <label><input type="checkbox"   class="car-brand-type"  data-brand-type="{$k}" >{$v}</label>
                        </div>
                        {/volist}
<!--                        <div class="col-md-2 p-r-0 checkbox">-->
<!--                            <label><input type="checkbox"   class="car-brand-type"  data-brand-type="1" >NISSAN</label>-->
<!--                        </div>-->
<!--                        <div class="col-md-2 p-r-0 checkbox">-->
<!--                            <label><input type="checkbox"  class="car-brand-type"  data-brand-type="2" >启辰</label>-->
<!--                        </div>-->
                    </div>
                    <legend class="pull-left width-full"></legend>
                    <?php foreach($car_series_list as $key=>$val):?>
                        <div class=" form-group ">
                            <div class="col-md-2 p-r-0 checkbox">
                                <label><input type="checkbox"  class="car_checkbox " id="{$key}"  data-parent="check_all"> {$val['car_series_name']}</label>
                            </div>

                            <div class="col-md-9 checkbox">
                                <?php foreach($val['sub_data'] as $key1=>$val2):?>
                                    <label class="m-r-5"><input type="checkbox" data-sub-brand-type="{$val2['brand_type']}" <?php if(in_array($key1,explode(',',$row['car_series_id']))) echo 'checked';?> class="car_checkbox min" data-parent="{$key}" value="{$key1}" data-car-series="{$val2['sub_car_series_name']}" name="car_series[]">{$val2['sub_car_series_name']}</label>
                                <?php endforeach;?>
                            </div>

                        </div>
                        <legend class="pull-left width-full"></legend>
                    <?php endforeach;?>

                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="add-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 预览二维码---->
    <div class="modal fade" id="preview-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">扫码二维码预览</h4>
                </div>

                <div class="modal-body qc_code text-center ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>

                    <img src="">

                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white btn-sm" data-dismiss="modal">关闭</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 删除---->
    <div class="modal fade" id="deleteModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i> 删除操作</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" id="no-del" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>



</div>
{/block}
{block name="script"/}
{include  file="public/baidu_ueditor_js" /}
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/purify.min.js"></script>
<script src="__STATIC__admin_v2/js/sortable.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput_locale_zh.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js?v=25"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>





<script>
    var url_get_sku_table="{:url('getSkuTable')}";
    ue.addListener("ready", function() {
        ue.setContent('{$row['detail_content']}');
    });
    $('#btn-px').on('click',function(){
        if($(this).hasClass('on')){
            $('.goods_pic>ul').sortable({
                disabled: true,
            });
            $(this).removeClass('on').text('点击排序');
        }else {
            $('.goods_pic>ul').sortable({
                disabled: false,
                items:'.move-item',
                // cancel:'.other',
            });
            $(this).addClass('on').text('排序完成');;
        }
    })
    var is_show = 0;
    $(function(){
        carIni();
        batchIni();
        $("#put-form,#put-form-view").on('click',function(){
            var sku_list  =getSkuList();
            var image_list=getImageList();
            var $form=$("#add-step-2");
            var validate=$form.psly().validate();  //表单验证
            if(!validate) return false;
            var form_data=$form.serialize();
            var alert_obj=$("#alert-danger");
            if($(this).attr("id")=='put-form-view'){
                var is_preview=1;
            }else {
                var is_preview=0;
            }
            if (image_list.length<=0){
                layer.msg('默认图片不能为空');
                return false;
            }
            console.log(form_data);
            var data=$.param({is_preview:is_preview,car_series_id:car_series_id,'sku_list':sku_list,'image_list':image_list}) + '&' + form_data
            // form_data.push({name:'sku_list',value:sku_list});
            if (is_preview){
                Custom.ajaxPost("{:url('saveGroup')}",data,alert_obj,null,function(res){
                    var url=res.data.com_url;
                    console.log(url);
                    //调用lay_out 生成二维码
                   /* $('#output').empty();
                    $('#output').qrcode(url);
                    $("#commodity-qrc-modal").modal("show");*/
                    $("#mod-mobile-pop").find("#output").attr("src",url);
                    $("#mod-mobile-pop").show();
                })
            }else {
                if (is_show == 1) {
                    layer.open({
                        type: 2,
                        title: ['查看#备件名称#车型详情'],
                        btn: ['全部下架并保存', '取消'],
                        area: ['70%', '80%'],
                        content: 'shelf_commodity?commodity_id=' + $('input[name="id"]').val(),
                        yes: function (res) {
                            Custom.ajaxPost("{:url('saveGroup')}?is_off=1", data, alert_obj, "{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}");
                        }
                    })
                } else {
                    Custom.ajaxPost("{:url('saveGroup')}", data, alert_obj, "{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}");
                }
            }
        });

    });

    $('.is_show_change').on('change', function (){
        is_show = 1;
    })
</script>
{/block}