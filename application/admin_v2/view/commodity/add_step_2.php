{extend name="public:base_layout" /}

{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<link href="__STATIC__admin_v2/plugins/layui_v2.5.7/css/layui.css" rel="stylesheet"/>
<link rel="stylesheet" href="__STATIC__admin_v2/css/jquery.page.css">
<style>
    .file-drop-zone-title {
        color: #aaa;
        font-size: 20px;
        padding: 25px 15px;
    }
    .kv-file-zoom,.file-footer-buttons, .file-drag-handle, .file-upload-indicator  {
        display: none;
    }
    .progress{
        display: none;
    }
    .layui-tab-title .layui-this{
        background-color: #348fe2;
        color: #FFF;
    }
    .layui-tab-title li{
        border-top:0.5px solid #ccc;
        border-right:0.5px solid #ccc;
    }
    .layui-tab-title li:first-child{
        border-left:0.5px solid #ccc;
    }
</style>
{/block}

{block name="content"/}

<div class="panel-body">
    {include file="commodity:commodity_nav" /}
    <div class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">商品基本信息</legend>
        <form id="add-step-2"class="form-horizontal form-bordered" data-parsley-trigger="change">
            <div class="form-group">
                <label class="control-label col-md-2">商品分类<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">

                    {$commodity_type->pparent_type_name} > {$commodity_type->parent_type_name} > {$commodity_type->comm_type_name}
                    <a href="{:url('addStep1')}" class="btn btn-white btn-sm m-r-5 m-b-5">选择分类</a>
                    <input type="hidden" name="comm_type_id" value="{$commodity_type->id}">

                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" name="commodity_name" placeholder="请输入商品名称" class="form-control " data-parsley-required="true" data-parsley-length="[3, 50]">
                    <p class="m-t-5">商品标题名称长度至少3个字符，最长50个汉字</p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">到店商品类型:</label>
                <div class="col-md-10">
                    <div class="col-md-12">
                        <!--到店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-五年双保升级权益套餐 6保养套餐-其他 7到店代金券 8到店电子券-->
                        {foreach ($dd_commodity_type as $k => $dd_type)}
                        <label class="radio-inline">
                            <input type="radio"  name="dd_commodity_type" value="{$k}" data-parsley-required="true" data-parsley-multiple="radiorequired" />
                            {$dd_type}
                        </label>
                        {/foreach}
                    </div>
                    <div class="col-md-12" style="margin-top: 8px">
                        <span style="color: red">注意：运营规范，到店类商品务必选择此项某个类型！！</span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">收录推荐系统:</label>
                <div class="col-md-10">
                    <!--0否1是-->
                    <label class="radio-inline">
                        <input type="radio"  name="arrival_bdp" value="0" data-parsley-required="true" data-parsley-multiple="radiorequired" /> 否
                    </label>
                    <label class="radio-inline">
                        <input type="radio"  name="arrival_bdp" value="1" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 是
                    </label>
                    <p class="m-t-5">部分商品不能推给推荐系统，因为是组合销售商品，如车联保养套餐</p>
                </div>
            </div>

<!--            <div class="form-group" id="wi_business_type" style="display: none">-->
<!--                <label class="control-label col-md-2">工时业务类别选择:</label>-->
<!--                <div class="col-md-5">-->
<!--                    <select id="work_hour_type" class="form-control width-300" name="work_hour_type">-->
<!--                        <option value="0">请选择</option>-->
                        <?php /** foreach ($work_hour_type as $k => $vo): ?>
                            <option value="{$k}">{$vo}</option>
                        <?php endforeach; */?>
<!--                    </select>-->
<!--                </div>-->
<!--                <p class="m-t-5"></p>-->
<!--            </div>-->

            <div class="form-group">
                <label class="control-label col-md-2">机油油型:</label>
                <div class="col-md-5">
                    <select id="machine_oil_type" class="form-control width-300" name="machine_oil_type">
                        <option value="0">请选择</option>
                        <option value="1">1L</option>
                        <option value="4">4L</option>
                    </select>
                </div>
                <p class="m-t-5"></p>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">品牌:</label>
                <div class="col-md-5">
                    <select name="brands_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id">
                        <option value="">
                            请选择
                        </option>
                        <?php foreach($brands_list as $key=>$val):?>
                            <option value="{$val['id']}" >{$val['brands_name']}</option>
                        <?php endforeach;?>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品编码:</label>
                <div class="col-md-5">
                    <input type="text" name="commodity_code" placeholder="请输入商品编码" class="form-control "  data-parsley-length="[1, 200]">

                </div>
            </div>
            <div class="form-group" style="display: none">
                <label class="control-label col-md-2">价格范围<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">

                    <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">原价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " name="original_price_range_start" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly>
                    </div>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " name="original_price_range_end" placeholder="最高价格"  data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly>
                    </div>

                    <label class="control-label col-md-2 width-80 p-l-0 m-r-0 text-right">现价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " name="discount_price_range_start" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly>

                    </div>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " name="discount_price_range_end" placeholder="最高价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly>
                    </div>

                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2 ">商品特性<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-5 p-l-0">
                        <label class="control-label col-md-4 width-100 m-r-0 p-l-0 text-left ">是否纯正商品<i class="m-r-3 text-danger">*</i>:</label>
                        <label class="radio-inline">
                            <input type="radio"  checked name="is_pure" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="radiorequired" /> 是
                        </label>

                        <label class="radio-inline">
                            <input type="radio"  name="is_pure" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 否
                        </label>
                    </div>
                    <div class="col-md-7 p-l-0">
                        <label class="control-label col-md-4 width-100 m-r-0 p-l-0 text-right">是否商城商品<i class="m-r-3 text-danger">*</i>:</label>
                        <label class="radio-inline">
                            <input type="radio" checked name="is_shop" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="is_shop" /> 是
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="is_shop" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="is_shop"/> 否
                        </label>

                    </div>
                    <?php if ($admin_type==1):?>
                    <div class="col-md-5 p-l-0">
                        <label class="control-label col-md-4 width-80 m-r-0 p-l-0 text-left">上架源<i class="m-r-3 text-danger">*</i>:</label>
                        <?php foreach ($shelves_sources as $key=>$val):?>
                            <label class="checkbox-inline">
                                <input type="checkbox" class="shelves_sources_check" id="shelves_sources_check_{$key}" name="shelves_sources[]" value="{$key}" <?php  if (!in_array($key,[5,6,7,8])){ echo 'checked';} ?>>{$val}
                            </label>
                        <?php endforeach;?>
                    </div>
                    <br>
                    <?php endif;?>




                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品种类:</label>


                    <div class="col-md-5 ">
                        <?php foreach ($commodity_class as $key=>$val):?>

                            <label class="radio-inline">
                                <input type="radio"  name="commodity_class" value="{$key}" id="radio-required" data-parsley-required="true" data-parsley-multiple="commodtiy_class" /> {$val}
                            </label>
                        <?php endforeach;?>

                    </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">订单提示:</label>
                <div class="col-md-5 ">
                    <textarea  class="form-control letter-count" placeholder="订单提示" rows="5" name="tips" id="tips" maxlength="50" title="字数不能超过50个。"></textarea>
                </div>

            </div>





            <div class="form-group" id="commodity_card_ids" style="display: none;">
                <label class="control-label col-md-2">卡券:</label>
                <div class="col-md-5">

                        <input type="text" class="form-control commodity_card" onkeydown="onlyView();" name="commodity_card_ids_name" placeholder="选择优惠券" >


                    <input type="hidden" class="form-control hide_card_ids" name="commodity_card_ids" >
                </div>
            </div>

            <div class="form-group" id="mark_coupon_id" style="display: none;">
                <label class="control-label col-md-2">平台卡券id<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control " name="mark_coupon_id" placeholder="请输入平台卡券id">
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品单位<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control " name="unit" placeholder="请输入商品单位" data-parsley-required="true" data-parsley-length="[0,4]">
                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">供应商渠道:</label>
                <div class="col-md-5">
                    <select id="template_guid" class="form-control width-300" name="supplier">
                        <option value="">请选择</option>
                        <?php foreach ($supp_list as $k => $vo): ?>
                            <option value="{$vo['value_code']}">
                                {$vo['county_name']}
                            </option>
                        <?php endforeach;?>
                    </select>
                </div>
            </div>
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">税务编码:</label>-->
<!--                <div class="col-md-5">-->
<!--                    <input type="text" class="form-control " name="tax_code" placeholder="请输入税务编" >-->
<!--                </div>-->
<!--            </div>-->

            <div class="form-group">
                <label class="control-label col-md-2">排序<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control " name="sort" placeholder="请输入排序" data-parsley-required="true" data-parsley-range="[0,10000]">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">积分兑换专区:</label>
                <div class="col-md-5">
                    <label class="checkbox-inline">
                        <input type="checkbox" class="is_integral" onclick="on_integral(this)" id="is_integral" name="is_integral_shop" value="1">是否积分兑换
                    </label>
                    <label class="checkbox-inline">
                        <input type="checkbox" class="is_integral"  id=" " name="is_integral_shop" value="2">是否移动积分兑换
                    </label>
                </div>
            </div>
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">适用车型:</label>-->
<!--                <div class="col-md-5">-->
<!--                    <input type="text" data-toggle="modal" class="form-control " name="car_series_name" placeholder="选择车型" data-target="#car-modal">-->
<!--                </div>-->
<!--            </div>-->

            <legend class="pull-left width-full m-t-15">商品规格及图片</legend>

            <div class="form-group">
                <label class="control-label col-md-2">商品规格:</label>
                <div class="col-md-10 sp">
                    <div class="col-md-12">
                        <span class="m-r-20">规格</span>规格值（通过选择规格值建立商品与商品规格的关系，最多只能选择3种规格）
                    </div>
                    <legend class="pull-left width-full"></legend>
                    <ul class="col-md-12 checkbox" id="commodity_type_none" style="display: none">
                        <li>
                            <span class="m-r-20 sp-name">地区</span>
                            <div>
                                <label class=" m-r-5 sp-value">
                                        <input type="checkbox"  class="sku_check_area" sp-value-name="T0:上海"  value="A1" name="area_value[]" >T0:上海</label>
                                <label class=" m-r-5 sp-value">
                                    <input type="checkbox"  class="sku_check_area" sp-value-name="T1:A类"  value="A" name="area_value[]" >T1:A类</label>
                                <label class=" m-r-5 sp-value">
                                    <input type="checkbox"  class="sku_check_area" sp-value-name="T2:准A类"  value="B" name="area_value[]" >T2:准A类</label>
                                <label class=" m-r-5 sp-value">
                                    <input type="checkbox"  class="sku_check_area" sp-value-name="T3:B类"  value="C" name="area_value[]" >T3:B类</label>
                                <label class=" m-r-5 sp-value">
                                    <input type="checkbox"  class="sku_check_area" sp-value-name="T4:C类"  value="D" name="area_value[]" >T4:C类</label>
                            </div>
                        </li>
                        <legend class="pull-left width-full is_integral_list"></legend>
                    </ul>
                    <ul class="col-md-12 checkbox" id="commodity_type_none_one" style="display: none">
                        <li>
                            <span class="m-r-20 sp-name">升级类型</span>
                            <div>
                                <label class=" m-r-5 sp-value">
                                    <input type="checkbox"  class="sku_check_area_type" sp-value-name="0-普通"  value="0" name="area_value[]" >0-普通</label>
                                <label class=" m-r-5 sp-value">
                                    <input type="checkbox"  class="sku_check_area_type" sp-value-name="1-全合成"  value="1" name="area_value[]" >1-全合成</label>
                            </div>
                        </li>
                        <legend class="pull-left width-full is_integral_list"></legend>
                    </ul>
                    <ul class="col-md-12 checkbox ">
                        <?php foreach($is_integral as $key=>$val):?>
                            <li class="is_integral_list" style="display: none">
                                <span class="m-r-20  sp-name" sp-id="{$key}" sp-name="{$val['sp_name']}">{$val['sp_name']}</span>
                                <div sp-id="{$key}">
                                    <?php foreach($val['sp_value'] as $key1=>$val1):?>
                                        <label class=" m-r-5 sp-vlaue"><input type="checkbox" sp-index="{$key}" sp-value-name="{$val1['sp_value_name']}" sp-id="{$val1['sp_id']}" class="sku_ckeck"  value="{$val1['id']}" name="sp_value_id[]" >{$val1['sp_value_name']}</label>
                                    <?php endforeach;?>
                                </div>
                            </li>
                            <legend class="pull-left width-full is_integral_list" style="display: none"></legend>
                        <?php endforeach;?>
                        <?php foreach($sp_value as $key=>$val):?>
                            <li>
                                <span class="m-r-20  sp-name" sp-id="{$key}" sp-name="{$val['sp_name']}">{$val['sp_name']}</span>
                                <div sp-id="{$key}">
                                    <?php foreach($val['sp_value'] as $key1=>$val1):?>
                                        <label class=" m-r-5 sp-vlaue"
                                            <?php if ($val['sp_name'] == '套餐折扣'):?>
                                               id="zhe_kou_{$key1}"
                                               <?php endif;?>
                                        ><input type="checkbox" sp-index="{$key}" sp-value-name="{$val1['sp_value_name']}" sp-id="{$val1['sp_id']}" class="sku_ckeck"  value="{$val1['id']}" name="sp_value_id[]" >{$val1['sp_value_name']}</label>
                                    <?php endforeach;?>
                                </div>
                            </li>
                            <legend class="pull-left width-full"></legend>
                        <?php endforeach;?>
                    </ul>
                    <div class="col-md-12">
                        可勾选商品对应的规格及规格值，当勾选两种不同规格的规格值后将组合成一种商品SKU，可从下方表格中进行具体设定。<br>
                        规格最多不超过3组。
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品价格及库存<i class="m-r-3 text-danger">*</i>:</label>
                <div id="sku" class="col-md-10 ">
                    <table class="table table-bordered price-stock">
                        <thead>
                        <tr class="text-center">
                            <th>规格图片</th>
                            <th>商品规格</th>
                            <th id="all_price_th">
                                <a href="#" id="all_price" data-type="text" class="btn btn-primary btn-sm editable editable-click all_price" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i class="fa fa-edit m-l-5"></i></a>
                            </th>
                            <th>
                                <a href="#" id="all_cost_price" data-type="text" class="btn btn-primary btn-sm editable editable-click all_cost_price" data-value="" data-placeholder="成本价" data-title="批量设置成本价">成本价<i class="fa fa-edit m-l-5"></i></a>
                            </th>
                            <th>
                                <a href="#" id="all_stock" data-type="text" class="btn btn-primary btn-sm editable editable-click all_stock" data-value="" data-placeholder="请输入库存" data-title="批量设置库存">库存<i class="fa fa-edit m-l-5"></i></a>
                            </th>
                            <th style="width: 110px;">
                                <a href="#" id="all_coefficient" data-type="text" class="btn btn-primary btn-sm editable editable-click all_coefficient" data-value="" data-placeholder="请输入发货系数" data-title="批量设置发货系数">发货系数<i class="fa fa-edit m-l-3"></i></a>
                            </th>
                            <th>
                                <a href="#" id="all_tax" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax" data-value="" data-placeholder="税率" data-title="批量设置税率">税率<i class="fa fa-edit m-l-5"></i></a>
                            </th>
                            <th id="taxt_code_th">
                                <a href="#" id="all_tax_code" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax_code"  data-value="" data-placeholder="税务编码" data-title="批量设置税务编码">税务编码<i class="fa fa-edit m-l-5"></i></a>
                            </th>
                            <th class="dd_commodity_type_7" style="display: none;">
                                <a href="javascript:;" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="">关联代金券</a>
                            </th>
                        </tr>
                        </thead>

                        <tbody><tr class="text-center" data-sku-list="0">
                            <td class="text-left sku-image-td">

                                <div class="parents-add">
                                    <a class="btn btn-success btn-sm add-sku-image" data-sku-image=""><i class="fa fa-lg fa-plus"></i>添加</a>
                                </div>
                                <div class="parents-update" style="display: none;">
                         <span class="dndc-example1">
                         <a class="btn btn-success btn-sm view-sku-image" ><i class="fa fa-x fa-eye "></i>预览</a>
                            <div class="pop-preview">
                                <img src="">
                           </div>
                       </span>

                                    <a class="btn btn-white btn-sm update-sku-image" ><i class="fa fa-undo"></i></i>替换</a>
                                    <a class="btn btn-default btn-sm del-sku-image" ><i class="fa fa-trash-o"></i></i>删除</a>
                                </div>


                            </td>
                            <td id="element" style="display: none">
                                <input type="hidden" name="hours_id" class="hours_id">
                            </td>
                            <td id="element" class="text-left">
                                <div class="col-md-12 p-l-0" id="sku_code" style="width: 100px;">
                                    <input type="text" onmouseover="this.title=this.value" class="form-control sku_code" data-parsley-length="[1,2000]" >
                                </div>
                                <p><a href="javascript:void(0);" class="hidden win_beijian">选择</a></p>
                                <p><a href="javascript:void(0);" class="hidden win_taocan">选择</a></p><!--新版套餐变成选择-->
                                <p><a href="javascript:void(0);" class="hidden win_pz1a">选择</a></p>
                                <p><a href="javascript:void(0);" class="hidden win_hours">选择</a></p>
                            </td>
                            <td id="element" class="text-left">
                                <div class="input-group col-md-3" id="all_price_td" >
                                    <input type="text" data-parsley-errors-container="#element" onmouseover="this.title=this.value" class="form-control sku_price col-md-3">
                                    <span class="input-group-addon" >元</span>
                                </div>

                            </td>

                            <td id="element" class="text-left">
                                <div class="input-group col-md-3" >
                                    <input type="text" data-parsley-errors-container="#element" class="form-control cost_price col-md-3">

                                    <span class="input-group-addon" >元</span>
                                </div>

                            </td>

                            <td id="element" class="text-left">
                                <div class="col-md-12 p-l-0">
                                    <input type="text" class="form-control sku_stock" data-parsley-required="true" data-parsley-type="integer">
                                </div>
                            </td>

                            <td id="element" class="text-left">
                                <input type="text" class="form-control delivery_coefficient" data-parsley-required="true" data-parsley-type="integer">
                            </td>

                            <td id="element" class="text-left">
                                <div class="input-group col-md-3" >
                                    <input type="text" data-parsley-errors-container="#element" class="form-control tax col-md-3 invoice_rate_0" data-parsley-pattern="/^(0|6|9|11|13|16|17)$/" >
                                </div>

                            </td>


                            <td id="element" class="text-left">
                                <div class="input-group col-md-3" >
                                    <input type="text" data-parsley-errors-container="#element" data-i="0"  is_search="0" class="form-control tax_code col-md-3 invoice_type_0 select_invoice_type_show" data-parsley-pattern="/^\d{19}$/" data-parsley-required="true" >
                                </div>

                            </td>

                            <td id="element" class="text-left dd_commodity_type_7" style="display: none;">
                                <div class="input-group col-md-3" >
                                    <div class="dd_commodity_type_7_1"><span class="card7" data-id="0" style="color: blue;">选择</span></div>
                                    <div class="dd_commodity_type_7_2" style="display: none;"><span class="card7_name" style="color: red;">券名称 </span> <span class="card7" data-id="" style="color: blue;">替换 </span><span class="del_card" onclick="del_card(this)" style="color: blue;">删除</span></div>
                                    <input type="hidden" class="card_id" value="">
                                </div>

                            </td>



                        </tr>
                        </tbody>
                    </table>
                </div>

                <!-- sku-image-->
                <input type="file" id="sku-image-file" name="sku-image" class="hide">
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">商品图片<i class="m-r-3 text-danger">*</i>:<br><span>(用于商品详情的头图)</span></label>

                <div class="col-md-10  image_group">

                    <div class="dndc-upload-pic goods_pic" sp-id="0" data-image-id="0" id="image-id-0">
                        <label class="sp-value m-t-10 m-l-10">默认</label>
                        <label>
                            <a href="javascript:;" class="btn-image btn btn-primary m-r-5">上传图片</a>
                        </label>
                        <label style="top: 80px;" data-toggle="modal" data-target="#jd-img-modal" >
                            <a href="javascript:;"  class="btn-image-jd btn btn-primary m-r-5">京东主图</a>
                        </label>
                        <p style="bottom:5px;">5张,200K,jpg,png</p><ul></ul>
                    </div>
                    <input type="file" name="image" class="hide"  id="goods-image-input">
                </div>


            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品视频</label>
                <div class="col-md-10 ">
                    <input id="file_video" name="file_video" type="file" class="file" data-overwrite-initial="false">
                    <input id="video_img" name="video_img" type="hidden" >
                </div>


            </div>

<!--            <legend class="pull-left width-full m-t-15">商品详情描述</legend>-->
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">商品详情<i class="m-r-3 text-danger">*</i>:</label>-->
<!--                <div class="col-md-9 ">-->
<!--                    <script id="content" name="content" type="text/plain" style="width:100%;height:300px;z-index:999;"></script>-->
<!--                </div>-->
<!--            </div>-->

            <!-- <legend class="pull-left width-full m-t-15">物流信息</legend>
             <div class="form-group">
                 <label class="control-label col-md-3">物流:</label>
                 <div class="col-md-9 ">

                     <div class="col-md-2 p-l-0">
                         <div class="radio">
                             <label>
                                 <input type="radio" checked checked name="is_mail" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="radiorequired" /> 支持快递
                             </label>
                         </div>
                         <div class="radio">
                             <label>
                                 <input type="radio" name="is_mail" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 不支持快递
                             </label>
                         </div>
                     </div>

                     <div class="input-group col-md-2 m-t-10">
                         <input type="text" class="form-control" name="mail_price" placeholder="快递费">
                         <span class="input-group-addon ">元</span>
                     </div>
                 </div>


             </div>-->

            <input type="hidden" name="action" value="add">
            <input type="hidden" name="set_type" value="{$set_type}">

            <div class="text-center m-t-20" style="position: sticky;bottom: 0px;height:80px;background-color: #fff;line-height: 80px;">
<!--                <button type="button" id="put-form-view" class="btn btn-primary m-r-5 m-b-5 btn-sm">预览</button>-->
                <button id="put-form" type="button" class="btn btn-primary m-r-5 m-b-5 btn-sm">下一步</button>
            </div>
        </form>
    </div>
    <!----begin 选择车系---->
    <div class="modal fade" id="car-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择车型</h4>
                </div>

                <div class="modal-body car-series ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <div class="form-group">
                        <div class="col-md-2 p-r-0 checkbox">
                            <label><input type="checkbox" id="check_all" class="car_checkbox ">全选</label>
                        </div>
                        {volist name="brand_s" id="v" key="k"}
                        <div class="col-md-2 p-r-0 checkbox">
                            <label><input type="checkbox"   class="car-brand-type"  data-brand-type="{$k}" >{$v}</label>
                        </div>
                        {/volist}

                    </div>
                    <legend class="pull-left width-full m-t-15"></legend>
                    <?php foreach($car_series_list as $key=>$val):?>
                        <div class=" form-group ">
                            <div class="col-md-2 p-r-0 checkbox">
                                <label><input type="checkbox"  class="car_checkbox " id="{$key}"  data-parent="check_all"> {$val['car_series_name']}</label>
                            </div>

                            <div class="col-md-9 checkbox">
                                <?php foreach($val['sub_data'] as $key1=>$val2):?>
                                    <label class="m-r-5"><input type="checkbox" class="car_checkbox min" data-sub-brand-type="{$val2['brand_type']}" data-parent="{$key}" value="{$key1}" data-car-series="{$val2['sub_car_series_name']}" name="car_series[]">{$val2['sub_car_series_name']}</label>
                                <?php endforeach;?>
                            </div>

                        </div>
                        <legend class="pull-left width-full m-t-15"></legend>
                    <?php endforeach;?>


                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white btn-sm" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary btn-sm"  id="add-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 预览二维码---->
    <div class="modal fade" id="preview-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">扫码二维码预览</h4>
                </div>

                <div class="modal-body qc_code text-center ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>

                    <img src="">

                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white btn-sm" data-dismiss="modal">关闭</a>
                </div>
            </div>
        </div>
    </div>

    <!-- begin 选择优惠券 -->
    <div class="modal fade" id="card-modal">
        <div class="modal-dialog">
            <div class="modal-content" style="width: 800px;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择优惠券</h4>
                </div>
                <div class="modal-body car-series ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <form class="form search-form">
                        <label>优惠券名称：
                            <input type="text" id="card_name" class="form-control input-sm element width-200" placeholder="请输入优惠券名称" aria-controls="data-table" >
                        </label>
                        <button id="card-search" type="button" class=" btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                    </form>
                    <div class="table-scrollable">
                        <table id="card_form" class="table table-bordered">
                            <thead>
                            <th class="text-center">序号</th>
                            <th class="text-center">归属</th>
                            <th class="text-center">优惠券类型</th>
                            <th class="text-center">优惠券id</th>
                            <th class="text-center">优惠券名称</th>
                            <th class="text-center">操作</th>
                            </thead>
                            <tbody id="card-body">
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <ul class="pagination"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="card_add" data-dismiss="modal">确定</a>
                </div>
            </div>
        </div>
    </div>
    <!-- end 选择优惠券-->


    <!-- begin 选择备件 -->
    <div class="modal fade" id="bj-modal">
    </div>
    <!-- end 选择备件-->

    <!-- begin 选择工时 -->
    <div class="modal fade" id="hours-modal">
    </div>
    <!-- end 选择工时-->

    <!-- begin 选择套餐 -->
    <div class="modal fade" id="taocan-modal">
    </div>
    <!-- end 选择套餐-->

    <!-- begin 选择pz套餐 -->
    <div class="modal fade" id="pz1a-modal">
    </div>
    <!-- end 选择pz套餐-->

    <!-- begin 选择pz套餐 -->
    <div class="modal fade" id="pz1a-car-modal">
    </div>
    <!-- end 选择pz套餐-->

    <!-- begin 通过备件查看车型 -->
    <div class="modal fade" id="bj-car-modal">
    </div>
    <!-- end 通过备件查看车型-->

    <!----begin 选择到店代金券---->
    <div class="modal fade" id="addcard7Modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="card7_title">选择到店代金券</h4>
                </div>
                <form data-parsley-trigger="change" class="form">
                    <div class="modal-body">
                        <div class="alert alert-danger m-b-8" style="display: none;">
                            <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                            <p></p>
                        </div>
                        <div class="form-group" style="width: 600px">
                            <div style="padding: 2px 0px; width: 210px;float: left;">
                                <label class="control-label col-md-2 m-l-2" style="padding: 2px 0px; width: 55px;">规则名称:</label>
                                <input type="text" class="form-control input-sm element col-md-3 m-r-2 width-150" id="card7_name" name="card7_name" placeholder="请输入优惠券名称">
                            </div>

                            <button id="card7-search" type="button" class="btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                        </div>
                        <p></p>

                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th class="text-center">序号</th>
                                <th class="text-center">归属</th>
                                <th class="text-center">优惠券类型</th>
                                <th class="text-center">优惠券id</th>
                                <th class="text-center">优惠券名称</th>
                                <th class="text-center">操作</th>
                            </tr>
                            </thead>
                            <tbody id="add-card7-tbody">

                            </tbody>
                        </table>
                        <div>
                            <ul class="pagination" id="card7-pagination"></ul>
                        </div>
                    </div>
                    <!--  当前打开弹层的文本框 -->
                    <input type="hidden" name="text_click_id" value="">

                    <div class="modal-footer">
                        <a href="javascript:;" class="btn btn-sm btn-white" id="add-card7-no" data-dismiss="modal">取消</a>
                        <a href="javascript:;" class="btn btn-sm btn-primary" id="add-card7-ok" data-dismiss="modal">确定</a>
                    </div>

                </form>
            </div>
        </div>
    </div>
    <!----end 选择到店代金券---->
<!--税收分类编码 start----->
    <div class="modal fade" id="invoice-type-modal" data-comm-id="" data-comm-set-id="" data-type="">
        <div class="modal-dialog" style="width: 1000px">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <form class="form search-form" action="{:url('Invoice_type/getTypeList')}">
                        <label>税收分类编码:
                            <input type="text" id="tax_code" name="tax_code" value="" class="form-control input-sm element width-200" placeholder="请输入税收分类编码" aria-controls="data-table" >
                        </label>
                        <label>税收分类名称:
                            <input type="text" id="tax_name" name="tax_name" value="" class="form-control input-sm element width-200" placeholder="请输入税收分类名称" aria-controls="data-table" >
                        </label>
                        <label>税收分类简称:
                            <input type="text" id="short_name" name="short_name" value="" class="form-control input-sm element width-200" placeholder="请输入税收分类简称" aria-controls="data-table" >
                        </label>
                        <button  type="button" class=" btn btn-sm btn-success select_invoice_type_show" is_search="1" ><i class="fa fa-search"></i>搜索</button>
                    </form>
                </div>
                <input id="modal_com_type_id" type="hidden" value="" />
                <div class="modal-body" style="">
                    <div class="hide">
                        <div class="sku-image" style="display: inline-block;">
                            <img class=" cover-image" src="">
                        </div>
                        <div style="display: inline-block;">
                            <div  class="sku-comm"></div>
                        </div>
                    </div>
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th class="text-center">序号</th>
                            <th class="text-center">税收分类编码</th>
                            <th class="text-center">税收分类名称</th>
                            <th class="text-center">简称</th>
                            <th class="text-center">税率</th>
                            <th class="text-center">是否可用</th>
                            <th class="text-center">说明</th>
                            <th class="text-center">操作</th>
                        </tr>
                        </thead>
                        <tbody class="invoice-type-tb"></tbody>
                    </table>
                </div>
                <table><tr><td  style="padding-left: 60px">共搜索到<span id="invoice_type_count"></span>条数据</td><td><div id="invoice_type_page" ></div></td></tr></table>

                <!--                <div class="modal-footer">-->
<!--                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >关闭</a>-->
<!--                </div>-->
            </div>
        </div>
    </div>
    <!--税收分类编码 end----->

    <!----begin 京东显示图片---->
    <div class="modal fade in" id="jd-img-modal">
            <div class="modal-dialog" style="width: 45%">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        <h4 class="modal-title">选择京东主图</h4>
                    </div>

                    <div class="modal-body car-series ">
                        <div class="alert alert-danger m-b-8" style="display: none;">
                            <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                            <p></p>
                        </div>
                        <form class="form search-form">
                            <label>JD商品sku:
                                <input type="text" id="sku_id" value="" class="form-control input-sm element width-200" placeholder="jd商品sku" aria-controls="data-table" >
                            </label>
                            <button type="button" class=" btn btn-sm btn-success select_invoice_type"><i class="fa fa-search"></i>搜索</button>
                        </form>
                        <legend class="pull-left width-full m-t-15"></legend>
                        <div class="table-scrollable">
                            <table id="card_form" class="table table-bordered">
                                <thead>
                                    <th class="text-center">JD图片预览</th>
                                    <th class="text-center">JD是否可用</th>
                                    <th class="text-center">JD是否主图</th>
                                    <th class="text-center">JD排序</th>
                                    <th class="text-center">JD位置</th>
                                    <th class="text-center">JD更新时间</th>
                                </thead>
                                <tbody id="jd-body">
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="javascript:;" class="btn btn-sm btn-white btn-sm" data-dismiss="modal">确认</a>
<!--                        <a href="javascript:;" class="btn btn-sm btn-primary btn-sm" data-dismiss="modal"">确定</a>-->
                    </div>
                </div>
            </div>
        </div>
    <!----end 京东显示图片---->
</div>
{/block}
{block name="script"/}

<script type="text/javascript" src="__STATIC__admin_v2/js/jquery.page.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/plugins/layui_v2.5.7/layui.js"></script>
<script src="__STATIC__admin_v2/js/purify.min.js"></script>
<script src="__STATIC__admin_v2/js/sortable.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput_locale_zh.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js?v=2.1.11"></script>
<!--<script src="__STATIC__admin_v2/js/commodity.js?v=--><?php //echo time(); ?><!--"></script>-->


<script>
    layui.use('element', function(){
        var $ = layui.jquery
            ,element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
        //触发事件
        var active = {
            tabAdd: function(){
                //新增一个Tab项
                element.tabAdd('demo', {
                    title: '新选项'+ (Math.random()*1000|0) //用于演示
                    ,content: '内容'+ (Math.random()*1000|0)
                    ,id: new Date().getTime() //实际使用一般是规定好的id，这里以时间戳模拟下
                })
            }
            ,tabDelete: function(othis){
                //删除指定Tab项
                element.tabDelete('demo', '44'); //删除：“商品管理”


                othis.addClass('layui-btn-disabled');
            }
            ,tabChange: function(){
                //切换到指定Tab项
                element.tabChange('demo', '22'); //切换到：用户管理
            }
        };

        $('.site-demo-active').on('click', function(){
            var othis = $(this), type = othis.data('type');
            active[type] ? active[type].call(this, othis) : '';
        });

        //Hash地址的定位
        var layid = location.hash.replace(/^#test=/, '');
        element.tabChange('test', layid);

        element.on('tab(test)', function(elem){
            location.hash = 'test='+ $(this).attr('lay-id');
        });

    });
    var url_select_invoice_type="{:url('Invoice_type/getTypeList?pagesize=1000000&status=1')}";
    var url_get_sku_table="{:url('getSkuTable')}";
    var url_get_bj_table="{:url('getBjTable')}";
    var url_get_bj_car_table="{:url('getBjCarTable')}";
    var url_get_hours_table="{:url('getHoursTable')}";
    var url_get_pz1a_table="{:url('getPz1aTable')}";
    var url_get_pz1a_car_table="{:url('getPz1aCarTable')}";

    var ajaxGetCard_url  ="{:url('ajaxLiveGetCard')}";
    var tips_url  ="{:url('getClassTips')}";
    var jd_sku_img  ="{:url('jd_sku_img')}";
    var ajaxGetBj_url  ="{:url('e3sSparePart/ajaxGetBj')}";
    var ajaxGetBjCar_url  ="{:url('e3sSparePart/select_car_series')}";
    var ajaxGetHours_url = "{:url('e3sPartTime/ajaxGetHours')}";
    var ajaxGetPz1a_url = "{:url('e3sPz1a/ajaxGetSetMeal')}"
    var ajaxGetPz1aCar_url = "{:url('e3sPz1a/ajaxGetPz1aCar')}"
    var validate_spec_val  ="{:url('validateSpec')}";
    var ajaxcard7Url = ajaxGetCard_url + '?pagesize=10';      //列表
    var ajaxGetTaoCan_url = "{:url('e3sSetMeal/ajaxGetTaoCan')}";//获取套餐列表

    $(function(){


        //初始化
        $('.goods_pic>ul').sortable({
            disabled: true,
        });
        localStorage.clear()
        batchIni();

        $("#put-form,#put-form-view").on('click',function(){
            var sku_list  =getSkuList();
            var image_list=getImageList();
            var $form=$("#add-step-2");
            var validate=$form.psly().validate();  //表单验证
            if(!validate) return false;
            var form_data=$form.serialize();
            var dd_commodity_type = $("[name='dd_commodity_type']:checked").val();
            if(dd_commodity_type == 9){
                var is_true = false;
                $("#element .sku_code").each(function(){
                    if($("#sku_code input").val() == ''){
                        is_true = true
                        return false;
                    }
                })
                if(is_true){
                    layer.msg('sku规格不能为空');
                    return false
                }
            }
            console.log(form_data);

            if($(this).attr("id")=='put-form-view'){
                var is_preview=1;
            }else {
                var is_preview=0;
            }
            if (image_list.length<=0){
                layer.msg('默认图片不能为空');
                return false;
            }
            var is_cost_price = false;
            if(dd_commodity_type === 0){
                $.each(sku_list,function(k,v){
                    if (!(/^[0-9/.]*$/).test(v.cost_price)) {
                        is_cost_price = true;
                        sku_list[k]['cost_price'] = '';
                    }
                })
            }

            var alert_obj=$("#alert-danger");
            var dd_commodity_type = $("[name='dd_commodity_type']:checked").val();
            if(dd_commodity_type == 1 || dd_commodity_type == 3 || dd_commodity_type == 4 || dd_commodity_type == 12){
                var data=form_data + "&" + $.param({is_preview:is_preview,car_series_id:car_series_id,'sku_list':JSON.stringify(sku_list),'image_list':image_list})
            }else{
                var data=form_data + "&" + $.param({is_preview:is_preview,car_series_id:car_series_id,'sku_list':sku_list,'image_list':image_list})
            }
            if(is_cost_price){
                var cost_price_is =  layer.msg("有sku成本价未填写或格式不正确，是否确认进行下一步编辑？", {
                    icon: 0
                    , time: false
                    , btn: ['下一步', '取消'] //按钮
                    , yes: function () {
                        layer.close(cost_price_is);
                        if (is_preview) {
                            Custom.ajaxPost("{:url('save')}", data, null, null, function (res) {
                                if (res.error == 0) {
                                    var url = res.data.com_url;
                                    console.log(url);

                                    //提交成功删除本地缓存 start
                                    localStorage.clear()
                                    //提交成功删除本地缓存 end

                                    $("#mod-mobile-pop").find("#output").attr("src", url);
                                    $("#mod-mobile-pop").show();
                                    /*  $('#output').empty();
                                      $('#output').qrcode(url);
                                      $("#commodity-qrc-modal").modal("show");*/
                                } else {
                                    layer.msg('预览失败');
                                }
                            })
                        } else {
                            Custom.ajaxPost("{:url('save')}", data, null, null, function (res) {
                                if (res.error == 0) {
                                    location.href = "{:url('successStep3')}?is_ok=0&commodity_id=" + res.data.id + "&is_grouped=0";
                                } else {
                                    layer.msg('保存失败');
                                }
                            })
                        }
                        return true;
                    }, btn2: function () {
                        layer.close(cost_price_is);
                        return false;
                    }
                })
            }else{
                if (is_preview){
                    Custom.ajaxPost("{:url('save')}",data,null,null,function(res){
                        if (res.error==0){
                            var url=res.data.com_url;
                            console.log(url);

                            //提交成功删除本地缓存 start
                            localStorage.clear()
                            //提交成功删除本地缓存 end

                            $("#mod-mobile-pop").find("#output").attr("src",url);
                            $("#mod-mobile-pop").show();
                            /*  $('#output').empty();
                              $('#output').qrcode(url);
                              $("#commodity-qrc-modal").modal("show");*/
                        }else {
                            layer.msg('预览失败');
                        }
                    })
                }else {
                    Custom.ajaxPost("{:url('save')}",data,null,null,function(res){
                        if (res.error==0){
                            location.href="{:url('successStep3')}?is_ok=0&commodity_id="+res.data.id+"&is_grouped=0";
                        }else {
                            layer.msg('保存失败');
                        }
                    })
                }
            }
        });
    });

    $("input[name='dd_commodity_type']").on('change', function (){
        console.log('dd_commodity_type:',$(this).val())
        $("#zhe_kou_1").show();
        $("#zhe_kou_2").show();
        if($(this).val() == 9){
            //备件
            var th = '';
            th += '<div data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></div>'
            $("#all_price_th").html(th)

            $(".win_beijian").attr('style','display:block!important');
            $(".win_taocan").attr('style','display:none!important');
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:none!important');

            $("#sku_code input").attr('readonly','readonly')
            $("#all_price_td input").attr('readonly','readonly')
            $('#wi_business_type').show()
            var html = '';
            html += '<input onmouseover="this.title=this.value" readonly="readonly" type="text" data-parsley-errors-container="#element" class="form-control sku_price col-md-3 sku_price2">'
            html += '<span class="input-group-addon">元</span>'
            $("#all_price_td").html(html)
            $('.dd_commodity_type_7').hide();
        }else if($(this).val() == 7 || $(this).val() == 8){
            //代金券
            $('.dd_commodity_type_7').find('.editable-click').text('关联到店代金券');
            if ($(this).val() == 8){
                $('.dd_commodity_type_7').find('.editable-click').text('关联到店电子券');
            }
            $('.dd_commodity_type_7').show();
            ajaxcard7Url += "&card_type=6";
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:none!important');
            $(".win_new_taocan").attr('style','display:none!important');
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:none!important');
            $('#wi_business_type').hide()
            $('#shelves_sources_check_5').removeAttr('checked');
            $('#shelves_sources_check_6').removeAttr('checked');
            $('#shelves_sources_check_7').removeAttr('checked');
            $('#shelves_sources_check_8').removeAttr('checked');
        }else if($(this).val() == 10){
            //保养套餐-ARIYA
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:none!important');
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:block!important');
            $("#sku_code input").attr('readonly','readonly')
            $("#all_price_td input").attr('readonly','readonly')
            $('#wi_business_type').hide()
            $('.dd_commodity_type_7').hide();
            console.log('123123-------------')
        } else if($(this).val() == 11){
            //到店工时
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:none!important');
            $(".win_hours").attr('style','display:block!important');
            $(".win_pz1a").attr('style','display:none!important');
            $("#sku_code input").attr('readonly','readonly')
            $('.dd_commodity_type_7').hide();
            $('#wi_business_type').show()
            console.log('123123-------------')
        }else if($(this).val() == 1 || $(this).val() == 3 || $(this).val() == 12){

            if ($(this).val() == 12) {
                // 保养套餐-五年双保专享心悦套餐
                console.log('保养套餐-五年双保专享心悦套餐');
                // 套餐折扣只保留 2次(7折)
                $("#zhe_kou_1").hide();
                $("#zhe_kou_2").hide();
            }

            //显示城市级别
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:block!important');
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:none!important');
            $("#sku_code input").removeAttr('readonly')
            $('#wi_business_type').hide() //不显示工时业务类别选择
            $("#commodity_type_none").show() //显示地区
            $("#commodity_type_none_one").hide() //显示地区
            $("#sku_code input").attr('readonly','readonly')
        }else if($(this).val() == 4){
            //显示城市级别
            console.log("双保")
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:block!important');
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:none!important');
            $("#sku_code input").removeAttr('readonly')
            $('#wi_business_type').hide() //不显示工时业务类别选择
            $("#commodity_type_none").hide() //显示地区
            $("#commodity_type_none_one").show() //显示地区
            $("#sku_code input").attr('readonly','readonly')
        } else{
            var th = '';
            th += '<a href="#" id="all_price_th_a" data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></a>'
            console.log(th)
            $("#all_price_th").html(th)
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:none!important');
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:none!important');
            $("#sku_code input").removeAttr('readonly')
            $("#all_price_td input").removeAttr('readonly')
            $("#commodity_type_none").hide() //显示地区
            $("#commodity_type_none_one").hide() //显示地区
            $('#wi_business_type').hide()
            var html = '';
            html += '<input onmouseover="this.title=this.value" type="text" data-parsley-errors-container="#element" class="form-control sku_price col-md-3 sku_price2" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'
            html += '<span class="input-group-addon" >元</span>'
            $("#all_price_td").html(html)
            $('.dd_commodity_type_7').hide();

        }
        if ($("input[name='commodity_class']:checked").val() == 6){
            $("input[name='commodity_class']:checked").removeAttr('checked');
        }
    });


    $('.card7').on('click', function (){
        $('#addcard7Modal').modal('show');
    });

    //异步请求数据地址
    var obj;
    //操作类型 显示弹层
    $('.card7').live('click',function(){
        obj = $(this);
        //初始化分页控件
        var shelves_sources = getShelvesSources();
        initcard7(ajaxcard7Url + '&shelves_sources='+shelves_sources);
        var data_id = $(this).attr('data-id');
        var text_click_id = $(this).attr('id');
        $('#addcard7Modal').find("[name='text_click_id']").val(text_click_id);
        $('#addcard7Modal').find("[name='text_click_id']").attr('data-id',data_id);
        $('#addcard7Modal').find("[name='text_click_id']").attr('page');
        $('#addcard7Modal').modal('show');
    });

    function initcard7(url) {
        $.getJSON(url, null, function (resData) {
            createPagecard7(10, 10, resData.data.total, url);//创建翻页功能按钮，翻
            $("#add-card7-tbody").empty();
            if (resData.data.total > 0) {                          //页向后台请求连接
                setcard7(url);
            }
        });
    }

    function createPagecard7(pageSize, buttons, total, url) {        //contracts_url为点击
        var shelves_sources = getShelvesSources();
        $("#card7-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
            pageSize : pageSize,
            total : total,
            maxPageButton:buttons,
            onPageClicked: function(obj, page) {    //分页事件
                if(isNaN(page)){ console.log(page);initcard7(ajaxcard7Url + '&shelves_sources='+shelves_sources);return false; }
                $("#add-card7-tbody").empty();
                setcard7(url+"&page="+(page+1));
                $('#addcard7Modal').find("[name='text_click_id']").attr('page',page);    //记住当前页码
            }
        });
    }
    //获取并设置列表
    function setcard7(param){
        var shelves_sources = getShelvesSources();
        var url = ajaxcard7Url + '&shelves_sources='+shelves_sources;
        if(param!=null) url=param;
        $.get(url,function(res){
            var html ='';
            var list = res.data.data;
            console.log(list)
            $.each(list,function(i,val){
                var checkbox_obj = '';
                var check = '';
                var card7_id = obj.attr('data-id');
                if (card7_id == val.id){
                    check = 'checked';
                }
                checkbox_obj = '<label> <input type="checkbox" '+check+' class="card7_checkbox" value="'+val.id+'"><input type="hidden" value="'+val.card_name+'"> </label>';
                html += '<tr id="card7_tab_tr_'+val.id+'">' +
                    '<td>'+ (i+1) +'</td>' + // 序号
                    '<td>'+ val.belong_to +'</td>' + // 归属
                    '<td>'+ val.card_type_name +'</td>' +
                    '<td>'+ val.card_id +'</td>' +
                    '<td>'+ val.card_name +'</td>' +
                    '<td class="text-center">' + checkbox_obj +
                    '</td> </tr>';
            });
            $("#add-card7-tbody").html(html);

        },'json');
    }
    //搜索
    $("#card7-search").on('click',function(){
        var obj = $(this).parent();
        var card7_name = obj.find("#card7_name").val();
        var shelves_sources = getShelvesSources();
        var param = ajaxcard7Url + '&card_name='+card7_name + '&shelves_sources='+shelves_sources;
        initcard7(param);
    });

    //复选框 只能单选
    $("body").on('click','#add-card7-tbody .card7_checkbox',function(){
        var card7_id = $(this).val();
        if($(this).is(':checked')){
            obj.parent().parent().find('.card_id').val(card7_id);
            if (obj.attr('data-id') == 0) {
                obj.parent().next().find('.card7_name').text($(this).next().val());
                obj.parent().next().find('.card7').attr('data-id', card7_id);
            }else{
                obj.prev().text($(this).next().val());
                obj.attr('data-id', card7_id);
            }
            obj.parent().parent().find('.dd_commodity_type_7_1').hide();
            obj.parent().parent().find('.dd_commodity_type_7_2').show();
            $(this).closest('#add-card7-tbody').find("input.card7_checkbox").each(function(){
                if($(this).val()!=card7_id){
                    $(this).removeAttr('checked');
                }
            });
        }else{
            obj.parent().parent().find('.dd_commodity_type_7_2').hide();
            obj.parent().parent().find('.dd_commodity_type_7_1').show();
            obj.parent().parent().find('.card_id').val('');
            if (obj.attr('data-id') == 0) {
                obj.parent().next().find('.card7').attr('data-id', 0);
            }else{
                obj.attr('data-id', 0);
            }
        }
    });

    function on_integral(e){
        console.log(e.checked)
        if(e.checked == true){
            $(".is_integral_list").css('display','block')
        }else{
            $(".is_integral_list").css('display','none')
        }
    }

    function del_card(obj){
        $(obj).parent().next().val('');
        $(obj).prev().attr('data-id', 0);
        $(obj).parent().parent().find('.dd_commodity_type_7_2').hide();
        $(obj).parent().parent().find('.dd_commodity_type_7_1').show();
    }

    var user_data = [];
    // $.ajaxSettings.async = true;  //设为同步请求
    $.get("{:url('e3s_specific_relation/e3s_spec_part_group')}",function (res){
        var array = JSON.parse(res)
        $.each(array.data,function (i,item){
            user_data[i] = [];
            user_data[i]['name'] = item.spec_part_group
            user_data[i]['value'] = item.spec_part_group_id
        })
    })

</script>

{/block}
