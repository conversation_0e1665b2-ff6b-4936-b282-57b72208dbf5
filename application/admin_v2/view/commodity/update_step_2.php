{extend name="public:base_layout" /}

{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<link href="__STATIC__admin_v2/plugins/layui_v2.5.7/css/layui.css" rel="stylesheet"/>
<link rel="stylesheet" href="__STATIC__admin_v2/css/jquery.page.css">
<style>
    .file-drop-zone-title {
        color: #aaa;
        font-size: 20px;
        padding: 25px 15px;
    }
    .kv-file-zoom,.file-footer-buttons, .file-drag-handle, .file-upload-indicator  {
        display: none;
    }
    .progress{
        display: none;
    }
    .layui-tab-title .layui-this{
        background-color: #348fe2;
        color: #FFF;
    }
    .layui-tab-title li{
        border-top:0.5px solid #ccc;
        border-right:0.5px solid #ccc;
    }
    .layui-tab-title li:first-child{
        border-left:0.5px solid #ccc;
    }
</style>
{/block}

{block name="content"/}

<div class="panel-body">
    {include file="commodity:commodity_nav" /}
    <div id="alert-danger" class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">商品基本信息</legend>
        <form id="add-step-2" class="form-horizontal form-bordered" data-parsley-trigger="change">
            <div class="form-group">
                <label class="control-label col-md-2">商品分类<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">


                    {$commodity_type['pparent_type_name']} > {$commodity_type['parent_type_name']} > {$commodity_type['comm_type_name']}
                    <a href="{:url('addStep1')}?commodity_id={$commodity_id}&is_grouped={$is_grouped}" class="btn btn-white btn-sm m-r-5 m-b-5">选择分类</a>
                    <input type="hidden" name="comm_type_id" value="{$commodity_type['id']}">

                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" name="commodity_name" placeholder="请输入商品单位" value="{$row['commodity_name']}" class="form-control " data-parsley-required="true" data-parsley-length="[3, 50]">
                    <p class="m-t-5">商品标题名称长度至少3个字符，最长50个汉字</p>
                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">到店商品类型:</label>
                <div class="col-md-10">
                    <input type="hidden" class="is_show_change" name="dd_commodity_type" value="{$row['dd_commodity_type']}">
                    <!--到店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-五年双保升级权益套餐 6保养套餐-其他 7到店代金券-->
                    {foreach ($dd_commodity_type as $k => $dd_type)}
                    <label class="radio-inline">
                        <input type="radio"  name="dd_commodity_type" <?php if($row['dd_commodity_type']==$k) echo 'checked';?> value="{$k}" data-parsley-required="true" data-parsley-multiple="radiorequired" />
                        {$dd_type}
                    </label>
                    {/foreach}
                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">收录推荐系统:</label>
                <div class="col-md-10">
                    <!--0否1是-->
                    <label class="radio-inline">
                        <input type="radio"  name="arrival_bdp" <?php if($row['arrival_bdp']==0) echo 'checked';?> value="0" data-parsley-required="true" data-parsley-multiple="radiorequired" /> 否
                    </label>
                    <label class="radio-inline">
                        <input type="radio"  name="arrival_bdp" <?php if($row['arrival_bdp']==1) echo 'checked';?> value="1" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 是
                    </label>
                    <p class="m-t-5">部分商品不能推给推荐系统，因为是组合销售商品，如车联保养套餐</p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">机油油型:</label>
                <div class="col-md-5">
                    <select id="machine_oil_type" class="form-control width-300 is_show_change" name="machine_oil_type">
                        <option value="0">请选择</option>
                        <option value="1" <?php if ($row['machine_oil_type'] == 1) echo 'selected'?>>1L</option>
                        <option value="4" <?php if ($row['machine_oil_type'] == 4) echo 'selected'?>>4L</option>
                    </select>
                </div>
                <p class="m-t-5"></p>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">品牌:</label>
                <div class="col-md-5">
                    <select name="brands_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id">
                        <option value="">
                            请选择
                        </option>
                        <?php foreach($brands_list as $key=>$val):?>
                            <option value="{$val['id']}" <?php if ($val['id'] == $row['brands_id']) echo 'selected'?>>{$val['brands_name']}</option>
                        <?php endforeach;?>
                    </select>
                </div>
            </div>

            <div class="form-group machine_oil_type" {if ($row['machine_oil_type'] == 0)}style='display:none;'{/if}>
                <label class="control-label col-md-2">油型:</label>
                <div class="col-md-5">
                    <input type="text" name="machine_oil_value" value="{$row['machine_oil_type']}L"  class="form-control " readonly data-parsley-length="[1, 200]">
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品编码:</label>
                <div class="col-md-5">
                    <input type="text" name="commodity_code" value="{$row['commodity_code']}" placeholder="请输入商品编码" class="form-control "  data-parsley-length="[1, 200]">

                </div>
            </div>
            <div class="form-group" style="display: none">
                <label class="control-label col-md-2">价格范围<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">

                    <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">原价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " value="" name="original_price_range_start" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly="readonly">

                    </div>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " value="" name="original_price_range_end" placeholder="最高价格"  data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly="readonly">
                    </div>

                    <label class="control-label col-md-2 m-r-0 text-right">现价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " value="" name="discount_price_range_start" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/"  readonly="readonly" >

                    </div>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " value="" name="discount_price_range_end" placeholder="最高价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly="readonly">
                    </div>

                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2 ">商品特性<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-5 p-l-0">
                        <label class="control-label col-md-4 width-100 m-r-0 p-l-0 text-left ">是否纯正商品<i class="m-r-3 text-danger">*</i>:</label>
                        <label class="radio-inline">
                            <input type="radio"   <?php if($row['is_pure']==1) echo 'checked';?> name="is_pure" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="radiorequired" class="is_show_change"/> 是
                        </label>

                        <label class="radio-inline">
                            <input type="radio"  <?php if($row['is_pure']==0) echo 'checked';?> name="is_pure" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="radiorequired" class="is_show_change"/> 否
                        </label>
                    </div>
                    <div class="col-md-5 p-l-0">
                        <label class="control-label col-md-4 width-100 m-r-0 p-l-0 text-left">是否商城商品<i class="m-r-3 text-danger">*</i>:</label>


                        <label class="radio-inline">
                            <input type="radio" <?php if($row['is_shop']==1) echo 'checked';?> name="is_shop" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="is_shop" class="is_show_change" /> 是
                        </label>


                        <label class="radio-inline">
                            <input type="radio" <?php if($row['is_shop']==0) echo 'checked';?> name="is_shop" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="is_shop" class="is_show_change"/> 否
                        </label>

                    </div>

                    <?php if ($admin_type==1):?>

                    <div class="col-md-7 p-l-0">
                        <label class="control-label col-md-4 width-80 m-r-0 p-l-0 text-left">上架源<i class="m-r-3 text-danger">*</i>:</label>
                        <?php foreach ($shelves_sources as $key=>$val):?>
                            <label class="checkbox-inline">
                                <input type="checkbox" class="shelves_sources_check" id="shelves_sources_check_{$key}" name="shelves_sources[]" value="{$key}" <?php if (in_array($key,explode(',',$row['shelves_sources']))) echo 'checked';?> >{$val}
                            </label>
                        <?php endforeach;?>
                    </div>

                    <?php endif;?>



                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">商品种类:</label>
                <div class="col-md-5 ">
                    <?php foreach ($commodity_class as $key=>$val):?>

                       <label class="radio-inline">
                          <input type="radio" disabled="disabled"  name="commodity_class" value="{$key}" <?php if ($key==$row['commodity_class']) echo 'checked';?> id="radio-required" data-parsley-required="true" data-parsley-multiple="commodtiy_class" /> {$val}
                       </label>
                    <?php endforeach;?>

                    <input type="hidden" name="commodity_class" value="{$row['commodity_class']}">
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">订单提示:</label>
                <div class="col-md-5 ">
                    <textarea  class="form-control letter-count" placeholder="订单提示" rows="5" name="tips" id="tips" maxlength="50" title="字数不能超过50个。">{$row['tips']}</textarea>
                </div>

            </div>


            <?php if ($row['commodity_class']==3) :?>
            <div class="form-group" id="commodity_card_ids" >
                <label class="control-label col-md-2">卡券:</label>
                <div class="col-md-5">

                    <input type="text" class="form-control is_show_change" value="{$row['commodity_card_name']}" onkeydown="onlyView();" name="commodity_card_ids_name" placeholder="选择优惠券" >


                    <input type="hidden" class="form-control hide_card_ids" name="commodity_card_ids" >
                </div>
            </div>
            <?php endif;?>
            <?php if ($row['commodity_class']==5) :?>
            <div class="form-group" id="mark_coupon_id">
                <label class="control-label col-md-2">平台卡券id<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control is_show_change" name="mark_coupon_id" placeholder="请输入平台卡券id" value="{$row['commodity_card_ids']}">
                </div>
            </div>
            <?php endif;?>
            <div class="form-group">
                <label class="control-label col-md-2">商品单位<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control "  value="{$row['unit']}" name="unit" placeholder="商品单位" data-parsley-required="true"  data-parsley-length="[0,4]">
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">供应商渠道:{$row.supplier}</label>
                <div class="col-md-5">
                    <select id="template_guid" class="form-control width-300" name="supplier">
                        <option value="">请选择</option>
                        <?php foreach ($supp_list as $k => $vo): ?>
                            <option value="{$vo['value_code']}"
                                    <?php if($row['supplier']===$vo['value_code']):?>
                                        selected="selected"
                                    <?php endif; ?>
                                    >
                                {$vo['county_name']}
                            </option>
                        <?php endforeach;?>
                    </select>
                </div>
            </div>



            <div class="form-group">
                <label class="control-label col-md-2">排序<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control " value="{$row['sort']}" name="sort" placeholder="请输入排序" data-parsley-required="true" data-parsley-range="[0,10000]">
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">积分兑换专区:</label>
                <div class="col-md-5">
                    <label class="checkbox-inline">
                        <input type="checkbox" class="is_integral is_show_change" onclick="on_integral(this)" id="is_integral" value="1" name="is_integral_shop" {if condition="$row['is_integral_shop'] eq 1" }checked{/if}>是否积分兑换
                    </label>
                    <label class="checkbox-inline">
                        <input type="checkbox" class="is_integral is_show_change"  id="" value="2" name="is_integral_shop" {if condition="$row['is_integral_shop'] eq 2" }checked{/if}>是否移动积分兑换
                    </label>
                </div>
            </div>
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">适用车型:</label>-->
<!--                <div class="col-md-5">-->
<!--                    <input type="text" data-toggle="modal" value="{$row['sort']}" class="form-control " name="car_series_name" placeholder="选择车型" data-target="#car-modal">-->
<!--                </div>-->
<!--            </div>-->

            <legend class="pull-left width-full">商品规格及图片</legend>
            <div class="form-group">
                <label class="control-label col-md-2">商品规格:</label>
                <div class="col-md-10 sp">
                    <div class="col-md-12">
                        <span class="m-r-20">规格</span>规格值（通过选择规格值建立商品与商品规格的关系，最多只能选择3种规格）
                    </div>
                    <legend class="pull-left width-full"></legend>
                    <ul class="col-md-12 checkbox <?php if(!in_array($row['dd_commodity_type'],[1,3,12])){echo 'hidden';} ?>" id="commodity_type_none">
                        <li>
                            <span class="m-r-20 sp-name">地区</span>
                            <div>
                                {volist name="tabs_list" id="v" key="k"}
                                    <label class=" m-r-5 sp-value">
                                    <input type="checkbox" {in name="key" value="$tab"}checked{/in}  class="sku_check_area" sp-value-name="{$v}"  value="{$key}" name="area_value[]" >{$v}</label>
                                {/volist}
                            </div>
                        </li>
                    </ul>
                    <ul class="col-md-12 checkbox <?php if(!in_array($row['dd_commodity_type'],[4])){echo 'hidden';} ?>" id="commodity_type_none_one">
                        <li>
                            <span class="m-r-20 sp-name">升级类型</span>
                            <div>
                                <label class=" m-r-5 sp-value">
                                    <input type="checkbox"  class="sku_check_area_type" sp-value-name="0-普通" {in name="0" value="$select_tab"}checked{/in} value="0" name="area_value[]" >0-普通</label>
                                <label class=" m-r-5 sp-value">
                                    <input type="checkbox"  class="sku_check_area_type" sp-value-name="1-全合成" {in name="1" value="$select_tab"}checked{/in} value="1" name="area_value[]" >1-全合成</label>
                            </div>
                        </li>
                    </ul>
                    <ul class="col-md-12 checkbox ">
                        <?php foreach($is_integral as $key=>$val):?>
                            <li class="is_integral_list"  {if condition="$row['is_integral_shop'] eq 1" }style="display: display"{else /}style="display: none"{/if}>
                            <span class="m-r-20  sp-name" sp-id="{$key}" sp-name="{$val['sp_name']}">{$val['sp_name']}</span>
                            <div sp-id="{$key}">
                                <?php foreach($val['sp_value'] as $key1=>$val1):?>
                                    <label class=" m-r-5 sp-vlaue is_show_change"><input type="checkbox" sp-index="{$key}" sp-value-name="{$val1['sp_value_name']}" sp-id="{$val1['sp_id']}" class="sku_ckeck"  value="{$val1['id']}" <?php if(isset($sp_list[$val1['id']])) echo 'checked';?> name="sp_value_id[]" >{$val1['sp_value_name']}</label>
                                <?php endforeach;?>
                            </div>
                            </li>
                            <legend class="pull-left width-full is_integral_list"></legend>
                        <?php endforeach;?>
                        <?php foreach($sp_value as $key=>$val):?>
                            <li>
                                <span class="m-r-20 sp-name" sp-id="{$key}" sp-name="{$val['sp_name']}">{$val['sp_name']}</span>
                                <div sp-id="{$key}">
                                    <?php foreach($val['sp_value'] as $key1=>$val1):?>
                                        <label class=" m-r-5 sp-vlaue is_show_change"
                                            <?php if ($val['sp_name'] == '套餐折扣'):?>
                                                id="zhe_kou_{$key1}"
                                            <?php endif;?>
                                        ><input type="checkbox" <?php if(isset($sp_list[$val1['id']])) echo 'checked';?> sp-index="{$key}" sp-value-name="{$val1['sp_value_name']}" sp-id="{$val1['sp_id']}" class="sku_ckeck"  value="{$val1['id']}" name="sp_value_id[]" >{$val1['sp_value_name']}</label>
                                    <?php endforeach;?>
                                </div>
                            </li>

                            <legend class="pull-left width-full"></legend>
                        <?php endforeach;?>
                    </ul>
                    <div class="col-md-12">
                        可勾选商品对应的规格及规格值，当勾选两种不同规格的规格值后将组合成一种商品SKU，可从下方表格中进行具体设定。<br>
                        规格最多不超过3组。
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品价格及库存<i class="m-r-3 text-danger">*</i>:</label>
                <div id="sku" class="col-md-10 ">
                    {in name="$row['dd_commodity_type']" value="1,3,4,12"}
                    <div class="layui-tab">
                        <ul class="layui-tab-title">
                            {volist name="tab_data" key="key" id="val"}
                            <li class="{eq name="$key" value="1"} layui-this {/eq}">{$val.name}</li>
                            {/volist}
                        </ul>
                        <div class="layui-tab-content">
                            {volist name="tab_data" key="keys" id="val"}

                            <div class="layui-tab-item {eq name='$keys' value='1'} layui-show {/eq} {$val.id}" >
                                <table class="table table-bordered price-stock">
                                    <thead>
                                    <tr class="text-center">
                                        {volist name="sp_title" key="k" id="v"}
                                        <th sp-title="">{$v}</th>
                                        {/volist}
                                        <th>规格图片</th>
                                        <th>规格编码</th>
                                        <th id="all_price_th">
                                            {eq name="$keys" value="1"}
                                            <a href="#" id="all_price" data-type="text" class="btn btn-primary btn-sm all_price" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></a>
                                            {else /}
                                                价格
                                            {/eq}

                                        </th>
                                        <th>
                                            {eq name="$keys" value="1"}
                                            <a href="#" id="all_cost_price" data-type="text" class="btn btn-primary btn-sm editable editable-click all_cost_price" data-value="" data-placeholder="成本价" data-title="批量设置成本价">成本价<i class="fa fa-edit m-l-5"></i></a>
                                            {else /}
                                            成本价
                                            {/eq}

                                        </th>
                                        <th>
                                            {eq name="$keys" value="1"}
                                            <a href="#" id="all_stock" data-type="text" class="btn btn-primary btn-sm editable editable-click all_stock" data-value="" data-placeholder="请输入库存" data-title="批量设置库存">库存<i class="fa fa-edit m-l-5"></i></a>
                                            {else /}
                                            库存
                                            {/eq}
                                        </th>
                                        <th>
                                            {eq name="$keys" value="1"}
                                            <a href="#" id="all_tax" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax" data-value="" data-placeholder="税率" data-title="批量设置税率">税率<i class="fa fa-edit m-l-5"></i></a>
                                            {else /}
                                            税率
                                            {/eq}
                                        </th>
                                        <th>
                                            {eq name="$keys" value="1"}
                                            <a href="#" id="all_tax_code" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax_code" data-value="" data-placeholder="税务编码" data-title="批量设置税务编码">税务编码<i class="fa fa-edit m-l-5"></i></a>
                                            {else /}
                                            税务编码443
                                            {/eq}
                                           </th>
                                        <th class="dd_commodity_type_7" {if !in_array($row['dd_commodity_type'], [7,8])}style="display: none;"{/if}><a href="javascript:;" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="税务编码445" data-title="批量设置税务编码">关联代金券</a></th>
                                    </tr>
                                    </thead>
                                    {volist name="sku_list[$val.id]" key="keys" id="val1"}
                                    <tr class="text-center" data-sku-list="{$val1['sp_value_list']}">
                                        <td  id="valid-{$key1}" style="display: none">
                                            <div class="input-group col-md-3" >
                                                <input type="text" class="sku_id" value="{$val1['id']}">
                                            </div>
                                        </td>
                                        <?php if(!empty($val1['sp_value_arr'])):?>
                                            <?php  foreach($val1['sp_value_arr'] as $key2=>$val2):?>
                                                <td data-sp-value-id="{$val2}">{$sp_list[$val2]['sp_value_name']}</td>
                                            <?php endforeach;?>
                                        <?php endif;?>
                                        <input type="hidden" name="city_type" class="city_type" value="{$val.id}">
                                        <td class="text-left belong_code" data-belong_code="{$val.id}">{$val.name}</td>
                                        <td class="text-left sku-image-td">
                                            <div class="parents-add" <?php if(!empty($val1['image'])) echo 'style="display: none;"';?>>
                                                <a class="btn btn-success btn-sm add-sku-image" data-sku-image="{$val1['image']}"><i class="fa fa-lg fa-plus"></i>添加</a>
                                            </div>
                                            <div class="parents-update" <?php if(empty($val1['image'])) echo 'style="display: none;"';?>>
                                                             <span class="dndc-example1">
                                                             <a class="btn btn-success btn-sm view-sku-image" ><i class="fa fa-x fa-eye "></i>预览</a>
                                                                <div class="pop-preview">
                                                                    <img src="{$val1['image']}">
                                                               </div>
                                                           </span>

                                                <a class="btn btn-white btn-sm update-sku-image" ><i class="fa fa-undo"></i></i>替换</a>
                                                <a class="btn btn-default btn-sm del-sku-image" ><i class="fa fa-trash-o"></i></i>删除</a>
                                            </div>

                                        </td>
                                        <td id="element" style="display: none">
                                            <input type="hidden" name="hours_id" class="hours_id">
                                        </td>
                                        <td  class="text-left">
                                            <div class=" col-md-12 p-l-0" id="sku_code" style="width: 100px;">
                                                {if $row['dd_commodity_type'] == 9}
                                                <input type="text" value="{$val1['sku_code']}" data-parsley-required="true" onmouseover="this.title=this.value" {if $row['dd_commodity_type'] == 9}readonly="readonly"{/if} class="form-control is_show_change sku_code">
                                                {elseif $row['dd_commodity_type'] == 10 }
                                                <input type="text" value="{$val1['sku_code']}" data-parsley-required="true" onmouseover="this.title=this.value" {if $row['dd_commodity_type'] == 10}readonly="readonly"{/if} class="form-control is_show_change sku_code">
                                                {elseif $row['dd_commodity_type'] == 11}
                                                <input type="text" value="{$val1['sku_code']}" data-parsley-required="true" onmouseover="this.title=this.value" {if $row['dd_commodity_type'] == 11}readonly="readonly"{/if} class="form-control is_show_change sku_code">
                                                {elseif in_array($row['dd_commodity_type'],[1,3,4,12])}
                                                <input type="text" value="{$val1['sku_code']}" data-parsley-required="true" onmouseover="this.title=this.value" readonly="readonly" class="form-control is_show_change sku_code">
                                                {else /}
                                                <input type="text" value="{$val1['sku_code']}" class="form-control is_show_change sku_code">
                                                {/if}
                                            </div>
                                            {if $row['dd_commodity_type'] != 0}
                                            <p><a href="javascript:void(0);" class="win_beijian <?php if ($row['dd_commodity_type']!=9){echo 'hidden';} ?>">选择</a></p>
                                            <p><a href="javascript:void(0);" data-address="{$val.id}" class="win_taocan <?php if(!in_array($row['dd_commodity_type'],[1,3,4,12])){echo 'hidden';} ?>">选择</a></p>
                                            <p><a href="javascript:void(0);" class="win_pz1a <?php if($row['dd_commodity_type']!=10){echo 'hidden';} ?>">选择</a></p>
                                            <p><a href="javascript:void(0);" class="win_hours <?php if($row['dd_commodity_type']!=11){echo 'hidden';}?>">选择</a></p>
                                            {/if}
                                        </td>
                                        <td id="valid-{$key1}" class="text-left" >
                                            <div class="input-group col-md-12" id="all_price_td">
                                                {if $row['dd_commodity_type'] == 9}
                                                <input type="text" value="{$val1['price']}" data-parsley-required="true" onmouseover="this.title=this.value" readonly="readonly" class="form-control is_show_change sku_price col-md-4" data-parsley-errors-container="#valid-{$key1}">
                                                {elseif $row['dd_commodity_type'] == 10}
                                                <input type="text" value="{$val1['price']}" data-parsley-required="true" onmouseover="this.title=this.value" readonly="readonly" class="form-control is_show_change sku_price col-md-4" data-parsley-errors-container="#valid-{$key1}">
                                                {elseif $row['dd_commodity_type'] == 11}
                                                <input type="text" value="{$val1['price']}" data-parsley-required="true" onmouseover="this.title=this.value" readonly="readonly" class="form-control is_show_change sku_price col-md-4" data-parsley-errors-container="#valid-{$key1}">
                                                {elseif in_array($row['dd_commodity_type'],[1,3,4,12])}
                                                <input type="text" value="{$val1['price']}" data-parsley-required="true" onmouseover="this.title=this.value" readonly="readonly" class="form-control is_show_change sku_price col-md-4" data-parsley-errors-container="#valid-{$key1}">
                                                {else /}
                                                <input type="text" value="{$val1['price']}" data-parsley-required="true" class="form-control is_show_change sku_price col-md-4">
                                                {/if}
                                                <span class="input-group-addon ">元</span>
                                            </div>
                                        </td>
                                        <td id="valid-{$key1}" class="text-left">
                                            <div class="input-group col-md-3" >
                                                <input type="text" data-parsley-errors-container="#valid-{$key1}" onmouseover="this.title=this.value" value="{$val1['cost_price']}" class="form-control is_show_change cost_price col-md-3" >
                                                <span class="input-group-addon" >元</span>
                                            </div>

                                        </td>
                                        <td >
                                            <div class="col-md-12 p-l-0">
                                                <input type="text" value="{$val1['stock']}" class="form-control is_show_change sku_stock" data-parsley-required="true" data-parsley-type="integer">
                                            </div>
                                        </td>
                                        <td id="valid-{$key1}" class="text-left">
                                            <div class="input-group col-md-3" >
                                                <input type="text"  value="{$val1['tax']}" data-parsley-errors-container="#valid-{$key1}" class="form-control is_show_change tax col-md-3"  >
                                            </div>

                                        </td>
                                        <td id="valid-{$key1}" class="text-left">
                                            <div class="input-group col-md-3" >
                                                <input type="text"  value="{$val1['tax_code']}" data-parsley-errors-container="#valid-{$key1}" class="form-control is_show_change tax_code col-md-3"  >
                                            </div>

                                        </td>
                                        <td id="element" class="text-left dd_commodity_type_7" {if $row['dd_commodity_type'] != 7 && $row['dd_commodity_type'] != 8 && $row['commodity_class'] != 6}style="display: none;"{/if}>
                                            <div class="input-group col-md-3" >
                                                <?php if (isset($card_name_list[$val1['card_id']])) {?>
                                                    <div class="dd_commodity_type_7_1" {if $val1['card_id'] > 0}style="display: none;"{/if}><span class="card7" data-id="0" style="color: blue;">选择</span></div>
                                                    <div class="dd_commodity_type_7_2" {if $val1['card_id'] == 0}style="display: none;"{/if}><span class="card7_name" style="color: red;">{if $val1['card_id'] > 0}{$card_name_list[$val1['card_id']]}{/if} </span> <span class="card7" data-id="{$val1['card_id']}" style="color: blue;">替换 </span><span class="del_card" onclick="del_card(this)" style="color: blue;">删除</span></div>
                                                    <input type="hidden" class="card_id is_show_change" value="{$val1['card_id']}">
                                                <?php } else {  ?>
                                                    <div class="dd_commodity_type_7_1" ><span class="card7" data-id="0" style="color: blue;">选择</span></div>
                                                    <div class="dd_commodity_type_7_2" style="display: none;"><span class="card7_name" style="color: red;"></span> <span class="card7" data-id="" style="color: blue;">替换 </span><span class="del_card" onclick="del_card(this)" style="color: blue;">删除</span></div>
                                                    <input type="hidden" class="card_id is_show_change" value="}">
                                                 <?php }   ?>
                                            </div>
                                        </td>
                                    </tr>
                                    {/volist}
                                </table>
                            </div>
                            {/volist}
                        </div>
                    </div>
                    {else /}
                        <!--  非套餐变商品  -->
                        <table class="table table-bordered price-stock">
                            <thead>
                            <tr class="text-center">
                                <?php foreach($sp_title as $val):?>
                                    <th sp-title="">{$val}</th>
                                <?php endforeach; ?>
                                <th>规格图片</th>
                                <th>规格编码</th>
                                <th>
                                    {if $row['dd_commodity_type'] == 9}
                                    <div id="all_price1" readonly="readonly" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i class="fa fa-edit m-l-5"></i></div>
                                    {else /}
                                    <a href="#" id="all_price" data-type="text" class="btn btn-primary btn-sm all_price" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></a>
                                    {/if}
                                </th>
                                <th>
                                    <a href="#" id="all_cost_price" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="成本价" data-title="批量设置成本价">成本价<i class="fa fa-edit m-l-5"></i></a>
                                </th>
                                <th><a href="#" id="all_stock" data-type="text" class="btn btn-primary btn-sm editable editable-click all_stock" data-value="" data-placeholder="请输入库存" data-title="批量设置库存">库存<i class="fa fa-edit m-l-5"></i></a></th>
                                <th style="width: 110px;">
                                    <a href="#" id="all_coefficient" data-type="text" class="btn btn-primary btn-sm editable editable-click all_coefficient" data-value="" data-placeholder="请输入发货系数" data-title="批量设置发货系数">发货系数<i class="fa fa-edit m-l-3"></i></a>
                                </th>
                                <th><a href="#" id="all_tax" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax" data-value="" data-placeholder="税率" data-title="批量设置税率">税率<i class="fa fa-edit m-l-5"></i></a></th>
                                <th id="taxt_code_th"><a href="#" id="all_tax_code" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax_code" data-value="" data-placeholder="税务编码" data-title="批量设置税务编码">税务编码<i class="fa fa-edit m-l-5"></i></a></th>
                                <th class="dd_commodity_type_7" {if $row['dd_commodity_type'] != 7 && $row['dd_commodity_type'] != 8}style="display: none;"{/if}><a href="javascript:;" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="税务编码" data-title="批量设置税务编码">关联代金券</a></th>

                            </tr>
                            </thead>
                                <?php foreach($sku_list as $key1=>$val1):?>
                                <tr class="text-center" data-sku-list="{$val1['sp_value_list']}">
                                    <td  id="valid-{$key1}" style="display: none">
                                        <div class="input-group col-md-3" >
                                            <input type="text" class="sku_id" value="{$val1['id']}">
                                        </div>
                                    </td>
                                    <?php if(!empty($val1['sp_value_arr'])):?>
                                        <?php  foreach($val1['sp_value_arr'] as $key2=>$val2):?>
                                            <td data-sp-value-id="{$val2}">{$sp_list[$val2]['sp_value_name']}</td>
                                        <?php endforeach;?>
                                    <?php endif;?>
                                    <td class="text-left sku-image-td">
                                        <div class="parents-add" <?php if(!empty($val1['image'])) echo 'style="display: none;"';?>>
                                            <a class="btn btn-success btn-sm add-sku-image" data-sku-image="{$val1['image']}"><i class="fa fa-lg fa-plus"></i>添加</a>
                                        </div>
                                        <div class="parents-update" <?php if(empty($val1['image'])) echo 'style="display: none;"';?>>
                                                             <span class="dndc-example1">
                                                             <a class="btn btn-success btn-sm view-sku-image" ><i class="fa fa-x fa-eye "></i>预览</a>
                                                                <div class="pop-preview">
                                                                    <img src="{$val1['image']}">
                                                               </div>
                                                           </span>

                                            <a class="btn btn-white btn-sm update-sku-image" ><i class="fa fa-undo"></i></i>替换</a>
                                            <a class="btn btn-default btn-sm del-sku-image" ><i class="fa fa-trash-o"></i></i>删除</a>
                                        </div>

                                    </td>
                                    <td id="element" style="display: none">
                                        <input type="hidden" name="hours_id" class="hours_id">
                                    </td>
                                    <td  class="text-left">
                                        <div class=" col-md-12 p-l-0" id="sku_code" style="width: 100px;">
                                            {if $row['dd_commodity_type'] == 9}
                                            <input type="text" value="{$val1['sku_code']}" data-parsley-required="true" onmouseover="this.title=this.value" {if $row['dd_commodity_type'] == 9}readonly="readonly"{/if} class="form-control is_show_change sku_code">
                                            {elseif $row['dd_commodity_type'] == 10 }
                                            <input type="text" value="{$val1['sku_code']}" data-parsley-required="true" onmouseover="this.title=this.value" {if $row['dd_commodity_type'] == 10}readonly="readonly"{/if} class="form-control is_show_change sku_code">
                                            {elseif $row['dd_commodity_type'] == 11}
                                            <input type="text" value="{$val1['sku_code']}" data-parsley-required="true" onmouseover="this.title=this.value" {if $row['dd_commodity_type'] == 11}readonly="readonly"{/if} class="form-control is_show_change sku_code">
                                            {else /}
                                            <input type="text" value="{$val1['sku_code']}" class="form-control is_show_change sku_code">
                                            {/if}
                                        </div>
                                        {if $row['dd_commodity_type'] != 0}
                                        <p><a href="javascript:void(0);" class="win_beijian <?php if ($row['dd_commodity_type']!=9){echo 'hidden';} ?>">选择</a></p>
                                        <p><a href="javascript:void(0);" class="win_taocan <?php if(!in_array($row['dd_commodity_type'],[1,3,4,12])){echo 'hidden';} ?>">选择</a></p>
                                        <p><a href="javascript:void(0);" class="win_pz1a <?php if($row['dd_commodity_type']!=10){echo 'hidden';} ?>">选择</a></p>
                                        <p><a href="javascript:void(0);" class="win_hours <?php if($row['dd_commodity_type']!=11){echo 'hidden';}?>">选择</a></p>
                                        {/if}
                                    </td>
                                    <td id="valid-{$key1}" class="text-left" >
                                        <div class="input-group col-md-12" id="all_price_td">
                                            {if $row['dd_commodity_type'] == 9}
                                            <input type="text" value="{$val1['price']}" data-parsley-required="true" onmouseover="this.title=this.value" readonly="readonly" class="form-control is_show_change sku_price col-md-4" data-parsley-errors-container="#valid-{$key1}">
                                            {elseif $row['dd_commodity_type'] == 10}
                                            <input type="text" value="{$val1['price']}" data-parsley-required="true" onmouseover="this.title=this.value" readonly="readonly" class="form-control is_show_change sku_price col-md-4" data-parsley-errors-container="#valid-{$key1}">
                                            {elseif $row['dd_commodity_type'] == 11}
                                            <input type="text" value="{$val1['price']}" data-parsley-required="true" onmouseover="this.title=this.value" readonly="readonly" class="form-control is_show_change sku_price col-md-4" data-parsley-errors-container="#valid-{$key1}">
                                            {else /}
                                            <input type="text" value="{$val1['price']}" data-parsley-required="true" class="form-control is_show_change sku_price col-md-4">
                                            {/if}
                                            <span class="input-group-addon ">元</span>
                                        </div>
                                    </td>
                                    <td id="valid-{$key1}" class="text-left">
                                        <div class="input-group col-md-3" >
                                            <input type="text" data-parsley-errors-container="#valid-{$key1}" onmouseover="this.title=this.value" value="{$val1['cost_price']}" class="form-control is_show_change cost_price col-md-3" >
                                            <span class="input-group-addon" >元</span>
                                        </div>

                                    </td>

                                    <td >
                                        <div class="col-md-12 p-l-0">
                                            <input type="text" value="{$val1['stock']}" class="form-control is_show_change sku_stock" data-parsley-required="true" data-parsley-type="integer">
                                        </div>
                                    </td>

                                    <td >
                                            <input type="text" value="{$val1['delivery_coefficient']}" class="form-control is_show_change delivery_coefficient" data-parsley-required="true" data-parsley-type="integer">
                                    </td>

                                    <td id="valid-{$key1}" class="text-left">
                                        <div class="input-group col-md-3" >
                                            <input type="text"  value="{$val1['tax']}" data-parsley-errors-container="#valid-{$key1}"  class="form-control is_show_change tax col-md-3 invoice_rate_{$key1}"  data-parsley-pattern="/^(0|6|9|11|13|16|17)$/" >
                                        </div>

                                    </td>


                                    <td id="valid-{$key1}" class="text-left">
                                        <div class="input-group col-md-3" >
                                            <input type="text"  value="{$val1['tax_code']}" data-i="{$key1}" is_search="0" data-parsley-errors-container="#valid-{$key1}" class="invoice_type_{$key1} form-control is_show_change tax_code col-md-3 select_invoice_type_show" data-parsley-pattern="/^\d{19}$/" >
                                        </div>

                                    </td>

                                    <td id="element" class="text-left dd_commodity_type_7" {if $row['dd_commodity_type'] != 7 && $row['dd_commodity_type'] != 8 && $row['commodity_class'] != 6}style="display: none;"{/if}>
                                    <div class="input-group col-md-3" >
                                    <?php if (isset($card_name_list[$val1['card_id']])) {?>
                                        <div class="dd_commodity_type_7_1" {if $val1['card_id'] > 0}style="display: none;"{/if}><span class="card7" data-id="0" style="color: blue;">选择</span></div>
                                        <div class="dd_commodity_type_7_2" {if $val1['card_id'] == 0}style="display: none;"{/if}><span class="card7_name" style="color: red;">{if $val1['card_id'] > 0}{$card_name_list[$val1['card_id']]}{/if} </span> <span class="card7" data-id="{$val1['card_id']}" style="color: blue;">替换 </span><span class="del_card" onclick="del_card(this)" style="color: blue;">删除</span></div>
                                        <input type="hidden" class="card_id is_show_change" value="{$val1['card_id']}">
                                    <?php } else {?>
                                        <div class="dd_commodity_type_7_1" ><span class="card7" data-id="0" style="color: blue;">选择</span></div>
                                        <div class="dd_commodity_type_7_2" style="display: none;"><span class="card7_name" style="color: red;"></span> <span class="card7" data-id="" style="color: blue;">替换 </span><span class="del_card" onclick="del_card(this)" style="color: blue;">删除</span></div>
                                        <input type="hidden" class="card_id is_show_change" value="">
                                    <?php } ?>
                        </div>

                        </td>

                        </tr>

                        <?php endforeach;?>
                        </tbody>
                        </table>
                    {/in}


                </div>

                <!-- sku-image-->
                <input type="file" id="sku-image-file" name="sku-image" class="hide">
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">商品图片<i class="m-r-3 text-danger">*</i>:<br><span>(用于商品详情的头图)</span></label>
                <div class="col-md-10  image_group">


                        <div class="dndc-upload-pic goods_pic" sp-id="0" data-image-id="0" id="image-id-0" style="height: 180px;">
                            <label  class="sp-value m-t-10 m-l-10">默认</label>
                            <label>
                                <a href="javascript:;" class="btn-image btn btn-primary m-r-5">上传图片</a>
                            </label>
                            <label style="top: 80px;" data-toggle="modal" data-target="#jd-img-modal" >
                                <a href="javascript:;"  class="btn-image-jd btn btn-primary m-r-5">京东主图</a>
                            </label>
                            <p style="bottom:5px;">5张,200K,jpg,png
                                <br/><a href="javascript:;" class="btn-px" id="btn-px">点击排序</a>
                            </p>
                            <ul>
                                <?php foreach($sku_image as $val1):?>
                                    <li class="move-item">
                                        <img image-value="{$val1}" src="{$Think.config.upload.url}{$val1}">
                                        <del></del><span>修改</span>
                                    </li>
                                <?php endforeach;?>
                            </ul>
                        </div>

                    <input type="file" name="image" class="hide"  id="goods-image-input">
                </div>


            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品视频</label>
                <div class="col-md-10 ">
                    <input id="file_video" name="file_video" type="file" class="file" data-overwrite-initial="false">
                    <input id="video_img" name="video_img" type="hidden" value="{$row['video']}">
                </div>
            </div>


<!--            <legend class="pull-left width-full">商品详情描述</legend>-->
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">商品详情<i class="m-r-3 text-danger">*</i>:</label>-->
<!--                <div class="col-md-9 ">-->
<!--                    <script id="content"  name="content" type="text/plain" style="width:100%;height:300px;"></script>-->
<!--                </div>-->
<!---->
<!--            </div>-->

            <input type="hidden" name="id" value="{$row['id']}">
            <input type="hidden" name="action" value="update">

            <div class="text-center m-t-20" style="position: sticky;bottom: 0px;height:80px;background-color: #fff;line-height: 80px;">
<!--                <button type="button" id="put-form-view" class="btn btn-primary m-r-5 m-b-5">预览</button>-->
                <button id="put-form" type="button" class="btn btn-primary m-r-5 m-b-5">下一步</button>
            </div>
        </form>
    </div>


    <!-- begin 选择备件 -->
    <div class="modal fade" id="bj-modal">
    </div>
    <!-- end 选择备件-->

    <!-- begin 通过备件查看车型 -->
    <div class="modal fade" id="bj-car-modal">
    </div>
    <!-- end 通过备件查看车型-->

    <!-- begin 查看上架商品 -->
    <div class="modal fade" id="commodity_set">
    </div>
    <!-- end 通过备件查看车型-->

    <!----begin 选择车系---->
    <div class="modal fade" id="car-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择车型</h4>
                </div>

                <div class="modal-body car-series ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <div class="form-group">
                        <div class="col-md-2 p-r-0 checkbox">
                            <label><input type="checkbox" id="check_all" class="car_checkbox ">全选</label>
                        </div>
                        {volist name="brand_s" id="v" key="k"}
                        <div class="col-md-2 p-r-0 checkbox">
                            <label><input type="checkbox"   class="car-brand-type"  data-brand-type="{$k}" >{$v}</label>
                        </div>
                        {/volist}
<!--                        <div class="col-md-2 p-r-0 checkbox">-->
<!--                            <label><input type="checkbox"   class="car-brand-type"  data-brand-type="1" >NISSAN</label>-->
<!--                        </div>-->
<!--                        <div class="col-md-2 p-r-0 checkbox">-->
<!--                            <label><input type="checkbox"  class="car-brand-type"  data-brand-type="2" >启辰</label>-->
<!--                        </div>-->
                    </div>
                    <legend class="pull-left width-full"></legend>
                    <?php foreach($car_series_list as $key=>$val):?>
                        <div class=" form-group ">
                            <div class="col-md-2 p-r-0 checkbox">
                                <label><input type="checkbox"  class="car_checkbox " id="{$key}"  data-parent="check_all"> {$val['car_series_name']}</label>
                            </div>

                            <div class="col-md-9 checkbox">
                                <?php foreach($val['sub_data'] as $key1=>$val2):?>
                                    <label class="m-r-5"><input type="checkbox" data-sub-brand-type="{$val2['brand_type']}" <?php if(in_array($key1,explode(',',$row['car_series_id']))) echo 'checked';?> class="car_checkbox min" data-parent="{$key}" value="{$key1}" data-car-series="{$val2['sub_car_series_name']}" name="car_series[]">{$val2['sub_car_series_name']}</label>
                                <?php endforeach;?>
                            </div>

                        </div>
                        <legend class="pull-left width-full"></legend>
                    <?php endforeach;?>

                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="add-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 预览二维码---->
    <div class="modal fade" id="preview-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">扫码二维码预览</h4>
                </div>

                <div class="modal-body qc_code text-center ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>

                    <img src="">

                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white btn-sm" data-dismiss="modal">关闭</a>
                </div>
            </div>
        </div>
    </div>

    <!-- begin 选择套餐 -->
    <div class="modal fade" id="taocan-modal">
    </div>
    <!-- end 选择套餐-->

    <!-- begin 选择工时 -->
    <div class="modal fade" id="hours-modal">
    </div>
    <!-- end 选择工时-->

    <!-- begin 选择pz套餐 -->
    <div class="modal fade" id="pz1a-modal">
    </div>
    <!-- end 选择pz套餐-->

    <!-- begin 选择pz套餐 -->
    <div class="modal fade" id="pz1a-car-modal">
    </div>
    <!-- end 选择pz套餐-->

    <!----begin 删除---->
    <div class="modal fade" id="deleteModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i> 删除操作</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" id="no-del" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 选择到店代金券---->
    <div class="modal fade" id="addcard7Modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="card7_title">选择到店代金券</h4>
                </div>
                <form data-parsley-trigger="change" class="form">
                    <div class="modal-body">
                        <div class="alert alert-danger m-b-8" style="display: none;">
                            <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                            <p></p>
                        </div>
                        <div class="form-group" style="width: 600px">
                            <div style="padding: 2px 0px; width: 210px;float: left;">
                                <label class="control-label col-md-2 m-l-2" style="padding: 2px 0px; width: 55px;">优惠券名称:</label>
                                <input type="text" class="form-control input-sm element col-md-3 m-r-2 width-150" name="card7_name" placeholder="请输入优惠券名称">
                            </div>

                            <button id="card7-search" type="button" class="btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                        </div>
                        <p></p>

                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th class="text-center">序号</th>
                                <th class="text-center">归属</th>
                                <th class="text-center">优惠券类型</th>
                                <th class="text-center">优惠券id</th>
                                <th class="text-center">优惠券名称</th>
                                <th class="text-center">操作</th>
                            </tr>
                            </thead>
                            <tbody id="add-card7-tbody">

                            </tbody>
                        </table>
                        <div>
                            <ul class="pagination" id="card7-pagination"></ul>
                        </div>
                    </div>
                    <!--  当前打开弹层的文本框 -->
                    <input type="hidden" name="text_click_id" value="">

                    <div class="modal-footer">
                        <a href="javascript:;" class="btn btn-sm btn-white" id="add-card7-no" data-dismiss="modal">取消</a>
                        <a href="javascript:;" class="btn btn-sm btn-primary" id="add-card7-ok" data-dismiss="modal">确定</a>
                    </div>

                </form>
            </div>
        </div>
    </div>
    <!----end 选择到店代金券---->

    <!----begin 分类删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>确定删除该分类?</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-type-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>
    <!----end 分类删除设置---->

    <!----begin 京东显示图片---->
    <div class="modal fade in" id="jd-img-modal">
    <div class="modal-dialog" style="width: 45%">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title">选择京东主图</h4>
            </div>

            <div class="modal-body car-series ">
                <div class="alert alert-danger m-b-8" style="display: none;">
                    <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                    <p></p>
                </div>
                <form class="form search-form">
                    <label>JD商品sku:
                        <input type="text" id="sku_id" value="" class="form-control input-sm element width-200" placeholder="jd商品sku" aria-controls="data-table" >
                    </label>
                    <button id="jd-sku-search" type="button" class=" btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                </form>
                <legend class="pull-left width-full m-t-15"></legend>
                <div class="table-scrollable">
                    <table id="card_form" class="table table-bordered">
                        <thead>
                        <th class="text-center">JD图片预览</th>
                        <th class="text-center">JD是否可用</th>
                        <th class="text-center">JD是否主图</th>
                        <th class="text-center">JD排序</th>
                        <th class="text-center">JD位置</th>
                        <th class="text-center">JD更新时间</th>
                        </thead>
                        <tbody id="jd-body">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-sm btn-white btn-sm" data-dismiss="modal">确认</a>
                <!--                        <a href="javascript:;" class="btn btn-sm btn-primary btn-sm" data-dismiss="modal"">确定</a>-->
            </div>
        </div>
    </div>
</div>
    <!----end 京东显示图片---->
<!--税收分类编码 start----->
<div class="modal fade" id="invoice-type-modal" data-comm-id="" data-comm-set-id="" data-type="">
    <div class="modal-dialog" style="width: 1000px">
        <div class="modal-content">
            <div class="modal-header">
                <div class="alert alert-danger m-b-8" style="display: none;">
                    <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                    <p></p>
                </div>
                <form class="form search-form" action="{:url('Invoice_type/getTypeList')}">
                    <label>税收分类编码:
                        <input type="text" id="tax_code" name="tax_code" value="" class="form-control input-sm element width-200" placeholder="请输入税收分类编码" aria-controls="data-table" >
                    </label>
                    <label>税收分类名称:
                        <input type="text" id="tax_name" name="tax_name" value="" class="form-control input-sm element width-200" placeholder="请输入税收分类名称" aria-controls="data-table" >
                    </label>
                    <label>税收分类简称:
                        <input type="text" id="short_name" name="short_name" value="" class="form-control input-sm element width-200" placeholder="请输入税收分类简称" aria-controls="data-table" >
                    </label>
                    <button  type="button" class=" btn btn-sm btn-success select_invoice_type_show" is_search="1" id="invoice_type_search"><i class="fa fa-search"></i>搜索</button>
                </form>
            </div>
            <input id="modal_com_type_id" type="hidden" value="" />
            <div class="modal-body" style="">
                <div class="hide">
                    <div class="sku-image" style="display: inline-block;">
                        <img class=" cover-image" src="">
                    </div>
                    <div style="display: inline-block;">
                        <div  class="sku-comm"></div>
                    </div>
                </div>
                <table class="table table-hover">
                    <thead>
                    <tr>
                        <th class="text-center">序号</th>
                        <th class="text-center">税收分类编码</th>
                        <th class="text-center">税收分类名称</th>
                        <th class="text-center">简称</th>
                        <th class="text-center">税率</th>
                        <th class="text-center">是否可用</th>
                        <th class="text-center">说明</th>
                        <th class="text-center">操作</th>
                    </tr>
                    </thead>
                    <tbody class="invoice-type-tb"></tbody>
                </table>
            </div><table><tr><td  style="padding-left: 60px">共搜索到<span id="invoice_type_count"></span>条数据</td><td><div id="invoice_type_page" ></div></td></tr></table>

<!--            <div class="modal-footer">-->
<!--                <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >关闭</a>-->
<!--            </div>-->
        </div>
    </div>
</div>
<!--税收分类编码 end----->

</div>
{/block}
{block name="script"/}

<script type="text/javascript" src="__STATIC__admin_v2/js/jquery.page.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/plugins/layui_v2.5.7/layui.js"></script>
<script src="__STATIC__admin_v2/js/purify.min.js"></script>
<script src="__STATIC__admin_v2/js/sortable.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput_locale_zh.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js?v=2.1.9"></script>
<!--<script src="__STATIC__admin_v2/js/commodity.js?v=--><?php //echo time(); ?><!--"></script>-->



<script>
    layui.use('element', function(){
        var $ = layui.jquery
            ,element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
        //触发事件
        var active = {
            tabChange: function(){
                //切换到指定Tab项
                element.tabChange(); //切换到：用户管理
            }
        };

        $('.site-demo-active').on('click', function(){
            var othis = $(this), type = othis.data('type');
            active[type] ? active[type].call(this, othis) : '';
        });

        //Hash地址的定位
        var layid = location.hash.replace(/^#test=/, '');
        element.tabChange('test', layid);

        element.on('tab(test)', function(elem){
            location.hash = 'test='+ $(this).attr('lay-id');
        });

    });
    var url_select_invoice_type="{:url('Invoice_type/getTypeList?pagesize=1000000&status=1')}";
    var url_get_sku_table="{:url('getSkuTable')}";
    var url_get_bj_table="{:url('getBjTable')}";
    var url_get_bj_car_table="{:url('getBjCarTable')}";
    var url_get_hours_table="{:url('getHoursTable')}";
    var url_get_pz1a_table="{:url('getPz1aTable')}";
    var url_get_pz1a_car_table="{:url('getPz1aCarTable')}";
    var jd_sku_img  ="{:url('jd_sku_img')}";
    var ajaxGetCard_url  ="{:url('ajaxLiveGetCard')}";
    var ajaxGetBj_url  ="{:url('e3sSparePart/ajaxGetBj')}";
    var ajaxGetBjCar_url  ="{:url('e3sSparePart/select_car_series')}";
    var validate_spec_val  ="{:url('validateSpec')}";
    var ajaxGetHours_url = "{:url('e3sPartTime/ajaxGetHours')}";
    var ajaxGetPz1a_url = "{:url('e3sPz1a/ajaxGetSetMeal')}"
    var ajaxGetPz1aCar_url = "{:url('e3sPz1a/ajaxGetPz1aCar')}"
    var ajaxcard7Url = ajaxGetCard_url + '?pagesize=10';      //列表
    var ajaxGetTaoCan_url = "{:url('e3sSetMeal/ajaxGetTaoCan')}";//获取套餐列表
    var tips_url  ="{:url('getClassTips')}";

    // ue.addListener("ready", function() {
    //     ue.setContent('{$row['detail_content']}');
    // });

    $('#btn-px').on('click',function(){
        if($(this).hasClass('on')){
            $('.goods_pic>ul').sortable({
                disabled: true,
            });
            $(this).removeClass('on').text('点击排序');
        }else {
            $('.goods_pic>ul').sortable({
                disabled: false,
                items:'.move-item',
                // cancel:'.other',
            });
            $(this).addClass('on').text('排序完成');;
        }
    })
    var is_show = 0;
    var dd_commodity_type = {$row['dd_commodity_type']};
    var commodity_class = {$row['commodity_class']};



    $(function(){

        commodityClass(commodity_class);

        carIni();
        batchIni();
        localStorage.clear()
        if(dd_commodity_type === 9){
            <?php if($row['dd_commodity_type'] === 9) :?>
            <?php foreach($sku_list as $key=>$val):?>
            localStorage.setItem("part_no_"+{$key}, "{$val['sku_code']}");
            localStorage.setItem("part_price_"+ {$key}, "{$val['price']}");
            <?php endforeach;?>
            <?php endif?>

        }
        if(dd_commodity_type === 10){
            <?php foreach($sku_list as $key=>$val):?>
            var part_no = localStorage.getItem('pz1a_code_'+{$key})
            if(part_no == null){

            }
            <?php endforeach;?>
        }else if(dd_commodity_type == 11){
            <?php foreach($sku_list as $key=>$val):?>
            var part_no = localStorage.getItem('wi_code_'+{$key})
            if(part_no == null){

            }
            <?php endforeach;?>
        }
        else if(dd_commodity_type == 7 || dd_commodity_type == 8){
            ajaxcard7Url += "&card_type=6";
        }else if(commodity_class == 6){
            $("input[name='dd_commodity_type']:checked").removeAttr('checked');
            $('.dd_commodity_type_7').find('.editable-click').text('关联取送车券');
            $('.dd_commodity_type_7').show();
            ajaxcard7Url += "&card_type=7";
        }else if(dd_commodity_type == 1 || dd_commodity_type == 3 || dd_commodity_type == 4 || dd_commodity_type == 12){

            if (dd_commodity_type == 12) {
                // 保养套餐-五年双保专享心悦套餐
                // 套餐折扣只保留 2次(7折)
                $("#zhe_kou_1").hide();
                $("#zhe_kou_2").hide();
            }


            <?php foreach($tab_data as $key=>$val) :?>
            <?php foreach($sku_list[$val["id"]] as $k=>$v) :?>
                var sp_value_list = "{$v.sp_value_list}"
                var appoint = sp_value_list.replace(/,/g, '');
                var address_key = "{$val.id}"
                localStorage.setItem("taocan_no_"+address_key+"_"+appoint,"{$v.sku_code}");
                localStorage.setItem("taocan_price_"+address_key+"_"+appoint,"{$v.price}");
                <?php endforeach;?>
            <?php endforeach;?>
        }


        $("#put-form,#put-form-view").on('click',function(){
            var sku_list  =getSkuList();
            var image_list=getImageList();
            var $form=$("#add-step-2");
            var validate=$form.psly().validate();  //表单验证
            if(!validate) return false;
            var form_data=$form.serialize();
            var alert_obj=$("#alert-danger");

            if (image_list.length<=0){
                layer.msg('默认图片不能为空');
                return false;
            }
            if($(this).attr("id")=='put-form-view'){
                var is_preview=1;
            }else {
                var is_preview=0;
            }
            var is_cost_price = false;
            if(dd_commodity_type === 0){
                $.each(sku_list,function(k,v){
                    if (!(/^[0-9/.]*$/).test(v.cost_price)) {
                        is_cost_price = true;
                        sku_list[k]['cost_price'] = '';
                    }
                })
            }

            if(dd_commodity_type == 1 || dd_commodity_type == 3 || dd_commodity_type == 4 ||dd_commodity_type == 4){
                var data=form_data + "&" + $.param({is_preview:is_preview,car_series_id:car_series_id,'sku_list':JSON.stringify(sku_list),'image_list':image_list})
            }else{
                var data=form_data + "&" + $.param({is_preview:is_preview,car_series_id:car_series_id,'sku_list':sku_list,'image_list':image_list})
            }
            if(is_cost_price){
                var cost_price_is =  layer.msg("有sku成本价未填写或格式不正确，是否确认进行下一步编辑？",{
                     icon: 0
                    ,time:false
                    ,btn: ['下一步','取消'] //按钮
                    ,yes:function(){
                        layer.close(cost_price_is);
                        if (is_preview){
                            Custom.ajaxPost("{:url('save')}",data,null,null,function(res){
                                var url=res.data.com_url;
                                // console.log(url);
                                //调用lay_out 生成二维码

                                //提交成功删除本地缓存 start
                                localStorage.clear()
                                //提交成功删除本地缓存 end

                                /* $('#output').empty();
                                 $('#output').qrcode(url);
                                 $("#commodity-qrc-modal").modal("show");*/
                                $("#mod-mobile-pop").find("#output").attr("src",url);
                                $("#mod-mobile-pop").show();
                            })
                        }else{
                            if(is_show == 1){
                                var load_index = layer.open({
                                    type:2,
                                    title: ['查看#备件名称#车型详情'],
                                    btn:['全部下架并保存','取消'],
                                    area: ['70%','80%'],
                                    content: 'shelf_commodity?commodity_id='+ $('input[name="id"]').val(),
                                    yes:function (res){
                                        Custom.ajaxPost("{:url('save')}?is_off=1",data,null,null,function(res){
                                            if (res.error==0){
                                                location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                            }else {
                                                layer.msg(res.msg);
                                            }
                                        });
                                    }
                                })
                            }else{
                                var shelves_sources = '';
                                $("[name='shelves_sources[]']:checked").each(function (){
                                    shelves_sources += $(this).val() + ',' ;
                                })
                                shelves_sources = shelves_sources.slice(0,shelves_sources.length-1)

                                if({$is_show} == 1) {
                                    if(dd_commodity_type == 1 || dd_commodity_type == 3 || dd_commodity_type == 4 || dd_commodity_type == 12){
                                        var old_sku_list = JSON.stringify(<?php echo json_encode($sku_list); ?>)
                                        var check_data = {'dd_commodity_type':"{$row['dd_commodity_type']}","commodity_id":$('input[name="id"]').val(),'shelves_sources': shelves_sources, 'old_shelves_sources': "{$row['shelves_sources']}",'old_sku_list': old_sku_list,'change_sku_list': JSON.stringify(sku_list)}
                                    }else{
                                        var check_data = {'dd_commodity_type':"{$row['dd_commodity_type']}","commodity_id":$('input[name="id"]').val(),'shelves_sources': shelves_sources, 'old_shelves_sources': "{$row['shelves_sources']}",'change_sku_list': sku_list,'old_sku_list': <?php echo json_encode($sku_list); ?>}
                                    }
                                    $.ajax({
                                        url: "{:url('change_check')}",
                                        type: 'POST',
                                        data: check_data,
                                        dataType: 'JSON',
                                        success: function (res) {
                                            if (res.error == 1) {    //成功返回
                                                layer.open({
                                                    type: 2,
                                                    title: ['查看#备件名称#车型详情'],
                                                    btn: ['全部下架并保存', '取消'],
                                                    area: ['70%', '80%'],
                                                    content: 'shelf_commodity?commodity_id=' + $('input[name="id"]').val(),
                                                    yes: function (res) {
                                                        Custom.ajaxPost("{:url('save')}?is_off=1",data,null,null,function(res){
                                                            if (res.error==0){
                                                                location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                                            }else {
                                                                layer.msg(res.msg);
                                                            }
                                                        });
                                                    }
                                                })
                                            } else {
                                                Custom.ajaxPost("{:url('save')}",data,null,null,function(res){
                                                    if (res.error==0){
                                                        location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                                    }else {
                                                        layer.msg(res.msg);
                                                    }
                                                });
                                            }
                                        },
                                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                                            layer.close(load_index);
                                            layer.alert('系统错误:状态码' + XMLHttpRequest.status + ' ' + errorThrown, {icon: 2});
                                        }
                                    });
                                }  else {
                                    //没有上架，判断有没有修改规格啥的
                                    if(dd_commodity_type == 1 || dd_commodity_type == 3 || dd_commodity_type == 4 || dd_commodity_type == 12){
                                        var old_sku_list = JSON.stringify(<?php echo json_encode($sku_list); ?>)
                                        var check_data = {'dd_commodity_type':"{$row['dd_commodity_type']}","commodity_id":$('input[name="id"]').val(),'shelves_sources': shelves_sources, 'old_shelves_sources': "{$row['shelves_sources']}",'old_sku_list': old_sku_list,'change_sku_list': JSON.stringify(sku_list)}
                                    }else{
                                        var check_data = {'dd_commodity_type':"{$row['dd_commodity_type']}","commodity_id":$('input[name="id"]').val(),'shelves_sources': shelves_sources, 'old_shelves_sources': "{$row['shelves_sources']}",'change_sku_list': sku_list,'old_sku_list': <?php echo json_encode($sku_list); ?>}
                                    }
                                    $.ajax({
                                        url: "{:url('change_check')}",
                                        type: 'POST',
                                        data: check_data,
                                        dataType: 'JSON',
                                        success: function (res) {
                                            if(res.error == 1 ){
                                                Custom.ajaxPost("{:url('save')}?is_off=1",data,null,null,function(res){
                                                    if (res.error==0){
                                                        location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                                    }else {
                                                        layer.msg(res.msg);
                                                    }
                                                });
                                            }else{
                                                Custom.ajaxPost("{:url('save')}?is_off=2",data,null,null,function(res){
                                                    if (res.error==0){
                                                        location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                                    }else {
                                                        layer.msg(res.msg);
                                                    }
                                                });
                                            }
                                        }
                                    })
                                }
                            }
                        }
                        return true;
                    },btn2:function(){
                        layer.close(cost_price_is);
                        return false;
                    }
                })
            }else{
                if (is_preview){
                    Custom.ajaxPost("{:url('save')}",data,null,null,function(res){
                        var url=res.data.com_url;
                        // console.log(url);
                        //调用lay_out 生成二维码

                        //提交成功删除本地缓存 start
                        localStorage.clear()
                        //提交成功删除本地缓存 end

                        /* $('#output').empty();
                         $('#output').qrcode(url);
                         $("#commodity-qrc-modal").modal("show");*/
                        $("#mod-mobile-pop").find("#output").attr("src",url);
                        $("#mod-mobile-pop").show();
                    })
                }else{
                    if(is_show == 1){
                        var load_index = layer.open({
                            type:2,
                            title: ['查看#备件名称#车型详情'],
                            btn:['全部下架并保存','取消'],
                            area: ['70%','80%'],
                            content: 'shelf_commodity?commodity_id='+ $('input[name="id"]').val(),
                            yes:function (res){
                                Custom.ajaxPost("{:url('save')}?is_off=1",data,null,null,function(res){
                                    if (res.error==0){
                                        location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                    }else {
                                        layer.msg(res.msg);
                                    }
                                });
                            }
                        })
                    }else{
                        var shelves_sources = '';
                        $("[name='shelves_sources[]']:checked").each(function (){
                            shelves_sources += $(this).val() + ',' ;
                        })
                        shelves_sources = shelves_sources.slice(0,shelves_sources.length-1)

                        if({$is_show} == 1) {
                            if(dd_commodity_type == 1 || dd_commodity_type == 3 || dd_commodity_type == 4 || dd_commodity_type == 12){
                                var old_sku_list = JSON.stringify(<?php echo json_encode($sku_list); ?>)
                                var check_data = {'dd_commodity_type':"{$row['dd_commodity_type']}","commodity_id":$('input[name="id"]').val(),'shelves_sources': shelves_sources, 'old_shelves_sources': "{$row['shelves_sources']}",'old_sku_list': old_sku_list,'change_sku_list': JSON.stringify(sku_list)}
                            }else{
                                var check_data = {'dd_commodity_type':"{$row['dd_commodity_type']}","commodity_id":$('input[name="id"]').val(),'shelves_sources': shelves_sources, 'old_shelves_sources': "{$row['shelves_sources']}",'change_sku_list': sku_list,'old_sku_list': <?php echo json_encode($sku_list); ?>}
                            }
                            $.ajax({
                                url: "{:url('change_check')}",
                                type: 'POST',
                                data: check_data,
                                dataType: 'JSON',
                                success: function (res) {
                                    if (res.error == 1) {    //成功返回
                                        layer.open({
                                            type: 2,
                                            title: ['查看#备件名称#车型详情'],
                                            btn: ['全部下架并保存', '取消'],
                                            area: ['70%', '80%'],
                                            content: 'shelf_commodity?commodity_id=' + $('input[name="id"]').val(),
                                            yes: function (res) {
                                                Custom.ajaxPost("{:url('save')}?is_off=1",data,null,null,function(res){
                                                    if (res.error==0){
                                                        location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                                    }else {
                                                        layer.msg(res.msg);
                                                    }
                                                });
                                            }
                                        })
                                    } else {
                                        Custom.ajaxPost("{:url('save')}",data,null,null,function(res){
                                            if (res.error==0){
                                                location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                            }else {
                                                layer.msg(res.msg);
                                            }
                                        });
                                    }
                                },
                                error: function (XMLHttpRequest, textStatus, errorThrown) {
                                    layer.close(load_index);
                                    layer.alert('系统错误:状态码' + XMLHttpRequest.status + ' ' + errorThrown, {icon: 2});
                                }
                            });
                        }  else {
                            //没有上架，判断有没有修改规格啥的
                            if(dd_commodity_type == 1 || dd_commodity_type == 3 || dd_commodity_type == 4 || dd_commodity_type == 12){
                                var old_sku_list = JSON.stringify(<?php echo json_encode($sku_list); ?>)
                                var check_data = {'dd_commodity_type':"{$row['dd_commodity_type']}","commodity_id":$('input[name="id"]').val(),'shelves_sources': shelves_sources, 'old_shelves_sources': "{$row['shelves_sources']}",'old_sku_list': old_sku_list,'change_sku_list': JSON.stringify(sku_list)}
                            }else{
                                var check_data = {'dd_commodity_type':"{$row['dd_commodity_type']}","commodity_id":$('input[name="id"]').val(),'shelves_sources': shelves_sources, 'old_shelves_sources': "{$row['shelves_sources']}",'change_sku_list': sku_list,'old_sku_list': <?php echo json_encode($sku_list); ?>}
                            }
                            $.ajax({
                                url: "{:url('change_check')}",
                                type: 'POST',
                                data: check_data,
                                dataType: 'JSON',
                                success: function (res) {
                                    if(res.error == 1 ){
                                        Custom.ajaxPost("{:url('save')}?is_off=1",data,null,null,function(res){
                                            if (res.error==0){
                                                location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                            }else {
                                                layer.msg(res.msg);
                                            }
                                        });
                                    }else{
                                        Custom.ajaxPost("{:url('save')}?is_off=2",data,null,null,function(res){
                                            if (res.error==0){
                                                location.href="{:url('successStep3')}?is_ok=0&commodity_id={$commodity_id}&is_grouped={$is_grouped}";
                                            }else {
                                                layer.msg(res.msg);
                                            }
                                        });
                                    }
                                }
                            })
                        }
                    }
                }
            }



        });

    });

    $("input[name='dd_commodity_type']").on('change', function (){

        console.log('dd_commodity_type:',$(this).val())
        $("#zhe_kou_1").show();
        $("#zhe_kou_2").show();

        if($(this).val() == 9){
            var th = '';
            th += '<div data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></div>'
            $("#all_price_th").html(th)

            $(".win_beijian").attr('style','display:block!important');
            $(".win_taocan").attr('style','display:none!important');
            $("#sku_code input").attr('readonly','readonly')
            $("#all_price_td input").attr('readonly','readonly')
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:none!important');
            $('#wi_business_type').show()
            var html = '';
            html += '<input onmouseover="this.title=this.value" readonly="readonly" type="text" data-parsley-errors-container="#element" class="form-control sku_price col-md-3 sku_price2" >'
            html += '<span class="input-group-addon">元</span>'
            $("#all_price_td").html(html)
            $('.dd_commodity_type_7').hide();
            $("#sku_code input").val('')
            $(".cost_price").val('')
        }
        else if($(this).val() == 10){
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:none!important');
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:block!important');
            $("#sku_code input").attr('readonly','readonly')
            $("#sku_code input").val('')
            $("#all_price_td input").val('')
            $("#all_price_td input").attr('readonly','readonly')
            $('#wi_business_type').hide()
            $('.dd_commodity_type_7').hide();
            $(".cost_price").val('')
        }
        else if($(this).val() == 11){
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:none!important');
            $(".win_hours").attr('style','display:block!important');
            $(".win_pz1a").attr('style','display:none!important');
            $("#sku_code input").attr('readonly','readonly')
            $("#sku_code input").val('')
            $("#all_price_td input").val('')
            $("#all_price_td input").attr('readonly','readonly')
            $('#wi_business_type').show() //显示工时业务类别选择
            $('.dd_commodity_type_7').hide();
            $("#all_price_td input").removeAttr('readonly')
            $(".cost_price").val('')
        }
        else if($(this).val() == 7 || $(this).val() == 8){
            $('.dd_commodity_type_7').find('.editable-click').text('关联到店代金券');
            if ($(this).val() == 8){
                $('.dd_commodity_type_7').find('.editable-click').text('关联到店电子券');
            }
            $('.dd_commodity_type_7').show();
            ajaxcard7Url += "&card_type=6";
            $('#shelves_sources_check_5').removeAttr('checked');
            $('#shelves_sources_check_6').removeAttr('checked');
            $('#shelves_sources_check_7').removeAttr('checked');
            $("#sku_code input").removeAttr('readonly')
            $("#all_price_td input").removeAttr('readonly')
            $("#sku_code input").val('')
            $("#all_price_td input").val('')
            $(".cost_price").val('')
        }else if($(this).val() == 1 || $(this).val() == 3 || $(this).val() == 12){

            if ($(this).val() == 12) {
                // 保养套餐-五年双保专享心悦套餐
                console.log('保养套餐-五年双保专享心悦套餐');
                // 套餐折扣只保留 2次(7折)
                $("#zhe_kou_1").hide();
                $("#zhe_kou_2").hide();
            }

            //显示城市级别
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:block!important');
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:none!important');
            $("#sku_code input").removeAttr('readonly')
            $('#wi_business_type').hide() //不显示工时业务类别选择
            $("#commodity_type_none").show() //显示地区
            $("#commodity_type_none_one").hide() //显示地区
            $("#commodity_type_none").removeClass('hidden')
            $("#sku_code input").attr('readonly','readonly')
        }else if($(this).val() == 4){
            //显示
            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:block!important');
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:none!important');
            $("#sku_code input").removeAttr('readonly')
            $('#wi_business_type').hide() //不显示工时业务类别选择
            $("#commodity_type_none").hide() //显示地区
            $("#commodity_type_none_one").show() //显示地区
            $("#commodity_type_none_one").removeClass('hidden')
            $("#sku_code input").attr('readonly','readonly')
        }
        else{
            var th = '';
            th += '<a href="#" id="all_price_th_a" data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></a>'
            // console.log(th)
            $("#all_price_th").html(th)

            $(".win_beijian").attr('style','display:none!important');
            $(".win_taocan").attr('style','display:none!important');
            // $("#all_price").css('display','block');
            // $("#all_price1").css('display','none');
            $("#sku_code input").removeAttr('readonly')
            $("#all_price_td input").removeAttr('readonly')
            $('#wi_business_type').hide()
            $(".win_hours").attr('style','display:none!important');
            $(".win_pz1a").attr('style','display:none!important');
            var html = '';
            html += '<input onmouseover="this.title=this.value" type="text" data-parsley-errors-container="#element" class="form-control sku_price col-md-3 sku_price2">'
            html += '<span class="input-group-addon" >元</span>'
            $("#all_price_td").html(html)
            $('.dd_commodity_type_7').hide();
            $("#sku_code input").val('')
            $(".cost_price").val('')
        }
        if ($("input[name='commodity_class']:checked").val() == 6){
            $("input[name='commodity_class']:checked").removeAttr('checked');
        }
    });
    $('.card7').on('click', function (){
        $('#addcard7Modal').modal('show');
    });

    //操作类型 显示弹层
    $('.card7').live('click',function(){
        obj = $(this);
        console.log('obj-1',obj)
        var shelves_sources = getShelvesSources();
        //初始化分页控件
        initcard7(ajaxcard7Url + '&shelves_sources='+shelves_sources);
        var data_id = $(this).attr('data-id');
        var text_click_id = $(this).attr('id');
        $('#addcard7Modal').find("[name='text_click_id']").val(text_click_id);
        $('#addcard7Modal').find("[name='text_click_id']").attr('data-id',data_id);
        $('#addcard7Modal').find("[name='text_click_id']").attr('page');
        $('#addcard7Modal').modal('show');
    });

    function initcard7(url) {
        $.getJSON(url, null, function (resData) {
            createPagecard7(10, 10, resData.data.total, url);//创建翻页功能按钮，翻
            $("#add-card7-tbody").empty();
            if (resData.data.total > 0) {                          //页向后台请求连接
                setcard7(url);
            }
        });
    }

    function createPagecard7(pageSize, buttons, total, url) {        //contracts_url为点击
        var shelves_sources = getShelvesSources();
        $("#card7-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
            pageSize : pageSize,
            total : total,
            maxPageButton:buttons,
            onPageClicked: function(obj, page) {    //分页事件
                if(isNaN(page)){ initcard7(ajaxcard7Url + '&shelves_sources='+shelves_sources);return false; }
                $("#add-card7-tbody").empty();
                setcard7(url+"&page="+(page+1));
                $('#addcard7Modal').find("[name='text_click_id']").attr('page',page);    //记住当前页码
            }
        });
    }
    //获取并设置列表
    function setcard7(param){
        var shelves_sources = getShelvesSources();
        var url = ajaxcard7Url + '&shelves_sources='+shelves_sources;
        if(param!=null) url=param;
        $.get(url,function(res){
            var html ='';
            var list = res.data.data;
            // console.log(list)
            $.each(list,function(i,val){
                var checkbox_obj = '';
                var check = '';
                var card7_id = obj.attr('data-id');
                if (card7_id == val.id){
                    check = 'checked';
                }
                checkbox_obj = '<label> <input type="checkbox" '+check+' class="card7_checkbox" value="'+val.id+'"><input type="hidden" value="'+val.card_name+'"> </label>';
                html += '<tr id="card7_tab_tr_'+val.id+'">' +
                    '<td>'+ (i+1) +'</td>' +
                    '<td>'+ val.belong_to +'</td>' +  // 归属
                    '<td>'+ val.card_type_name +'</td>' + // 优惠券类型
                    '<td>'+ val.id +'</td>' + // 优惠券id
                    '<td>'+ val.card_name +'</td>' + // 优惠券名称
                    '<td class="text-center">' + checkbox_obj +
                    '</td> </tr>';
            });
            $("#add-card7-tbody").html(html);

        },'json');
    }
    //搜索
    $("#card7-search").on('click',function(){
        console.log(123);
        var obj = $(this).parent();
        var card7_name = obj.find("[name='card7_name']").val();
        console.log('card7_name:',card7_name)
        var shelves_sources = getShelvesSources();
        var param = ajaxcard7Url + '&card_name='+card7_name + '&shelves_sources='+shelves_sources;
        initcard7(param);
    });

    //复选框 只能单选
    $("body").on('click','#add-card7-tbody .card7_checkbox',function(){
        var card7_id = $(this).val();
        console.log('obj:',obj)
        console.log('card_id:',card7_id)
        console.log('check:',$(this).is(':checked'))
        if($(this).is(':checked')){
            obj.parent().parent().find('.card_id').val(card7_id);
            console.log('data-id',obj.attr('data-id'))
            if (obj.attr('data-id') == 0) {
                obj.parent().next().find('.card7_name').text($(this).next().val());
                obj.parent().next().find('.card7').attr('data-id', card7_id);
            }else{
                obj.prev().text($(this).next().val());
                obj.attr('data-id', card7_id);
            }
            obj.parent().parent().find('.dd_commodity_type_7_1').hide();
            obj.parent().parent().find('.dd_commodity_type_7_2').show();
            $(this).closest('#add-card7-tbody').find("input.card7_checkbox").each(function(){
                if($(this).val()!=card7_id){
                    $(this).removeAttr('checked');
                }
            });
        }else{
            obj.parent().parent().find('.dd_commodity_type_7_2').hide();
            obj.parent().parent().find('.dd_commodity_type_7_1').show();
            obj.parent().parent().find('.card_id').val('');
            if (obj.attr('data-id') == 0) {
                obj.parent().next().find('.card7').attr('data-id', 0);
            }else{
                obj.attr('data-id', 0);
            }
        }
    });

    function del_card(obj){
        $(obj).parent().next().val('');
        $(obj).prev().attr('data-id', 0);
        $(obj).parent().parent().find('.dd_commodity_type_7_2').hide();
        $(obj).parent().parent().find('.dd_commodity_type_7_1').show();
    }

    $('.is_show_change').on('change', function (){
        if ({$is_show} == 1) {
            is_show = 1;
        }
    })

    $('.is_title_change').on('change', function (){
            is_title = 1;
    })

    //商品说明

    $('.sku_price').on('change', function (){
        var min_price = $("input[name='original_price_range_start']").val();
        var max_price = $("input[name='original_price_range_end']").val();
        if ($(this).val() > max_price){
            $("input[name='original_price_range_end']").val($(this).val());
            $("input[name='discount_price_range_end']").val($(this).val());
        }else if ($(this).val() < min_price){
            $("input[name='original_price_range_start']").val($(this).val());
            $("input[name='discount_price_range_start']").val($(this).val());
        }
    })
    function on_integral(e){
        // console.log(e.checked == true)
        // return
        if(e.checked == true){
            $(".is_integral_list").css('display','block')
        }else{
            $(".is_integral_list .sku_ckeck").each(function (i){
                if ($(this).attr("checked")) {
                    $(".is_integral_list .sku_ckeck").attr("checked",false)
                }
            })
            $(".is_integral_list").css('display','none')
            get_sku()
        }
    }

    function get_sku() {
        var sp_value_id_arr = [];
        $(".sku_ckeck:checked").each(function () {
            sp_value_id_arr.push($(this).val());
        });
        //  var sp_value_id=$("input[name='sp_value_id[]']").val();
        //组合sku逻辑
        $.get(url_get_sku_table, {sp_value_id_arr: sp_value_id_arr}, function (res) {
            var obj = $(res);
            obj.find("tr[data-sku-list]").each(function () {
                var sku_list = $(this).data('sku-list');
                var sku_price = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_price").val();
                var sku_cost_price = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".cost_price").val();
                var sku_stock = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_stock").val();
                var sku_tax = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".tax").val();
                var sku_tax_code = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".tax_code").val();
                var sku_code = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku_code").val();
                if (sku_price) {
                    $(this).find(".sku_price").val(sku_price);
                }
                if ($("input[name='dd_commodity_type']:checked").val() == 9) {
                    var th = '';
                    th += '<div href="#" data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></div>'
                    $("#all_price_th").html(th)
                }
                if (sku_stock) {
                    $(this).find(".sku_stock").val(sku_stock);
                }
                if (sku_code) {
                    $(this).find(".sku_code").val(sku_code);
                }
                if (sku_cost_price) {
                    $(this).find(".cost_price").val(sku_cost_price);
                }
                if (sku_tax) {
                    $(this).find(".tax").val(sku_tax);
                }
                if (sku_tax_code) {
                    $(this).find(".tax_code").val(sku_tax_code);
                }
                var sku_image = $("#sku").find("tr[data-sku-list='" + sku_list + "']").find(".sku-image-td").html();
                $(this).find(".sku-image-td").html(sku_image);
            });
            // console.log(obj)
            $("#sku").html(obj);
            //初始化
            batchIni();
        });
    }

    var user_data = [];
    // $.ajaxSettings.async = true;  //设为同步请求
    $.get("{:url('e3s_specific_relation/e3s_spec_part_group')}",function (res){
        var array = JSON.parse(res)
        $.each(array.data,function (i,item){
            user_data[i] = [];
            user_data[i]['name'] = item.spec_part_group
            user_data[i]['value'] = item.spec_part_group_id
        })
    })


</script>
{/block}