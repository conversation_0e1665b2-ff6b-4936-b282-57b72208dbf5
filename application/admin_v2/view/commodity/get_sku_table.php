{eq name="$package" value="1"}
<div class="layui-tab">
    <ul class="layui-tab-title">
        {volist name="tab" key="key" id="val"}
        <li class="{eq name="$key" value="1"} layui-this {/eq}">{$val.name}</li>
        {/volist}
    </ul>
    <div class="layui-tab-content">
        {volist name="tab" key="keys" id="val"}
        <div class="layui-tab-item {eq name="$keys" value="1"} layui-show {/eq} {$val.id}">
            <table class="table table-bordered price-stock">
            <thead>
            <tr class="text-center">
                {volist name="sp_title" key="k" id="v"}
                    <th sp-title="">{$v}</th>
                {/volist}
                <th>规格图片</th>
                <th>规格编码</th>
                <th id="all_price_th">
                    <a href="#" id="all_price" data-type="text" class="btn btn-primary btn-sm all_price" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></a>
                    <div id="all_price1" style="display:none;width: 63px;height: 29px;" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i class="fa fa-edit m-l-5"></i></div>
                </th>
                <th><a href="#" id="all_cost_price" data-type="text" class="btn btn-primary btn-sm editable editable-click all_cost_price" data-value="" data-placeholder="成本价" data-title="批量设置成本价">成本价<i class="fa fa-edit m-l-5"></i></a></th>
                <th><a href="#" id="all_stock" data-type="text" class="btn btn-primary btn-sm editable editable-click all_stock" data-value="" data-placeholder="请输入库存" data-title="批量设置库存">库存<i class="fa fa-edit m-l-5"></i></a></th>
                <th style="width: 110px;"><a href="#" id="all_coefficient" data-type="text" class="btn btn-primary btn-sm editable editable-click all_coefficient" data-value="" data-placeholder="发货系数" data-title="批量发货系数">发货系数<i class="fa fa-edit m-l-5"></i></a></th>
                <th><a href="#" id="all_tax" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax" data-value="" data-placeholder="税率" data-title="批量设置税率">税率<i class="fa fa-edit m-l-5"></i></a></th>
                <th ><a href="#" id="all_tax_code" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax_code" data-value="" data-placeholder="税务编码" data-title="批量设置税务编码">税务编码<i class="fa fa-edit m-l-5"></i></a></th>
                <th class="dd_commodity_type_7" {if !in_array($bj_type, [7,8])}style="display: none;"{/if}><a href="javascript:;" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="税务编码" data-title="批量设置税务编码">关联代金券</a></th>
            </tr>
            </thead>
            <?php foreach($sku_data as $key1=>$val1):?>
                <tr  data-sku-list="{$val1['sku_list']}">
                    <?php foreach($val1['group'] as $key2=>$val2):?>
                        <td data-sp-value-id="{$val2['id']}">{$val2['sp_value_name']}</td>
                    <?php endforeach;?>
                    <td class="text-left belong_code" data-belong_code="{$val.id}">{$val.name}</td>
                    <input type="hidden" name="city_type" class="city_type" value="{$val.id}">
                    <td class="text-left sku-image-td">

                        <div class="parents-add">
                            <a class="btn btn-success btn-sm add-sku-image" data-sku-image=""><i class="fa fa-lg fa-plus"></i>添加</a>
                        </div>
                        <div class="parents-update" style="display: none;">
                                     <span class="dndc-example1">
		                             <a class="btn btn-success btn-sm view-sku-image" ><i class="fa fa-x fa-eye "></i>预览</a>
		                                <div class="pop-preview">
			                                <img src="">
		                               </div>
	                               </span>

                            <a class="btn btn-white btn-sm update-sku-image" ><i class="fa fa-undo"></i></i>替换</a>
                            <a class="btn btn-default btn-sm del-sku-image" ><i class="fa fa-trash-o"></i></i>删除</a>
                        </div>
                    </td>
                    <td id="element" style="display: none">
                        <input type="hidden" name="hours_id" class="hours_id">
                    </td>
                    <td id="element" class="text-left">
                        <div class="col-md-12 p-l-0" id="sku_code" style="width: 100px;">
                            <input type="text" data-parsley-required="true" onmouseover="this.title=this.value" class="form-control sku_code" data-parsley-length="[1,2000000]" >
                        </div>
                        <p><a href="javascript:void(0);" class="win_beijian {if $bj_type != 9}hidden{/if}">选择</a></p>
                        <p><a href="javascript:void(0);" class="win_pz1a {if $bj_type != 10}hidden{/if}">选择</a></p>
                        <p><a href="javascript:void(0);" class="win_hours {if $bj_type != 11}hidden{/if}">选择</a></p>
                        <p><a href="javascript:void(0);" data-address="{$val.id}" class="win_taocan {if !in_array($bj_type,[1,3,4,6,12,41])}hidden{/if}">选择</a></p>

                    </td>
                    <!--价格-->
                    <td id="element" class="text-left">
                        <div class="input-group col-md-3" id="all_price_td">
                            <input type="text" onmouseover="this.title=this.value" data-parsley-required="true" data-parsley-errors-container="#element" class="form-control sku_price col-md-3 sku_price2" data-parsley-required="true">
                            <span class="input-group-addon" >元</span>
                        </div>

                    </td>
                    <!--成本价-->
                    <td id="element" class="text-left">
                        <div class="input-group col-md-3" id="all_cost_price1" >
                            <input type="text" data-parsley-errors-container="#element" value="0" class="form-control cost_price col-md-3" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">

                            <span class="input-group-addon" >元</span>
                        </div>

                    </td>
                    <!--库存-->
                    <td id="element" class="text-left">
                        <div class="col-md-3 p-l-0">
                            <input type="text" data-parsley-required="true" class="form-control sku_stock" data-parsley-required="true" data-parsley-type="integer">
                        </div>
                    </td>
                    <!--发货系数-->
                    <td id="element" class="text-left">
                        <input type="text" class="form-control delivery_coefficient" data-parsley-required="true" data-parsley-type="integer">
                    </td>
                    <!--税率-->
                    <td id="element" class="text-left">
                        <div class="input-group col-md-3" >
                            <input type="text" data-parsley-errors-container="#element" class="form-control tax col-md-3" data-parsley-pattern="/^(0|6|9|11|13|16|17)$/" >
                        </div>

                    </td>

                    <!--税务编码-->
                    <td id="element" class="text-left">
                        <div class="input-group col-md-3" >
                            <input type="text" data-parsley-errors-container="#element" class="form-control tax_code col-md-3" data-parsley-pattern="/^\d{19}$/" data-parsley-required="true" >
                        </div>

                    </td>

                    <td id="element" class="text-left dd_commodity_type_7" {if !in_array($bj_type, [7,8])}style="display: none;"{/if}>
                        <div class="input-group col-md-3" >
                            <div class="dd_commodity_type_7_1"><span class="card7" data-id="0" style="color: blue;">选择</span></div>
                            <div class="dd_commodity_type_7_2" style="display: none;"><span class="card7_name" style="color: red;">券名称 </span> <span class="card7" data-id="" style="color: blue;">替换 </span><span onclick="del_card(this)" style="color: blue;">删除</span></div>
                            <input type="hidden" class="card_id" value="">
                        </div>

                    </td>

                </tr>
            <?php endforeach;?>
            </tbody>
        </table>
        </div>
        {/volist}
    </div>
</div>
{/eq}
{empty name="$tab"}
<table class="table table-bordered price-stock">
    <thead>
    <tr class="text-center">
        <?php foreach($sp_title as $val):?>
            <th sp-title="">{$val}</th>
        <?php endforeach; ?>
        <th>规格图片</th>
        <th>规格编码</th>
        <th id="all_price_th">
            <a href="#" id="all_price" data-type="text" class="btn btn-primary btn-sm all_price" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></a>
        </th>
        <th><a href="#" id="all_cost_price" data-type="text" class="btn btn-primary btn-sm editable editable-click all_cost_price" data-value="" data-placeholder="成本价" data-title="批量设置成本价">成本价<i class="fa fa-edit m-l-5"></i></a></th>
        <th><a href="#" id="all_stock" data-type="text" class="btn btn-primary btn-sm editable editable-click all_stock" data-value="" data-placeholder="请输入库存" data-title="批量设置库存">库存<i class="fa fa-edit m-l-5"></i></a></th>
        <th style="width: 110px;">
            <a href="#" id="all_coefficient" data-type="text" class="btn btn-primary btn-sm editable editable-click all_coefficient" data-value="" data-placeholder="发货系数" data-title="批量发货系数">发货系数<i class="fa fa-edit m-l-5"></i></a></th>
        <th>
            <a href="#" id="all_tax" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax" data-value="" data-placeholder="税率" data-title="批量设置税率">税率<i class="fa fa-edit m-l-5"></i></a></th>
        <th id="taxt_code_th">
            <a href="#" id="all_tax_code" data-type="text" class="btn btn-primary btn-sm editable editable-click all_tax_code" data-value="" data-placeholder="税务编码" data-title="批量设置税务编码">税务编码<i class="fa fa-edit m-l-5"></i></a></th>
        <th class="dd_commodity_type_7" {if !in_array($bj_type, [7,8])}style="display: none;"{/if}>
        <a href="javascript:;" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="税务编码" data-title="批量设置税务编码">关联代金券</a></th>




    </tr>
    </thead>

    <?php foreach($sku_data as $key1=>$val1):?>
        <tr  data-sku-list="{$val1['sku_list']}">
            <?php foreach($val1['group'] as $key2=>$val2):?>
                <td data-sp-value-id="{$val2['id']}">{$val2['sp_value_name']}</td>
            <?php endforeach;?>
            <td class="text-left sku-image-td">

                <div class="parents-add">
                    <a class="btn btn-success btn-sm add-sku-image" data-sku-image=""><i class="fa fa-lg fa-plus"></i>添加</a>
                </div>
                <div class="parents-update" style="display: none;">
                                     <span class="dndc-example1">
		                             <a class="btn btn-success btn-sm view-sku-image" ><i class="fa fa-x fa-eye "></i>预览</a>
		                                <div class="pop-preview">
			                                <img src="">
		                               </div>
	                               </span>

                    <a class="btn btn-white btn-sm update-sku-image" ><i class="fa fa-undo"></i></i>替换</a>
                    <a class="btn btn-default btn-sm del-sku-image" ><i class="fa fa-trash-o"></i></i>删除</a>
                </div>
            </td>
            <td id="element" style="display: none">
                <input type="hidden" name="hours_id" class="hours_id">
            </td>
            <td id="element" class="text-left">
                <div class="col-md-12 p-l-0" id="sku_code" style="width: 100px;">
                    <input type="text" onmouseover="this.title=this.value" class="form-control sku_code" data-parsley-length="[1,2000]" >
                </div>
                <p><a href="javascript:void(0);" class="win_beijian {if $bj_type != 9}hidden{/if}">选择</a></p>
                <p><a href="javascript:void(0);" class="win_pz1a {if $bj_type != 10}hidden{/if}">选择</a></p>
                <p><a href="javascript:void(0);" class="win_hours {if $bj_type != 11}hidden{/if}">选择</a></p>
                <p><a href="javascript:void(0);" class="win_taocan {if !in_array($bj_type,[1,3,4,6,12,41])}hidden{/if}">选择</a></p>

            </td>
            <td id="element" class="text-left">
                <div class="input-group col-md-3" id="all_price_td">
                    <input type="text" onmouseover="this.title=this.value" data-parsley-errors-container="#element" class="form-control sku_price col-md-3 sku_price2" data-parsley-required="true">
                    <span class="input-group-addon" >元</span>
                </div>

            </td>

            <td id="element" class="text-left">
                <div class="input-group col-md-3" >
                    <input type="text" data-parsley-errors-container="#element" class="form-control cost_price col-md-3">

                    <span class="input-group-addon" >元</span>
                </div>

            </td>

            <td id="element" class="text-left">
                <div class="col-md-3 p-l-0">
                    <input type="text" class="form-control sku_stock" data-parsley-required="true" data-parsley-type="integer">
                </div>
            </td>

            <td id="element" class="text-left">
                <input type="text" class="form-control delivery_coefficient" data-parsley-required="true" data-parsley-type="integer">
            </td>

            <td id="element" class="text-left">
                <div class="input-group col-md-3" >
                    <input type="text" data-parsley-errors-container="#element" class="form-control tax col-md-3 invoice_rate_{$key1}" data-parsley-pattern="/^(0|6|9|11|13|16|17)$/" >
                </div>

            </td>


            <td id="element" class="text-left">
                <div class="input-group col-md-3" >
                    <input type="text" data-parsley-errors-container="#element" data-i="{$key1}" class="select_invoice_type form-control tax_code col-md-3 invoice_type_{$key1} " data-parsley-pattern="/^\d{19}$/" data-parsley-required="true" >
                </div>

            </td>

            <td id="element" class="text-left dd_commodity_type_7" {if !in_array($bj_type, [7,8])}style="display: none;"{/if}>
            <div class="input-group col-md-3" >
                <div class="dd_commodity_type_7_1"><span class="card7" data-id="0" style="color: blue;">选择</span></div>
                <div class="dd_commodity_type_7_2" style="display: none;"><span class="card7_name" style="color: red;">券名称 </span> <span class="card7" data-id="" style="color: blue;">替换 </span><span onclick="del_card(this)" style="color: blue;">删除</span></div>
                <input type="hidden" class="card_id" value="">
            </div>

            </td>

        </tr>

    <?php endforeach;?>
    </tbody>
</table>
{/empty}





<script>
    $(document).ready(function(){
        if($("input[name='dd_commodity_type']:checked").val() == 9){
            var th = '';
            th += '<div href="#" data-type="text" class="btn btn-primary btn-sm" data-parsley-required="true" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></div>'
            $("#all_price_th").html(th)
            $("#sku_code input").attr('readonly','readonly')
            $("#all_price_td input").attr('readonly','readonly')

        }else if($("input[name='dd_commodity_type']:checked").val() == 11){
            $("#sku_code input").attr('readonly','readonly')
        }else if($("input[name='dd_commodity_type']:checked").val() == 10){
            $("#sku_code input").attr('readonly','readonly')
            $("#all_price_td input").attr('readonly','readonly')
        }else if($("input[name='dd_commodity_type']:checked").val() == 1 || $("input[name='dd_commodity_type']:checked").val() == 3 || $("input[name='dd_commodity_type']:checked").val() == 12){
            $("#sku_code input").val('')
            $("#all_price_td input").val('')
            $("#all_cost_price1 input").val('')
            <?php foreach($tab as $key=>$val) :?>
            <?php foreach($sku_data as $k=>$v) :?>
            var sp_value_list = "{$v.sku_list}"
            var appoint = sp_value_list.replace(/,/g, '');
            var address_key = "{$val.id}"
            var taocan_no = localStorage.getItem("taocan_no_"+address_key+"_"+appoint);
            var taocan_price = localStorage.getItem("taocan_price_"+address_key+"_"+appoint);
            if(taocan_no !== null){
                var data_tr = $(".layui-tab-content ."+address_key+" tbody tr" )
                $.each(data_tr,function (key,value){
                    var sku_list = $(this).data('sku-list')
                    var new_appoint = sku_list.replace(/,/g, '');
                    if(appoint == new_appoint){
                        $(this).find('.sku_code').val(taocan_no)
                        $(this).find('.sku_price').val(taocan_price)
                    }
                })
            }

            <?php endforeach;?>
            <?php endforeach;?>

            $("#sku_code input").attr('readonly','readonly')
            $("#all_price_td input").attr('readonly','readonly')
        }else if($("input[name='dd_commodity_type']:checked").val() == 4){
            $("#sku_code input").val('')
            $("#all_price_td input").val('')
            <?php foreach($tab as $key=>$val) :?>
            <?php foreach($sku_data as $k=>$v) :?>
            var sp_value_list = "{$v.sku_list}"
            var appoint = sp_value_list.replace(/,/g, '');
            var address_key = "{$val.id}"
            var taocan_no = localStorage.getItem("taocan_no_"+address_key+"_"+appoint);
            var taocan_price = localStorage.getItem("taocan_price_"+address_key+"_"+appoint);
            if(taocan_no !== null){
                var data_tr = $(".layui-tab-content ."+address_key+" tbody tr" )
                $.each(data_tr,function (key,value){
                    var sku_list = $(this).data('sku-list')
                    var new_appoint = sku_list.replace(/,/g, '');
                    if(appoint == new_appoint){
                        $(this).find('.sku_code').val(taocan_no)
                        $(this).find('.sku_price').val(taocan_price)
                    }
                })
            }

            <?php endforeach;?>
            <?php endforeach;?>
            $("#sku_code input").attr('readonly','readonly')
            $("#all_price_td input").attr('readonly','readonly')
        }
        else{
            var th = '';
            th += '<a href="#" id="all_price" data-type="text" class="btn btn-primary btn-sm all_price" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i  class="fa fa-edit m-l-5"></i></a>'
            $("#all_price_th").html(th)
            $("#sku_code input").removeAttr('readonly')
            $("#all_price_td input").removeAttr('readonly')
        }




    //添加商品
    $(".select_invoice_type").on('click',function (e) {
        var selecti = $(this).data("i")
        e.preventDefault();
        var obj=$(this);
        $.getJSON(url_select_invoice_type,{tax_code:$("#tax_code").val(),tax_name:$("#tax_name").val(),short_name:$("#short_name").val()},function (res) {
            var data_list=res.data;
            var pagecount = 1//默认第一页
            var result = [];
            $("#invoice_type_count").empty().append(data_list.length)
            if (data_list.length === 0) {
                return ;
            }
            for (var i = 0, len = data_list.length; i < len; i += 10) {
                result.push(data_list.slice(i, i + 10));
            }
            $(".invoice-type-tb").empty()
            html = "";
            if (pagecount == 1) {
                var dataFirst = result[0]
                $.each(dataFirst, function (kb, val) {
                    kb++
                    html += '<tr invoice_type_id="'+val.id+'">' +
                        '<td class="text-left">'+ kb++ +'</td>' +
                        '<td class="text-left">'+val.tax_code+'</td>' +
                        '<td >'+ val.tax_name +'</td>' +
                        '<td >'+val.short_name+'</td>' +
                        '<td >' + val.tax_rate + '</td>'+
                        '<td >是</td>'+
                        '<td >' +truncateString( val.desc)  + '</td>'+
                        '<td ><a class="select_tax_code" data-i="'+selecti+'" data-rate="'+val.first_rate+'" data-code="'+val.tax_code+'" >选择</a></td>'+
                        '</tr>';
                })
                $(".invoice-type-tb").html(html);
            }
            $("#invoice_type_page").Page({

                totalPages: data_list.length,//分页总数
                liNums: 9,//分页的数字按钮数(建议取奇数)
                activeClass: 'activP', //active 类样式定义
                callBack: function (page) {
                    pagecount = page
                    var arr = result[page - 1]
                    $(".invoice-type-tb").empty()
                    var html = ''
                    var i = 1;

                    $.each(arr, function (k, val) {
                        k++;
                        html += '<tr invoice_type_id="'+val.id+'">' +
                            '<td class="text-left">'+k+'</td>' +
                            '<td class="text-left">'+val.tax_code+'</td>' +
                            '<td >'+ val.tax_name +'</td>' +
                            '<td >'+val.short_name+'</td>' +
                            '<td >' + val.tax_rate + '</td>'+
                            '<td >是</td>'+
                            '<td >' + truncateString(val.desc)  + '</td>'+
                            '<td ><a class="select_tax_code" data-i="'+selecti+'" data-rate="'+val.first_rate+'" data-code="'+val.tax_code+'" >选择</a></td>'+
                            '</tr>';

                    })

                    $(".invoice-type-tb").html(html);
                }
            });
            $("#invoice-type-modal").data("comm-id",obj.data('comm-id'));
            $("#invoice-type-modal").data("comm-set-id",obj.data('comm-set-id'));
            $("#invoice-type-modal").data('dd-commodity-type',obj.data('dd-commodity-type'));
            $("#invoice-type-modal").data('type','add');
            //  $(".invoice-type-tb").html(html);
            $("#invoice-type-modal").modal('show');
            //dlr初始化
            $("#invoice-type-modal").find(".sku-dlr").val("").data("sku-dlr","");
        });
    })
    });

    $(".select_tax_code").live('click',function (e) {
        $("#invoice-type-modal").modal('hide');
        var classn = ".invoice_type_"+$(this).data("i")
        var rate = ".invoice_rate_"+$(this).data("i")
        $(rate).val($(this).data("rate"))
        $(classn).val($(this).data("code"))
    })


    $("#taxt_code_th input").live('click',function(){
        $.getJSON(url_select_invoice_type,{tax_code:$("#tax_code").val(),tax_name:$("#tax_name").val(),short_name:$("#short_name").val()},function (res) {
            var data_list=res.data;
            $(".invoice-type-tb").empty()
////////////////////////////////////////////////////////////////////////////////////////////////////////////
            var pagecount = 1//默认第一页
            $("#invoice_type_count").empty().append(data_list.length)
            var result = [];
            if (data_list.length === 0) {
                return ;
            }
            for (var i = 0, len = data_list.length; i < len; i += 10) {
                result.push(data_list.slice(i, i + 10));
            }

            html = "";
            if (pagecount == 1) {
                var dataFirst = result[0]
                $.each(dataFirst, function (ki, val) {
                    ki++;
                    html += '<tr invoice_type_id="'+val.id+'">' +
                        '<td class="text-left">'+ ki++ +'</td>' +
                        '<td class="text-left">'+val.tax_code+'</td>' +
                        '<td >'+ val.tax_name +'</td>' +
                        '<td >'+val.short_name+'</td>' +
                        '<td >' + truncateString(val.tax_rate) + '</td>'+
                        '<td >是</td>'+
                        '<td >' + truncateString(val.desc)  + '</td>'+
                        '<td ><a class="batch_tax_code" data-rate="'+val.first_rate+'" data-code="'+val.tax_code+'" >选择</a></td>'+
                        '</tr>';
                })
                $(".invoice-type-tb").html(html);
            }
            $("#invoice_type_page").Page({

                totalPages: data_list.length,//分页总数
                liNums: 9,//分页的数字按钮数(建议取奇数)
                activeClass: 'activP', //active 类样式定义
                callBack: function (page) {
                    pagecount = page
                    var arr = result[page - 1]
                    $(".invoice-type-tb").empty()
                    var html = ''
                    var i = 1;

                    $.each(arr, function (k, val) {

                        k++;
                        html += '<tr invoice_type_id="'+val.id+'">' +
                            '<td class="text-left">'+k+'</td>' +
                            '<td class="text-left">'+val.tax_code+'</td>' +
                            '<td >'+ val.tax_name +'</td>' +
                            '<td >'+val.short_name+'</td>' +
                            '<td >' + val.tax_rate + '</td>'+
                            '<td >是</td>'+
                            '<td >' + truncateString(val.desc)  + '</td>'+
                            '<td ><a class="batch_tax_code"  data-rate="'+val.first_rate+'" data-code="'+val.tax_code+'" >选择</a></td>'+
                            '</tr>';

                    })

                    $(".invoice-type-tb").html(html);
                }
            });
            $("#invoice-type-modal").modal('show');
        });
    })

   



</script>
