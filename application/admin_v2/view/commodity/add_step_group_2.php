{extend name="public:base_layout" /}

{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<style>
    .file-drop-zone-title {
        color: #aaa;
        font-size: 20px;
        padding: 25px 15px;
    }
    .kv-file-zoom,.file-footer-buttons, .file-drag-handle, .file-upload-indicator  {
        display: none;
    }
    .progress{
        display: none;
    }
</style>
{/block}

{block name="content"/}

<div class="panel-body">
    {include file="commodity:commodity_nav" /}
    <div class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">商品基本信息</legend>
        <form id="add-step-2"class="form-horizontal form-bordered" data-parsley-trigger="change">
            <div class="form-group">
                <label class="control-label col-md-2">商品分类<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">

                    {$commodity_type->pparent_type_name} > {$commodity_type->parent_type_name} > {$commodity_type->comm_type_name}
                    <a href="{:url('addStep1')}" class="btn btn-white btn-sm m-r-5 m-b-5">选择分类</a>
                    <input type="hidden" name="comm_type_id" value="{$commodity_type->id}">

                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" name="commodity_name" placeholder="请输入商品名称" class="form-control " data-parsley-required="true" data-parsley-length="[3, 50]">
                    <p class="m-t-5">商品标题名称长度至少3个字符，最长50个汉字</p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">到店商品类型:</label>
                <div class="col-md-10">
                    <!--到店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-五年双保升级权益套餐 6保养套餐-其他 7到店代金券-->
                    <label class="radio-inline">
                        <input type="radio"  name="dd_commodity_type" value="9" data-parsley-required="true" data-parsley-multiple="radiorequired" /> 到店备件商品
                    </label>
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio"  name="dd_commodity_type" value="1" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-老友惠保养套餐-->
<!--                    </label>-->
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio"  name="dd_commodity_type" value="3" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-心悦保养套餐-->
<!--                    </label>-->
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio"  name="dd_commodity_type" value="4" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-五年双保升级权益套餐-->
<!--                    </label>-->
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio"  name="dd_commodity_type" value="41" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-五年双保升级权益套餐(全合成)-->
<!--                    </label>-->
                    <label class="radio-inline">
                        <input type="radio"  name="dd_commodity_type" value="6" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 保养套餐-其他
                    </label>
<!--                    <label class="radio-inline">-->
<!--                        <input type="radio"  name="dd_commodity_type" value="7" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 到店代金券-->
<!--                    </label>-->
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">收录推荐系统:</label>
                <div class="col-md-10">
                    <!--0否1是-->
                    <label class="radio-inline">
                        <input type="radio"  name="arrival_bdp" value="9" data-parsley-required="true" data-parsley-multiple="radiorequired" /> 否
                    </label>
                    <label class="radio-inline">
                        <input type="radio"  name="arrival_bdp" value="1" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 是
                    </label>
                    <p class="m-t-5">部分商品不能推给推荐系统，因为是组合销售商品，如车联保养套餐</p>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">品牌:</label>
                <div class="col-md-5">
                    <select name="brands_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id">
                        <option value="">
                            请选择
                        </option>
                        <?php foreach($brands_list as $key=>$val):?>
                            <option value="{$val['id']}" >{$val['brands_name']}</option>
                        <?php endforeach;?>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品编码:</label>
                <div class="col-md-5">
                    <input type="text" name="commodity_code" placeholder="请输入商品编码" class="form-control "  data-parsley-length="[1, 200]">

                </div>
            </div>
            <div class="form-group" style="display: none">
                <label class="control-label col-md-2">价格范围<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">

                    <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">原价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " name="original_price_range_start" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly>
                    </div>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " name="original_price_range_end" placeholder="最高价格"  data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly>
                    </div>

                    <label class="control-label col-md-2 width-80 p-l-0 m-r-0 text-right">现价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " name="discount_price_range_start" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly>

                    </div>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " name="discount_price_range_end" placeholder="最高价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" readonly>
                    </div>

                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2 ">商品特性<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-5 p-l-0">
                        <label class="control-label col-md-4 width-100 m-r-0 p-l-0 text-left ">是否纯正商品<i class="m-r-3 text-danger">*</i>:</label>
                        <label class="radio-inline">
                            <input type="radio"  checked name="is_pure" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="radiorequired" /> 是
                        </label>

                        <label class="radio-inline">
                            <input type="radio"  name="is_pure" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 否
                        </label>
                    </div>
                    <div class="col-md-7 p-l-0">
                        <label class="control-label col-md-4 width-100 m-r-0 p-l-0 text-right">是否商城商品<i class="m-r-3 text-danger">*</i>:</label>
                        <label class="radio-inline">
                            <input type="radio" checked name="is_shop" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="is_shop" /> 是
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="is_shop" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="is_shop"/> 否
                        </label>

                    </div>
                    <?php if ($admin_type==1):?>
                    <div class="col-md-5 p-l-0">
                        <label class="control-label col-md-4 width-80 m-r-0 p-l-0 text-left">上架源<i class="m-r-3 text-danger">*</i>:</label>
                        <?php foreach ($shelves_sources as $key=>$val):?>
                            <?php if (!in_array($key,[8])):?>
                            <label class="checkbox-inline">
                                <input type="checkbox" class="shelves_sources_check" name="shelves_sources[]" value="{$key}" checked>{$val}
                            </label>
                            <?php endif;?>
                        <?php endforeach;?>
                    </div>
                    <br>
                    <?php endif;?>




                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品种类:</label>


                    <div class="col-md-5 ">
<!--                        --><?php //foreach ($commodity_class as $key=>$val):?>

                            <label class="radio-inline">
                                <input type="radio"  name="commodity_class" value="1" checked id="radio-required" data-parsley-required="true" data-parsley-multiple="commodtiy_class" /> {$commodity_class[1]}
                            </label>
                            <label class="radio-inline">
                                <input type="radio"  name="commodity_class" value="8"  id="radio-required" data-parsley-required="true" data-parsley-multiple="commodtiy_class" /> {$commodity_class[8]}
                            </label>
<!--                        --><?php //endforeach;?>

<!--                        <label class="radio-inline">-->
<!--                            <input type="radio" checked name="commodity_class" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="commodtiy_class" /> 实物商品-->
<!--                        </label>-->
<!---->
<!---->
<!--                        <label class="radio-inline">-->
<!--                            <input type="radio" name="commodity_class" id="radio-required2" value="2" data-parsley-required="true" data-parsley-multiple="commodtiy_class"/> 虚拟商品-->
<!--                        </label>-->
<!---->
<!--                        <label class="radio-inline">-->
<!--                            <input type="radio" name="commodity_class" id="radio-required2" value="3" data-parsley-required="true" data-parsley-multiple="commodtiy_class"/> 电子卡劵-->
<!--                        </label>-->

                    </div>


            </div>


            <div class="form-group">
                <label class="control-label col-md-2">订单提示:</label>
                <div class="col-md-5 ">
                    <input type="text" class="form-control " name="tips" id="tips" data-toggle="tooltip" title="字数不能超过50个。" maxlength="50" >
                </div>

            </div>


            <div class="form-group" id="commodity_card_ids" style="display: none;">
                <label class="control-label col-md-2">卡券:</label>
                <div class="col-md-5">

                        <input type="text" class="form-control commodity_card" onkeydown="onlyView();" name="commodity_card_ids_name" placeholder="选择优惠券" >


                    <input type="hidden" class="form-control hide_card_ids" name="commodity_card_ids" >
                </div>
            </div>

            <div class="form-group" id="mark_coupon_id" style="display: none;">
                <label class="control-label col-md-2">平台卡券id<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control " name="mark_coupon_id" placeholder="请输入平台卡券id">
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品单位<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control " name="unit" placeholder="请输入商品单位" data-parsley-required="true" data-parsley-length="[0,4]">
                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">供应商渠道:</label>
                <div class="col-md-5">
                    <select id="template_guid" class="form-control width-300" name="supplier">
                        <option value="">请选择</option>
                        <?php foreach ($supp_list as $k => $vo): ?>
                            <option value="{$vo['value_code']}">
                                {$vo['county_name']}
                            </option>
                        <?php endforeach;?>
                    </select>
                </div>
            </div>
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">税务编码:</label>-->
<!--                <div class="col-md-5">-->
<!--                    <input type="text" class="form-control " name="tax_code" placeholder="请输入税务编" >-->
<!--                </div>-->
<!--            </div>-->

            <div class="form-group">
                <label class="control-label col-md-2">排序<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control " name="sort" placeholder="请输入排序" data-parsley-required="true" data-parsley-range="[0,10000]">
                </div>
            </div>
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">适用车型:</label>-->
<!--                <div class="col-md-5">-->
<!--                    <input type="text" data-toggle="modal" class="form-control " name="car_series_name" placeholder="选择车型" data-target="#car-modal">-->
<!--                </div>-->
<!--            </div>-->


            <div class="form-group">
                <label class="control-label col-md-2">商品图片<i class="m-r-3 text-danger">*</i>:<br><span>(用于商品详情的头图)</span></label>

                <div class="col-md-10  image_group">

                    <div class="dndc-upload-pic goods_pic" sp-id="0" data-image-id="0" id="image-id-0">
                        <label class="sp-value m-t-10 m-l-10">默认</label>
                        <label>
                            <a href="javascript:;" class="btn-image btn btn-primary m-r-5">上传图片</a>
                        </label><p>5张,200K,jpg,png</p><ul></ul>
                    </div>

                    <input type="file" name="image" class="hide"  id="goods-image-input">
                </div>


            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品视频</label>
                <div class="col-md-10 ">
                    <input id="file_video" name="file_video" type="file" class="file" data-overwrite-initial="false">
                    <input id="video_img" name="video_img" type="hidden" >
                </div>


            </div>

<!--            <legend class="pull-left width-full m-t-15">商品详情描述</legend>-->
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">商品详情<i class="m-r-3 text-danger">*</i>:</label>-->
<!--                <div class="col-md-9 ">-->
<!--                    <script id="content" name="content" type="text/plain" style="width:100%;height:300px;z-index:999;"></script>-->
<!--                </div>-->
<!--            </div>-->

            <!-- <legend class="pull-left width-full m-t-15">物流信息</legend>
             <div class="form-group">
                 <label class="control-label col-md-3">物流:</label>
                 <div class="col-md-9 ">

                     <div class="col-md-2 p-l-0">
                         <div class="radio">
                             <label>
                                 <input type="radio" checked checked name="is_mail" value="1" id="radio-required" data-parsley-required="true" data-parsley-multiple="radiorequired" /> 支持快递
                             </label>
                         </div>
                         <div class="radio">
                             <label>
                                 <input type="radio" name="is_mail" id="radio-required2" value="0" data-parsley-required="true" data-parsley-multiple="radiorequired"/> 不支持快递
                             </label>
                         </div>
                     </div>

                     <div class="input-group col-md-2 m-t-10">
                         <input type="text" class="form-control" name="mail_price" placeholder="快递费">
                         <span class="input-group-addon ">元</span>
                     </div>
                 </div>


             </div>-->
            <input type="hidden" name="action" value="add">
            <input type="hidden" name="set_type" value="{$set_type}">

            <div class="text-center m-t-20" style="position: sticky;bottom: 0px;height:80px;background-color: #fff;line-height: 80px;">
<!--                <button type="button" id="put-form-view" class="btn btn-primary m-r-5 m-b-5 btn-sm">预览</button>-->
                <button id="put-form" type="button" class="btn btn-primary m-r-5 m-b-5 btn-sm">下一步</button>
            </div>
        </form>


    </div>

    <!----begin 选择车系---->
    <div class="modal fade" id="car-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择车型</h4>
                </div>

                <div class="modal-body car-series ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <div class="form-group">
                        <div class="col-md-2 p-r-0 checkbox">
                            <label><input type="checkbox" id="check_all" class="car_checkbox ">全选</label>
                        </div>
                        {volist name="brand_s" id="v" key="k"}
                        <div class="col-md-2 p-r-0 checkbox">
                            <label><input type="checkbox"   class="car-brand-type"  data-brand-type="{$k}" >{$v}</label>
                        </div>
                        {/volist}

                    </div>
                    <legend class="pull-left width-full m-t-15"></legend>
                    <?php foreach($car_series_list as $key=>$val):?>
                        <div class=" form-group ">
                            <div class="col-md-2 p-r-0 checkbox">
                                <label><input type="checkbox"  class="car_checkbox " id="{$key}"  data-parent="check_all"> {$val['car_series_name']}</label>
                            </div>

                            <div class="col-md-9 checkbox">
                                <?php foreach($val['sub_data'] as $key1=>$val2):?>
                                    <label class="m-r-5"><input type="checkbox" class="car_checkbox min" data-sub-brand-type="{$val2['brand_type']}" data-parent="{$key}" value="{$key1}" data-car-series="{$val2['sub_car_series_name']}" name="car_series[]">{$val2['sub_car_series_name']}</label>
                                <?php endforeach;?>
                            </div>

                        </div>
                        <legend class="pull-left width-full m-t-15"></legend>
                    <?php endforeach;?>

                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white btn-sm" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary btn-sm"  id="add-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 预览二维码---->
    <div class="modal fade" id="preview-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">扫码二维码预览</h4>
                </div>

                <div class="modal-body qc_code text-center ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>

                    <img src="">

                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white btn-sm" data-dismiss="modal">关闭</a>
                </div>
            </div>
        </div>
    </div>

    <!-- begin 选择优惠券 -->
    <div class="modal fade" id="card-modal">
        <div class="modal-dialog">
            <div class="modal-content" style="width: 800px;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择优惠券</h4>
                </div>
                <div class="modal-body car-series ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <form class="form search-form">
                        <label>优惠券名称：
                            <input type="text" id="card_name" class="form-control input-sm element width-200" placeholder="请输入优惠券名称" aria-controls="data-table" >
                        </label>
                        <button id="card-search" type="button" class=" btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                    </form>
                    <div class="table-scrollable">
                        <table id="card_form" class="table table-bordered">
                            <thead>
                            <th class="text-center">序号</th>
                            <th class="text-center">归属</th>
                            <th class="text-center">优惠券类型</th>
                            <th class="text-center">优惠券id</th>
                            <th class="text-center">优惠券名称</th>
                            <th class="text-center">操作</th>
                            </thead>
                            <tbody id="card-body">
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <ul class="pagination"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="card_add" data-dismiss="modal">确定</a>
                </div>
            </div>
        </div>
    </div>
    <!-- end 选择优惠券-->


    <!-- begin 选择备件 -->
    <div class="modal fade" id="bj-modal">
    </div>
    <!-- end 选择备件-->

    <!-- begin 通过备件查看车型 -->
    <div class="modal fade" id="bj-car-modal">
    </div>
    <!-- end 通过备件查看车型-->


</div>
{/block}
{block name="script"/}
{include  file="public/baidu_ueditor_js" /}
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>

<script src="__STATIC__admin_v2/js/purify.min.js"></script>
<script src="__STATIC__admin_v2/js/sortable.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput_locale_zh.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js?v=2611"></script>


<script>
    var url_get_sku_table="{:url('getSkuTable')}";
    var url_get_bj_table="{:url('getBjTable')}";
    var url_get_bj_car_table="{:url('getBjCarTable')}";
    var ajaxGetCard_url  ="{:url('ajaxLiveGetCard')}";
    var ajaxGetBj_url  ="{:url('e3sSparePart/ajaxGetBj')}";
    var ajaxGetBjCar_url  ="{:url('e3sSparePart/select_car_series')}";

    $(function(){
        //初始化
        $('.goods_pic>ul').sortable({
            disabled: true,
        });

        batchIni();

        $("#put-form,#put-form-view").on('click',function(){
            var sku_list  =getSkuList();
            var image_list=getImageList();
            var $form=$("#add-step-2");
            var validate=$form.psly().validate();  //表单验证
            if(!validate) return false;
            var form_data=$form.serialize();
            console.log(form_data);
            if($(this).attr("id")=='put-form-view'){
                var is_preview=1;
            }else {
                var is_preview=0;
            }
            if (image_list.length<=0){
                layer.msg('默认图片不能为空');
                return false;
            }
            var alert_obj=$("#alert-danger");
            var data=$.param({is_preview:is_preview,car_series_id:car_series_id,'sku_list':sku_list,'image_list':image_list}) + '&' + form_data
            // form_data.push({name:'sku_list',value:sku_list});
            if (is_preview){
                Custom.ajaxPost("{:url('saveGroup')}",data,alert_obj,null,function(res){
                    if (res.error==0){
                        var url=res.data.com_url;
                        console.log(url);

                        $("#mod-mobile-pop").find("#output").attr("src",url);
                        $("#mod-mobile-pop").show();
                      /*  $('#output').empty();
                        $('#output').qrcode(url);
                        $("#commodity-qrc-modal").modal("show");*/
                    }else {
                        layer.msg('预览失败');
                    }
                })
            } else {
                Custom.ajaxPost("{:url('saveGroup')}",data,null,null,function(res){
                    if (res.error==0){
                        location.href="{:url('successStep3')}?is_ok=0&commodity_id="+res.data.id+"&is_grouped=1";
                    }else {
                        layer.msg('保存失败');
                    }
                })
            }


        });


    });

    <!--实例化编辑器-->
    var editUe = UE.getEditor('content1',{
        toolbars: [
            ['fullscreen', 'source', 'undo', 'redo', 'insertimage', 'insertvideo',
//'insertcode', //代码语言
                'insertrow', //前插入行
                'insertcol', //前插入列
                'mergeright', //右合并单元格
                'mergedown', //下合并单元格
                'deleterow', //删除行
                'deletecol', //删除列
                'splittorows', //拆分成行
                'splittocols', //拆分成列
                'splittocells', //完全拆分单元格
                'deletecaption', //删除表格标题
                'inserttitle', //插入标题
                'mergecells', //合并多个单元格
                'deletetable', //删除表格
                'inserttable',
                'fontfamily', //字体
                'fontsize', //字号
                'paragraph', //段落格式
                'justifyleft', //居左对齐
                'justifyright', //居右对齐
                'justifycenter', //居中对齐
                'justifyjustify', //两端对齐
                'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', 'preview']
        ]
    });
</script>
<script src="__STATIC__admin_v2/js/commodity_type.js?time=<?php echo time(); ?>"></script>
{/block}