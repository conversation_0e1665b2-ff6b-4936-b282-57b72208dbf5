<div class="yanbao_div">
    <div class="modal-content" >
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">选择延保服务包</h4>
        </div>
        <div class="modal-body ">
            <div class="alert alert-danger m-b-8" style="display: none;">
                <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                <p></p>
            </div>

            <div class="margin-bottom-10 p-r-0">
                <form class="form search-form" id="yanbao_search_form">
                    <div class="dndc-form-mod1" id="dndc-form-mod1">
                        <div class="form-main">
                            <div class="form-item">
                                <em>产品编码:</em>
                                <div class="form-wp">
                                    <input type="text" id="n_product_code" name="n_product_code" class="form-control input-sm element width-200" value="{$n_product_code}" placeholder="请输入产品编码" aria-controls="data-table" >
                                </div>
                            </div>

                            <div class="form-item">
                                <em>产品名称:</em>
                                <div class="form-wp">
                                    <input type="text" id="n_product_name" name="n_product_name" class="form-control input-sm element width-200" placeholder="请输入产品名称" aria-controls="data-table" >
                                </div>
                            </div>

                            <div class="form-item">
                                <em>车俩品牌:</em>
                                <div class="form-wp">
                                    <select name="car_brand_code" class="form-control input-sm element default-select2" >
                                        <option value="">全部</option>
                                    </select>
                                </div>
                            </div>


                            <div class="form-item">
                                <em>是否可用:</em>
                                <div class="form-wp">
                                    <select name="is_enable" class="form-control input-sm element default-select2" >
                                        <option value="">请选择</option>
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>


                            <div class="form-item">
                                <div class="btn-wp">
                                    <button id="yanbao-search" type="button" class=" btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                                    <button type="button" class="btn btn-sm btn-white m-r-3 reset">重置</button>
                                </div>
                            </div>

                        </div>
                    </div>
                </form>
            </div>
            <div class="table-scrollable" style="min-height:400px;">
                <table id="yanbao_form" class="table table-bordered">
                    <thead>
                    <th class="text-center">序号</th>
                    <th class="text-center">车系编码</th>
                    <th class="text-center">延保车系名称</th>
                    <th class="text-center">延保适用车型</th>
                    <th class="text-center">适用车型说明</th>
                    <th class="text-center">延保产品编码</th>
                    <th class="text-center">延保产品名称</th>
                    <th class="text-center">延保时间（月）</th>
                    <th class="text-center">延保购买条件</th>
                    <th class="text-center">延保内容</th>
                    <th class="text-center">专营店价格</th>
                    <th class="text-center">用户价</th>
                    <th class="text-center">适用月数范围</th>
                    <th class="text-center">适用里程数范围</th>
                    <th class="text-center">品牌名称</th>
                    <th class="text-center">最后更新时间</th>
                    <th class="text-center">是否可用</th>
                    <th class="text-center"><input type="checkbox" class="select_all" id="select_all" name="select_all">选择</th>
                    </thead>
                    <tbody id="yanbao-body">

                    </tbody>
                </table>
            </div>
            <div>
                <ul class="pagination"></ul>
            </div>
        </div>
        <div class="modal-footer">
            <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
            <a href="javascript:;" class="btn btn-sm btn-primary" id="yanbao_add" data-dismiss="modal">确定</a>
        </div>
    </div>
</div>

{block name="css"/}
<style type="text/css">
    .search-form .select2-container{
        width: 100%!important;
        margin-bottom: 0px!important;
    }
</style>
{/block}
<script src="__STATIC__admin_v2/js/xm-select.js"></script>

<script>
    var appoint = "{$appoint}";
    var n_product_code = "{$n_product_code}";

    $(".reset").click(function () {
        $("#yanbao_search_form").find("input:not([type='checkbox'])").val('');
        $.each($("#yanbao_search_form").find("select.default-select2"),function () {
            $(this).find("option").eq(0).attr("selected",true);
        })
        $(".comm_type_id").find('option[value!=""]').remove();
        $(".default-select2").select2();
        daterange_ini("#default-daterange","YYYY-MM-DD");
        $('option', $('#multiselect')).each(function(element) {
            $('#multiselect').multiselect('deselect', $(this).val());
        });
    });


    $("#yanbao_add").on('click', function () {
        var yan_bao_code = localStorage.getItem("yan_bao_code_"+appoint);
        var yan_bao_price = localStorage.getItem("yan_bao_price_"+appoint);
        console.log('yan_bao_code:',yan_bao_code)
        var num = ++appoint;
        if(yan_bao_code != null){
            if(yan_bao_code.length > 0){
                console.log('yan_bao_code:',yan_bao_code);
                console.log('yan_bao_price:',yan_bao_price);
                $(".price-stock").find("tr:eq("+(num)+")").find('.sku_code').val(yan_bao_code);
                $(".price-stock").find("tr:eq("+(num)+")").find('.sku_price').val(yan_bao_price);
            }
        }else{
            $(".price-stock").find("tr:eq("+(num)+")").find('.sku_code').val(yan_bao_code);
            $(".price-stock").find("tr:eq("+(num)+")").find('.sku_price').val(yan_bao_price);
        }

    });


    $("#select_all").on('click',function (){
        if($('input[name = select_all]').is(':checked')){
            var n_product_code = localStorage.getItem("yan_bao_code_"+appoint);
            var price = localStorage.getItem("yan_bao_price_"+appoint);
            var string = '';
            var string_price = '';
            $('input[name = select_n_product_code]').prop("checked","true")
            console.log('全选择前n_product_code_'+appoint,n_product_code)
            if(n_product_code != null){
                if(n_product_code.length > 0){
                    var new_string = ''
                    var new_string_price = ''
                    var array_n_product_code = n_product_code.split(',')
                    $("input[name='select_n_product_code']:checkbox:checked").each(function(){
                        if($.inArray($(this).data('n_product_code'),array_n_product_code) == -1){
                            new_string += $(this).data('n_product_code')+','
                            new_string_price += $(this).data('price')+','
                        }
                    })
                    string += n_product_code+","+new_string
                    string_price += price+","+new_string_price
                }
                // console.log('新的备件号-----',string)
                // console.log('新的备件价格-----',string_price)
                string = string.substr(0,string.length-1);
                string_price = string_price.substr(0,string_price.length-1);
                localStorage.setItem("yan_bao_code_"+appoint, string);
                localStorage.setItem("yan_bao_price_"+appoint, string_price);
            }else{
                $("input[name='select_n_product_code']:checkbox:checked").each(function(){
                    string += $(this).data('n_product_code')+','
                    string_price += $(this).data('price')+','
                })
                string = string.substr(0,string.length-1);
                string_price = string_price.substr(0,string_price.length-1);
                // console.log('不存在------',string)
                localStorage.setItem("yan_bao_code_"+appoint, string);
                localStorage.setItem("yan_bao_price_"+appoint, string_price);
            }
        }else{
            var part_no = localStorage.getItem("yan_bao_code_"+appoint);
            var price = localStorage.getItem("yan_bao_price_"+appoint);
            var string = '';
            var string_price = '';
            let array = part_no.split(',')
            var array_price = price.split(',')
            if(array.length >0){
                $.each(array,function (k,v){
                    var is_true = false
                    $("input[name='select_n_product_code']").each(function(){
                        if(v == $(this).data('n_product_code')){
                            is_true = true
                            return false
                        }
                    })
                    if(!is_true){
                        string += v+','
                        string_price += array_price[k]+","
                    }
                })
                string = string.substr(0,string.length-1);
                string_price = string_price.substr(0,string_price.length-1);
                if(string == ''){
                    localStorage.removeItem('yan_bao_code_'+appoint)
                    localStorage.removeItem('yan_bao_price_'+appoint)
                }else{
                    localStorage.setItem("yan_bao_code_"+appoint, string);
                    localStorage.setItem("yan_bao_price_"+appoint, string_price);
                }

            }else{
                localStorage.removeItem('yan_bao_code_'+appoint)
                localStorage.removeItem('yan_bao_price_'+appoint)
            }
            $('input[name = select_n_product_code]').removeAttr("checked")
            console.log('取消全选后part_no_'+appoint,part_no)
        }
    })

</script>