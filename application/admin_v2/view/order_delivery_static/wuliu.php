<style>
    .logistics_content_new {
        display: flex;
    }

    .logistics_content_new .left-content {
        display: flex;
        flex-direction: column;
        width: 40%;
        max-height: 500px;
        overflow-y: auto;
    }

    .logistics_content_new .left-content .box {
        height: 166px;
        line-height: 28px;
    }

    .logistics_content_new .left-content .box .text {
        color: #093;
    }

    .logistics_content_new .right-content {
        width: 60%;
        height: auto;
        max-height: 500px;
        overflow-y: auto;
    }

    .logistics_content_new .right-content .box {
        position: relative;
        padding: 6px 0;
        padding-right: 15px;
        margin-left: 25px;
        line-height: 28px;
        border-bottom: 1px solid rgba(231, 236, 241, 1);
    }

    .logistics_content_new .right-content .box .radio {
        position: absolute;
        top: 14px;
        left: -18px;
        width: 12px;
        height: 12px;
        background-color: rgba(231, 236, 241, 1);
        border-radius: 50%;
    }

    .logistics_content_new .right-content .box .line {
        position: absolute;
        top: 14px;
        left: -13px;
        width: 2px;
        height: 104%;
        background-color: rgba(231, 236, 241, 1);
    }
</style>
<style>
    .send-tab .table-scrollable th {
        height: 50px;
        font-size: 16px;
        line-height: 30px;
        color: #666;
        background-color: rgba(242, 242, 242, 1);
        border: 1px solid #e6e6e6;
    }

    .send-tab .table-scrollable table {
        width: 100%;
        border-collapse: collapse;
    }

    .send-tab .table-scrollable td {
        height: 100px;
        padding: 5px;
        font-size: 14px;
        border: 1px solid #e6e6e6;
    }

    .send-tab .table-scrollable .wrapbox {

    .send-tab .table-scrollable ::-webkit-scrollbar {
        width: 17px;
        height: 5px;
    }

    .send-tab .table-scrollable ::-webkit-scrollbar-thumb {
        background-color: rgba(50, 50, 50, 0.3);
        border-radius: 1em;
    }

    .send-tab .table-scrollable ::-webkit-scrollbar-track {
        background-color: rgba(50, 50, 50, 0.1);
        border-radius: 1em;
    }

    }

    .send-tab .table-scrollable .table-head {
        height: 5vh;
        padding-right: 16px;
        color: #000;
    }

    .send-tab .table-scrollable .table-body {
        max-height: 350px;
        margin-top: 12px;
        overflow-y: scroll;
    }

    .send-tab .table-scrollable .tableHead {
        table-layout: fixed;
    }

    .send-tab .table-scrollable .tableBody {
        table-layout: fixed;
    }

    .send-tab .table-scrollable .table_img {
        display: flex;
        justify-items: center;
        align-items: center;
        width: 100%;
        height: 100px;
    }

    .send-tab .table-scrollable .table_img .img {
        width: 100px;
        height: 100px;
        margin-right: 12px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
    }
    .look-logistics{
      width: 1000px;
      margin: auto;
    }
    .sku_info_new{
      white-space: normal;
    }

</style>
<style type="text/css">
    /*page.css begin*/
    img {
        display: inline-block;
        overflow: hidden;
        vertical-align: middle;
    }

    /*page.css end*/
    h4 {
        font-size: 18px;
    }
    .step-mod-1>ul>li {
        position: relative;
        font-size: 14px;
        padding-left: 30px;
        color: #333;
        height: 50px;
        box-sizing: border-box;
    }
    .com-image img{
        vertical-align: middle;
        max-width: 50px;
        max-height: 50px;
    }
    .table-scrollable {
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden;
        border: none;
        margin-bottom: 10px !important;
    }
    .table-scrollable th {
        min-width: 100%;
    }
    .table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th{
        vertical-align: middle;
    }
    td, th {
        padding: 0;
        white-space: nowrap;
    }

    .send-tab {
        position: relative;
        margin-top: 12px;
    }


    .send-tab .nav {
        display: flex;
        position: relative;
    }

    .send-tab .nav li {
        width: 250px;
        height: 48px;
        text-align: center;
        background: #fff;
        list-style: none;
    }

    .send-tab .nav li label {
        display: block;
        width: 250px;
        font-size: 18px;
        line-height: 48px;
        cursor: pointer;
    }

    .send-tab .content {
        margin-left: 10px;
        margin-top: 15px;
        position: relative;
        box-sizing: border-box;
        padding: 10px;
        overflow: hidden;
    }

    .send-tab .content1,
    .send-tab .content3,
    .send-tab .content2 {
        display: none;
        width: 100%;
        height: 100%;
    }

    .send-tab .nav1:checked ~ .nav li {
        color: #000;
        background: #fff;
    }

    .send-tab .nav li #liLeft1 {
        background: #f1f1f1;
    }

    .send-tab .nav li .tab_status {
        position: absolute;
        top:-16px;
        left:2px;
        width: 49px;
        height: 22px;
        font-size: 12px;
        line-height: 22px;
        color: #fff;
        text-align: center;
        background-color: rgba(239, 90, 161, 1);
    }

    .send-tab .nav2:checked ~ .nav li {
        color: #000;
        background: #fff;
    }
    .send-tab .nav3:checked ~ .nav li {
        color: #000;
        background: #fff;
    }

    .send-tab .nav li.active {
        color: #fff;
        background: rgba(242, 242, 242, 1);
    }

    .send-tab .content .default {
        display: block;
    }
    .send-shop-dialog{
        width: 100%;
    }
    .send-shop-dialog .send-shop-content{
        width: 1000px;
        margin: auto;
    }
    .modal-footer .send-shop-item {
        display: flex;
    }
    .modal-footer .send-shop-item .form-wp {
        display: flex;
    }
    .modal-footer .send-shop-item .form-wp label {
        width:94px;
        line-height: 32px;
    }
</style>
<div class="modal fade" id="logisticsModal">
    <div class="modal-dialog look-logistics">
        <div class="modal-content look-logistics">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title">查看物流</h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger m-b-8" style="display: none;">
                    <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                    <p></p>
                </div>
                <div class="logistics_content_new">
                    <div class="left-content left-content-click wuliu_show">
                        <div class="box" id="logistics_new_box">
                            <div>包裹1</div>
                            <div>物流状态：<span class="text">派发中</span></div>
                            <div>承运来源：EMS</div>
                            <div>运单编号：11111111111111111111</div>
                            <div>官方电话：18888888888（如果没有显示“暂无”）</div>
                        </div>
                    </div>
                    <div class="right-content wuliu_show">
                        <div class="send-tab">
                            <ul class='nav tab-nav'>
                                <li><label data-tab="logistics" for="li1" id="logistics_tab1">物流动态</label></li>
                                <li><label data-tab="commodity" for="li2" id="logistics_tab2">包含商品</label></li>
                            </ul>
                            <div class="table-scrollable">

                            </div>
                        </div>
                    </div>
                </div
            </div>
        </div>
    </div>
</div>





<script>
    var leftClick = '';
    var logistics_data = {};
    var id = '';

    $("a.logisticsModal").on("click", function () {
        $('.logistics_content_new .left-content-click').html('');
        var id = $(this).attr('data-id');
        var order_code = $(this).attr('data-order-code');
        var supplier = $(this).attr('data-supplier');
        var logisticsModal = $("#logisticsModal");
        logisticsModal.find(".alert").find("p").html('');
        logisticsModal.find(".alert").hide();
        $('.wuliu_show').hide();
        $.getJSON("{:url('order_delivery_static/ajaxGetLogisticsList')}", {
            order_code: order_code,
            supplier: supplier
        }, function (resData) {
            if (resData.error == 0) {
                var str = '';
                var i   = 1;
                logistics_data = resData.data;
                $.each(logistics_data, function (k, v) {
                    if (k == 'no_goods'){
                        str += '<div class="box" id="' + k + '" onclick="left_click(this);"> ' +
                            '<div>暂无物流信息</div> ' +
                            '<div>共' + v.length + '种商品待发货</div> ' +
                            '</div>';
                    } else {
                        var delivery = v.delivery;
                        str += '<div class="box" id="' + k + '" onclick="left_click(this);"> ' +
                            '<div>包裹'+i+'</div> ' +
                            '<div>物流状态：<span class="text">' + delivery.deliveryStatus + '</span></div> ' +
                            '<div>承运来源：' + delivery.commonCarrier + '</div> ' +
                            '<div>运单编号：' + delivery.waybill_number + '</div> ' +
                            '<div>官方电话：' + delivery.tel + '</div> ' +
                            '</div>';
                    }
                    i++;
                })
                $('.logistics_content_new .left-content-click').html(str);
                $('.lgt-wp').show();
                $('.empty-style-a').hide();
                $('.logistics_content_new .left-content-click .box:first-child').click();
                $('.wuliu_show').show();
            } else {
                $('.lgt-wp').hide();
                $('.empty-style-a').show();
                logisticsModal.find(".alert").find("p").html(resData.msg);
                logisticsModal.find(".alert").show();
            }
            $("#logisticsModal").modal('show');
        })
    });




    // 物流左边点击样式
    // $('.left-content-click .box').click(function(e) {
    //     if (leftClick !== e.currentTarget) {
    //         $(leftClick).attr("style","background:#ffffff");
    //     }
    //     leftClick = e.currentTarget;
    //     $(e.currentTarget).attr("style","background:#f1f1f1");
    // })
    $('#logistics_tab1').click(function () {
        var str = '';
        if (id == 'no_goods'){
            $('#logistics_tab1').text('未发货商品');
            $('#logistics_tab2').hide();
            $('.wrapbox').next().hide();
            var goods = logistics_data[id];
            str += '<div class="wrapbox">';
            str += '<div class="table-head">';
            str += '<table class="tableHead">';
            str += '<colgroup align="center">';
            str += '<col>';
            str += '<col>';
            str += '<col>';
            str += '</colgroup>';
            str += '<thead>';
            str += '<tr>';
            str += '<th>商品图片</th>';
            str += '<th>商品名称</th>';
            str += '<th>商品规格</th>';
            str += '<th>商品状态</th>';
            str += '</tr>';
            str += '</thead>';
            str += '</table>';
            str += '</div>';
            str += '<div class="table-body">';
            str += '<table class="tableBody">';
            str += '<colgroup align="center">';
            str += '<col>';
            str += '<col>';
            str += '<col>';
            str += '</colgroup>';
            str += '<tbody id="order_goods_tbody">';
            $.each(goods, function (i, v) {
                str += '<tr>';
                str += '<td>';
                str += '<div class="table_img">';
                str += '<div class="img" style="background-image: url(' + v.commodity_pic + '")></div>';
                str += '</div>';
                str += '</td>';
                str += '<td class="sku_info_new">' + v.commodity_name + '</td>';
                str += '<td class="sku_info_new">';
                $.each(v.sku_list, function (k, y) {
                    str += '<P>' + y + '</P>';
                });
                str += '</td>';
                str += '<td class="sku_info_new">' + v.order_commodity_status_name + '</td>';
                str += '</tr>';
            });
            str += '</tbody>';
            str += '</table>';
            str += '</div>';
            str += '</div>';
        }else {
            $('#logistics_tab1').text('物流动态');
            $('#logistics_tab2').show();
            $('#logistics_tab2').attr("style", "background:#ffffff");
            $(this).attr("style", "background:#f1f1f1");
            var data = logistics_data[id];
            var deliveryList = data.delivery.deliveryList;
            $.each(deliveryList, function (i, v) {
                str += '<div class="box"> <div>' + v.status + '</div> ' +
                    '<div>' + v.time + '</div> ' +
                    '<div class="radio"></div> ' +
                    '<div class="line"></div>' +
                    '</div>';
            })
        }

        $('.right-content .table-scrollable').html(str);
    })
    $('#logistics_tab2').click(function () {
        $('#logistics_tab1').show();
        $('#logistics_tab2').show();
        $('#logistics_tab3').hide();
        $('#logistics_tab1').attr("style", "background:#ffffff");
        $(this).attr("style", "background:#f1f1f1");
        $('.wrapbox').next().hide();
        var data = logistics_data[id];
        var goods = data.goods;
        var str = '';

        str += '<div class="wrapbox">';
        str += '<div class="table-head">';
        str += '<table class="tableHead">';
        str += '<colgroup align="center">';
        str += '<col>';
        str += '<col>';
        str += '<col>';
        str += '</colgroup>';
        str += '<thead>';
        str += '<tr>';
        str += '<th>商品图片</th>';
        str += '<th>商品名称</th>';
        str += '<th>商品规格</th>';
        str += '</tr>';
        str += '</thead>';
        str += '</table>';
        str += '</div>';
        str += '<div class="table-body">';
        str += '<table class="tableBody">';
        str += '<colgroup align="center">';
        str += '<col>';
        str += '<col>';
        str += '<col>';
        str += '</colgroup>';
        str += '<tbody id="order_goods_tbody">';
        $.each(goods, function (i, v) {
            str += '<tr>';
            str += '<td>';
            str += '<div class="table_img">';
            str += '<div class="img" style="background-image: url(' + v.commodity_pic + '")></div>';
            str += '</div>';
            str += '</td>';
            str += '<td class="sku_info_new">' + v.commodity_name + '</td>';
            str += '<td class="sku_info_new">';
            $.each(v.sku_list, function (k, y) {
                str += '<P>' + y + '</P>';
            });
            str += '</td>';
            str += '</tr>';
        });
        str += '</tbody>';
        str += '</table>';
        str += '</div>';
        str += '</div>';

        $('.right-content .table-scrollable').html(str);
    })

    function left_click(obj) {
        id = $(obj).attr('id');
        $(obj).siblings().attr("style", "background:#ffffff");
        $(obj).attr("style", "background:#f1f1f1");

            $('#logistics_tab1').click();

    }
</script>
