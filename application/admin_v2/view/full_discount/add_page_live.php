{extend name="public:base_layout" /}
{block name="css"/}
<style>

    .level_name_box{
        text-align: center;padding-top: 5px;
    }
    .level_name_item_box{
        width: 80px
    }
    .btnc{
        text-align: center;
    }
    .stylelt{
        width: 90px;text-align: center;float:left;
    }

    .fulldisbath{
        padding-top: 8px
    }
</style>
{/block}
{block name="content"/}

<div class="panel-body">

    <div class="alert alert-danger fade in m-b-15 " style="display: none">
        <p></p>
    </div>
    <form id="add-form" class="form-horizontal form-bordered" role="form">
        <input type="hidden" id="is_change" value="1">
        <div class="form-group">
            <label class="control-label col-md-2">
                活动名称<i class="m-r-3 text-danger">*</i>:
            </label>
            <div class="col-md-10">
                <input class="form-control width-400" name="activity_title" placeholder="请输入活动名称" data-parsley-required="true">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-2">
                起止时间<i class="m-r-3 text-danger">*</i>:
            </label>
            <div class="col-md-9">
                <div class="col-md-3 p-l-0" style="">
                    <input type="text" name="start_time" placeholder="请输入开始时间" class="form-control datetimepicker3 width-150" data-parsley-required="true"  >
                </div>
                <div class="col-md-3">
                    <input type="text" name="end_time" placeholder="请输入结束时间" class="form-control datetimepicker3 width-150" data-parsley-required="true"  >
                </div>
                <div class="col-md-4 tip-text">
                    <i class="fa fa-exclamation"></i>
                    活动开始后不可修改，请慎重选择！
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-2">促销标签:</label>
            <div class="col-md-5" style="width: 70%;">
                <input type="text" name="tag"  style="width:200px;background-color: #fff;background-image: none;
    border: 1px solid #ccc;border-radius: 4px;box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset; color: #555;font-size: 14px;height: 34px;line-height: 1.42857;padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;border-radius: 3px;box-shadow: none; font-size: 12px;" maxlength="4"/>
                (促销标签是商家对促销动作的别名操作，用于前台显示，最多可输入4个字符，非必填选项）
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-2">定向人群:</label>
            <div class="col-md-9">
                <div >未选择定向人群则代表所有用户可参与活动。选择定向人群后若更改人群类型，已添加的商品列表会被清空！活动开始后不可修改</div>
                <?php if($set_type != 6):?>
                <label class="radio-inline">
                    <input type="radio" name="user_segment" class="user_segment" value="1"   data-parsley-multiple="dis_type" /> 会员
                </label>
                <?php endif;?>
                <label class="radio-inline">
                    <input type="radio" name="user_segment" class="user_segment" value="2"  data-parsley-multiple="dis_type"/> 车主
                </label>
                <div style="margin-top: 30px;display: none;" id="user_level_box">
                    <div>可参与的会员等级，若选择了某个会员等级后，该等级及更高等级的会员都有活动资格</div>
                    <?php foreach($user_level as $key=>$val):?>
                        <label class="radio-inline">
                                <input type="radio" name="user_segment_options" data-show-key="{$key}" value="{$val['value_code']}"  title="{$val['county_name']}" data-parsley-multiple="dis_type"/> {$val['county_name']}
                        </label>
                    <?php endforeach;?>
                </div>
            </div>
        </div>
        <div class="form-group" id="rule_old">
            <label class="control-label col-md-2">
                活动金额设定<i class="m-r-3 text-danger">*</i>:
            </label>
            <div class="col-md-10 ruleEdit" id="ruleEdit">
                <div class="row row2">
                    满<input type="text"  class="form-control piece form-j" >元
                    减<input type="text" class="form-control discount form-z">元

                    <i class="fa fa-2x fa-plus-circle text-success"></i>
                </div>
                <div class="col-md-4 tip-text">
                    <i class="fa fa-exclamation"></i>
                    活动开始后不可修改，请慎重选择！
                </div>
            </div>
        </div>
        <div class="form-group"  id="rule_new" style="display: none;">
            <label class="control-label col-md-2">活动金额设定<i class="m-r-3 text-danger">*</i>:</label>
            <div  class="col-md-8" id="discount">
                <div class="input-group col-md-12" >
                    <div id="member_box" class="limit_boxs" >
                        <div class="row dis_1 slope-dis col-md-12"  style="margin-top: 12px;"  >
                            <div  class="rule_new_box col-md-12"  >
                                <table class="membertable">
                                    <tr>
                                        <td></td>
                                        <td></td>
                                        <?php foreach($user_level as $key=>$val):?>
                                        <td class="show_{$key}"> <div  class="stylelt radio-inline " data-title="{$val['county_name']}" data-code="{$val['value_code']}">{$val['county_name']}</div></td>
                                        <?php endforeach;?>
                                    </tr>
                                    <tr>
                                        <td>满<input style="height: 30px;margin-top: 8px" name="m_level[]" data-parsley-required="true"  size="4">元 减</td>
                                        <td><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click batch_price" style="margin-top:5px" data-value="" data-placeholder="批量设置" data-title="批量设置" >批量设置<i class="fa fa-edit m-l-5"></i></a></td>
                                        <?php foreach($user_level as $key=>$val):?>
                                        <td class="show_{$key}"><div class="radio-inline stylelt"><input name="{$val['value_code']}[]" size="4" data-parsley-required="true"  class="v_dis_type dis_type_1 {$val['value_code']}"/></div></td>
                                        <?php endforeach;?>
                                        <td><span class="btnc stylelt"><span class="adbtn">【 + 】</span></span></td>
                                    </tr>
                                </table>
                            </div>
                         </div>
                    </div>

                    <div id="car_onwer_box" class="limit_boxs">
                        <div class="row dis_1 slope-dis"  style="margin-top: 12px;">
                            <div  class="rule_new_box col-md-12"  >
                                <?php if($set_type == 6):?>
                                    <div>若未设置非车主价则非车主无参与资格，如需设置多个梯度，请保证多个梯度资格一致，三类人群的优惠力度需排序为：Ariya车主≥日产车主≥非车主</div>
                                <?php else:?>
                                    <div>若未设置非车主价则非车主无参与资格，如需设置多个梯度，请保证多个梯度资格一致，车主的优惠力度需大于等于非车主</div>
                                <?php endif;?>

                                <table>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                        <td><div class="radio-inline stylelt">非车主</div></td>
                                        <td><div class="radio-inline stylelt">车主</div></td>
                                        <?php if($set_type == 6):?>
                                        <td><div class="radio-inline stylelt">Ariya车主</div></td>
                                        <?php endif;?>
                                    </tr>
                                    <tr>
                                        <td>满<input name="cm_level[]" data-parsley-required="true"  style="height: 30px;margin-top: 8px" size="4">元 减</td>
                                        <td><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click car_batch_price" style="margin-top:5px" data-value="" data-placeholder="批量设置" data-title="批量设置" >批量设置<i class="fa fa-edit m-l-5"></i></a></td>
                                        <td><div class="radio-inline stylelt"><input name="none_car[]" size="4" class="carv_dis_type dis_type_1 tip_show_0"/></div></td>
                                        <td><div class="radio-inline stylelt"><input name="nissan_car[]" size="4"   class="carv_dis_type dis_type_1 tip_show_1"/></div></td>
                                        <?php if($set_type == 6):?>
                                            <td><div class="radio-inline stylelt"><input name="ariya_car[]" size="4" data-parsley-required="true"  class="carv_dis_type dis_type_1 tip_show_2"/></div></td>
                                        <?php endif;?>
                                        <td><span class="btnc stylelt"><span class="adbtn2">【 + 】</span></span></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                  </div>
                </div>
            </div>
        </div>
        <div class="form-group"  >
            <label class="control-label col-md-2">可参与次数:</label>
            <div  class="col-md-5" id="purchase_number">
                <div class="input-group col-md-12" >
                    <input type="text" data-parsley-errors-container="#purchase_number" name="purchase_number" class="form-control  col-md-3"   data-parsley-type="integer"  data-parsley-min="0"  >
                    <span class="input-group-addon" data-parsley-required="true" >次</span>
                </div>
                <dd class="tip-text"> <i class="fa fa-exclamation"></i>如果设置为0或不设置则被视为不限制购买数量</dd>
            </div>
        </div>

        <div class="form-group"  >
            <label class="control-label col-md-2">业务归属:</label>
            <div  class="col-md-5" id="is_enable">
                <div class="input-group col-md-9">
                    <select name="gather_id" class="form-control input-sm ">
                        <option value=""> 全部 </option>
                        {volist name="$gather_list" id="val" key="key"}
                        <option value="{$val['id']}" {if($Think.get.gather_id == $val['id'])}selected{/if} > {$val['name']} </option>
                        {/volist}
                    </select>
                </div>
            </div>
        </div>

        <div class="form-group"  >
            <label class="control-label col-md-2">主题活动:</label>
            <div  class="col-md-5" id="is_enable">
                <input type="text" name="theme_name" id="theme_name" placeholder="请输入主题活动" value="" class="form-control" data-parsley-length="[0, 100]">
            </div>
        </div>

        <div class="form-group"  >
            <label class="control-label col-md-2">是否PV补贴:</label>
            <div  class="col-md-5" id="is_enable">
                <div class="input-group col-md-9">
                    <label>
                        <input class="switch" id="is_pv_subsidy" type="checkbox" data-input-name="is_pv_subsidy" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" />
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group" >
            <label class="control-label col-md-2">是否叠加券:</label>
            <div  class="col-md-5" id="card_available">
                <div class="input-group col-md-9">
                    <label>
                        <input class="switch" id="is_card_change" type="checkbox" name="switch" data-input-name="card_available"
                               data-input-true="1" data-input-false="0"
                               data-size="small" data-on-text="可用" data-off-text="不可用" checked />
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group no_card">
            <label class="col-md-2 control-label" id="card_activity">选择可叠加的券:</label>
            <div class="col-md-6">
                <input type="text" autocomplete="off" class="form-control card_activity" name="card_activity" placeholder="点击选择" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="">
                <input type="hidden" class="form-control hide_card_ids" name="rel_card_ids" >
            </div>
            <div class="bottom-wp">
                <div class="bottom-left">
                    已选<input type="" class="num_card_ids" style="width: 20px;outline:none;border: 0; readonly " value="0">张
                </div>
            </div>
        </div>

        <div class="form-group"  >
            <label class="control-label col-md-2">优惠规则<i class="m-r-3 text-danger">*</i>:</label>
            <div  class="col-md-8" id="discount_type">
                <div class="input-group col-md-12" >
                    <div class="row  col-md-12">
                        <label class="radio-inline">
                            <input type="radio" checked name="discount_type" value="1" id="radio-required1" data-parsley-required="true" data-parsley-multiple="discount_type" /> 仅商品
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="discount_type" id="radio-required2" value="2" data-parsley-required="true" data-parsley-multiple="discount_type"/> 仅商品工时
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="discount_type" id="radio-required2" value="3" data-parsley-required="true" data-parsley-multiple="discount_type"/> 商品+工时
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group ">
            <label class="col-md-2 control-label" id="e3s_activity">关联E3S活动:</label>
            <div class="col-md-6">
                <input type="text" autocomplete="off" class="form-control e3s_activity" name="e3s_activity" placeholder="请选择活动" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="">
                <input type="hidden" id="e3s_activity_id" name="e3s_activity_id" value="">
            </div>
        </div>
        <div class="form-group ">
            <label class="col-md-2 control-label" id="activity_type">活动设置类型:</label>
            <div class="col-md-6">
                <select class="form-control width-300" name="activity_type" >
                    <option value="0">请选择</option>
                    <option value="1">备件</option>
                    <option value="2">工时</option>
                    <option value="3">赠品</option>
                </select>
            </div>
        </div>
        <div class="form-group ">
            <label class="col-md-2 control-label" id="settlement_rule">关联结算规则:</label>
            <div class="col-md-6">
                <input type="text" autocomplete="off" class="form-control settlement_rule" name="settlement_rule" id="template_name" placeholder="请选择选择结算规则" data-parsley-group="wizard-step-1" >
                <input type="hidden" id="settlement_rule_id" name="settlement_rule_id">
                <input type="hidden" id="settlement_rule_value" name="settlement_rule_value">
                <input type="hidden" id="settlement_rule_type" name="settlement_rule_type">
                <input type="hidden" id="act_sett_standard" name="act_sett_standard">
            </div>
        </div>

        <div class="form-group" id="up_down">
            <label class="control-label col-md-2">活动渠道<i class="m-r-3 text-danger">*</i>:</label>
            <div class="col-md-10">
                <div>
                    <?php foreach ($up_down_channel as $key => $val): ?>
                        <?php if($live_type == 1):  if (in_array($key,['PZ1AAPP','PZ1ASM'])):?>
                            <label class="checkbox-inline">
                                <input type="checkbox" data-parsley-required="true"
                                       name="up_down_channel[]"
                                       value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                            </label>
                        <?php endif; else:?>
                        <?php if($live_type == 2):  if (in_array($key,['QCSM','QCAPP'])):?>
                        <label class="checkbox-inline">
                            <input type="checkbox" data-parsley-required="true"
                                   name="up_down_channel[]"
                                   value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                        </label>
                        <?php endif; else:?>
                            <?php if(!in_array($key,['PZ1AAPP','PZ1ASM','QCSM','QCAPP','TOBPC'])):?>
                                <label class="checkbox-inline">
                                    <input type="checkbox" data-parsley-required="true"
                                           name="up_down_channel[]"
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endif; endif;endif; endforeach; ?>
                </div>
                <div style="margin-top:20px;" class="hidden" id="dealer_select">
                    <input id="dlr_show" type="text" class="form-control width-300" value="" placeholder="请点击选择经销商"
                           data-parsley-required="true">
                    <input id="dlr_hide" type="text" name="dlr_code" class="hidden" value="">
                </div>
            </div>
        </div>

        <div class="form-group"  >
            <label class="control-label col-md-2">活动状态:</label>
            <div  class="col-md-5" id="is_enable">
                <div class="input-group col-md-9">
                    <label>
                        <input class="switch" id="is_enable" type="checkbox" data-input-name="is_enable" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭" checked />
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-md-2">
                活动商品<i class="m-r-3 text-danger">*</i>:
            </label>
            <div class="col-md-10">
                <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                    <div style="float:left;width:60%">
                        <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品分类:</label>
                        <select class="form-control input-sm element default-select2 col-md-3" name="comm_parent_id" id="slt_comm_type_id" style="width: 25%">
                            <option value="0">
                                请选择
                            </option>
                            <?php foreach($comm_parent_list as $key=>$val):?>
                                <option value="{$val['id']}" >{$val['comm_type_name']}</option>
                            <?php endforeach;?>
                        </select>
                        <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id" style="width: 25%">
                            <option value="0">
                                请选择
                            </option>
                        </select>
                        <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id" style="width: 25%">
                            <option value="0">
                                请选择
                            </option>
                        </select>
                    </div>
                    <div style="float:left;width:30%">
                        <label class="control-label col-md-2" style="padding:  5px 5px;  width: auto;">商品名称:</label>
                        <input type="text" class="form-control input-sm element col-md-3 m-r-2 " style=" width: 78%"
                               name="commodity_name" placeholder="请输入商品名称">
                    </div>
                </div>
                <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                    <div style="float:left;width:60%">
                        <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品种类:</label>
                        <select class="form-control input-sm element default-select2 col-md-3 comm-type-search2"
                                name="commodity_class" onchange="commTypeSearch2()"  id="slt_comm_type_id" style="width: 76%">
                            <option value="0">
                                请选择
                            </option>
                            <?php foreach($commodity_class as $key=>$val):?>
                                <option value="{$key}" >{$val}</option>
                            <?php endforeach;?>
                        </select>
                    </div>

                    <div   id="btn-sm-div" style="float:left;width:40%">
                        <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品类型:</label>
                        <select class="form-control input-sm element default-select2 col-md-3 comm-type-search2"
                                name="commodity_dlr_type_id" onchange="commTypeSearch2()" id="slt_comm_type_id" style="width: 58%">
                            <option value="0">
                                请选择
                            </option>
                            <?php foreach($comm_dlr_type_list as $key=>$val):?>
                                <option value="{$val['id']}" >{$val['inner_name']}</option>
                            <?php endforeach;?>
                        </select>
                        <button id="comm-type-search" type="button" class="btn btn-sm btn-success comm-type-search2"><i class="fa fa-search"></i>搜索</button>
                    </div>
                </div>
                <div>
                    <div class="table-scrollable">
                        <table id="" class="table table-hover">
                            <thead>
                            <th class="text-center">商品规格</th>
                            <th class="text-center">上架渠道</th>
                            <th class="text-center">原价</th>
                            <th class="text-center">库存</th>
                            <th class="text-center">
                                <a
                                        data-current-page="1"
                                        data-is-sku-confirm-all="1"
                                        data-comm-id="all"
                                        home="all"
                                        data-comm-set-id="all"
                                        commodity_class=""
                                        data-dlr-code="all"
                                        class="btn btn-sm btn-default btn-white"
                                        id="sku-confirm-all"
                                >批量添加</a>
                            </th>
                            </thead>
                            <tbody id="add-comm-tbody">

                            </tbody>
                        </table>
                    </div>
                    <div>
                        <ul class="pagination" id="comm-pagination"></ul>
                    </div>
                </div>

                <legend class="pull-left width-full" style="font-size: 18px;">
                    <div class="col-md-10">
                        已选活动商品
                    </div>
                    <div class="col-md-2">
                        <a class="btn btn-danger btn-sm m-r-5 m-b-5 del-commodity">删除全部商品</a>
                    </div>
                </legend>

                <div>
                    <div class="table">
                        <table id="commodity_select" class="table">
                            <thead></thead>
                            <tbody id="haved-commodtiy">

                            </tbody>
                        </table>
                        <div id="pagiDiv" style="width:100%;text-align: center;display: none">
                            <span id="spanFirst">首页</span>
                            <span id="spanPre">上一页</span>
                            <span id="spanNext">下一页</span>
                            <span id="spanLast">尾页</span>
                            第 <span id="spanPageNum"></span> 页/共 <span id="spanTotalPage"></span> 页
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-2">
            </label>
            <div class="col-md-10">
                <input type="hidden" name="set_type" value="{$set_type}">
                <a href="javascript:;" class="btn btn-sm btn-primary" id="add_submit">提交设置</a>
            </div>
        </div>
    </form>

    <!----begin 查看规格---->
    <div class="modal fade" id="sku-modal" data-per-page="" data-comm-id="" data-comm-set-id="" data-type="">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择商品: <span id="comm-name-header"></span></h4>
                </div>

                <input id="modal_home"  type="hidden" value="" />
                <input id="modal_commodity_class"  type="hidden" value="" />
                <input id="commodity_dlr_type_id"  type="hidden" value="" />

                <div class="modal-body" style="">
                    <div style="background-color: #dadada;height: 50px;line-height: 50px;">
                        <div class="sku-image" style="display: inline-block;">
                            <img class=" cover-image" src="">
                        </div>
                        <div style="display: inline-block;">
                            <div  class="sku-comm">
                            </div>
                        </div>
                    </div>
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th class="col-md-6" >商品规格</th>
                            <th >原价</th>
                            <th>库存</th>
                        </tr>
                        </thead>
                        <tbody class="sku-tb">

                        </tbody>
                    </table>
                    <?php if($set_type==1):?>
                        <div>
                            <label class="control-label col-md-1" style="width: 80px;">经销商<i class="m-r-3 text-danger">*</i>:</label>
                            <textarea class="form-control sku-dlr" readonly placeholder="请点击选择经销商" data-sku-dlr=""  style="width: 470px; height: 50px;"></textarea>
                        </div>
                    <?php endif;?>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >关闭</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary  " id="sku-confirm" >确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>您选择对商品 XXXXXXXXX 进行移除操作吗？</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

{/block}

{block name="css"}
<style type="text/css">
    .tip{
        height: 34px;
        line-height: 34px;
        display: inline-block;
        float: left;
        margin: 0 5px;
        padding: 0px;
    }
    .tip-text{
        height: 34px;
        line-height: 34px;
        margin-left: 10px;
        color: rgba(253, 6, 6, 0.67);
        background-color: rgba(180, 237, 245, 0.52);

    }
    .tip-youhui {
        height: 34px;
        line-height: 34px;
        display: inline-block;
        float: left;
        margin: 0 5px 0 0;
        padding: 0px;
        padding: 0px 10px;
        border: 0.5px solid #a9abab;
        border-radius: 7px;
    }
    .tip-youhui-money{
        color: #0aafb0;
    }
    .tip-youhui-card{
        color: #fbd28b;
    }
    .youhui{
        display: inline-block;
        float: left;
    }
    .row {
        margin: 10px 5px;
    }
    .cover-image{
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-left: 20px;
    }
    .text-hidden{
        width: 30%;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
    }
    .form-group dd{
        margin-top: 5px;
    }
    .cover-image{
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-left: 20px;
    }

    /*添加规则200515*/
    .ruleEdit {
        font-size: 14px;
        padding:0 15px 15px;
    }
    .ruleEdit .form-control {
        width:80px;
        display: inline-block;
        margin:0 10px;
    }
    .ruleEdit .row {
        margin-bottom:10px;
        overflow: hidden;
    }
    .ruleEdit .row1 .fa-minus-circle {
        float: right;
        position: relative;
        top:3px;
        margin-right:10px;
    }
    .ruleEdit .row .fa.fa-2x {
        vertical-align: -5px;
        margin:0 10px;
    }
    .ruleEdit .items-wp {
        margin-bottom:20px;
    }
    .ruleEdit .item {
        padding:15px 0;
        border-bottom:1px dashed #ccc;
    }
    .ruleEdit .btn-wp {
        margin-right:10px;
    }

    .sku-confirm-all-active{
        background: #929ba1;
        border-color: #929ba1
    }
</style>
{/block}

{block name="script"}
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js"></script>
<script src="__STATIC__admin_v2/dndc_file/jquery.masonry.min.js" type="text/javascript"></script>
<script>
    //批量设置库存
    $('.batch_price').editable({
        success: function (response, newValue) {
            $(this).parent().parent().find(".v_dis_type").val(newValue)
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
                return '格式有误';
            }


        }
    });

    //批量设置库存
    $('.car_batch_price').editable({
        success: function (response, newValue) {
            $(this).parent().parent().find(".carv_dis_type").val(newValue)
            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
                return '格式有误';
            }

        }
    });
    var user_segment = 0;
    $(".user_segment").click(function(){
        $("#rule_new").show()
        $("#rule_old").hide()
        $("#radio-required3").attr("checked","true")
        $("#haved-commodtiy").empty()
        if($(this).val() == 1){
            $("#user_level_box").show();
            $("#car_onwer_box").hide();
            $("#member_box").show();
            user_segment = 1;
        }else{
            $("#user_level_box").hide();
            $("#member_box").hide();
            $("#car_onwer_box").show();
            user_segment = 2
        }
        $("#show_tip").hide()
        is_new_brand = 2
        // alert(is_new_brand)
        // alert(user_segment)
        // alert(dis_type)
        //layer.msg('切换人群，会清空下方添加的商品')
        var index = layer.open({
            title: ['操作提醒'],
            btn: ['确认', '取消'],
            content:"<div style='font-size: 15px'>更改活动时间会清空下方添加商品列表</div>",
            yes: function (res) {
                layer.close(index)
                start_time = s_time;
                end_time = e_time;
                comm_id = [];
                $("#haved-commodtiy").empty();
                $("#sku-confirm-all").data('is-sku-confirm-all',1)
                $("#haved-commodtiy-gift").empty()
                comm_set_id_arr = [];
                skuConfirmAllCurrentPage = []
                initComm(ajaxCommUrl);
            }
        })
        commTypeSearch2(0)
    })

    $(".adbtn2").on('click',function(){
        var html = '<tr> <td>满<input name="cm_level[]" data-parsley-required="true"  style="height: 30px;margin-top: 8px" size="4">元 减</td>'
            +'<td><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click batch_price" style="margin-top:5px" data-value="" data-placeholder="批量设置" data-title="批量设置" >批量设置<i class="fa fa-edit m-l-5"></i></a></td>'
            +'<td><div  class="radio-inline stylelt"><input name="none_car[]" size="4" class="carv_dis_type dis_type_1 tip_show_0"/></div></td>'
            +'<td><div  class="radio-inline stylelt"><input name="nissan_car[]"  size="4" class="carv_dis_type dis_type_1 tip_show_1"/></div></td>'
            <?php if($set_type == 6):?>
            +'<td><div  class="radio-inline stylelt"><input name="ariya_car[]" data-parsley-required="true"  size="4" class="carv_dis_type dis_type_1 tip_show_2"/></div></td>'
            <?php endif;?>
            +'<td><span class="btnc stylelt"><span class="delbtn2">【 - 】</span></span></td></tr>';
        $("#car_onwer_box .rule_new_box").find("table").append(html);
        //$(".editable").editable()
        $('.batch_price').editable({
            success: function (response, newValue) {
                $(this).parent().parent().find(".carv_dis_type").val(newValue)
                $(".editable-cancel").click();
                return false;
            },
            validate: function (value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
                    return '格式有误';
                }
            }
        });
    })

    $(".adbtn").on("click",function(){
        var which_select = selected_options();
        var html = '<tr> <td>满<input name="m_level[]" data-parsley-required="true"  style="height: 30px;margin-top: 8px" size="4">元 减</td>'
            +'<td><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click batch_price" style="margin-top:5px" data-value="" data-placeholder="批量设置" data-title="批量设置" >批量设置<i class="fa fa-edit m-l-5"></i></a></td>'
                    <?php foreach($user_level as $key=>$val):?>
                    if("{$key}" >= which_select){
                        html = html +'<td><div class="radio-inline stylelt"><input data-parsley-required="true"  name="{$val.value_code}[]" size="4" class="v_dis_type dis_type_1 "/></div></td>'
                    }
                    <?php endforeach;?>
        html = html +'<td><span class="btnc stylelt"><span class="delbtn">【 - 】</span></span></td> </tr>';

        $("#member_box .rule_new_box").find("table").append(html);
        //$(".editable").editable()
        $('.batch_price').editable({
            success: function (response, newValue) {
                $(this).parent().parent().find(".v_dis_type").val(newValue)
                $(".editable-cancel").click();
                return false;
            },
            validate: function (value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9]+(.[0-9]{1,2})?$/).test(value)) {
                    return '格式有误';
                }
            }
        });
    })

    $(".delbtn2").live('click',function(){
        $(this).parent().parent().parent().remove();
    })

    $(".delbtn").live('click',function(){
        $(this).parent().parent().parent().remove();
    })


    $('input:radio[name=dis_type]').click(function(){
        if($('input:radio[name=dis_type]:checked').val() == 1){
            $(".c_dis_type").empty().append('折');
        }else{
            $(".c_dis_type").empty().append('元');
        }
    });




    /////////////////////////////////////////////
    var urlCardparam    ="{:url('ajaxGetCard2')}" + "?set_type=" + "{$set_type}";

    $(".datetimepicker3").datetimepicker({
        format:"YYYY-MM-DD HH:mm:00",
        locale: moment.locale('zh-cn'),
    });

    $(".default-select2").select2();

    function count_contracts_ajax(urlparam,modal){
        $.getJSON(urlparam,null,function(resData){
            $(".commodity-body").empty();
            createPage_commodityModal(5, 10, resData.data.total,urlparam);//创建翻页功能按钮
            list_contracts_ajax_commodity(urlparam);
        })
    }

    function list_contracts_ajax_card(urlparam){
        $.getJSON(urlparam,null,function(result){
            for(var i = 0;i < result.data.data.length; i++){   //json格式，多行数据的数组
                var tr = $("<tr></tr>");                      //一行记录
                tr.appendTo($("tbody#card-body"));
                var td = $("<td class='text-center'><input class='card-id' type='radio' name='card' value='"+result.data.data[i].id+"'></td>");    //应用场景
                td.appendTo(tr);
                var td = $("<td class='text-center'>"+(result.data.data[i].card_name?result.data.data[i].card_name:'')+"</td>");    //归属
                td.appendTo(tr);
                var td = $("<td class='text-center'>"+(result.data.data[i].card_type_name?result.data.data[i].card_type_name:'')+"</td>");    //应用场景
                td.appendTo(tr);
                var td = $("<td class='text-center'>"+(result.data.data[i].validity_date?result.data.data[i].validity_date:'')+"</td>");    //类型
                td.appendTo(tr);
                var td = $("<td class='text-center'>"+(result.data.data[i].belong_to?result.data.data[i].belong_to:"")+"</td>");    //优惠券id
                td.appendTo(tr);
            }
            $("#card-modal").modal("show");
        })
    }

    $(".comm_type_parent").on("change",function () {
        var ct_parent_id = $(this).val();
        $.getJSON("{:url('ajaxGetCt')}",{ct_parent_id:ct_parent_id},function (resData) {
            if(resData.error==0){
                var data = resData.data;
                var select = $(".comm_type");
                select.find("option").not(":first").remove();
                for (var i=0;i<data.length;i++){
                    var option = $("<option value='"+data[i]['id']+"'>"+data[i]['comm_type_name']+"</option>");
                    select.append(option);
                }
            }else {
                layer.msg(resData.msg);
            }
        })
    })

    $("#commodity-search").on("click",function () {
        var data = $("#commodity-form").serialize();
        var user_segment = $('input:radio[name=user_segment]:checked').val()
        if(user_segment == 'undefined') user_segment = 0;
        var urlParams = "{:url('ajaxGetCommodity')}?pagesize=6&user_segment="+user_segment+"&"+data;
        count_contracts_ajax(urlParams,null);
    })

    $("#is_preferential_money").on("change",function () {
        var check = $(this).attr("checked");
        if(check){
            $("#preferential_money").removeAttr("disabled");
            $("#preferential_money").attr("data-parsley-required",true);
            $("#preferential_money").attr("data-parsley-min","0.01");
            $("#preferential_money").attr("data-parsley-pattern-message","格式不正确,请输入不小于0.01的数值");
            $("#preferential_money").attr("data-parsley-pattern",'/^(\\d+)(\\.\\d{1,2})?$/');
        }else {
            $("#preferential_money").removeAttr("data-parsley-required");
            $("#preferential_money").removeAttr("data-parsley-min");
            $("#preferential_money").removeAttr("data-parsley-pattern-message");
            $("#preferential_money").removeAttr("data-parsley-pattern");
            $("#preferential_money").attr("disabled",true);
            $("#preferential_money").val('');
        }
    })

    $("#slt_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">选择</option>';
            $("#slt_three_comm_type_id").html(html);
            if(comm_parent_id == 0){
                $("#slt_sub_comm_type_id").html(html);return;
            }
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_sub_comm_type_id").html(html);
        },'json');

    }) ;

    $("#slt_sub_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        if(comm_parent_id == 0){
            var html='<option value="0">请选择</option>';
            $("#slt_three_comm_type_id").html(html);return;
        }
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">请选择</option>';
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_three_comm_type_id").html(html);
        },'json');
    }) ;
    var set_type = "{$set_type}";
    var admin_type="{$admin_type}";
    var getSkuUrl ="{:url('getSkuLive')}";
    var fight_group_id=0;
    var getDlr_url="{:url('ajaxGetDlr')}";
    //异步请求数据地址
    var ajaxCommUrl = "{:url('ajaxGetLiveCommodityList',['pagesize'=>10,'set_type'=>$set_type,'live_type'=>$live_type])}";      //商品
    var save_url    ="{:url('saveLive',['live_type'=>$live_type])}";
    var index_url   ="{:url('live',['set_type'=>$set_type,'live_type'=>$live_type,'live_type'=>$live_type])}";
    var discount_type = 1;
    var start_time='';
    var end_time='';
    /**
     * 新增参数验证
     * @type {string}
     */
    var ajaxCommUrl2 = ajaxCommUrl;
    var isInitComm = 1;
    var isInitCommMsg = '';
    //已全选的分页
    var skuConfirmAllCurrentPage = [];
    //当前页所有的comm_set_id值
    currentPageCommSetIds = [];
    //第一个选中的【商品种类】
    var oneCommodityClass = 0;
    var commodityClassJson = {$commodity_class_json};
    //第一个选中的【商品类型】
    var oneCommodityDlrTypeId = 0;
    var commDlrTypeJson = {$comm_dlr_type_json};


    $(".comm-type-search2").on('click',function(){
        var obj = $(this).parents('.form-group');
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();

        //验证【商品种类】和【商品类型】是否已选择
        // if(commodity_class==0 || commodity_dlr_type_id==0){
        //     isInitComm = 0;
        // }else{
        //     isInitComm = 1;
        // }
        var user_segment = $('input:radio[name=user_segment]:checked').val()
        if(user_segment == 'undefined') user_segment = 0;
        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id+ '&user_segment='+user_segment;

    });
    $("#sku-confirm-all").on('click',function(){
        var isSkuConfirmAll = $(this).data('is-sku-confirm-all');
        if(isSkuConfirmAll == 0){
            layer.msg('当前页已全部添加，请不要重复操作');
            return false;
        }

        var obj = $(this).parents('.form-group');
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();

        //验证【商品种类】和【商品类型】是否已选择
        if(commodity_class==0){
            layer.msg('请选择商品种类');
            return;
        }
        if(commodity_dlr_type_id==0){
            layer.msg('请选择商品类型');
            return;
        }

        if(oneCommodityClass==0){
            oneCommodityClass = commodity_class;
        }
        if(oneCommodityClass != commodity_class){
            $("#sku-modal").modal('hide');
            var msg1 = "只能添加“"+commodityClassJson[oneCommodityClass]+"“的商品，该商品为“"+commodityClassJson[commodity_class]+"”";
            layer.msg(msg1);
            return;
        }

        if(oneCommodityDlrTypeId==0){
            oneCommodityDlrTypeId = commodity_dlr_type_id;
        }
        if(oneCommodityDlrTypeId != commodity_dlr_type_id){
            $("#sku-modal").modal('hide');
            var msg2 = "只能添加同种类型的商品，该商品为“"+commDlrTypeJson[oneCommodityDlrTypeId]+"“";
            layer.msg(msg2);
            return;
        }

        //先请求列表数据，同时进行批量数据操作
        commTypeSearch2(1);
    });
    //活动商品列表搜索
    function commTypeSearch2(isSkuConfirmAll = 0){
        var commodity_class = $("select[name='commodity_class']").val();
        var commodity_dlr_type_id = $("select[name='commodity_dlr_type_id']").val();
        var comm_parent_id = $("select[name='comm_parent_id']").val();
        var page = $("#sku-confirm-all").data("current-page");
        var user_segment = $('input:radio[name=user_segment]:checked').val()
        if(user_segment == 'undefined') user_segment = 0;
        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id+ '&page='+page + '&user_segment='+user_segment;

        //请求公示接口数据
        commTypeSearch(isSkuConfirmAll)
    }


    //已经添加过的商品
    var comm_set_id_arr = [];
    var commodity_dlr_type_selected = 0;

    $("body").on('click','#GWDLR',function(){
        if($("#GWDLR").is(':checked')){
            $('#dealer_select').removeClass('hidden');
        }else{
            $('#dealer_select').addClass('hidden');
        }
    });

    var dlr_data = <?= $dlr_data ?>;

    $("body").on("click",'#dlr_show',function () {
        var select_data = $("#dlr_hide").val().split(',');
        Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
            $("#dlr_show").val(dlr_name.join(','));
            $("#dlr_hide").val(dlr_code.join(','));
        });
    })

    // 添加小规则
    $(document).on('click','#ruleEdit .fa-plus-circle',function(){
        var l=$(this).parents('.ruleEdit').find('.row').length;
        if(l>2){
            layer.msg('最多设置3个层级');
            return false;
        }
        $(this).parents('.ruleEdit').find('.row:last').after('<div class="row row2">满<input type="text"'
        +' class="form-control piece '
        +' form-j" >元减<input type="text" class="form-control discount form-z">元<i class="fa fa-2x fa-minus-circle'
        +' text-danger"></i></div>');
    })

    // 删除小规则
    $(document).on('click','#ruleEdit .row2 .fa-minus-circle',function(){
        $(this).closest('.row').remove();
    })

    var get_data=function(){
        data_l=[];

        var user_segment = $('input:radio[name=user_segment]:checked').val()
        if(user_segment == 1){


        }else if(user_segment == 2){

        }else{
            $('#ruleEdit').find('.form-control').each(function(){
                if($(this).val()!==''){
                    data_l.push($(this).val());
                }
            })
            if(data_l.length!=$('#ruleEdit').find('.form-control').length ){
                alert('请填写完整的满减信息')
                return false;
            }else{
                var data=[];
                $('.ruleEdit .row2').each(function(){
                    var data2=[];
                    data2[0]=$(this).find('.form-j').val();
                    data2[1]=$(this).find('.form-z').val();
                    data.push(data2);
                })

                return data;
            }
        }

    }
    
    $('.del-commodity').on('click',function (){
        if($("table #haved-commodtiy tr:visible").length > 0){
            var index = layer.open({
                title: ['操作提醒'],
                btn: ['确认', '取消'],
                content:"<div style='font-size: 15px'>您选择对商品列表中全部商品进行移除操作吗?</div>",
                yes: function (res) {
                    $("#haved-commodtiy").empty();
                    $("#sku-confirm-all")
                        .removeClass("btn-default active")
                        .addClass("btn-white")
                        .html('批量添加');
                    $("#sku-confirm-all").data('is-sku-confirm-all',1)

                    var commodity_class = $("select[name='commodity_class']").val();
                    var commodity_dlr_type_id = $("select[name='commodity_dlr_type_id']").val();
                    var comm_parent_id = $("select[name='comm_parent_id']").val();
                    var page = $("#sku-confirm-all").data("current-page");
                    var user_segment = $('input:radio[name=user_segment]:checked').val()
                    if(user_segment == 'undefined') user_segment = 0;
                    ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id+ '&page='+page + '&user_segment='+user_segment+'&commodity_class='+commodity_class+'&comm_parent_id='+comm_parent_id;

                    initComm(ajaxCommUrl);
                    commodity_select()
                    comm_set_id_arr = [];
                    skuConfirmAllCurrentPage = []
                    layer.close(index);
                }
            })
        }

    })

</script>
    <script src="__STATIC__admin_v2/js/full_discount_live.js?rand=*******"></script>
    <script src="__STATIC__admin_v2/js/commodity_page.js?rand=*******"></script>
{include file="card:act_rule" /}
    {include file="card:act_card_rule" /}
{/block}
