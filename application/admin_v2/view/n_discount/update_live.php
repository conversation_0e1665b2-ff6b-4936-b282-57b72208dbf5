{extend name="public:base_layout" /}

{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<link href="__STATIC__admin_v2/css/jBootsrapPage.css" rel="stylesheet"/>
<style>
    .form-group dd{
        margin-top: 5px;
    }
    .cover-image{
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-left: 20px;
    }

    /*添加规则200515*/
    .ruleEdit {
        font-size: 14px;
        padding:0 15px 15px;
    }
    .ruleEdit .form-control {
        width:80px;
        display: inline-block;
        margin:0 10px;
    }
    .ruleEdit .row {
        margin-bottom:10px;
        overflow: hidden;
    }
    .ruleEdit .row1 .fa-minus-circle {
        float: right;
        position: relative;
        top:3px;
        margin-right:10px;
    }
    .ruleEdit .row .fa.fa-2x {
        vertical-align: -5px;
        margin:0 10px;
    }
    .ruleEdit .items-wp {
        margin-bottom:20px;
    }
    .ruleEdit .item {
        padding:15px 0;
        border-bottom:1px dashed #ccc;
    }
    .ruleEdit .btn-wp {
        margin-right:10px;
    }

    .level_name_box{
        width: 140px;float: left;text-align: center;padding-top: 5px;
    }
    .level_name_item_box{
        float:left;width: 70px
    }
    .btnc{
        text-align: center;padding: 10px 0 0 10px;float:left;
    }
    .stylelt{
        width: 80px;float: left;text-align: center;
    }
    .level_name_box_1{
        height:100px;
    }
    .level_name_box_2{
        height:100px;
    }
    .radio, .radio-inline{
        line-height:unset !important;
    }
    .level_item_level .user_level{
        margin-left:0 !important;
    }
    .level_zhe_box >div{
        width:65px;
        margin-right:10px;
        float:left;
    }
    .level_zhe_car >div{
        width:65px;
        margin-right:10px;
        float:left;
    }
    .level_jian{width:6%}
    .level_pi{ width:15%}
    .level_zhe_box{width:60%}
    .level_zhe_car{width: 25%}

</style>
{/block}

{block name="content"/}

<div class="panel-body">

    <div class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">编辑-N件N折</legend>
        <form id="add-form" class="form-horizontal form-bordered" data-parsley-trigger="change">

            {if (in_array($row['act_status'],[2,3]))/}
                <input type="hidden" name="start_time" value="{$row['start_time']}">
                <input type="hidden" name="end_time" value="{$row['end_time']}">
                <input type="hidden" name="user_segment" value="{$row['user_segment']}">
                <input type="hidden" name="user_segment_options" value="{$row['user_segment_options']}">
            {/if}


            <div class="form-group">
                <label class="control-label col-md-2">活动名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" name="title" placeholder="请输入活动名称" value="{$row['title']}" class="form-control " data-parsley-required="true" data-parsley-length="[1, 15]">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">起止时间<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-3 p-l-0" style="">
                        <input type="text" name="start_time" value="{$row['start_time']}"{if (in_array($row['act_status'],[2,3]))/} disabled {/if}placeholder="请输入开始时间" class="form-control datetimepicker3" data-parsley-required="true"  >
                    </div>
                    <div class="col-md-3">
                        <input type="text" name="end_time" value="{$row['end_time']}" {if (in_array($row['act_status'],[2,3]))/} disabled {/if} placeholder="请输入结束时间" class="form-control datetimepicker3" data-parsley-required="true"  >
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">促销标签:</label>
                <div class="col-md-5" style="width: 70%;"><input type="text" maxlength="4"  name="tag" value="{$row.tag}" style="width:200px;background-color: #fff;background-image: none;
    border: 1px solid #ccc;border-radius: 4px;box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset; color: #555;font-size: 14px;height: 34px;line-height: 1.42857;padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;border-radius: 3px;box-shadow: none; font-size: 12px;" {eq name="$row.is_enable" value="1"} {if(in_array($row.act_status, [3]))} disabled {/if}{/eq} />
                    (促销标签是商家对促销动作的别名操作，用于前台显示，最多可输入4个字符，非必填选项）
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">定向人群:</label>
                <div class="col-md-9">
                    <dd>未选择定向人群则代表所有用户可参与活动。选择定向人群后若更改人群类型，已添加的商品列表会被清空！活动开始后不可修改</dd>

                    <?php if ($set_type != 6) :?>
                    <label class="radio-inline">
                        <input type="radio" name="user_segment" class="user_segment" value="1" <?php if($row['user_segment']==1) echo 'checked';?>  {if (in_array($row['act_status'],[2,3]))/} disabled {/if}   data-parsley-multiple="dis_type" /> 会员
                    </label>
                    <?php endif;?>
                    <label class="radio-inline">
                        <input type="radio" name="user_segment" class="user_segment" value="2" <?php if($row['user_segment']==2) echo 'checked';?> {if (in_array($row['act_status'],[2,3]))/} disabled {/if}   data-parsley-multiple="dis_type"/> 车主
                    </label>
                    <div style="margin-top: 30px;display: none;" id="user_level_box">
                        <dd>可参与的会员等级，若选择了某个会员等级后，该等级及更高等级的会员都有活动资格，高等级用户优惠力度需大于等于低等级用户。</dd>

                        <?php foreach($user_level as $key=>$val):?>
                            <label class="radio-inline">
                                <input type="radio" name="user_segment_options" class="user_segment_options" id="{$val['order_no']}" <?php if($val['value_code'] == $row['user_segment_options']):?> checked <?php endif;?> {if (in_array($row['act_status'],[2,3]))/} disabled {/if} value="{$val['value_code']}" title="{$val['county_name']}" data-parsley-required="true" data-parsley-multiple="dis_type"/>{$val['county_name']}
                            </label>
                        <?php endforeach;?>
                    </div>
                </div>
            </div>


            <div class="form-group"  id="rule_old">
                <label class="control-label col-md-2">活动金额设定<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10 ruleEdit" id="ruleEdit">
                    <div class="items-wp">
                        {volist name="$dis_info" id="v"}
                        <div class="item" id="item-original">
                            {volist name="$v.list" id="vv"}
                            <div class="row row2">
                                <input type="text" value="{$vv.piece}" class="form-control piece form-j" data-parsley-required="true" >件<input type="text" class="form-control discount form-z"  value="{$vv.discount}" data-parsley-required="true" >折<i class="fa fa-2x fa-plus-circle text-success"></i>
                                <span></span>
                            </div>
                            {/volist}
                        </div>
                        {/volist}
                    </div>
                    <dd>活动开始后不可修改，请慎重选择！最多可添加三个折扣等级</dd>

                </div>
            </div>


            <div class="form-group"  id="rule_new" style="display: none">
                <label class="control-label col-md-2">活动金额设定<i class="m-r-3 text-danger">*</i>:</label>
                <div  class="col-md-8" id="discount">
                    <div class="input-group col-md-12">
                        <dd>活动开始后不可修改，请慎重选择！最多可添加三个折扣等级</dd>

                        <!--会员折扣-->
                        <div id="member_box" class="limit_boxs" >
                            <div class="row dis_1 slope-dis"  style="margin-top: 12px;">
                                <div  class="rule_new_box">
                                    <div class="level_item_level" style="height:60px">
                                        <div class="stylelt level_jian" style="color:transparent">占</div>
                                        <div class="level_name_box stylelt level_pi" style="color:transparent">占</div>
                                        <div class="level_zhe_box stylelt" id="county_name">

                                            <?php foreach($old_user_level as $key=>$val):?>
                                                <div class="stylelt user_level county_name" data-title="{$val['county_name']}" data-code="{$val['value_code']}">{$val['county_name']}</div>
                                            <?php endforeach;?>
                                        </div>

                                    </div>
                                    <?php $i=0;?>
                                    <?php if(!empty($user_n_dis_info)):?>
                                    <?php foreach ($user_n_dis_info as $key => $item):?>
                                    <div class="level_item level_name_box_1 user_level_{$key}">
                                        <div class="stylelt level_jian">
                                            <input size="4"  class="piece form-j" value="{$item['piece']}" {if (in_array($row['act_status'],[2,3]))/} disabled {/if} data-parsley-required="true"><span class="c_dis_type">件</span>
                                        </div>
                                        <div class="level_name_box stylelt level_pi">
                                            <div class="level_name_item_box" ><a href="javascript:void(0);" data-type="text" class="btn btn-primary btn-sm editable editable-click {if (in_array($row['act_status'],[1]))/} batch_price {/if} "  data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>
                                        </div>
                                        <div class="level_zhe_box stylelt" id="value_code">
                                            <?php foreach($old_user_level as $key=>$val):?>
                                                <div class="stylelt level_jian form-z value_code">
                                                    <input size="4" class="v_dis_type" value_code="{$val['value_code']}" value="{$item['discount'][$val['value_code']] ?? ''}" {if (in_array($row['act_status'],[2,3]))/} disabled {/if}/><span class="c_dis_type">折</span>
                                                </div>
                                            <?php endforeach;?>
                                        </div>
                                        {if ($row['act_status'] == 1)/}
                                        <?php if ($i==0):?>
                                            <div class="btnc"><i class="fa fa-2x fa-plus-circle text-success adbtn"></i></div>
                                        <?php else:?>
                                            <div class="btnc"><i  data-toggle="modal" class="fa fa-2x fa-minus-circle text-danger delbtn"></i></div>
                                        <?php endif;?>
                                        {/if}
                                    </div>
                                        <?php $i=$i+1; ?>
                                    <?php endforeach;?>
                                    <?php else:?>
                                        <div id="member_box" class="limit_boxs">
                                            <div class="row dis_1 slope-dis"  style="margin-top: 12px;">
                                                <div class="rule_new_box">
                                                    <div class="level_item_level" style="height:60px">
                                                        <div class="stylelt level_jian" style="color:transparent">占</div>
                                                        <div class="level_name_box stylelt level_pi" style="color:transparent">占</div>
                                                        <div class="level_zhe_box stylelt" id="county_name" >
                                                            <?php foreach($user_level as $key=>$val):?>
                                                                <div class="stylelt user_level" data-title="{$val['county_name']}" data-code="{$val['value_code']}">{$val['county_name']}</div>
                                                            <?php endforeach;?>
                                                        </div>
                                                    </div>
                                                    <div class="level_item level_name_box_1">
                                                        <div class="stylelt level_jian">
                                                            <input size="4"  class=" piece form-j" data-parsley-required="true"><span class="c_dis_type">件</span>
                                                        </div>
                                                        <div class="level_name_box stylelt level_pi">
                                                            <div class="level_name_item_box" ><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click batch_price" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>
                                                        </div>
                                                        <div class="level_zhe_box stylelt" id="value_code">

                                                            <?php foreach($user_level as $key=>$val):?>
                                                                <div class="stylelt level_jian form-z">
                                                                    <input size="4" class="v_dis_type" value_code="{$val['value_code']}"  /><span class="c_dis_type">折</span>
                                                                </div>
                                                            <?php endforeach;?>
                                                        </div>

                                                        <div class="btnc level_btn">
                                                            <i class="fa fa-2x fa-plus-circle text-success adbtn"></i>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif;?>
                                </div>


                            </div>
                        </div>



                        <!--车主折扣-->
                        <div id="car_onwer_box" class="limit_boxs">
                            <div class="row dis_1 slope-dis"  style="margin-top: 12px;">
                                <div class="rule_new_box">
                                    <div class="level_item_level" style="height:60px">
                                        <div class="stylelt level_jian" style="color:transparent">占</div>
                                        <div class="level_name_box stylelt level_pi" style="color:transparent">占</div>
                                        <div class="level_zhe_car stylelt">
                                            <div class="stylelt">非车主</div>
                                            <?php if ($set_type == 6):?>
                                                <div class="stylelt">日产车主</div>
                                                <div class="stylelt" >Ariya车主</div>
                                            <?php else:?>
                                                <div class="stylelt" >车主</div>
                                            <?php endif;?>
                                        </div>
                                    </div>

                                    <?php $i=0;?>
                                    <?php if(!empty($car_n_dis_info)):?>
                                        <?php foreach ($car_n_dis_info as $key => $item):?>
                                            <div class="level_item level_name_box_2">
                                                <div class="stylelt level_jian">
                                                    <input size="4" class="piece form-j" value="{$item['piece']}" data-parsley-required="true" {if (in_array($row['act_status'],[2,3]))/} disabled {/if}><span class="c_dis_type">件</span>
                                                </div>

                                                <div class="level_name_box stylelt level_pi">
                                                    <div class="level_name_item_box" ><a href="javascript:void(0);" data-type="text" class="btn btn-primary btn-sm editable editable-click {if (in_array($row['act_status'],[1]))/} car_batch_price {/if}" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>
                                                </div>
                                                <div class="level_zhe_car stylelt">
                                                    <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_1"  value="{$item['discount']['NONE'] ?? ''}" {if (in_array($row['act_status'],[2,3]))/} disabled {/if} /><span class="c_dis_type">折</span></div>
                                                    <?php if ($set_type == 5):?>
                                                    <!--日产-->
                                                    <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_2"  value="{$item['discount']['N'] ?? ''}" {if (in_array($row['act_status'],[2,3]))/} disabled {/if} /><span class="c_dis_type">折</span></div>
                                                    <?php elseif($set_type == 7) :?>
                                                    <!--启辰-->
                                                    <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_2"  value="{$item['discount']['V'] ?? ''}" {if (in_array($row['act_status'],[2,3]))/} disabled {/if} /><span class="c_dis_type">折</span></div>
                                                    <?php endif; ?>
                                                    <!--pz1a-->
                                                    <?php if ($set_type == 6):?>
                                                        <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_2"  value="{$item['discount']['N'] ?? ''}" {if (in_array($row['act_status'],[2,3]))/} disabled {/if} /><span class="c_dis_type">折</span></div>

                                                        <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_3"  value="{$item['discount']['P'] ?? ''}" {if (in_array($row['act_status'],[2,3]))/} disabled {/if} /><span class="c_dis_type">折</span></div>
                                                    <?php endif;?>
                                                </div>
                                                {if ($row['act_status'] == 1)/}
                                                <?php if ($i==0):?>
                                                    <div class="btnc"><i class="fa fa-2x fa-plus-circle text-success adbtn2"></i></div>
                                                <?php else:?>
                                                    <div class="btnc"><i  data-toggle="modal" class="fa fa-2x fa-minus-circle text-danger delbtn2"></i></div>
                                                <?php endif;?>
                                                {/if}

                                            </div>
                                            <?php $i=$i+1; ?>
                                        <?php endforeach;?>
                                    <?php else:?>
                                        <div class="level_item level_name_box_2">
                                            <div class="stylelt level_jian">
                                                <input size="4"  class=" piece form-j" data-parsley-required="true"><span class="c_dis_type">件</span>
                                            </div>
                                            <div class="level_name_box stylelt level_pi">
                                                <div class="level_name_item_box" ><a href="#" data-type="text" class="btn btn-primary btn-sm editable editable-click car_batch_price" data-value="" data-placeholder="批量设置" data-title="批量设置">批量设置<i class="fa fa-edit m-l-5"></i></a></div>
                                            </div>
                                            <div class="level_zhe_car stylelt">
                                                <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_1"/><span class="c_dis_type">折</span></div>
                                                <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_2" data-parsley-required="true"/><span class="c_dis_type">折</span></div>

                                                <?php if ($set_type == 6):?>
                                                    <div class="stylelt"><input size="4" class="carv_dis_type carv_dis_type_3" data-parsley-required="true"/><span class="c_dis_type">折</span></div>
                                                <?php endif;?>
                                            </div>
                                            <div class="btnc"><i class="fa fa-2x fa-plus-circle text-success adbtn2"></i></div>
                                        </div>
                                    <?php endif;?>
                                </div>
                                <dd>若未设置非车主价则非车主无参与资格，如需设置多个梯度，请保证多个梯度资格一致，车主的优惠力度需大于等于非车主</dd>
                            </div>
                        </div>


                    </div>
                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">活动描述:</label>
                <div class="col-md-5">
                    <input type="text" name="des" id="des" placeholder="请输入活动描述" value="{$row['des']}" class="form-control" data-parsley-length="[0, 30]">
                    <dd>活动描述是商家对促销活动的补充说明文字，用于前台显示，最多可输入30个字符，非必填选项</dd>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">业务归属:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <select name="gather_id" class="form-control input-sm ">
                            <option value=""> 全部 </option>
                            {volist name="$gather_list" id="val" key="key"}
                            <option value="{$val['id']}" {if($row['gather_id'] == $val['id'])}selected{/if} > {$val['name']} </option>
                            {/volist}
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">主题活动:</label>
                <div  class="col-md-5" id="is_enable">
                    <input type="text" name="theme_name" id="theme_name" placeholder="请输入主题活动" value="{$row['theme_name']}" class="form-control" data-parsley-length="[0, 100]">
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否PV补贴:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_pv_subsidy" type="checkbox" data-input-name="is_pv_subsidy" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭"  
                            <?php if($row['is_pv_subsidy']==1) echo 'checked';?> />
                            {if(in_array($row.act_status, [3]))}
                            <input type="hidden" name="is_pv_subsidy" value="{$row.is_pv_subsidy}">
                            {/if}
                        </label>
                    </div>
                </div>
            </div>


            <div class="form-group"  >
                <label class="control-label col-md-2">是否叠加券:</label>
                <div  class="col-md-5" id="card_available">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_card_change" type="checkbox" name="switch" data-input-name="card_available"
                                   data-input-true="1" data-input-false="0"
                                   data-size="small" data-on-text="可用" data-off-text="不可用"
                                <?php if($row['card_available']==1) echo 'checked';?> />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group no_card ">
                <label class="col-md-2 control-label" id="card_activity">选择可叠加的券:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control card_activity" name="card_activity" placeholder="点击选择" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="{$row['commodity_card_name']}">
                    <input type="hidden" class="form-control hide_card_ids" name="rel_card_ids" value="{$row['rel_card_ids']}">
                </div>
                <div class="bottom-wp">
                    <div class="bottom-left">
                        已选<input type="" class="num_card_ids" style="width: 20px;outline:none;border: 0; readonly" value="{$row['num_card']}">
                        张
                    </div>
                </div>
            </div>

            <div class="form-group ">
                <label class="col-md-2 control-label" id="e3s_activity">关联E3S活动:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control e3s_activity" name="e3s_activity" placeholder="请选择活动" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="{if (!empty($row['e3s_activity_id']))}{$row['e3s_activity_id']} | {$row['e3s_activity_name']}{/if}">
                    <input type="hidden" id="e3s_activity_id" name="e3s_activity_id" value="{$row['e3s_activity_id']}">
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label" id="activity_type">活动设置类型:</label>
                <div class="col-md-6">
                    <select class="form-control width-300" name="activity_type" >
                        <option value="0">请选择</option>
                        <option value="1" {if($row['activity_type'] == 1)}selected{/if}>备件</option>
                        <option value="2" {if($row['activity_type'] == 2)}selected{/if}>工时</option>
                        <option value="3" {if($row['activity_type'] == 3)}selected{/if}>赠品</option>
                    </select>
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label" id="settlement_rule">关联结算规则:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control settlement_rule" name="settlement_rule" id="template_name" placeholder="请选择选择结算规则" data-parsley-group="wizard-step-1"  value="{if ($row['settlement_rule_id'] > 0)}{$row['settlement_rule_name']}{/if}">
                    <input type="hidden" id="settlement_rule_id" name="settlement_rule_id" value="{$row['settlement_rule_id']}">
                    <input type="hidden" id="settlement_rule_value" name="settlement_rule_value" value="{$row['settlement_rule_value']}">
                    <input type="hidden" id="settlement_rule_type" name="settlement_rule_type" value="{$row['settlement_rule_type']}">
                    <input type="hidden" id="act_sett_standard" name="act_sett_standard" value="{$row['act_sett_standard']}">

                </div>
            </div>

            <div class="form-group" id="up_down">
                <label class="control-label col-md-2">活动渠道:<i class="m-r-3 text-danger">*</i></label>
                <div class="col-md-10">
                    <div>
                        <?php $row_code = explode(',', $row['up_down_channel_dlr']);
                        foreach ($up_down_channel as $key => $val):
                            if($live_type == 1): if (in_array($key,['PZ1AAPP','PZ1ASM'])):?>
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                            <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endif; else: ?>
                                <?php if($live_type == 2): if (in_array($key,['QCAPP','QCSM'])):?>
                                    <label class="checkbox-inline">
                                        <input type="checkbox"
                                               name="up_down_channel[]" data-parsley-required="true"
                                                <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                               value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                    </label>
                            <?php endif; else: if (!in_array($key,['PZ1AAPP','PZ1ASM','QCAPP','QCSM','TOBPC'])):?>
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                            <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endif; endif; endif; endforeach; ?>
                    </div>
                    <div style="margin-top:20px;" class="<?= $dlr_hidden ?>" id="dealer_select">
                        <input id="dlr_show" type="text" class="form-control width-300" placeholder="请点击选择经销商"
                               data-parsley-required="true" value="<?=$dlr_str?>">
                        <input id="dlr_hide" type="text" name="dlr_code" class="hidden"
                               value="<?=$row['up_down_channel_dlr']?>">
                    </div>

                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">活动状态:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_enable" type="checkbox" data-input-name="is_enable" data-input-true="1" data-input-false="0"
                                   data-size="small" data-on-text="开启" data-off-text="关闭"
                                <?php if($row['is_enable']==1) echo 'checked';?> />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">活动商品<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left;width:60%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品分类:</label>
                            <select class="form-control input-sm element default-select2 col-md-3" name="comm_parent_id" id="slt_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($comm_parent_list as $key=>$val):?>
                                    <option value="{$val['id']}" >{$val['comm_type_name']}</option>
                                <?php endforeach;?>
                            </select>
                            <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                            <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                        </div>
                        <div style="float:left;width:30%">
                            <label class="control-label col-md-2" style="padding:  5px 5px;  width: auto;">商品名称:</label>
                            <input type="text" class="form-control input-sm element col-md-3 m-r-2 " style=" width: 78%"
                                   name="commodity_name" placeholder="请输入商品名称">
                        </div>
                    </div>
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left;width:60%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品种类:</label>
                            <select class="form-control input-sm element default-select2 col-md-3 comm-type-search2"
                                    name="commodity_class" onchange="commTypeSearch2()"  id="slt_comm_type_id" style="width: 76%">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($commodity_class as $key=>$val):?>
                                    <option value="{$key}" >{$val}</option>
                                <?php endforeach;?>
                            </select>
                        </div>

                        <div   id="btn-sm-div" style="float:left;width:40%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品类型:</label>
                            <select class="form-control input-sm element default-select2 col-md-3 comm-type-search2"
                                    name="commodity_dlr_type_id" onchange="commTypeSearch2()" id="slt_comm_type_id" style="width: 58%">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($comm_dlr_type_list as $key=>$val):?>
                                    <option value="{$val['id']}" >{$val['inner_name']}</option>
                                <?php endforeach;?>
                            </select>
                            <button id="comm-type-search" type="button" class="btn btn-sm btn-success comm-type-search2"><i class="fa fa-search"></i>搜索</button>
                        </div>
                    </div>
                    <div>
                        <div class="table-scrollable">
                            <table id="" class="table table-hover">
                                <thead>
                                <th class="text-center">商品规格</th>
                                <th class="text-center">上架渠道</th>
                                <th class="text-center">原价</th>
                                <th class="text-center">库存</th>
                                <th class="text-center">
                                    <a
                                            data-current-page="1"
                                            data-is-sku-confirm-all="1"
                                            data-comm-id="all"
                                            home="all"
                                            data-comm-set-id="all"
                                            commodity_class=""
                                            data-dlr-code="all"
                                            class="btn btn-sm btn-default btn-white"
                                            id="sku-confirm-all"
                                    >批量添加</a>
                                </th>
                                </thead>
                                <tbody id="add-comm-tbody">

                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ul class="pagination" id="comm-pagination"></ul>
                        </div>
                    </div>

                    <legend class="pull-left width-full" style="font-size: 18px;">
                        <div class="col-md-10">
                            已选活动商品
                        </div>
                        <div class="col-md-2">
                            <a class="btn btn-danger btn-sm m-r-5 m-b-5 del-commodity">删除全部商品</a>
                        </div>
                    </legend>

                    <div>
                        <div class="table">
                            <table id="commodity_select" class="table">
                                <thead></thead>
                                <tbody id="haved-commodtiy">
                                <?php foreach($row_comm as $key=>$val):?>
                                    <tr class="info haved-add" id="t_{$val.commodity_set_id}" commid="{$val.commodity_id}" set_id="{$val.commodity_set_id}" data="">
                                        <td style="width: 350px;">
                                            <input class="home" value="{$val.is_home}" type="hidden" />
                                            <input class="commodity_class" value="{$val.commodity_class}" type="hidden" />
                                            <input type="hidden" class="com_type_id" value="{$val.comm_type_id}"/>
                                            <img class="cover-image" src="{$val['cover_image']}">{$val.commodity_name}
                                        </td>
                                        <td class="text-right"><button class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list" data-comm_name="{$val.commodity_name}" data-com-id="{$val['commodity_id']}" data-comm_set_id ="{$val['commodity_set_id']}" >删除</button>
                                        </td>
                                    </tr>
                                <?php endforeach;?>
                                </tbody>
                            </table>
                            <div id="pagiDiv" style="width:100%;text-align: center;display: none">
                                <span id="spanFirst">首页</span>
                                <span id="spanPre">上一页</span>
                                <span id="spanNext">下一页</span>
                                <span id="spanLast">尾页</span>
                                第 <span id="spanPageNum"></span> 页/共 <span id="spanTotalPage"></span> 页
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2"></label>
                <div class="col-md-5">
                    <input type="hidden" name="set_type" value="{$row['set_type']}">
                    <?php if($row['act_status']!=3) {?>
                    <a href="javascript:;" class="btn btn-sm btn-primary btn-sm"  id="put-form">确认</a>
                    <?php }?>
                </div>
            </div>

            <input type="hidden" name="id" value="{$row['id']}">

        </form>
    </div>


    <!----begin 查看规格---->
    <div class="modal fade" id="sku-modal" data-per-page="" data-comm-id="" data-comm-set-id="" data-type="">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择商品: <span id="comm-name-header"></span></h4>
                </div>
                <input id="modal_home"  type="hidden" value="" />
                <input id="modal_commodity_class"  type="hidden" value="" />
                <input id="commodity_dlr_type_id"  type="hidden" value="" />

                <div class="modal-body" style="">
                    <div class="alert alert-info m-b-0" style="padding: 8px;padding-top: 5px;">
                        <h6>操作提示</h6>
                        <p>默认该商品全部规格选中参加该项促销活动,如某个规格不想参与该项活动,可通过取消选择排除掉;被排除的规格将保持原价</p>
                    </div>
                    <d></d><br>

                    <div class="form-group">
                        <label class="control-label col-md-3" style="padding-left: 11px; padding-top: 10px; width: 100px;">折扣价设置:</label>
                        <div class="col-md-5" id="comm_discount">
                            <div class="input-group col-md-12" >
                                <input type="text" data-parsley-errors-container="#comm_discount" name="comm_discount" readonly="readonly" class="form-control col-md-3" maxlength="3" data-parsley-type="number"  data-parsley-range="[0.1,9.9]">
                                <span class="input-group-addon" >折</span>
                            </div>
                        </div>
                    </div>
                    <div class="hide">
                        <div class="sku-image" style="display: inline-block;">
                            <img class=" cover-image" src="">
                        </div>
                        <div style="display: inline-block;">
                            <div  class="sku-comm">
                            </div>
                        </div>
                    </div>

                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th class="col-md-6" >商品规格</th>
                            <th >原价</th>
                            <th style="text-align: center">
<!--                                <a href="#" id="all_price" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格<i class="fa fa-edit m-l-5"></i></a>-->
                                价格
                            </th>
                            <th>库存</th>
                        </tr>
                        </thead>
                        <tbody class="sku-tb" id="change_sku_tb">

                        </tbody>
                    </table>

                    <?php if($set_type==1):?>
                        <div>
                            <label class="control-label col-md-1" style="width: 80px;">经销商<i class="m-r-3 text-danger">*</i>:</label>
                            <textarea class="form-control sku-dlr" id="select-dlr" readonly placeholder="请点击选择经销商"  data-parsley-required="true" data-sku-dlr="" style="width: 470px; height: 50px;"></textarea>
                        </div>
                    <?php endif;?>

                </div>

                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >关闭</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary  " id="sku-confirm" >确定</a>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除提示 -->
    <div class="modal fade" id="modal-del">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" id="btn-pop-del-cancel2" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">提示</h4>
                </div>
                <div class="modal-body">
                    是否删除该规则
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" id="btn-pop-del-cancel" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-success" id="btn-pop-del" data-dismiss="modal">确定</a>
                </div>
            </div>
        </div>
    </div>
    <!----begin 删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>确定删除该商品?</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"/}

<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js"></script>

<script>
    var admin_type="{$admin_type}";
    var getSkuUrl ="{:url('getSku')}";
    var limit_discount_id=0;
    var getDlr_url="{:url('ajaxGetDlr')}";
    //异步请求数据地址
    var ajaxCommUrl = "{:url('ajaxGetLiveCommodityList',['live_type'=>$live_type,'pagesize'=>10,'set_type'=>$set_type,'active_id'=>$row['id']])}";
    //商品
    var save_url    ="{:url('saveLive',['live_type'=>$live_type])}";
    var index_url   ="{:url('live',['set_type'=>$set_type,'live_type'=>$live_type,])}";
    var set_type    ="{$set_type}";
    //已经添加过的商品
    var comm_set_id_arr = {$comm_set_id_arr|default=[]};
    var comm_id = {$comm_id|default=[]};
    var action = "add";
    var commodity_dlr_type_selected = 0;
    var ajaxGetCommTypeIdUrl ="{:url('ajaxGetCommTypeId')}";

    /**
     * 新增参数验证
     * @type {string}
     */
    var ajaxCommUrl2 = ajaxCommUrl;
    var isInitComm = 1;
    var isInitCommMsg = '';
    //已全选的分页
    var skuConfirmAllCurrentPage = [];
    //当前页所有的comm_set_id值
    currentPageCommSetIds = [];
    //第一个选中的【商品种类】
    var oneCommodityClass = 0;
    var commodityClassJson = {$commodity_class_json};
    //第一个选中的【商品类型】
    var oneCommodityDlrTypeId = 0;
    var commDlrTypeJson = {$comm_dlr_type_json};

    var start_time = "{$row['start_time']}";
    var end_time = "{$row['end_time']}";
    var urlCardparam    ="{:url('ajaxGetCard')}"  + "?set_type=" + {$row['set_type']};
    var user_level_list = {:json_encode($user_level)}


    $(".comm-type-search2").on('click',function(){
        var obj = $(this).parents('.form-group');
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();

        //验证【商品种类】和【商品类型】是否已选择
        // if(commodity_class==0 || commodity_dlr_type_id==0){
        //     isInitComm = 0;
        // }else{
        //     isInitComm = 1;
        // }

        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id;

    });
    $("#sku-confirm-all").on('click',function(){
        var isSkuConfirmAll = $(this).data('is-sku-confirm-all');
        if(isSkuConfirmAll == 0){
            layer.msg('当前页已全部添加，请不要重复操作');
            return false;
        }

        var obj = $(this).parents('.form-group');
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();

        //验证【商品种类】和【商品类型】是否已选择
        if(commodity_class==0){
            layer.msg('请选择商品种类');
            return;
        }
        // if(commodity_dlr_type_id==0){
        //     layer.msg('请选择商品类型');
        //     return;
        // }

        if(oneCommodityClass==0){
            oneCommodityClass = commodity_class;
        }
        if(oneCommodityClass != commodity_class){
            $("#sku-modal").modal('hide');
            var msg1 = "只能添加“"+commodityClassJson[oneCommodityClass]+"“的商品，该商品为“"+commodityClassJson[commodity_class]+"”";
            layer.msg(msg1);
            return;
        }

        // if(oneCommodityDlrTypeId==0){
        //     oneCommodityDlrTypeId = commodity_dlr_type_id;
        // }
        // if(oneCommodityDlrTypeId != commodity_dlr_type_id){
        //     $("#sku-modal").modal('hide');
        //     var msg2 = "只能添加同种类型的商品，该商品为“"+commDlrTypeJson[oneCommodityDlrTypeId]+"“";
        //     layer.msg(msg2);
        //     return;
        // }

        //先请求列表数据，同时进行批量数据操作
        commTypeSearch2(1);
    });
    //活动商品列表搜索
    function commTypeSearch2(isSkuConfirmAll = 0){
        var commodity_class = $("select[name='commodity_class']").val();
        var commodity_dlr_type_id = $("select[name='commodity_dlr_type_id']").val();
        var comm_parent_id = $("select[name='comm_parent_id']").val();
        var page = $("#sku-confirm-all").data("current-page");
        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id+ '&page='+page;

        //请求公示接口数据
        commTypeSearch(isSkuConfirmAll)
    }

    $(function(){
        // 添加大规则
        $('#btn-addRule2').on('click',function(){
            var l=$('#ruleEdit .item').length;
            if(l<2){
                $('#item-original').after('<div class="item" id="item-original"><div class="row row1"><input type="text" class="form-control">人下单购买享折扣优惠<i href="#modal-del" data-toggle="modal" class="fa fa-2x fa-minus-circle text-danger"></i></div><div class="row row2"><input type="text" class="form-control form-j">件<input type="text" class="form-control form-z" maxlength="3" data-parsley-type="number"  data-parsley-range="[0.1,9.9]">折<i class="fa fa-2x fa-plus-circle text-success"></i><span>（如填写9则表示九折，9.9则表示九九折）</span></div></div>')
            }
            return false;
        })
        // 添加小规则
        $(document).on('click','#ruleEdit .fa-plus-circle',function(){
            var l=$(this).closest('.item').find('.row2').length;
            if(l<3){
                $(this).closest('.row').after('<div class="row row2"><input type="text" class="form-control form-j">件<input type="text" class="form-control form-z" maxlength="3" data-parsley-type="number"  data-parsley-range="[0.1,9.9]">折<i class="fa fa-2x fa-minus-circle text-danger"></i></div>');
            }
        })
        // 删除大规则
        $(document).on('click','#ruleEdit .row1 .fa-minus-circle',function(){
            $(this).closest('.item').addClass('active_del');
        })
        // 删除小规则
        $(document).on('click','#ruleEdit .row2 .fa-minus-circle',function(){
            $(this).closest('.row').remove();
        })
        // 是否删除弹窗取消按钮
        $('#btn-pop-del-cancel,#btn-pop-del-cancel2').on('click',function(){
            $('.item.active_del').removeClass('active_del')
        })
        // 是否删除弹窗确认按钮
        $('#btn-pop-del').on('click',function(){
            $('.item.active_del').remove()
        })
    })




</script>

<script type="text/javascript">
    var get_data=function(){
        data_l=[];
        $('#ruleEdit').find('.form-control').each(function(){
            if($(this).val()!==''){
                data_l.push($(this).val());
            }
        })
        if(data_l.length!=$('#ruleEdit').find('.form-control').length){
            layer.msg('请填写完整的折扣信息');
            isInitComm = 0;
            return ;
        }else{
            var data=[];
            $('#ruleEdit').find('.item').each(function(){
                var data0=[];
                var data1=[];
                data0.push($(this).find('.row1 .form-control').val());
                $(this).find('.row2').each(function(){
                    var data2=[];
                    data2[0]=$(this).find('.form-j').val();
                    data2[1]=$(this).find('.form-z').val();
                    data1.push(data2);
                })
                data0.push(data1);
                data.push(data0);
            })
            return data;
        }
    }


    $("#slt_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">选择</option>';
            $("#slt_three_comm_type_id").html(html);
            if(comm_parent_id == 0){
                $("#slt_sub_comm_type_id").html(html);return;
            }
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_sub_comm_type_id").html(html);
        },'json');

    }) ;

    $("#slt_sub_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        if(comm_parent_id == 0){
            var html='<option value="0">请选择</option>';
            $("#slt_three_comm_type_id").html(html);return;
        }
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">请选择</option>';
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_three_comm_type_id").html(html);
        },'json');
    }) ;

    $("body").on('click','#GWDLR',function(){
        if($("#GWDLR").is(':checked')){
            $('#dealer_select').removeClass('hidden');
        }else{
            $('#dealer_select').addClass('hidden');
        }
    });

    var dlr_data = <?= $dlr_data ?>;

    $("body").on("click",'#dlr_show',function () {
        var select_data = $("#dlr_hide").val().split(',');
        Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
            $("#dlr_show").val(dlr_name.join(','));
            $("#dlr_hide").val(dlr_code.join(','));
        });
    })

    //是否关闭活动
    var card_available = {$row['card_available']};



    var user_segment = {$row['user_segment']};
    if (user_segment === 1) {
        $("#rule_new").show()
        $("#rule_old").hide()
        console.log('user_segment:',user_segment)
        $("#user_level_box").show();
        $("#car_onwer_box").hide();
        $("#member_box").show();
    }
    if (user_segment === 2){
        $("#rule_new").show()
        $("#rule_old").hide()
        console.log('user_segment:',user_segment)

        $("#user_level_box").hide();
        $("#member_box").hide();
        $("#car_onwer_box").show();
    }

    $(".user_segment").click(function(){

        console.log('comm_id:',comm_id);
        if (comm_id.length > 0) {
            var index = layer.open({
                title: ['操作提醒'],
                btn: ['确认', '取消'],
                content:"<div style='font-size: 15px'>更改人群类型会清空下方添加商品列表</div>",
                yes: function (res) {
                    layer.close(index)
                    comm_id = [];
                    comm_set_id_arr = [];
                    $('#haved-commodtiy').text('');
                }
            })
        }
        // 获取会员级别
        var order_no = $("input[name='user_segment_options']:checked").attr('id');
        if (order_no != undefined) {
            refresh_user_level(order_no);
        } else {
            var html = '';
            $.each(user_level_list,function(i,val){
                html += '<div class="stylelt user_level county_name" data-title="' + val.value_code +'" id="' + val.order_no + '"  data-code="' + val.value_code +'">'+ val.county_name +'</div>'
            })

            $(".county_name").remove();
            $("#county_name").append(html);
            // 输入框
            var k_html = '';
            $.each(user_level_list, function (i, val) {
                k_html += '<div class="stylelt level_jian form-z value_code"><input size="4" class="v_dis_type" value_code="' +val.value_code+ '"  data-parsley-required="true" /><span class="c_dis_type">折</span></div>'
            })
            $(".value_code").remove();
            $("#value_code").append(k_html);
        }

        $("#rule_new").show()
        $("#rule_old").hide()
        if($(this).val() == 1){
            $("#user_level_box").show();
            $("#car_onwer_box").hide();
            $("#member_box").show();
            user_segment = 1;
        }else{
            $("#user_level_box").hide();
            $("#member_box").hide();
            $("#car_onwer_box").show();
            user_segment = 2
        }
        commTypeSearch2(0)
    })

    // 选择会员级别 规则跟着变动
    var new_user_level = {}
    $('.user_segment_options').click(function () {

        var _this = $(this);
        var order_no = _this.attr('id');
        refresh_user_level(order_no)
    })
    $('.del-commodity').on('click',function (){
        if($("table #haved-commodtiy tr:visible").length > 0){
            var index = layer.open({
                title: ['操作提醒'],
                btn: ['确认', '取消'],
                content:"<div style='font-size: 15px'>您选择对商品列表中全部商品进行移除操作吗?</div>",
                yes: function (res) {
                    $("#haved-commodtiy").empty();
                    $("#sku-confirm-all").data('is-sku-confirm-all',1)
                    initComm(ajaxCommUrl2);
                    commodity_select()
                    comm_id = [];
                    comm_set_id_arr = [];
                    skuConfirmAllCurrentPage = []
                    layer.close(index);
                }
            })
        }

    })
</script>
<script src="__STATIC__admin_v2/js/n_discount_live.js?rand=2.0.1"></script>
<script src="__STATIC__admin_v2/js/commodity_page.js?rand=*******"></script>
{include file="card:act_rule" /}
{include file="card:act_card_rule" /}
{/block}
