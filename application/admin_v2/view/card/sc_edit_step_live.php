{extend name="public:base_layout" /}

{block name="begin_css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap-wizard/css/bwizard.min.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_card_setp.css" rel="stylesheet"/>
<style>
    .bwizard-buttons{
        display: none;
    }
    #card-body td{
        text-align: center;
    }
</style>
{/block}

{block name="content"/}

<div class="panel-body">

    <form action="/" method="POST" class="form-horizontal" data-parsley-validate="true" name="form-wizard" 
          id="update_live">
        <input type="hidden" name="action" value="update">
        <input type="hidden" name="live_type" value="{$live_type}">
        <div id="wizard">
            <ol>
                <li>
                    优惠券基本信息 <small></small>
                </li>
                {if (!in_array($card_detail['card_type'], [6,7]))}
                <li>
                    使用设置 <small></small>
                </li>
                <li>
                    关联商品 <small></small>
                </li>
                <li>
                    商品排序 <small></small>
                </li>
                {/if}
                <li>
                    更新成功 <small></small>
                </li>
            </ol>
            <!-- begin wizard step-1 -->


            <div class="wizard-step-1">
                {include file="card:shop_edit_step_live_1" /}
            </div>

            <!-- end wizard step-1 -->
            <!-- begin wizard step-2 -->
            <div class="wizard-step-2">
                <fieldset>
                    <div class="row col-md-12">

                        <div class="table-content col-md-12">
                            <legend class="pull-left width-full">使用设置</legend>

                        </div>

                        <div class="form-group " id="up_down">
                            <label class="col-md-2 control-label">发放渠道<i class="text-danger">*</i>:</label>
                            <div class="col-md-8">
                                <div>
                                    <?php
                                    $row_code = explode(',', $dlr_code_str);
                                    foreach ($up_down_channel as $key => $val):
                                        if($live_type == 1): if (in_array($key,['PZ1AAPP','PZ1ASM'])):?>
                                            <label class="checkbox-inline">
                                                <input type="checkbox"
                                                       name="up_down_channel[]"
                                                    <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                                       value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                            </label>
                                        <?php endif; else: if (!in_array($key,['PZ1AAPP','PZ1ASM'])):?>
                                        <label class="checkbox-inline">
                                            <input type="checkbox"
                                                   name="up_down_channel[]"
                                                <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                                   value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                        </label>
                                    <?php endif;endif; endforeach; ?>
                                </div>

                                <div style="margin-top:10px;" class="<?=$dlr_hidden?>" id="dealer_select">
                                    <input id="dlr_show" type="text" class="form-control" placeholder="请点击选择经销商"
                                           data-parsley-required="true" value="<?=$dlr_str?>">
                                    <input id="dlr_hide" type="text" name="dlr_code" class="hidden" value="<?=$dlr_code_str?>">
                                </div>
                            </div>
                        </div>

                        <div class="form-group ">
                            <label class="col-md-2 control-label">是否可与其他券共用<i class="text-danger">*</i>:</label>
                            <div class="col-md-8">
                                <div>
                                    <label class="radio-inline">
                                        <input type="radio" name="can_with" value="1" id="radio-required"
                                            <?php if($card_detail['can_with'] == 1){echo "checked";}?> /> 是
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="can_with" id="radio-required2" value="0"
                                            <?php if($card_detail['can_with'] == 0){echo "checked";}?> /> 否
                                    </label>
                                    <dd>多选与单选券的区别</dd>
                                </div>

<!--                                <div style="margin-top:10px;" class="--><?php //if($card_detail['can_with'] == 0){echo "hidden";}?><!--" id="card_select">-->
<!--                                    <input id="card_show" type="text" class="form-control commodity_card "-->
<!--                                           onkeydown="onlyView();"-->
<!--                                           value="--><?//= $card_str ?><!--"-->
<!--                                           name="commodity_card_ids_name" placeholder="请点击选择优惠券">-->
<!--                                    <input id="card_hide" type="hidden" class="form-control hide_card_ids"-->
<!--                                           value="--><?//= $card_detail['can_with_ids'] ?><!--"-->
<!--                                           name="can_with_ids">-->
<!--                                </div>-->

                            </div>
                        </div>

                        <div class="form-group hidden">
                            <label class="col-md-2 control-label">是否可领取<i class="text-danger">*</i>:</label>
                            <div class="col-md-8">
                                <div>

                                    <label class="radio-inline">
                                        <input type="radio" checked name="can_get_in_detail" value="1"
                                               id="radio-can-required"
                                               data-parsley-required="true" data-parsley-multiple="is_shop"/> 是
                                    </label>

                                    <label class="radio-inline">
                                        <input type="radio" name="can_get_in_detail" id="radio-can-required2"
                                               value="0" data-parsley-required="true" data-parsley-multiple="is_shop"/> 否
                                    </label>
                                    <dd>可领取表示在商品详情中显示优惠券</dd>
                                </div>
                            </div>
                        </div>

                        <div class="form-group ">
                            <label class="col-md-2 control-label">状态<i class="text-danger">*</i>:</label>
                            <div class="col-md-8">
                                <div class="input-group col-md-9">
                                    <label>
                                        <input class="switch" id="is_enable" type="checkbox"
                                               data-input-name="is_enable" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭"
                                            <?php if($card_detail['is_enable']==1) echo 'checked';?>
                                        />
                                    </label>
                                </div>
                            </div>
                        </div>


                    </div>
                </fieldset>
            </div>
            <!-- end wizard step-2 -->

        </div>

        <ul class="pager bwizard-buttons" style="display: block">
            <li class="previous hidden" role="button" aria-disabled="false">
                <a href="#">← 上一步</a>
            </li>
            <li class="next" role="button" aria-disabled="false">
                <a href="#" id="my_next">下一步 →</a>
                <a href="#" class="redirect_back" style="margin-right: 10px;" id="my_next_back">提交并返回</a>
            </li>
        </ul>

    </form>

</div>


<!-- begin 选择优惠券 -->
<div class="modal fade" id="card-modal">
    <div class="modal-dialog">
        <div class="modal-content" style="width: 800px;">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title">选择优惠券</h4>
            </div>
            <div class="modal-body car-series ">
                <div class="alert alert-danger m-b-8" style="display: none;">
                    <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                    <p></p>
                </div>
                <form class="form search-form">
                    <label>优惠券名称：
                        <input type="text" id="card_name" class="form-control input-sm element width-200"
                               placeholder="请输入优惠券名称" aria-controls="data-table">
                    </label>
                    <button id="card-search" type="button" class=" btn btn-sm btn-success"><i
                                class="fa fa-search"></i>搜索
                    </button>
                </form>
                <div class="table-scrollable">
                    <table id="card_form" class="table table-bordered">
                        <thead>
                        <th class="text-center">ID</th>
                        <th class="text-center">归属</th>
                        <th class="text-center">优惠券类型</th>
                        <th class="text-center">优惠券ID</th>
                        <th class="text-center">优惠券名称</th>
                        <th class="text-center">操作</th>
                        </thead>
                        <tbody id="card-body">
                        </tbody>
                    </table>
                </div>
                <div>
                    <ul class="pagination"></ul>
                </div>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                <a href="javascript:;" class="btn btn-sm btn-primary" id="card_add" data-dismiss="modal">确定</a>
            </div>
        </div>
    </div>
</div>
<!-- end 选择优惠券-->


{/block}

{block name="css"/}
<style type="text/css">

</style>
{/block}

{block name="script"/}
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-wizard/js/bwizard.js"></script>
<script src="__STATIC__admin_v2/js/form-wizards-validation.demo.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js?v=111"></script>
<script src="__STATIC__admin_v2/js/card.js"></script>
<script src="__STATIC__admin_v2/js/fileinput.min.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js?v=25"></script>
<script>
    $(".timepicker3").datetimepicker({
        format:"HH:mm",
        locale: moment.locale('zh-cn'),
    }).on('dp.change', function(e){
        // var start_time = $("#start_time").val()
        // var end_time = $("#end_time").val()
        // if(start_time == ''){
        //     layer.msg('请选择开始时间段');
        //     return false;
        // }
        // if(end_time != ''){
        //     if(end_time <= start_time){
        //         layer.msg('结束时间不能早于开始时间，请重新选择');
        //         return false;
        //     }
        // }
    })
var ajaxGetCard_url  ="{:url('commodity/ajaxlivegetcard')}";
var card_type = "{$card_detail['card_type']}";;
    $(".default-select2").select2({'width':'23%'});
    $(".modal-select2").select2({'width':'23%'});

    var topbar_white="__STATIC__admin_v2/css/dndc_file/topbar_white.png";
    $(function(){

        $("body").on('click','#my_next,#my_next_back',function(){
	        var step_next = $(".bwizard-steps li").eq(1);

	        // dealer_select 有hidden, 就清空 dlr_hide 的value
            if($("#dealer_select").hasClass("hidden")){
                $("#dlr_hide").attr('value','');
            }else{
                var dlr_value = $("#dlr_hide").val();
                //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
                $(".checkbox-inline input:not(:checked)").each(function(i,v){
                    var reg = new RegExp(this.value,"g");//g,表示全部替换。
                    dlr_value = dlr_value.replace(reg,"");
                })
                $("#dlr_hide").attr('value',dlr_value);
            }
            if(false === $('form[name="form-wizard"]').parsley().validate("wizard-step-1")){
                return false;
            }
            var up_down_channel_name = getUpDownChannel();

            var  form=$(this).parents('form');
            var formData=new FormData(form[0]);
            formData.append('up_down_channel_name',up_down_channel_name);
	        if(step_next.hasClass('active') || card_type == 6 || card_type == 7){
	            //1.触发表达验证
	            //2.提交表单
                if(false === $('form[name="form-wizard"]').parsley().validate("wizard-step-2")){
                    return false;
                }
                var msg = '正在提交...';
                if($(this).hasClass('redirect_back')){
                    Custom.ajaxImpData("{:url('saveLive')}?is_redirect_back=1&is_change=1",formData,msg,"{:url('live')}?live_type={$live_type}");
                }else{
                    Custom.ajaxImpData("{:url('saveLive')}?is_redirect_back=1&is_change=1",formData,msg,"{:url('live')}",function(res){
                        if(res.error == 1){
                            layer.alert(res.msg);
                            return false;
                        }
                        if (card_type == 6 || card_type == 7){
                            Custom.ajaxPost("{:url('cardTmpUpdate')}",{id:res.data.id},'',"{:url('successStep4')}?live_type={$live_type}&card_type="+card_type);
                        }else {
                            window.location.href = "{:url('commodityCardLive')}" + '?card_id=' + res.data.id + '&live_type=' + "{$live_type}";
                        }
                    });
                }
	        }else{


	            if($(this).hasClass('redirect_back')){
                    Custom.ajaxImpData("{:url('saveLive')}?is_redirect_back=1&is_change=1",formData,msg,"{:url('live')}?live_type={$live_type}");
                }else{
                    // var limit_count = $("input[name='limit_count']").val();
                    // var available_count = $("input[name='available_count']").val();
                    // if(limit_count != ''){
                    //     if(parseInt(available_count) < parseInt(limit_count)){
                    //         layer.msg('卡劵库存不能小于每日领取库存')
                    //         return false;
                    //     }
                    // }
                    // var start_time = $("#start_time").val()
                    // var end_time = $("#end_time").val()
                    // if(end_time != ''){
                    //     if(end_time <= start_time){
                    //         layer.msg('结束时间不能早于开始时间，请重新选择');
                    //         return false;
                    //     }
                    // }
                    step_next.trigger('click');
                }
	        }
	    })

        //优惠券类型 变化表单
        $("input[name='card_type']").on('change',function(){
            var checked_val = $(this).val();
            _cardTypeUpdate(checked_val);
        });

        function _cardTypeUpdate(checked_val){		//优惠券类型设置表单
            if(checked_val==1){
                $("#t_title").html('立减券标题<i class="text-danger">*</i>:');								//标题
                $("input[name='card_name']").attr('placeholder','请输入代金券标题');
                $("input[name='card_name']").parent().find('dd').text('不能为空，最多9个汉字或18个字母。建议涵盖卡券服务及金额，如：保养100元代金券');
                $("#t_card_quota").html('减免金额<i class="text-danger">*</i>:');							//减免金额
                $("input[name='card_quota']").attr('placeholder','请输入优惠券减免金额');
                $("input[name='card_quota']").attr('data-parsley-min',0.1);
                $("input[name='card_quota']").attr('data-parsley-max',100000);
                $("input[name='card_quota']").parents(".form-group").find('.input-group-addon').text('元');
                $("input[name='card_quota']").parents(".form-group").find('dd').text('不能为空，减免金额数大于0.1 元且小于100000元，精确到角');
                var text_zdxf = $("#text-checkbox-zdxf").parents(".form-group");							//最低消费
                text_zdxf.find('.checkbox.zdxf').eq(0).show(); text_zdxf.find('dd').eq(0).show();
                var obj = $("#text-checkbox-zdxf").parent().parent();
                obj.find("select").remove();
                if($("#text-checkbox-zdxf").is(':checked')){
                    obj.find('label').html('<input type="checkbox" id="text-checkbox-zdxf" value="" checked>最低消费');
                }else{
                    obj.find('label').html('<input type="checkbox" id="text-checkbox-zdxf" value="">最低消费');
                }
                text_zdxf.find('dd').eq(0).text('用于设置满减的消费限额。最低消费金额必须大于减免金额。大于0且只能到百分位');
                $("#card-quota-group").show().find("input").attr("type","text");
            }else if(checked_val==2){
                $("#t_title").html('折扣券标题<i class="text-danger">*</i>:');
                $('#text-checkbox-zdxf-text').val('');
                $("input[name='card_name']").attr('placeholder','请输入折扣券标题');
                $("input[name='card_name']").parent().find('dd').text('不能为空，最多9个汉字或18个字母。建议填写折扣券“折扣额度”及自定义内容，如：保养8.8折扣券');
                $("#t_card_quota").html('折扣额度<i class="text-danger">*</i>:');
                $("input[name='card_quota']").attr('placeholder','请输入优惠券折扣额度');
                $("input[name='card_quota']").attr('data-parsley-min',0);
                $("input[name='card_quota']").attr('data-parsley-max',9.9);
                $("input[name='card_quota']").parents(".form-group").find('dd').text('不能为空，折扣额度只能请填写1-9.9之间的数字');
                $("input[name='card_quota']").parents(".form-group ").find('.input-group-addon').text('折');
                var text_zdxf = $("#text-checkbox-zdxf").parent().parent().parent();
                text_zdxf.find('div').eq(0).hide(); text_zdxf.find('dd').eq(0).hide();
                $("#text-checkbox-zdxf").parent().parent().find("select").remove();
                text_zdxf.find('dd').eq(0).text('用于设置满减的消费限额。最低消费金额必须大于减免金额。大于0且只能到百分位');
                $("#card-quota-group").show().find("input").attr("type","text");
            }else if(checked_val==3){
                //隐藏减免金额
                $("#card-quota-group").hide().find("input").attr("type","hidden");
                $("#t_title").html('兑换券标题<i class="text-danger">*</i>:');
                $("input[name='card_name']").attr('placeholder','请输入兑换券标题');
                $("input[name='card_name']").parent().find('dd').text('不能为空，最多9个汉字或18个字母。建议填写兑换券提供的服务或礼品名称，如：免费洗车服务');
                $("#t_card_quota").html('减免金额<i class="text-danger">*</i>:');
                $("input[name='card_quota']").attr('placeholder','请输入优惠券减免金额');
                /*	$("input[name='card_quota']").attr('data-parsley-min',0.1);
                 $("input[name='card_quota']").attr('data-parsley-max',100000);*/
                $("input[name='card_quota']").parents(".form-group").find('input-group-addon').text('元');
                $("input[name='card_quota']").parents(".form-group").find('dd').text('不能为空，减免金额数大于0.1 元且小于100000元，精确到角');
                var text_zdxf = $("#text-checkbox-zdxf").parent().parent().parent();
                text_zdxf.find('div').eq(0).show(); text_zdxf.find('dd').eq(0).show();
                var obj = $("#text-checkbox-zdxf").parent().parent();										//消费类型下拉框
                if($("#text-checkbox-zdxf").is(':checked')){
                    obj.find('label').html('<input type="checkbox" id="text-checkbox-zdxf" value="" checked>消费');
                }else{
                    obj.find('label').html('<input type="checkbox" id="text-checkbox-zdxf" value="">消费');
                }
                obj.find('label').after('<select class="form-control" id="least_type" name="least_type" disabled="disabled" style="width:90px;margin-left:15px;padding: 1px;display: inline;"> <option value="1">金额</option><option value="2">指定商品</option> </select>');
                obj.parent().find('dd').eq(0).text('最低消费金额大于0且只能到百分位');
            }else if(checked_val==5){
                $("#t_title").html('优惠券标题<i class="text-danger">*</i>:');								//标题
                $("input[name='card_name']").attr('placeholder','请输入优惠券标题');
                $("input[name='card_name']").parent().find('dd').text('不能为空，最多9个汉字或18个字母。建议涵盖卡券服务及金额，如：保养100元代金券');
                $("#t_card_quota").html('减免金额<i class="text-danger">*</i>:');							//减免金额
                $("input[name='card_quota']").attr('placeholder','请输入优惠券减免金额');
                $("input[name='card_quota']").attr('data-parsley-min',0.1);
                $("input[name='card_quota']").attr('data-parsley-max',100000);
                $("input[name='card_quota']").parents(".form-group").find('.input-group-addon').text('元');
                $("input[name='card_quota']").parents(".form-group").find('dd').text('不能为空，减免金额数大于0.1 元且小于100000元，精确到角');
                var text_zdxf = $("#text-checkbox-zdxf").parents(".form-group");							//最低消费
                text_zdxf.find('.checkbox.zdxf').eq(0).hide(); text_zdxf.find('dd').eq(0).hide();
                var obj = $("#text-checkbox-zdxf").parent().parent();
                obj.find("select").remove();
                if($("#text-checkbox-zdxf").is(':checked')){
                    obj.find('label').html('<input type="checkbox" id="text-checkbox-zdxf" value="" checked>最低消费');
                }else{
                    obj.find('label').html('<input type="checkbox" id="text-checkbox-zdxf" value="">最低消费');
                }
                text_zdxf.find('dd').eq(0).text('用于设置满减的消费限额。最低消费金额必须大于减免金额。大于0且只能到百分位');
                //优惠说明
                $("textarea[name='default_detail']").parent().parent().show();
                $("textarea[name='default_detail']").attr('required','required');
                $("#card-quota-group").hide();
                $("#card-quota-group").find("#card_quota").removeAttr('required');
                $(".apply-condition-group").hide();
            }else if(checked_val==6 || checked_val==7){
                $('.table-content-6').hide();
                $("#t_title").html('代金券标题<i class="text-danger">*</i>:');								//标题
                $("input[name='card_name']").attr('placeholder','请输入代金券标题');
                $("input[name='card_name']").parent().find('dd').text('不能为空，最多9个汉字或18个字母。建议涵盖卡券服务及金额，如：保养100元代金券');
                $("#t_card_quota").html('面值金额<i class="text-danger">*</i>:');							//减免金额
                $("input[name='card_quota']").attr('placeholder','请输入代金券面值金额');
                $("input[name='card_quota']").attr('data-parsley-min',0.1);
                $("input[name='card_quota']").attr('data-parsley-max',100000);
                $("input[name='card_quota']").parents(".form-group").find('.input-group-addon').text('元');
                $("input[name='card_quota']").parents(".form-group").find('dd').text('代金券面值金额');
                $("#card-quota-group").show().find("input").attr("type","text");
                $("textarea[name='default_detail']").parent().parent().hide();
                $("textarea[name='default_detail']").removeAttr('required');
                $("#card-quota-group").show();
                $("#card-quota-group").find("#card_quota").attr('required','required');

                $("input[name='least_cost']").val('');
                $("input[name='apply_des']").val('');
                $("input[name='not_apply_des']").val('');

                $(".apply-condition-group").show();
            }
            condition_ini();
        }
        //问号图标悬浮文案
        $("#what-text img").hover(function(){
            $("#what-text div").show()
        },function(){
            $("#what-text div").hide()
        });
        //可使用时间
        $("body").on('click','.applyBtn',function(){
            var validity_date = $("input[name='validity_date']").val();
            $("#js_use_time_preview").text(validity_date);
        });
        //使用条件
        $("body").on("click","#text-checkbox-zdxf",function(){
//            _parsley_zdxf();	//校验最低消费
        });

        //校验最低消费
        function _parsley_zdxf(){
            var zdxf = $("#text-checkbox-zdxf");
            var zdxf_text = $("#text-checkbox-zdxf-text");
            if(zdxf.attr("checked")=='checked'){
                zdxf_text.removeAttr("readonly");
                zdxf_text.attr("required","required");
                zdxf_text.attr("data-parsley-type","number");
                zdxf_text.attr("data-parsley-group","wizard-step-1");
            }else{
                zdxf_text.attr("readonly","readonly");
                zdxf_text.removeAttr("required");
                zdxf_text.removeAttr("data-parsley-type");
                zdxf_text.removeAttr("data-parsley-group");
            }
            condition_ini();
        }

        //消费类型
        $("body").on('change','#least_type',function(){
            if($(this).val()==1){
                $(this).parent().parent().find('dd').eq(0).text('最低消费金额大于0且只能到百分位');
                $("#text-checkbox-sp").html('满 <input type="text" id="text-checkbox-zdxf-text" class="form-control" name="least_cost" data-parsley-group="wizard-step-1" data-parsley-errors-container="#valid-zdxf" required="required" data-parsley-type="number" data-parsley-min="0"> 元可用');
            }else{
                $(this).parent().parent().find('dd').eq(0).text('不能为空且长度不能超过15个汉字或30个英文字母');
                $("#text-checkbox-sp").html('<input type="text" id="text-checkbox-zdxf-text" class="form-control" name="least_cost" data-parsley-group="wizard-step-1" data-parsley-maxlengthstr="15" data-parsley-errors-container="#valid-zdxf" required="required"> 可用');
            }
        });
        //最低消费要大于减免金额 提示
        $('body').on('change',"#card_quota,#text-checkbox-zdxf-text",function(){
            var ct_val = $("input[name='card_type']:checked").val();
            var this_zdxf_val = parseFloat($("#text-checkbox-zdxf-text").val());
            var card_quota_val = parseFloat($("#card_quota").val());
            if(ct_val==1){
                if(this_zdxf_val<card_quota_val || this_zdxf_val==card_quota_val){
                    $("#text-checkbox-zdxf-text").attr("data-parsley-min",""+(card_quota_val+0.1));
                    $("#text-checkbox-zdxf-text").attr("data-parsley-pattern-message","最低消费金额必须大于减免金额");
                }else{
                    $("#text-checkbox-zdxf-text").removeAttr('data-parsley-min');
                    $("#text-checkbox-zdxf-text").removeAttr('data-parsley-pattern-message');
                }
            }else if(ct_val==3){
                if($("#least_type").val()==1){
                    $("#text-checkbox-zdxf-text").attr("data-parsley-min","0.1");
                }
            }else{
                $("#text-checkbox-zdxf-text").removeAttr("data-parsley-min");
            }
        });
        $("#text-checkbox-syfw").on('click',function(){
            if($(this).attr("checked")=='checked'){
                $(".text-checkbox-syfw-text").removeAttr("readonly");
                $(".text-checkbox-syfw-text").attr("required","required");
            }else{
                $(".text-checkbox-syfw-text").attr("readonly","readonly");
                $(".text-checkbox-syfw-text").val('');
                $(".text-checkbox-syfw-text").removeAttr("required");
            }
        });

        //可使用范围
        $(".use_range").on('change',function(){
            _up_use_range();
        });
        //更新使用范围
        function _up_use_range(){
            var obj = $(".addCommodityModal");
            var this_val = $(".use_range:checked").val();
            if(this_val==1){
                obj.eq(0).show();
                obj.eq(1).hide();
            }else{
                obj.eq(1).show();
                obj.eq(0).hide();
            }
        }
        //领取范围
        $(".receive_range").on('change',function(){
            var this_val = $(this).val();
            _up_receive_range_stuta(this_val);
        });
        //更新领取范围状态
        function _up_receive_range_stuta(this_val){
            var obj = $(".receive_range").parent().parent();
            if(this_val==3 || this_val==4){
                obj.find('.add-upload-modal').eq(0).hide();
                obj.find('.add-upload-modal').eq(1).show();
            }else{
                obj.find('.add-upload-modal').eq(1).hide();
                obj.find('.add-upload-modal').eq(0).show();
            }
        }
        //封面图片
        $("input[name='abstract']").blur(function(){
            _up_abstract();
        });
        //更新封面图片
        function _up_abstract(){
            var this_val = $("input[name='abstract']").val();
            var html = this_val+'<span class="ic ic_go"></span>';
            $("#click-get-detail").html(html);
        }
        //上传文件
        $(".add-upload-modal.file").on('click',function(){
            $(this).parent().find('#input-file').click();
        });
        //显示文件名
        $("#input-file").on('change',function(){
            var file_name=$(this).val();
            $("#file-sp-des").text(file_name);
        });

        //保存优惠券
        $("#add-card-confirm").on('click',function(){
            //校验表单
            if(false === $('form[name="form-wizard"]').parsley().validate("wizard-step-3")){
                return false;
            }
            var  form=$(this).parents('form');
            var formData=new FormData(form[0]);
            var file_name=$("#input-file").val();
            var msg = '正在保存...';
            if(file_name!=''){
                if (file_name.substring(file_name.lastIndexOf('.') + 1)!='csv'){
                    var val = $(this).val('');
                    layer.alert('请将文件另存为.csv格式后再上传!',{icon: 5,scrollbar: false});
                    return false;
                }
                msg = '正在保存并上传...';
            }
            Custom.ajaxImpData("{:url('saveLive')}?is_change=1",formData,msg,"{:url('live')}");
        });

        _detail_init();	//初始化

        //卡券详情初始化
        function _detail_init(){
            //初始化卡券额度/折扣
            var card_type = "{$card_detail['card_type']}";
            if(card_type==1){
                $("#card_quota").val("{$card_detail['card_quota']}");
            }else if(card_type==2){
                $("#card_quota").val("{$card_detail['card_discount']}");
            }else if(card_type==7){
                $("#get_card_scene").val('上门取送车');
            }
            //优惠券标题
            var card_name = "{$card_detail['card_name']}";
            //有效期
            $("input[name='validity_date']").removeAttr('disabled');
            $(".btn.btn-default").removeAttr('disabled');
            $("select[name='fixed_begin_term']").attr('disabled','disabled');
            $("[name='fixed_term']").attr('disabled','disabled');
            $("[name='fixed_term']").removeAttr('required');
            //初始化使用条件
            var least_cost = "{$card_detail['least_cost']}";
            if(least_cost=='' || least_cost==0){
            }else{
                $("#text-checkbox-zdxf-text").val(least_cost);
                $("#text-checkbox-zdxf").attr("checked","checked");
//                _parsley_zdxf();	//校验最低消费
            }
            var text_checkbox_syfw = "{$card_detail['apply_des']}";
            if(text_checkbox_syfw=='') text_checkbox_syfw = "{$card_detail['not_apply_des']}";
            if(text_checkbox_syfw!='') {
                $("#text-checkbox-syfw").attr('checked','checked');
//                $("#apply_des").removeAttr("readonly");
//                $("#not_apply_des").removeAttr("readonly");
            }

            _cardTypeUpdate(card_type);	//初始化表单

            condition_ini();
            //更新表单隐藏状态
            //_up_c_t_hide_stute();

        }

    });

</script>
<script>
    //初始化控件
    $(document).ready(function() {
      //  App.init();
        FormWizardValidation.init();
    });
   /* //日期
    $(".default-daterange,#default-daterange").daterangepicker({
        //opens:"right",
        startDate:"{$card_detail['validity_date_start']}",
        endDate:"{$card_detail['validity_date_end']}",
        dateLimit: {
            days: 180
        },
        format:"YYYY-MM-DD",
        separator:" to ",
        locale:{
            applyLabel: '确认',
            cancelLabel: '取消',
            fromLabel: '从',
            toLabel: '到',
            weekLabel: 'W',
            daysOfWeek:["日","一","二","三","四","五","六"],
            monthNames: ["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],
        }
    });*/
    $("[data-toggle='tooltip']").tooltip({'placement':'bottom'});

    $("body").on('click','#GWDLR',function(){
        if($("#GWDLR").is(':checked')){
            $('#dealer_select').removeClass('hidden');
        }else{
            $('#dealer_select').addClass('hidden');
        }
    });

    $("body").on('click','#radio-required',function(){
        $('#card_select').removeClass('hidden');
    });

    $("body").on('click','#radio-required2',function(){
        $('#card_select').addClass('hidden');
    });

    var dlr_data = <?= $dlr_list ?>;
    $("body").on("click",'#dlr_show',function () {
        var select_data = $("#dlr_hide").val().split(',');
        Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
            $("#dlr_show").val(dlr_name.join(','));
            $("#dlr_hide").val(dlr_code.join(','));
        });
    })

    $(".multiple-select2").select2({
        placeholder: "请选择"
    })

    $('.can_user').on('change', function (){
        var val = $(this).val();
        if (val == 1){
            $('.can_user_group').removeAttr('disabled');
            $('.can_user_level').attr('disabled', 'disabled');
            $('.can_user_level').val('');
        }else if (val == 2){
            $('.can_user_group').attr('disabled', 'disabled');
            $('.can_user_level').removeAttr('disabled');
            $('.can_user_group').val('');
        }
        $(".multiple-select2").select2({
            placeholder: "请选择"
        })
    });

</script>
{include file="card:act_rule" /}
{/block}
