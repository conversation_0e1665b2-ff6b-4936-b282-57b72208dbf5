{extend name="public:base_layout" /}
{block name="css"/}
<style>

    .table-scrollable th {
        min-width: 100%;
    }
    td, th {
        padding: 0;
        white-space: nowrap;

    }
    .search-form .select2-container{
        width: 100%!important;
        margin-bottom: 0px!important;
    }

</style>
{/block}
{{block name="content"/}
<div class="panel-body" style="background: #FFFFFF">
    <div class="alert alert-info fade in m-b-15 ">
        <h4>操作提示</h4>
        <!--        <p>1、商品设置是对商品进行价格修改及上下架处理，上架后商品在会员商城中显示，下架为商品不在会员商城显示；</p>-->
        <!--        <p>2、操作顺序：先在搜索条件中搜索出合适的商品，再对搜索结果选择“批量修改价格”或 “批量上下架”操作；</p>-->
    </div>
    <form id="searchform" class="form search-form" action="{:url('index')}" method="get">
        <div class="dndc-form-mod1" id="dndc-form-mod1"  >
            <div class="form-main">
                <div class="form-item">
                    <em>所属库:</em>
                    <div class="form-wp">
                        <select name="service_type" class="form-control input-sm">
                            <option value="0" selected >全部</option>
                            <option value="备件" {if condition="$Think.get.service_type eq '备件'" }selected{/if} >备件</option>
                            <option value="保养套餐" {if condition="$Think.get.service_type eq '保养套餐'" }selected{/if} >保养套餐</option>
                            <option value="保养套餐产品" {if condition="$Think.get.service_type eq '保养套餐产品'" }selected{/if} >保养套餐产品</option>
                            <option value="备件车型车系" {if condition="$Think.get.service_type eq '备件车型车系'" }selected{/if} >备件车型车系</option>
                            <option value="车系18位码关系" {if condition="$Think.get.service_type eq '车系18位码关系'" }selected{/if} >车系18位码关系</option>
                            <option value="PZ1A保养套餐" {if condition="$Think.get.service_type eq 'PZ1A保养套餐'" }selected{/if} >PZ1A保养套餐</option>
                            <option value="KV养护品" {if condition="$Think.get.service_type eq 'KV养护品'" }selected{/if} >KV养护产品</option>
                        </select>
                    </div>
                </div>
                <div class="form-item">
                    <em>更新编码:</em>
                    <div class="form-wp">
                        <input type="text" name="update_no" class="form-control input-sm element" value="{$Think.get.update_no}" placeholder="请输入更新编码">
                    </div>
                </div>
                <div class="btn-more dndc_bcke" id="dndc-btn-more1">
<!--                    更多<i class="fa fa-plus-circle"></i><span class="arrow-spread"></span>-->
                </div>
                <div class="btn-wp">
                    <a href="javascript:;" id="btn-primary_sumb" class="btn btn-primary btn-sm m-r-5">搜索</a>
                </div>
                <div class="btn-wp">
                    <a href="javascript:;" id="reset" class="btn btn-primary btn-sm m-r-5">重置</a>
                </div>
            </div>
        </div>
    </form>

    <div style="overflow-x:scroll;">
        <table class="table table-hover table-bordered">
            <thead>
            <tr >
                <th>序号</th>
                <th>更新编码</th>
                <th>所属库</th>
                <th>状态</th>
                <th>更新结果</th>
                <th>关联商城商品/套餐</th>
                <th>更新时长</th>
                <th>更新时间</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            {volist name='list' id='vo' key='k'}
            <tr>
                <td>{:get_number($key)}</td>
                <td>{$vo.update_no}</td>
                <td>{$vo.service_type}</td>
                <td>
                    {$vo.update_type_msg}</td>
                <td>
                    {if condition="$vo.update_type == 1"}
                        {if condition="$vo.new_add != 0"}
                            <span>新增/修改：{$vo.new_add}</span><br />
                        {/if}
                        {if condition="$vo.new_update != 0"}
                        <span>修改：{$vo.new_update}</span><br />
                        {/if}
                        {if condition="$vo.new_del != 0"}
                            <span>删除：{$vo.new_add}</span><br />
                        {/if}
                    {else /}
                        -
                    {/if}
                </td>
                <td>
                    {if condition="$vo.is_goods == 0" }
                    否
                    {else /}
                    是
                    {/if}
                </td>
                <td>{$vo.update_duration}</td>
                <td>{$vo.created_date}</td>
                <td>
                    {if condition="$vo.update_type == 1"}
                        {if condition="$vo.service_type == '备件'"}
                        <a href="javascript:void(0);" data-update_no="{$vo.update_no}" class="e3s_spare_part">查看明细</a>
                        {elseif  condition="$vo.service_type == '备件车型车系'"/}
                        <a href="javascript:void(0);" data-update_no="{$vo.update_no}" class="e3s_part_car_series">查看明细</a>
                        {elseif  condition="$vo.service_type == '车系18位码关系'"/}
                        <a href="javascript:void(0);" data-update_no="{$vo.update_no}" class="e3s_car_series">查看明细</a>
                        {elseif  condition="$vo.service_type == '保养套餐'"/}
                        <a href="javascript:void(0);" data-update_no="{$vo.update_no}" class="e3s_maintenance_package">查看明细</a>
                        {elseif  condition="$vo.service_type == '保养套餐产品'"/}
                        <a href="javascript:void(0);" data-update_no="{$vo.update_no}" class="e3s_maintenance_product">查看明细</a>
                        {elseif  condition="$vo.service_type == 'PZ1A保养套餐'"/}
                        <a href="javascript:void(0);" data-update_no="{$vo.update_no}" class="e3s_pz1a_maintenance_package">查看明细</a>
                        {elseif  condition="$vo.service_type == 'KV养护品'"/}
                        <a href="javascript:void(0);" data-update_no="{$vo.update_no}" class="e3s_specific_relation">查看明细</a>
                        {else /}
                        -
                        {/if}
                    {else /}
                        -
                    {/if}
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>
    </div>
    <div class="row">
        <div class="col-sm-5">
            <div class="dataTables_info" id="data-table_info" role="status" aria-live="polite">
                共查询到 {$list->total()} 条数据
            </div>
        </div>
        <div class="col-sm-7">
            <div class="dataTables_paginate paging_simple_numbers" id="data-table_paginate">
                {$page}
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"/}
<script type="text/javascript">
    $("#reset").click(function(){
        location.href= "{:url('index')}"
    })

    $("#btn-primary_sumb").click(function(){
        $("#searchform").submit();
    })

    //查看备件明细，获得弹窗
    $("body").on('click',".e3s_spare_part",function(){
        var update_no = $(this).data('update_no');
        layer.open({
            type:2,
            title: ['查看备件 #更新编码'+update_no+'#更新明细'],
            area: ['90%','90%'],
            content: 'select_e3s_spare_part?update_no='+ $(this).data('update_no')
        })
    })

    //选择备件车型车系，获得弹窗
    $("body").on('click',".e3s_part_car_series",function(){
        var update_no = $(this).data('update_no');
        layer.open({
            type:2,
            title: ['查看备件车型车系 #更新编码'+update_no+'#更新明细'],
            area: ['70%','80%'],
            content: 'select_e3s_part_car_series?update_no='+ $(this).data('update_no')
        })
    })

    //选择车系18位码关系，获得弹窗
    $("body").on('click',".e3s_car_series",function(){
        var update_no = $(this).data('update_no');
        layer.open({
            type:2,
            title: ['查看车系-18位码-18位码中文关系接口 #更新编码'+update_no+'#更新明细'],
            area: ['70%','80%'],
            content: 'select_e3s_car_series?update_no='+ $(this).data('update_no')
        })
    })

    //选择保养套餐，获得弹窗
    $("body").on('click',".e3s_maintenance_package",function(){
        var update_no = $(this).data('update_no');
        layer.open({
            type:2,
            title: ['查看保养套餐 #更新编码'+update_no+'#更新明细'],
            area: ['90%','90%'],
            content: 'select_e3s_maintenance_package?update_no='+ $(this).data('update_no')
        })
    })

    //选择保养套餐，获得弹窗
    $("body").on('click',".e3s_maintenance_product",function(){
        var update_no = $(this).data('update_no');
        layer.open({
            type:2,
            title: ['查看保养套餐产品 #更新编码'+update_no+'#更新明细'],
            area: ['70%','80%'],
            content: 'select_e3s_maintenance_product?update_no='+ $(this).data('update_no')
        })
    })

    //查看pz1a保养套餐明细，获得弹窗
    $("body").on('click',".e3s_pz1a_maintenance_package",function(){
        var update_no = $(this).data('update_no');
        layer.open({
            type:2,
            title: ['查看pz1a保养套餐 #更新编码'+update_no+'#更新明细'],
            area: ['85%','85%'],
            content: 'select_e3s_pz1a_maintenance_package?update_no='+ $(this).data('update_no')
        })
    })

    //查看KV养护品套餐明细，获得弹窗
    $("body").on('click',".e3s_specific_relation",function(){
        var update_no = $(this).data('update_no');
        layer.open({
            type:2,
            title: ['查看KV养护品套餐 #更新编码'+update_no+'#更新明细'],
            area: ['85%','85%'],
            content: 'select_e3s_specific_relation?update_no='+ $(this).data('update_no')
        })
    })



</script>
{/block}
