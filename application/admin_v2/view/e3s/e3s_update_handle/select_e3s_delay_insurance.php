{include file="public:basic_css" /}
{block name="css"/}
<style>
    body{
        min-height: 100%;
    }
    .table-scrollable th {
        min-width: 100%;
    }
    td, th {
        padding: 0;
        white-space: nowrap;

    }
    .search-form .select2-container{
        width: 100%!important;
        margin-bottom: 0px!important;
    }

</style>
{/block}
{block name="content"/}
<div class="panel-body" style="background: #FFFFFF;width: 100%;">
    <div style="overflow-x:scroll;">
        <table class="table table-hover table-bordered">
            <thead>
            <tr >
                <th>序号</th>
                <th>所属库</th>
                <th>延保产品编码</th>
                <th>延保产品名称</th>
                <th>被替换编码</th>
                <th>类型</th>
                <th>变更明细</th>
                <th>关联套餐名称(点击名称可直接编辑)</th>
            </tr>
            </thead>
            <tbody>
            {volist name='list' id='vo' key='k'}
            <tr>
                <td>{:get_number($key)}</td>
                <td>延保产品</td>
                <td>{$vo.n_product_code}</td>
                <td>{$vo.n_product_name}</td>
                <td></td>
                <td>{$vo.type_name}</td>
                <td>{$vo.part_details}</td>
                <td>
                    {volist name='vo.goods_list' id='v' key='k2'}
                    <a onclick="on_click({$v['commodity_id']})" href="javascript:;" style="text-decoration: none;border-bottom: 1px solid #337ab7; ">{$v.commodity_name}</a>
                    {/volist}
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>
    </div>
    <div class="row">
        <div class="col-sm-5">
            <div class="dataTables_info" id="data-table_info" role="status" aria-live="polite">
                共查询到 {$list->total()} 条数据
            </div>
        </div>
        <div class="col-sm-7">
            <div class="dataTables_paginate paging_simple_numbers" id="data-table_paginate">
                {$page}
            </div>
        </div>
    </div>
</div>

{/block}
{include file="public:basic_js" /}
{block name="script"/}
<script type="text/javascript">
    function on_click(id){
        window.open("/admin_v2/commodity/addstep2?commodity_id="+id)
    }
</script>
{/block}