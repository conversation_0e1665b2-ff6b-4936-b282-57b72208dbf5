{extend name="public:base_layout" /}

{block name="content"/}

<div class="panel-body">

    <div class="alert alert-danger m-b-8" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">上架组合商品设置维护</legend>
        <form id="add_form" class="form-horizontal form-bordered" data-parsley-trigger="change">
            <input class="hidden" type="text" name="id" value="{$info.id}">
            <input class="hidden" type="text" name="commodity_id" value="{$info.commodity_id}">
            <div class="form-group">
                <label class="control-label col-md-2">商品名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    {$info.commodity_name}
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">优惠信息:</label>
                <div class="col-md-10">
                    <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">简称:</label>
                    <div class="col-md-3 m-l-0 p-l-0 ">
                        <input type="text" class="form-control " name="favourable_introduction" data-toggle="tooltip" title="字数不能超过10个。" maxlength="10" value="{$info['favourable_introduction']}">
                    </div>
                    <label class="control-label col-md-2 width-80 p-l-0 m-r-0 text-right">详情:</label>
                    <div class="col-md-3 m-l-0 p-l-0">
                        <input type="text" class="form-control " name="favourable_detail" value="{$info['favourable_detail']}">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">活动头图:</label>
                <div class="col-md-10  activity_image_group">
                    <div class="dndc-upload-pic goods_pic" sp-id="0" data-image-id="0" id="image-id-0">
                        <label class="sp-value m-t-10 m-l-10">默认</label>
                        <label>
                            <a href="javascript:;" class="btn-image btn btn-primary m-r-5">上传图片</a>
                        </label>
                        <p style="bottom:5px;">1张,200K,jpg,png</p><ul>
                            {notempty name="$info['activity_image']"}
                            <li class="move-item">
                                <img image-value="{$info['activity_image']}" src="{$info['activity_image']}">
                                <del></del><span>修改</span>
                            </li>
                            {/notempty}
                        </ul>
                    </div>
                    <input type="file" name="activity_image" class="hide" value="{$info['activity_image']}" id="activity-image-input">
                </div>
            </div>

            <div {empty name="$info['activity_image']"}style="display:none;"{/empty}  class="form-group activity_img_time" >
                <label class="control-label col-md-2"><span style="color: red">*</span>活动头图生效时间:</label>
                <div class="col-md-10  activity_image_group">
                    <div class="col-md-3 p-l-0" style="">
                        <input type="text" name="activity_start_time" value="{$info['activity_start_time']}" id="activity_start_time" placeholder="请输入开始时间" class="form-control datetimepicker4" data-parsley-required="true" >
                    </div>
                    <div class="col-md-3">
                        <input type="text" name="activity_end_time" value="{$info['activity_end_time']}" id="activity_end_time" placeholder="请输入结束时间" class="form-control datetimepicker4" data-parsley-required="true" >
                    </div>
                </div>
             </div>
            <?php if ($info['shelves_type'] == 6) : ?>
                <div class="form-group">
                    <label class="control-label col-md-2">服务说明:</label>
                    <div class="col-md-10">
                        <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">说明1:</label>
                        <div class="col-md-3 m-l-0 p-l-0 ">
                            <input type="text" class="form-control " name="directions1" value="{$info['directions1']}" data-toggle="tooltip" title="字数不能超过10个。" maxlength="10">
                        </div>
                        <label class="control-label col-md-2 width-80 p-l-0 m-r-0 text-right">说明2:</label>
                        <div class="col-md-3 m-l-0 p-l-0">
                            <input type="text" class="form-control " name="directions2" value="{$info['directions2']}" data-toggle="tooltip" title="字数不能超过10个。" maxlength="10">
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <div class="form-group">
                <label class="control-label col-md-2">已售数量:</label>
                <div class="col-md-10">
                    <input type="text" class="form-control " value="{$info['quantitys']}" name="quantitys" value="0">

                </div>
            </div>
            <?php if (!empty($card_info)) { ?>
                <div class="form-group">
                    <label class="control-label col-md-2">已关联券:</label>
                    <div class="col-md-10">
                        <?php foreach ($card_info as $key => $val) : ?>
                            <div class="coupon_info" style="margin-bottom: 5px;">
                                <span>优惠券名称: {$val['card_name']} </span> <span>优惠券有效时间: {$val['validity_date_start']} ~
                                    {$val['validity_date_end']}</span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php } ?>

            <div class="form-group hidden">
                <label class="control-label col-md-2">优惠券:</label>
                <div class="col-md-9">

                    <label class="vouher-margin">
                        是否可以使用多张：
                        <input type="radio" name="is_card_multic" value="1" class="vouher-margin" <?php if ($info['is_card_multic'] == 1) {
                                                                                                        echo 'checked';
                                                                                                    } ?>>是
                        <input type="radio" name="is_card_multic" value="0" class="vouher-margin" <?php if ($info['is_card_multic'] == 0) {
                                                                                                        echo 'checked';
                                                                                                    } ?>>否
                    </label>

                    <label class="vouher-margin">

                        <input id="card_hide" type="text" class="hidden" name="card_id">
                        <input id="has_use_card_mj" type="text" class="hidden" name="has_use_card_mj">
                    </label>

                    <ul class="nav nav-tabs" style="margin-top:10px;">
                        <li class="active"><a href="#default-tab-1" data-toggle="tab" aria-expanded="true" style="line-height: 12px;">微信公众号</a></li>
                        <li class=""><a href="#default-tab-2" data-toggle="tab" aria-expanded="false" style="line-height: 12px;">其他</a></li>

                    </ul>
                    <div class="tab-content p-t-5" style="background: #f2f6f9ab;">
                        <div class="tab-pane fade active in" id="default-tab-1">
                            <div class="col-md-12 p-l-0" style="height: 40px;">
                                <blockquote class="text-left col-md-3 p-t-5 p-b-5" style="width: 200px;">
                                    <p class="text-left" style="font-size: 12px;width: 200px;">用于微信公众号进去商城的优惠劵</p>

                                </blockquote>
                                <p class="col-md-4 m-b-0 ">
                                    <a href="javascript:;" class="btn btn-sm btn-primary add-card-wx">添加</a>
                                </p>
                            </div>
                            <div>
                                <table id="" class="table">
                                    <thead></thead>
                                    <tbody id="haved-card-1">
                                        <?php foreach ($card_list as $key => $val) : ?>
                                            <?php if ($val['card_type'] == 1) : ?>
                                                <tr class="info">
                                                    <td><a class="card-info" data-card-id="{$val['card_id']}" data-card-type="{$val['card_class']}" data-card-role="{$val['set_type']}">{$val['card_name']}</a></td>
                                                    <td class="text-right">
                                                    </td>
                                                    <td class="text-right">

                                                        <label class="checkbox-inline m-r-10 p-b-10">
                                                            <input type="checkbox" class="is-can-receive" value="1" <?php if ($val['is_can_receive'] == 1) {
                                                                                                                        echo 'checked';
                                                                                                                    } ?>>可领取
                                                        </label>
                                                        <button class="btn btn-danger btn-sm m-r-5 m-b-5 del-card">删除</button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>

                            </div>


                        </div>
                        <div class="tab-pane fade" id="default-tab-2">
                            <div class="col-md-12 p-l-0" style="height: 40px;">
                                <blockquote class="text-left col-md-2 p-t-5 p-b-5 ">
                                    <p class="text-left" style="font-size: 12px;width: 200px;">其他优惠劵</p>

                                </blockquote>
                                <p class="col-md-3 m-b-0 ">
                                    <a href="javascript:;" class="btn btn-sm btn-primary add-card-ot">添加</a>
                                </p>
                            </div>
                            <div>
                                <table id="" class="table">
                                    <thead></thead>
                                    <tbody id="haved-card-2">
                                        <?php foreach ($card_list as $key => $val) : ?>
                                            <?php if ($val['card_type'] == 2) : ?>
                                                <tr class="info">
                                                    <td><a class="card-info" data-card-id="{$val['card_id']}" data-card-type="{$val['card_class']}" data-card-role="{$val['set_type']}">{$val['card_name']}</a></td>
                                                    <td class="text-right">
                                                    </td>
                                                    <td class="text-right">
                                                        <label class="checkbox-inline m-r-10 p-b-10">
                                                            <input type="checkbox" class="is-can-receive" value="1" <?php if ($val['is_can_receive'] == 1) {
                                                                                                                        echo 'checked';
                                                                                                                    } ?>>可领取
                                                        </label>
                                                        <button class="btn btn-danger btn-sm m-r-5 m-b-5 del-card">删除</button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; ?>

                                    </tbody>
                                </table>


                            </div>
                        </div>

                    </div>

                </div>
            </div>

            <?php if ($info['shelves_type'] != 2) : ?>
                <div class="form-group">
                    <label class="control-label col-md-2">销售渠道<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-4">

                        <select class="multiple-select2 form-control" multiple="multiple" name="sales_channel[]">
                            <?php foreach ($sales_channel_list as $key => $val) : ?>
                                <option value="{$key}" <?php if (in_array($key, explode(',', $info['sales_channel']))) {
                                                            echo 'selected';
                                                        } ?>>{$val}</option>

                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($info['shelves_type'] != 4) : ?>
                <div class="form-group">
                    <label class="control-label col-md-2">商品类型<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-10">
                        <select id="comm_dlr_type" class="form-control width-300" name="commodity_dlr_type_id" data-parsley-required="true">
                            <option value="">请选择</option>
                            {volist name='comm_dlr_type_list' id='commDlrType'}
                            {if condition="$commDlrType.id==$info.commodity_dlr_type_id"}
                            <option value="{$commDlrType.id}" selected>{$commDlrType.inner_name}</option>
                            {else/}
                            <option value="{$commDlrType.id}">{$commDlrType.inner_name}</option>
                            {/if}
                            {/volist}
                        </select>
                    </div>
                </div>
            <?php endif; ?>
            {if condition="$role==1 and $info['shelves_type']!=5 and $info['shelves_type']!= 6 and $info['shelves_type']!=7"}
            <div class="form-group">
                <label class="control-label col-md-2">经销商<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <input id="dlr_show" type="text" class="form-control" value="{$info['dlr_name_str']}" placeholder="请点击选择经销商" data-parsley-required="true">
                    <input id="dlr_hide" type="text" name="dlr_code" class="hidden" value="{$info['dlr_code_str']}">
                </div>
            </div>
            {/if}
            <div class="form-group">
                <label class="control-label col-md-2">配送方式<i class="m-r-3 text-danger">*</i></label>
                <div class="col-md-10">
                    {if condition="in_array($commodity_class, [8,9])"} <!--充电桩-->
                    <input type="radio" {if condition="$info['mail_type']==2"/} checked {/if} name="mail_type" value="2"> <span style="color: black"> 快递</span>&nbsp;&nbsp;&nbsp;
                    {else/}
                    <input type="radio" {if condition="$info['mail_type']==2"/} checked {/if} name="mail_type" value="2"> <span style="color: black"> 快递</span>&nbsp;&nbsp;&nbsp;
                    <input type="radio" {if condition="$info['mail_type']==1"/} checked {/if}  name="mail_type" value="1"> <span style="color: black"> 自提</span>&nbsp;&nbsp;&nbsp;
                    <input type="radio" {if condition="$info['mail_type']==3"/} checked {/if}  name="mail_type" value="3"> <span style="color: black"> 可快递或自提</span>&nbsp;&nbsp;&nbsp;
                    <input type="radio" {if condition="$info['mail_type']==4"/} checked {/if}  name="mail_type" value="4"> <span style="color: black"> 仅展示</span>&nbsp;&nbsp;<input type="text" name="mail_show_word" id="" style="color: black" value="<?php if (empty($info['mail_show_word'])){ echo '商品购买详询专营店'; }else{echo $info['mail_show_word'];} ?>" maxlength="10">
                    <span ><br /><br />{$pay_text}</span>
                    {/if}
                </div>
            </div>
            <div class="form-group ks_md <?php if(in_array($info['mail_type'], [2,4])){ echo "hidden";} ?>">
                <label class="control-label col-md-2">可售门店:</label>
                <div class="col-md-10">
                    <div class="col-md-4  m-l-0 p-l-0">
                        <select id="dlr_groups" class="form-control width-130" name="dlr_groups" data-parsley-required="true">
                            <option value="0" id="default_group">请选择</option>
                            {volist name='$group_list' id='gitem'}
                            <option value="{$gitem.id}" {if condition="$info['dlr_groups']==$gitem['id']"/} selected {/if}>{$gitem.dlr_group_name}</option>
                            {/volist}
                        </select>
                    </div>

                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">运费模板:</label>
                <div class="col-md-10">
<!--                    <label class="checkbox-inline">-->
<!--                        <input type="checkbox" name="is_mail" onclick="express(this)" value="1" -->
<!--                            --><?php //if ($info['is_mail'] == 1) {
//                                    echo "checked";
//                                } ?>
<!--                        >支持快递-->
<!--                    </label>-->
<!--                    <label class="checkbox-inline">-->
<!--                        <input type="checkbox" id="id_is_store" name="is_store" value="1" -->
<!--                            --><?php //if ($info['is_store'] == 1) {
//                                echo "checked";
//                            } ?>
<!--                        >门店自提-->
<!--                    </label>-->
                    <div class="col-md-2 m-l-0 p-l-0 ">
                        <select id="template_guid" class="form-control width-130" name="template_guid" data-parsley-required="true" {if condition="in_array($info['mail_type'], [1, 4])"}disabled{/if}>
                        {if condition="in_array($info['mail_type'], [2, 3])"}
                        <option value="" id="default_name">请选择</option>
                        {else /}
                            <option value="0" id="default_name">请选择</option>
                            {/if}
                            {volist name='$templatelist' id='templatelistitem'}
                            <option {if condition="$info.template_guid eq $templatelistitem.guid" }selected="selected" {/if} value="{$templatelistitem.guid}">{$templatelistitem.template_name}</option>
                            {/volist}
                        </select>
                    </div>

                </div>
            </div>

            <?php if (in_array($info['shelves_type'], [5, 6, 7]) && ($info['is_store'] == 1)  && $info['dd_commodity_type'] != 8) { ?>
                <div class="form-group sbt_div ">
                    <label class="control-label col-md-2">预约类型<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-10">
                        <select id="template_guid" class="form-control width-300" name="subscribe_type" data-parsley-required="true">
                            <option value="">请选择</option>
                            <?php foreach ($subscribe_types as $k => $vo) : ?>
                                <option value="{$k}" <?php if ($info['subscribe_type'] == $k) {
                                                            echo "selected";
                                                        } ?>>
                                    {$vo}
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <span>购买安装类商品产生预约单时，根据此选择的类型提交预约单</span>
                    </div>
                </div>

            <?php } ?>

            <div class="form-group">
                <label class="control-label col-md-2">积分支付:</label>
                <div class="col-md-10">

                    <label class="checkbox-inline">
                        <input type="checkbox" name="factory_points" value="1" {if condition="$info['factory_points']==1" /} checked {/if}>厂家积分
                    </label>
                    <?php if (!in_array($info['shelves_type'], [5, 6, 7])) { ?>
                        <label class="checkbox-inline">
                            <input type="checkbox" name="dlr_points" value="1" {if condition="$info['dlr_points']==1" /} checked {/if}>专营店积分
                        </label>
                    <?php } ?>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">支付方式<i class="m-r-3 text-danger">*</i></label>
                <div class="col-md-10">

                    <input type="radio" {if condition="$info['pay_style']==1" /} checked {/if} name="pay_style" value="1"> <span style="color: black"> 现金+积分</span>
                    <input type="radio" {if condition="$info['pay_style']==2" /} checked {/if} name="pay_style" value="2"> <span style="color: black"> 现金</span>
                    <input type="radio" {if condition="$info['pay_style']==3" /} checked {/if} name="pay_style" value="3"> <span style="color: black"> 积分</span>
                    <span ><br /><br />{$pay_text}</span>
                </div>
            </div>
<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">特殊支付:</label>-->
<!--                <div class="col-md-10">-->
<!--                    <select class="form-control width-300" name="pay_dlr_code">-->
<!--                        <option value="">请选择</option>-->
                        <?php foreach ($pay_dlr_code_arr as $k => $vo) : ?>
                            <?php if ($k == $info['pay_dlr_code']) { ?>
<!--                                <option value="{$k}" selected>{$vo}</option>-->
                            <?php } else { ?>
<!--                                <option value="{$k}">{$vo}</option>-->
                            <?php } ?>
                        <?php endforeach; ?>
<!--                    </select>-->
<!--                    <span>当商品类型中无可选择的支付类型时，需指定特殊支付账号，请先前往基础数据中设置特殊支付的值类型后再选择。</span>-->
<!--                </div>-->
<!--            </div>-->

<!--            <div class="form-group">-->
<!--                <label class="control-label col-md-2">最多可用积分:</label>-->
<!--                <div class="col-md-10">-->
<!--                    <div class="col-md-2 m-l-0 p-l-0">-->
<!--                        <input type="text" class="form-control " name="max_point" placeholder="最多可用积分" value="{$info.max_point}">-->
<!--                        <span>(10积分=1元，0不限制)</span>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
            <div class="form-group">
                <label class="control-label col-md-2">商品属性:</label>
                <div class="col-md-10">
                    <select class="form-control width-300" name="commodity_attr" id="commodity_attr">
                        <option value="">请选择</option>
                        {volist name="comm_attr_list" id="vo" key="k"}
                        {if condition="$info['commodity_attr']==$k"/}
                        <option value="{$k}" selected>{$vo}</option>
                        {else/}
                        <option value="{$k}">{$vo}</option>
                        {/if}
                        {/volist}
                    </select>
                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">商品标签排序:</label>
                <div class="col-md-10">
                    <?php foreach ($tag_zdy as $key => $item):?>
                        <label class="checkbox-inline" id="act_type_{$item['act_type']}"
                            <?php if ($item['act_type'] == 22 && empty($info['commodity_attr'])) {?>
                                style="display: none"
                            <?php }?>
                        >
                            <input type="radio"  name="tag_zdy"   {if condition="$item['is_check'] == true"}checked{/if} value="{$item['act_type']}_{$item['act_id']}_{$item['act_name']}">  <span style="color: black"> {$item['act_name']} </span>
                        </label>
                    <?php endforeach;?>

                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2">商品标签:</label>
                <div class="col-md-10">
                    <input type="text" class="form-control width-300" name="commodity_label" data-toggle="tooltip" placeholder="填写后将在商品名称前显示" maxlength="5" value="{$info['commodity_label']}">
                    <span>非必填项，最多5个字。填写后显示在首页、专题页、商品列表页、商品详情页的商品标题前。</span>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品服务标签:</label>
                <div class="col-md-4" id="valid-service-channel">

                    <select class="multiple-select2 form-control" multiple="multiple" name="service_channel[]"  data-parsley-errors-container="#valid-service-channel">
                        <?php foreach ($service_channel_list as $key => $val):?>
                            <option value="{$key}" <?php if (in_array($key, explode(',', $info['service_channel']))) {
                                echo 'selected';
                            }?>>{$val}</option>

                        <?php endforeach;?>
                    </select>
                </div>
            </div>

            {in name="$info['shelves_type']" value="5,6,7"}
            <div class="form-group">
                <label class="control-label col-md-2">映射前台分类:</label>
                <div class="col-md-4" id="home_type_class"></div>
            </div>
            {/in}

            <?php if ($info['shelves_type'] == 1) : ?>
                <div class="form-group">
                    <label class="control-label col-md-2">赠送积分:</label>
                    <div class="col-md-2">
                        <input type="text" name="integral_per" value="{$info['integral_per']}" class="form-control col-md-4" data-parsley-min="0.00" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                    </div>
                </div>

            <?php elseif ($info['shelves_type'] == 2) : ?>
                <div class="form-group">
                    <label class="control-label col-md-2">是否新品:</label>
                    <div class="col-md-10">
                        <div class="col-md-1 p-l-0 width-50">
                            <label class="checkbox-inline">
                                <input type="checkbox" name="is_new_products" value="1" <?php if ($info['is_new_products'] == 1) {
                                                                                            echo 'checked';
                                                                                        } ?>>是
                            </label>
                        </div>
                        <div class="input-group col-md-2">
                            <input type="text" name="new_products_sort" value="{$info['new_products_sort']}" class="form-control col-md-4" data-parsley-range="[0,10000]"">
                            <span class=" input-group-addon ">排名</span>
                        </div>
                        <p class=" m-t-5">新品排行按照排名从小到大的顺序排序</p>
                        </div>
                    </div>

                <?php endif; ?>

                <?php if (in_array($info['shelves_type'], [5, 6, 7])) { ?>
                    <legend class="pull-left width-full m-t-15">商品上下架</legend>
                    <div class="form-group" id="up_down">
                        <label class="control-label col-md-2">上下架渠道:</label>
                        <div class="col-md-10">
                            <div>
                                <?php $row_code = explode(',', $info['up_down_channel_dlr']);
                                foreach ($up_down_channel as $key => $val) :
                                    if ($info['shelves_type'] == 6) : if (in_array($key, ['PZ1AAPP', 'PZ1ASM'])) : ?>
                                            <label class="checkbox-inline">
                                                <input type="checkbox" name="up_down_channel[]" <?php if (in_array($key, $row_code)) echo 'checked'; ?> value="<?= $key ?>" id="<?= $key ?>" data-name="<?= $val ?>"><?= $val ?>
                                            </label>
                                        <?php endif;
                                    else : if (!in_array($key, ['PZ1AAPP', 'PZ1ASM', 'TOBPC'])) : ?>
                                            <label class="checkbox-inline">
                                                <input type="checkbox" name="up_down_channel[]" <?php if (in_array($key, $row_code)) echo 'checked'; ?> value="<?= $key ?>" id="<?= $key ?>" data-name="<?= $val ?>"><?= $val ?>
                                            </label>
                                <?php endif;
                                    endif;
                                endforeach; ?>
                            </div>

                            <div style="margin-top:20px;" class="<?= $dlr_hidden ?>" id="dealer_select">
                                <input id="dlr_show" type="text" class="form-control width-300" placeholder="请点击选择经销商" data-parsley-required="true" value="<?= $dlr_str ?>">
                                <input id="dlr_hide" type="text" name="dlr_code" class="hidden" value="<?= $dlr_code_select_str ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-2">上下架时间:</label>
                        <div class="col-md-10  activity_image_group">
                            <div class="col-md-3 p-l-0" style="">
                                <input type="text" name="latest_listing_time" value="{$info['latest_listing_time']}" id="latest_listing_time" placeholder="请输入开始时间" class="form-control datetimepicker4" >
                            </div>
                            <div class="col-md-3">
                                <input type="text" name="latest_end_time" value="{$info['latest_end_time']}" id="latest_end_time" placeholder="请输入结束时间" class="form-control datetimepicker4">
                            </div>
                            <div class="col-md-9">
                                <span>*可不设置结束时间，若不设置，商品有效期为长期</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-2">子商品<i class="m-r-3 text-danger">*</i>:</label>
                        <div class="col-md-10">
                            <div class="col-md-10 m-b-10" style="width: 100%;padding-left:0px;">
                                <div style="float:left">
                                    <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品分类:</label>
                                    <select class="form-control input-sm element default-select2 col-md-2" name="comm_parent_id" id="slt_comm_type_id">
                                        <option value="0">
                                            请选择
                                        </option>
                                        <?php foreach ($comm_parent_list as $key => $val) : ?>
                                            <option value="{$val['id']}">{$val['comm_type_name']}</option>
                                        <?php endforeach; ?>
                                    </select>
                                    <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id">
                                        <option value="0">
                                            请选择
                                        </option>
                                    </select>
                                    <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id">
                                        <option value="0">
                                            请选择
                                        </option>
                                    </select>
                                </div>

                            </div>
                            <div class="col-md-10 m-b-10" style="width: 100%;padding-left:0px;">
                                <div style="float:left">
                                    <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品种类:</label>
                                    <select class="form-control input-sm element default-select2 col-md-2" name="commodity_class" id="slt_comm_type_id">
                                        <option value="0">
                                            请选择
                                        </option>
                                        <?php foreach ($commodity_class_list as $key => $val) : ?>
                                            <option value="{$key}">{$val}</option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div style="float:right;">
                                    <label class="control-label col-md-2" style="padding:  5px 5px;  width: 90px;">商品名称:</label>
                                    <input type="text" class="form-control input-sm element col-md-3 m-r-2 width-200" name="commodity_name" placeholder="请输入商品名称">
                                    <button id="comm-type-search" type="button" class="btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                                </div>

                            </div>
                            <div>
                                <div class="table-scrollable">
                                    <table id="" class="table table-hover">
                                        <thead>
                                            <th class="text-center">商品规格</th>
                                            <th class="text-center">上架渠道</th>
                                            <th class="text-center">原价</th>
                                            <th class="text-center">库存</th>
                                            <th class="text-center">操作</th>
                                        </thead>
                                        <tbody id="add-comm-tbody">

                                        </tbody>
                                    </table>
                                </div>
                                <div>
                                    <ul class="pagination" id="comm-pagination"></ul>
                                </div>
                            </div>

                            <legend class="pull-left width-full" style="font-size: 18px;margin-top: 20px;">已选子商品</legend>
                            <p style="color: red">如果您选择了4L机油，请再额外选择1L机油的商品</p>

                            <div>
                                <div class="table">
                                    <table id="" class="table">
                                        <thead></thead>

                                        <tbody id="haved-commodtiy">
                                            {volist name='group_commodity_ids_info' id ='vo' }
                                            <tr id="{$vo['commodity_id']}" set_id="" class="info haved-add">
                                                <td style="width: 250px;">
                                                    <input type="hidden" class="com_type_id" value="" />
                                                    <input type="hidden" name="home" class="home" value="" />
                                                    <input type="hidden" name="commodity_class" class="commodity_class" value="commodity_class" />
                                                    <input type='hidden' class="json_st" value='{$vo.json_st}' />
                                                    <img class="cover-image" src="{$vo.cover_image}">{$vo['commodity_name']}
                                                </td>
                                                <td class="text-right">
                                                    <div style="clear: both;float: left;" class="child_commodity">
                                                        <label class="mgr">初始数量:<input class="initial_num" name="initial_num" type="text" value="{$vo.initial_num}" size="5"></label>
                                                        <label class="mgr"><input type="checkbox" class="user_can_des" name="user_can_des" value="{$vo['user_can_des']}" {if ($vo['user_can_des']==1)}checked{/if}>用户可增减数量</label>
                                                        <label class="mgr"><input type="checkbox" class="can_select" name="can_select" value="{$vo['can_select']}" {if ($vo['can_select']==1)}checked{/if}>可选</label>
                                                        {if $vo['machine_oil_type'] > 0}
                                                        <label class="mgr"><input type="radio" class="{$vo['machine_oil']}" name="{$vo['machine_oil']}" checked value="{$vo['machine_oil_type']}">{$vo['machine_oil_type']}L机油</label>
                                                        {/if}
                                                    </div>
                                                    <button data-sku-list='{$vo.json_st}' class="btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>
                                                    <button class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button>
                                                </td>
                                            </tr>
                                            {/volist}
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-md-2">规格联动设置:</label>
                        <div class="col-md-10">
                            <div class="row">
                                <div class="col-md-10 text-danger">注意：若上方商品有变动，联动会全部清空，重新设置。同个商品只能有一个联动设置。暂只支持单个规格商品设置。</div>
                                <div class="col-md-2">
                                    <a href="javascript:;" class="btn btn-sm btn-primary add-union-spec">新增</a>
                                </div>
                            </div>
                            <div class="union-content union-for-del m-t-50">
                                <table class="table union-for-del spec-relation m-t-10">
                                    {volist name='unions' id ='vo' }
                                    <tr data-relation="{$vo['union_code']}">
                                        <td>{$vo['union_spec']}</td>
                                        <td><a href="javascript:;" class="union-del" style="width:20px">删除</a><input type=hidden name="spec_union[]" value={$vo['goods']}></td>
                                    </tr>
                                    {/volist}
                                </table>
                            </div>

                        </div>
                    </div>

                    <legend class="pull-left width-full m-t-15">商品分账信息</legend>
                    <div class="form-group" id="spr_id">
                        <label class="control-label col-md-2">分账规则<i class="m-r-3 text-danger">*</i>:</label>
                        <div class="col-md-10">
                            <select class="form-control width-400" name="spr_id" data-parsley-required="true">
                                <option value="">请选择</option>
                                <?php foreach ($spr_rules as $key => $val) : ?>
                                    <?php $rule_info = json_decode($val['rule_info'], true); ?>
                                    <option value="{$val['id']}" <?php if ($info['spr_id'] == $val['id']) {
                                                                        echo "selected";
                                                                    } ?>>
                                        {$val['rule_name']} / 平台:{$rule_info['platform']} , 专营店:{$rule_info['dlr']}
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" id="spr_id">
                        <label class="control-label col-md-2">对账商务项目<i class="m-r-3 text-danger">*</i>:</label>
                        <div class="col-md-10">
                            <input type="text" class="form-control width-300" name="business_project" data-toggle="tooltip" value="{$info.business_project}" placeholder="默认值为商品一级分类名称" maxlength="20" data-parsley-required="true">
                            <span>默认值为商品一级分类名称,可修改</span>
                        </div>
                    </div>

                <?php } ?>

                <div class="text-center" style="padding-top: 10px">
                    <input type="hidden" name="shelves_type" value="{$info['shelves_type']}">
                    <input type="hidden" name="set_sku_list" value="">
                    <a type="button" id="bt-submit" class="btn btn-primary btn-sm m-l-200 submit">提交</a>
                </div>
        </form>
    </div>

    <!-- begin 选择优惠券 -->
    <div class="modal fade" id="card-modal">
        <div class="modal-dialog">
            <div class="modal-content" style="width: 800px;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择优惠券</h4>
                </div>
                <div class="modal-body car-series ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <form class="form search-form">
                        <label>优惠券名称：
                            <input type="text" id="card_name" class="form-control input-sm element width-200" placeholder="请输入优惠券名称" aria-controls="data-table">
                        </label>
                        <button id="card-search" type="button" class=" btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                    </form>
                    <div class="table-scrollable">
                        <table id="card_form" class="table table-bordered">
                            <thead>
                                <th class="text-center">应用场景</th>
                                <th class="text-center">归属</th>
                                <th class="text-center">优惠券类型</th>
                                <th class="text-center">优惠券id</th>
                                <th class="text-center">优惠券名称</th>
                                <th class="text-center">操作</th>
                            </thead>
                            <tbody id="card-body">
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <ul class="pagination"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary" id="card_add" data-dismiss="modal">确定</a>
                </div>
            </div>
        </div>
    </div>
    <!-- end 选择优惠券-->

    <!-- begin 选择经销商 -->

    <!-- end 选择经销商-->


    <!----begin 查看规格---->
    <div class="modal fade" id="sku-modal" data-comm-id="" data-comm-set-id="" data-type="">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <input type="hidden" id="comm-id" />
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择商品: <span id="comm-name-header"></span></h4>
                </div>
                <input id="modal_com_type_id" type="hidden" value="" />
                <input id="modal_home" type="hidden" value="" />
                <input id="modal_commodity_class" type="hidden" value="" />
                <div class="modal-body" style="">

                    <div class="hide">
                        <div class="sku-image" style="display: inline-block;">
                            <img class=" cover-image" src="">
                        </div>
                        <div style="display: inline-block;">
                            <div class="sku-comm"></div>
                        </div>
                    </div>

                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th class="col-md-6">商品规格</th>

                                <th>原价</th>
                                <th>库存</th>
                            </tr>
                        </thead>
                        <tbody class="sku-tb">

                        </tbody>
                    </table>

                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">关闭</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary" id="sku-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 新增联动---->
    <div class="modal fade" id="union-spu-modal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">规格联动</h4>
                </div>
                <div class="modal-body form-horizontal">
                    <div class="form-group">
                        <label class="control-label col-md-2">选择A商品和规格：</label>
                        <select class="form-control col-md-3 default-select2 union-goods union-1">
                            <option value="">请选择</option>
                        </select>

                        <select class="form-control col-md-3 default-select2 union-spec spec-1">
                        </select>

                        <select class="form-control col-md-3 default-select2 union-spec-val spec-val-1">
                        </select>

                        <button class="btn btn-primary add-union-btn">新增关联关系</button>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-2">选择B商品和规格：</label>
                        <select class="form-control col-md-3 default-select2 union-goods union-2">
                            <option value="">请选择</option>
                        </select>

                        <select class="form-control col-md-3 default-select2 union-spec spec-2">

                        </select>
                        <select class="form-control col-md-3 default-select2 union-spec-val spec-val-2">

                        </select>
                    </div>
                    <h5 class="modal-title">规格联动关系</h5>
                    <table class="table union-for-del spec-relation m-t-10">
                        {volist name='unions' id ='vo' }
                        <tr data-relation="{$vo['union_code']}" data-goods-spec="{$vo['goods_spec']}">
                            <td>{$vo['union_spec']}</td>
                            <td><a href="javascript:;" class="union-del" style="width:20px">删除</a><input type=hidden name="spec_union[]" value={$vo['goods']}></td>
                        </tr>
                        {/volist}
                    </table>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary" id="union-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>确定删除该商品?</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary" id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>


</div>
{/block}
{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/jBootsrapPage.css" rel="stylesheet" />
<style>
    .nav-tabs>li.active>a,
    .nav-tabs>li.active>a:focus,
    .nav-tabs>li.active>a:hover {
        background: #f7fafdf2 !important;
    }

    .info td {
        padding: 3px 12px !important;
    }

    .modal-body.car-series {
        height: 400px;
        overflow: auto;
    }

    .sku legend {
        margin-bottom: 10px;

    }

    .editableform .form-group>div {
        padding: 0px !important;
        border: 0px !important;
    }

    .editable-click,
    a.editable-click,
    a.editable-click:hover {
        border-bottom: none !important;
    }

    .sku .sp_vlaue {
        width: 13%;
    }

    ul,
    ol {
        list-style: none outside none;
    }

    .sp_name {
        float: left;
    }

    .sku li div {
        margin-left: 50px;
    }

    .form-control.card,
    .form-control.date-picker {
        width: 300px;
    }

    .form-control.price {
        width: 100px;
        display: inherit;
    }

    .vouher-margin {
        margin-right: 10px;
    }

    .price-margin {
        margin-right: 20px;
    }

    label {
        font-weight: 400;
    }

    .submit {
        width: 100px;
    }

    .commodity-th,
    .commodity-td {
        text-align: -webkit-center;
    }

    #card-modal {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: -200px;
        z-index: 1050;
        display: none;
        overflow: hidden;
        -webkit-overflow-scrolling: touch;
        outline: 0;
    }

    .logistics {
        float: left;
        margin-right: 20px;
    }

    .width-sku {
        width: 140px !important;
    }

    .coupon_info span {
        display: block;
        margin-right: 15px;
    }

    .form-group dd {
        margin-top: 5px;
    }

    .cover-image {
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-left: 20px;
        display: block;
    }

    .mgr {
        margin-right: 10px;
    }
</style>
{/block}
{block name="script"/}
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js?v=25"></script>
<script src="__STATIC__admin_v2/js/xm-select.js"></script>

<script>
    var ajaxCommUrl = "{:url('ajaxGetLiveCommodityList')}?wxtag=1&pagesize=5&dlrs=GWSC&shelves_type={$info['shelves_type']}&dd_commodity_type={$info.dd_commodity_type}"; //商品
    var getSkuUrl = "{:url('getSkuLive')}";

    $(function() {


        $('#commodity_attr').on('change',function () {
            var commodity_attr = $(this).val();
            console.log('commodity_attr:',commodity_attr)
            if (commodity_attr != 0) {
                $('#act_type_22').show();
            } else {
                $('#act_type_22').hide();
            }
        });



        var a = [3];
        var b = a;

        var card_checked_id = [];
        var card_checked_name = [];
        /**优惠券使用规则：
         *  1.满减券不可与其他券同时使用
         *  2.平台只能使用平台发布的优惠券
         *  3.专营店端只能使用平台或者自营店的其中一种优惠券
         * @type {boolean}
         */
        var manjian_exist = false;
        var card_selected_type = 0; //0为初始值,1代表平台端,2代表专营店端,3为集团。
        var role = "{$role}";
        var checked_card_info = []; //弹窗已x经选中的卡券;
        var select_card_type = '';
        $("#card_show").on("keyup", function() {
            console.log($(this).val())
            if ($(this).val().length == 0) {
                card_checked_id.length = 0;
                card_checked_name.length = 0;
                manjian_exist = false;
                card_selected_type = 0;
            }
            console.log("card_selected_type = " + card_selected_type);
            console.log("card_checked_id = " + card_checked_id);

        })


        $(".multiple-select2").select2({
            placeholder: "请选择"
        })

        function count_contracts_ajax(urlparam) {
            $.getJSON(urlparam, null, function(resData) {
                createPage(5, 10, resData.data.total, urlparam); //创建翻页功能按钮，翻
                $("tbody#card-body").empty();
                if (resData.data.total > 0) { //页向后台请求连接
                    list_contracts_ajax(urlparam);
                } else {
                    // layer.msg("没有更多数据");
                    $("#card-modal").modal("show");
                }
            })
        }

        function createPage(pageSize, buttons, total, contracts_url) { //contracts_url为点击
            $(".pagination").jBootstrapPage({ //功能按钮后需要访问action路径
                pageSize: pageSize,
                total: total,
                maxPageButton: buttons,
                onPageClicked: function(obj, page) {
                    $("tbody#card-body").empty();
                    list_contracts_ajax(contracts_url + "&page=" + (page + 1));
                }
            });
        }

        function list_contracts_ajax(urlparam) {
            var card_info = getCardInfo(select_card_type);
            var selected_card_id = [];
            var checked_card_id = []
            for (var i = 0; i < card_info.length; i++) {
                selected_card_id.push(card_info[i].card_id);
            }
            for (var i = 0; i < checked_card_info.length; i++) {
                checked_card_id.push(checked_card_info[i].card_id);
            }
            $.getJSON(urlparam, null, function(result) {
                for (var i = 0; i < result.data.data.length; i++) { //json格式，多行数据的数组
                    var tr = $("<tr></tr>"); //一行记录
                    tr.appendTo($("tbody#card-body"));
                    var td = $("<td>" + (result.data.data[i].type_name ? result.data.data[i].type_name : '') + "</td>"); //应用场景
                    td.appendTo(tr);
                    var td = $("<td>" + (result.data.data[i].belong_to ? result.data.data[i].belong_to : '') + "</td>"); //归属
                    td.appendTo(tr);
                    var td = $("<td>" + (result.data.data[i].card_type_name ? result.data.data[i].card_type_name : '') + "</td>"); //类型
                    td.appendTo(tr);
                    var td = $("<td>" + (result.data.data[i].id ? result.data.data[i].card_id : "") + "</td>"); //优惠券id
                    td.appendTo(tr);
                    var td = $("<td>" + (result.data.data[i].card_name ? result.data.data[i].card_name : '') + "</td>"); //优惠券名称
                    td.appendTo(tr);
                    td = $("<td></td>"); //操作
                    console.log(result.data.data[i].id);
                    if (selected_card_id.indexOf(result.data.data[i].id) != -1) {
                        var option = $("<label>已选择</label>");
                    } else if (checked_card_id.indexOf(result.data.data[i].id) != -1) {
                        var option = $("<label>选择<input class='card_checkbox' type='checkbox' data-id='" + result.data.data[i].id + "' data-type='" + result.data.data[i].card_type + "' data-role='" + result.data.data[i].create_by_role + "' data-title='" + result.data.data[i].card_name + "' checked></label>");

                    } else {
                        var option = $("<label>选择<input class='card_checkbox' type='checkbox' data-id='" + result.data.data[i].id + "' data-type='" + result.data.data[i].card_type + "' data-role='" + result.data.data[i].create_by_role + "' data-title='" + result.data.data[i].card_name + "'></label>");
                    }
                    option.appendTo(td);
                    td.appendTo(tr);
                }
                $("#card-modal").modal("show");
            })
        }


        $("#card_show").on("click", function() {
            count_contracts_ajax("{:url('ajaxGetCard')}?pagesize=5");
        })

        $(".add-card-wx,.add-card-ot").on('click', function(e) {
            checked_card_info = []; //初始化点击选中优惠券
            if ($(this).hasClass("add-card-wx")) {
                select_card_type = 1;
            } else {
                select_card_type = 2;
            }
            count_contracts_ajax("{:url('ajaxGetCard')}?pagesize=5&select_card_type=" + select_card_type);

        })
        $("#card-search").on("click", function() {
            var card_name_like = $("#card_name").val();
            var urlParams = "{:url('ajaxGetCard')}?pagesize=5&card_name=" + card_name_like + "&select_card_type=" + select_card_type;
            count_contracts_ajax(urlParams);
        })

        //获取已选择的卡券信息
        function getCardInfo(tag) {
            var card_info = [];
            var is_can_receive = 0;
            $("#haved-card-" + tag).find(".card-info").each(function() {
                if ($(this).parents('tr').find(".is-can-receive").attr("checked")) {
                    is_can_receive = 1;
                } else {
                    is_can_receive = 0;
                }
                card_info.push({
                    card_id: $(this).data("card-id"),
                    card_type: $(this).data("card-type"),
                    card_role: $(this).data("card-role"),
                    is_can_receive: is_can_receive,
                    card_tag: tag
                });
            })
            // console.log(card_info);
            return card_info;

        }

        $(".card_checkbox").live("click", function() {
            var selected_card_info = getCardInfo(select_card_type);
            //合并下面表格和弹窗选中的优惠券
            for (var i = 0; i < checked_card_info.length; i++) {
                selected_card_info.push(checked_card_info[i]);
            }
            console.log(selected_card_info);
            var check = $(this).attr("checked");
            if (check) {

                var card_type = $(this).attr("data-type");
                var create_by_role = $(this).attr('data-role');
                //判断规则
                for (var i = 0; i < selected_card_info.length; i++) {

                    //专营店只能选择要么自营店里的优惠券要么平台的优惠券

                    if (create_by_role != selected_card_info[i].card_role) {
                        $(this).removeAttr("checked");
                        layer.msg("平台、专营店、集团优惠券无法同时选择,只能选择其中一种");
                        return;
                    }

                    if ((card_type == 4 && selected_card_info[i].card_type != 4) || (card_type != 4 && selected_card_info[i].card_type == 4)) {
                        $(this).removeAttr("checked");
                        layer.msg("满减券不可与其他优惠券同时使用");
                        return;
                    }

                }
                checked_card_info.push({
                    card_id: $(this).data("id"),
                    card_type: $(this).data("type"),
                    card_role: $(this).data("role"),
                    card_name: $(this).data("title")
                })
                console.log(checked_card_info);

            } else {
                var s_card_info = [];
                for (var i = 0; i < checked_card_info.length; i++) {
                    if (checked_card_info[i].card_id != $(this).data("id")) {
                        s_card_info.push(checked_card_info[i]);
                    }
                }
                checked_card_info = s_card_info;
                console.log(checked_card_info);
            }

        })

        $("#card_add").on("click", function() {
            var html = '';
            for (var i = 0; i < checked_card_info.length; i++) {
                html += '<tr class="info"> <td ><a class="card-info" data-card-id="' + checked_card_info[i].card_id + '" data-card-type="' + checked_card_info[i].card_type + '" data-card-role="' + checked_card_info[i].card_role + '">' + checked_card_info[i].card_name + '</a></td>' +
                    '<td class="text-right"> </td> <td class="text-right"> <label class="checkbox-inline m-r-10 p-b-10"> <input type="checkbox" class="is-can-receive" value="1">可领取' +
                    '</label> <button class="btn btn-danger btn-sm m-r-5 m-b-5 del-card">删除</button></td></tr>';
            }
            $("#haved-card-" + select_card_type).append(html);

        })
        //删除卡券
        $("body").on('click', '.del-card', function(e) {
            e.preventDefault();
            $(this).parents('tr').remove();

        })


        var dlr_checked_code = [];
        var dlr_checked_name = [];

        $("#comm_dlr_type").on("change", function() {
            if ("{$role}" == 1) {
                //商品类型修改，经销商置空
                $("#dlr_show").val('');
                $("#dlr_hide").val('');
                dlr_checked_code.length = 0;
                dlr_checked_name.length = 0;
            }
        })

        $("#check_all").on("change", function() {
            var check = $(this).attr("checked");
            if (check) {
                $.each($(".dlr_checkbox.single"), function(i, ele) {
                    $(ele).attr("checked", true);
                    dlr_checked_code[i] = $(ele).attr("data-id");
                    dlr_checked_name[i] = $(ele).attr("data-title");
                })
            } else {
                $.each($(".dlr_checkbox.single"), function(i, ele) {
                    $(ele).attr("checked", false);
                })
                dlr_checked_code.length = 0;
                dlr_checked_name.length = 0;
            }
        })

        $("#dlr_add").on("click", function() {
            dlr_checked_code.length = 0;
            dlr_checked_name.length = 0;
            $('.dlr_checkbox.single').each(function(i) {
                if ($(this).attr("checked")) {
                    dlr_checked_code[dlr_checked_code.length] = $(this).attr("data-id");
                    dlr_checked_name[dlr_checked_name.length] = $(this).attr("data-title");
                }
            })
            if (dlr_checked_name.length <= 0) {
                layer.msg("请选择经销商")
                return;
            }
            var dlr_ids = dlr_checked_code.join(",");
            var dlr_names = dlr_checked_name.join(",");
            $("#dlr_show").val(dlr_names)
            $("#dlr_hide").val(dlr_ids)
        })

        var dlr_data = <?php echo $dlr_list; ?>

        $("#dlr_show").on("click", function() {
            var select_data = $("#dlr_hide").val().split(',');
            Custom.selectDlr(dlr_data, select_data, function(dlr_code, dlr_name) {
                $("#dlr_show").val(dlr_name.join(','));
                $("#dlr_hide").val(dlr_code.join(','));
                console.log('dlr_code:' + dlr_code);
                console.log('dlr_name:' + dlr_name);
            });

            /* var comm_dlr_type_id = $("#comm_dlr_type").val();
             var commodity_id = "{$info.id}";
             if(comm_dlr_type_id.length==0){
                 layer.msg('请先选择商品类型');
                 return
             }else {
                 $.getJSON("{:url('ajaxGetDlr')}",{comm_dlr_type_id:comm_dlr_type_id,commodity_id:commodity_id},function (resData) {
                     if(resData.error==0){
                         $("#dlr_content").empty();
                         var data = resData.data;
                         for (var i = 0;i<data.length;i++){
                             var label = $('<label class="m-r-5 dlr_label" style="width: 30%"></label>');
                             label.appendTo($("#dlr_content"));
                             var input = $('<input type="checkbox" class="dlr_checkbox single min" data-id="'+data[i].dlr_code+'" data-title="'+data[i].dlr_name+'">');
                             input.appendTo(label);
                             label.append(data[i].dlr_name);
                         }

                     }else {
                         layer.msg(resData.msg);
                     }
                 })
             }*/
        })

        //设置最高价最低价
        function setMaxMinPrice() {
            var price_arr = [];

            $("#spec_default .set-price").each(function() {
                price_arr.push($(this).val());
            })


            $("input[name='discount_price_range_start']").val(Math.min.apply(null, price_arr));
            $("input[name='discount_price_range_end']").val(Math.max.apply(null, price_arr));

        }
        //批量设置价格
        $('#all_price').editable({
            success: function(response, newValue) {
                $(".set-price").val(newValue);
                $(".editable-cancel").click();
                allcommission();
                setMaxMinPrice();
                return false;

            },
            validate: function(value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9/.]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });
        //批量设置成本价
        $('#cost_price').editable({
            success: function(response, newValue) {
                $(".cost-price").val(newValue);
                $(".editable-cancel").click();

                return false;

            },
            validate: function(value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9/.]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });


        //批量设置库存
        $('#all_stock').editable({

            success: function(response, newValue) {
                $(".set-stock").val(newValue);
                $(".editable-cancel").click();
                return false;
            },
            validate: function(value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });

        //批量设置分成
        $('#all_divided').editable({

            success: function(response, newValue) {
                $(".set-divided").val(newValue);
                $(".editable-cancel").click();
                allcommission();
                return false;
            },
            validate: function(value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9/.]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });

        //批量设置安卓费
        $('#all_install').editable({
            success: function(response, newValue) {
                $(".set-install").val(newValue);
                $(".editable-cancel").click();
                allcommission();

                return false;

            },
            validate: function(value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9/.]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });

        //        data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/"

        $("#bt-submit").on("click", function() {
            var validate = $("#add_form").psly().validate(); //表单验证
            if (!validate) {
                return false;
            }
            var service_channel = $("[name='service_channel[]']").val();
            if(service_channel && service_channel.length > 6){
                layer.msg('商品服务标签最多只能选择6个');
                return false;
            }
            var four_l = 0;
            var one_l = 0;
            var up_down_channel_name = getUpDownChannel();
            var set_sku_list = [];
            $('#haved-commodtiy').find("tr").each(function(i, tr) {
                var set_sku = new Object();
                set_sku['json_st'] = $(tr).find(".json_st").val();
                set_sku['initial_num'] = $(tr).find(".initial_num").val();
                set_sku['user_can_des'] = $(tr).find(".user_can_des").val();
                set_sku['can_select'] = $(tr).find(".can_select").val();
                set_sku_list[set_sku_list.length] = set_sku;
                if ($(tr).find(".four_l").val() == 4) {
                    four_l = 4;
                } else if ($(tr).find(".one_l").val() == 1) {
                    one_l = 1;
                }
            })
            if (set_sku_list.length == 0) {
                layer.msg('请至少选择一个子商品');
                return false;
            }
            if (four_l == 4 && one_l != 1) {
                layer.msg('您选择了4L机油，请再额外选择1L机油的商品');
                return false;
            }
            if (set_sku_list.length == 0) {
                layer.msg('请至少选择一个子商品');
                return false;
            }
            var set_sku_list_str = JSON.stringify(set_sku_list);
            $("input[name='set_sku_list']").val(set_sku_list_str);
            $("#has_use_card_mj").val(manjian_exist == true ? 1 : 0);
            var wx_card = getCardInfo(1);
            var ot_card = getCardInfo(2);
            var dlr_label = $("input[name='dlr_label']:checked").val();
            if (!dlr_label) {
                dlr_label = 0;
            }

            // 活动图片
            var activity_image=[];
            $(".activity_image_group .goods_pic").each(function(){
                $(this).find('li img').each(function(){
                    activity_image.push($(this).attr("image-value"));
                });
            });
            if(activity_image.length !== 0){
                var activity_start_time = $('input[name = activity_start_time]').val()
                if(activity_start_time == ''){
                    layer.msg('活动头图生效开始时间不能为空',{icon:2})
                    return;
                }
                var activity_end_time = $('input[name = activity_end_time]').val()
                if(activity_end_time == ''){
                    layer.msg('活动头图生效结束时间不能为空',{icon:2})
                    return;
                }
            }

            //todo
            //alert($("input[name='is_store']").is(':checked'));
            //return false;
            var shelves_type = $("input[name='shelves_type'").val();
            var home_type_data = '';
            if(shelves_type == 5 || shelves_type == 6 || shelves_type == 7){
                var home_type = home_type_class.getValue('value');
                if(home_type.length !== 0){
                    home_type_data = home_type.join()
                }
            }

            var data = $.param({
                wx_card: wx_card,
                ot_card: ot_card,
                'up_down_channel_name': up_down_channel_name,
                dlr_label: dlr_label,
                home_type_data:home_type_data,
                "activity_image":activity_image,
                "is_add":false,
            }) + '&' + $("#add_form").serialize();
            var alert_obj = $("div.alert-danger");
            Custom.ajaxPost("{:url('saveGroup')}", data, alert_obj, "{:url('commodity/index',['is_platform'=>$is_platform])}", function(res) {
                if (res.error == 0) {
                    layer.msg(res.msg);
                    window.location.href = document.referrer;
                    //window.history.go(-1);
                } else {
                    layer.msg(res.msg);
                }

            });
        })

        $('.add-union-spec').on('click', function() {
            $('.union-goods-option').remove();
            $('.json_st').each(function(index, item) {
                var goods = JSON.parse($(item).val());
                $('.union-goods').append('<option class="union-goods-option" data-good-id="' + goods.commodity_id + '" value="' + goods.commodity_id + '">' + goods.commodity_name + '</option>')
            })
            $('#union-spu-modal').modal("show");
        })

        $('.union-goods').on('change', function() {
            var that = $(this);
            var commodity_id = that.children('option:selected').data('good-id');
            $.getJSON("{:url('ajaxGetSpec')}", {
                commodity_id: commodity_id
            }, function(resData) {
                var spec = resData.data.sp_title;
                var specElement = that.parent().find('.union-spec');
                var specVal = resData.data.sp_list;
                var specValElement = that.parent().find('.union-spec-val');
                if (Object.keys(spec).length == 1) {
                    specElement.find('.spec-option').remove();
                    specValElement.find('.spec-val-option').remove();
                    for (var index in spec) {
                        specElement.append('<option class="spec-option" value="' + index + '">' + spec[index] + '</option>')
                    }
                    for (var index in specVal) {
                        specValElement.append('<option class="spec-val-option" value="' + index + '">' + specVal[index].sp_value_name + '</option>')
                    }
                }
            })
        })

        var goodsSpec = '';
        var unionArr = [];
        if ($('#union-spu-modal .spec-relation tr').length > 0) {
            $('#union-spu-modal .spec-relation tr').each(function(i, item) {
                unionArr.push($(item).data('relation'));
                goodsSpec = $(item).data('goods-spec');
            })
        }
        //增加关联
        $('.add-union-btn').on('click', function() {
            var goodsA = {
                sub_commodity_id: $('.union-1').find('option:selected').val(),
                sub_commodity_name: $('.union-1').find('option:selected').text(),
                sp_id: $('.spec-1').find('option:selected').val(),
                sp_name: $('.spec-1').find('option:selected').text(),
                sp_value_id: $('.spec-val-1').find('option:selected').val(),
                sp_value_name: $('.spec-val-1').find('option:selected').text()
            };
            var goodsB = {
                assoc_sub_commodity_id: $('.union-2').find('option:selected').val(),
                assoc_sub_commodity_name: $('.union-2').find('option:selected').text(),
                assoc_sp_id: $('.spec-2').find('option:selected').val(),
                assoc_sp_name: $('.spec-2').find('option:selected').text(),
                assoc_sp_value_id: $('.spec-val-2').find('option:selected').val(),
                assoc_sp_value_name: $('.spec-val-2').find('option:selected').text()
            };
            var goodsUnion = goodsA.sub_commodity_id + '-' + goodsA.sp_id + ',' + goodsB.assoc_sub_commodity_id + '-' + goodsB.assoc_sp_id;
            var unionCode = goodsA.sub_commodity_id + '-' + goodsA.sp_id + '-' + goodsA.sp_value_id + ',' + goodsB.assoc_sub_commodity_id + '-' + goodsB.assoc_sp_id + '-' + goodsB.assoc_sp_value_id;
            var spec_union = JSON.stringify({
                "goods_a": goodsA,
                "goods_b": goodsB,
                "union_code": unionCode
            });
            console.log(spec_union);

            if (!goodsA.sub_commodity_id || !goodsB.assoc_sub_commodity_id) {
                alert('请选择需要关联的商品');
                return;
            }
            if (goodsA.sub_commodity_id == goodsB.assoc_sub_commodity_id) {
                alert('同一商品不可关联');
                return;
            }
            if (goodsSpec != '' && goodsUnion != goodsSpec) {
                alert('当前不能存在第三种商品关联关系');
                return;
            }
            if (unionArr.indexOf(unionCode) != -1) {
                alert('该关联关系已存在');
                return;
            }
            goodsSpec = goodsUnion;
            unionArr.push(unionCode);
            var relation = goodsA.sub_commodity_name + '｜' + goodsA.sp_name + '｜' + goodsA.sp_value_name + ' ———— ' + goodsB.assoc_sub_commodity_name + '｜' + goodsB.assoc_sp_name + '｜' + goodsB.assoc_sp_value_name;
            var relationHtml = '<tr data-relation="' + unionCode + '">' +
                '<td>' + relation + '</td>' +
                '<td><a href="javascript:;" class="union-del" style="width:20px">删除</a><input type="hidden" name="spec_union[]" value=\'' + spec_union + '\'></td>' +
                '</tr>';
            $('#union-spu-modal .spec-relation').append(relationHtml);
        })

        $('#union-confirm').on('click', function() {
            $('.union-content').text('');
            var element = $('.spec-relation').clone();
            $('.union-content').append(element);
            $('#union-spu-modal').modal("hide");
            console.log(element);
        })

        $('.union-for-del').on('click', '.union-del', function() {
            var row = $(this).parents('tr');
            unionArr.splice(unionArr.indexOf(row.data('relation')), 1)
            row.remove();
            if ($('#union-spu-modal .spec-relation tr').length == 0) {
                goodsSpec = ''
            }
        })
        //商品变动清除联动
        $('#sku-confirm,#del-confirm').on('click', function() {
            $('.union-for-del').text('');
            goodsSpec = '';
            unionArr = [];
        })

        //算分佣比例
        $(".set-divided,.set-install,.set-price").on('change', function(e) {
            var prents_obj = $(this).parents('tr');
            var price = checkPrice(prents_obj.find('.set-price').val());
            var divided = checkPrice(prents_obj.find('.set-divided').val());
            var install = checkPrice(prents_obj.find('.set-install').val());
            if (price === false) {
                layer.msg('价格格式不对')
                return;
            }
            if (divided === false) {
                layer.msg('分成格式不对')
                return;
            }
            if (install === false) {
                layer.msg('安装费格式不对')
                return;
            }
            var commission = (price - install) * (divided / 100) + install;
            // alert(commission);
            prents_obj.find('.commission').html(Math.round(commission * 100) / 100);
            setMaxMinPrice();


        })

        //批量设置分佣
        function allcommission() {
            $("#spec_default tr").each(function() {
                var price = checkPrice($(this).find('.set-price').val());
                var divided = checkPrice($(this).find('.set-divided').val());
                var install = checkPrice($(this).find('.set-install').val());
                if (price === false) {
                    layer.msg('价格格式不对')
                    return;
                }
                if (divided === false) {
                    layer.msg('分成格式不对')
                    return;
                }
                if (install === false) {
                    layer.msg('安装费格式不对')
                    return;
                }

                var commission = (price - install) * (divided / 100) + install;
                // alert(commission);
                $(this).find('.commission').html(Math.round(commission * 100) / 100);
            })
        }

        function checkPrice(price) {
            if (!price || price == 0) return 0
            var reg = /^[0-9]+([.]{1}[0-9]{1,2})?$/
            if (!reg.test(price)) {
                return false;
            } else {
                return parseFloat(price);
            }

        }

        function checkInt(divided) {
            if (!divided || divided == 0) return 0

            var reg = /^\+?[1-9][0-9]*$/;

            if (!reg.test(divided)) {
                return false;
            } else {
                return parseInt(divided);
            }
        }


        $("body").on('click', '#GWDLR', function() {
            if ($("#GWDLR").is(':checked')) {
                $('#dealer_select').removeClass('hidden');
            } else {
                $('#dealer_select').addClass('hidden');
            }
        });

        var dlr_data = <?= $dlr_list ?>;

        $("body").on("click", '#dlr_show', function() {
            var select_data = $("#dlr_hide").val().split(',');
            Custom.selectDlr(dlr_data, select_data, function(dlr_code, dlr_name) {
                $("#dlr_show").val(dlr_name.join(','));
                $("#dlr_hide").val(dlr_code.join(','));
            });
        })

        $("body").on("click", '#id_is_store', function() {
            if ($(this).is(':checked')) {
                $(".sbt_div").removeClass("hidden");
            } else {
                $(".sbt_div").addClass("hidden");
            }
        })

        $(".datetimepicker4").datetimepicker({
            format:"YYYY-MM-DD HH:mm:ss",
            locale: moment.locale('zh-cn'),
        });

        var image_sp_value_id = '';
        $('.activity_image_group').on('click', '.btn-image', function () {
            image_sp_value_id = $(this).parents('.goods_pic').data('image-id')
            var image_coutn = $(this).parents('.goods_pic').find('li').length;
            if (image_coutn >= 1) {
                layer.msg('图片最多能上传1张');
                return;
            }
            $("#activity-image-input").click();

        });

        $(".activity_image_group").on('click', "del", function () {
            $(this).parents('li').remove();
            $(".activity_img_time").css('display','none')
        });
        //修改图片
        var on_span = 0;
        $(".activity_image_group").on('click', "span", function () {
            $(this).parents('li').addClass('on_span');
            on_span = 1;
            $("#activity-image-input").click();
        });

        $(".activity_image_group").on('change', "#activity-image-input", function (e) {

            /**
             * begin
             *
             * 此处有修改
             * 修改人：吴炜文
             * 时间2017.10.20 16:00
             * 修改内容：上传图片大小、格式做限制
             */
            var files = this.files;
            var cc_rr = check_file(files);
            if (cc_rr == 1) {
                return;
            }
            /**
             * end
             */

            Custom.ajaxFileUpload('activity-image-input', {file_path: 'commodity_activity'}, function (res) {
                if (res.error == 0) {
                    $(".activity_img_time").css('display','block')
                    var data = res.data;
                    if (on_span == 1) {
                        var html = '<img image-value="' + data.image + '" src="' + data.upload_url + data.image + '"> <del></del><span>修改</span> ';
                        $('.on_span').html(html);
                        $('.on_span').removeClass('on_span');
                        on_span = 0;
                    } else {
                        var html = '<li class="move-item"> <img image-value="' + data.image + '" src="' + data.upload_url + data.image + '"> <del></del><span>修改</span> </li>';
                        $("#image-id-" + image_sp_value_id).find('ul').append(html);
                    }
                } else {
                    layer.msg(res.msg);
                }
                $("#activity-image-input").val("");
                image_sp_value_id = '';
            })
        });

    });
    //选择快递必填运费模板
    function express(check) {
        if (check.checked) {
            $("#default_name").val('')
        } else {
            $("#default_name").val(0)
        }
    }

    $("[name='mail_type']").on('change', function () {
        var mail_type = $(this).val();
        console.log(mail_type)
        var sbt_div = $(".sbt_div");
        var ks_md = $(".ks_md");
        var template_guid = $("[name='template_guid']");
        var default_name = $("#default_name");
        sbt_div.addClass("hidden");
        ks_md.addClass("hidden");
        template_guid.removeAttr("disabled", "disabled");
        $('#mail_show_word').removeAttr('required');
        switch (mail_type){
            case '1': sbt_div.removeClass("hidden");template_guid.attr("disabled", "disabled");ks_md.removeClass("hidden");break;
            case '3': sbt_div.removeClass("hidden");ks_md.removeClass("hidden");break;
            case '2': ks_md.addClass("hidden");break;
            case '4': template_guid.attr("disabled", "disabled");$('#mail_show_word').attr('required', 'required');break;
        }
    });
</script>

<script>

    var user_data = [];
    $.ajaxSettings.async = false

    $.get('get_home_type_class?commodity_id=' + $("input[name='commodity_id'").val() + '&shelves_type='+{$info['shelves_type']},function (res){
        var array = JSON.parse(res)
        console.log(array)
        var type_id = array.data.type_id
        $.each(array.data.data,function (i,item){
            user_data[i] = [];
            user_data[i]['name'] = item.class_name
            user_data[i]['value'] = item.id
            var children = [];
            $.each(item.subclass,function (ii,item1){
                children[ii] = [];
                children[ii]['name'] = item1.class_name
                children[ii]['value'] = item1.id
                var children_three = []
                $.each(item1.subclass,function (iii,item2){
                    children_three[iii] = [];
                    children_three[iii]['name'] = item2.class_name
                    children_three[iii]['value'] = item2.id
                    if(type_id.indexOf(item2.id) != -1){
                        children_three[iii]['selected'] = true
                    }
                })
                children[ii]['children'] = children_three
            })
            user_data[i]['children'] = children
        })
    })

    var home_type_class = xmSelect.render({
        el: '#home_type_class',
        autoRow: true,
        cascader: {
            show: true,
            indent: 200,
        },
        height: '200px',
        data(){
            return  user_data;
        }
    })


</script>
<script src="__STATIC__admin_v2/js/group_commodity.js?rand=*******"></script>

{/block}
