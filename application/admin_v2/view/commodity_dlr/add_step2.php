{extend name="public:base_layout" /}

{block name="content"/}

<div class="panel-body">

    <div class="alert alert-danger m-b-8" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">上架商品设置维护</legend>
        <form id="add_form" class="form-horizontal form-bordered" data-parsley-trigger="change">
            <div class="form-group">
                <input type="text" class="hidden" name="commodity_id" value="{$info.id}">
                <label class="control-label col-md-2">商品名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    {$info.commodity_name}

                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">优惠信息:</label>
                <div class="col-md-10">
                    <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">简称:</label>
                    <div class="col-md-3 m-l-0 p-l-0 " >
                        <input type="text" class="form-control " name="favourable_introduction" data-toggle="tooltip" title="字数不能超过10个。" maxlength="10">
                    </div>
                    <label class="control-label col-md-2 width-80 p-l-0 m-r-0 text-right">详情:</label>
                    <div class="col-md-3 m-l-0 p-l-0" >
                        <input type="text" class="form-control " name="favourable_detail"  >
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">活动头图:</label>
                <div class="col-md-10  activity_image_group">
                    <div class="dndc-upload-pic goods_pic" sp-id="0" data-image-id="0" id="image-id-0">
                        <label class="sp-value m-t-10 m-l-10">默认</label>
                        <label>
                            <a href="javascript:;" class="btn-image btn btn-primary m-r-5">上传图片</a>
                        </label>
                        <p style="bottom:5px;">1张,200K,jpg,png</p><ul></ul>
                    </div>
                    <input type="file" name="activity_image" class="hide"  id="activity-image-input">
                </div>
            </div>

            <div class="form-group activity_img_time" style="display:none;">
                <label class="control-label col-md-2"><span style="color: red">*</span>活动头图生效时间:</label>
                <div class="col-md-10  activity_image_group">
                    <div class="col-md-3 p-l-0" style="">
                        <input type="text" name="activity_start_time" id="activity_start_time" placeholder="请输入开始时间" class="form-control datetimepicker4" data-parsley-required="true" >
                    </div>
                    <div class="col-md-3">
                        <input type="text" name="activity_end_time" id="activity_end_time" placeholder="请输入结束时间" class="form-control datetimepicker4" data-parsley-required="true" >
                    </div>
                </div>
            </div>

            <?php if($shelves_type == 6 ):?>
                <div class="form-group">
                    <label class="control-label col-md-2">服务说明:</label>
                    <div class="col-md-10">
                        <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">说明1:</label>
                        <div class="col-md-3 m-l-0 p-l-0 " >
                            <input type="text" class="form-control " name="directions1" data-toggle="tooltip" title="字数不能超过10个。" maxlength="10">
                        </div>
                        <label class="control-label col-md-2 width-80 p-l-0 m-r-0 text-right">说明2:</label>
                        <div class="col-md-3 m-l-0 p-l-0" >
                            <input type="text" class="form-control " name="directions2" data-toggle="tooltip" title="字数不能超过10个。" maxlength="10">
                        </div>
                    </div>
                </div>
            <?php endif;?>
                <div class="form-group">
                    <label class="control-label col-md-2">已售数量:</label>
                    <div class="col-md-10">
                        <input type="text" class="form-control " name="quantitys" value="0" >

                    </div>
                </div>
            <?php if(!empty($card_info)){ ?>
                <div class="form-group">
                    <label class="control-label col-md-2">已关联券:</label>
                    <div class="col-md-10">
                        <?php foreach ($card_info as $key => $val):?>
                            <div class="coupon_info" style="margin-bottom: 5px;">
                                <span>优惠券名称: {$val['card_name']} </span> <span>优惠券有效时间: {$val['validity_date_start']} ~
                                {$val['validity_date_end']}</span>
                            </div>
                        <?php endforeach;?>
                    </div>
                </div>
            <?php } ?>
            <div class="form-group">
                <label class="control-label col-md-2">价格范围<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">原价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0 " >
                        <input type="text" class="form-control " readonly="readonly" name="original_price_range_start" value="{$info.original_price_range_start}" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" >
                    </div>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control " readonly="readonly" name="original_price_range_end" value="{$info.original_price_range_end}" placeholder="最高价格"  data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" >
                    </div>
                    <label class="control-label col-md-2 width-80 p-l-0 m-r-0 text-right">现价范围<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-2 m-l-0 p-l-0" >
                        <input type="text" class="form-control "  readonly="readonly" name="discount_price_range_start" value="{$info.discount_price_range_start}" placeholder="最低价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" >
                    </div>
                    <div class="col-md-2 m-l-0 p-l-0   m-b-10" >
                        <input type="text" class="form-control " readonly="readonly"  name="discount_price_range_end" value="{$info.discount_price_range_end}" placeholder="最高价格" data-parsley-required="true" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" >
                    </div>
                    <?php if ($shelves_type == 1):?>
                        <div class="col-md-3 p-l-0 ">
                            <label class="control-label col-md-5 width-80 p-l-0 m-r-0 text-left">积分价格:</label>
                            <div class="col-md-6 m-l-0 p-l-0 " >
                                <input type="text" class="form-control " name="integral_price" value="" placeholder="积分价格"  data-parsley-min="0.00" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                            </div>
                        </div>
                    <?php endif;?>


                </div>




            </div>
            <div class="form-group hidden">
                <label class="control-label col-md-2">优惠券:</label>
                <div class="col-md-9">

                    <label class="vouher-margin">
                        是否可以使用多张：
                        <input type="radio"  name="is_card_multic" value="1" class="vouher-margin"  >是
                        <input type="radio"  name="is_card_multic" value="0" class="vouher-margin" checked>否
                    </label>

                    <label class="vouher-margin">
                        <input id="card_hide" type="text" class="hidden" name="card_id" >
                        <input id="has_use_card_mj" type="text" class="hidden" name="has_use_card_mj" >
                    </label>

                    <ul class="nav nav-tabs" style="margin-top:10px;">
                        <li class="active"><a href="#default-tab-1" data-toggle="tab" aria-expanded="true" style="line-height: 12px;">微信公众号</a></li>
                        <li class=""><a href="#default-tab-2" data-toggle="tab" aria-expanded="false" style="line-height: 12px;">其他</a></li>

                    </ul>
                    <div class="tab-content p-t-5" style="background: #f2f6f9ab;" >
                        <div class="tab-pane fade active in" id="default-tab-1">
                            <div class="col-md-12 p-l-0" style="height: 40px;">
                                <blockquote class="text-left col-md-3 p-t-5 p-b-5" style="width: 200px;">
                                    <p class="text-left" style="font-size: 12px;width: 200px;">用于微信公众号进去商城的优惠劵</p>

                                </blockquote>
                                <p class="col-md-4 m-b-0 ">
                                    <a href="javascript:;" class="btn btn-sm btn-primary add-card-wx">添加</a>
                                </p>
                            </div>
                            <div>
                                <table id="" class="table">
                                    <thead></thead>
                                    <tbody id="haved-card-1">


                                    </tbody>
                                </table>

                            </div>


                        </div>
                        <div class="tab-pane fade" id="default-tab-2">
                            <div class="col-md-12 p-l-0" style="height: 40px;">
                                <blockquote class="text-left col-md-2 p-t-5 p-b-5 ">
                                    <p class="text-left" style="font-size: 12px;width: 200px;">其他优惠劵</p>

                                </blockquote>
                                <p class="col-md-3 m-b-0 ">
                                    <a href="javascript:;" class="btn btn-sm btn-primary add-card-ot">添加</a>
                                </p>
                            </div>
                            <div>
                                <table id="" class="table">
                                    <thead></thead>

                                    <tbody id="haved-card-2">

                                    </tbody>
                                </table>


                            </div>
                        </div>

                    </div>

                </div>
            </div>
            <?php if($shelves_type !=2 ):?>
                <div class="form-group">
                    <label class="control-label col-md-2">销售渠道<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-4" id="valid-sales-channel">

                        <select class="multiple-select2 form-control" multiple="multiple" name="sales_channel[]"  data-parsley-errors-container="#valid-sales-channel" data-parsley-required="true">
                            <?php foreach ($sales_channel_list as $key => $val):?>
                                <option value="{$key}">{$val}</option>

                            <?php endforeach;?>
                        </select>
                    </div>
                </div>
            <?php endif;?>

            <div class="form-group">
                <label class="control-label col-md-2">商品价格及库存<i class="m-r-3 text-danger">*</i>:</label>
                <input type="text" class="hidden" name="sku_list" value="">
                <div class="col-md-10">

                    <table class="table table-hover table-bordered" style="table-layout:fixed">
                        <thead>
                        <tr>
                            <th class="commodity-th" colspan="12">规格编码</th>
                            {volist name='spec_list.sp_title' id ='vo' }
                            <th class="commodity-th" colspan="12">{$vo}</th>
                            {/volist}
                            <th class="commodity-th" colspan="16">
                                {if $info['dd_commodity_type'] == 9}
                                <div id="all_price1" style="dwidth: 63px;height: 29px;" data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格(元)<i class="fa fa-edit m-l-5"></i></div>
                                {else /}
                                <a href="#" id="all_price"data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入价格" data-title="批量设置价格">价格(元)<i  class="fa fa-edit m-l-5"></i></a>
                                {/if}

                            </th>
                            <th class="commodity-th" colspan="10"><a href="#" id="all_stock"data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入库存" data-title="批量设置库存">库存<i  class="fa fa-edit m-l-5"></i></a></th>
                            <th class="commodity-th" colspan="12">成本价</th>
                            {if condition="$shelves_type==1"}
                            <th class="commodity-th" colspan="12"><a href="#" id="all_divided" data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入分成" data-title="批量设置分成">分成<i  class="fa fa-edit m-l-5"></i></a></th>

                            <th class="commodity-th" colspan="12"><a href="#" id="all_install" data-type="text" class="btn btn-primary btn-sm" data-value="" data-placeholder="请输入安装费" data-title="批量设置安装费">安装费<i  class="fa fa-edit m-l-5"></i></a></th>
                            <th class="commodity-th" colspan="6">分佣</th>
                            {/if}
                        </tr>
                        </thead>
                        <tbody id="spec_default">
                        <!--行数-->
                        {volist name="spec_list.sku_list" id="sku_vo" key="k"}
                        <tr data-id="{$sku_vo.id}" colspan="12">
                            <!--列数-->
                            <input type="hidden" disabled class="sp_value_list" name="sp_value_list" value="{$sku_vo.sp_value_list}">
                            <input type="hidden" disabled class="commodity_id" name="commodity_id" value="{$sku_vo.commodity_id}">
                            <input type="hidden" disabled class="sku_code" name="sku_code" value="{$sku_vo.sku_code}">
                            <td class="commodity-td sku_code" colspan="12" onclick="exhibition(this,'{$sku_vo.sku_code}')" style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">{$sku_vo.sku_code}</td>
                            <?php if(!empty($sku_vo['sp_value_arr'])):?>
                                {volist name='$sku_vo.sp_value_arr' id ='sp_id' }
                                <td class="commodity-td" colspan="12">{$spec_list.sp_list[$sp_id]['sp_value_name']}</td>
                                {/volist}
                            <?php endif;?>

                            <td class="commodity-td  width-sku" id="valid-{$k}" colspan="16" >
                                <div class="input-group  width-sku ">
                                    <input  type="text" {in name="$info['dd_commodity_type']" value="1,3,4,9"}readonly="readonly"{/in} class="form-control set-price default-price" value="{$sku_vo.price}" data-parsley-required="true" data-parsley-errors-container="#valid-{$k}" data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                                    <span class="input-group-addon">元</span>
                                </div>
                            </td>

                            <td class="commodity-td width-sku" colspan="10">
                                <div class="input-group ">
                                    <input  type="text" class="form-control set-stock default-stock" value="{$sku_vo.stock}" data-parsley-required="true" data-parsley-type="integer">
                                </div>
                            </td>

                            <td class="commodity-td width-sku" colspan="12">
                                <div class="input-group ">
                                    <input  type="text" readonly class="form-control cost-price" name="cost_price" value="{$sku_vo.cost_price}">
                                    <span class="input-group-addon">元</span>
                                </div>
                            </td>
                            {if condition="$shelves_type==1"}

                            <td class="commodity-td width-sku" colspan="12">
                                <div class="input-group ">
                                    <input  type="text" class="form-control set-divided " value=""  data-parsley-min="0.00" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" data-parsley-min="0">
                                    <span class="input-group-addon">%</span>
                                </div>
                            </td>


                            <td class="commodity-td width-sku" colspan="12">
                                <div class="input-group ">
                                    <input  type="text" class="form-control set-install " value=""  data-parsley-min="0.00" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/" data-parsley-min="0">
                                    <span class="input-group-addon">元</span>
                                </div>
                            </td>
                            <td class="commodity-td width-sku commission" colspan="6">

                            </td>
                            {/if}
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    点击<i class="fa fa-edit"></i>可批量修改所在列的值。<br>
                    当规格值较多时，可在操作区域通过滑动滚动条查看超出隐藏区域

                </div>
            </div>
            <?php if ($shelves_type !=4) :?>
                <div class="form-group">

                    <label class="control-label col-md-2">商品类型<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-10">
                        <select id="comm_dlr_type" class="form-control width-300" name="commodity_dlr_type_id" data-parsley-required="true">
                            {volist name='comm_dlr_type_list' id='commDlrType'}
                            <option value="{$commDlrType.id}">{$commDlrType.inner_name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
            <?php endif;?>
            {if condition="$role==1 and !in_array($shelves_type, [5,6,7])"}
            <div class="form-group">
                <label class="control-label col-md-2">经销商<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <input id="dlr_show" type="text"  class="form-control width-300" value="" placeholder="请点击选择经销商"  data-parsley-required="true">
                    <input id="dlr_hide" type="text"  name="dlr_code" class="hidden" value="">
                </div>
            </div>
            {/if}
            <div class="form-group">
                <label class="control-label col-md-2">配送方式<i class="m-r-3 text-danger">*</i></label>
                <div class="col-md-10">
                    {if condition="$commodity_class == 8"} <!--充电桩-->
                    <input type="radio" name="mail_type" value="2"> <span style="color: black"> 快递</span>&nbsp;&nbsp;&nbsp;
                    {else/}
                    <input type="radio" name="mail_type" value="2"> <span style="color: black"> 快递</span>&nbsp;&nbsp;&nbsp;
                    <input type="radio" name="mail_type" value="1"> <span style="color: black"> 自提</span>&nbsp;&nbsp;&nbsp;
                    <input type="radio" name="mail_type" value="3"> <span style="color: black"> 可快递或自提</span>&nbsp;&nbsp;&nbsp;
                    <input type="radio" name="mail_type" value="4"> <span style="color: black"> 仅展示</span>&nbsp;&nbsp;<input type="text" name="mail_show_word" id="mail_show_word" style="color: black" value="商品购买详询专营店" maxlength="10">
                    <span ><br /><br />{$pay_text}</span>
                    {/if}
                </div>
            </div>
            <div class="form-group ks_md">
                <label class="control-label col-md-2">可售门店:</label>
                <div class="col-md-10">
                    <div class="col-md-4  m-l-0 p-l-0">
                        <select id="dlr_groups" class="form-control width-130" name="dlr_groups" data-parsley-required="true">
                            <option value="0" id="default_group">请选择</option>
                            {volist name='$group_list' id='gitem'}
                            <option value="{$gitem.id}">{$gitem.dlr_group_name}</option>
                            {/volist}
                        </select>
                    </div>

                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">运费模板:</label>
                <div class="col-md-10">
<!--                    <label class="checkbox-inline">-->
<!--                        <input type="checkbox" onclick="express(this)" name="is_mail" value="1">支持快递-->
<!--                    </label>-->
<!--                    <label class="checkbox-inline">-->
<!--                        <input id="id_is_store" type="checkbox" name="is_store" value="1" checked>门店自提-->
<!--                    </label>-->
                    <div class="col-md-2">
                        <select id="template_guid" class="form-control width-130" name="template_guid" data-parsley-required="true">
                            <option value="0" id="default_name">请选择</option>
                            {volist name='$templatelist' id='templatelistitem'}
                            <option value="{$templatelistitem.guid}">{$templatelistitem.template_name}</option>
                            {/volist}
                        </select>
                    </div>

                </div>
            </div>

            <?php if (in_array($shelves_type, [5,6,7]) && $info['dd_commodity_type'] != 8) {?>

                <div class="form-group sbt_div hidden ">
                    <label class="control-label col-md-2">预约类型<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-10">
                        <select id="template_guid" class="form-control width-300" name="subscribe_type" data-parsley-required="true">
                            <option value="">请选择</option>
                            <?php foreach ($subscribe_types as $k => $vo): ?>
                                <option value="{$k}">{$vo}</option>
                            <?php endforeach;?>
                        </select>
                        <span>购买安装类商品产生预约单时，根据此选择的类型提交预约单</span>
                    </div>
                </div>

            <?php } ?>
                <div class="form-group">
                    <label class="control-label col-md-2">积分支付:</label>
                    <div class="col-md-10">
                        <label class="checkbox-inline">
                            <input type="checkbox" name="factory_points" value="1">厂家积分
                        </label>
                        <?php if (!in_array($shelves_type, [5,6,7])) {?>
                            <label class="checkbox-inline">
                                <input type="checkbox" name="dlr_points" value="1">专营店积分
                            </label>
                        <?php } ?>
                    </div>
                </div>
            <div class="form-group">
                <label class="control-label col-md-2">支付方式<i class="m-r-3 text-danger">*</i></label>
                <div class="col-md-10">
                    <input type="radio" checked name="pay_style" value="1"> <span style="color: black"> 现金+积分</span>
                    <input type="radio" name="pay_style" value="2"> <span style="color: black"> 现金</span>
                    <input type="radio" name="pay_style" value="3"> <span style="color: black"> 积分</span>
                    <span ><br /><br />{$pay_text}</span>
                </div>
            </div>
<!--                <div class="form-group">-->
<!--                    <label class="control-label col-md-2">特殊支付:</label>-->
<!--                    <div class="col-md-10">-->
<!--                        <select class="form-control width-300" name="pay_dlr_code" >-->
<!--                            <option value="">请选择</option>-->
                            <?php foreach ($pay_dlr_code_arr as $k => $vo): ?>
<!--                                <option value="{$k}" >{$vo}</option>-->
                            <?php endforeach; ?>
<!--                        </select>-->
<!--                        <span >当商品类型中无可选择的支付类型时，需指定特殊支付账号，请先前往基础数据中设置特殊支付的值类型后再选择。</span>-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div class="form-group">-->
<!--                    <label class="control-label col-md-2">最多可用积分:</label>-->
<!--                    <div class="col-md-10">-->
<!--                        <div class="col-md-2 m-l-0 p-l-0" >-->
<!--                            <input type="text" class="form-control " name="max_point" placeholder="最多可用积分" value="0" >-->
<!--                            <span >(10积分=1元，0不限制)</span>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
            <div class="form-group">
                <label class="control-label col-md-2">商品属性:</label>
                <div class="col-md-10">
                    <select class="form-control width-300" name="commodity_attr" >
                        <option value="">请选择</option>
                        {volist name="comm_attr_list" id="vo" key="k"}
                        <option value="{$k}">{$vo}</option>
                        {/volist}
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">商品标签:</label>
                <div class="col-md-10">
                    <input type="text" class="form-control width-300" name="commodity_label" data-toggle="tooltip" placeholder="填写后将在商品名称前显示" maxlength="5">
                    <span >非必填项，最多5个字。填写后显示在首页、专题页、商品列表页、商品详情页的商品标题前。</span>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">商品服务标签:</label>
                <div class="col-md-4" id="valid-service-channel">

                    <select class="multiple-select2 form-control" multiple="multiple" name="service_channel[]"  data-parsley-errors-container="#valid-service-channel">
                        <?php foreach ($service_channel_list as $key => $val):?>
                            <option value="{$key}">{$val}</option>

                        <?php endforeach;?>
                    </select>
                </div>
            </div>

            {in name="$shelves_type" value="5,6,7"}
            <div class="form-group">
                <label class="control-label col-md-2">映射前台分类:</label>
                <div class="col-md-4" id="home_type_class"></div>
            </div>
            {/in}

            <?php if ($shelves_type ==1 ):?>
                <div class="form-group">
                    <label class="control-label col-md-2">赠送积分:</label>
                    <div class="col-md-2">
                        <input type="text" name="integral_per" value="" class="form-control col-md-4"   data-parsley-min="0.00" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">
                    </div>
                </div>

            <?php elseif($shelves_type==2):?>
                <div class="form-group">
                    <label class="control-label col-md-2">是否新品:</label>
                    <div class="col-md-10">
                        <div class="col-md-1 p-l-0 width-50">
                            <label class="checkbox-inline">
                                <input type="checkbox" name="is_new_products" value="1">是
                            </label>
                        </div>
                        <div class="input-group col-md-2" >
                            <input type="text" name="new_products_sort" value="" class="form-control col-md-4"  data-parsley-range="[0,10000]"">
                            <span class="input-group-addon ">排名</span>
                        </div>
                        <p class="m-t-5">新品排行按照排名从小到大的顺序排序</p>
                    </div>
                </div>

            <?php endif;?>

            {if condition="$shelves_type == 6"}
                <legend class="pull-left width-full m-t-15">商品上下架</legend>
                <div class="form-group" id="listing_type" style="display:none">
                    <label class="control-label col-md-2">上架类型:</label>
                    <div class="col-md-10">
                        <input type="radio" checked name="listing_type" value="1"> <span style="color: black"> 普通上架</span>
                        {if condition="$commodity_class == 1"}
                        <input type="radio" name="listing_type" value="2"> <span style="color: black"> 众筹上架</span>
                        <span >配送方式为快递，且配置运费模版,才可选众筹上架。</span>
                        {/if}
                    </div>

                </div>
                <div class="form-group" id="up_down">
                    <label class="control-label col-md-2">上下架渠道:</label>
                    <div class="col-md-10">
                        <div>
                            <?php foreach ($up_down_channel as $key => $val): ?>
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]"
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endforeach; ?>
                        </div>

                    </div>

                </div>
                <div class="form-group" id="up_down">
                    <label class="control-label col-md-2">
                        温馨提示:</label>
                    <div class="col-md-10">

                        <div>
                            <textarea name="remark" class="form-control col-md-4" ></textarea>
                        </div>

                    </div>

                </div>
            {/if}


            <?php if ($shelves_type == 7):?>
                <legend class="pull-left width-full m-t-15">商品上下架</legend>
                <div class="form-group" id="listing_type" style="display:none">
                    <label class="control-label col-md-2">上架类型:</label>
                    <div class="col-md-10">
                        <input type="radio" checked name="listing_type" value="1"> <span style="color: black"> 普通上架</span>
                        {if condition="$commodity_class == 1"}
                        <input type="radio" name="listing_type" value="2"> <span style="color: black"> 众筹上架</span>
                        <span >配送方式为快递，且配置运费模版,才可选众筹上架。</span>
                        {/if}

                    </div>
                </div>
                <div class="form-group" id="up_down">
                    <label class="control-label col-md-2">上下架渠道:</label>
                    <div class="col-md-10">

                        <div>
                            <?php foreach ($up_down_channel as $key => $val): ?>
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]"
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endforeach; ?>
                        </div>

                    </div>

                </div>
                <div class="form-group" id="up_down">
                    <label class="control-label col-md-2">
                        温馨提示:</label>
                    <div class="col-md-10">

                        <div>
                            <textarea name="remark" class="form-control col-md-4" ></textarea>
                        </div>

                    </div>

                </div>
            <?php endif;?>

            <?php if ($shelves_type ==5) {?>
                <legend class="pull-left width-full m-t-15">商品上下架</legend>
                <div class="form-group" id="listing_type" style="display:none">
                    <label class="control-label col-md-2">上架类型:</label>
                    <div class="col-md-10">
                        <input type="radio" checked name="listing_type" value="1"> <span style="color: black"> 普通上架</span>
                        {if condition="$commodity_class == 1"}
                        <input type="radio" name="listing_type" value="2"> <span style="color: black"> 众筹上架</span>
                        <span >配送方式为快递，且配置运费模版,才可选众筹上架。</span>
                        {/if}
                    </div>
                </div>
                <div class="form-group" id="up_down">
                    <label class="control-label col-md-2">上下架渠道:</label>
                    <div class="col-md-10">
                        <div>
                            <?php foreach ($up_down_channel as $key => $val): ?>
                                <label class="checkbox-inline">
                                    <input type="checkbox" class="up_down_channel"
                                           name="up_down_channel[]"
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endforeach; ?>
                        </div>

                        <div style="margin-top:20px;" class="hidden" id="dealer_select">
                            <input id="dlr_show" type="text" class="form-control width-300" value="" placeholder="请点击选择经销商"
                                   data-parsley-required="true">
                            <input id="dlr_hide" type="text" name="dlr_code" class="hidden" value="">
                        </div>

                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label col-md-2">上下架时间:</label>
                    <div class="col-md-10  activity_image_group">
                        <div class="col-md-3 p-l-0" style="">
                            <input type="text" name="latest_listing_time" id="latest_listing_time" placeholder="请输入开始时间" class="form-control datetimepicker4" >
                        </div>
                        <div class="col-md-3">
                            <input type="text" name="latest_end_time" id="latest_end_time" placeholder="请输入结束时间" class="form-control datetimepicker4">
                        </div>
                        <div class="col-md-9">
                            <span>*可不设置结束时间，若不设置，商品有效期为长期</span>
                        </div>
                    </div>
                </div>

                <legend class="pull-left width-full m-t-15">商品分账信息</legend>
                <div class="form-group" id="spr_id">
                    <label class="control-label col-md-2">分账规则<i class="m-r-3 text-danger">*</i>:</label>
                    <div class="col-md-10">
                        <select class="form-control width-400" name="spr_id"
                                data-parsley-required="true">
                            <option value="">请选择</option>
                            <?php foreach ($spr_rules as $key => $val): ?>
                                <?php $rule_info = json_decode($val['rule_info'],true);?>
                                <option value="{$val['id']}">
                                    {$val['rule_name']}  |  平台-{$rule_info['platform']}% / 专营店-{$rule_info['dlr']}%
                                </option>
                            <?php endforeach;?>
                        </select>
                    </div>
                </div>
            <?php } ?>
            <div class="form-group" id="spr_id">
                <label class="control-label col-md-2">对账商务项目<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <input type="text" class="form-control width-300" name="business_project" data-toggle="tooltip" value="{$business_project}" placeholder="默认值为商品一级分类名称" maxlength="20" data-parsley-required="true">
                    <span>默认值为商品一级分类名称,可修改</span>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">支付规则:</label>
                <div class="col-md-10">
                    <input type="text" readonly class="form-control width-300" value="{$pay_text}" >
                </div>
            </div>

            <!-- 取送车券判断   -->
            {if condition="$commodity_class == 6"}
            <div class="form-group" id="up_down">
                <label class="control-label col-md-2">判断次数:</label>
                <label class="checkbox-inline">
                    <input type="radio"  id="no_num" name="num" checked>否
                </label>
                <label class="checkbox-inline">
                    <input  type="radio" id="is_num" name="num">是
                </label>
                <div id="time" style="display: none">
                    <div class="col-md-3 p-l-0" style="">
                        <input type="text" name="start_time" id="start_time" placeholder="请输入开始时间" class="form-control datetimepicker3" data-parsley-required="true" >
                    </div>

                    <div class="col-md-3">
                        <input type="text" name="end_time" id="end_time" placeholder="请输入结束时间" class="form-control datetimepicker3" data-parsley-required="true" >
                    </div>
                    <dd style="color:red">*若选择是，需选择开始和结束日期，用于判断此期间，取送车券商品，用户最多可购买数量</dd>
                </div>

            </div>

            <div class="form-group" id="up_down">
                <label class="control-label col-md-2">首单立减:</label>

                <div class="col-md-10">

                    <div class="col-md-3 m-l-0 p-l-0 " >
                        <label class="checkbox-inline">
                            <input type="radio"  id="no_sub" name="sub" checked>否
                        </label>
                        <label class="checkbox-inline">
                            <input  type="radio" id="is_sub" name="sub">是
                        </label>
                    </div>

                    <div class="col-md-2 m-l-0 p-l-0"  id="sub_price" style="display: none">
                        <input type="number" min="0" step="any" class="form-control " name="first_free_price" placeholder="请输入减免金额" value="0" >
                    </div>

                    <dd style="color:red">若选择是，系统会判断是否享受过取送车服务，若无，用户下单时减免设置金额。金额不高于关联取送车商品最低价</dd>

                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">选择电子卡券商品<i class="m-r-3 text-danger"></i>:</label>
                <div class="col-md-10">
                    <div class="col-md-10 m-b-10" style="width: 100%;padding-left:0px;">
                        <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">券包名称:</label>
                        <div class="col-md-3 m-l-0 p-l-0 " >
                            <input type="text" class="form-control " name="qsc_group_name"  title="字数不能超过50个。" maxlength="50">
                        </div>
                        <label class="control-label col-md-2 width-80 p-l-0 m-r-0 text-right">原价:</label>
                        <div class="col-md-3 m-l-0 p-l-0" >
                            <input type="text" class="form-control "  min="0" name="qsc_group_price" placeholder="请输入金额元" value="0" >
                        </div>
                    </div>
                    <div class="col-md-10 m-b-10" style="width: 100%;padding-left:0px;">
                        <label class="control-label col-md-2 width-80 p-l-0  m-r-0 text-left">商品数量:</label>
                        <div class="col-md-3 m-l-0 p-l-0 ">
                            <input type="number" class="form-control" name="qsc_group_num" value="1" placeholder="请输入商品数量" min="1">
                        </div>
                        <div style="float:right;">
                            <label class="control-label col-md-2" style="padding:  5px 5px;  width: 90px;">商品名称:</label>
                            <input type="text" class="form-control input-sm element col-md-3 m-r-2 width-200" name="commodity_name" placeholder="请输入商品名称">
                            <button id="comm-type-search" type="button" class="btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                        </div>

                    </div>
                    <div>
                        <div class="table-scrollable">
                            <table id="" class="table table-hover">
                                <thead>
                                <th class="text-center">商品名称</th>
                                <th class="text-center">商品库存</th>
                                <th class="text-center">商品价格</th>
                                <th class="text-center">操作</th>
                                </thead>
                                <tbody id="add-comm-tbody">

                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ul class="pagination" id="comm-pagination"></ul>
                        </div>
                    </div>

                    <legend class="pull-left width-full" style="font-size: 18px;margin-top: 20px;">已选电子卡券商品</legend>
                    <p style="color: red"></p>

                    <div>
                        <div class="table">
                            <table id="" class="table">
                                <thead></thead>

                                <tbody id="haved-commodtiy">

                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
            {/if}



            <input type="hidden" name="shelves_type" value="{$shelves_type}">
            <div class="text-center" style="padding-top: 10px;position: sticky;bottom: 0px;height:80px;background-color: #fff;line-height: 80px;z-index:999">
                <a type="button" id="bt-submit"  class="btn btn-primary btn-sm m-l-200 submit">提交</a>
            </div>
        </form>
    </div>

    <!-- begin 选择优惠券 -->
    <div class="modal fade" id="card-modal">
        <div class="modal-dialog">
            <div class="modal-content" style="width: 800px;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择优惠券</h4>
                </div>
                <div class="modal-body car-series ">
                    <div class="alert alert-danger m-b-8" style="display: none;">
                        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
                        <p></p>
                    </div>
                    <form class="form search-form">
                        <label>优惠券名称：
                            <input type="text" id="card_name" class="form-control input-sm element width-200" placeholder="请输入优惠券名称" aria-controls="data-table" >
                        </label>
                        <button id="card-search" type="button" class=" btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                    </form>
                    <div class="table-scrollable">
                        <table id="card_form" class="table table-bordered">
                            <thead>
                            <th class="text-center">应用场景</th>
                            <th class="text-center">归属</th>
                            <th class="text-center">优惠券类型</th>
                            <th class="text-center">优惠券id</th>
                            <th class="text-center">优惠券名称</th>
                            <th class="text-center">操作</th>
                            </thead>
                            <tbody id="card-body">
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <ul class="pagination"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="card_add" data-dismiss="modal">确定</a>
                </div>
            </div>
        </div>
    </div>
    <!-- end 选择优惠券-->

    <!-- begin 选择经销商 -->

    <!-- end 选择经销商-->


    <!----begin 删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>确定删除该商品?</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>
    <!----end 删除设置---->

</div>
{/block}
{block name="css"/}

<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />

<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<link href="__STATIC__admin_v2/css/jBootsrapPage.css" rel="stylesheet"/>
<style>
    .nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover{
        background:  #f7fafdf2 !important;
    }

    .info td{
        padding: 3px 12px !important;
    }
    .modal-body.car-series{
        height:400px;
        overflow: auto;
    }
    .sku legend{
        margin-bottom: 10px;

    }
    .editableform .form-group>div {
        padding: 0px!important;
        border:0px !important;
    }
    .editable-click, a.editable-click, a.editable-click:hover{
        border-bottom:none!important;
    }
    .sku .sp_vlaue{
        width: 13%;
    }
    ul, ol {
        list-style: none outside none;
    }
    .sp_name{
        float: left;
    }
    .sku li div{
        margin-left: 50px;
    }
    .form-control.card,.form-control.date-picker{
        width: 300px;
    }
    .form-control.price{
        width: 100px;
        display: inherit;
    }
    .vouher-margin{
        margin-right: 10px;
    }
    .price-margin{
        margin-right: 20px;
    }
    label {
        font-weight: 400;
    }
    .submit{
        width: 100px;
    }
    .commodity-th,.commodity-td{
        text-align: -webkit-center;
    }
    #card-modal {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: -200px;
        z-index: 1050;
        display: none;
        overflow: hidden;
        -webkit-overflow-scrolling: touch;
        outline: 0;
    }
    .logistics{
        float: left;
        margin-right: 20px;
    }
    .width-sku{
        width: 140px!important;
    }
    .coupon_info span{
        display: block;
        margin-right: 15px;
    }
</style>
{/block}
{block name="script"/}
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/js/fileinput.min.js"></script>
<script src="__STATIC__admin_v2/js/fileinput_locale_zh.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js?v=27"></script>
<script type="text/javascript" src="__STATIC__admin_v2/js/clipboard.min.js" ></script>
<script src="__STATIC__admin_v2/js/xm-select.js"></script>
<script>



    $(function(){

        $('.goods_pic>ul').sortable({
            disabled: true,
        });

        var a=[3];
        var b=a;

        var card_checked_id = [];
        var card_checked_name = [];
        /**优惠券使用规则：
         *  1.满减券不可与其他券同时使用
         *  2.平台只能使用平台发布的优惠券
         *  3.专营店端只能使用平台或者自营店的其中一种优惠券
         * @type {boolean}
         */
        var manjian_exist = false;
        var card_selected_type = 0;//0为初始值,1代表平台端,2代表专营店端,3为集团。
        var role = "{$role}";
        var checked_card_info= []; //弹窗已经选中的卡券;
        var select_card_type='';
        $("#card_show").on("keyup",function () {
            // console.log($(this).val())
            if($(this).val().length==0){
                card_checked_id.length=0;
                card_checked_name.length=0;
                manjian_exist = false;
                card_selected_type = 0;
            }
            // console.log("card_selected_type = "+card_selected_type);
            // console.log("card_checked_id = "+card_checked_id);

        });

        var is_num = 0;
        // 设置次数
        $('#is_num').click(function () {
            $("#time").show();
            is_num = 1;

        })
        // 没有次数
        $('#no_num').click(function () {
            $("#time").hide();
            is_num = 0;
            $("input[name^='start_time']").val('');
            $("input[name^='end_time']").val('');
        })

        var is_sub = 0;
        // 设置首单立减金额
        $('#is_sub').click(function () {
            $("#sub_price").show();
            is_sub = 1;
        })
        $('#no_sub').click(function () {
            $("#sub_price").hide();
            is_sub = 0;
        })

        $(".datetimepicker3").datetimepicker({
            format:"YYYY-MM-DD",
            locale: moment.locale('zh-cn'),
        });

        $(".datetimepicker4").datetimepicker({
            format:"YYYY-MM-DD HH:mm:ss",
            locale: moment.locale('zh-cn'),
        });

        $(".multiple-select2").select2({
            placeholder: "请选择"
        })

        function count_contracts_ajax(urlparam){
            $.getJSON(urlparam,null,function(resData){
                createPage(5, 10, resData.data.total,urlparam);//创建翻页功能按钮，翻
                $("tbody#card-body").empty();
                if(resData.data.total > 0){                          //页向后台请求连接
                    list_contracts_ajax(urlparam);
                }else {
                    // layer.msg("没有更多数据");
                    $("#card-modal").modal("show");
                }
            })
        }

        function createPage(pageSize, buttons, total, contracts_url) {        //contracts_url为点击
            $(".pagination").jBootstrapPage({      //功能按钮后需要访问action路径
                pageSize : pageSize,
                total : total,
                maxPageButton:buttons,
                onPageClicked: function(obj, page) {
                    $("tbody#card-body").empty();
                    list_contracts_ajax(contracts_url+"&page="+(page+1));
                }
            });
        }

        function list_contracts_ajax(urlparam){
            var card_info = getCardInfo(select_card_type);
            var selected_card_id =[];
            var checked_card_id =[]
            for (var i=0;i<card_info.length;i++){
                selected_card_id.push(card_info[i].card_id);
            }
            for (var i=0;i<checked_card_info.length;i++){
                checked_card_id.push(checked_card_info[i].card_id);
            }
            $.getJSON(urlparam,null,function(result){
                for(var i = 0;i < result.data.data.length; i++){   //json格式，多行数据的数组
                    var tr = $("<tr></tr>");                      //一行记录
                    tr.appendTo($("tbody#card-body"));
                    var td = $("<td>"+(result.data.data[i].type_name?result.data.data[i].type_name:'')+"</td>");    //应用场景
                    td.appendTo(tr);
                    var td = $("<td>"+(result.data.data[i].belong_to?result.data.data[i].belong_to:'')+"</td>");    //归属
                    td.appendTo(tr);
                    var td = $("<td>"+(result.data.data[i].card_type_name?result.data.data[i].card_type_name:'')+"</td>");    //类型
                    td.appendTo(tr);
                    var td = $("<td>"+(result.data.data[i].id?result.data.data[i].card_id:"")+"</td>");    //优惠券id
                    td.appendTo(tr);
                    var td = $("<td>"+(result.data.data[i].card_name?result.data.data[i].card_name:'')+"</td>");    //优惠券名称
                    td.appendTo(tr);
                    td = $("<td></td>"); //操作
                    // console.log(result.data.data[i].id);
                    if(selected_card_id.indexOf(result.data.data[i].id)!=-1){
                        var option = $("<label>已选择</label>");
                    }else if(checked_card_id.indexOf(result.data.data[i].id)!=-1){
                        var option = $("<label>选择<input class='card_checkbox' type='checkbox' data-id='"+result.data.data[i].id+"' data-type='"+result.data.data[i].card_type+"' data-role='"+result.data.data[i].create_by_role+"' data-title='"+result.data.data[i].card_name+"' checked></label>");

                    }else {
                        var option = $("<label>选择<input class='card_checkbox' type='checkbox' data-id='"+result.data.data[i].id+"' data-type='"+result.data.data[i].card_type+"' data-role='"+result.data.data[i].create_by_role+"' data-title='"+result.data.data[i].card_name+"'></label>");
                    }
                    option.appendTo(td);
                    td.appendTo(tr);
                }
                $("#card-modal").modal("show");
            })
        }


        $("#card_show").on("click",function () {
            count_contracts_ajax("{:url('ajaxGetCard')}?pagesize=5");
        })

        $(".add-card-wx,.add-card-ot").on('click',function (e) {
            checked_card_info = [];  //初始化点击选中优惠券
            if ($(this).hasClass("add-card-wx")){
                select_card_type=1;
            }else {
                select_card_type=2;
            }
            count_contracts_ajax("{:url('ajaxGetCard')}?pagesize=5&select_card_type="+select_card_type);

        })
        $("#card-search").on("click",function () {
            var card_name_like = $("#card_name").val();
            var urlParams = "{:url('ajaxGetCard')}?pagesize=5&card_name="+card_name_like+"&select_card_type="+select_card_type;
            count_contracts_ajax(urlParams);
        })

        //获取已选择的卡券信息
        function getCardInfo(tag) {
            var card_info = [];
            var is_can_receive = 0;
            $("#haved-card-"+tag).find(".card-info").each(function () {
                if($(this).parents('tr').find(".is-can-receive").attr("checked")){
                    is_can_receive = 1;
                }else {
                    is_can_receive = 0;
                }
                card_info.push({card_id:$(this).data("card-id"),card_type:$(this).data("card-type"),card_role:$(this).data("card-role"),is_can_receive:is_can_receive,card_tag:tag});
            })
            // console.log(card_info);
            return card_info;

        }

        $(".card_checkbox").live("click",function () {
            var selected_card_info = getCardInfo(select_card_type);
            //合并下面表格和弹窗选中的优惠券
            for (var i=0;i<checked_card_info.length;i++){
                selected_card_info.push(checked_card_info[i]);
            }
            // console.log(selected_card_info);
            var check = $(this).attr("checked");
            if(check){

                var card_type = $(this).attr("data-type");
                var create_by_role = $(this).attr('data-role');
                //判断规则
                for (var i=0;i<selected_card_info.length;i++){

                    //专营店只能选择要么自营店里的优惠券要么平台的优惠券

                    if (create_by_role != selected_card_info[i].card_role){
                        $(this).removeAttr("checked");
                        layer.msg("平台、专营店、集团优惠券无法同时选择,只能选择其中一种");
                        return;
                    }

                    if ((card_type == 4 && selected_card_info[i].card_type!=4) || (card_type != 4 && selected_card_info[i].card_type==4)){
                        $(this).removeAttr("checked");
                        layer.msg("满减券不可与其他优惠券同时使用");
                        return;
                    }

                }
                checked_card_info.push({card_id:$(this).data("id"),card_type:$(this).data("type"),card_role:$(this).data("role"),card_name:$(this).data("title")})
                // console.log(checked_card_info);

            }else {
                var s_card_info = [];
                for(var i=0;i<checked_card_info.length;i++){
                    if (checked_card_info[i].card_id != $(this).data("id")){
                        s_card_info.push(checked_card_info[i]);
                    }
                }
                checked_card_info = s_card_info;
                // console.log(checked_card_info);
            }

        })

        $("#card_add").on("click",function () {
            var html = '';
            for (var i=0;i<checked_card_info.length;i++){
                html += '<tr class="info"> <td ><a class="card-info" data-card-id="'+checked_card_info[i].card_id+'" data-card-type="'+checked_card_info[i].card_type+'" data-card-role="'+checked_card_info[i].card_role+'">'+checked_card_info[i].card_name+'</a></td>'+
                    '<td class="text-right"> </td> <td class="text-right"> <label class="checkbox-inline m-r-10 p-b-10"> <input type="checkbox" class="is-can-receive" value="1">可领取'+
                    '</label> <button class="btn btn-danger btn-sm m-r-5 m-b-5 del-card">删除</button></td></tr>';
            }
            $("#haved-card-"+select_card_type).append(html);

        })
        //删除卡券
        $("body").on('click','.del-card',function (e) {
            e.preventDefault();
            $(this).parents('tr').remove();

        })


        var dlr_checked_code = [];
        var dlr_checked_name = [];

        $("#comm_dlr_type").on("change",function () {
            if("{$role}"==1){
                //商品类型修改，经销商置空
                $("#dlr_show").val('');
                $("#dlr_hide").val('');
                dlr_checked_code.length = 0;
                dlr_checked_name.length = 0;
            }
        })

        $("#check_all").on("change",function () {
            var check = $(this).attr("checked");
            if(check){
                $.each($(".dlr_checkbox.single"),function (i,ele) {
                    $(ele).attr("checked",true);
                    dlr_checked_code[i]=$(ele).attr("data-id");
                    dlr_checked_name[i]=$(ele).attr("data-title");
                })
            }else {
                $.each($(".dlr_checkbox.single"),function (i,ele) {
                    $(ele).attr("checked",false);
                })
                dlr_checked_code.length = 0;
                dlr_checked_name.length = 0;
            }
        })

        $("#dlr_add").on("click",function () {
            dlr_checked_code.length = 0;
            dlr_checked_name.length = 0;
            $('.dlr_checkbox.single').each(function (i) {
                if($(this).attr("checked")){
                    dlr_checked_code[dlr_checked_code.length]=$(this).attr("data-id");
                    dlr_checked_name[dlr_checked_name.length]=$(this).attr("data-title");
                }
            })
            if(dlr_checked_name.length<=0){
                layer.msg("请选择经销商")
                return;
            }
            var dlr_ids = dlr_checked_code.join(",");
            var dlr_names = dlr_checked_name.join(",");
            $("#dlr_show").val(dlr_names)
            $("#dlr_hide").val(dlr_ids)
        })

        var  dlr_data = {$dlr_list}

            $("#dlr_show").on("click",function () {
                var select_data = $("#dlr_hide").val().split(',');
                Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
                    $("#dlr_show").val(dlr_name.join(','));
                    $("#dlr_hide").val(dlr_code.join(','));
                    // console.log('dlr_code:'+dlr_code);
                    // console.log('dlr_name:'+dlr_name);
                });

                /* var comm_dlr_type_id = $("#comm_dlr_type").val();
                 var commodity_id = "{$info.id}";
                 if(comm_dlr_type_id.length==0){
                 layer.msg('请先选择商品类型');
                 return
                 }else {
                 $.getJSON("{:url('ajaxGetDlr')}",{comm_dlr_type_id:comm_dlr_type_id,commodity_id:commodity_id},function (resData) {
                 if(resData.error==0){
                 $("#dlr_content").empty();
                 var data = resData.data;
                 for (var i = 0;i<data.length;i++){
                 var label = $('<label class="m-r-5 dlr_label" style="width: 30%"></label>');
                 label.appendTo($("#dlr_content"));
                 var input = $('<input type="checkbox" class="dlr_checkbox single min" data-id="'+data[i].dlr_code+'" data-title="'+data[i].dlr_name+'">');
                 input.appendTo(label);
                 label.append(data[i].dlr_name);
                 }

                 }else {
                 layer.msg(resData.msg);
                 }
                 })
                 }*/
            })

        //设置最高价最低价
        function setMaxMinPrice(){
            var price_arr = [];

            $("#spec_default .set-price").each(function () {
                price_arr.push($(this).val());
            })


            $("input[name='original_price_range_start']").val( Math.min.apply(null,price_arr));
            $("input[name='original_price_range_end']").val( Math.max.apply(null,price_arr));
            $("input[name='discount_price_range_start']").val( Math.min.apply(null,price_arr));
            $("input[name='discount_price_range_end']").val( Math.max.apply(null,price_arr));

        }
        //批量设置价格
        $('#all_price').editable({
            success: function(response, newValue) {
                $(".set-price").val(newValue);
                $(".editable-cancel").click();
                allcommission();
                setMaxMinPrice();
                return false;

            },
            validate: function (value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9/.]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });
        //批量设置成本价
        $('#cost_price').editable({
            success: function(response, newValue) {
                $(".cost-price").val(newValue);
                $(".editable-cancel").click();

                return false;

            },
            validate: function (value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9/.]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });


        //批量设置库存
        $('#all_stock').editable({

            success: function(response, newValue) {
                $(".set-stock").val(newValue);
                $(".editable-cancel").click();
                return false;
            },
            validate: function (value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });

        //批量设置分成
        $('#all_divided').editable({

            success: function(response, newValue) {
                $(".set-divided").val(newValue);
                $(".editable-cancel").click();
                allcommission();
                return false;
            },
            validate: function (value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9/.]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });

        //批量设置安卓费
        $('#all_install').editable({
            success: function(response, newValue) {
                $(".set-install").val(newValue);
                $(".editable-cancel").click();
                allcommission();

                return false;

            },
            validate: function (value) { //字段验证
                if (!$.trim(value)) {
                    return '不能为空';
                }
                if (!(/^[0-9/.]*$/).test(value)) {
                    return '格式有误';
                }
            }

        });

//        data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/"

        $("#bt-submit").on("click",function () {
            var validate=$("#add_form").psly().validate();  //表单验证
            if(!validate){
                return false;
            }
            var start_time = $("#start_time").val();
            var end_time = $("#end_time").val();
            if (is_num) {
                if (start_time == '' || end_time == '') {
                    layer.msg('开始时间或结束时间不能为空');
                    return false;
                }
                if (end_time <= start_time) {
                    layer.msg('结束时间应大于开始时间');
                    return false;
                }
            }


            var service_channel = $("[name='service_channel[]']").val();
            if(service_channel && service_channel.length > 6){
                layer.msg('商品服务标签最多只能选择6个');
                return false;
            }
            var up_down_channel_name=getUpDownChannel();
            var sku_list = [];
            var lose_money = false;
            $("#spec_default").find("tr").each(function (i,tr) {
                var commodity_sku_id = $(tr).attr("data-id");
                var default_price = $(tr).find(".default-price").val();
                var default_stock = $(tr).find(".default-stock").val();
                var set_divided   = $(tr).find('.set-divided').val();
                var set_install   = $(tr).find('.set-install').val();
                var cost_price   = $(tr).find('.cost-price').val();
                var sp_value_list   = $(tr).find('.sp_value_list').val();
                var commodity_id   = $(tr).find('.commodity_id').val();
                var sku_code   = $(tr).find('.sku_code').val();
                var sku = new Object();
                if(parseFloat(default_price) < parseFloat(cost_price)){
                    lose_money = true;
                }
                sku['sp_value_list'] = sp_value_list;
                sku['sku_code'] = sku_code;
                sku['commodity_id'] = commodity_id;
                sku['commodity_sku_id'] = commodity_sku_id;
                sku['price'] = default_price;
                sku['stock'] = default_stock;
                sku['divided_into']      = set_divided ? set_divided :0;
                sku['install_fee']       = set_install ? set_install :0;
                sku['cost_price']       = cost_price ? cost_price :0;
                sku_list[sku_list.length] = sku;
            })
            var sku_list_str = JSON.stringify(sku_list);
            $("input[name='sku_list'").val(sku_list_str);
            $("#has_use_card_mj").val(manjian_exist==true?1:0);
            var wx_card = getCardInfo(1);
            var ot_card = getCardInfo(2);
            var dlr_label = $("input[name='dlr_label']:checked").val();
            if(!dlr_label){
                dlr_label = 0;
            }

            //todo
            //alert($("input[name='is_store']").is(':checked'));
            //return false;
            var qsc_group_name = $("input[name='qsc_group_name']").val();
            console.log(qsc_group_name);
            var qsc_group = commodity_set_id.toString();
            if (qsc_group != '' && qsc_group_name == '') {
                layer.msg('取送车卷包名称不能为空');
                return ;
            }
            var shelves_type = $("input[name='shelves_type'").val();
            var home_type_data = '';
            if(shelves_type == 5 || shelves_type == 6 || shelves_type == 7){
                var home_type = home_type_class.getValue('value');
                if(home_type.length !== 0){
                    home_type_data = home_type.join()
                }
            }
            var sku_list = [];

            // 活动图片
            var activity_image=[];
            $(".activity_image_group .goods_pic").each(function(){
                $(this).find('li img').each(function(){
                    activity_image.push($(this).attr("image-value"));
                });
            });
            if(activity_image.length !== 0){
                var activity_start_time = $('input[name = activity_start_time]').val()
                if(activity_start_time == ''){
                    layer.msg('活动头图生效开始时间不能为空',{icon:2})
                    return;
                }
                var activity_end_time = $('input[name = activity_end_time]').val()
                if(activity_end_time == ''){
                    layer.msg('活动头图生效结束时间不能为空',{icon:2})
                    return;
                }
            }


            var dd_commodity_type = "{$info['dd_commodity_type']}"
            if(dd_commodity_type == 1 || dd_commodity_type == 3 || dd_commodity_type == 4){
                var data  =$.param({wx_card:wx_card,ot_card:ot_card,'up_down_channel_name':up_down_channel_name, dlr_label: dlr_label,
                    is_num:is_num,first_is_sub:is_sub,qsc_group:qsc_group,home_type_data:home_type_data,"activity_image":activity_image})+ '&' + $("#add_form").serialize();
            }else{
                var data  =$.param({wx_card:wx_card,ot_card:ot_card,'up_down_channel_name':up_down_channel_name, dlr_label: dlr_label,
                    is_num:is_num,first_is_sub:is_sub,qsc_group:qsc_group,home_type_data:home_type_data,"activity_image":activity_image
                }) + '&' + $("#add_form").serialize();
            }
            var alert_obj = $("div.alert-danger");
            if(lose_money){
                var cost_price_is =  layer.msg("有sku销售价小于成本价，若确认上架，将邮件通知运营主管，是否仍要操作上架？",{
                    icon: 0
                    ,time:false
                    ,btn: ['确认','取消'] //按钮
                    ,yes:function(){
                        layer.close(cost_price_is);
                        Custom.ajaxPost("{:url('save')}",data+"&send_email=1",alert_obj,"{:url('commodity/index',['is_platform'=>$is_platform])}",function (res) {
                            if (res.error==0){
                                layer.msg(res.msg);
                                window.location.href = document.referrer;
                                //window.history.go(-1);
                            }else{
                                layer.msg(res.msg);
                            }

                        });
                        return true;
                    },btn2:function(){
                        layer.close(cost_price_is);
                        return false;
                    }
                })
            }else{
                Custom.ajaxPost("{:url('save')}",data,alert_obj,"{:url('commodity/index',['is_platform'=>$is_platform])}",function (res) {
                    if (res.error==0){
                        layer.msg(res.msg);
                        window.location.href = document.referrer;
                        //window.history.go(-1);
                    }else{
                        layer.msg(res.msg);
                    }
                });
            }
        })

        //算分佣比例
        $(".set-divided,.set-install,.set-price").on('change',function (e) {
            var prents_obj= $(this).parents('tr');
            var price     = checkPrice(prents_obj.find('.set-price').val());
            var divided   = checkPrice(prents_obj.find('.set-divided').val());
            var install   = checkPrice(prents_obj.find('.set-install').val());
            if (price===false) {
                layer.msg('价格格式不对')
                return;
            }
            if (divided===false) {
                layer.msg('分成格式不对')
                return;
            }
            if (install===false) {
                layer.msg('安装费格式不对')
                return;
            }
            var commission = (price-install)*(divided/100)+install;
            // alert(commission);
            prents_obj.find('.commission').html(Math.round(commission*100)/100);
            setMaxMinPrice();


        })

        //批量设置分佣
        function allcommission() {
            $("#spec_default tr").each(function () {
                var price     = checkPrice($(this).find('.set-price').val());
                var divided   = checkPrice($(this).find('.set-divided').val());
                var install   = checkPrice($(this).find('.set-install').val());
                if (price===false) {
                    layer.msg('价格格式不对')
                    return;
                }
                if (divided===false) {
                    layer.msg('分成格式不对')
                    return;
                }
                if (install===false) {
                    layer.msg('安装费格式不对')
                    return;
                }

                var commission = (price-install)*(divided/100)+install;
                // alert(commission);
                $(this).find('.commission').html(Math.round(commission*100)/100);
            })
        }

        function  checkPrice(price) {
            if (!price || price==0) return 0
            var reg=/^[0-9]+([.]{1}[0-9]{1,2})?$/
            if(!reg.test(price)) {
                return false;
            }else {
                return parseFloat(price);
            }

        }

        function checkInt(divided) {
            if (!divided || divided==0) return 0

            var reg=/^\+?[1-9][0-9]*$/;

            if (!reg.test(divided)){
                return false;
            }else {
                return  parseInt(divided);
            }
        }


        $("body").on('click','#GWDLR',function(){
            if($("#GWDLR").is(':checked')){
                $('#dealer_select').removeClass('hidden');
            }else{
                $('#dealer_select').addClass('hidden');
            }
        });

        $("body").on("click",'#dlr_show',function () {
            var select_data = $("#dlr_hide").val().split(',');
            Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
                $("#dlr_show").val(dlr_name.join(','));
                $("#dlr_hide").val(dlr_code.join(','));
            });
        })

        $("body").on("click",'#id_is_store',function () {
            if($(this).is(':checked')){
                $(".sbt_div").removeClass("hidden");
            }else{
                $(".sbt_div").addClass("hidden");
            }
        })

        var image_sp_value_id = '';
        $('.activity_image_group').on('click', '.btn-image', function () {
            image_sp_value_id = $(this).parents('.goods_pic').data('image-id')
            var image_coutn = $(this).parents('.goods_pic').find('li').length;
            if (image_coutn >= 1) {
                layer.msg('图片最多能上传1张');
                return;
            }
            $("#activity-image-input").click();

        });
        $(".activity_image_group").on('click', "del", function () {
            $(this).parents('li').remove();
            $(".activity_img_time").css('display','none')
        });
        //修改图片
        var on_span = 0;
        $(".activity_image_group").on('click', "span", function () {
            $(this).parents('li').addClass('on_span');
            on_span = 1;
            $("#activity-image-input").click();
        });

        $(".activity_image_group").on('change', "#activity-image-input", function (e) {

            /**
             * begin
             *
             * 此处有修改
             * 修改人：吴炜文
             * 时间2017.10.20 16:00
             * 修改内容：上传图片大小、格式做限制
             */
            var files = this.files;
            var cc_rr = check_file(files);
            if (cc_rr == 1) {
                return;
            }
            /**
             * end
             */

            Custom.ajaxFileUpload('activity-image-input', {file_path: 'commodity_activity'}, function (res) {
                if (res.error == 0) {
                    $(".activity_img_time").css('display','block')
                    var data = res.data;
                    if (on_span == 1) {
                        var html = '<img image-value="' + data.image + '" src="' + data.upload_url + data.image + '"> <del></del><span>修改</span> ';
                        $('.on_span').html(html);
                        $('.on_span').removeClass('on_span');
                        on_span = 0;
                    } else {
                        var html = '<li class="move-item"> <img image-value="' + data.image + '" src="' + data.upload_url + data.image + '"> <del></del><span>修改</span> </li>';
                        $("#image-id-" + image_sp_value_id).find('ul').append(html);
                    }
                } else {
                    layer.msg(res.msg);
                }
                $("#activity-image-input").val("");
                image_sp_value_id = '';
            })
        });

    });

    //选择快递必填运费模板
    function express(check){
        if(check.checked){
            $("#default_name").val('')
        }else{
            $("#default_name").val(0)
        }
    }

    $("[name='mail_type']").on('change', function () {
        var mail_type = $(this).val();
        // console.log(mail_type)
        var sbt_div = $(".sbt_div");
        var ks_md = $(".ks_md");
        var template_guid = $("[name='template_guid']");
        var default_name = $("#default_name");
        sbt_div.addClass("hidden");
        ks_md.addClass("hidden");
        template_guid.removeAttr("disabled", "disabled");
        $('#mail_show_word').removeAttr('required');
        switch (mail_type){
            case '1': sbt_div.removeClass("hidden");template_guid.attr("disabled", "disabled");ks_md.removeClass("hidden");break;
            case '3': sbt_div.removeClass("hidden");ks_md.removeClass("hidden");break;
            case '2': ks_md.addClass("hidden");break;
            case '4': template_guid.attr("disabled", "disabled");$('#mail_show_word').attr('required', 'required');break;
        }
        if(mail_type == 2){
            $("#listing_type_hidden").attr('disabled',false)
            $("#listing_type").css('display','block');
        }else{
            $("#listing_type_hidden").attr('disabled',true)
            $("#listing_type").css('display','none');
            $.each($("[name='listing_type']"),function(k,v){
                if(k == 0){
                    $(v).prop("checked", true)
                }else{
                    $(v).prop("checked", false)
                }
            })
        }
    });

    var ajaxCommUrl = "{:url('ajaxGetCommodityList',['pagesize'=>10])}";

    var up_down_channel = [];
    // 复选框点击事件
    $(".up_down_channel").click(function(){
        let channel = $(this).attr('id');
        if($(this).is(':checked')) {
            console.log('push:',channel)
            up_down_channel.push(channel);
        } else {
            console.log('pop:',channel)
            up_down_channel.splice($.inArray(channel,up_down_channel),1);
        }
        var commodity_class = "{$commodity_class}";
        if (commodity_class == 6) {
            var up_down_channel_str = up_down_channel.toString();
            if (up_down_channel_str == '') {
                $("#add-comm-tbody").html('');
                $("#comm-pagination").html('');
                return ;
            }
            ajaxCommUrl = "{:url('ajaxGetCommodityList',['pagesize'=>10])}";
            ajaxCommUrl = ajaxCommUrl + '&up_down_channel_str='+ up_down_channel_str;
            getCommodityList();
        }

    });

    $("#comm-type-search").on('click',function(){
        var commodity_name = $("input[name='commodity_name']").val();
        ajaxCommUrl = "{:url('ajaxGetCommodityList',['pagesize'=>10])}";
        ajaxCommUrl = ajaxCommUrl + '&commodity_name='+commodity_name;
        getCommodityList();
    });


    function getCommodityList() {
            $.getJSON(ajaxCommUrl, null, function (resData) {

                createPageComm(10, 10, resData.data.total, ajaxCommUrl);//创建翻页功能按钮，翻

                if (resData.data.total >0) {
                    setComm(ajaxCommUrl);
                }
            });
    }


    // 分页
    function createPageComm(pageSize, buttons, total, url) {        //contracts_url为点击
        $("#comm-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
            pageSize : pageSize,
            total : total,
            maxPageButton:buttons,
            onPageClicked: function(obj, page) {    //分页事件
                $("#add-comm-tbody").empty();
                setComm(url+"&page="+(page+1));
            }
        });
    }


    function setComm(param) {
        var url = ajaxCommUrl;
        if(param!=null) url=param;
        $.get(url,function(res){
            var html='';
            var list = res.data.data;
            //当前分页条数
            var per_page = res.data.per_page;


            $.each(list,function(i,val){
                var button='<button data-per-page="'+per_page+'"  data-commodity_name="'+val.commodity_name+'" data-count_stock="'+val.count_stock+'" ' +
                    ' data-final_price="'+val.final_price+'" data-flat_id="'+val.id+'"   data-commodity_set_id="'+val.commodity_set_id+'"   class=" btn btn-white btn-sm  btn-fight">选择添加</button>';


                html += '<tr id="comm_tab_tr_">' +
                    '<td class="text-center">'+ val.commodity_name +'</td>' +
                    '<td class="text-center">'+ val.count_stock +'</td>' +
                    '<td class="text-center">'+ val.final_price +'</td>' +
                    '<td class="text-center">'+  button +
                    '</td> </tr>';
            });
            $("#add-comm-tbody").html(html);

        },'json');

    }

    let commodity_set_id = [];

    $("#add-comm-tbody").on('click',".btn-fight",function (e) {
        e.preventDefault();
        let nuom = $('#haved-commodtiy').find('tr').length + 1;
        if (nuom > 2) {
            layer.msg('最多关联2个卡券');
            return;
        }

        commodity_set_id.push($(this).attr('data-commodity_set_id'))
        console.log(commodity_set_id);
        var commodity_list =new Object();
        commodity_list.commodity_name    = $(this).attr('data-commodity_name');
        commodity_list.count_stock       = $(this).attr('data-count_stock');
        commodity_list.final_price       = $(this).attr('data-final_price');
        commodity_list.flat_id           = $(this).attr('data-flat_id');
        commodity_list.commodity_set_id  = $(this).attr('data-commodity_set_id');
        // console.log(commodity_list);

        var html='<tr class="info " data>' +
            '<td style="width: 350px;">' +
            ''+commodity_list.commodity_name+'/'+ commodity_list.final_price + '元/' + commodity_list.count_stock +

            '</td>' +
            '<td class="text-right">'+

            '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list"  data-commodity_set_id="'+commodity_list.commodity_set_id+'"  data-flat_id="'+commodity_list.flat_id+'"  data-comm_name="'+commodity_list.commodity_name+'">删除</button>' +
            '</td>' +
            '</tr>';
        $("#haved-commodtiy").append(html);
        $(this).removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');

    });


    $("body").on("click",".del-sku-list",function (e) {
        e.preventDefault();
        var comm_name = $(this).data('comm_name');
        var comm_set_id =  $(this).attr('data-commodity_set_id');

        $("#del-sku-modal").find("#del-data-id").val(comm_set_id);
        $("#del-sku-modal").find(".alert").html('<i class="fa fa-info-circle"></i>您选择对商品 '+comm_name+' 进行移除操作吗？');
        $("#del-sku-modal").modal('show');
    })


    $("#del-confirm").on("click",function () {
        var comm_set_id = $("#del-sku-modal").find("#del-data-id").val();
        console.log('要删除的数据：', comm_set_id)
        commodity_set_id.splice($.inArray(comm_set_id,commodity_set_id),1);
        console.log(commodity_set_id);
        $("#haved-commodtiy").find("[data-commodity_set_id='"+comm_set_id+"']").html('去掉').parents('tr').remove();
        $("#del-sku-modal").modal('hide');
        var btn_obj=$('#add-comm-tbody').find("[data-commodity_set_id='"+comm_set_id+"']");
        btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');


    })

    //弹框显示套餐编码
    function exhibition(obj,data)
    {
        var index = layer.open({
            type:0,
            title:['查看套餐编码'],
            content:data,
            btn:['复制','取消'],
            yes:function(index,layero){
                copyText(selectText(obj),index);
                layer.close(index)
                // navigator.clipboard.writeText(obj.html());
            },
            btn2:function (index,layero){
                layer.close(index)
            }
        })
        // console.log(data)
    }


    function selectText(element){
        return element.innerText;
    }

    function copyText(text,obj){
        var textValue = document.createElement('textarea');
        textValue.setAttribute('readonly', 'readonly'); //设置只读属性防止手机上弹出软键盘
        textValue.value = text;
        document.body.appendChild(textValue); //将textarea添加为body子元素
        textValue.select();
        var res = document.execCommand('copy');
        document.body.removeChild(textValue);//移除DOM元素
        layer.msg("成功复制到粘贴板。")
        return res;
    }

    $("[name='listing_type']").on('change', function () {
        var listing_type = $(this).val();
        if(listing_type == 2){
            var arr = ['GWNET','GWDLR']
            $.each($("[name='up_down_channel[]']"),function(res,e){
                if($.inArray($(e).val(),arr) != -1){
                    $(e).attr('disabled','disabled')
                    $(e).prop("checked", false)
                    $('#dealer_select').addClass('hidden')
                    $('#dlr_hide').val('')
                }
            })
        }else{
            $.each($("[name='up_down_channel[]']"),function(res,e){
                $(e).removeAttr('disabled')
            })
        }
    });

</script>

<script>

    var user_data = [];
    $.ajaxSettings.async = false

    $.get('get_home_type_class?commodity_id=' + $("input[name='commodity_id'").val() + '&shelves_type='+{$shelves_type},function (res){
        var array = JSON.parse(res)
        // console.log(array)
        var type_id = array.data.type_id
        $.each(array.data.data,function (i,item){
            user_data[i] = [];
            user_data[i]['name'] = item.class_name
            user_data[i]['value'] = item.id
            var children = [];
            $.each(item.subclass,function (ii,item1){
                children[ii] = [];
                children[ii]['name'] = item1.class_name
                children[ii]['value'] = item1.id
                var children_three = []
                $.each(item1.subclass,function (iii,item2){
                    children_three[iii] = [];
                    children_three[iii]['name'] = item2.class_name
                    children_three[iii]['value'] = item2.id
                    if(type_id.indexOf(item2.id) != -1){
                        children_three[iii]['selected'] = true
                    }
                })
                children[ii]['children'] = children_three
            })
            user_data[i]['children'] = children
        })
    })

    var home_type_class = xmSelect.render({
        el: '#home_type_class',
        autoRow: true,
        cascader: {
            show: true,
            indent: 200,
        },
        height: '200px',
        data(){
            return  user_data;
        }
    })


</script>
{/block}
