{extend name="public:base_layout" /}

{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<link href="__STATIC__admin_v2/css/jBootsrapPage.css" rel="stylesheet"/>
<style>
    .form-group dd{
        margin-top: 5px;
    }
    .cover-image{
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-left: 20px;
    }
    .modal-body tr th{
        text-align: center;
    }
</style>
{/block}

{block name="content"/}

<div class="panel-body">

    <div class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">编辑-预售活动</legend>
        <form id="fight-form" class="form-horizontal form-bordered" data-parsley-trigger="change">

            <input type="hidden" name="acstatus" value="{$row['act_status']}" id="act_status" >

            <div class="form-group">
                <label class="control-label col-md-2">活动名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" value="{$row['title']}" name="title" placeholder="请输入活动名称" class="form-control" data-parsley-required="true" data-parsley-length="[1, 15]">
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">促销标签:</label>
                <div class="col-md-5" style="width: 70%;"><input type="text" maxlength="4"  name="tag" value="{$row.tag}" style="width:200px;background-color: #fff;background-image: none;
    border: 1px solid #ccc;border-radius: 4px;box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset; color: #555;font-size: 14px;height: 34px;line-height: 1.42857;padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;border-radius: 3px;box-shadow: none; font-size: 12px;" {eq name="$row.is_enable" value="1"} {if(in_array($row.act_status, [3]))} disabled {/if}{/eq} />
                    (促销标签是商家对促销动作的别名操作，用于前台显示，最多可输入4个字符，非必填选项）
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">活动规则<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <textarea class="form-control" id="dec" name="dec" rows="4" maxlength="1000" data-parsley-required="true">{$row['dec']}</textarea>
                    <dd>少于1000字，若无设置，则不显示</dd>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">定金膨胀<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <div class="col-sm-1" style="padding: 0">
                        <label class="control-label">定金</label>
                    </div>
                    <div class="col-sm-4" style="padding: 0">
                        <input class="form-control" data-parsley-required="true" onkeyup="value=value.replace(/[^\d.]/g,'')" value="{$row['front_money']}" name="deposit" type="text">
                    </div>
                    <div class="col-sm-1" style="padding-left: 8px">
                        <label class="control-label">元</label>
                    </div>
                    <div class="col-sm-1" style="padding-left: 8px;padding-right: 0">
                        <label class="control-label">抵扣</label>
                    </div>
                    <div class="col-sm-4" style="padding: 0">
                        <input class="form-control" data-parsley-required="true" onkeyup="value=value.replace(/[^\d.]/g,'')" value="{$row['dedu_money']}" name="deduction" type="text">
                    </div>
                    <div class="col-sm-1" style="padding-left: 8px">
                        <label class="control-label">元</label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">定金支付时间<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-3 p-l-0" style="">
                        <input type="text" data-parsley-required="true" <?php if(date("Y-m-d H:i:s",time()) > $row['front_s_time']) echo 'disabled'; ?>  value="{$row['front_s_time']}" name="deposit_start_time" id="deposit_start_time" placeholder="请输入开始时间" class="form-control datetimepicker3"  >
                    </div>

                    <div class="col-md-3">
                        <input type="text" data-parsley-required="true" <?php if(date("Y-m-d H:i:s",time()) > $row['front_s_time']) echo 'disabled'; ?> value="{$row['front_e_time']}" name="deposit_end_time" id="deposit_end_time" placeholder="请输入结束时间" class="form-control datetimepicker3"  >
                    </div>
                    <div class="col-md-4" style="color: #1296DB">
                        活动开始后不可修改，请慎重选择！
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">尾款支付时间<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-3 p-l-0" style="">
                        <input type="text" data-parsley-required="true" <?php if(date("Y-m-d H:i:s",time()) > $row['front_s_time']) echo 'disabled'; ?> value="{$row['balance_s_time']}" name="balance_start_time" id="balance_start_time" placeholder="请输入开始时间" class="form-control datetimepicker3"  >
                    </div>
                    <div class="col-md-3">
                        <input type="text" data-parsley-required="true" <?php if(date("Y-m-d H:i:s",time())  > $row['front_s_time']) echo 'disabled'; ?> value="{$row['balance_e_time']}" name="balance_end_time" id="balance_end_time" placeholder="请输入结束时间" class="form-control datetimepicker3"  >
                    </div>
                    <div class="col-md-4" style="color: #1296DB">
                        活动开始后不可修改，请慎重选择！
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">限购数量:</label>
                <div  class="col-md-5" id="purchase_number">
                    <div class="input-group col-md-12" >
                        <input type="text" data-parsley-errors-container="#purchase_number" value="{$row['purchase_number']}" name="purchase_number" class="form-control  col-md-3"   data-parsley-type="integer"  data-parsley-min="0"  >
                        <span class="input-group-addon" >件／人</span>
                    </div>
                    <dd>如果设置为0或不设置则被视为不限制购买数量</dd>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否叠加券:</label>
                <div  class="col-md-5" id="card_available">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_card_change" type="checkbox"  name="switch" data-input-name="card_available"
                                   data-input-true="1" data-input-false="0"
                                   data-size="small" data-on-text="可用" data-off-text="不可用"
                                <?php if($row['card_available']==1) echo 'checked';?> />
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group no_card ">
                <label class="col-md-2 control-label" id="card_activity">选择可叠加的券:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control card_activity" name="card_activity" placeholder="点击选择" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="{$row['commodity_card_name']}">
                    <input type="hidden" class="form-control hide_card_ids" name="rel_card_ids" value="{$row['rel_card_ids']}">
                </div>
                <div class="bottom-wp">
                    <div class="bottom-left">
                        已选<input type="" class="num_card_ids" style="width: 20px;outline:none;border: 0; readonly" value="{$row['num_card']}">
                        张
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">业务归属:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <select name="gather_id" class="form-control input-sm ">
                            <option value=""> 全部 </option>
                            {volist name="$gather_list" id="val" key="key"}
                            <option value="{$val['id']}" {if($row['gather_id'] == $val['id'])}selected{/if} > {$val['name']} </option>
                            {/volist}
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">主题活动:</label>
                <div  class="col-md-5" id="is_enable">
                    <input type="text" name="theme_name" id="theme_name" placeholder="请输入主题活动" value="{$row['theme_name']}" class="form-control" data-parsley-length="[0, 100]">
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否PV补贴:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_pv_subsidy" type="checkbox" data-input-name="is_pv_subsidy" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭"  {eq name="row.is_enable" value="1"} {if(in_array($row.act_status, [2]))} disabled {/if}{/eq}
                            <?php if($row['is_pv_subsidy']==1) echo 'checked';?> />
                            {if(in_array($row.act_status, [3]))}
                            <input type="hidden" name="is_pv_subsidy" value="{$row.is_pv_subsidy}">
                            {/if}
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group ">
                <label class="col-md-2 control-label" id="e3s_activity">关联E3S活动:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control e3s_activity" name="e3s_activity" placeholder="请选择活动" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="{if (!empty($row['e3s_activity_id']))}{$row['e3s_activity_id']} | {$row['e3s_activity_name']}{/if}">
                    <input type="hidden" id="e3s_activity_id" name="e3s_activity_id" value="{$row['e3s_activity_id']}">
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label" id="activity_type">活动设置类型:</label>
                <div class="col-md-6">
                    <select class="form-control width-300" name="activity_type" >
                        <option value="0">请选择</option>
                        <option value="1" {if($row['activity_type'] == 1)}selected{/if}>备件</option>
                        <option value="2" {if($row['activity_type'] == 2)}selected{/if}>工时</option>
                        <option value="3" {if($row['activity_type'] == 3)}selected{/if}>赠品</option>
                    </select>
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label" id="settlement_rule">关联结算规则:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control settlement_rule" name="settlement_rule" id="template_name" placeholder="请选择选择结算规则" data-parsley-group="wizard-step-1"  value="{if ($row['settlement_rule_id'] > 0)}{$row['settlement_rule_name']}{/if}">
                    <input type="hidden" id="settlement_rule_id" name="settlement_rule_id" value="{$row['settlement_rule_id']}">
                    <input type="hidden" id="settlement_rule_value" name="settlement_rule_value" value="{$row['settlement_rule_value']}">
                    <input type="hidden" id="settlement_rule_type" name="settlement_rule_type" value="{$row['settlement_rule_type']}">
                    <input type="hidden" id="act_sett_standard" name="act_sett_standard" value="{$row['act_sett_standard']}">
                </div>
            </div>

            <div class="form-group" id="up_down">
                <label class="control-label col-md-2">活动渠道:<i class="m-r-3 text-danger">*</i></label>
                <div class="col-md-10">
                    <div>
                        <?php $row_code = explode(',', $row['up_down_channel_dlr']);
                        foreach ($up_down_channel as $key => $val):
                            if($live_type == 1): if (in_array($key,['PZ1AAPP','PZ1ASM'])):?>
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                        <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endif; else: ?>
                                <?php if($live_type == 2): if (in_array($key,['QCAPP','QCSM'])):?>
                                    <label class="checkbox-inline">
                                        <input type="checkbox"
                                               name="up_down_channel[]" data-parsley-required="true"
                                            <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                               value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                    </label>
                            <?php endif; else: if (!in_array($key,['PZ1AAPP','PZ1ASM','TOBPC'])):?>
                                <label class="checkbox-inline">
                                    <input type="checkbox"
                                           name="up_down_channel[]" data-parsley-required="true"
                                        <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                           value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                                </label>
                            <?php endif; endif; endif; endforeach; ?>
                    </div>
                    <div style="margin-top:20px;" class="<?= $dlr_hidden ?>" id="dealer_select">
                        <input id="dlr_show" type="text" class="form-control width-300" placeholder="请点击选择经销商"
                               data-parsley-required="true" value="<?=$dlr_str?>">
                        <input id="dlr_hide" type="text" name="dlr_code" class="hidden"
                               value="<?=$row['up_down_channel_dlr']?>">
                    </div>

                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">活动状态:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_enable" type="checkbox" data-input-name="is_enable" data-input-true="1" data-input-false="0"
                                   data-size="small" data-on-text="开启" data-off-text="关闭"
                                <?php if($row['is_enable']==1) echo 'checked';?> />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group" id="coupon" style="display: none">
                <label class="control-label col-md-2">优惠券:</label>
                <div class="col-md-5">
                    <input type="text" class="form-control" id="coupon_name" placeholder="选择优惠券" name="coupon_name" value="">
                    <input type="hidden" class="form-control" id="coupon_id" name="coupon_id" value="">
                </div>
            </div>
            <input type="hidden" name="id" value="{$row['id']}">

            <div class="form-group">
                <label class="control-label col-md-2">活动商品<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left;width:60%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品分类:</label>
                            <select class="form-control input-sm element default-select2 col-md-3" name="comm_parent_id" id="slt_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($comm_parent_list as $key=>$val):?>
                                    <option value="{$val['id']}" >{$val['comm_type_name']}</option>
                                <?php endforeach;?>
                            </select>
                            <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                            <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id" style="width: 25%">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                        </div>
                        <div style="float:left;width:30%">
                            <label class="control-label col-md-2" style="padding:  5px 5px;  width: auto;">商品名称:</label>
                            <input type="text" class="form-control input-sm element col-md-3 m-r-2 " style=" width: 78%"
                                   name="commodity_name" placeholder="请输入商品名称">
                        </div>
                    </div>
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left;width:60%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品种类:</label>
                            <select class="form-control input-sm element default-select2 col-md-3 comm-type-search2"
                                    name="commodity_class" onchange="commTypeSearch2()"  id="slt_comm_type_id" style="width: 76%">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($commodity_class as $key=>$val):?>
                                    <option value="{$key}" >{$val}</option>
                                <?php endforeach;?>
                            </select>
                        </div>

                        <div   id="btn-sm-div" style="float:left;width:40%">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品类型:</label>
                            <select class="form-control input-sm element default-select2 col-md-3 comm-type-search2"
                                    name="commodity_dlr_type_id" onchange="commTypeSearch2()" id="slt_comm_type_id" style="width: 58%">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($comm_dlr_type_list as $key=>$val):?>
                                    <option value="{$val['id']}" >{$val['inner_name']}</option>
                                <?php endforeach;?>
                            </select>
                            <button id="comm-type-search" type="button" class="btn btn-sm btn-success comm-type-search2"><i class="fa fa-search"></i>搜索</button>
                        </div>
                    </div>
                    <div>
                        <div class="table-scrollable">
                            <table id="" class="table table-hover">
                                <thead>
                                <th class="text-center">商品规格</th>
                                <th class="text-center">上架渠道</th>
                                <th class="text-center">原价</th>
                                <th class="text-center">库存</th>
                                <th class="text-center">
                                    <a
                                            data-current-page="1"
                                            data-is-sku-confirm-all="1"
                                            data-comm-id="all"
                                            home="all"
                                            data-comm-set-id="all"
                                            commodity_class=""
                                            data-dlr-code="all"
                                            class="btn btn-sm btn-default btn-white"
                                            id="sku-confirm-all"
                                    >批量添加</a>
                                </th>
                                </thead>
                                <tbody id="add-comm-tbody">

                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ul class="pagination" id="comm-paginations"></ul>
                        </div>
                    </div>

                    <legend class="pull-left width-full" style="font-size: 18px;">
                        <div class="col-md-10">
                            已选活动商品
                        </div>
                        <div class="col-md-2">
                            <a class="btn btn-danger btn-sm m-r-5 m-b-5 del-commodity">删除全部商品</a>
                        </div>
                    </legend>

                    <div>
                        <div class="table">
                            <table id="commodity_select" class="table">
                                <thead></thead>

                                <tbody id="haved-commodtiy">
                                <?php foreach($row_comm as $key=>$val):?>
                                    <tr class="info" data="">
                                        <td style="width: 350px;"><img class="cover-image" src="{$val['cover_image']}"><a class="init-commodity-preview" data-dlr-code="{$dlr_code}" data-commodity-id="{$val['commodity_id']}">{$val['commodity_name']}</a></td>
                                        <td class="text-right"><button data-sku-list='{$val["sku_list"]}' class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button><button class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button>
                                        </td></tr>
                                <?php endforeach;?>
                                </tbody>
                            </table>
                            <div id="pagiDiv" style="width:100%;text-align: center;display: none">
                                <span id="spanFirst">首页</span>
                                <span id="spanPre">上一页</span>
                                <span id="spanNext">下一页</span>
                                <span id="spanLast">尾页</span>
                                第 <span id="spanPageNum"></span> 页/共 <span id="spanTotalPage"></span> 页
                            </div>
                        </div>

                    </div>

                </div>
            </div>


            <div class="form-group">
                <label class="control-label col-md-2"></label>
                <div class="col-md-5">
                    <input type="hidden" name="set_type" value="{$set_type}">
                    <?php if($row['act_status']!=3) {?>
                    <a href="javascript:;" class="btn btn-sm btn-primary btn-sm"  id="put-form">确认</a>
                    <?php }?>
                </div>
            </div>

        </form>

    </div>

    <!--  优惠券列表  -->
    <div class="modal" id="coupon_list">
        <div class="modal-dialog" style="width: 800px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择优惠券</h4>
                </div>
                <div class="modal-body" style="">
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>优惠券名称</th>
                            <th>优惠劵类型</th>
                            <th>有效期</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody id="coupon-pagination"></tbody>
                    </table>
                    <div>
                        <ul class="pagination" id="comm-pagination"></ul>
                    </div>
                </div>

                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal">关闭</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary" id="coupon-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 查看规格---->
    <div class="modal fade" id="sku-modal" data-per-page="" data-comm-id="" data-comm-set-id="" data-type="">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择商品: <span id="comm-name-header"></span></h4>
                </div>
                <input id="modal_home"  type="hidden" value="" />
                <input id="modal_commodity_class"  type="hidden" value="" />
                <input id="commodity_dlr_type_id"  type="hidden" value="" />

                <div class="modal-body" style="">
                    <div class="hide">
                        <div class="sku-image" style="display: inline-block;">
                            <img class=" cover-image" src="">
                        </div>
                        <div style="display: inline-block;">
                            <div  class="sku-comm">
                            </div>

                        </div>
                    </div>

                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th class="col-md-6" >商品规格</th>
                            <th >原价</th>
                            <th >尾款</th>
                            <th>库存</th>
                        </tr>
                        </thead>

                        <tbody class="sku-tb" id="change_sku_tb">

                        </tbody>
                    </table>
                </div>

                <div class="modal-footer">

                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >关闭</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary  " id="sku-confirm" >确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>确定删除该商品?</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>


</div>
{/block}
{block name="script"/}


<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js"></script>

<script>
    $(".default-select2").select2({width:'150px'});
    $(".datetimepicker3").datetimepicker({
        format:"YYYY-MM-DD HH:mm:00",
        locale: moment.locale('zh-cn'),
    })

    $("#slt_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">选择</option>';
            $("#slt_three_comm_type_id").html(html);
            if(comm_parent_id == 0){
                $("#slt_sub_comm_type_id").html(html);return;
            }
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_sub_comm_type_id").html(html);
        },'json');

    }) ;

    $("#slt_sub_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        if(comm_parent_id == 0){
            var html='<option value="0">选择</option>';
            $("#slt_three_comm_type_id").html(html);return;
        }
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">选择</option>';
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_three_comm_type_id").html(html);
        },'json');
    }) ;

    var ajaxCouponUrl = "{:url('ajaxGetCouponList',['live_type'=>$live_type,'pagesize'=>5])}"; //优惠券
    // var admin_type="{$admin_type}";
    var getSkuUrl ="{:url('getSku')}";
    // var fight_group_id=0;
    // var getDlr_url="{:url('ajaxGetDlr')}";
    // var dlr_code  ="{$dlr_code}";
    var ajaxCommUrl = "{:url('ajaxGetLiveCommodityList',['live_type'=>$live_type,'pagesize'=>10,'set_type'=>3])}";      //商品
    var index_url   ="{:url('live',['live_type'=>$live_type])}";
    var save_url    ="{:url('saveLive',['live_type'=>$live_type])}";
    // var ajaxGetCommTypeIdUrl ="{:url('ajaxGetCommTypeId')}";
    var urlCardparam    ="{:url('ajaxGetCard')}" + "?set_type=" + {$row['set_type']};


    /**
     * 新增参数验证
     * @type {string}
     */
    var ajaxCommUrl2 = ajaxCommUrl;
    var isInitComm = 1;
    var isInitCommMsg = '';
    //已全选的分页
    var skuConfirmAllCurrentPage = [];
    //当前页所有的comm_set_id值
    currentPageCommSetIds = [];
    //第一个选中的【商品种类】
    var oneCommodityClass = {$oneCommodityClass};
    var commodityClassJson = {$commodity_class_json};
    //第一个选中的【商品类型】
    var oneCommodityDlrTypeId = {$oneCommodityDlrTypeId};
    var commDlrTypeJson = {$comm_dlr_type_json};

    $(".comm-type-search2").on('click',function(){
        var obj = $(this).parents('.form-group');
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();

        //验证【商品种类】和【商品类型】是否已选择
        // if(commodity_class==0 || commodity_dlr_type_id==0){
        //     isInitComm = 0;
        // }else{
        //     isInitComm = 1;
        // }

        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id;

    });
    $("#sku-confirm-all").on('click',function(){
        var isSkuConfirmAll = $(this).data('is-sku-confirm-all');
        if(isSkuConfirmAll == 0){
            layer.msg('当前页已全部添加，请不要重复操作');
            return false;
        }

        var obj = $(this).parents('.form-group');
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var commodity_dlr_type_id = obj.find("select[name='commodity_dlr_type_id']").val();

        //验证【商品种类】和【商品类型】是否已选择
        if(commodity_class==0){
            layer.msg('请选择商品种类');
            return;
        }
        if(commodity_dlr_type_id==0){
            layer.msg('请选择商品类型');
            return;
        }

        if(oneCommodityClass==0){
            oneCommodityClass = commodity_class;
        }
        if(oneCommodityClass != commodity_class){
            $("#sku-modal").modal('hide');
            var msg1 = "只能添加“"+commodityClassJson[oneCommodityClass]+"“的商品，该商品为“"+commodityClassJson[commodity_class]+"”";
            layer.msg(msg1);
            return;
        }

        if(oneCommodityDlrTypeId==0){
            oneCommodityDlrTypeId = commodity_dlr_type_id;
        }
        if(oneCommodityDlrTypeId != commodity_dlr_type_id){
            $("#sku-modal").modal('hide');
            var msg2 = "只能添加同种类型的商品，该商品为“"+commDlrTypeJson[oneCommodityDlrTypeId]+"“";
            layer.msg(msg2);
            return;
        }

        //先请求列表数据，同时进行批量数据操作
        commTypeSearch2(1);
    });
    //活动商品列表搜索
    function commTypeSearch2(isSkuConfirmAll = 0){
        var commodity_class = $("select[name='commodity_class']").val();
        var commodity_dlr_type_id = $("select[name='commodity_dlr_type_id']").val();
        var comm_parent_id = $("select[name='comm_parent_id']").val();
        var page = $("#sku-confirm-all").data("current-page");
        ajaxCommUrl = ajaxCommUrl2 + '&commodity_dlr_type_id='+commodity_dlr_type_id+ '&page='+page;

        //请求公示接口数据
        commTypeSearch(isSkuConfirmAll)
    }

    //已经添加过的商品
    var comm_set_id_arr = [];
    var commodity_dlr_type_selected = 0;

    $("#deposit_end_time").on('blur',(function(){
        var start_time = $('input[name="deposit_start_time"]').val()
        var end_time = $("#deposit_end_time").val();
        if(end_time < start_time){
            layer.msg('定金结束时间不能小于开始时间', { icon:5,time:3000});
            return false;
        }
    }));
    $("#balance_start_time").on('blur',(function(){
        var start_time = $('input[name="balance_start_time"]').val()
        var end_time = $("#deposit_end_time").val();
        if(start_time < end_time){
            layer.msg('尾款开始时间不能小于定金结束时间', { icon:5,time:3000});
            return false;
        }
    }));
    $("#balance_end_time").on('blur',(function(){
        var start_time = $('#balance_start_time').val()
        var end_time = $("#balance_end_time").val();
        if(end_time < start_time){
            layer.msg('尾款结束时间不能小于尾款开始时间', { icon:5,time:3000});
            return false;
        }
    }));

    $("body").on('click','#GWDLR',function(){
        if($("#GWDLR").is(':checked')){
            $('#dealer_select').removeClass('hidden');
        }else{
            $('#dealer_select').addClass('hidden');
        }
    });

    var dlr_data = <?= $dlr_data ?>;

    $("body").on("click",'#dlr_show',function () {
        var select_data = $("#dlr_hide").val().split(',');
        Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
            $("#dlr_show").val(dlr_name.join(','));
            $("#dlr_hide").val(dlr_code.join(','));
        });
    })

    //是否关闭活动
    var card_available = {$row['card_available']};
    $('.del-commodity').on('click',function (){
        if($("table #haved-commodtiy tr:visible").length > 0){
            var index = layer.open({
                title: ['操作提醒'],
                btn: ['确认', '取消'],
                content:"<div style='font-size: 15px'>您选择对商品列表中全部商品进行移除操作吗?</div>",
                yes: function (res) {
                    $("#haved-commodtiy").empty();
                    $("#sku-confirm-all").data('is-sku-confirm-all',1)
                    initComm(ajaxCommUrl2);
                    commodity_select()
                    comm_id = [];
                    comm_set_id_arr = [];
                    skuConfirmAllCurrentPage = []
                    layer.close(index);
                }
            })
        }
    })
</script>
<script src="__STATIC__admin_v2/js/presale_live.js?rand=*******"></script>
<script src="__STATIC__admin_v2/js/commodity_page.js?rand=*******"></script>
{include file="card:act_rule" /}
{include file="card:act_card_rule" /}
{/block}