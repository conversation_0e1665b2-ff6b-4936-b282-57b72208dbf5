{extend name="public:base_layout" /}

{block name="css"/}
<link href="__STATIC__admin_v2/plugins/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />
<link href="__STATIC__admin_v2/css/style_commodity_type.css" rel="stylesheet"/>
<link href="__STATIC__admin_v2/css/jBootsrapPage.css" rel="stylesheet"/>
<style>
    .form-group dd{
        margin-top: 5px;
    }
    .cover-image{
        width: 40px;
        height: 40px;
        margin-right: 10px;
        margin-left: 20px;
    }

</style>
{/block}

{block name="content"/}

<div class="panel-body">

    <div class="alert alert-danger m-b-8 m-t-10" style="display: none;">
        <h4><i class="fa fa-info-circle"></i> 操作失败</h4>
        <p></p>
    </div>
    <div class="well m-t-10">
        <legend class="pull-left width-full">编辑</legend>
        <form id="fight-form"class="form-horizontal form-bordered" data-parsley-trigger="change">

            <input type="hidden" name="acstatus" value="{$row['act_status']}" id="act_status" >

            <div class="form-group">
                <label class="control-label col-md-2">活动名称<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <input type="text" name="title" placeholder="请输入活动名称" value="{$row['title']}" class="form-control " data-parsley-required="true" data-parsley-length="[1, 15]">
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">起止时间<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-9">
                    <div class="col-md-3 p-l-0" style="">
                        <input type="text" name="start_time" value="{$row['start_time']}" placeholder="请输入开始时间" class="form-control datetimepicker3"  >
                    </div>

                    <div class="col-md-3">
                        <input type="text" name="end_time" value="{$row['end_time']}" placeholder="请输入结束时间" class="form-control datetimepicker3"  >
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">促销标签:</label>
                <div class="col-md-5" style="width: 70%;"><input type="text" maxlength="4"  name="tag" value="{$row.tag}" style="width:200px;background-color: #fff;background-image: none;
    border: 1px solid #ccc;border-radius: 4px;box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset; color: #555;font-size: 14px;height: 34px;line-height: 1.42857;padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;border-radius: 3px;box-shadow: none; font-size: 12px;" {eq name="$row.is_enable" value="1"} {if(in_array($row.act_status, [3]))} disabled {/if}{/eq} />
                    (促销标签是商家对促销动作的别名操作，用于前台显示，最多可输入4个字符，非必填选项）
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">参团人数<i class="m-r-3 text-danger">*</i>:</label>
                <div  class="col-md-5" id="people_number">
                    <div class="input-group col-md-12" >
                        <input type="text" data-parsley-errors-container="#people_number" readonly name="people_number"
                               value="{$row['people_number']}" class="form-control  col-md-3" data-parsley-required="true" data-parsley-type="integer"  data-parsley-range="[2,50]">
                        <span class="input-group-addon" >人</span>
                    </div>
                    <dd>参团人数不少于2人，不超过50人</dd>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">限购数量:</label>
                <div  class="col-md-5" id="purchase_number">
                    <div class="input-group col-md-12" >
                        <input type="text" data-parsley-errors-container="#purchase_number" readonly name="purchase_number"
                               value="{$row['purchase_number']}" class="form-control  col-md-3"   data-parsley-type="integer"  data-parsley-min="0"  >
                        <span class="input-group-addon" >件／人</span>
                    </div>
                    <dd>如果设置为0或不设置则被视为不限制购买数量</dd>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">参团时间:</label>
                <div  class="col-md-5" id="buy_hour">
                    <div class="input-group col-md-12" >
                        <input type="text" data-parsley-errors-container="#buy_hour"  name="buy_hour" value="{$row['buy_hour']}" class="form-control  col-md-3 "   data-parsley-type="integer"  data-parsley-min="1"  >
                        <span class="input-group-addon" >小时</span>
                    </div>
                    <dd>用户发起多少小时后如果拼团失败自动退款</dd>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">拼团规则<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-5">
                    <textarea class="form-control" id="rule" name="rule" rows="4" value="" data-parsley-length="[0,255]" placeholder="每个商品都有单独购买价格和拼团价格，选择拼团购买进行商品下单，开团支付成功后获取转发链接，邀请好友参团，参团成员也可以将该团分享出去邀约更多的团员参团，在规定时间内邀请到相应人数支付购买则拼团成功，等待收货;未达到人数则团购失败，系统会自动退款到付款账户。" data-parsley-required="true">{$row['rule']}</textarea>
                    <dd>拼团规则描述不能为空且不能大于255个字</dd>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">业务归属:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <select name="gather_id" class="form-control input-sm ">
                            <option value=""> 全部 </option>
                            {volist name="$gather_list" id="val" key="key"}
                            <option value="{$val['id']}" {if($row['gather_id'] == $val['id'])}selected{/if} > {$val['name']} </option>
                            {/volist}
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">主题活动:</label>
                <div  class="col-md-5" id="is_enable">
                    <input type="text" name="theme_name" id="theme_name" placeholder="请输入主题活动" value="{$row['theme_name']}" class="form-control" data-parsley-length="[0, 100]">
                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">是否PV补贴:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_pv_subsidy" type="checkbox" data-input-name="is_pv_subsidy" data-input-true="1" data-input-false="0"  data-size="small" data-on-text="开启" data-off-text="关闭"  {eq name="row.is_enable" value="1"} {if(in_array($row.act_status, [2]))} disabled {/if}{/eq}
                            <?php if($row['is_pv_subsidy']==1) echo 'checked';?> />
                            {if(in_array($row.act_status, [3]))}
                            <input type="hidden" name="is_pv_subsidy" value="{$row.is_pv_subsidy}">
                            {/if}
                        </label>
                    </div>
                </div>
            </div>


            <div class="form-group"  >
                <label class="control-label col-md-2">是否叠加券:</label>
                <div  class="col-md-5" id="card_available">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_card_change" type="checkbox"  name="switch" data-input-name="card_available"
                                   data-input-true="1" data-input-false="0"
                                   data-size="small" data-on-text="可用" data-off-text="不可用"
                                <?php if($row['card_available']==1) echo 'checked';?> />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group no_card ">
                <label class="col-md-2 control-label" id="card_activity">选择可叠加的券:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control card_activity" name="card_activity" placeholder="点击选择" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="{$row['commodity_card_name']}">
                    <input type="hidden" class="form-control hide_card_ids" name="rel_card_ids" value="{$row['rel_card_ids']}">
                </div>
                <div class="bottom-wp">
                    <div class="bottom-left">
                        已选<input type="" class="num_card_ids" style="width: 20px;outline:none;border: 0; readonly" value="{$row['num_card']}">张
                    </div>
                </div>
            </div>

            <div class="form-group ">
                <label class="col-md-2 control-label" id="e3s_activity">关联E3S活动:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control e3s_activity" name="e3s_activity" placeholder="请选择活动" data-parsley-group="wizard-step-1" id="text-a" data-id="" value="{if (!empty($row['e3s_activity_id']))}{$row['e3s_activity_id']} | {$row['e3s_activity_name']}{/if}">
                    <input type="hidden" id="e3s_activity_id" name="e3s_activity_id" value="{$row['e3s_activity_id']}">
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label" id="activity_type">活动设置类型:</label>
                <div class="col-md-6">
                    <select class="form-control width-300" name="activity_type" >
                        <option value="0">请选择</option>
                        <option value="1" {if($row['activity_type'] == 1)}selected{/if}>备件</option>
                        <option value="2" {if($row['activity_type'] == 2)}selected{/if}>工时</option>
                        <option value="3" {if($row['activity_type'] == 3)}selected{/if}>赠品</option>
                    </select>
                </div>
            </div>
            <div class="form-group ">
                <label class="col-md-2 control-label" id="settlement_rule">关联结算规则:</label>
                <div class="col-md-6">
                    <input type="text" autocomplete="off" class="form-control settlement_rule" name="settlement_rule" id="template_name" placeholder="请选择选择结算规则" data-parsley-group="wizard-step-1"  value="{if ($row['settlement_rule_id'] > 0)}{$row['settlement_rule_name']}{/if}">
                    <input type="hidden" id="settlement_rule_id" name="settlement_rule_id" value="{$row['settlement_rule_id']}">
                    <input type="hidden" id="settlement_rule_value" name="settlement_rule_value" value="{$row['settlement_rule_value']}">
                    <input type="hidden" id="settlement_rule_type" name="settlement_rule_type" value="{$row['settlement_rule_type']}">
                    <input type="hidden" id="act_sett_standard" name="act_sett_standard" value="{$row['act_sett_standard']}">

                </div>
            </div>

            <div class="form-group" id="up_down">
                <label class="control-label col-md-2">活动渠道:</label>
                <div class="col-md-10">
                    <div>

                        <?php $row_code = explode(',', $row['up_down_channel_dlr']);
                        foreach ($up_down_channel as $key => $val):
                        if($live_type == 1): if (in_array($key,['PZ1AAPP','PZ1ASM'])):?>
                            <label class="checkbox-inline">
                                <input type="checkbox"
                                       name="up_down_channel[]"
                                    <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                       value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                            </label>
                        <?php endif; else: if ($live_type == 2): if (in_array($key,['QCAPP','QCSM'])):?>
                            <label class="checkbox-inline">
                                <input type="checkbox"
                                       name="up_down_channel[]"
                                    <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                       value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                            </label>
                        <?php endif; else: if (!in_array($key,['PZ1AAPP','PZ1ASM','QCAPP','QCSM','TOBPC'])):?>
                            <label class="checkbox-inline">
                                <input type="checkbox"
                                       name="up_down_channel[]"
                                    <?php if (in_array($key, $row_code)) echo 'checked'; ?>
                                       value="<?= $key ?>" id="<?=$key?>" data-name="<?= $val ?>"><?= $val ?>
                            </label>
                        <?php endif; endif; endif; endforeach; ?>
                    </div>
                    <div style="margin-top:20px;" class="<?= $dlr_hidden ?>" id="dealer_select">
                        <input id="dlr_show" type="text" class="form-control width-300" placeholder="请点击选择经销商"
                               data-parsley-required="true" value="<?=$dlr_str?>">
                        <input id="dlr_hide" type="text" name="dlr_code" class="hidden"
                               value="<?=$row['up_down_channel_dlr']?>">
                    </div>

                </div>
            </div>

            <div class="form-group"  >
                <label class="control-label col-md-2">活动状态:</label>
                <div  class="col-md-5" id="is_enable">
                    <div class="input-group col-md-9">
                        <label>
                            <input class="switch" id="is_enable" type="checkbox" data-input-name="is_enable" data-input-true="1" data-input-false="0"
                                   data-size="small" data-on-text="开启" data-off-text="关闭"
                                <?php if($row['is_enable']==1) echo 'checked';?> />
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2">活动商品（仅支持快递商品）<i class="m-r-3 text-danger">*</i>:</label>
                <div class="col-md-10">
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品分类:</label>
                            <select class="form-control input-sm element default-select2 col-md-2" name="comm_parent_id" id="slt_comm_type_id">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($comm_parent_list as $key=>$val):?>
                                    <option value="{$val['id']}" >{$val['comm_type_name']}</option>
                                <?php endforeach;?>
                            </select>
                            <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                            <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id">
                                <option value="0">
                                    请选择
                                </option>
                            </select>
                        </div>

                    </div>
                    <div class="col-md-10 m-b-10"  style="width: 100%;padding-left:0px;">
                        <div style="float:left">
                            <label class="control-label col-md-2" style="padding: 5px 5px; width: auto;">商品种类:</label>
                            <select class="form-control input-sm element default-select2 col-md-2"
                                    name="commodity_class" id="slt_comm_type_id">
                                <option value="0">
                                    请选择
                                </option>
                                <?php foreach($commodity_class as $key=>$val):?>
                                    <option value="{$key}" >{$val}</option>
                                <?php endforeach;?>
                            </select>
                        </div>
                        <div style="float:right;">
                            <label class="control-label col-md-2" style="padding:  5px 5px;  width: 90px;">商品名称:</label>
                            <input type="text" class="form-control input-sm element col-md-3 m-r-2 width-200" name="commodity_name" placeholder="请输入商品名称">
                            <button id="comm-type-search" type="button" class="btn btn-sm btn-success"><i class="fa fa-search"></i>搜索</button>
                        </div>

                    </div>
                    <div>
                        <div class="table-scrollable">
                            <table id="" class="table table-hover">
                                <thead>
                                <th class="text-center">商品规格</th>
                                <th class="text-center">上架渠道</th>
                                <th class="text-center">原价</th>
                                <th class="text-center">库存</th>
                                <th class="text-center">操作</th>
                                </thead>
                                <tbody id="add-comm-tbody">

                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ul class="pagination" id="comm-pagination"></ul>
                        </div>
                    </div>

                    <legend class="pull-left width-full" style="font-size: 18px;">已选活动商品</legend>

                    <div>
                        <div class="table">
                            <table id="" class="table">
                                <thead></thead>

                                <tbody id="haved-commodtiy">
                                <?php foreach($row_comm as $key=>$val):?>
                                    <tr class="info" data="">
                                        <td style="width: 350px;"><img class="cover-image" src="{$val['cover_image']}"><a class="init-commodity-preview" data-dlr-code="{$dlr_code}" data-commodity-id="{$val['commodity_id']}">{$val['commodity_name']}</a></td>
                                        <td class="text-right"><button data-sku-list='{$val["sku_list"]}' class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button><button class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button>
                                        </td></tr>
                                <?php endforeach;?>

                                </tbody>
                            </table>
                        </div>

                    </div>

                </div>
            </div>

            <div class="form-group">
                <label class="control-label col-md-2"></label>
                <div class="col-md-5">

                    <input type="hidden" name="set_type" value="{$row['set_type']}">

                    <?php if($row['act_status']!=3) {?>
                        <a href="javascript:;" class="btn btn-sm btn-primary btn-sm"  id="put-form">确认</a>
                    <?php }?>

                </div>
            </div>

            <input type="hidden" name="id" value="{$row['id']}">
        </form>
    </div>


    <!----begin 查看规格---->
    <div class="modal fade" id="sku-modal" data-comm-id="" data-comm-set-id="" data-type="">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">选择商品: <span id="comm-name-header"></span></h4>
                </div>
                <input id="modal_home"  type="hidden" value="" />
                <div class="modal-body" style="">
                    <div class="alert alert-info m-b-0" style="padding: 8px;padding-top: 5px;">
                        <h6>操作提示</h6>
                        <p>默认该商品全部规格选中参加该项促销活动,如某个规格不想参与该项活动,可通过取消选择排除掉;被排除的规格将不参加多人拼团活动</p>

                    </div>

                    <div class="hide">
                        <div class="sku-image" style="display: inline-block;">
                            <img class=" cover-image" src="">
                        </div>
                        <div style="display: inline-block;">
                            <div  class="sku-comm">
                            </div>
                        </div>
                    </div>


                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th class="col-md-6" >商品规格</th>
                            <th >原价</th>
                            <th>
                                <a href="#" id="all_price"  data-type="text" class="btn btn-primary btn-sm editable editable-click" data-value="" data-placeholder="请输入折扣" data-title="批量设置折扣">折扣<i class="fa fa-edit m-l-5"></i></a>
                            </th>
                            <th>库存</th>
                        </tr>
                        </thead>
                        <tbody class="sku-tb" id="change_sku_tb">
                        </tbody>
                    </table>

                    <?php if($row['set_type']==1):?>
                        <div>
                            <label class="control-label col-md-1" style="width: 80px;">经销商<i class="m-r-3 text-danger">*</i>:</label>
                            <textarea class="form-control sku-dlr" readonly placeholder="请点击选择经销商"  data-parsley-required="true" data-sku-dlr="" style="width: 470px; height: 50px;"></textarea>
                        </div>
                    <?php endif;?>
                </div>

                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >关闭</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary  " id="sku-confirm" >确定</a>
                </div>
            </div>
        </div>
    </div>

    <!----begin 删除设置---->
    <div class="modal fade" id="del-sku-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title">操作提醒</h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger m-b-0">
                        <h4><i class="fa fa-info-circle"></i>确定删除该商品?</h4>
                        <p></p>
                    </div>
                </div>
                <input type="hidden" id="del-data-id" data-comm-set-id="" value="">
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-sm btn-white" data-dismiss="modal" >取消</a>
                    <a href="javascript:;" class="btn btn-sm btn-primary"  id="del-confirm">确定</a>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"/}

<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/moment-with-locales.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap-eonasdan-datetimepicker/build/js/bootstrap-datetimepicker.min.js"></script>
<script src="__STATIC__admin_v2/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="__STATIC__admin_v2/js/jBootstrapPage.js"></script>
<script src="__STATIC__admin_v2/js/commodity.js"></script>

<script>
    $("#slt_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">选择</option>';
            $("#slt_three_comm_type_id").html(html);
            if(comm_parent_id == 0){
                $("#slt_sub_comm_type_id").html(html);return;
            }
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_sub_comm_type_id").html(html);
        },'json');
    }) ;

    $("#slt_sub_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        if(comm_parent_id == 0){
            var html='<option value="0">请选择</option>';
            $("#slt_three_comm_type_id").html(html);return;
        }
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">请选择</option>';
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_three_comm_type_id").html(html);
        },'json');
    }) ;
    var admin_type="{$admin_type}";
    var getSkuUrl ="{:url('getSku')}";
    var fight_group_id="{$row['id']}";
    var getDlr_url="{:url('ajaxGetDlr')}";
    var dlr_code  ="{$dlr_code}";
    //异步请求数据地址
    var ajaxCommUrl = "{:url('ajaxGetLiveCommodityList',['live_type'=>$live_type,'set_type'=>$row['set_type'],'pagesize'=>5])}";      //商品
    var save_url    ="{:url('saveLive',['live_type'=>$live_type])}";
    var index_url   ="{:url('live',['set_type'=>$row['set_type'],'live_type'=>$live_type])}";
    var ajaxGetCommTypeIdUrl ="{:url('ajaxGetCommTypeId')}";
    var set_type = "{$row['set_type']}";
    var urlCardparam    ="{:url('ajaxGetCard')}" + "?set_type=" + {$row['set_type']};

    $("body").on('click','#GWDLR',function(){
        if($("#GWDLR").is(':checked')){
            $('#dealer_select').removeClass('hidden');
        }else{
            $('#dealer_select').addClass('hidden');
        }
    });

    var dlr_data = <?= $dlr_data ?>;

    $("body").on("click",'#dlr_show',function () {
        var select_data = $("#dlr_hide").val().split(',');
        Custom.selectDlr(dlr_data,select_data,function (dlr_code,dlr_name) {
            $("#dlr_show").val(dlr_name.join(','));
            $("#dlr_hide").val(dlr_code.join(','));
        });
    })

    //是否关闭活动
    var card_available = {$row['card_available']};

</script>
<script src="__STATIC__admin_v2/js/fight_group_live.js?rand=1.0.1"></script>
{include file="card:act_rule" /}
{include file="card:act_card_rule" /}
{/block}