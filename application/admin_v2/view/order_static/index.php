{extend name="public:base_layout" /}

{block name="content"/}

<div class="panel-body">
    <div class="alert alert-info fade in m-b-15" >
        <h4>操作提示</h4>
        <div>默认显示最近3天数据!导出操作,限制30秒才能操作一次</div>
        <div style="color: red;">异步导出默认当天下单时间,根据下单时间/支付时间最多导出一个月,注意:下单/支付时间必选其一</div>
    </div>
    <div class="margin-bottom-10 p-r-0">
        <form id="searchform" class="form search-form" action="" method="get">
            <input type="text" class="hidden" name="order_status" value="{$query.order_status}">
            <input type="text" class="hidden" name="channel" value="{$query.channel}">
            <input type="text" class="hidden" name="order_source" value="{$query.order_source}">
            {if condition="$role==1"}<!--平台端-->
            <div class="dndc-form-mod1" id="dndc-form-mod1">
                <div class="form-main">
                    <div class="form-item">
                        <em>订单编码：</em>
                        <div class="form-wp">
                            <input type="text" class="form-control input-sm element" name="order_code" value="{$query.order_code}" placeholder="请输入订单编码">
                        </div>
                    </div>
                    <div class="form-item">
                        <em>手机号：</em>
                        <div class="form-wp">
                            <input type="text" name="phone" class="form-control input-sm element" placeholder="请输入手机号" aria-controls="data-table" value="{$query.phone}">
                        </div>
                    </div>

                    <div class="btn-more" id="dndc-btn-more1">
                        更多<i class="fa fa-plus-circle"></i><span class="arrow-spread"></span>
                    </div>
                    <div class="btn-wp">
                        <button type="submit" class=" btn btn-sm btn-success m-r-3 btn-submit"><i class="fa fa-search"></i>搜索</button>
                        <button type="button" class="btn btn-sm btn-primary m-r-3" id="exp_data"><i class="fa fa-1x fa-download"></i>导出</button>
                        <button type="button" class="btn btn-sm btn-primary m-r-3" id="ajax_exp_data">
                            <i class="fa fa-1x fa-download"></i>异步导出
                        </button>
                        <button type="button" class="btn btn-sm btn-white m-r-3 reset">重置</button>
                    </div>
                </div>
                <div class="form-spread">
                    <ul>


                        <li>
                            <div class="form-item">
                                <em>商品名称：</em>
                                <div class="form-wp">
                                    <input type="text" name="commodity_name" class="form-control input-sm element " placeholder="请输入商品名称" aria-controls="data-table" value="{$query.commodity_name}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>订单状态：</em>
                                <div class="form-wp">
                                    <select id="multiselect" class="multiselect" multiple="multiple">
                                        {volist name="order_status_list" id="order_status" key="k"}
                                        {if condition="in_array($k,$query.order_status_array)"}
                                        <option value="{$k}" selected>{$order_status}</option>
                                        {else/}
                                        <option value="{$k}">{$order_status}</option>
                                        {/if}
                                        {/volist}
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>总订单号：</em>
                                <div class="form-wp">
                                    <input type="text" class="form-control input-sm element" name="parent_order_code" value="{$query.parent_order_code}" placeholder="请输入总订单号">
                                </div>
                            </div>
                        </li>

                        <li>
                            <div class="form-item">
                                <em>订单来源：</em>
                                <div class="form-wp">
                                    <select  id="order_source" class="multiselect" multiple="multiple">
                                        <?php foreach ($order_source_list as $key=>$val):?>
                                            <option value="{$key}" <?php if(in_array($key,$query['order_source_array'])) {
                                                echo 'selected';
                                            } ?>>{$val}</option>
                                        <?php endforeach;?>

                                    </select>
                                </div>
                            </div>
                        </li>

                        <li>
                            <div class="form-item">
                                <em>渠道来源：</em>
                                <div class="form-wp">
                                    <select id="channel" class="multiselect" multiple="multiple">
                                        {volist name="channel" id="vo" key="k"}
                                        {if condition="in_array($k,$query.channel_array)"}
                                        <option value="{$k}" selected>{$vo}</option>
                                        {else/}
                                        <option value="{$k}">{$vo}</option>
                                        {/if}
                                        {/volist}
                                    </select>
                                </div>
                            </div>
                        </li>


                        <li>
                            <div class="form-item">
                                <em>商品分类：</em>
                                <div class="form-wp two" >
                                    <div class="left" style="width: 33%">
                                        <select class="form-control input-sm element default-select2 col-md-3" name="comm_parent_id" id="slt_comm_type_id">
                                            <option value="0">
                                                请选择
                                            </option>
                                            <?php foreach($comm_parent_list as $key=>$val):?>
                                                <option value="{$val['id']}" <?php if($val['id']==$comm_parent_id) echo 'selected'?>>{$val['comm_type_name']}</option>
                                            <?php endforeach;?>
                                        </select>
                                    </div>
                                    <div class="left" style="width: 33%">
                                        <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id">
                                            <option value="0">
                                                请选择
                                            </option>
                                            <?php foreach($comm_type_list as $key=>$val):?>
                                                <option value="{$val['id']}" <?php if($val['id']==$sub_comm_type_id) echo 'selected'?>>{$val['comm_type_name']}</option>
                                            <?php endforeach;?>
                                        </select>
                                    </div>
                                    <div class="left" style="width: 33%">
                                        <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id">
                                            <option value="0">
                                                请选择
                                            </option>
                                            <?php foreach($comm_three_type_list as $key=>$val):?>
                                                <option value="{$val['id']}" <?php if($val['id']==$comm_three_parent_id) echo 'selected'?>>{$val['comm_type_name']}</option>
                                            <?php endforeach;?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </li>

                        <li>
                            <div class="form-item">
                                <em>支付单号：</em>
                                <div class="form-wp">
                                    <input type="text" name="pay_order_code" class="form-control input-sm element" placeholder="请输入支付订单号" aria-controls="data-table" value="{$query.pay_order_code}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>支付时间：</em>
                                <div class="form-wp">
                                    <div class="input-group date" id="pay-time-daterange">
                                        <input type="text"  name="pay_time" class="form-control input-sm element" value="{$query.pay_time}" placeholder="请选择日期" >
                                        <span class="input-group-addon">
	                                        <span class="glyphicon glyphicon-calendar"></span>
	                                    </span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>下单时间：</em>
                                <div class="form-wp">
                                    <div class="input-group date" id="default-daterange">
                                        <!--异步下载的时候这里必选择-->
                                        <input type="text"  name="created_date" class="form-control input-sm element" value="{$query.created_date}" placeholder="请选择日期" >
                                        <span class="input-group-btn">
												<button class="btn btn-default btn-sm" type="button">
                                                    <i class="fa fa-calendar"></i>
                                                </button>
										</span>
                                    </div>
                                </div>
                            </div>
                        </li>




<!--                        <li>
                            <div class="form-item">
                                <em>商品归属：</em>
                                <div class="form-wp">
                                    <select  name="set_type" class="form-control input-sm element  default-select2">
                                        <option value="" selected>请选择</option>
                                        <option value="1" <?php /*if ($query['set_type']==1) {
    echo 'selected';
} */?>>平台</option>
                                        <option value="2" <?php /*if ($query['set_type']==2) {
    echo 'selected';
} */?>>专营店</option>
                                        <option value="3" <?php /*if ($query['set_type']==3) {
    echo 'selected';
} */?>>官微</option>
                                    </select>
                                </div>
                            </div>
                        </li>
-->
                        <li>
                            <div class="form-item">
                                <em>姓名：</em>
                                <div class="form-wp">
                                    <input type="text" name="name" class="form-control input-sm element" placeholder="请输入姓名" aria-controls="data-table" value="{$query.name}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>专营店编码：</em>
                                <div class="form-wp">
                                    <input type="text" class="form-control input-sm element" name="dlr_name"  placeholder="专营店编码" aria-controls="data-table" value="{$query.dlr_name}">
                                </div>
                            </div>
                        </li>

                        <li>
                            <div class="form-item">
                                <em>vin：</em>
                                <div class="form-wp">
                                    <input type="text" class="form-control input-sm element" name="vin"  placeholder="vin" aria-controls="data-table" value="{$query.vin}">
                                </div>
                            </div>
                        </li>


                        <li>
                            <div class="form-item">
                                <em>供应商：</em>
                                <div class="form-wp">
                                    <select class="form-control input-sm element default-select2" name="js_jd">
                                        <option value="">请选择</option>
                                        {volist name="sups" id="vo"}
                                        <option value="{$vo.value_code}" <?php if($query['is_jd'] == $vo['value_code'])  echo "selected"   ?>>{$vo.county_name}</option>
                                        {/volist}
                                    </select>
                                </div>
                            </div>
                        </li>

                        <li>
                            <div class="form-item">
                                <em>用户ID：</em>
                                <div class="form-wp">
                                    <input type="text" class="form-control input-sm element" name="user_id"  placeholder="user_id" aria-controls="data-table" value="{$query.user_id}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>物流方式：</em>
                                <div class="form-wp">
                                    <select  name="logistics_mode" class="form-control input-sm element  default-select2">
                                        <option value="">请选择</option>
                                        {if condition="$query.logistics_mode==1"}
                                        <option value="1" selected>自提</option>
                                        <option value="2">快递</option>
                                        {elseif condition="$query.logistics_mode==2"}
                                        <option value="1">自提</option>
                                        <option value="2" selected>快递</option>
                                        {else/}
                                        <option value="1">自提</option>
                                        <option value="2">快递</option>
                                        {/if}
                                    </select>
                                </div>
                            </div>
                        </li>


                        <li>
                            <div class="form-item">
                                <em>供应商订单号：</em>
                                <div class="form-wp">
                                    <input type="text" class="form-control input-sm element" name="third_order_id"  placeholder="供应商订单号" aria-controls="data-table" value="{$query.third_order_id}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>积分扣除状态：</em>
                                <div class="form-wp">
                                    <select  name="is_cc_ok" class="form-control input-sm element  default-select2">
                                        <option value="" selected>请选择</option>
                                        <option value="10" <?php if($query['is_cc_ok']==10) echo 'selected' ?>>积分扣除成功</option>
                                        <option value="1" <?php if($query['is_cc_ok']==1) echo 'selected' ?>>积分扣除失败</option>
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>开票状态：</em>
                                <div class="form-wp">
                                    <select  name="invoice_status" class="form-control input-sm element  default-select2">
                                        <option value="" selected>请选择</option>
                                        <option value="1001" <?php if($query['invoice_status']==1001) echo 'selected' ?>>已申请</option>
                                        <option value="1004" <?php if($query['invoice_status']==1004) echo 'selected' ?>>已开票</option>
                                        <option value="1007" <?php if($query['invoice_status']==1007) echo 'selected' ?>>开票失败</option>
                                        <option value="1011" <?php if($query['invoice_status']==1011) echo 'selected' ?>>换开待审核</option>
                                        <option value="1014" <?php if($query['invoice_status']==1014) echo 'selected' ?>>冲红中</option>
                                        <option value="1017" <?php if($query['invoice_status']==1017) echo 'selected' ?>>已冲红</option>
                                        <option value="1021" <?php if($query['invoice_status']==1021) echo 'selected' ?>>冲红失败</option>
                                        <option value="101" <?php if($query['invoice_status']==101) echo 'selected' ?>>待审核</option>
                                        <option value="107" <?php if($query['invoice_status']==107) echo 'selected' ?>>开票驳回</option>
                                        <option value="9999" <?php if($query['invoice_status']==9999) echo 'selected' ?>>无需开票</option>
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>开票时间：</em>
                                <div class="form-wp">
                                    <div class="input-group date" id="invoice-daterange">
                                        <input type="text"  name="invoice_time" class="form-control input-sm element" value="{$query.invoice_time}" placeholder="请选择日期" >
                                        <span class="input-group-addon">
	                                        <span class="glyphicon glyphicon-calendar"></span>
	                                    </span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li></li>
                    </ul>
                </div>
            </div>
            {else/}<!--专营店端-->
            <div class="dndc-form-mod1" id="dndc-form-mod1">
                <div class="form-main">
                    <div class="form-item">
                        <em>订单编码：</em>
                        <div class="form-wp">
                            <input type="text" class="form-control input-sm element" name="order_code" value="{$query.order_code}" placeholder="请输入订单编码">
                        </div>
                    </div>
                    <div class="btn-more" id="dndc-btn-more1">
                        更多<i class="fa fa-plus-circle"></i><span class="arrow-spread"></span>
                    </div>
                    <div class="btn-wp">
                        <button type="submit" class=" btn btn-sm btn-success m-r-3"><i class="fa fa-search"></i>搜索</button>
                        <button type="button" class="btn btn-sm btn-primary m-r-3" id="exp_data"><i class="fa fa-1x fa-download"></i>导出</button>
                        <button type="button" class="btn btn-sm btn-white  reset">重置</button>
                    </div>
                </div>
                <div class="form-spread">
                    <ul>
                        <li>
                            <div class="form-item">
                                <em>下单时间：</em>
                                <div class="form-wp">
                                    <div class="input-group date" id="default-daterange">
                                        <input type="text"  name="created_date" class="form-control input-sm element" value="{$query.created_date}" placeholder="请选择日期" >
                                        <span class="input-group-addon">
	                                        <span class="glyphicon glyphicon-calendar"></span>
	                                    </span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>商品名称：</em>
                                <div class="form-wp">
                                    <input type="text" name="commodity_name" class="form-control input-sm element " placeholder="请输入商品名称" aria-controls="data-table" value="{$query.commodity_name}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>订单状态：</em>
                                <div class="form-wp">
                                    <select id="multiselect" class="multiselect" multiple="multiple">
                                        {volist name="order_status_list" id="order_status" key="k"}
                                        {if condition="in_array($k,$query.order_status_array)"}
                                        <option value="{$k}" selected>{$order_status}</option>
                                        {else/}
                                        <option value="{$k}">{$order_status}</option>
                                        {/if}
                                        {/volist}
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>商户订单号：</em>
                                <div class="form-wp">
                                    <input type="text" name="pay_order_code" class="form-control input-sm element " placeholder="请输入商户订单号" aria-controls="data-table" value="{$query.pay_order_code}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>商品分类：</em>
                                <div class="form-wp two" >
                                    <div class="left" style="width: 33%">
                                        <select class="form-control input-sm element default-select2 col-md-3" name="comm_parent_id" id="slt_comm_type_id">
                                            <option value="0">
                                                请选择
                                            </option>
                                            <?php foreach($comm_parent_list as $key=>$val):?>
                                                <option value="{$val['id']}" <?php if($val['id']==$comm_parent_id) echo 'selected'?>>{$val['comm_type_name']}</option>
                                            <?php endforeach;?>
                                        </select>
                                    </div>
                                    <div class="left" style="width: 33%">
                                        <select name="sub_comm_type_id" class="form-control input-sm element default-select2" id="slt_sub_comm_type_id">
                                            <option value="0">
                                                请选择
                                            </option>
                                            <?php foreach($comm_type_list as $key=>$val):?>
                                                <option value="{$val['id']}" <?php if($val['id']==$sub_comm_type_id) echo 'selected'?>>{$val['comm_type_name']}</option>
                                            <?php endforeach;?>
                                        </select>
                                    </div>
                                    <div class="left" style="width: 33%">
                                        <select name="three_comm_type_id" class="form-control input-sm element default-select2" id="slt_three_comm_type_id">
                                            <option value="0">
                                                请选择
                                            </option>
                                            <?php foreach($comm_three_type_list as $key=>$val):?>
                                                <option value="{$val['id']}" <?php if($val['id']==$comm_three_parent_id) echo 'selected'?>>{$val['comm_type_name']}</option>
                                            <?php endforeach;?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>物流方式：</em>
                                <div class="form-wp">
                                    <select  name="logistics_mode" class="form-control input-sm element  default-select2">
                                        <option value="">请选择</option>
                                        {if condition="$query.logistics_mode==1"}
                                        <option value="1" selected>自提</option>
                                        <option value="2">快递</option>
                                        {elseif condition="$query.logistics_mode==2"}
                                        <option value="1">自提</option>
                                        <option value="2" selected>快递</option>
                                        {else/}
                                        <option value="1">自提</option>
                                        <option value="2">快递</option>
                                        {/if}
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>订单来源：</em>
                                <div class="form-wp">
                                    <select  name="order_source" class="form-control input-sm element  default-select2">
                                        <option value="">请选择</option>
                                        <?php foreach ($order_source_list as $key=>$val):?>
                                            <option value="{$key}" <?php if ($query['order_source']==$key) {
    echo 'selected';
} ?>>{$val}</option>
                                        <?php endforeach;?>
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>商品归属：</em>
                                <div class="form-wp">
                                    <select  name="set_type" class="form-control input-sm element  default-select2">
                                        <option value="" selected>请选择</option>
                                        <option value="1" <?php if ($query['set_type']==1) {
    echo 'selected';
} ?>>平台</option>
                                        <option value="2" <?php if ($query['set_type']==2) {
    echo 'selected';
} ?>>专营店</option>
                                        <option value="3" <?php if ($query['set_type']==3) {
    echo 'selected';
} ?>>官微</option>
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>手机号：</em>
                                <div class="form-wp">
                                    <input type="text" name="phone" class="form-control input-sm element" placeholder="请输入手机号" aria-controls="data-table" value="{$query.phone}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>姓名：</em>
                                <div class="form-wp">
                                    <input type="text" name="name" class="form-control input-sm element" placeholder="请输入姓名" aria-controls="data-table" value="{$query.name}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>销售来源：</em>
                                <div class="form-wp">
                                    <select class="form-control input-sm element default-select2" name="sales_source">
                                        <option value="">请选择</option>
                                        <option value="1" <?php if ($query['sales_source']==1) {
    echo "selected";
}?>>平台自营销售</option>
                                        <option value="2" <?php if ($query['sales_source']==2) {
    echo "selected";
}?>>专营店销售</option>
                                        <option value="3" <?php if ($query['sales_source']==3) {
    echo "selected";
}?>>官微销售</option>
                                    </select>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="form-item">
                                <em>支付时间：</em>
                                <div class="form-wp">
                                    <div class="input-group date" id="pay-time-daterange">
                                        <input type="text"  name="pay_time" class="form-control input-sm element" value="{$query.pay_time}" placeholder="请选择日期" >
                                        <span class="input-group-addon">
	                                        <span class="glyphicon glyphicon-calendar"></span>
	                                    </span>
                                    </div>
                                </div>
                            </div>
                        </li>

                        <li></li>
                    </ul>
                </div>
            </div>
            {/if}
        </form>
    </div>
    <div class="table-scrollable">
        <table id="main" class="table table-hover table-bordered">
            <thead>
            <tr class="active">
                <th class="text-center"></th>
                <th>序号</th>
                <th>订单id</th>
                <th>订单编码</th>
                <th>总订单号</th>
                <th>新支付单号</th>
                <th>旧支付单号</th>
                <th>结算单</th>
                <th>会员ID</th>
                <th>姓名</th>
                <th>收件人手机号</th>
                <th>会员手机</th>
                <th>vin</th>
                <th>下单vin</th>
                <th>会员卡</th>
                <th>支付前积分余额</th>
                <th>车牌</th>
                <th>专营店</th>
                <th>订单总价</th>
                <th>订单金额</th>
                <th>实付金额</th>
                <th>运费</th>
                <th>厂家积分</th>
                <th>移动积分</th>
                <th>卡劵名称</th>
                <th>卡劵code</th>
                <th>满减</th>
                <th>买赠活动</th>
                <th>是否赠品</th>
                <th>秒杀活动</th>
                <th>下单时间</th>
                <th>支付时间</th>
                <th>订单状态</th>
                <th>购买卡券状态</th>
                <th>积分扣除状态</th>
                <th>物流方式</th>
                <th>物流信息</th>
                <th>商品归属</th>
                <th>渠道来源</th>
                <th>订单来源</th>
                <th>开票状态</th>
                <th>开票时间</th>

            </tr>
            </thead>
            <tbody>
            <?php foreach ($list as $k=>$order):?>

                <tr>
                    <td data-id="{$order.order_code}" class="extra-column text-center width-10"><i class="fa fa-plus"></i></td>
                    <td><?php echo  get_number($k);?></td>
                    <td>{$order['id']}</td>
                    <td><a class="show-order-detail" data-id="{$order.order_code}">{$order['order_code']}</a></td>
                    <td>{$order['parent_order_code']}</td>
                    <td>
                        <?= !empty($order['ms_order_code2']) ? $order['ms_order_code2'] : '-' ?>
                        <br/>
                        <?= !empty($order['ms_order_code']) ? $order['ms_order_code'] : '-' ?>
                    </td>
                    <td>
                        <?= empty($order['ms_order_code2']) ? $order['cashier_trade_no2'] : '-' ?>
                        <br/>
                        <?= empty($order['ms_order_code']) ? $order['cashier_trade_no'] : '-' ?>
                    </td>
                    <td>{$order['cashier_settlement_no']}</td>
                    <td>{$order['user_id']}</td>
                    <td>{$order['name']}</td>
                    <td>{$order['phone']}</td>
                    <td>{$order['mid_phone']}</td>
                    <td>{$order['vin']}</td>
                    <td>{$order['order_vin']}</td>
                    <td>{$order['ic_card_no']}</td>
                    <td>{$order['current_points']}</td>
                    <td>{$order['license_plate']}</td>
                    <td>{$order['dlr']}</td>
                    <td>{$order['total_order_price']}</td>

                    <!--预付款-->
                    {if $order['order_status'] == 15}
                    <td>{$order['pre_order_money']}</td>
                    <td>{$order['pre_use_money']}</td>
                    {else/}
                    <td>{$order['order_money']}</td>
                    <td>{$order['pay_money']}</td>
                    {/if}

                    <td>{$order['mail_price']}</td>
                    <td>{$order['total_integral']}</td>
                    <td>{$order['yd_point']|default=''}</td>
                    <td>{$order['card_name']}</td>
                    <td>{$order['card_code']}</td>
                    <td>{$order['mj_info']}</td>
                    <td>{$order['gift_title']}</td>
                    <td>{$order['is_gift_name']}</td>
                    <td>{$order['seckill_name']}</td>
                    <td>{$order['created_date']}</td>
                    {if $order['pre_sale_id'] == 0}
                    <td>{$order['pay_time']}</td>
                    {else/}
                    <td>
                        <?= !empty($order['front_pay_time']) ? $order['front_pay_time'] : '-' ?>
                        <br/>
                        <?= !empty($order['pay_time']) ? $order['pay_time'] : '-' ?>
                    </td>
                    {/if}
                    <td>{$order['order_status_name']}</td>
                    <td>{$order['card_status']}</td>
                    <td>{$order['is_cc_ok']}</td>
                    <td>{$order['logistics_mode_name']}</td>
                    <td><a href="javascript:;" data-id="{$order['id']}" data-order-code="{$order['order_code']}" data-supplier="" class="logisticsModal" style="color: blue">{$order['waybill_msg']}</a></td>
                    <td>{$order['type']}</td>
                    <td>{$channel[$order['channel']]}</td>
                    <td>{$order['order_source']}</td>
                    <td>{$order['invoice_status']}</td>
                    <td>{$order['invoice_time']}</td>
                </tr>

            <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <div class="row">
        <div class="col-sm-5">
            <div class="dataTables_info" id="data-table_info" role="status" aria-live="polite">
                共查询到 {$list->total()} 条数据
            </div>
        </div>
        <div class="col-sm-7">
            <div class="dataTables_paginate paging_simple_numbers" id="data-table_paginate">
                {$page}
            </div>
        </div>
    </div>

    <!-- BEGIN 订单详情-->
    <div class="modal fade" id="order_detail_modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <table id="order_detail" class="table">
                        <thead>
                        <tr>
                            <th class="text-center">订单状态</th>
                            <th class="text-center">修改人</th>
                            <th class="text-center">时间</th>
                        </tr>
                        </thead>
                        <tbody id="order_detail_body">

                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>
    <!-- END -->


    <!-- BEGIN 物流-->
    <!-- END -->
</div>
{/block}

{block name="script"/}

<script>


    var order_status = '';
    var order_statu_array = [{$query.order_status}];
    $('#multiselect').multiselect({
        buttonClass: 'btn-success btn-sm',
        buttonWidth: '100%',
        nonSelectedText: '请选择',
        onChange: function(option, checked){
            if(checked){
                order_statu_array.push(parseInt($(option).val()))
            }else {
                if($.inArray(parseInt($(option).val()),order_statu_array)!=-1){
                    order_statu_array.splice($.inArray(parseInt($(option).val()),order_statu_array),1);
                }
            }
            order_status = order_statu_array.join(',');
            $("input[name='order_status']").val(order_status);
        }
    });


    var channel = '';
    var channel_array = [{$query.channel}]
    $('#channel').multiselect({
        buttonClass: 'btn-success btn-sm',
        buttonWidth: '100%',
        nonSelectedText: '请选择',
        onChange: function(option, checked){
            if(checked){
                channel_array.push(parseInt($(option).val()))
                console.log(channel_array);
            }else {
                if($.inArray(parseInt($(option).val()),channel_array)!=-1){
                    channel_array.splice($.inArray(parseInt($(option).val()),channel_array),1);
                }
            }
            channel = channel_array.join(',');
            console.log(channel);
            $("input[name='channel']").val(channel);
        }
    });


    var order_source = '';
    var order_source_array = [{$query.order_source}]
    $('#order_source').multiselect({
        buttonClass: 'btn-success btn-sm',
        buttonWidth: '100%',
        nonSelectedText: '请选择',
        onChange: function(option, checked){
            if(checked){
                order_source_array.push(parseInt($(option).val()))
                console.log(order_source_array);
            }else {
                if($.inArray(parseInt($(option).val()),order_source_array)!=-1){
                    order_source_array.splice($.inArray(parseInt($(option).val()),order_source_array),1);
                }
            }
            order_source = order_source_array.join(',');
            console.log(order_source);
            $("input[name='order_source']").val(order_source);
        }
    });


    $(".reset").on("click",function (e) {
        $("#searchform").find("input:not([type='checkbox'])").val('');
        $.each($("#searchform").find("select.default-select2"),function () {
            $(this).find("option").eq(0).attr("selected",true);
        })
        $(".comm_type_id").find('option[value!=""]').remove();
        $(".default-select2").select2();
        daterange_ini("#default-daterange","YYYY-MM-DD");
        $('option', $('#multiselect')).each(function(element) {
            $('#multiselect').multiselect('deselect', $(this).val());
        });
    })

    $("a.show-order-detail").on("click",function () {
        var order_code = $(this).attr('data-id');
        $("#order_detail_modal .modal-header").find("h4").html("订单"+order_code);
        $.getJSON("{:url('order_static/ajaxGetOrderDetail')}",{order_code:order_code},function (resData) {
            if(resData.error == 0){
                var data = resData.data;
                console.log(data);
                var table_body = $("#order_detail_body");
                table_body.empty();
                for (var i=0;i<data.length;i++){
                    var tr = "<tr class='text-center'>";
                    tr+="<td>"+data[i].order_status+"</td>";
                    tr+="<td>"+data[i].modifier+"</td>";
                    tr+="<td>"+data[i].created_date+"</td>";
                    tr+="</tr>";
                    table_body.append($(tr));
                }
                $("#order_detail_modal").modal("show");
            }else {
                layer.msg(resData.msg);
            }
        })
    })

    $(".default-select2").select2();

    daterange_ini("#pay-time-daterange");
    daterange_ini("#invoice-daterange");
    $("#slt_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        $.get("{:url('ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">请选择</option>';
            $("#slt_three_comm_type_id").html(html);
            if(comm_parent_id == 0){
                $("#slt_sub_comm_type_id").html(html);return;
            }
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_sub_comm_type_id").html(html);
        },'json');

    }) ;

    $("#slt_sub_comm_type_id").on('change',function(){
        var comm_parent_id=$(this).val();
        if(comm_parent_id == 0){
            var html='<option value="0">请选择</option>';
            $("#slt_three_comm_type_id").html(html);return;
        }
        $.get("{:url('order_static/ajaxGetCommTypeId')}",{comm_parent_id:comm_parent_id},function(res){
            var html='<option value="0">请选择</option>';
            $.each(res.data,function(i,val){
                html+='<option value="'+val.id+'">'+val.comm_type_name+'</option>';
            });
            $("#slt_three_comm_type_id").html(html);
        },'json');
    }) ;



    $(".show-panel").on('click',function () {
        if($(this).find('i').hasClass('fa-angle-down')){
            $(this).html('收起<i class="fa fa-2x fa-angle-up"></i>');
            $("#panel").show();
        }else {
            $(this).html('展开<i class="fa fa-2x fa-angle-down"></i>');
            $("#panel").hide();
        }
    })

    $('table#main').on('click','.extra-column',function () {
        var span = "&nbsp;&nbsp;&nbsp;&nbsp;";
        var tr_obj=$(this).parents('tr');
        var i_obj = $(this).find("i");
        var order_code = $(this).attr("data-id");
        var order_comm_class = $(".order_comm_class_"+order_code);
        if(i_obj.hasClass("fa-plus")){
            //点击展开
            $.getJSON("{:url('order_static/ajaxGetOrderCommodity')}",{order_code:order_code},function (resData) {
                if(resData.error==0){
                    var data = resData.data;
                    var carstr = "";
                    for (var i=0;i<data.length;i++){
                        carstr = "车型："+data[i].car_info ;
                        if(data[i].adlrcode == 'nissan_jifen'){
                            carstr = "";
                        }
                        var priceData = '<tr class="order_comm_class_'+data[i].order_code+'">';
                        priceData+="<td ></td>";
                        priceData+="<td class='com-image' colspan='14' style='border: none'>";
                        priceData+="<img src='"+data[i].cover_image+"'/>"+"<a  class='init-commodity-preview' data-dlr-code='"+data[i].dlr_code+"' data-commodity-id='"+data[i].commodity_id+"'>"+data[i].commodity_name+"</a>"+span+data[i].sku_info+span+carstr+span+"单价："+data[i].price+span+"实付现金："+data[i].actual_use_money+span+"实付积分："+data[i].actual_point+span+"数量："+data[i].count+span+"总价："+(data[i].price*data[i].count)+span+"商品编码："+data[i].commodity_code+span+"规格编码："+data[i].sku_code+" "+data[i].limit+span+" 供应商:"+data[i].supplier;
                        priceData+="</td>";
                        priceData+="</tr>";
                        tr_obj.after(priceData);
                    }
                }else {
                    layer.msg(resData.msg);
                }
            })
            i_obj.removeClass("fa-plus")
            i_obj.addClass("fa-minus")
        }else {
            //点击缩放
            i_obj.removeClass("fa-minus")
            i_obj.addClass("fa-plus")
            order_comm_class.hide();
        }
    })

    $("#exp_data").click(function() {
        var url = "{:url('index')}";
        $("#is_down").val(1);
        var data = $("#searchform").serialize();
        window.location.href=url+"?is_down=1&"+data;
    })

    $("#ajax_exp_data").click(function() {
        var url = "{:url('index')}";
        $("#is_down").val(1);
        var data = $.param({is_ajax_down:1}) + '&' + $("#searchform").serialize()
        Custom.ajaxPost( url , data);
    })


</script>

{include file="order_delivery_static:wuliu" /}
{/block}
{block name="css"/}
<link rel="stylesheet" href="__STATIC__admin_v2/css/logistics/pages.css">

<style type="text/css">
    .com-image img{
        vertical-align: middle;
        max-width: 50px;
        max-height: 50px;
    }
    .table-scrollable {
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden;
        margin-bottom: 10px !important;
        border: none;
    }
    .table-scrollable th {
        min-width: 100%;
    }
    .table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th{
        vertical-align: middle;
    }
    td, th {
        padding: 0;
        white-space: nowrap;
    }
    .search-form .select2-container{
        width: 100%!important;
        margin-bottom: 0px!important;
    }
    /*.dndc-form-mod1 .form-spread>ul>li {*/
        /*width: 33%;*/
        /*box-sizing: border-box;*/
        /*padding: 5px;*/
        /*float: left;*/
    /*}*/

</style>
{/block}
