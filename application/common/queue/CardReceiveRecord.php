<?php

namespace app\common\queue;

use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbCardLog;
use app\common\model\db\DbJobsLog;
use app\common\net_service\NetUser;
use app\common\port\connectors\QuickWin;
use think\Log;
use think\queue\Job;

class CardReceiveRecord extends Base
{
    /**
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        trace('650,push info');
        $params         = json_decode($data, true);
        $data           = $params['data']; // 卡券中心的参数
        $recordId       = $data['id'] ?? 0; // 卡券领取记录表主键id
        $coupon_code    = $data['coupon_code'] ?? '';
        $userId         = $data['user_id'];

        $jobsLog        = new DbJobsLog();
        $card_r_r_model = new BuCardReceiveRecord();
        $card_log_model = new DbCardLog();


        $str = 'event_type:'.$data['event_type'];
        $add = [
            'queue'       => 'card_receive',
            'source_type' => $coupon_code,
            'data_info'   => json_encode_cn($data['data']),
            'modifier' => $str,
        ];
        $jobId = $jobsLog->insertGetId($add);

        try {

            if ($data['event_type'] == 1) { // 领取
                $cards   = $card_r_r_model->where(['id' => $data['id']])->find();
                if (!empty($cards['coupon_code'])) return;
                // 领券
                $ret = QuickWin::create('quick_win')->postCouponReceive($data['data']);
                if($ret['result'] == '1') {
                    getRedisLock('coupon_code_' . $ret['rows'], 300);
                    $card_r_r_model->where('id', $data['id'])->update(['coupon_code' => $ret['rows']]);

                    if($cards['status'] == BuCardReceiveRecord::STATUS_KEY5){
                        $coupon_receive = QuickWin::create('quick_win')->getCouponReceiveRecord([
                            'coupon_code' => $ret['rows'],
                            'request_channel' => 1
                        ]);
                        $order = BuOrder::get(['id' => $cards['act_id']]);
                        $quickWinData = [
                            "business_order_no" => $order['order_code'] ?? '',
                            "coupon_code" => $ret['rows'],
                            "coupon_receive_id" => $coupon_receive['rows'][0]['id'] ?? 0
                        ];
                        $ret5 = QuickWin::create('quick_win')->postCouponFreezeCoupon($quickWinData);
                        $is_success = $ret5['result'] == '1' ? 1 : 0;
                        $card_log = [
                            'user_id'       => $cards['user_id'],
                            'event_type'    => 5,
                            'is_success'    => $is_success,
                            'card_id'       => $cards['card_id'],
                            'request_info'  => json_encode_cn($quickWinData),
                            'response_info' => json_encode_cn($ret5),
                        ];
                        $card_log_model->insertGetId($card_log);
                    }
                }
            }
            else {
                // 查询领券记录
                $coupon_receive = QuickWin::create('quick_win')->getCouponReceiveRecord(['coupon_code' => $coupon_code, 'request_channel' => 1]);

                $ret = ['result' =>0]; // 初始化
                // 核销
                if ($data['event_type'] == 2) {

                    $data['data'][0]['coupon_receive_id'] = $coupon_receive['rows'][0]['id'];

                    $ret = QuickWin::create('quick_win')->postCouponConsume($data['data']);

                    if ($ret['result'] == 1) {
                        $upd = [
                            'coupon_receive_id'    => $coupon_receive['rows'][0]['id'],
                            'quick_win_is_consume' => 1,
                            'consume_date'         => date('Y-m-d H:i:s'),
                            'last_updated_date'    => date('Y-m-d H:i:s'),
                            'modifier'             => 'consumeJob'
                        ];
                        $card_r_r_model->where('id', $recordId)->update($upd);

                    } else {
//                        // 卡券支付失败
//                        $card_code = $card_r_r_model->where('id', $data['id'])->value('card_code');
//                        // 查询主订单
//                        $where[]           = ['exp', "FIND_IN_SET('{$card_code}',card_codes)"];
//                        $parent_order_code = BuOrderCommodity::where($where)->value('parent_order_code');
//                        if (!empty($parent_order_code)) {
//                            $order_model = new BuOrder();
//                            $order_model->where('order_code', $parent_order_code)->update(['is_cc_ok' => 2]);
//                        }
                        $order_model = new BuOrder();
                        $order_model->where('order_code', $data['data'][0]['business_order_no'])->update(['is_cc_ok' => 2]);

                    }
                }

                // 失效
                if ($data['event_type'] == 3) {
                    $data['data']['receive_id'] = $coupon_receive['rows'][0]['id'];
                    $ret = QuickWin::create('quick_win')->postCouponInvalid($data['data']);
                }
                // 复活
                if ($data['event_type'] == 4) {
                    $data['data']['receive_id'] = $coupon_receive['rows'][0]['id'];
                    $ret = QuickWin::create('quick_win')->postCouponRevive($data['data']);
                }

                $is_success = $ret['result'] == 1 ? 1 : 0;

                $card_log = [
                    'user_id'       => $userId,
                    'event_type'    => $data['event_type'],
                    'is_success'    => $is_success,
                    'card_id'       => $data['card_id'],
                    'request_info'  => json_encode_cn($data['data']),
                    'response_info' => json_encode_cn($ret),
                    'created_date'  => date('Y-m-d H:i:s'),
                    'last_updated_date' => date('Y-m-d H:i:s'),
                ];
                $card_log_model->insertGetId($card_log);

            }

            $upd = ['result_info' => json_encode_cn($ret)];
            $jobsLog->where('id', $jobId)->update($upd);

            $job->delete();
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $upd = ['result_info' => $msg];
            $jobsLog->where('id', $jobId)->update($upd);
            Log::error('card receive : ' . $msg);
            $job->attempts() > $this->_attempts ? $job->delete() : $job->release($this->_release_time);
        }
        trace('650,end card receive info');
    }

}
