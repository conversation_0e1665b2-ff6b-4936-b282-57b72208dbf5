<?php


namespace app\common\queue;


use app\common\model\bu\BuOrder;
use app\common\model\db\DbJobsLog;
use app\common\service\PushMaintainService;
use think\queue\Job;
use tool\Logger;

class PushOrder extends Base
{
    /**
     * @param Job $job
     * @param $data
     */
    public function fire(Job $job, $data)
    {
        trace('500,start PushOrder');
        Logger::info('PushOrder:',$data);
        $data = json_decode($data, true);
        $maintain = new PushMaintainService();
        $job_model = new DbJobsLog();
        try {
            // 同步订单
            // 到店类商品
            $map = [
                'id' => $data['order_id'],
                'order_source' => ['in', [15, 16, 17, 18, 19, 20, 21, 22, 23, 40, 43]],
                'parent_order_type' => ['in', [1,2]],  // 1原单2子单
            ];
            // 更改判断「到店类」订单条件
            $order_model = new BuOrder();
            $orderInfo = $order_model->where($map)->find();
            if (!empty($orderInfo)) {
                $log = [
                    'queue'       => 'push_order',
                    'source_type' => $orderInfo['order_code'],
                ];
                $logId = $job_model->insertGetId($log);


                $re = $maintain->messageReceive($orderInfo);
                $upd = [
                    'last_updated_date' => date('Y-m-d H:i:s'),
                    'data_info'   => json_encode_cn($re['params']),
                    'result_info' => json_encode_cn($re['return']),
                ];
                $job_model->where('id', $logId)->update($upd);
            }

            $job->delete();
        } catch (\Exception $e) {
            Logger::error('PushOrder : ' . $e->getMessage());
            $job->attempts() > $this->_attempts ? $job->delete() : $job->release($this->_release_time);
        }
        trace('500,end PushOrder');
    }

}