<?php


namespace app\common\port\connectors;


use tool\Logger;

class E3spRefactor extends Connect
{

    private $prefix;
    private $prefix_new;
    private $options;

    public function __construct()
    {

        $config        = config('port')['e3sp_refactor'];
        $gVersion      = $config['gversion'];
        $this->prefix  = $config['prefix'];
        $this->prefix_new  = $config['prefix_2'];
        $this->options = [];
        if (!empty($gVersion)) {
            $this->options['headers']['gversion'] = $gVersion;
        }

    }

    private function queryParams($cmd, $params)
    {
        $query['requestHead'] = ['mainCmd' => $cmd, 'requSeq' => time()];
        $query['requestBody'] = ['entityInfo' => $params];
        return [
            'data' => $query,
        ];
    }




    /**
     * 时间容量接口
     * @param $params
     * @return array|mixed|string|
     */
    public function dlrTimetable($params)
    {
        $url = $this->prefix.'refactor/e4s/api/ssa/base/v1/dlrTimetable';
        $data = $this->queryParams('SSA00001', $params);
        $result = $this->postJson($url, $data, $this->options);
        Logger::info('dlr-time-table', json_encode(['params'=>$params, 'return'=>$result]));
        return $result;
    }



    /**
     * 提交预约单
     * @param $params
     * @return array|mixed|string
     */
    public function createBooking($params)
    {
        $url = $this->prefix.'refactor/e4s/api/ssa/booking/v1/create';
        $data = $this->queryParams('SSA00006', $params);
        $result = $this->postJson($url, $data, $this->options);
        Logger::info('create-booking', json_encode(['params'=>$params, 'return'=>$result]));
        return $result;
    }


    /**
     * 取消预约单
     * @param $params
     * @return array|mixed|string
     */
    public function cancelBooking($params)
    {
        $url = $this->prefix.'refactor/e4s/api/ssa/booking/v1/cancel';
        $data = $this->queryParams('SSA00007', $params);
        $result = $this->postJson($url, $data, $this->options);
        Logger::info('cancel-booking', json_encode(['params'=>$params, 'return'=>$result]));
        return $result;
    }


    /**
     * 读取车辆信息及里程数
     * @param $params
     * @return array|mixed|string
     */
    public function getCarInfo($params)
    {
        $url = $this->prefix.'refactor/e4s/api/ssa/base/v1/car/owner/info';
        $data = $this->queryParams('SSA00015', $params);
        $result = $this->postJson($url, $data, $this->options);
        Logger::info('e3sp-get-car-info', json_encode(['params'=>$params, 'return'=>$result]));
        return $result;
    }


    /**
     * 主表
     * @param $params
     * @return array|mixed|string|
     */
    public function toIndex($params)
    {
        $url = $this->prefix.'refactor/e3s-egt/postdata/DNDC_ONLINESHOP/DNDC_RECEIVE_MAIN';
        $result = $this->postJson($url, $params, $this->options);
        Logger::info('e3sp-to-index', json_encode(['params'=>$params, 'return'=>$result]));
        return $result;
    }


    /**
     * 详情
     * @param $params
     * @return array|mixed|string
     */
    public function toDetail($params)
    {
        $url = $this->prefix.'refactor/e3s-egt/postdata/DNDC_ONLINESHOP/DNDC_RECEIVE_DETAIL';
        $result = $this->postJson($url, $params, $this->options);
        Logger::info('e3sp-to-detail', json_encode(['params'=>$params, 'return'=>$result]));
        return $result;
    }


    /**
     * 退款
     * @param $params
     * @return array|mixed|string
     */
    public function toRefund($params)
    {
        $url = $this->prefix.'refactor/e3s-egt/postdata/DNDC_ONLINESHOP/DNDC_RECEIVE_STATUS';
        $result = $this->postJson($url, $params, $this->options);
        Logger::info('e3sp-to-refund', json_encode(['params'=>$params, 'return'=>$result]));
        return $result;
    }


    /**
     * 获取车辆维护信息
     * @param $params
     * @return array|mixed|string
     */
    public function getMaintainInfo($params)
    {
        $url = $this->prefix.'refactor/e3s-egt/postdata/DNDC_ONLINESHOP/DNDC_GET_MAINTAIN_INFO';
        $result = $this->postJson($url, $params, $this->options);
        Logger::info('e3sp-get-maintain-info', json_encode(['params'=>$params, 'return'=>$result]));
        return json_encode($result);
    }


    /**
     * 获取车辆
     * @param $params
     * @return array|mixed|string
     */
    public function getCarAge($params)
    {
        $url = $this->prefix.'refactor/e3s-egt/postdata/DNDC_ONLINESHOP/DNDC_GET_CAR_AGE';
        $result = $this->postJson($url, $params, $this->options);
        Logger::info('e3sp-get-car-age', json_encode(['params'=>$params, 'return'=>$result]));
        return json_encode($result);
    }

    /**
     * 获取车型数据
     * @param $params
     * @return false|string
     */
    public function getCarSList($params)
    {
        $url = $this->prefix_new.'ly/busiplat/xapi/vehicle/normal/restsign/DNDC/DNDC_NORMAL_R_VE_R031';
        $result = $this->postJson($url, $params, $this->options);
        Logger::info('e3sp-getcarlist', json_encode(['params'=>$params, 'return'=>$result]));
        return $result;
    }
}
