<?php


namespace app\common\service;


use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderSubscribe;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityType;
use app\common\model\db\DbDlr;
use app\common\net_service\NetCart;
use tool\Logger;

class PushMaintainService
{


    /**
     * 订单同步
     * @param $orderInfo
     * @return array|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function messageReceive($orderInfo)
    {
        $dlr_model = new DbDlr();
        // 卡券
        $card_names = '';
        if (!empty($orderInfo['card_id'])) {
            $cardIds = explode(',', $orderInfo['card_id']);
            $db_card_model = new DbCard();
            $cardNameArr = $db_card_model->whereIn('id', $cardIds)->column('card_name');
            $card_names = implode(',', $cardNameArr);
        }
        $map = ['dlr_code' => $orderInfo['dd_dlr_code']];
        $dlrInfo = $dlr_model->where($map)->order('id desc')->find();
        $maintainService = new \app\common\net_service\MaintainService();

        // 订单实付现金
        $money = bcadd($orderInfo['money'], $orderInfo['pre_use_money'], 2);
        // 订单实付积分
        $integral = bcadd($orderInfo['integral'], $orderInfo['pre_point']);
        // 订单实付金额
        $orderPrice = bcadd($money, bcdiv($integral, 10, 2), 2);
        $list = $this->commodityList($orderInfo['order_code'], $orderInfo['order_status']);
        $order_commodity = '';
        $act_names = '';
        if (!empty($list)) {
            $arr = array_column($list, 'COMMODITY_NAME');
            $order_commodity = implode(',', $arr);
            $actArr = array_column($list, 'ACT');
            $act_names = implode(',', array_unique($actArr));
        }

        $map = [
            'order_code' => $orderInfo['order_code'],
            'subscribe_status' => ['neq', 4],
        ];
        $subscribe = BuOrderSubscribe::where($map)->order('id', 'desc')->find();
        // 订单预约表
        $is_subscribe = 0;
        if (!empty($subscribe)) {
            $is_subscribe = 1;
        }
        // 退款时间
        $return_money_time = '';
        $order_bt_money = bcadd($orderInfo['act_sett_money'], $orderInfo['card_sett_money'], 2);
        // 退款状态
        if ($orderInfo['order_status'] == 18) {
            $re = DbAfterSaleOrders::where('order_id', $orderInfo['id'])->order('id', 'desc')->find();
            $return_money_time = $re['refund_delivery_at'] ?? '';
            $order_bt_money = '';
        }

        $ic_card_no = ($orderInfo['order_vin'] == $orderInfo['vin']) ? $orderInfo['license_plate'] : '';
        // 售后状态
        $afs_status = '';
        $map = ['order_id' => $orderInfo['id']];
        $aftInfo = DbAfterSaleOrders::where($map)->field('id,order_id,afs_status')->find();
        if (!empty($aftInfo)) {
            $afs_status = $aftInfo['afs_status'];
        }
        // 判断物流方式
        if ($orderInfo['logistics_mode'] == 2) {
            // 快递
            // 日产、PZ1A商城送 T9932， 数据公司(日产）
            if (in_array($orderInfo['brand'], [1, 2])) {
                $dlr_code = 'T9932';
                $dlr_name = '数据公司(日产)';
            } else {
                $dlr_code = 'V0001';
                $dlr_name = '数据公司(启辰)';
            }
        } else {
            $dlr_code = $orderInfo['dd_dlr_code'];
            $dlr_name = $dlrInfo['dlr_name'] ?? '';
        }


        $data = [
            'ORDER_CODE' => $orderInfo['order_code'],
            'ORDER_STATUS' => $orderInfo['order_status'],
            'CHANNEL' => $orderInfo['channel'],
            'DLR_CODE' => $dlr_code,
            'DLR_NMAE' => $dlr_name,
            'CREATED_ORDER_DATE' => $orderInfo['pay_time'],
            'DQ_TIME' => $orderInfo['dq_time'],
            'DELIVERY_TIME' => $orderInfo['delivery_time'],
            'VIN' => $orderInfo['order_vin'] ?? '',
            'IC_CARD_NO' => $ic_card_no,
            'NAME' => $orderInfo['name'],
            'PHONE' => $orderInfo['phone'],
            'IS_SUBSCRIBE' => $is_subscribe,
            'SUBSCRIBE_CODE' => !empty($is_subscribe) ? ($subscribe['reservation_code'] ?? '') : '',
            'SUBSCRIBE_TIME' => !empty($is_subscribe) ? ($subscribe['subscribe_time'] ?? '') : '',
            'ORDER_COMMODITY' => $order_commodity,
            'LOGISTICS_MODE' => $orderInfo['logistics_mode'],
            'TOTAL_MONEY' => bcadd($orderInfo['b_act_goods_price'], $orderInfo['b_work_time_price'], 2), // 订单原价
            'ORDER_PRICE' => $orderPrice,
            'PAYMENT_METHOD' => $orderInfo['payment_method'],
            'MONEY' => $money,
            'INTEGRAL' => $integral,
            'ORDER_YH_MONEY' => bcadd($orderInfo['all_act_yh'], $orderInfo['all_card_yh'], 2),
            'CARD_NAMES' => $card_names,
            'ACT_NAMES' => $act_names,
            'ORDER_BT_MONEY' => $order_bt_money,
            'PAYMENT_ORDER_CODE' => $orderInfo['link_order_code'],
            'SETTLEMENT_STATE' => $orderInfo['settlement_state'] ?? 0,
            'RETURN_MONEY_TIME' => $return_money_time,
            'AFS_STATUS' => $afs_status,
            'ORDER_SOURCE' => $orderInfo['order_source'],
            'ORDER_SOURCE_NAME' => BuOrder::orderBigType($orderInfo['order_source']),
            'LIST' => $list
        ];
        $order_commodity_model = new BuOrderCommodity();

        // 保养套餐-五年双保专享心悦套餐
        if ($orderInfo['order_source'] == 40) {
            $field = 'is_sb,re_time';
            $where = ['order_code'=>$orderInfo['order_code']];
            $order_commodity = $order_commodity_model->where($where)->field($field)->find();
            $data['IS_WNSB'] = $order_commodity['is_sb'];
            $data['REMAINQTY'] = $order_commodity['re_time'];
        }

//        //若订单状态为19-交易关闭，需清空 ORDER_BT_MONEY 和 COMMO_BT_MONEY 字段值再传
//        if ($data['ORDER_STATUS'] == 19) {
//            $data['ORDER_BT_MONEY'] = '';
//        }


        $re = $maintainService->messageReceive($data);
        $result = ['params' => $data, 'return' => $re->getData()];
        if (!$re->isSuccess()) {
            Logger::error('PushOrder:', $re->getMessage());
        }

        return $result;
    }


    /**
     * @param $orderCode
     * @param $orderStatus
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function commodityList($orderCode, $orderStatus)
    {
        $order_commodity_model = new BuOrderCommodity();
        $commodity_model = new DbCommodity();
        $commodity_type_model = new DbCommodityType();
        $data = [];
        $map = [
            'order_code' => $orderCode,
            'mo_id' => 0
        ];
        $list = $order_commodity_model->where($map)->select();

        $card_sett_data = [];
        foreach ($list as $item) {
            $commodityField = 'comm_type_id,commodity_code';
            $commodity = $commodity_model->where('id', $item['commodity_id'])->field($commodityField)->find()->toArray();
            $commTypeName = $commodity_type_model->where('id', $commodity['comm_type_id'])->value('comm_type_name');

            // 商品活动优惠金额
            if ($item['mo_sub_id'] == 0) {
                // 普通商品
                $order_goods_yh_money = bcsub($item['all_dis'], bcmul($item['work_time_dis'], $item['count'], 2), 2);
                $count = $item['count'];
                // 商品实付金额
                $act_price = bcsub(bcmul($item['actual_price'], $count, 2), $item['card_all_dis'], 2);
            } else {
                // 子商品
                $order_goods_yh_money = bcsub($item['all_dis'], $item['work_time_dis'], 2);
                $map = ['order_code' => $item['order_code'], 'mo_id' => $item['mo_sub_id']];
                $zCount = $order_commodity_model->where($map)->value('count');
                $count = bcmul($zCount, $item['count']); // 主商品数量*子商品数量
                // 商品实付金额
                $act_price = bcsub(bcmul($item['actual_price'], $zCount, 2), $item['card_all_dis'], 2);
            }

            if ($act_price < 0) {
                $act_price = 0;
            }


            $is_payment = 0; // 未支付
            $actual_hour_name = ''; // 商品工时编码
            $hour_money = 0; // 工时原价
            $payment_money = 0; // 工时实付金额
            $hour_card_money = 0; // 工时优惠金额

            if (!empty($item['work_time_json'])) {
                $workTimeArr = json_decode($item['work_time_json'], true);
                $work_time_number = $workTimeArr['work_time_number'] ?? 0;
                if (!empty($work_time_number)) {
                    $is_payment = 1;
                    $actual_hour_name = $workTimeArr['work_time_code'];
                    if ($item['mo_sub_id'] == 0) {
                        // 普通商品
                        $hour_money = bcmul($item['work_time_money'], $item['count'], 2);
                        $payment_money = bcmul($item['work_time_actual_money'], $item['count'], 2);
                        $hour_card_money = bcmul($item['work_time_dis'], $item['count'], 2);
                    } else {
                        // 子商品
                        $hour_money = $item['work_time_money'];
                        $payment_money = $item['work_time_actual_money'];
                        $hour_card_money = $item['work_time_dis'];
                    }

                }
            }
            if ($payment_money < 0) {
                $payment_money = 0;
            }

            $act = '';  // 商品参与活动
            $hour_act_name = '';  // 工时活动名称
            // act_type  1-商品、2-工时、3商品+工时
            if ($item['act_type'] == 1) {
                $act = $item['act_name'];
            }
            if ($item['act_type'] == 2) {
                $hour_act_name = $item['act_name'];
            }

            if ($item['act_type'] == 3) {
                $act = $item['act_name'];
                $hour_act_name = $item['act_name'];
            }

            //若订单状态为18-交易关闭，需清空 ORDER_BT_MONEY 和 COMMO_BT_MONEY 字段值再传
            $commo_bt_money = bcadd($item['act_sett_money'], $item['card_sett_money'], 2);
            if ($orderStatus == 18) {
                $commo_bt_money = '';
            }


            $data[] = [
                'ORDER_COMMODITY_CODE' => $item['commodity_set_id'],
                'COMMODITY_NAME' => $item['commodity_name'],
                'COMM_TYPE_NAMES' => $commTypeName,
                'THIRD_SKU_CODE' => $item['third_sku_code'],
                'SKU_INFO' => !empty($item['sku_info']) ? $item['sku_info'] : '空',
                'COUNT' => (int)$count,
                'PRICE' => $item['price'],
                'ACT_PRICE' => $act_price,  // 商品实付金额
                'ACTUAL_USE_MONEY' => '', // 商品实付现金
                'ACTUAL_POINT' => '', // 实付积分
                'ACT' => $act,
                'COMMO_BT_MONEY' => $commo_bt_money,
                'ORDER_GOODS_YH_MONEY' => ($order_goods_yh_money != 0) ? $order_goods_yh_money : '', // 商品活动优惠金额
                'ACTUAL_CARD_MONEY' => ($item['card_all_dis'] != 0) ? $item['card_all_dis'] : '', // 商品优惠券金额
                'IS_PAYMENT' => $is_payment,
                'ACTUAL_HOUR_NAME' => $actual_hour_name,
                'HOUR_MONEY' => ($hour_money != 0) ? $hour_money : '',
                'PAYMENT_MONEY' => ($payment_money != 0) ? $payment_money : '',
                'HOUR_CARD_MONEY' => ($hour_card_money != 0) ? $hour_card_money : '',
                'HOUR_ACT_NAME' => $hour_act_name,
            ];

            // 卡券补贴数据
            if ($orderStatus == 19) {
                $sett_data = json_decode($item['card_sett_money_info'], true) ?? [];
                foreach ($sett_data as $card_code => $sett_money) {
                    $re = array_key_exists($card_code, $card_sett_data);
                    if ($re) {
                        $card_sett_data[$card_code] = $card_sett_data[$card_code] + $sett_money;
                    } else {
                        $card_sett_data = array_merge($card_sett_data, [$card_code=>$sett_money]);
                    }

                }
            }

        }

        // 订单核销  下发卡券中心结算信息
        if ($orderStatus == 19 && !empty($card_sett_data)) {
            $card_record_model = new BuCardReceiveRecord();
            $net_cart = new NetCart();
            foreach ($card_sett_data as $card_code => $sett_money) {
                $map = ['card_code'  => $card_code];
                $coupon_code = $card_record_model->where($map)->value('coupon_code');
                $params = [
                    'order_code' => $orderCode,
                    'coupon_code'  => $coupon_code,
                    'sett_money' => $sett_money,
                ];
                // 通知卡券中心
                $net_cart->saveSubsidy($params);
            }
        }

        return $data;
    }
}
