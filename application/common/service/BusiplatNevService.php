<?php


namespace app\common\service;


use app\common\model\db\DbLog;
use app\common\model\db\DbArea;
use app\common\model\db\DbUser;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\port\connectors\BusiplatNevDc;

class BusiplatNevService
{


    /**
     * 日产充电桩通知联友
     * @param $orderCode
     * @return array|mixed|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function create($orderCode)
    {
        $chargingModel = 'B96AM24003KS-D300';
        $serviceCode   = 'XNCDZAZ244KS-D300';
        $field         = 'id,order_code,user_id,order_status,brand,address_area_ids,name,phone,receipt_address,remark';
        $order_model   = new BuOrder();
        $orderInfo     = $order_model->where('order_code', $orderCode)->field($field)->find();
        // 订单商品
        $field                 = 'commodity_name,sku_info,third_sku_code,price';
        $order_commodity_model = new BuOrderCommodity();
        $map                   = [
            'order_code'     => $orderInfo['order_code'],
            'third_sku_code' => $chargingModel,
            'mo_id' => 0, // 普通商品或者子品
        ];
        $order_commodity       = $order_commodity_model->where($map)->field($field)->find();

        // 用户信息
        $user_filed = 'one_id,name,phone,mid_phone';
        $user_model = new DbUser();
        $userInfo   = $user_model->where('id', $orderInfo['user_id'])->field($user_filed)->find();

        $address_area_ids = explode(',', $orderInfo['address_area_ids']);
        $area_model       = new DbArea();
        $province_name    = $area_model->where('area_id', $address_area_ids[0])->value('area_name') ?? '';
        $city_name        = $area_model->where('area_id', $address_area_ids[1])->value('area_name') ?? '';
        $county_name      = $area_model->where('area_id', $address_area_ids[2])->value('area_name') ?? '';


        $params     = [
            'carshopOrderCode'       => $orderInfo['order_code'], // 订单号
            'carshopOrderStatus'     => $orderInfo['order_status'], // 订单状态编码
            'carshopOrderStatusName' => BuOrder::orderStatus($orderInfo['order_status']), // 订单状态名称
            'carBrandCode'           => $orderInfo['brand'], // 品牌编码
            'carBrandCn'             => BuOrder::brandText($orderInfo['brand']),// 品牌名称
            'chargeTypeCode'         => '2', // 充电桩类型编码  1、随车赠送；2、商城购买；0：不赠桩
            'chargeTypeName'         => '商城购买', // 充电桩类型名称 1、随车赠送；2、商城购买；0：不赠桩
            'chargingName'           => $order_commodity['commodity_name'], // 充电桩名称
            'chargingModel'          => $order_commodity['third_sku_code'], // 充电桩型号
            'chargeModelServiceCode' => $serviceCode, // 充电桩安装服务件号
            'chargeSalePrice'        => $order_commodity['price'], // 充电桩价格
            'oneid'                  => $userInfo['one_id'], // 注册人ONEID
            'registName'             => $userInfo['name'], // 注册人姓名
            'registPhone'            => $userInfo['phone'], // 注册人手机号
            'carCustPhone'           => $userInfo['mid_phone'], // 车主手机号
            'custName'               => $orderInfo['name'], // 购车人姓名
            'custPhone'              => $orderInfo['phone'], // 购车人手机号
            'provinceId'             => $address_area_ids[0], // 购车人省份编码
            'provinceName'           => $province_name, // 购车人省份
            'cityId'                 => $address_area_ids[1], // 购车人城市编码
            'cityName'               => $city_name, // 购车人城市
            'countyId'               => $address_area_ids[2], // 购车人区域编码
            'countyName'             => $county_name, // 购车人区域
            'detailAddr'             => $orderInfo['receipt_address'], // 购车人详细地址
            'remark'                 => $orderInfo['remark'] ?? '', // 备注
        ];
        $log_model  = new DbLog();
        $add        = [
            'type'       => 'create_nev_charge',
            'send_note'  => json_encode_cn($params),
            'order_code' => $orderInfo['order_code']
        ];
        $logId      = $log_model->insertGetId($add);
        $result     = BusiplatNevDc::create('pz1a')->infoApplySave($params);
        $is_success = 0;
        if (isset($result['result']) && $result['result'] == 1) {
            $is_success = 1;
        }
        $upd = [
            'is_success'   => $is_success,
            'receive_note' => json_encode_cn($result)
        ];
        $log_model->where('id', $logId)->update($upd);
        return $result;
    }


    /**
     * 日产充电桩售后
     * @param $orderCode
     * @return array|mixed|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function after($orderCode)
    {
        $field       = 'id,order_code,order_status';
        $order_model = new BuOrder();
//        $orderInfo   = $order_model->where('order_code', $orderCode)->field($field)->find();

        $params     = [
            'carshopOrderCode'       => $orderCode, // 订单号
            'carshopOrderStatus'     => 18, // 订单状态编码
            'carshopOrderStatusName' => BuOrder::orderStatus(18),
            'cancelReasonCode'       => 5,
            'cancelReason'           => '商城订单退款',
        ];
        $log_model  = new DbLog();
        $add        = [
            'type'       => 'after_nev_charge',
            'send_note'  => json_encode_cn($params),
            'order_code' => $orderCode
        ];
        $logId      = $log_model->insertGetId($add);
        $result     = BusiplatNevDc::create('pz1a')->infoApplyUpdate($params);
        $is_success = 0;
        if (isset($result['result']) && $result['result'] == 1) {
            $is_success = 1;
        }
        $upd = [
            'is_success'   => $is_success,
            'receive_note' => json_encode_cn($result)
        ];
        $log_model->where('id', $logId)->update($upd);
        return $result;
    }


}