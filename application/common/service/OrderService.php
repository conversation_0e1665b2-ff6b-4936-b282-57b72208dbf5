<?php


namespace app\common\service;


use app\admin_v2\service\InvoiceApplyService;
use app\common\model\bu\BuOrder;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDlrInvoice;
use app\common\model\db\DbLyPaySettle;
use app\common\model\db\DbOrderInvoice;
use app\common\model\db\DbTaxOrderInvoice;
use app\common\model\db\DbUser;
use app\common\model\db\InvoiceApplyDetailModel;
use app\common\model\db\InvoiceApplyModel;
use app\common\model\db\InvoiceRecordModel;
use app\common\net_service\ConsumeRule;
use app\common\return_data\JsonBuilder;
use app\common\return_data\MessageBag;
use think\Exception;
use think\Queue;
use tool\Logger;

class OrderService
{


    /**
     * 结算回调
     * @param $callback
     * @param $settleInfo
     * @return MessageBag
     */
    public function settCallback($callback, $settleInfo)
    {
        $messageBag = new MessageBag();
        try {

            $upd = [
                'settle_status'     => $callback['settleState'] ?? '',
                'settle_state_name' => $callback['settleStateName'] ?? '',
                'settle_amt'        => $callback['settleAmt'] ?? '',
                'fee_amt'           => $callback['feeAmt'] ?? '',
                'settle_time'       => $callback['settleTime'] ?? '',
                'bank_code'         => $callback['bankCode'] ?? '',
                'bank_name'         => $callback['bankName'] ?? '',
            ];
            $map = ['id' => $settleInfo['id']];
            DbLyPaySettle::where($map)->update($upd);

            // 判断当前订单是否还有其他的结算
            $map = [
                'settle_status'  => ['neq', 'S2'],  // S2结算成功
                'sub_order_code' => $callback['subOrderCode'],
                'is_enable'      => 1
            ];
            $re  = DbLyPaySettle::where($map)->find();

            if (empty($re)) {
                if ($callback['settleState'] == 'S2') {
                    $status = 1; // 成功
                } else if ($callback['settleState'] == 'S3') {
                    $status = 2; // 失败
                } else {
                    $status = 3; // 分帐中
                }

                // 同步订单主表
                $upd = [
                    'settlement_time'  => $callback['settleTime'] ?? '',
                    'settlement_state' => $status, // 结算成功
                    'last_updated_date' => date('Y-m-d H:i:s'),
                ];
                BuOrder::where('order_code', $callback['subOrderCode'])->update($upd);

                // 全部结算成功同步e3s订单
                $orderInfo = BuOrder::where('order_code', $callback['subOrderCode'])->find();



                // 结算成功 赠送积分
                if ($status == 1) {
                    $service = new ConsumeRule();
                    $service->addPoint($callback['subOrderCode']);
//                    // 插入待开票信息表  后续查询待开票用这个表
//                    $this->addOrderInvoice($orderId);
//                    //结算成功自动开票
//                    $this->autoInvoice($orderId,$callback['subOrderCode']);
                    // 通过队列请求
                    if (empty($orderInfo['dd_dlr_code'])) {
                        Queue::push('app\admin_v2\queue\OrderSettleAfter', json_encode($orderInfo), config('queue_type.order_settle_after'));
                    }

                }

                Queue::push('app\common\queue\PushOrder', json_encode(['order_id' => $orderInfo['id']]), config('queue_type.push_order'));

            }

            return $messageBag;
        } catch (Exception $e) {
            $msg = $e->getMessage();
            Logger::error('cron getSettleDetail:' . $msg);
            $messageBag->setCode(JsonBuilder::CODE_ERROR);
            $messageBag->setMessage($msg);
            return $messageBag;
        }
    }


    /**
     * 添加待开票记录
     * @param $orderInfo
     * @return int|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function addOrderInvoice($orderInfo)
    {
        $user_model          = new DbUser();
        $mid_phone           = $user_model->where('id', $orderInfo['user_id'])->value('mid_phone')?? '';
        $order_invoice_model = new DbTaxOrderInvoice();
        // 判断是否存在
        $map = [
            'order_code' => $orderInfo['order_code'],
        ];
        $info = $order_invoice_model->where($map)->find();
        if (empty($info)) {
            $add                 = [
                'order_code'  => $orderInfo['order_code'],
                'order_date'  => $orderInfo['created_date'],
                'settle_date' => $orderInfo['settlement_time'],
                'money'       => $orderInfo['money'],
                'name'        => $orderInfo['name'],
                'phone'       => $orderInfo['phone'],
                'mid_phone'   => $mid_phone,
                'user_id'     => $orderInfo['user_id'],
            ];
            return $order_invoice_model->insertGetId($add);
        } else {
            return $info['id'];
        }
    }





    /**
     * 结算自动开票
     * @param $orderInfo
     * @return array|string
     */
    public function autoInvoice($orderInfo){
        try {
            $orderInvoiceObj = new DbOrderInvoice();
            $orderObj = new BuOrder();
            //$recordModel =  new InvoiceRecordModel();
            //$orderInfo = $orderObj->where(['id'=>$orderId])->find();
            $invoiceApplyDetailModel = new InvoiceApplyDetailModel();
            $recordInfo = $invoiceApplyDetailModel->alias("a")->join("t_db_tax_invoice_record b","a.apply_id = b.apply_id")
                                                  ->where(['b.invoice_status'=>3007,'b.is_red_invoice'=>0,'a.good_order_no'=>$orderInfo['order_code']])->find();

            $orderId = $orderInfo['id'];
            if(empty($recordInfo)){
//                $mainOrder = $orderObj->getMainOrderId($orderId);
               // $invoiceinfo = $orderInvoiceObj->where(['order_id'=>$mainOrder['order_id']])->find();
                $invoiceinfo = $orderInvoiceObj->where(['order_id'=>$orderId])->find();
              //  dd($invoiceinfo);
               // $orderId = $mainOrder['order_id'] ;
                if(empty($invoiceinfo)){
//                    $orderInfo = $orderObj->alias("a")
//                        ->where(['a.settlement_state'=>1,'a.id'=>$orderId ])
//                        ->field("a.*")->find();

//                    $spitOrder = $orderObj->where(['id'=>$orderId,'parent_order_type'=>2])->find();
//                    if(!empty($spitOrder)){
//                        $suborderInfo = $orderObj->where(['order_code'=>$spitOrder['parent_order_code']])->find();
//                        $orderInfo['dlr_code'] = $suborderInfo['order_code'];
//                    }

                        $dbDlrObj = new DbDlr();
                        $orderInfo['dlr_code'] = "GWSM";
                        $gwsmDlrInfo = $dbDlrObj->alias("c")->join("t_db_dlr_invoice b","b.dlr_id=c.id")
                            ->where(['c.dlr_code'=>$orderInfo['dlr_code']])
                            ->field("b.id as dlr_id,b.bank,b.bank_account,b.register_address,b.register_mobile")->find();

                        $orderInfo['dlr_id'] = $gwsmDlrInfo['dlr_id'];
                        $orderInfo['bank'] = $gwsmDlrInfo['bank'];
                        $orderInfo['bank_account'] = $gwsmDlrInfo['bank_account'];
                        $orderInfo['register_address'] = $gwsmDlrInfo['register_address'];
                        $orderInfo['register_mobile'] = $gwsmDlrInfo['register_mobile'];

//                        if($orderInfo['settlement_time'] >='2025-04-01 00:00:00' or 1==1){
                        if($orderInfo['settlement_time'] >'2025-04-01 00:00:00'){
//                            $orderDlrObj = new DbDlrInvoice();
//                            $orderDlrList = $orderDlrObj->select();
//                            $dlr_arr = [];
//                            foreach($orderDlrList as $orderDlr){
//                                $dlr_arr[$orderDlr['id']] = $orderDlr;
//                            }

//                            $spitOrder = $orderObj->where(['id'=>$orderId,'parent_order_type'=>2])->find();
                            // 抬头
                            $invoice_info =  $orderInvoiceObj->where(['order_code'=>$orderInfo['parent_order_code']])->find();
                            if(!empty($invoice_info)){
                                $data['order_id'] = $orderId;
                                $data['order_code'] = $orderInfo['order_code'];
                                $data['invoice_type'] =  $invoice_info['invoice_type'];
                                $data['invoice_header_type'] =  $invoice_info['invoice_header_type'];
                                $data['invoice_header'] = $invoice_info['invoice_header'];
                                $data['mobile'] = $invoice_info['mobile'];
                                $data['email'] = $invoice_info['email'];
                                $data['tax_code'] = $invoice_info['tax_code'] ?? "";
                                $data['bank'] =  $invoice_info['bank'] ?? "";
                                $data['bank_account'] =  $invoice_info['bank_account'] ?? "";
                                $data['invoice_materials_image'] = $invoice_info['invoice_materials_image'];
                                $data['register_address'] =  $invoice_info['register_address'] ?? "";
                                $data['register_mobile'] =  $invoice_info['register_mobile'] ?? "";

                                $orderInvoiceObj->insertGetId($data);
                            }else{
                              //  $dlrItem = $dlr_arr[$orderInfo['dlr_id']];
                                $data['order_id'] = $orderId;
                                $data['invoice_type'] = 1;
                                $data['invoice_header_type'] = 1;
                                $data['invoice_header'] = "个人";
                                $data['mobile'] = $orderInfo['phone'];
//                                $data['tax_code'] = $dlrItem['tax_num'] ?? "";
//                                $data['bank'] =  $dlrItem['bank'] ?? "";
//                                $data['bank_account'] =  $dlrItem['bank_account'] ?? "";
//                                $data['register_address'] =  $dlrItem['register_address'] ?? "";
//                                $data['register_mobile'] =  $dlrItem['register_mobile'] ?? "";
                                $data['tax_code'] =  "";
                                $data['bank'] =   "";
                                $data['bank_account'] =   "";
                                $data['register_address'] =  "";
                                $data['register_mobile'] =   "";
                                $data['order_code'] =  $orderInfo['order_code'] ?? "";
                                $orderInvoiceObj->insertGetId($data);
                            }
                        }
                }

                $orderInvoiceInfo =  $orderInvoiceObj->alias("a")->join("t_bu_order b","a.order_id=b.id")->where(['a.order_id'=>$orderId,'a.is_enable'=>1])->field("b.dlr_code,a.*")->find();
        ///   echo $orderInvoiceObj->getLastSql();exit;
               return InvoiceApplyService::getInstance()->createInvoice($orderInvoiceInfo);
            }else{
                return "用户已开票";
            }

        }catch (Exception $e){
            return $e->getMessage();
        }
    }

}