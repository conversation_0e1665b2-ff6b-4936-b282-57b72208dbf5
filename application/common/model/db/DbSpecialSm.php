<?php

namespace app\common\model\db;

use app\common\model\Common;
use think\Model;

/**
 * 专题页列表
 * Class DbSpecialSm
 * @package app\common\model\db
 */
class DbSpecialSm extends Common
{
    /**
     * @var string
     */
    protected $table = 't_db_special_sm';
    /**
     * @var string
     */
    protected $pk = 'id';

    /**
     * 专题
     * page_type
     * 1-日产小程序，2-OneApp
     * 5-启辰小程序，6-启辰app
     * 3-PZ1A
     */
    const DEFAULT_PAGE_TYPE   = 1;
    const V_DEFAULT_PAGE_TYPE = 5;
    const ONE_APP_PAGE_TYPE   = 2;
    const V_ONE_APP_PAGE_TYPE = 6;
    const P_PZ1A_PAGE_TYPE = 3;

    const PAGE_TYPE_CHANNEL   = [1 => 'GWSM', 2 => 'GWAPP', 3 => 'PZ1ASM', 5 => 'QCSM', 6 => 'QCAPP'];

    public function get_list($page_type)
    {
        $search    = input('search');//接收前端传过来的搜索条件
        $is_enable = input('is_enable');
        $page      = input('page', 1);//页码 默认第一页
        $paginate  = input('paginate', 10);//一页显示多少条数据 默认显示10条数据
        $gather_id = input('gather_id');//业务归属
        $params    = [
            'field'    => '*',
            'where'    => ['page_type' => $page_type],
            'order'    => 'created_date desc',
            'page'     => $page,
            'paginate' => $paginate
        ];
        //查询条件
        if (!empty($search)) {
            $params['where']['title'] = ['like', "%$search%"];
        }
        if (!empty($is_enable)) {
            $params['where']['is_enable'] = $is_enable;
        }
        if (!empty($gather_id)) {
            $params['where']['gather_id'] = $gather_id;
        }
        $list =  $this->getListPaginate($params);
        $model = new DbSystemValue();
        $gather_name_array = $model->where(['value_type'=>26])->field('sys_value_id as id,county_name as name')->select();
        $gather_array = [];
        foreach ($gather_name_array as $value){
            $gather_array[$value['id']] = $value['name'];
        }
        foreach ($list as $value){
            if($value['gather_id'] == 0 || empty($value['gather_id'])){
                $value['gather_name'] = '-';
            }else{
                if(isset($gather_array[$value['gather_id']])){
                    $value['gather_name'] = $gather_array[$value['gather_id']];
                }else{
                    $value['gather_name'] = '-';
                }
            }
        }
        return $list;
    }


}


