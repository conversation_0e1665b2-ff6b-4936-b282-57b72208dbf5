<?php

namespace app\common\model\db;

use app\common\model\Common;

/**
 * 导出记录表
 * Class DbExports
 * @package app\common\model\db
 */
class DbExports extends Common
{
    static $export_type_list = [
        'order'           => '订单',
        'trans_static'    => '交易账单汇总',
        'trans_order'     => '交易账单',
        'settle_order'    => '结算账单',
        'unsettle_order'  => '未结算账单',
        'sd_trans_order'  => '手工拉表_交易账单',
        'sd_settle_order' => '手工拉表_结算账单',
        'sd_after_order'  => '手工拉表_退款账单',
        'subscribe'       => '预约单',
        'delivery'        => '发货单',
        'after_order'     => '售后单',
        'coupon'          => '优惠券',
        'supplier_order'  => '供应商结算',
        'crowdfund'       => '众筹订单',

    ];


    static $finance_export_type = ['trans_static', 'trans_order', 'settle_order', 'unsettle_order', 'sd_trans_order', 'sd_settle_order', 'sd_after_order', 'supplier_order'];


    static $finance_sd_export_type = ['sd_trans_order', 'sd_settle_order', 'sd_after_order'];

    /**
     * @var string
     */
    protected $table = 't_db_exports';
    /**
     * @var string
     */
    protected $pk = 'id';

    static public $export_type = [
        0 => '未开始',
        1 => '生成中',
        2 => '已生成',
        3 => '数据异常',
    ];

    static public $type_column = [
        'order' => [
            "a.created_date" => '下单时间',
            "a.pay_time"     => '支付时间',
            "between"        => "介于",
            "00:00:00"       => "",
            "23:59:59"       => "",
            "a.order_source" => "订单来源",
            "neq"            => "不等于",
            "a.order_status" => "订单状态",
            "a.order_code"   => "订单编码",
            "a.phone"        => "手机号",
        ]
    ];

}
