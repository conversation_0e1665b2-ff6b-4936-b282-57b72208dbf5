<?php
/**
 * 商品model
 * @author: lian<PERSON><PERSON><PERSON>
 * @time: 2017-04-10
 */

namespace app\common\model\db;

use app\common\model\Common;

class DbCommodity extends Common
{
    protected $table = 't_db_commodity';
    protected $pk = 'id';

    const COMMODITY_CLASS_KEY1 = 1;
    const COMMODITY_CLASS_KEY1_VALUE = '实物商品';
    const COMMODITY_CLASS_KEY2 = 2;
    const COMMODITY_CLASS_KEY2_VALUE = '虚拟商品';
    const COMMODITY_CLASS_KEY3 = 3;
    const COMMODITY_CLASS_KEY3_VALUE = '电子卡劵';
    const COMMODITY_CLASS_KEY4 = 4;
    const COMMODITY_CLASS_KEY4_VALUE = 'CSS流量套餐';
    const COMMODITY_CLASS_KEY5 = 5;
    const COMMODITY_CLASS_KEY5_VALUE = '平台卡券';
    const COMMODITY_CLASS_KEY6 = 6;
    const COMMODITY_CLASS_KEY6_VALUE = '取送车券';
    const COMMODITY_CLASS_KEY7 = 7;
    const COMMODITY_CLASS_KEY7_VALUE = '启辰充电桩';
    const COMMODITY_CLASS_KEY8 = 8;
    const COMMODITY_CLASS_KEY8_VALUE = '日产充电桩';
    const COMMODITY_CLASS_KEY9 = 9;
    const COMMODITY_CLASS_KEY9_VALUE = '延保服务包';
    const COMMODITY_CLASS_LIST = [
        self::COMMODITY_CLASS_KEY1 => self::COMMODITY_CLASS_KEY1_VALUE,
        self::COMMODITY_CLASS_KEY2 => self::COMMODITY_CLASS_KEY2_VALUE,
        self::COMMODITY_CLASS_KEY3 => self::COMMODITY_CLASS_KEY3_VALUE,
        self::COMMODITY_CLASS_KEY4 => self::COMMODITY_CLASS_KEY4_VALUE,
        self::COMMODITY_CLASS_KEY5 => self::COMMODITY_CLASS_KEY5_VALUE,
        self::COMMODITY_CLASS_KEY6 => self::COMMODITY_CLASS_KEY6_VALUE,
        self::COMMODITY_CLASS_KEY7 => self::COMMODITY_CLASS_KEY7_VALUE,
        self::COMMODITY_CLASS_KEY8 => self::COMMODITY_CLASS_KEY8_VALUE,
        self::COMMODITY_CLASS_KEY9 => self::COMMODITY_CLASS_KEY9_VALUE,
    ];




    //到店商品类型
    public static function ddCommodityType($id)
    {
        //9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-双保专属权益套餐 6保养套餐-其他 7到店代金券
        $type_arr = [1 => '老友惠保养套餐', 3=> '心悦保养套餐', 4 => '五年双保', 5 => '保养套餐-其他', 7 => '到店券', 12=>'五年双保专享心悦套餐'];
        if(isset($type_arr[$id])){
            return $type_arr[$id];
        }else{
            return '';
        }
    }

//return [0 => '', 1 => '商品订单', 2 => '拼团订单', 3 => '套装', 4 => '礼包', 5 => '到店有礼', 6 => '积分-日产', 7 => "会员日", 8 => '锦尚阳光口罩', 11 => 'CCS订单', 12 => '虚拟商品订单', 13 => '电子卡券商品订单',14=>'积分-启辰',15=>'到店备件',16=>'保养套餐-老友惠保养套餐',17=>'保养套餐-心悦保养套餐',18=>'保养套餐-双保专属权益套餐',19=>'保养套餐-其他',20=>'到店代金券',21=>'到店电子券','22'=>'pz1a套餐','23'=>'工时商品','24'=>'取送车券'];
    //到店商品类型 '9' => '到店备件商品', '1' => '保养套餐-老友惠保养套餐', '3' => '保养套餐-心悦保养套餐', '4' => '保养套餐-五年双保升级权益套餐', '41' => '保养套餐-五年双保升级权益套餐(全合成)', '6' => '保养套餐-其他', '7' => '到店代金券', '8' => '到店电子券',10=>"pz1a套餐",11=>"工时"
    public static function dd_order_s($type){
        $dd_arr = [
            'order_s'=>[9=>15,1=>16,3=>17,4=>18,6=>19,7=>20,41=>18,8=>21,10=>22,11=>23,12=>40],
        ];
        if(isset($dd_arr[$type])){
            return $dd_arr[$type];
        }else{
            return  [];
        }
    }

    /**
     * 根据商品类型查询商品
     * @param $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getListByCommType($params)
    {
        $params = parent::_checkParams($params);
        $list = $this->alias('a')
            ->join('t_db_commodity_type b', 'a.comm_type_id=b.id', 'left')
            ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id', 'left')
            ->where($params['where'])
            ->whereOr($params['where_or'])
            ->field($params['field'])
            ->order($params['order'])
            //  ->fetchSql()
            ->select();
        /*echo $list;
                exit();*/
        return $list;
    }


    public function getCommodityList($params)
    {
        $params = parent::_checkParams($params);
        $list = $this->alias('a')
            ->join('t_db_commodity_type b', 'a.comm_type_id=b.id', 'left')
            ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id', 'left')
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }

    public function getList_old($params = [])
    {
        $params = $this->_checkParams($params);
        isset($params['_where']) or $params['_where'] = null;
        $list = $this->where($params['where'])->where($params['_where'])->field($params['field'])->order($params['order'])->limit($params['limit'])->select();
        return $list;
    }

    /**
     * 新版获取商品列表
     * 20170726
     * 除了基本查询语句，还要带params['dlr_code']
     *
     */
    public function getList($params = [])
    {
        $params = $this->_checkParams($params);
        isset($params['_where']) or $params['_where'] = null;
        $list = $this->alias('a')->join('t_db_commodity_dlr b', "a.id=b.commodity_id")->join("t_db_commodity_set c", 'c.commodity_id=b.commodity_id')->where(sprintf("((b.set_type=1 AND c.set_type=1) OR  c.dlr_code='%s' ) and b.dlr_code='%s'", $params['dlr_code'], $params['dlr_code']))->where($params['where'])->where($params['_where'])->field($params['field'])->order($params['order'])->limit($params['limit'])->fetchSql()->select();
//        $list = $this->alias('a')->join("t_db_commodity_dlr b","a.id = b.commodity_id")->join("t_db_commodity_set c",'b.commodity_set_id = c.id')
//            ->where(sprintf("b.dlr_code = '%s' AND a.is_enable = 1 AND c.is_enable = 1",$params['dlr_code']))->where($params['where'])->where($params['_where'])->field($params['field'])->order($params['order'])->paginate($params['pagesize'],false,array('query'=>$params['query']));
        return $list;
    }

//    public function getListPage($params=[]){
//        $params=$this->_checkParams($params);
//        isset($params['_where'])    or $params['_where']=null;
//        $list = $this->alias('a')->join(sprintf("(SELECT * FROM
//		t_db_commodity_dlr a  WHERE a.set_type = (
//			SELECT
//				max(b.set_type)
//			FROM
//				t_db_commodity_dlr b
//			WHERE
//				a.commodity_id = commodity_id
//			AND a.dlr_code = b.dlr_code ) AND a.dlr_code = '%s' ) b",$params['dlr_code']),sprintf("a.id=b.commodity_id "))->join("t_db_commodity_set c",'c.commodity_id=b.commodity_id')->join("t_db_commodity_card d",'d.commodity_id=c.commodity_id','LEFT')
//            ->where(sprintf("(b.set_type=1 AND c.set_type=1) OR  c.dlr_code='%s'  ",$params['dlr_code']))->where($params['where'])->where($params['_where'])->field($params['field'])->order($params['order'])->paginate($params['pagesize'],false,array('query'=>$params['query']));
//        return $list;
//    }
    // AND ( FIND_IN_SET(1, c.sales_channel)) 销售渠道为C 商城
    public function getListPage($params = [])
    {
        $params = $this->_checkParams($params);
        isset($params['_where']) or $params['_where'] = null;
        $list = $this->alias('a')->join("t_db_commodity_dlr b", "a.id = b.commodity_id")->join("t_db_commodity_set c", 'b.commodity_set_id = c.id')->join("t_db_commodity_card d", "d.commodity_set_id=c.id", "left")
            ->where(sprintf("b.dlr_code = '%s' AND a.is_enable = 1 AND c.is_enable = 1 AND ( FIND_IN_SET(1, c.sales_channel))", $params['dlr_code']))->where($params['where'])->where($params['_where'])->field($params['field'])->group($params['group'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }

    //使用limit分页，不使用自带分页 AND ( FIND_IN_SET(1, c.sales_channel)) 销售渠道为C 商城
    public function getListLimit($params = [])
    {
        $params = $this->_checkParams($params);
        isset($params['_where']) or $params['_where'] = null;
        $list = $this->alias('a')->join("t_db_commodity_dlr b", "a.id = b.commodity_id")->join("t_db_commodity_set c", 'b.commodity_set_id = c.id')
            ->where(sprintf("b.dlr_code = '%s' AND a.is_enable = 1 AND a.is_shop=1 AND c.is_enable = 1 AND ( FIND_IN_SET(1, c.sales_channel))", $params['dlr_code']))->where($params['where'])->where($params['_where'])->field($params['field'])->order($params['order'])->limit($params['limit'])->select();
        return $list;
    }


    public static function attribute($v = '')
    {
        $data = [
            1 => '热销', 2 => '推荐', 3 => '新品', 4 => '促销', 5 => '预售',6=>'Ni+好物',7=>'Ni+潮创',8=>'ARIYA好物'
        ];
        if ($v !== '' && !isset($data[$v])) {
            return '';
        }
        return empty($v) ? $data : $data[$v];
    }

    /**
     * 经销商商品设置页面->查询平台设置的商品
     * @param $params
     * @return \think\Paginator
     */
    public function getCommodityDealerList($params)
    {
        $params = parent::_checkParams($params);
        $list = $this->alias('a')
            ->join('t_db_commodity_type b', 'a.comm_type_id=b.id', 'left')
            ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id', 'left')
            ->join('t_db_dlr d', 'a.create_dlr_code = d.dlr_code', 'left')
            ->join('t_db_commodity_set e', 'a.id = e.commodity_id', 'left')
            ->join('t_db_commodity_dlr_type f', 'e.commodity_dlr_type_id = f.id')
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }


    /**
     * 车品文章获取商品--吴炜文
     * @param $params
     * @return \think\Paginator
     */
    public function getListJoinCommoditySet($params)
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->field($params['field'])
            ->join("t_db_commodity_set b", 'a.id = b.commodity_id', 'right')
            ->where($params['where'])
            ->order($params['order'])
            ->paginate($params['pagesize'], false, ['query' => $params['query']]);
        return $list;
    }

    /***
     * 新获取商品列表 判断上架下架
     */
    public function getCommodityShelvesList($params, $shelve_type = 0)
    {
        $params = $this->_checkParams($params);
        if ($params['is_platform'] == 1) {
            if($shelve_type == 5){
                $list = $this->alias('a')
                    ->join('t_db_commodity_type b', 'a.comm_type_id=b.id', 'left')
                    ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id', 'left')
                    ->join("( SELECT cs.commodity_id,cs.up_down_channel_dlr,cs.platform_up_down_channel_dlr,cs.up_down_channel_name,cs.id,cs.set_type ,cs.shelves_type ,cs.is_mail,cs.original_price_range_start,original_price_range_end,cs.discount_price_range_start,cs.discount_price_range_end,cs.count_stock, cs.commodity_attr,cs.created_date FROM t_db_commodity_set cs ) e ", "a.id=e.commodity_id AND e.shelves_type={$params['shelves_type']} ", 'left')
                    ->where($params['where'])
                    ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
            }else{
                $list = $this->alias('a')
                    ->join('t_db_commodity_type b', 'a.comm_type_id=b.id', 'left')
                    ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id', 'left')
                    ->join("( SELECT cs.commodity_id,cs.up_down_channel_dlr,cs.platform_up_down_channel_dlr,cs.up_down_channel_name,cs.id,cs.set_type ,cs.shelves_type ,cs.is_mail,cs.original_price_range_start,original_price_range_end,cs.discount_price_range_start,cs.discount_price_range_end,cs.count_stock, cs.commodity_attr,cd.created_date ,cd.dlr_code FROM t_db_commodity_set cs inner JOIN t_db_commodity_dlr cd on cs.id=cd.commodity_set_id ) e ", "a.id=e.commodity_id AND e.shelves_type={$params['shelves_type']} AND e.dlr_code='{$params['dlr_code']}'", 'left')
                    ->where($params['where'])
                    ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
            }
        } else {
            $params['field'] .= ',e.up_down_channel_name';
            $list = $this->alias('a')
                ->join('t_db_commodity_type b', 'a.comm_type_id=b.id', 'left')
                ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id', 'left')
                ->join('t_db_commodity_set e', "a.id=e.commodity_id AND e.is_enable=1 AND e.dlr_code='{$params['dlr_code']}' AND  e.shelves_type = {$params['shelves_type']}", 'left')
                ->join('t_db_commodity_set ee', "a.id=ee.commodity_id  AND ee.dlr_code='{$params['dlr_code']}' AND  ee.shelves_type = {$params['shelves_type']}", 'left')
                ->where($params['where'])
                ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        }
        return $list;
    }

    /**
     * 专营店和平台获取上架商品
     * @param $set_type 应用端
     * @param $dlr_code 专营店编码
     * @param $params
     * @return \think\Paginator
     */
    public function getShelvesListByDlr($set_type, $dlr_code, $params)
    {
        if (empty($params['field'])) {
            $params['field'] = " a.id,a.commodity_name,a.cover_image,a.comm_type_id,a.set_type,a.create_dlr_code, b.comm_type_name,b.comm_parent_id,e.id as commodity_set_id,e.commodity_dlr_type_id,e.dlr_code,e.original_price_range_end,e.count_stock,e.shelves_type,g.highest_price,g.sku_price ";
        }
        $params = $this->_checkParams($params);
        $date = date('Y-m-d H:i:s');
        $limit_dis_sql = "(SELECT a.created_dlr_code,b.sku_price,b.highest_price,b.commodity_set_id FROM t_db_limit_discount  a  INNER JOIN t_db_limit_discount_commodity b on a.id=b.limit_discount_id and a.is_enable=1 and a.start_time<'{$date}' and a.end_time>'{$date}' ) g ";
        if ($set_type == 1) {
            //增加官微
            $shelves_type = !empty($params['shelves_type']) ? $params['shelves_type'] : $set_type;

            $where = "`e`.`shelves_type` = $shelves_type AND a.create_dlr_code='{$dlr_code}' AND `a`.`is_enable` = 1 AND e.count_stock>0";
            $list = $this->alias('a')
                ->join('t_db_commodity_type b', 'a.comm_type_id=b.id')
                ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id')
                ->join('t_db_commodity_type t', 'c.comm_parent_id=t.id')
                ->join('t_db_commodity_set e', 'e.commodity_id=a.id AND e.is_enable=1 AND e.dlr_code=a.create_dlr_code')
                ->join($limit_dis_sql, "g.commodity_set_id=e.id AND g.created_dlr_code= '{$dlr_code}'", 'left')
                ->where($where)->where($params['where'])
                ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        } elseif ($set_type == 2) {
            $where = "(`e`.`shelves_type` = 2 or a.create_dlr_code='{$dlr_code}' ) AND `a`.`is_enable` = 1 AND f.dlr_code = '$dlr_code' AND e.count_stock>0";
            $list = $this->alias('a')
                ->join('t_db_commodity_type b', 'a.comm_type_id=b.id')
                ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id')
                ->join('t_db_commodity_dlr f', "f.commodity_id=a.id ")
                ->join('t_db_commodity_type t', 'c.comm_parent_id=t.id')
                ->join('t_db_commodity_set e', 'f.commodity_set_id=e.id AND e.is_enable=1 ')
                ->join($limit_dis_sql, "g.commodity_set_id=e.id AND g.created_dlr_code= '{$dlr_code}'", 'left')
                ->where($where)->where($params['where'])
                ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        }
        return $list;
    }

    /**
     * 获取营店和平台上架商品（首页专用）
     * @param $set_type 应用端
     * @param $dlr_code 专营店编码
     * @param $params
     * @return \think\Paginator
     */
    public function getHomePageShelvesListByDlr($set_type, $dlr_code, $shelves_type, $params)
    {
        if (empty($params['field'])) {
            $params['field'] = "b.shelves_type,a.*";
        }
        $params = $this->_checkParams($params);
        if ($set_type == 1) {
            $limit_dis_sql = "(
            select b.commodity_id,a.shelves_type,b.dlr_code,count(1) count from t_db_commodity_set a
            inner join t_db_commodity_dlr b on a.id =b.commodity_set_id and b.is_enable=1
            where a.dlr_code='NISSAN' and a.shelves_type=$shelves_type and b.dlr_code in($dlr_code) {$params['where_1']}
            group by b.commodity_id,a.shelves_type ) b ";
            $where = "a.is_enable=1 and b.count>=" . count(explode(',', $dlr_code));
            $list = $this->alias('a')
                ->join($limit_dis_sql, "a.id=b.commodity_id")
                ->join('t_db_commodity_type c', 'a.comm_type_id=c.id')
                ->join('t_db_commodity_type t', 'c.comm_parent_id=t.id')
                ->join('t_db_commodity_type s', 't.comm_parent_id=s.id')
                ->where($where)->where($params['where'])
                ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        } elseif ($set_type == 2) {
            $where = "b.dlr_code in('$dlr_code','NISSAN') and b.shelves_type=$shelves_type and d.dlr_code ='$dlr_code' and a.is_enable=1 " . $params['where_1'];
            $list = $this->alias('a')
                ->join('t_db_commodity_set b', 'a.id=b.commodity_id')
                ->join('t_db_commodity_type c', 'a.comm_type_id=c.id')
                ->join('t_db_commodity_type t', 'c.comm_parent_id=t.id')
                ->join('t_db_commodity_type s', 't.comm_parent_id=s.id')
                ->join('t_db_commodity_dlr d', 'b.id =d.commodity_set_id and c.is_enable=1')
                ->where($where)->where($params['where'])
                ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        } elseif ($set_type == 3) {
            $where = "b.dlr_code in('$dlr_code','NISSAN') and b.shelves_type=$shelves_type and d.dlr_code ='$dlr_code' and a.is_enable=1 " . $params['where_1'];
            $list = $this->alias('a')
                ->join('t_db_commodity_set b', 'a.id=b.commodity_id')
                ->join('t_db_commodity_type c', 'a.comm_type_id=c.id')
                ->join('t_db_commodity_type t', 'c.comm_parent_id=t.id')
                ->join('t_db_commodity_type s', 't.comm_parent_id=s.id')
                ->join('t_db_commodity_dlr d', 'b.id =d.commodity_set_id and c.is_enable=1')
                ->where($where)->where($params['where'])
                ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        }
        return $list;
    }


    public function getCommoditysetList($params)
    {
        $params = parent::_checkParams($params);
        $list = $this->alias('a')
            ->join('t_db_commodity_dlr b', 'a.id=b.commodity_id', 'inner')
            ->join('t_db_commodity_set c', 'b.commodity_set_id=c.id', 'inner')
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        // echo  $this->alias('a')->getLastSql();exit;
        return $list;
    }

    public static function commodityClass()
    {
        return self::COMMODITY_CLASS_LIST;
    }

    public static function commodityClassByToB()
    {
        return [1 => '实物', 2 => '虚拟', 3 => '电子卡券'];
    }


    public static function workHourType()
    {
        return [30701 => '正常维修', 30702 => '定期保养', 30714 => '线上商城'];
    }
    //到店商品类型
    public static function commodityDdType(){
        return ['9' => '到店备件商品', '1' => '保养套餐-老友惠保养套餐', '3' => '保养套餐-心悦保养套餐', '4' => '保养套餐-双保专属权益套餐', '6' => '保养套餐-其他', '7' => '到店代金券', '8' => '到店电子券','10' => '保养套餐-ARIYA','11'=> '到店工时', 12 => '保养套餐-五年双保专享心悦套餐'];
    }

    //to-B到店商品类型
    public static function commodityDdTypeByToB(){
        return ['9' => '到店备件商品', '1' => '保养套餐-老友惠保养套餐', '3' => '保养套餐-心悦保养套餐', '4' => '保养套餐-双保专属权益套餐', '6' => '保养套餐-其他', '7' => '到店代金券'];
    }

    /**
     * 通过专营店获取自营上架商品
     * @param string $create_dlr_code
     * @param array $dlr_code
     * @param $params
     */
    public function getSelfmployedCommodityByDrl($create_dlr_code = 'NISSAN', $dlr_code = [], $params)
    {
        if (is_array($dlr_code)) {
            $count = count($dlr_code);
            $dlr_code = "('" . implode("','", $dlr_code) . "')";
        } else {
            return [];
        }
        if (empty($params['field'])) {
            $params['field'] = " a.id,a.commodity_name,a.cover_image,a.comm_type_id,a.set_type,a.create_dlr_code, b.comm_type_name,b.comm_parent_id,e.id as commodity_set_id,e.commodity_dlr_type_id,e.dlr_code,e.original_price_range_end,e.count_stock,e.shelves_type,g.highest_price,g.sku_price ";
        }

        $params = $this->_checkParams($params);
        $date = date('Y-m-d H:i:s');
        $limit_dis_sql = "(SELECT a.created_dlr_code,b.sku_price,b.highest_price,b.commodity_set_id FROM t_db_limit_discount  a  INNER JOIN t_db_limit_discount_commodity b on a.id=b.limit_discount_id and a.is_enable=1 and a.start_time<'{$date}' and a.end_time>'{$date}' ) g ";
        $where = "`e`.`shelves_type` = 1 AND a.create_dlr_code='{$create_dlr_code}' AND `a`.`is_enable` = 1 AND e.count_stock>0";
        $list = $this->alias('a')
            ->join('t_db_commodity_type b', 'a.comm_type_id=b.id')
            ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id')
            ->join('t_db_commodity_type t', 'c.comm_parent_id=t.id')
            ->join('t_db_commodity_set e', 'e.commodity_id=a.id AND e.is_enable=1 AND e.dlr_code=a.create_dlr_code')
            ->join("(SELECT commodity_set_id,count(1) count FROM `t_db_commodity_dlr`  where  dlr_code in $dlr_code  GROUP BY commodity_set_id having count=$count) cd", "cd.commodity_set_id=e.id")
            ->join($limit_dis_sql, "g.commodity_set_id=e.id AND g.created_dlr_code= '{$create_dlr_code}'", 'left')
            ->where($where)->where($params['where'])
            ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }

    /**
     * 通过专营店获取自营上架商品
     * @param string $create_dlr_code
     * @param array $dlr_code
     * @param $params
     */
    public function getSelfmployedCommodityWxByDrl($create_dlr_code = 'NISSAN', $dlr_code = [], $params)
    {
        if (is_array($dlr_code)) {
            $count = count($dlr_code);
            $dlr_code = "('" . implode("','", $dlr_code) . "')";
        } else {
            return [];
        }
        if (empty($params['field'])) {
            $params['field'] = " a.id,a.commodity_name,a.cover_image,a.comm_type_id,a.set_type,a.create_dlr_code, b.comm_type_name,b.comm_parent_id,e.id as commodity_set_id,e.commodity_dlr_type_id,e.dlr_code,e.original_price_range_end,e.count_stock,e.shelves_type,g.highest_price,g.sku_price ";
        }

        $params = $this->_checkParams($params);
        $date = date('Y-m-d H:i:s');
        $limit_dis_sql = "(SELECT a.created_dlr_code,b.sku_price,b.highest_price,b.commodity_set_id FROM t_db_limit_discount  a  INNER JOIN t_db_limit_discount_commodity b on a.id=b.limit_discount_id and a.is_enable=1 and a.start_time<'{$date}' and a.end_time>'{$date}' ) g ";
        $where = "`e`.`shelves_type` = 3 AND a.create_dlr_code='{$create_dlr_code}' AND `a`.`is_enable` = 1 AND e.count_stock>0";
        $list = $this->alias('a')
            ->join('t_db_commodity_type b', 'a.comm_type_id=b.id')
            ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id')
            ->join('t_db_commodity_type t', 'c.comm_parent_id=t.id')
            ->join('t_db_commodity_set e', 'e.commodity_id=a.id AND e.is_enable=1 AND e.dlr_code=a.create_dlr_code')
            ->join("(SELECT commodity_set_id,count(1) count FROM `t_db_commodity_dlr`  where  dlr_code in $dlr_code  GROUP BY commodity_set_id having count=$count) cd", "cd.commodity_set_id=e.id")
            ->join($limit_dis_sql, "g.commodity_set_id=e.id AND g.created_dlr_code= '{$create_dlr_code}'", 'left')
            ->where($where)->where($params['where'])
            ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }

    /**查找自营上架专营店可卖的商品
     * @param $params
     * @return \think\Collection
     */
    public function getSelfCommodityByDrl($params)
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->join('t_db_commodity_set b', 'a.id = b.commodity_id')
            ->join('t_db_commodity_dlr c', '  a.id = c.commodity_id')
            ->join('t_db_commodity_type d', '  a.comm_type_id = d.id')
            ->join('t_db_commodity_type e', '  d.comm_parent_id = e.id')
            ->join('t_db_commodity_type f', '  e.comm_parent_id = f.id')
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->group($params['group'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }


    public static function shelvesSources()
    {
        return array('1' => '平台', '2' => '专营店', '3' => '官微', '4' => '活动', '5' => 'NI+商城', '6' => 'PZ1A商城', '7' => '启辰商城','8'=> 'To B商城');
    }

    public static function shelvesSourcesByToB()
    {
        return array('1' => '平台', '2' => '专营店', '3' => '官微', '4' => '活动', '5' => 'NI+商城', '6' => 'PZ1A商城', '7' => '启辰商城', '8' => 'To B商城');
    }

    public static function displayChannel()
    {
        return array('5' => '日产商城(小程序、app、官网)', '6' => 'PZ1A商城(小程序、app)', '7' => '启辰商城(小程序、app)', '8' => 'to B商城(PC端)');

    }

    public function getCommodityCardList($params)
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->join('t_db_commodity_type b', 'a.comm_type_id=b.id')
            ->join('t_db_commodity_type c', 'b.comm_parent_id=c.id')
            ->join('t_db_commodity_set e', "a.id=e.commodity_id ")
            ->join("(SELECT commodity_set_id,group_concat(id) id_str,group_concat(card_type) card_type_str,GROUP_CONCAT(is_can_receive) is_can_receive_str FROM t_db_commodity_card  WHERE card_id={$params['card_id']} GROUP BY commodity_set_id) f", "`e`.id= f.commodity_set_id ", 'left')
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }


    public function getCommodityIds($commodity_ids)
    {
        $list = $this->alias('a')->join('t_db_commodity_set b', 'b.commodity_id = a.id', 'left')
            ->join('(select id,commodity_set_id,price from t_db_commodity_set_sku  where is_enable =1 group by commodity_set_id ) c ', 'c.commodity_set_id = b.id', 'left')
            ->where(['a.id' => ['in', $commodity_ids]])->field('b.*,a.commodity_name,a.cover_image,c.id as sku_id,c.price')->select();
        return $list;
    }


    /**
     * 【官网版】获取商品信息
     * @param $where
     * @return array|false|\PDOStatement|string|\think\Model
     */
    public function getCommodityInfo($where)
    {
        //c.is_enable=1 下架了不显示
        $redis_name =  "get_commodity_info".json_encode($where);
        $row =  redis($redis_name);
        if(!$row){
            $field = "c.group_commodity_ids_info,a.id,a.commodity_name,a.comm_type_id,a.original_price_range,a.discount_price_range,a.cover_image,a.detail_content,a.is_pure,a.is_integral,a.card_id,a.validity_date_start,a.validity_date_end,a.is_shopping_cart,a.is_mail,a.mail_price,a.is_shop,a.sku_image,a.unit,a.original_price_range_start,a.original_price_range_end,a.discount_price_range_start,a.discount_price_range_end,a.set_type,a.commodity_code,a.commodity_class,a.commodity_card_ids,a.shelves_sources,a.sale_num,a.brands_id,a.video,a.live_refresh,a.supplier,a.tax_code,a.dd_commodity_type,a.work_hour_type,a.is_grouped,a.machine_oil_type,a.arrival_bdp,a.is_integral_shop,c.discount_price_range_start dis_p_s,c.discount_price_range_end dis_p_e,limit_dis,limit_wi_dis,n_dis,group_dis,pre_dis,seckill_dis,cheap_dis,full_dis,full_wi_dis,c.pay_style,d.commodity_id,d.last_updated_date,c.point_discount,c.favourable_introduction,c.favourable_detail,d.commodity_set_id,c.is_store,c.is_mail,c.count_stock,c.max_point,a.work_hour_type,d.relate_car_ids,c.front_sale_num sale_num,c.directions1,c.directions2,c.remark,c.commodity_label,c.is_sp_associated,c.service_channel,d.gift_dis,c.qsc_group,c.first_free_price,c.qsc_group_price, c.qsc_group_num, c.qsc_group_name, d.shelves_type,a.poster_show_type,c.mail_show_word,c.mail_type,d.crowdfund_dis,c.listing_type,d.activity_image,c.activity_start_time,c.activity_end_time,a.liability_clause,a.liability_clause_pdf,a.no_nev_liability_clause,a.no_nev_liability_clause_pdf";
            $row = $this->alias('a')
                ->join("t_db_commodity_flat d", "d.commodity_id=a.id")
                ->join('t_db_commodity_set c',"c.id=d.commodity_set_id and c.is_enable=1")
                ->field($field)
                ->where($where)->where('a.is_enable=1')
                ->group("a.id")
                ->find();
            redis($redis_name,$row,10);
        }


        return $row;
    }

    /**
     * 融合卡券查询商品列表
     * @param $params
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getCommodityCardListByCode($params)
    {
        $params = parent::_checkParams($params);
        $list = $this->alias('a')
            ->join("t_db_commodity_set_sku b", "a.id = b.commodity_id")
            ->where($params['where'])
            ->field($params['field'])->group($params['group'])->order($params['order'])->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }

}
