<?php

namespace app\common\model\db;

use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\bu\BuCheapSuitSub;
use app\common\model\bu\BuGwRecommendationBannerCommodity;
use app\common\model\Common;
use app\common\model\db\DbCommoditySetSku;
use app\common\net_service\NetGoods;
use think\Db;

/**
 * 普通商品表
 * Class DbCommodityFlat
 * @package app\common\model\db
 */
class DbCommodityFlat extends Common
{
    /**
     * @var string
     */
    protected $table = 't_db_commodity_flat';
    /**
     * @var string
     */
    protected $pk = 'id';

    /**
     * 将表连查切换成为单表查询
     * @param $params
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getCommodityList($params)
    {
        $params   = parent::_checkParams($params);
        $c_needle = strpos($params['field'], 'c.');
        $query    = $this->alias('a')
            ->join('t_db_commodity_set b', "a.commodity_set_id=b.id");
        if ($c_needle) {
            $query->join('t_db_commodity c', "b.commodity_id=c.id");
        }

        return $query->where($params['where'])
            ->field($params['field'])
            ->order($params['order'])
            ->group($params['group'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
    }

    public function getPz1aCommodityList($params)
    {
        $params = parent::_checkParams($params);
        return $this->alias('a')
            ->join("t_db_commodity c", "a.commodity_id=c.id")
            ->join('t_db_commodity_set b', "a.commodity_set_id=b.id ")
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->group($params['group'])->paginate($params['pagesize'], false, array('query' => $params['query']));
    }


    /**
     * @param string $v
     * @return array|mixed|string
     */
    public static function tagArr($v = '')
    {
        //19卡券1，20卡券2 这两个过滤掉，后面从21开始--21赠品券
        //秒杀活动未开始 161
        //【秒杀活动（进行中）】--默认显示：秒杀;【限时优惠】--默认显示：限时优惠;【券标签】--满XX减XX/XX折券/XX元券;【买赠】--默认显示：享赠品;【满减活动】--默认显示：满减;【N件N折】;【拼团】;【优惠套装】-显示后台配置的促销标签，若无则不显示;【预售活动】-默认显示：预售;【秒杀活动（未开始）】-默认显示：秒杀;【商品属性标签】
        $data = [
            1 => '热销', 2 => '推荐', 3 => '新品', 4 => '促销', 5 => '预售', 6 => 'Ni+好物', 7 => 'Ni+潮创', 10 => '优惠套装', 11 => '满减', 12 => '限时优惠', 13 => '拼团', 14 => 'N件N折', 15 => '预售', 16 => '秒杀', 17 => '享赠品', 18 => '众筹', 161 => '秒杀', 21 => '赠品券'
        ];
        if ($v !== '' && !isset($data[$v])) {
            return '';
        }
        return empty($v) ? $data : $data[$v];
    }



    //活动ID转成标签ID
    public static function actToTag($v = '')
    {
        //1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减9臻享服务包10秒杀,19-20卡券，21赠品券，22原商品属性
        $data = [1 => 12, 2 => 13, 3 => 11, 5 => 10, 6 => 14, 7 => 15, 8 => 12, 10 => 161, 19 => 19, 20 => 20, 21 => 21, 22 => [1, 2, 3, 4, 6, 7], 12 => 17];
        if ($v !== '' && !isset($data[$v])) {
            return '';
        }
        return empty($v) ? $data : $data[$v];
    }

    /**
     * @param array $params
     * @return array|object
     * */
    public function getCommodity($params)
    {
        $params = parent::_checkParams($params);
        if (isset($params['no_page'])) {
            return $this
                ->alias('a')
                ->field($params['field'])
                ->join('t_db_commodity_set b', 'a.commodity_id=b.commodity_id')
                ->join('t_db_commodity c', "b.commodity_id=c.id")
                ->where($params['where'])
                ->group($params['group'])
                ->select();
        }
        return $this
            ->alias('a')
            ->field($params['field'])
            ->join('t_db_commodity_set b', 'a.commodity_id=b.commodity_id')
            ->where($params['where'])
            ->order($params['order'])
            ->group($params['group'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']))
            ->toArray();

    }

    /**
     * 卡券关联商品信息查询
     * */
    public function getCommodityCardList($params)
    {
        $params = parent::_checkParams($params);
        return $this->alias('a')
            ->join('t_db_commodity_set b', "a.commodity_set_id=b.id ")
            ->join('t_db_commodity c', "a.commodity_id=c.id ")
            ->join('t_db_commodity_type d', "a.comm_type_id=d.id ")
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->group($params['group'])->paginate($params['pageSize'], false, array('query' => $params['query']));
    }

    //要过滤的商品id
    private $limit_product_list = [5164, 5162, 5161, 5160, 5159, 5158, 5157, 5156, 3541, 3540, 3539, 3538, 1718, 1717, 1716, 1715, 1589, 1588, 1587, 1586, 1585, 1584, 1583, 1582, 1581, 1580, 1579, 1578, 1577, 1575, 1574, 1573, 1572, 1571, 1570, 1569, 5416, 5415, 5414, 5413, 4285, 4284, 4283, 4282, 4281, 4280, 4279, 4278, 4277, 4276, 4275, 4274, 4273, 4272, 4271, 4270, 4269, 4268, 4267, 4266, 4265, 4264, 4263, 4262, 4261, 4260, 4259, 4258, 4256, 4257, 5371, 5370, 5369, 5368, 5365, 5327, 5326, 5325, 5324, 5323, 5322, 5321, 5387, 5386, 5385, 5384, 5383, 5382, 5381, 5380, 5379, 5377, 5376, 5375, 5388];

    /**
     * 将表连查切换成为单表查询--速赢版本联合sku表--车型汤总要过滤商品
     * @param $params
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getCommodityListUSku_limit($params)
    {
        $params = parent::_checkParams($params);
        return $this->alias('a')->join('t_db_commodity_set b', "a.commodity_set_id=b.id and b.is_enable=1")->join('t_db_commodity_set_sku c ', "c.commodity_set_id=b.id and c.is_enable=1")->join("t_db_commodity_sku d ", "d.id=c.commodity_sku_id and d.is_enable=1")
            ->where($params['where'])->where(function ($query) {
                $query->whereNotIn("a.dd_commodity_type", [1, 3, 4, 41])->whereor(function ($query) {
                    $query->whereIn("a.dd_commodity_type", [1, 3, 4, 41])->whereIn("a.commodity_id", $this->limit_product_list);
                });
            })
            ->field($params['field'])->order($params['order'])->group($params['group'])->paginate($params['pagesize'], false, array('query' => $params['query']));
    }

    /**
     * 将表连查切换成为单表查询--速赢版本联合sku表--车型
     * @param $params
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getCommodityListUSku($params)
    {
        $params = parent::_checkParams($params);
        return $this->alias('a')->join('t_db_commodity_set b', "a.commodity_set_id=b.id and b.is_enable=1")
            ->join("t_db_commodity_card card_c", "a.commodity_id = card_c.commodity_id and card_c.sorts>0  " . $params['and_where'], "left")
            ->join("t_db_card cards", " card_c.card_id=cards.id and cards.is_enable=1 and cards.validity_date_start > now() and cards.validity_date_end <= NOW() ", "left")
            ->join('t_db_commodity_set_sku c ', "c.commodity_set_id=b.id and c.is_enable=1")->join("t_db_commodity_sku d ", "d.id=c.commodity_sku_id and d.is_enable=1")
            ->where($params['where'])
            ->field($params['field'])->order($params['order'])->group($params['group'])->paginate($params['pagesize'], $params['count'], array('page' => $params['query']['page']??0,'query'=>$params['query']));
    }

    /**
     * 将表连查切换成为单表查询--速赢版本联合sku表--车型2
     * @param $params
     * @return bool|\PDOStatement|string|\think\Collection|\think\Paginator
     * @throws \think\exception\DbException
     */
    public function getCommodityListUSku2($params)
    {
        $params = parent::_checkParams($params);
        return $this->alias('a')
            ->join('t_db_commodity_set b', "a.commodity_set_id=b.id and b.is_enable=1")
            ->join('t_db_commodity_set_sku c ', "c.commodity_set_id=b.id and c.is_enable=1")
            ->join("t_db_commodity_sku d ", "d.id=c.commodity_sku_id and d.is_enable=1")
            ->join("t_db_card e ", "e.id=d.card_id")
            ->where($params['where'])
            ->field($params['field'])
            ->order($params['order'])
            ->group($params['group'])
            ->select();
    }

    public function getProduct($shelves_type, $channel_type = 'GWSM', $tag = 'tag', $up_down_channel_dlr = '')
    {
        $commodity_name  = input('search');
        $type            = input('type', 1);//类型 1商品 2分类
        $search          = input('search');//搜索名称
        $select          = input('select');//二级分类id or 三级
        $activity_id     = input('activity_id', 0);
        $level           = input('level', 3);
        $params['query'] = input('get.');
        $page_size       = 10;
        $final           = [];
        if (!empty($activity_id)) {
            $where['a.id'] = $activity_id;
            if ($type != 7 && $type != 9) {
                $where['a.act_status'] = 2;
            }
            $where['a.is_enable'] = 1;
            if ($type == 9) {
                if ($shelves_type == 5) {
                    $where['a.brand'] = 1;
                } elseif ($shelves_type == 6) {
                    $where['a.brand'] = 3;
                } elseif ($shelves_type == 7) {
                    $where['a.brand'] = 2;
                }
            } else {
                $where['c.shelves_type'] = $shelves_type;
            }
            if ($type == 9) {
                $where['a.up_down_channel'] = array('like', '%' . $channel_type . '%');
            } else {
                $where['c.up_down_channel_dlr'] = array('like', '%' . $channel_type . '%');
            }

            if (!empty($select)) {
                $where[] = ['exp', " (find_in_set({$select},c.comm_type_id_str) ) "];
            }
            if (!empty($search)) {
                $where['c.commodity_name'] = ['like', "%$search%"];
            }
            //type 1限时优惠 2NN 3满优惠 4预售 5多人拼团 6优惠套装 7秒杀 8抽奖 9众筹
            switch ($type) {
                case 1:
                    $where['a.set_type'] = $shelves_type;
                    $list                = (new DbLimitDiscount())->getLiveLimitCommodityList([
                        'where'    => $where,
                        'pagesize' => $page_size,
                        'group'    => 'c.commodity_id',
                        'field'    => 'a.id as activity_id,d.id as set_id,c.shelves_type,b.commodity_id as id,c.cover_image as image_path,c.commodity_name as goods_name,b.commodity_id,b.commodity_set_id,d.shelves_type,c.comm_type_id_str,c.price as original_price,b.lowest_price as price,c.tag,c.tag_gwapp,c.tag_qcsm,c.tag_qcapp,c.tag_pz1asm,c.tag_pz1aapp,d.pay_style,d.max_point,c.dd_commodity_type,c.is_grouped,d.commodity_label,a.start_time,a.end_time'
                    ]);
                    break;
                case 2:
                    $where['a.set_type'] = $shelves_type;
                    $list                = (new DbNDiscount())->getLiveNDiscountCommodityList([
                        'where'    => $where,
                        'pagesize' => $page_size,
                        'group'    => 'c.commodity_id',
                        'field'    => 'd.id as set_id,c.shelves_type,b.commodity_id as id,c.cover_image as image_path,c.commodity_name as goods_name,b.commodity_id,b.commodity_set_id,d.shelves_type,c.comm_type_id_str,c.price as original_price,c.final_price as price,c.tag,c.tag_gwapp,c.tag_qcsm,c.tag_qcapp,c.tag_pz1asm,c.tag_pz1aapp,d.pay_style,d.max_point,c.dd_commodity_type,c.is_grouped,d.commodity_label'
                    ]);
                    break;
                case 3:
                    $where['a.set_type'] = $shelves_type;
                    $list                = (new DbFullDiscount())->getLiveFullDiscountCommodityList([
                        'where'    => $where,
                        'pagesize' => $page_size,
                        'group'    => 'c.commodity_id',
                        'field'    => 'd.id as set_id,c.shelves_type,b.commodity_id as id,c.cover_image as image_path,c.commodity_name as goods_name,b.commodity_id,b.commodity_set_id,d.shelves_type,c.comm_type_id_str,c.price as original_price,c.final_price as price,c.tag,c.tag_gwapp,c.tag_qcsm,c.tag_qcapp,c.tag_pz1asm,c.tag_pz1aapp,d.pay_style,d.max_point,c.dd_commodity_type,c.is_grouped,d.commodity_label'
                    ]);
                    break;
                case 4:
                    $where['a.set_type'] = $shelves_type;
                    $list                = (new DbPreSale())->getLivePreSaleCommodityList([
                        'where'    => $where,
                        'pagesize' => $page_size,
                        'group'    => 'c.commodity_id',
                        'field'    => 'd.id as set_id,c.shelves_type,b.commodity_id as id,c.cover_image as image_path,c.commodity_name as goods_name,b.commodity_id,b.commodity_set_id,d.shelves_type,c.comm_type_id_str,c.price as original_price,c.final_price as price,c.tag,c.tag_gwapp,c.tag_qcsm,c.tag_qcapp,c.tag_pz1asm,c.tag_pz1aapp,d.pay_style,d.max_point,c.dd_commodity_type,c.is_grouped,d.commodity_label'
                    ]);
                    break;
                case 5:
                    $where['a.set_type'] = $shelves_type;
                    $list                = (new DbFightGroup())->getLiveFightCommodityList([
                        'where'    => $where,
                        'pagesize' => $page_size,
                        'group'    => 'c.commodity_id',
                        'field'    => 'd.id as set_id,c.shelves_type,b.commodity_id as id,c.cover_image as image_path,c.commodity_name as goods_name,b.commodity_id,b.commodity_set_id,d.shelves_type,c.comm_type_id_str,c.price as original_price,b.lowest_price as price,c.tag,c.tag_gwapp,c.tag_qcsm,c.tag_qcapp,c.tag_pz1asm,c.tag_pz1aapp,d.pay_style,d.max_point,c.dd_commodity_type,c.is_grouped,d.commodity_label'
                    ]);
                    break;
                case 6:
                    $where['a.type'] = $shelves_type;
                    $list            = (new BuCheapSuitIndex())->getLiveCheapCommodityList([
                        'where'    => $where,
                        'pagesize' => $page_size,
                        'group'    => 'c.commodity_id',
                        'field'    => 'd.id as set_id,c.shelves_type,b.commodity_id as id,c.cover_image as image_path,c.commodity_name as goods_name,b.commodity_id,b.commodity_set_id,d.shelves_type,c.comm_type_id_str,c.price as original_price,c.price as price,c.tag,c.tag_gwapp,c.tag_qcsm,c.tag_qcapp,c.tag_pz1asm,c.tag_pz1aapp,d.pay_style,d.max_point,c.dd_commodity_type,c.is_grouped,d.commodity_label'
                    ]);
                    break;
                case 8:
                    $where['a.set_type']            = $shelves_type;
                    $where['d.up_down_channel_dlr'] = array('like', '%' . $channel_type . '%');
                    $list                           = (new DbDraw())->getLiveDrawCommodityList([
                        'where'    => $where,
                        'pagesize' => $page_size,
                        'group'    => 'c.commodity_id',
                        'field'    => 'd.id as set_id,c.shelves_type,c.commodity_id as id,c.cover_image as image_path,c.commodity_name as goods_name,c.commodity_id,d.id commodity_set_id,d.shelves_type,c.comm_type_id_str,c.price as original_price,c.price as price,c.tag,c.tag_gwapp,c.tag_qcsm,c.tag_qcapp,c.tag_pz1asm,c.tag_pz1aapp,d.pay_style,d.max_point,c.dd_commodity_type,c.is_grouped,d.commodity_label'
                    ]);
                    break;
                case 7:
                    $where['a.set_type'] = $shelves_type;
                    $list                = (new DbSeckill())->getLiveSeckillCommodityList([
                        'where'    => $where,
                        'pagesize' => $page_size,
                        'group'    => 'c.commodity_id',
                        'field'    => 'a.id as activity_id,d.id as set_id,c.shelves_type,b.commodity_id as id,d.count_stock stock,c.cover_image as image_path,c.commodity_name as goods_name,b.commodity_id,b.commodity_set_id,d.shelves_type,c.comm_type_id_str,c.price as original_price,c.final_price as price,c.tag,c.tag_gwapp,c.tag_qcsm,c.tag_qcapp,c.tag_pz1asm,c.tag_pz1aapp,d.pay_style,d.max_point,c.dd_commodity_type,c.is_grouped,d.commodity_label,b.sku_stock,a.start_time,a.end_time'
                    ]);
                    foreach ($list as $value) {
                        if ($value['is_grouped'] == 1) {
                            $group_sub_commodity_id = Db::name('db_commodity_set_sku')->where([
                                'commodity_id' => $value['commodity_id'],
                                'is_enable'    => 1
                            ])->group('group_sub_commodity_id')->column('group_sub_commodity_id');
                            $stock                  = Db::name('db_commodity_set')->where([
                                'commodity_id' => array('in', $group_sub_commodity_id),
                                'shelves_type' => $shelves_type,
                                'is_enable'    => 1
                            ])->order('count_stock')->column('count_stock');
                            if (!empty($stock)) {
                                $value->stock = $stock[0];
                            }
                        } else {
                            $value->stock = $value['sku_stock'];
                        }
//                        else{
//                            $common = new \app\common\net_service\Common();
//                            $value->stock = $common->kill_count($value['commodity_id'], $value['activity_id']);
//                        }
                    }
                    #1120版本不用秒杀库存
//                    foreach ($list as $value){
//                        $sku_stock = 0;
//                        $array = json_decode($value['sku_stock'],true);
//                        foreach ($array as $val){
//                            $sku_stock += $val;
//                        }
//                        $value->stock = $sku_stock;
//                    }
                    break;
                case 9:
                    $list = (new DbCrowdfund())->getCommodityListPaginate([
                        'where'    => $where,
                        'pagesize' => $page_size,
                        'group'    => 'c.commodity_id',
                        'field'    => 'a.id as activity_id,c.commodity_id as id,c.commodity_id,b.commodity_set_id as set_id,a.title,b.plan_status,c.commodity_name goods_name,c.comm_type_id,c.comm_type_id_str,c.tag,c.price as original_price,c.tag_gwapp,c.tag_qcsm,c.tag_qcapp,c.tag_pz1asm,c.tag_pz1aapp,c.final_price as price,c.cover_image as image_path'
                    ]);
                    break;
            }
            if (!empty($list)) {
                $allclass = (new DbCommodityType())->getClass(1);
                foreach ($list as $key => $value) {
                    $final[]             = $value['id'];
                    $cat                 = explode(',', $value['comm_type_id_str']);
                    $list[$key]['one']   = isset($allclass['one'][$cat[0] ?? '10000']) ? $allclass['one'][$cat[0] ?? '10000'] : '';
                    $list[$key]['two']   = isset($allclass['two'][$cat[1] ?? '10000']) ? $allclass['two'][$cat[1] ?? '10000'] : '';
                    $list[$key]['three'] = isset($allclass['three'][$cat[2] ?? '10000']) ? $allclass['three'][$cat[2] ?? '10000'] : '';
                    $tag_name            = [];
                    $tag_list            = explode(",", $value[$tag]);
                    if (!empty($tag_list)) {
                        $tag_list = $this->checkTagArr($tag_list, $channel_type, $value['id']);
                        foreach ($tag_list as $vv) {
                            if (empty($vv)) {
                                continue;
                            }
                            $tag_name[] = DbCommodityFlat::tagArr($vv);
                        }
                    }
                    $list[$key]['tag_name'] = $tag_name;
                    $list[$key]['src']      = '';
                    $list[$key]['path']     = $value['goods_name'];
                    $list[$key]['savePath'] = [
                        'type' => '1',
                        'url'  => 'javascript:;',
                        'id'   => $value['commodity_id'],
                    ];
                    //处理
                    if ($type == 9) {
                        $list[$key]['progress'] = DbCrowdfundCommodity::$plan_status[$value['plan_status']];
                    }
                }
            }
        } else {
            if ($type == 1) {
                $where = [];
                if (!empty($commodity_name)) {
                    $where['commodity_name'] = ['like', "%$commodity_name%"];
                }

                if (!empty($select)) {
                    $where[] = ['exp', " (find_in_set({$select},comm_type_id_str) ) "];
                }
                $where['b.shelves_type']        = $shelves_type;
                $where['a.shelves_type']        = $shelves_type;
                if(empty($up_down_channel_dlr)){
                    $where['b.up_down_channel_dlr'] = array('like', '%' . $channel_type . '%');
                }

                $field  = "b.id as set_id,a.shelves_type,comm_type_id_str,a.commodity_id as id,commodity_name as goods_name,cover_image as image_path,a.price as sales_price,final_price as price,a.price as original_price,tag_pz1asm,tag_pz1aapp,tag,tag_gwapp,tag_qcsm,tag_qcapp,b.pay_style,b.pay_style as pay_type,b.max_point,a.dd_commodity_type,a.is_grouped,b.commodity_label,b.listing_type";
                $params = array(
                    'qurey'    => input('get.'),
                    'where'    => $where,
                    'pagesize' => input('pagesize', 10),
                    'order'    => "sort_order asc, updated_at desc", #商品排序规则和上架时间
                    'field'    => $field,
                );

                $flat = new DbCommodityFlat();
                $list = $flat->getCommodityList($params);
                if (!empty($list)) {
                    $allclass = (new DbCommodityType())->getClass(1);
                    foreach ($list as $key => $value) {
                        $final[]             = $value['id'];
                        $cat                 = explode(',', $value['comm_type_id_str']);
                        $list[$key]['one']   = isset($allclass['one'][$cat[0] ?? '10000']) ? $allclass['one'][$cat[0] ?? '10000'] : '';
                        $list[$key]['two']   = isset($allclass['two'][$cat[1] ?? '10000']) ? $allclass['two'][$cat[1] ?? '10000'] : '';
                        $list[$key]['three'] = isset($allclass['three'][$cat[2] ?? '10000']) ? $allclass['three'][$cat[2] ?? '10000'] : '';
                        $tag_name            = [];
                        $tag_list            = explode(",", $value[$tag]);
                        if (!empty($tag_list)) {
                            $tag_list = $this->checkTagArr($tag_list, $channel_type, $value['id']);
                            foreach ($tag_list as $vv) {
                                if (empty($vv)) {
                                    continue;
                                }
                                $tag_name[] = DbCommodityFlat::tagArr($vv);
                            }
                        }
                        $list[$key]['tag_name'] = $tag_name;
                        if ($value['listing_type'] == 2) {
                            $crowdfund_where[] = ['exp', sprintf("FIND_IN_SET('%s',b.up_down_channel)", $channel_type)];
                            $crowdfund_count   = Db::name('db_crowdfund_commodity')->alias('a')
                                ->join('t_db_crowdfund b', 'a.crowdfund_id = b.id')
                                ->where(['a.commodity_id' => $value['id'],])
                                ->where($crowdfund_where)->count();
                            if ($crowdfund_count == 0) {
                                unset($list[$key]);
                            }
                        }
                    }
                }
            } else {
                //获取商品分类
                $params['where']['a.is_enable']      = 1;
                $params['where']['a.level']          = $level;//默认全部三级分类
                $params['where']['a.comm_type_name'] = array('like', '%' . $search . '%');
                $params['pagesize']                  = input('pagesize', 10);
                if ($select != 0) {
                    $params['where']['a.comm_parent_id|a.id'] = array('in', $select);
                }
                if ($level == 2) {
                    $map                                      = ['level' => 2, 'is_enable' => 1, 'comm_parent_id' => $select];
                    $two_ids                                  = Db::name('db_commodity_type')->where($map)->column('id');
                    $params['where']['a.level']               = 3;
                    $params['where']['a.comm_parent_id|a.id'] = array('in', $two_ids);
                }
                $list     = Db::name('db_commodity_type')->alias('a')
                    ->join('db_commodity_type b', 'a.comm_parent_id = b.id')
                    ->where($params['where'])
                    ->field('a.id,a.comm_type_name as class_name,a.img image_path,b.id two,b.comm_parent_id one')
                    ->paginate($params['pagesize'], false, array('query' => $params['query']));
                $list     = $list->toArray();
                $allclass = (new DbCommodityType())->getClass();
                foreach ($list['data'] as $key => $value) {
                    $list['data'][$key]['one'] = isset($allclass['one'][$value['one']]) ? $allclass['one'][$value['one']] : '';
                    $list['data'][$key]['two'] = isset($allclass['two'][$value['two']]) ? $allclass['two'][$value['two']] : '';
                }
                return $list;
            }
        }

        if (!empty($final)) {
            $new_info = (new NetGoods())->goodsList([
                'commodity_ids' => implode(',', $final),
                'pageSize'      => 20
            ], ['id' => '', 'bind_unionid' => '', 'brand' => '', 'vin' => ''], $channel_type);

            foreach ($new_info['msg']['data'] as $item_new) {
                foreach ($list as &$item_one) {
                    if ($item_new['commodity_id'] == $item_one['id']) {
                        if (is_numeric($item_new['final_price'])) {
                            $item_one['price'] = number_format($item_new['final_price'], 2, '.', '');
                            $item_one['sales_price'] = number_format($item_new['final_price'], 2, '.', '');
                        } else {
                            // 处理非数值的情况，例如设置默认值或记录日志
                            $item_one['price'] = '0.00';
                            $item_one['sales_price'] = '0.00';
                        }
                        if (is_numeric($item_new['price'])) {
                            $item_one['original_price'] = number_format($item_new['price'], 2, '.', '');
                        } else {
                            // 处理非数值的情况，例如设置默认值或记录日志
                            $item_one['original_price'] = '0.00';
                        }

//                        $item_one['price']          = number_format($item_new['final_price'], 2, '.', '');
//                        $item_one['sales_price']    = number_format($item_new['final_price'], 2, '.', '');
//                        $item_one['original_price'] = number_format($item_new['price'], 2, '.', '');
                        $item_one['tag_name']       = $item_new['tag_name'];
                        break;
                    }
                }
            }
        }


//        //再查询一次 set_sku 表，和 price 再对比一次, 如果是组合商品，获取上架的最低价（选中的最低价格，如果全选或者全不选，就是所有子上品的上架的最低价）
//        foreach ($list as $key => &$value) {
//            if (!empty($value['is_grouped'])) {
//                continue;
//            }
//            $set_sku = (new DbCommoditySetSku())->getOne([
//                'where' => ['commodity_id' => $value['id'], 'is_enable' => 1, 'commodity_set_id' => $value['set_id']],
//                'order' => 'price asc'
//            ]);
//            if (!empty($set_sku) && ($set_sku['price'] < $value['price'])) {
//                $value['price'] = $set_sku['price'];
//            }
//        }

        return $list ?? [];
    }

    protected function checkTagArr($tag_arr, $up_down_dlr = 'GWSM', $commodity_id = 0)
    {
        $return = [];
        foreach ($tag_arr as $item) {
            if ($item < 10) {
                $return[] = $item;
                continue;
            }

            //10=>'优惠套装',11=>'满优惠',12=>'限时优惠',13=>'多人拼团',14=>'N件N折',15=>'预售活动'
            $clm = '';
            switch ($item) {
                case 10:
                    $obj = new BuCheapSuitIndex();
                    $sub = new BuCheapSuitSub();
                    $clm = 'index_id';
                    break;
                case 11:
                    $obj = new DbFullDiscount();
                    $sub = new DbFullDiscountCommDlr();
                    $clm = 'discount_activity_id';
                    break;
                case 12:
                    $obj = new DbLimitDiscount();
                    $sub = new DbLimitDiscountCommodity();
                    $clm = 'limit_discount_id';
                    break;
                case 13:
                    $obj = new DbFightGroup();
                    $sub = new DbFightGroupCommodity();
                    $clm = 'fight_group_id';
                    break;
                case 14:
                    $obj = new DbNDiscount();
                    $sub = new DbNDiscountCommodity();
                    $clm = 'n_id';
                    break;
                case 15:
                    $obj = new DbPreSale();
                    $sub = new DbPreSaleCommodity();
                    $clm = 'pre_sale_id';
                    break;
                case 16:
                    $obj = new DbSeckill();
                    $sub = new DbSeckillCommodity();
                    $clm = 'seckill_id';
                    break;
            }

            if (empty($clm)) {
                continue;
            }

            //查询对应渠道下是否有在处于活动中的
            $res = $obj->where('act_status', 2)
                ->whereLike("up_down_channel_dlr", "%" . $up_down_dlr . "%")->column('id');
            if (!empty($res)) {

                $find = $sub->whereIn($clm, $res)->where('commodity_id', $commodity_id)->find();
                if (!empty($find)) {
                    $return[] = $item;
                }
            }
        }
        return $return;
    }


    /**
     * 获取商品信息
     * @param $params
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getCommodityFlatInfo($params)
    {
        $params = parent::_checkParams($params);
        return $this->alias('a')
            ->join('db_commodity b', 'a.commodity_id = b.id')
            ->join('db_commodity_set c', 'c.commodity_id = b.id')
            ->where($params['where'])
            ->field($params['field'])
            ->order($params['order'])
            ->group($params['group'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));

    }


    public function setSkus()
    {
        $map = ['is_enable' => 1];
        return $this->hasMany(DbCommoditySetSku::class, 'commodity_set_id', 'commodity_sku_id')->where($map);
    }


    public function getTagZdy($flatInfo, $setInfo)
    {
        $tag_zdy = [];
        // 1限时折扣2团购3满减5套装6N件N折7预售8立减10秒杀12买赠 19-20卡券 21买赠券 22商品属性 {"act_type":"","act_id":""}
        // 标签排序：【秒杀活动】>【限时优惠】>【券标签】>【买赠】>【满优惠】>【N件N折】>【多人拼团】>【优惠套装】>【预售活动】>【商品属性标签】
        // 1.秒杀活动
        if (!empty($flatInfo['seckill_dis'])) {
            $seckill_arr   = json_decode($flatInfo['seckill_dis'], true);
            $seckill_id    = reset($seckill_arr)[0] ?? 0;
            $seckill_model = new DbSeckill();
            $tag           = $seckill_model->where('id', $seckill_id)->value('tag');
            if (!empty($tag)) {
                $tag = '(显示为' . $tag . ')';
            }
            $tag_zdy[] = [
                'act_name' => '秒杀活动', 'act_type' => 10, 'act_id' => $seckill_id, 'is_check' => false, 'tag' => $tag
            ];
        }

        // 2.限时折扣
        if (!empty($flatInfo['limit_dis'])) {
            $limit_arr   = json_decode($flatInfo['limit_dis'], true);
            $limit_id    = reset($limit_arr)[0] ?? 0;
            $limit_model = new DbLimitDiscount();
            $tag         = $limit_model->where('id', $limit_id)->value('tag');
            if (!empty($tag)) {
                $tag = '(显示为' . $tag . ')';
            }
            $tag_zdy[] = [
                'act_name' => '限时优惠', 'act_type' => 1, 'act_id' => $limit_id, 'is_check' => false, 'tag' => $tag
            ];
        }

        // 3.券标签
        if (!empty($flatInfo['card_id'])) {
            $card_arr = explode(',', $flatInfo['card_id']);
            $card_id  = $card_arr[0] ?? 0;

            $tag_zdy[] = [
                'act_name' => '券标签', 'act_type' => 19, 'act_id' => $card_id, 'is_check' => false, 'tag' => '(按具体券面额显示)',
            ];
        }

        // 4.买赠活动
        if (!empty($flatInfo['gift_dis'])) {
            $gift_arr   = json_decode($flatInfo['gift_dis'], true);
            $gift_id    = reset($gift_arr)[0] ?? 0;
            $gift_model = new DbGift();
            $tag        = $gift_model->where('id', $gift_id)->value('tag');
            if (!empty($tag)) {
                $tag = '(显示为' . $tag . ')';
            }
            $tag_zdy[] = [
                'act_name' => '买赠活动', 'act_type' => 12, 'act_id' => $gift_id, 'is_check' => false, 'tag' => $tag
            ];
        }

        // 5.满优惠
        if (!empty($flatInfo['full_dis'])) {
            $full_arr   = json_decode($flatInfo['full_dis'], true);
            $full_id    = reset($full_arr)[0] ?? 0;
            $full_model = new DbFullDiscount();
            $tag        = $full_model->where('id', $full_id)->value('tag');
            if (!empty($tag)) {
                $tag = '(显示为' . $tag . ')';
            }
            $tag_zdy[]  = [
                'act_name' => '满优惠', 'act_type' => 3, 'act_id' => $full_id, 'is_check' => false, 'tag' =>  $tag
            ];
        }

        // 6.N件N折
        if (!empty($flatInfo['n_dis'])) {
            $n_arr     = json_decode($flatInfo['n_dis'], true);
            $n_id      = reset($n_arr)[0] ?? 0;
            $n_model   = new DbNDiscount();
            $tag       = $n_model->where('id', $n_id)->value('tag');
            if (!empty($tag)) {
                $tag = '(显示为' . $tag . ')';
            }
            $tag_zdy[] = [
                'act_name' => 'N件N折', 'act_type' => 6, 'act_id' => $n_id, 'is_check' => false, 'tag' => $tag
            ];
        }

        // 7.多人拼团
        if (!empty($flatInfo['group_dis'])) {
            $group_arr   = json_decode($flatInfo['group_dis'], true);
            $group_id    = reset($group_arr)[0] ?? 0;
            $group_model = new DbFightGroup();
            $tag         = $group_model->where('id', $group_id)->value('tag');
            if (!empty($tag)) {
                $tag = '(显示为' . $tag . ')';
            }
            $tag_zdy[]   = [
                'act_name' => '多人拼团', 'act_type' => 2, 'act_id' => $group_id, 'is_check' => false, 'tag' =>  $tag
            ];
        }

        // 8.优惠套装
        if (!empty($flatInfo['cheap_dis'])) {
            $cheap_arr   = json_decode($flatInfo['cheap_dis'], true);
            $cheap_id    = reset($cheap_arr)[0] ?? 0;
            $cheap_model = new BuCheapSuitIndex();
            $tag         = $cheap_model->where('id', $cheap_id)->value('tag');
            if (!empty($tag)) {
                $tag = '(显示为' . $tag . ')';
            }
            $tag_zdy[]   = [
                'act_name' => '优惠套装', 'act_type' => 5, 'act_id' => $cheap_id, 'is_check' => false, 'tag' =>  $tag
            ];
        }

        // 9.预售活动
        if (!empty($flatInfo['pre_dis'])) {
            $pre_arr   = json_decode($flatInfo['pre_dis'], true);
            $pre_id    = reset($pre_arr)[0] ?? 0;
            $pre_model = new DbPreSale();
            $tag       = $pre_model->where('id', $pre_id)->value('tag');

            if (!empty($tag)) {
                $tag = '(显示为' . $tag . ')';
            }

            $tag_zdy[] = [
                'act_name' => '预售活动', 'act_type' => 7, 'act_id' => $pre_id, 'is_check' => false, 'tag' => $tag
            ];
        }

        // 10.商品属性
        $tag_zdy[] = [
            'act_name' => '商品属性', 'act_type' => 22, 'act_id' => $setInfo['commodity_attr'], 'is_check' => false, 'tag' => ''
        ];

        if (!empty($setInfo['tag_zdy'])) {
            $set_tag_zdy = json_decode($setInfo['tag_zdy'], true);
            foreach ($tag_zdy as $key => $item) {
                if ($item['act_type'] == $set_tag_zdy['act_type']) {
                    $tag_zdy[$key]['is_check'] = true;
                }
            }
        }
//        print_json($tag_zdy);
        return $tag_zdy;

    }


    /**
     * @return \think\model\relation\HasOne
     */
    public function potCustomerCommodity()
    {
        return $this->hasOne('DbRecommendHotCommodity', 'commodity_id', 'commodity_id')->where('recommend_type', 2);
    }


    /**
     * @return \think\model\relation\HasOne
     */
    public function hotSaleCommodity()
    {
        return $this->hasOne('DbRecommendHotCommodity', 'commodity_id', 'commodity_id')->where('recommend_type', 1);
    }


    public function recommendationBannerCommodity()
    {
        return $this->hasOne(BuGwRecommendationBannerCommodity::class, 'commodity_id', 'commodity_id');
    }
}
