<?php
/**
 *
 * @author: lzx
 * @time: 2017年05月06日
 */

namespace app\common\model\bu;

use app\common\model\db\DbDlr;
use app\common\model\db\DbSendCardPage;
use think\Exception;
use app\common\model\Common;
use tool\Logger;

class BuCardReceiveRecord extends Common
{
    protected $table = 't_bu_card_receive_record';
    protected $pk = 'id';

    /**
     * 领取卡券状态：1-领取;；2-未领取；3-已核销；4-已删除；5-已冻结
     */
    const STATUS_KEY1 = 1;
    const STATUS_KEY1_VALUE = '已领取';
    const STATUS_KEY2 = 2;
    const STATUS_KEY2_VALUE = '未领取';
    const STATUS_KEY3 = 3;
    const STATUS_KEY3_VALUE = '已核销';
    const STATUS_KEY4 = 4;
    const STATUS_KEY4_VALUE = '已失效';
    const STATUS_KEY5 = 5;
    const STATUS_KEY5_VALUE = '已冻结';
    const STATUS_KEY7 = 7;
    const STATUS_KEY7_VALUE = '待激活';
    const STATUS_LIST = [
        self::STATUS_KEY1 => self::STATUS_KEY1_VALUE,
        self::STATUS_KEY2 => self::STATUS_KEY2_VALUE,
        self::STATUS_KEY3 => self::STATUS_KEY3_VALUE,
        self::STATUS_KEY4 => self::STATUS_KEY4_VALUE,
        self::STATUS_KEY5 => self::STATUS_KEY5_VALUE,
        self::STATUS_KEY7 => self::STATUS_KEY7_VALUE,
    ];


    const SOURCE_KEY1 = 1;
    const SOURCE_KEY1_VALUE = '商品';
    const SOURCE_KEY2 = 2;
    const SOURCE_KEY2_VALUE = '活动';
    const SOURCE_KEY3 = 3;
    const SOURCE_KEY3_VALUE = '后台核销';
    const SOURCE_KEY4 = 4;
    const SOURCE_KEY4_VALUE = '事件插入';
    const SOURCE_KEY5 = 5;
    const SOURCE_KEY5_VALUE = '发券页';
    const SOURCE_KEY6 = 6;
    const SOURCE_KEY6_VALUE = '建忠的推送';
    const SOURCE_KEY7 = 7;
    const SOURCE_KEY7_VALUE = '车品精选';
    const SOURCE_KEY8 = 8;
    const SOURCE_KEY8_VALUE = 'CASA扫码关注发券';
    const SOURCE_KEY9 = 9;
    const SOURCE_KEY9_VALUE = '运营发券';
    const SOURCE_KEY23 = 23;
    const SOURCE_KEY23_VALUE = '卡券中心推送';
    const SOURCE_KEY24 = 24;
    const SOURCE_KEY24_VALUE = '会员权益';
    const SOURCE_KEY25 = 25;
    const SOURCE_KEY25_VALUE = '启辰会员权益';
    const SOURCE_KEY26 = 26;
    const SOURCE_KEY26_VALUE = '卡夫卡领券';
    const SOURCE_KEY27 = 27;
    const SOURCE_KEY27_VALUE = '卡券抽奖';

    const SOURCE_LIST = [
        self::SOURCE_KEY1  => self::SOURCE_KEY1_VALUE,
        self::SOURCE_KEY2  => self::SOURCE_KEY2_VALUE,
        self::SOURCE_KEY3  => self::SOURCE_KEY3_VALUE,
        self::SOURCE_KEY4  => self::SOURCE_KEY4_VALUE,
        self::SOURCE_KEY5  => self::SOURCE_KEY5_VALUE,
        self::SOURCE_KEY6  => self::SOURCE_KEY6_VALUE,
        self::SOURCE_KEY7  => self::SOURCE_KEY7_VALUE,
        self::SOURCE_KEY8  => self::SOURCE_KEY8_VALUE,
        self::SOURCE_KEY9  => self::SOURCE_KEY9_VALUE,
        self::SOURCE_KEY23 => self::SOURCE_KEY23_VALUE,
        self::SOURCE_KEY24 => self::SOURCE_KEY24_VALUE,
        self::SOURCE_KEY25 => self::SOURCE_KEY25_VALUE,
        self::SOURCE_KEY26 => self::SOURCE_KEY26_VALUE,
        self::SOURCE_KEY27 => self::SOURCE_KEY27_VALUE,
    ];


    public function getDrawCardPaginate($params)
    {
        $params = $this->_checkParams($params);
        $list   = $this->alias('a')->join('t_db_card b', 'a.card_id=b.id')
            ->join('t_db_dlr c', 'a.dlr_code=c.dlr_code')
            ->where($params['where'])->field($params['field'])->order($params['order'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }


    /**
     * @param $key
     * @return string
     */
    public function getSourceName($key)
    {
        $arr = self::SOURCE_LIST;
        return $arr[$key] ?? '';
    }

    /**
     * 领取记录、核销记录
     * @param $params
     * @return \think\Paginator
     */
    public function getCardConPaginate($params)
    {
        $params = $this->_checkParams($params);
        $list = $this->alias('a')
            ->join('t_db_user u', 'a.user_id=u.id', 'left')
            ->join('t_db_card b', 'a.card_id=b.id', 'left')
            ->join('t_db_dlr c', 'a.dlr_code=c.dlr_code', 'left')     //领取专营店
            ->join('t_db_dlr d', 'b.dlr_code=d.dlr_code', 'left')     //创建专营店
            ->join('t_db_dlr e', 'a.consume_dlr_code=e.dlr_code and b.brand_id=e.brand_type', 'left')     //核销专营店
            ->where($params['where'])->field($params['field'])->order($params['order'])
            ->paginate($params['pagesize'], $params['count'], array('query' => $params['query']));
        return $list;
    }

    /**
     * 优惠券id关联优惠券
     * @param $params
     * @return \think\Paginator
     */
    public function getJoinCard($params)
    {
        $params = $this->_checkParams($params);
        $res    = $this->alias('a')->join('t_db_card b', 'a.card_id=b.id')
            ->where($params['where'])->field($params['field'])->find();
        return $res;
    }


    /**
     * 优惠券id关联优惠券
     * @param $params
     * @return \think\Paginator
     */
    public function getJoinCardList($params)
    {
        $params = $this->_checkParams($params);
        if (!empty($params['where_or'])) {
            $whereOr = $params['where_or'];
            $res     = $this->alias('a')->join('t_db_card b', 'a.card_id=b.id')
                ->where($params['where'])->whereOr(function ($query) use ($whereOr) {
                    $query->where($whereOr);
                })->field($params['field'])->select();
        } else {
            $res = $this->alias('a')->join('t_db_card b', 'a.card_id=b.id')
                ->where($params['where'])->field($params['field'])->select();
        }
        return $res;
    }

    /**
     * 获取所有卡券归属
     * @param $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getCardConReturnDdlrList($params)
    {
        $params = $this->_checkParams($params);

        $dlr = DbDlr::field("dlr_name as return_dlr_name,dlr_code as return_dlr_code")->select();
        return collection($dlr)->toArray();

        $list = $this->alias('a')->join('t_db_card b', 'a.card_id=b.id', 'left')
            ->join('t_db_dlr c', 'a.dlr_code=c.dlr_code', 'left')
            ->join('t_db_dlr d', 'b.dlr_code=d.dlr_code', 'left')
            ->where($params['where'])->field($params['field'])->group($params['group'])->order($params['order'])
            ->select();
        return $list;
    }

    /**
     * 导出数据方法 - 查询所有
     * @param $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getCardConPaginateDown($params)
    {
        $params = $this->_checkParams($params);
        $list   = $this->alias('a')->join('t_db_card b', 'a.card_id=b.id', 'left')
            ->join('t_db_dlr c', 'a.dlr_code=c.dlr_code', 'left')
            ->join('t_db_dlr d', 'b.dlr_code=d.dlr_code', 'left')
            ->join('t_db_dlr e', 'a.consume_dlr_code=e.dlr_code', 'left')     //核销专营店
            ->where($params['where'])->field($params['field'])->order($params['order'])
            ->select();
        return $list;
    }

    /**
     * 卡券统计
     * @param $params
     * @return \think\Paginator
     */
    public function getCardStatis($params)
    {
        $params = $this->_checkParams($params);
        $list   = $this->alias('a')->join('t_db_card b', 'a.card_id=b.id', 'right')
            ->join('t_db_dlr c', 'a.dlr_code=c.dlr_code', 'left')
            ->where($params['where'])->field($params['field'])->order($params['order'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));
        return $list;
    }

    /**
     * 卡券统计查询活动列表
     * @param $params
     * @return false|\PDOStatement|string|\think\Collection
     */
    public function getCardConActName($params)
    {
        $params = $this->_checkParams($params);
        $list   = $this->alias('a')->join('t_db_card b', 'a.card_id=b.id', 'left')
            ->join('t_db_dlr c', 'a.dlr_code=c.dlr_code', 'left')
            ->where($params['where'])->whereNotNull($params['whereNotNull'])->distinct($params['distinct'])
            ->field($params['field'])->order($params['order'])->select();
        return $list;
    }

    public function getDrawCardList($params)
    {
        $params = $this->_checkParams($params);
        $list   = $this->alias('a')->join('t_db_card b', 'a.card_id=b.id')->where($params['where'])->order($params['order'])
            ->field($params['field'])->limit($params['limit'])->select();
        return $list;
    }

    public function getCardStatic($c_condition, $r_condition, $condition)
    {
        $this->alias('a')->join('(SELECT COUNT(1) count , card_id FROM t_bu_card_receive_record WHERE ' . $c_condition . ' GROUP BY card_id ) b ', 'a.id=b.card_id', 'left')
            ->join('SELECT  COUNT(1) count , card_id FROM t_bu_card_receive_record WHERE is_enable=1 AND `status`=3 GROUP BY card_id');
    }

    /**
     * 定保邀约获取卡券
     */
    public function getActCard($vin)
    {
        $sql  = "select IFNULL(b.act_name,'未定义')act_name,b.card_name,(case when a.created_date=a.consume_date then '-' else a.created_date end) created_date " . "
            , IFNULL(a.consume_date,'-')consume_date from t_bu_card_receive_record a inner join t_db_card b on a.card_id=b.id where a.vin='{$vin}'";
        $list = $this->query($sql);
        return $list;
    }

    /**
     * card
     */

    public function staticSql($get_date = '', $consum_date = '', $cardId = "")
    {
        $map  = [
            'card_id'   => ['neq', ''],
            'is_enable' => 1,
            'dlr_code'  => ['not null', ''],
            'card_code' => ['not null', ''],
        ];
        $map2 = ['card_id' => ['neq', ''], 'status' => 3, 'is_enable' => 1];
        if ($cardId != '') {
            $map['card_id']  = $cardId;
            $map2['card_id'] = $cardId;
        }
        $field1 = '0 as get_count , COUNT(1) consume_count, card_id, consume_dlr_code';
        $field2 = 'COUNT(1) get_count, 0 as consume_count , card_id ,dlr_code';
        $re1    = $this->table('t_bu_card_receive_record')->where($consum_date)->where($map2)->field($field1)->group('card_id,consume_dlr_code')->buildSql();
        $re2    = $this->table('t_bu_card_receive_record')->where($get_date)->where($map)->field($field2)->group('card_id,dlr_code')->union($re1)->buildSql();
        $sql    = "SELECT SUM(get_count) get_sum ,SUM(consume_count) consume_sum ,card_id,dlr_code FROM " . $re2 . "a GROUP BY card_id,dlr_code";
////        $sql = "SELECT SUM(get_count) get_sum ,SUM(consume_count) consume_sum ,card_id,dlr_code FROM ( " .
//            " SELECT COUNT(1) get_count, 0 as consume_count , card_id ,dlr_code FROM t_bu_card_receive_record WHERE  card_id != {$cardId} AND dlr_code IS not NULL AND card_code IS not NULL {$get_date} AND is_enable=1   GROUP BY card_id,dlr_code UNION all " .
//            "SELECT 0 as get_count , COUNT(1) consume_count, card_id,consume_dlr_code  FROM t_bu_card_receive_record WHERE is_enable=1 AND card_id !='' AND  `status`=3 {$consum_date} GROUP BY card_id,consume_dlr_code ) a GROUP BY card_id,dlr_code";
        return $sql;
    }

    public function getStaticList($params)
    {
        $cardId = '';
        if (!empty($params['where']['tb_card.id'])) {
            $cardId = $params['where']['tb_card.id'];
        }
//        dd($params);
        $params = $this->_checkParams($params);
        $re     = db('db_card')->alias('tb_card')->join('(' . $this->staticSql($params['get_date'], $params['consum_date'], $cardId) . ') tb_sum', 'tb_card.id=tb_sum.card_id', 'right');
//        if (!empty($params['where']['tb_dlr.dlr_name'])) {
        $re = $re->join('t_db_dlr tb_dlr', 'tb_dlr.dlr_code=tb_sum.dlr_code', 'left')
            ->join('t_db_dlr dlr2', 'dlr2.dlr_code=tb_card.dlr_code', 'left');
//        }
        $list = $re->field($params['field'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));

        return $list;
    }


    public function getStaticSum($params)
    {
        $cardId = '';
        if (!empty($params['where']['tb_card.id'])) {
            $cardId = $params['where']['tb_card.id'];
        }
        $params = $this->_checkParams($params);
        $list   = db('db_card')->alias('tb_card')->join('(' . $this->staticSql($params['get_date'], $params['consum_date'], $cardId) . ') tb_sum', ' tb_card.id=tb_sum.card_id', 'right')
            ->join('t_db_dlr tb_dlr', 'tb_dlr.dlr_code=tb_sum.dlr_code', 'left')->where($params['where'])->field($params['field_sum'])->find();

        return $list;
    }

    public function getCardEvent($card_id, $openid, $dlr_openid)
    {
        $row = $this->where(sprintf("card_id='%s'  and (openid='%s') and is_enable=1 and status<>2", $card_id, $openid, $dlr_openid))->order("created_date desc")->find();
        return $row;
        #test
    }


    public function getCardEventByUserId($card_id, $user_id,$card_code='',$channel_type='',$order_vin='')
    {
        //20230707，因为LY发券有先发晚过期的情况，改为有效期到期时间倒序 TZL
        $where = [
            'rec.card_id'=>['in',$card_id],
            'rec.is_enable'=>1,
        ];
//        if($order_vin){
//            $where[] =  ['exp',sprintf("rec.receive_vin='%s' or rec.user_id='%s'",$order_vin,$user_id)];
//        }else{
//            $where['rec.user_id'] = $user_id;
//        }
        if($order_vin){
            //就是有下单vin就判断下单vin？不需要管是不是那个用户领的？ 林茹说的
            $where[] = ['exp',sprintf(" (rec.receive_vin='%s' ) || (rec.user_id='%s'  and (rec.receive_vin='' or rec.receive_vin is null))",$order_vin,$user_id)];
        }else{
            // and (rec.receive_vin='' or rec.receive_vin is null)
            $where[] = ['exp',sprintf(" rec.user_id='%s'",$user_id)];
        }


        $where[] =  ['exp',sprintf("rec.status in (1,5) or (rec.status in (1,5,7) and  a.is_gift_card=1)")];
        if($card_code){
            $where['card_code'] = $card_code;
        }
        if($channel_type){
            $where[]      = ['exp', sprintf("(rec.activity_id>0 and  FIND_IN_SET('%s',act.up_down_channel_dlr)) or (rec.activity_id=0 and  FIND_IN_SET('%s',a.up_down_channel_dlr))", $channel_type, $channel_type)];

        }

        $row = $this->alias('rec')->join('t_db_card a',"a.id=rec.card_id")
            ->join('t_db_activity act',"act.activity_id=rec.activity_id and rec.activity_id>0",'left')->where($where)->field("rec.*,act.select_obj")->order("rec.validity_date_end desc")->select();
        foreach ($row as $k=>$v){
            if($v['select_obj']){
                if($v['select_obj']==1){
                    if($v['user_id']!=$user_id){
                        unset($row[$k]);
                    }
                }
                if($v['select_obj']==2){
                    if($v['receive_vin']!=$order_vin){
                        unset($row[$k]);
                    }
                }
                if($v['select_obj']==3){
                    if($v['receive_vin']!=$order_vin || $v['user_id']!=$user_id){
                        unset($row[$k]);
                    }
                }
            }
        }

//        $row = $this->alias('a')->where($where)->order("validity_date_end desc")->select();
        return array_values($row);
        #test
    }


    //获取商城卡券列表
    public function getCardShopCardList($params)
    {
        $params = $this->_checkParams($params);

        $res = $this->alias("a")->join("t_db_card b", "a.card_id=b.id and b.is_enable=1 and a.is_enable=1")->join("t_db_commodity_card c", "a.card_id=c.card_id")->where($params['where'])->field($params['field'])->select();
        return $res;
    }

    //获取商城卡券列表
    public function getCardShopCardFlat($params)
    {
        $params = $this->_checkParams($params);

        $res = $this->alias("a")
            ->join("t_db_card b", "a.card_id=b.id and b.is_enable=1 and a.is_enable=1")
            ->join("t_db_dlr c", "a.get_dlr_code=c.dlr_code and c.is_enable=1", 'left')
            ->join("t_bu_order d", "d.id=a.act_id", 'left')
            ->where($params['where'])->field($params['field'])
            ->group($params['group'])->order($params['order'])
            ->limit($params['limit'] ?? 200)->select();//去掉关联商品的限制
//        $res  = $this->alias("a")->join("t_db_card b","a.card_id=b.id and b.is_enable=1 and a.is_enable=1")->join("t_db_commodity_flat e "," FIND_IN_SET(`a`.`card_id`,e.card_id)")->where($params['where'])->field($params['field'])->group($params['group'])->select();
        return $res;
    }

    //获取商城卡券列表-分页版
    public function getCardShopCardFlatPage($params)
    {
        $params = $this->_checkParams($params);

        return $this->alias("a")
            ->join("t_db_card b", "a.card_id=b.id and b.is_enable=1 and a.is_enable=1")
            ->join("t_db_dlr c", "a.get_dlr_code=c.dlr_code and c.is_enable=1", 'left')
            ->where($params['where'])->field($params['field'])
            ->group($params['group'])->order($params['order'])
            ->paginate($params['pagesize'], false, array('query' => $params['query']));

    }


    // 获取海报页卡券领取详情
    public function getPosterDetail($params)
    {
        $params = $this->_checkParams($params);
        $query = $this->alias('a')
            ->join('t_bu_qy_poster b','a.poster_id=b.id','left')
            ->join('t_db_send_card_page c','b.send_card_page_id=c.id','left')
            ->join('t_db_dlr d', 'b.dlr_code=d.dlr_code', 'left')
            ->join('t_db_card e', 'a.card_id=e.id', 'left')
            ->where($params['where'])
            ->field($params['field'])
            ->group($params['group'])
            ->order($params['order']);
            if ($params['is_down'] == 1) {
                $list = $query->select();
                $recordIds   = array_column($list, 'ids');
            } else {
                $list = $query->paginate($params['pagesize'], false, array('query' => $params['query']));
                $recordIds   = array_column($list->toArray()['data'], 'ids');
            }

        // 取出活动页id
        $recordIdArr = [];
        foreach ($recordIds as $val) {
            $recordIdArr = array_unique(array_merge($recordIdArr, explode(',', $val)));
        }
        $map          = ['is_enable' => 1, 'id' => ['in', $recordIdArr]];
        $records      = $this->where($map)->field('id,status')->select();
        $statusArr = array_column(collection($records)->toArray(),'status', 'id');

        foreach ($list as $key => $item) {
            $idArr         = explode(',', $item['ids']);
            $get_count     = count($idArr);
            $consume_count = 0;
            foreach ($idArr as $val) {

                if (in_array($val, $statusArr)) {
                    // 查询领取记录里各个状态出现的次数
                    $count     = array_count_values($statusArr[$val]);
                    // 累计核销的次数
                    $consume_count += $count[3] ?? 0;
                }
            }
            $list[$key]['get_count'] = $get_count;
            $list[$key]['consume_count']  = $consume_count;
            $list[$key]['send_type_name'] = DbSendCardPage::$send_type[$item['send_type']];
        }
        return $list;
    }

}
