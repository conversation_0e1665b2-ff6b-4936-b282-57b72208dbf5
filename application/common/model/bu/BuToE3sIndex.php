<?php
namespace app\common\model\bu;

use think\Exception;
use app\common\model\Common;
use tool\Logger;

class BuToE3sIndex extends Common{
    protected $table = 't_bu_to_e3s_index';
    protected $pk    = 'id';


    // to_status 状态，0已付款，1已使用，2已结算，3已作废，4.已销退  5已退款
    public function getOrderPaginate($params){
        //$params['pagesize'] = $this->_pageSzie;
        $this->_checkParams($params);
        return $this->alias('a')
            ->join("t_bu_to_e3s_detail b", "a.to_order_code = b.to_order_code ")
            // ->join("t_bu_product_card pc", "a.product_card_pwd = pc.password", 'left')
            ->field($params['field'])
            ->where($params['where'])
            ->order($params['order'])
            ->group($params['group'])
            ->paginate($params['pagesize'], false, array('query'=>$params['query']));
    }
}