<?php
namespace app\common\model\e3s;

use app\common\model\Common;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCommoditySku;
use think\Db;

class E3sSparePart extends E3sCommon
{
    protected $table = 't_e3s_spare_part';
    protected $pk    = 'id';
    protected $createTime = 'created_date';
    protected $updateTime = 'modified_date';
    private $brand = [
        'N' =>'日产',
        'V' =>'启辰',
        'I' =>'英菲',
        'NVI' =>'日产/启辰/英菲',
        'NI' =>'日产/英菲',
        'NV' =>'日产/启辰',
        'VI' =>'启辰/英菲',
    ];

    //获取备件品牌
    public function partBrand()
    {
        $return_list = $this->group('part_brand_code')
            ->field('part_brand_code,part_brand_code_cn')
            ->select();
        $array = [];
        foreach ($return_list as $key => $value)
        {
            $array[$key]['part_brand_code'] = $value['part_brand_code'];
            $array[$key]['part_brand_code_cn'] = $this->brand[$value['part_brand_code_cn']];
        }
        return $array;
    }


    /**
     * 获取其他类
     * @return array|false|string
     */
    public function otherAttr()
    {
        return $this->where('other_attr','<>', '')->group('other_attr')->column('other_attr');
    }

    //获取备件大类
    public function varietyBig()
    {
        $return_list = $this->group('variety_code_big_code')
            ->field('variety_code_big_code,variety_code_big_name')
            ->select();
        $array = [];
        foreach ($return_list as $key => $value)
        {
            $array[$key]['variety_code_big_code'] = $value['variety_code_big_code'];
            $array[$key]['variety_code_big_name'] = $value['variety_code_big_name'];
        }
        return $array;
    }

    //获取备件小类
    public function varietySmall()
    {
        $return_list = $this->group('variety_code_small_name')
            ->field('variety_code_small_code,variety_code_small_name')
            ->select();
        $array = [];
        foreach ($return_list as $key => $value)
        {
            $array[$key]['variety_code_small_code'] = $value['variety_code_small_code'];
            $array[$key]['variety_code_small_name'] = $value['variety_code_small_name'];
        }
        return $array;
    }

    //获取重点品类
    public function categories()
    {
        return $this->group('variety_code_mid_code')
            ->field('variety_code_mid_code,variety_code_mid_name')
            ->select();
    }

    public function return_brand_cn($brand){
            return $this->brand[$brand] ?? '';
    }

    public function other_attr($part_no){
        $list =  $this->where(['part_no'=>$part_no])->find();
        return $list['other_attr'] ?? '';
    }

    //判断是否关联商品
    public function is_goods($part_no){
        $sku = new DbCommoditySku();
        $find = $sku->alias('a')
            ->where(['a.sku_code'=>$part_no, 'a.is_enable' => 1])->count();
        if(!empty($find)){
            return 1;
        }
        return 0;
    }

    //判断是否关联商品
    public function goods_name($no,$part_no){
        $goods_list = [];
        if($no == 1){
            $goods_list = Db::name('db_commodity_sku')->alias('a')
                ->join('t_db_commodity b','a.commodity_id = b.id')
                ->where([
                    'a.is_enable'=>1,
                    'a.sku_code' => $part_no
                ])->field('a.commodity_id,b.commodity_name')->group('a.commodity_id')->select();
        }elseif ($no == 2){
            $goods_list = Db::name('db_commodity_sku')->alias('a')
                ->join('t_db_commodity b','a.commodity_id = b.id')
                ->where([
                    'a.is_enable'=>1,
                    'a.sku_code' => array('like',"%".$part_no."%")
                ])->field('a.commodity_id,b.commodity_name')->select();
        }

        return $goods_list;
    }

    //获取是否存在商品
    public function is_goods_update_no($service_type,$update_no)
    {
        $count = 0;
        if($service_type == '备件'){
            $part_no = Db::name('e3s_spare_part_log')->where(['e3s_update_no'=>$update_no])->column('part_no');
            $count = Db::name('db_commodity_sku')->alias('a')
                ->where(['a.is_enable'=>1, 'a.sku_code' => array('in',$part_no)])
                ->count();
        }elseif ($service_type == '备件车型车系'){
            $part_no = Db::name('e3s_part_car_series_log')
                ->where(['e3s_update_no'=>$update_no])->group('part_no')->column('part_no');
            $count = Db::name('db_commodity_sku')->alias('a')
                ->where(['a.is_enable'=>1, 'a.sku_code' => array('in',$part_no)])
                ->count();
        }
        elseif ($service_type == '保养套餐'){
            $maintain_group_code = Db::name('e3s_maintenance_package')
                ->where(['e3s_update_no'=>$update_no])
                ->column('maintain_group_code');
            foreach ($maintain_group_code as $value){
                $count = Db::name('db_commodity_sku')->alias('a')
                    ->where(['a.is_enable'=>1, 'a.sku_code' => array('like',"%".$value."%")])
                    ->count();
                if(!empty($count)){
                    $count = 1;
                    break;
                }
            }
        }elseif ($service_type == '保养套餐产品'){
            $maintain_group_code = Db::name('e3s_maintenance_product_log')->alias('a')
                ->join('t_e3s_maintenance_package b','a.product_type_id = b.product_type_id')
                ->where(['a.e3s_update_no'=>$update_no])
                ->column('b.maintain_group_code');
            foreach ($maintain_group_code as $value){
                $count = Db::name('db_commodity_sku')->alias('a')
                    ->where(['a.is_enable'=>1, 'a.sku_code' => array('like',"%".$value."%")])
                    ->count();
                if(!empty($count)){
                    $count = 1;
                    break;
                }
            }
        }elseif ($service_type == 'PZ1A保养套餐'){
            $sp_basic_code = Db::name('e3s_pz1a_maintenance_package')
                ->where(['e3s_update_no'=>$update_no])
                ->column('sp_basic_code');
            $count = Db::name('db_commodity_sku')->alias('a')
                ->where(['a.is_enable'=>1, 'a.sku_code' => array('in',$sp_basic_code)])
                ->count();
            if(!empty($count)){
                $count = 1;
            }
        }
        return $count;
    }

    public function partList($params)
    {
        $params=$this->_checkParams($params);
        return $this->alias('a')
            ->join('t_e3s_specific_relation_part srp','a.part_no = srp.part_no','left')
            ->join('t_e3s_specific_relation sr','sr.spec_part_group_id = srp.spec_part_group_id','left')
            ->where($params['where'])
            ->field($params['field'])
            ->order($params['order'])
            ->paginate($params['pagesize'],false,array('query'=>$params['query']));
    }
}