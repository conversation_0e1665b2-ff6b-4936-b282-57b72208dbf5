<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/17
 * Time: 6:29 PM
 */

namespace app\common\net_service;


use app\common\model\db\DbActivity;
use app\common\model\db\DbActivityCard;
use app\common\model\db\DbActivityCenterLog;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodityCard;
use app\common\model\db\DbUserCarSeries;
use app\common\port\connectors\ActivityCenter;
use app\common\port\connectors\QuickWin;
use tool\Logger;

class NetActivityCenter extends Common
{

    private $prefix;
    public function __construct()
    {
        parent::__construct();
        $this->prefix = config("card_center.url" )."/activity-center-middle-service/middle/middle/v1/open/";
    }


    /**
     *  AC03-单个查询活动目标用户是否符合参加资格API
     * @param $user
     * @param $channel_type
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function verifyActivitySingleUser($user,$requestData = [])
    {
        $data = [
            'activityId'   => $requestData['activityId'] ?? '',
            'oneid' => empty($user['one_id']) ? '' : $user['one_id'],
            'requestId' =>'verify-activity-single-user',
            'vin' => $user['vin'] ?? '',
            'phone' => $user['phone'] ?? '',
        ];
      //  $activityCenter = new ActivityCenter();
        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=>$data['activityId'],
            'oneid'=>empty($user['one_id']) ? '' : $user['one_id'],
            'request_url'=>$this->prefix. 'verify-activity-single-user',
            'request_info'=>json_encode($data),
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);
        $ret =  ActivityCenter::create('pz1a')->verifyActivitySingleUser($data);
        $dbActivityCenterLog->where(['id'=>$logid])->update(['response_info'=>json_encode_cn($ret)]);
        return $ret;
    }

    /**
     * AC04-批量查询活动目标用户是否符合参加资格API
     * @param $user
     * @param $channel_type
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function verifyActivityBatchUser($user,$requestData = [])
    {
        $dataAll = [];
        $activityIdStr = "";
        $requestIdStr ="";

        foreach($requestData as $requestDataItem){
            $data = [
                'activityId'   => $requestDataItem['activityId'] ?? '',
                'requestId' => $requestDataItem['requestId'] ?? '',
                'oneid' => empty($user['one_id']) ? '' : $user['one_id'],
                'vin' => $user['vin'] ?? '',
                'phone' => $user['phone'] ?? '',
            ];
            $activityIdStr .= $requestDataItem['activityId'].",";
            $requestIdStr  .= $data['requestId'].",";
            $dataAll[] = $data;
        }

        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=>rtrim($activityIdStr,','),
            'request_id'=>'verify-activity-batch-user',
            'oneid'=>empty($user['one_id']) ? '' : $user['one_id'],
            'request_url'=>$this->prefix. 'verify-activity-batch-user',
            'request_info'=>json_encode($dataAll),
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);
        $ret =  ActivityCenter::create('pz1a')->verifyActivityBatchUser($dataAll);
        $dbActivityCenterLog->where(['id'=>$logid])->update(['response_info'=>json_encode_cn($ret)]);
        return $ret;
    }

    /**
     * AC05-活动中心接收多个活动用户行为及发放卡劵API
     * @param $user
     * @param $channel_type
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function sendBehaviorCoupon($user,$requestData = [])
    {
        $dataAll = [];
        $activityIdStr = "";
        $requestIdStr ="";

        foreach($requestData as $requestDataItem){
            $data = [
                'brand'     => $requestDataItem['brand'] ?? '',
                'activityId'   => $requestDataItem['activityId'] ?? '',
                'behaviorTime' => date("Y-m-d H:i:s"),
                'behaviorTriggerScene' => $requestDataItem['behaviorTriggerScene'] ?? '',
                'businessCode' => 'mall',
                'insuranceEndTime' => $requestDataItem['insuranceEndTime'] ?? '',
                'insuranceStartTime' => $requestDataItem['insuranceStartTime'] ?? '',
               // 'isSendUnActive' => $requestDataItem['isSendUnActive'] ?? true,
                'oneid' => empty($user['one_id']) ? '' : $user['one_id'],
                'requestId' => $requestDataItem['requestId']  ?? '',
                'businessOrderNo' => $requestDataItem['businessOrderNo'] ?? '',
                'bindCodeType'=>$requestDataItem['bindCodeType'] ?? "",
                'bindCode' => $requestDataItem['bindCode'] ?? '',

            'vin' => $user['vin'] ?? '',
                'phone' => $user['phone'] ?? '',
            ];
            $activityIdStr .= $requestDataItem['activityId'].",";
            $requestIdStr  .= $data['requestId'].",";
            $dataAll[] = $data;
        }

        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=>rtrim($activityIdStr,','),
            'request_id'=>'send-behavior-coupon',
            'oneid'=>empty($user['one_id']) ? '' : $user['one_id'],
            'request_url'=>$this->prefix. 'send-behavior-coupon',
            'request_info'=>json_encode($dataAll)."=====>".json_encode($user),
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);

        $ret =  ActivityCenter::create('pz1a')->sendBehaviorCoupon($dataAll);

        $dbActivityCenterLog->where(['id'=>$logid])->update(['response_info'=>json_encode_cn($ret)]);

        return $ret;
    }

    /**
     * AC06-活动中心接收多个活动用户行为及发送待激活卡劵API
     * @param $user
     * @param $channel_type
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function sendActivateCoupon($user,$requestData = [])
    {

        $dataAll = [];
        $activityIdStr = "";
        $requestIdStr ="";

        foreach($requestData as $requestDataItem) {
            $data = [
                'activityId' => $requestDataItem['activityId'] ?? '',
                'behaviorTime' =>  date("Y-m-d H:i:s"),
                'behaviorTriggerScene' => $requestDataItem['behaviorTriggerScene'] ?? '',
                'oneid' => empty($user['one_id']) ? '' : $user['one_id'],
                'requestId' => $requestDataItem['requestId'] ?? '',
                'businessOrderNo' => $requestDataItem['businessOrderNo'] ?? '',
                'bindCodeType'=>$requestDataItem['bindCodeType'] ?? "",
                'bindCode' => $requestDataItem['bindCode'] ?? '',
                'businessCode' => 'mall',
                'vin' => $user['vin'] ?? '',
                'phone' => $user['phone'] ?? '',
            ];
            $activityIdStr .= $requestDataItem['activityId'].",";
            $requestIdStr  .= $data['requestId'].",";
            $dataAll[] = $data;
        }

        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=>rtrim($activityIdStr,','),
            'request_id'=>'sendActivateCoupon',
            'oneid'=>empty($user['one_id']) ? '' : $user['one_id'],
            'request_url'=>$this->prefix. 'send-activate-coupon',
            'request_info'=>json_encode($dataAll)."=====>".json_encode($user),
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);
        $ret =  ActivityCenter::create('pz1a')->sendActivateCoupon($dataAll);
        $dbActivityCenterLog->where(['id'=>$logid])->update(['response_info'=>json_encode_cn($ret)]);
        return $ret;
    }

    /**
     * AC10-判断用户行为并激活卡券
     * @param $user
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function activateCoupon($user,$requestData = [])
    {

        $dataAll = [];
        $activityIdStr = "";
        $requestIdStr ="";
        foreach($requestData as $requestDataItem) {
            $data = [
                'activityId' => $requestDataItem['activityId'] ?? '',
                'behaviorTime' =>  date("Y-m-d H:i:s"),
                'behaviorTriggerScene' => $requestDataItem['behaviorTriggerScene'] ?? '',
                'businessCode' => 'mall',
                'insuranceStartTime' => $requestDataItem['insuranceStartTime'] ?? '',
                'insuranceEndTime' => $requestDataItem['insuranceEndTime'] ?? '',
                'oneid' => empty($user['one_id']) ? '' : $user['one_id'],
                'requestId' => $requestDataItem['requestId'] ?? '',
                'businessOrderNo' => $requestDataItem['businessOrderNo'] ?? '',
                'bindCodeType'=>$requestDataItem['bindCodeType'] ?? "",
                'bindCode' => $requestDataItem['bindCode'] ?? '',
                'vin' => $user['vin'] ?? '',
                'phone' => $user['phone'] ?? '',
                'brand'=> $requestDataItem['brand'],
                'activateNum'=>$requestDataItem['activateNum'] ?? '',
                'couponCodes'=>$requestDataItem['couponCodes'] ?? [],
                'dealerCode' => $requestDataItem['dealerCode'] ?? ''
            ];
            $activityIdStr .= $requestDataItem['activityId'].",";
            $requestIdStr  .= $data['requestId'].",";
            $dataAll[] = $data;
        }

        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=>rtrim($activityIdStr,','),
            'request_id'=>'activateCoupon',
            'oneid'=>empty($user['one_id']) ? '' : $user['one_id'],
            'request_url'=>$this->prefix. 'send-activate-coupon',
            'request_info'=>json_encode($dataAll)."=====>".json_encode($user),
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);
        $ret =  ActivityCenter::create('pz1a')->activateCoupon($dataAll);
        $dbActivityCenterLog->where(['id'=>$logid])->update(['response_info'=>json_encode_cn($ret)]);
        return $ret;
    }

    private function getChannel($channel){
           $data =  ['GWSM'=>'mini_app', 'QCSM'=>'mini_app','GWNET'=>'official_website','QCNET'=>'official_website','GWAPP'=>'app','QCAPP'=>'app'];
           return $data[$channel];
    }

    /**
     * 商城活动可领取卡券接口
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getMallActivityCouponList($user,$requestData,$componentType='')
    {
        $userId =  $user['id'] ?? '';

        $activityIdList=$requestData['activityIdList'];
        $couponIdList=$requestData['couponIdList'];
        if(!empty($activityIdList)){
            $activityIdList = array_unique($activityIdList);
            $activityIdList = implode(',',$activityIdList);
        }
        if(!empty($couponIdList)){
            $couponIdList = array_unique($couponIdList);
            $couponIdList = implode(',',$couponIdList);
        }
        //$vinList=$requestData['vinList'];
        $vinList = [];
        $carSList=[];
        $user_bind_car='';
        $vin_car_arr = [];
        if($userId){
            $carSerModel =  new DbUserCarSeries();
            $userCarSeries = $carSerModel->getList(['where'=>['user_id' => $userId,'is_enable'=>1,'car_brand_code'=>$user['brand']]]);
            if($userCarSeries){
                foreach ($userCarSeries as $u_v){
                    $u_bind_car_info = $u_v['car_series_name'].' '.$u_v['car_type_name'];
                    if($u_v['is_vin_car'] == 1){
                        $vinList[]=$u_v['vin'];
                        $vin_car_arr[$u_v['vin']] = $u_bind_car_info;
                    }
                    $carSList[]=$u_v['car_series_id'];
                    if($u_v['is_bind']==1){
                        $user_bind_car = $u_bind_car_info;
                    }
                }
            }
        }

//        $user['one_id'] = '815032a337094550b5396e7d9f68a9d37164';
//        $user['channel_type'] = 'GWAPP';
//        $activityIdList = 1809415424257822722;
//        $vinList="LGBR4DE41HR045172";
        if(!empty($vinList)){
            $vinList = implode(',',array_unique($vinList));
        } else {
            $vinList = '';
        }
       // $oneID = '866e0111a66244fbb7cb9d54ac5426c27389';
       // $vinList = 'LGBH92E03KY740040,LGBZTDKEQU5554365,LGBH92E07LY698196,LGB612E03CS006808,LGBPJDKEUA0410040,LGBG22E07BY043065,LGBM9AE99M1500070,LGBZJDJPPA5515597';
        if($couponIdList){

            $data = [
                'activityIdList'=>$activityIdList,
                'couponIdList'=>$couponIdList,
                'oneid'=>empty($user['one_id']) ? '' : $user['one_id'],
                'mallChannel'=>$this->getChannel($requestData['channel_type']),
                'vinList'=>$vinList,
                'brand'=>$user['brand']
            ];
            $redis_name = md5(json_encode($data));
//            $ret  = redis($redis_name);
            $ret = [];//这里不缓存
            if(!$ret){
                $data['goods_set_id'] = $requestData['goods_set_id'];
                $data['from_mall'] = $requestData['from_mall'];
                $data['from_user_id'] = $user['id'];
//                $dbActivityCenterLog =  new DbActivityCenterLog();
//                $logData=[
//                    'activity_id'=>$requestData['activityIdList'] ?? "",
//                    'request_id'=>'getMallActivityCouponList',
//                    'oneid'=>empty($user['one_id']) ? '' : $user['one_id'],
//                    'request_url'=>$this->prefix. 'getMallActivityCouponList',
//                    'request_info'=>json_encode($data),
//                ];
              //  $logid = $dbActivityCenterLog->insertGetId($logData);
//            unset($data['goods_set_id']);
//            unset($data['from_mall']);
                $ret =  ActivityCenter::create('pz1a')->getMallActivityCouponList($data);
              //  $dbActivityCenterLog->where(['id'=>$logid])->update(['response_info'=>json_encode_cn($ret)]);
                redis($redis_name,$ret,rand(1,3));
            }


        }else{
            $ret = [];
        }
//        $ret = [];
        if(isset($ret['result']) && $ret['result']==1){
            $ret = $ret['rows'];
            if($ret){

            }else{
                $ret['result']=0;
            }
            $ret[0]['car_s_id_arr'] =$carSList;
            $ret[0]['user_vin'] =$user['vin'];
            $ret[0]['user_bind_car'] =$user_bind_car;
            $ret[0]['vin_car_arr'] =$vin_car_arr;
            return $ret;
        }
        $ret[0]['car_s_id_arr'] =$carSList;
        $ret[0]['user_vin'] =$user['vin'];
        $ret[0]['user_bind_car'] =$user_bind_car;
        $ret[0]['vin_car_arr'] =$vin_car_arr;
        return $ret;

    }

    /**
     * 商城活动领取卡券接口
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function mallActivityReceiveCoupon($user,$requestData = [])
    {

        $dbActivityObj  = new DbActivity();
        $dbActivityInfo = $dbActivityObj->where(['activity_id'=>$requestData['activityId'],'is_enable'=>1])->find();
        $vin = '';

        if($dbActivityInfo['select_obj'] != 1){
            $vin = $user['vin'] ?? "";
        }
        $activityIdList = $requestData['activityId'];
        $couponIdList = $requestData['couponId'];

//        if(!empty($activityIdList)){
//            $activityIdList = explode(',',$activityIdList);
//        }
//        if(!empty($couponIdList)){
//            $couponIdList = explode(',',$couponIdList);
//        }
        $dlr_code =  $requestData['dlr_code'] ?? '';


        $data = [
            "activityId"=>$activityIdList,
            "couponId"=>$couponIdList,
            "oneid"=>empty($user['one_id']) ? '' : $user['one_id'],
//            "oneid"=>'da0eb71856de4a56be98467d3d3f45ea7425',
            "phone"=>$user['phone'] ?? '',
            'mallChannel'=>$this->getChannel($requestData['channel_type']??$user['dlr_code']),
            "vin"=>$vin,
            'storeCode'=>$dlr_code,
            'storeId'=>'',//科老板说要传的,后面产品又说可以不传
        ];

//        if(!empty($dlr_code)){
//
//            $redis_dlr_key                 = 'e3s_dlr_id_' . $dlr_code;

            //产品说非必传
           // $redis_dlr_id                  = redis($redis_dlr_key);
//            if (!empty($redis_dlr_id)) {
//                $data['storeId'] = $redis_dlr_id;
//            } else {
//                $dlr_list                   = QuickWin::create('ly_e3s_dlr')->postDlr(['dlrCode' => $dlr_code, 'sortType' => '2'], $user['brand']);
//                $data['storeId'] = $dlr_list['rows'][0]['dlrId'] ?? 0;
//                redis($redis_dlr_key, $data['storeId'], 3600);
//            }
//        }

//        if($dlr_code == "H2901"){
//            $data['storeId'] = 2120;
//        }

        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=>$data['activityId'],
            'request_id'=>'mallActivityReceiveCoupon',
            'oneid'=>empty($user['one_id']) ? '' : $user['one_id'],
            'request_url'=>$this->prefix. 'mallActivityReceiveCoupon',
            'request_info'=>json_encode($data),
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);
        $ret =  ActivityCenter::create('pz1a')->mallActivityReceiveCoupon($data);
        $dbActivityCenterLog->where(['id'=>$logid])->update(['response_info'=>json_encode_cn($ret)]);
        return $ret;
    }


    /**
     * ac15接收多个活动用户行为使卡券失效-取消激活
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function invalidActivityCoupon($requestData = [])
    {
        $dataAll = [];
        $activityIdStr = "";
        $one_id = '';
        foreach($requestData as $requestDataItem) {
            $data = [
                'activityId' => $requestDataItem['activityId'] ?? '',
                'behaviorTime' =>  date("Y-m-d H:i:s"),
                'behaviorTriggerScene' => $requestDataItem['behaviorTriggerScene'] ?? '',
                'bindCode'=>$requestDataItem['bindCode'] ?? "",
                'bindCodeType'=>$requestDataItem['bindCodeType'] ?? "",
                'businessCode' => 'mall',

                'businessOrderNo' => $requestDataItem['businessOrderNo'] ?? "",
                'businessOrderStatus'=>$requestDataItem['businessOrderStatus'] ?? "",
                'oneid' =>$requestDataItem['oneid'],
                'requestId' => $requestDataItem['requestId'] ?? '',
                'vin' => $requestDataItem['vin'] ?? '',
                'couponCodes' => $requestDataItem['couponCodes'] ?? [],
            ];
            $one_id = $requestDataItem['oneid'];
            $activityIdStr .= $requestDataItem['activityId'].",";
            $dataAll[] = $data;
        }

        $dbActivityCenterLog =  new DbActivityCenterLog();
        $logData=[
            'activity_id'=>rtrim($activityIdStr,','),
            'request_id'=>'invalidActivityCoupon',
            'oneid'=>$one_id,
            'request_url'=>$this->prefix. 'invalidActivityCoupon',
            'request_info'=>json_encode($dataAll),
        ];
        $logid = $dbActivityCenterLog->insertGetId($logData);
        $ret =  ActivityCenter::create('pz1a')->invalidActivityCoupon($dataAll);
        $dbActivityCenterLog->where(['id'=>$logid])->update(['response_info'=>json_encode_cn($ret)]);
        return $ret;
    }



    /**
     * 卡券处理
     * @param array $cardIdArr
     */
    public function expireCard($cardIdArr = [])
    {
        $card_model = new DbCard();

        try {
            $map = [
                'card_type'    => ['neq', 6], // 不查到店代金券，到店代金券没有过期时间
                'shelves_type' => ['in', [5, 6, 7]],
                'act_status'   => ['in', [1, 2]],
                'is_enable'    => 1,
            ];
            if (!empty($cardIdArr)) {
                $map['id'] = ['in', $cardIdArr];
            }
            $card_model->where($map)
                ->chunk(100, function ($arr) use ($card_model) {
                    $time = time();
                    foreach ($arr as $card_item) {
                        $item_start_time = strtotime($card_item['receive_start_date']); // 领券开始时间
                        $item_end_time   = strtotime($card_item['validity_date_end']) + 86400; // 核销结束时间
                        $map             = ['id' => $card_item['id']];
                        $activity_id = $this->getActivity($card_item['id']);

                        $upd = ['activity_id' => $activity_id, 'last_updated_date' => date('Y-m-d H:i:s')];
                        if ($activity_id != 0) {
                            $upd['admin_act_id'] = $activity_id;
                        }
                        if ($card_item['act_status'] == 1) {
                            //卡券未开始
                            if (($time >= $item_start_time) && ($time < $item_end_time)) {
                                //卡券状态更新为 2
                                $upd['act_status']  = 2;
                            } else if ($time >= $item_end_time) {
                                //卡券状态更新为 3
                                $upd['act_status']  = 3;
                                $upd['activity_id'] = 0;
                                // 失效卡券商品关联表数据
                                $this->expire_commodity_card($card_item['id']);
                            }
                        } else {
                            //卡券已开始
                            if ($time >= $item_end_time) {
                                //卡券状态更新为 3
                                $upd['act_status']  = 3;
                                $upd['activity_id'] = 0;
                                // 失效卡券商品关联表数据
                                $this->expire_commodity_card($card_item['id']);
                            }
                        }
                        $card_model->where($map)->update($upd);
                    }
                });
        } catch (\Exception $e) {
            Logger::error("expire card : " . $e->getMessage());
        }
    }



    public function getActivity($cardId)
    {

        // 查询卡券关联的活动
        $map = [
            'a.card_id'              => $cardId,
            'b.activity_time_end'    => ['gt', date('Y-m-d H:i:s')],
            'b.activity_status_flag' => 1, // 开启
        ];
        $model = new DbActivityCard();
        return $model->alias('a')
                ->join('t_db_activity b', 'a.activity_id = b.activity_id and b.is_enable=1')
                ->where($map)
                ->order('b.activity_time_start')
                ->value('a.activity_id') ?? 0;
    }


    public function expire_commodity_card($cardId)
    {
        $commodity_card_model = new DbCommodityCard();
        $map                  = ['card_id' => $cardId];
        $upd                  = [
            'is_enable' => 0,
            'modifier'  => 'expire_card'
        ];
        $commodity_card_model->where($map)->update($upd);
    }


}
