<?php


namespace app\common\net_service;


use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuOrderGift;
use app\common\model\db\DbActivity;
use app\common\model\db\DbActivityAssSku;
use app\common\model\db\DbCard;
use app\common\model\db\DbCardLog;
use app\common\model\db\DbUser;
use app\common\port\connectors\QuickWin;
use think\Exception;

class NetGiftOrder extends Common
{

    /**
     * 退款-买赠券订单
     * @param $orderInfo
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function afterGiftOrder($orderInfo)
    {
        $behaviorTriggerScene = 'ShangChengDingDanXiaoTui';
        $order_c_model        = new BuOrderCommodity();
        $map                  = [
            'order_code'   => $orderInfo['order_code'],
            'gift_card_id' => ['neq', ''],
            'is_gift'      => 0, // 主品
        ];
        // 主品退款
        $commodity = $order_c_model->where($map)->find();
        if (empty($commodity)) {
            return;
        }

        // 查询主单一起下单的子品
        $map             = ['order_code' => $orderInfo['order_code'], 'is_gift' => 1];
        $zhu_commodities = $order_c_model->where($map)->select();
        $card_code       = [];
        if (!empty($zhu_commodities)) {
            // 新的返核销
            foreach ($zhu_commodities as $commodity) {
                if (!empty($commodity['card_codes'])) {
                    $arr       = explode(',', $commodity['card_codes']);
                    $card_code = array_merge($card_code, $arr);
                }
            }
        }
        $order_gift_model = new BuOrderGift();

        // 卡券中心  返核销返激活
        if (!empty($card_code)) {
            $map  = [
                'card_code' => ['in', $card_code],
                'is_enable' => 1
            ];
            $list = $order_gift_model->where($map)->select();
            // 卡券中心 返核销返激活
            $this->unConsumeUnactive($orderInfo['parent_order_code'], $orderInfo['user_id'], $list);

            // 同步活动中心返激活
            $this->invalidCoupon($list, 18, $orderInfo['user_id'], $behaviorTriggerScene, $orderInfo['order_vin']);
        }

        // 查询分开下单的子品
        $where        = [
            'parent_order_code' => $orderInfo['parent_order_code'],
            'card_code'         => ['not in', $card_code],
            'is_enable'         => 1
        ];
        $zi_gift_list = $order_gift_model->where($where)->select();

        if (!empty($zi_gift_list)) {
            // 返激活
            $this->invalidCoupon($zi_gift_list, 18, $orderInfo['user_id'], $behaviorTriggerScene, $orderInfo['order_vin']);
        }
    }


    /**
     * 取消-买赠券订单
     * @param $orderInfo
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function cancelGiftOrder($orderInfo)
    {
        // 查询是否是买赠券订单
        $gift_order_model = new BuOrderGift();
        $map              = [
            'parent_order_code' => $orderInfo['parent_order_code'],
            'is_enable'         => 1
        ];
        $giftOrderList    = $gift_order_model->where($map)->select();
        if (empty($giftOrderList)) {
            return;
        }
        $behaviorTriggerScene = 'ShangChengDingDanQuXiaoZhiFu';
        // 返激活
        $this->invalidCoupon($giftOrderList, 3, $orderInfo['user_id'], $behaviorTriggerScene, $orderInfo['order_vin']);
    }


    /**
     * 返激活卡券
     * @param $giftOrderList
     * @param $orderStatus
     * @param $userId
     * @param $behaviorTriggerScene
     * @param $orderVin
     */
    private function invalidCoupon($giftOrderList, $orderStatus, $userId, $behaviorTriggerScene, $orderVin)
    {
        // 失效记录
        $user_model       = new DbUser();
        $gift_order_model = new BuOrderGift();
        $oneid            = $user_model->where('id', $userId)->value('one_id');
        $data             = [];
        $couponCodeArr    = [];
        foreach ($giftOrderList as $item) {
            $couponCodeArr[$item['activity_id']][] = $item['coupon_code'];
            $gift_order_model->where('id', $item['id'])->update(['is_enable' => 0]);
            $data[$item['activity_id']] = [
                'activityId'           => $item['activity_id'],
                'behaviorTriggerScene' => $behaviorTriggerScene,
                'businessOrderNo'      => $giftOrderList[0]['parent_order_code'],
                'businessOrderStatus'  => $orderStatus,
                'oneid'                => $oneid,
                'requestId'            => $giftOrderList[0]['parent_order_code'] . '-F' . rand(100, 999),
                'vin'                  => $orderVin,
                'card_id'              => $item['card_id'],
            ];
        }

        $net_activity = new NetActivityCenter();
        $log_model    = new DbCardLog();
        foreach ($data as $key => $datum) {
            $datum['couponCodes'] = $couponCodeArr[$datum['activityId']] ?? [];

            $log        = [
                'user_id'      => $userId,
                'event_type'   => 9, // 反激活
                'card_id'      => $datum['card_id'],
                'request_info' => json_encode_cn($datum),
            ];
            $logId      = $log_model->insertGetId($log);
            $is_success = 0;
            try {
                $re         = $net_activity->invalidActivityCoupon([$datum]);
                $is_success = 1;
                $msg        = json_encode_cn($re);
            } catch (Exception $e) {
                $msg = $e->getMessage();
            }

            $upd = [
                'is_success'    => $is_success,
                'response_info' => $msg
            ];
            $log_model->where('id', $logId)->update($upd);
        }


    }


    /**
     * 返核销返激活
     * @param $orderCode
     * @param $userId
     * @param $giftOrderList
     */
    private function unConsumeUnactive($orderCode, $userId, $giftOrderList)
    {
        // 查询是否是买赠券订单
        $gift_order_model = new BuOrderGift();
        // 失效记录
        $cardReceiveId = [];
        $data          = [];
        foreach ($giftOrderList as $item) {
            $gift_order_model->where('id', $item['id'])->update(['is_enable' => 0]);
            $cardReceiveId[] = $item['card_receive_id'];
            $data[]          = [
                'business_order_no' => $orderCode,
                'coupon_code'       => $item['coupon_code'],
                'modifier'          => '',
            ];
        }

        $log_model  = new DbCardLog();
        $log        = [
            'user_id'      => $userId,
            'event_type'   => 10, // 返核销-反激活
            'card_id'      => $giftOrderList[0]['card_id'],
            'request_info' => json_encode_cn($data),
        ];
        $logId      = $log_model->insertGetId($log);
        $is_success = 0;
        try {
            $net_user = new NetUser();
            $re       = $net_user->postBatchUnConsumeUnactive($data);
            if ($re['result'] == 1) {
                $is_success = 1;
                // 更新卡券领取记录  状态变更为待激活
                $upd          = [
                    'status'               => 7,
                    'consume_date'         => '',
                    'consume_dlr_code'     => '',
                    'consume_order_code'   => '',
                    'intention_store'      => '',
                    'get_dlr_code'         => '',
                    'quick_win_is_consume' => 0,
                    'last_updated_date'    => date('Y-m-d H:i:s'),
                ];
                $card_r_model = new BuCardReceiveRecord();
                $card_r_model->whereIn('id', $cardReceiveId)->update($upd);
            }
            $msg = json_encode_cn($re);
        } catch (Exception $e) {
            $msg = $e->getMessage();
        }
        $upd = [
            'is_success'    => $is_success,
            'response_info' => $msg
        ];
        $log_model->where('id', $logId)->update($upd);
    }


    /**
     * 发起售后判断
     * 赠品券订单售后
     * @param $orderInfo
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function giftOrderAfterSale($orderInfo)
    {
        // 判断是否是买赠券主品订单
        $order_c_model = new BuOrderCommodity();
        $map           = [
            'order_code'   => $orderInfo['order_code'],
            'is_gift'      => 0,
            'gift_card_id' => ['neq', ''],
        ];
        $commodity     = $order_c_model->where($map)->find();
        if (empty($commodity)) {
            return $this->re_msg('success');
        }

        // 查询主单一起下单的赠品
        $map             = ['order_code' => $orderInfo['order_code'], 'is_gift' => 1];
        $zhu_commodities = $order_c_model->where($map)->select();
        $card_code       = [];
        if (!empty($zhu_commodities)) {
            // 主品下单的赠品不为空
            foreach ($zhu_commodities as $commodity) {
                $arr       = explode(',', $commodity['card_codes']);
                $card_code = array_merge($card_code, $arr);
            }
        }


        $where = [
            'parent_order_code' => $orderInfo['parent_order_code'],
            'is_enable'         => 1,
            'gift_order_code'   => ['neq', $orderInfo['order_code']],
        ];
        if (!empty($card_code)) {
            $where['card_code'] = ['not in', $card_code];
        }
        // 查询分开下单的赠品订单
        $data              = [];
        $freeze_receive_id = []; // 需要解冻的
        $cancel_order      = []; // 需要取消的订单
        $gift_order_model  = new BuOrderGift();
        $card_r_model      = new BuCardReceiveRecord();

        $gift_order_list = $gift_order_model->where($where)->select();
        if (!empty($gift_order_list)) {
            $order_model = new BuOrder();
            foreach ($gift_order_list as $key => $gift_order) {

                // 查询卡券的领取记录
                $card_r_info = $card_r_model->where(['card_code' => $gift_order['card_code']])->find();

                if (!empty($gift_order['gift_order_code'])) {
                    $gift_order_code = $gift_order['gift_order_code'];
                    // 判断1  卡券已核销
                    $gift_order_info = $order_model->whereIn('order_code', $gift_order_code)->find();
                    $apply_arr       = [1, 2, 3, 8, 18];
                    if (!in_array($gift_order_info['order_status'], $apply_arr)) {
                        // 已核销 提示：订单包含的赠品已核销，不可退款
                        return $this->re_msg('订单包含的赠品正在处理中或已核销，不可退款', 414);
                    }


                    // 判断2  已支付的订单 或者卡券状态 是已使用  提示先发起子单售后
                    if ($gift_order_info['order_status'] == 2 || $card_r_info['status'] == 3) {
                        $data[] = $gift_order_info['order_code'];
                    }

                    // 冻结未使用的卡券
                    if ($card_r_info['status'] == 1) {
                        // 卡券未核销  需要冻结
                        $freeze_receive_id[] = $gift_order['card_receive_id'];
                    }


                } else {

                    // 判断3 冻结的订单  自动取消订单
                    if ($card_r_info['status'] == 5 && $card_r_info['consume_order_code'] != $gift_order['parent_order_code']) {
                        $cancel_order[] = [
                            'card_id'         => $gift_order['card_id'],
                            'order_code'      => $card_r_info['consume_order_code'],
                            'card_receive_id' => $gift_order['card_receive_id'],
                            'coupon_code'     => $gift_order['coupon_code'],
                        ];
                    }
                    if ($card_r_info['status'] == 1) {
                        // 卡券未核销  需要冻结
                        $freeze_receive_id[] = $gift_order['card_receive_id'];
                    }
                }
            }

            if (!empty($data)) {
                $order_codes = implode(',', $data);
                return $this->re_msg('此订单关联的赠品订单:' . $order_codes . '需要先退赠品订单才可发起此订单的退款', 414);
            }

            // 需要取消的订单
            if (!empty($cancel_order)) {
                $upd             = [
                    'order_status'      => 3,
                    'modifier'          => 'gift_cancel',
                    'last_updated_date' => date('Y-m-d H:i:s'),
                ];
                $changeFreeze    = [];
                $card_receive_id = [];
                foreach ($cancel_order as $cancel) {
                    $order_model->where('order_code', $cancel['order_code'])->update($upd);
                    /*(new NetOrder())->orderChange($cancel['order_code']);
                    // 再次发起冻结
                    $freeze_receive_id[] = $cancel['card_receive_id'];*/
                    $changeFreeze[]    = [
                        'business_order_no_new' => $orderInfo['order_code'],
                        'business_order_no_old' => $cancel['order_code'],
                        'coupon_code'           => $cancel['coupon_code'],
                    ];
                    $card_receive_id[] = $cancel['card_receive_id'];
                }
                // 更换接口  冻结状态业务单号变更
                $card_log_model = new DbCardLog();
                $couponData     = ['coupon_freeze_change_params' => $changeFreeze];
                $log            = [
                    'user_id'      => $orderInfo['user_id'],
                    'event_type'   => 11, // 更换冻结业务单号
                    'card_id'      => $cancel_order[0]['card_id'],
                    'request_info' => json_encode_cn($couponData),
                ];
                $logId          = $card_log_model->insertGetId($log);
                $is_success     = 0;
                try {
                    $re = QuickWin::create('quick_win')->freezeCouponChange($couponData);
                    if (isset($re['result']) && $re['result'] == 1) {
                        $is_success = 1;
                        // 更新冻结订单号
                        $upd = [
                            'consume_order_code' => $orderInfo['order_code'],
                            'modifier'           => 'freeze_change',
                            'last_updated_date'  => date('Y-m-d H:i:s'),
                        ];
                        $card_r_model->whereIn('id', $card_receive_id)->update($upd);
                    }
                    $msg = json_encode_cn($re);
                } catch (Exception $e) {
                    $msg = $e->getMessage();
                }
                $upd = [
                    'is_success'    => $is_success,
                    'response_info' => $msg,
                ];
                $card_log_model->where('id', $logId)->update($upd);
                if (!$is_success) {
                    return $this->re_msg('批量更新冻结业务单号失败', 414);
                }
            }
        }


        // 未使用的赠品券 发起冻结
        if (!empty($freeze_receive_id)) {
            $net_user     = new NetUser();
            $card_r_model = new BuCardReceiveRecord();
            $map          = [
                'id'     => ['in', $freeze_receive_id],
                'status' => 1,
            ];
            $receive_list = $card_r_model->where($map)->select();
            foreach ($receive_list as $item) {
                $freeze_data = [
                    'business_order_no' => $orderInfo['parent_order_code'],
                    'coupon_code'       => $item['coupon_code'],
                    'card_code'         => $item['card_code'],
                ];
                $re          = $net_user->freezeCoupon($orderInfo['user_id'], $freeze_data);
                if ($re['code'] <> 200) {
                    return $this->re_msg($re['msg'], $re['code']);
                }
            }
        }


        return $this->re_msg('success', 200, $data);
    }


    /**
     * 赠品券订单售后审核
     * @param $orderInfo
     * @param $afterStatus
     * @return array|\think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function afterGiftCheck($orderInfo, $afterStatus)
    {
        // 判断是否是赠品订单主品
        $order_c_model = new BuOrderCommodity();
        $map           = [
            'order_code'   => $orderInfo['order_code'],
            'gift_card_id' => ['neq', ''],
            'is_gift'      => 0,
        ];
        $commodity     = $order_c_model->where($map)->find();
        if (empty($commodity)) {
            return $this->re_msg('success');
        }

        $gift_order_model = new BuOrderGift();
        $map              = [
            'parent_order_code' => $orderInfo['parent_order_code'],
            'is_enable'         => 1,
        ];
        $gift_order_list  = $gift_order_model->where($map)->select();
        if (!empty($gift_order_list)) {
            if ($afterStatus == 3) {
                // 拒绝
                $this->cancelAfterGiftOrder($orderInfo);
            } else {
                // 通过
                // 判断当前订单是否主赠一起
                $where = [
                    'order_code'   => $orderInfo['order_code'],
                    'gift_card_id' => ['neq', ''],
                    'is_gift'      => 1,
                ];
                $re    = $order_c_model->where($where)->find();
                if (empty($re)) {
                    // 主品和赠品分开下单
                    // 判断已使用的赠品券是否都退款了
                    $order_model  = new BuOrder();
                    $z_order_code = array_column($gift_order_list, 'gift_order_code');
                    // 查询赠品券订单的赠品单是否都已审核通过
                    $order_status_arr = $order_model->whereIn('order_code', $z_order_code)->column('order_status');
                    $status           = 18;
                    $re               = count(array_filter($order_status_arr, function ($item) use ($status) {
                            return $item === $status;
                        })) === count($order_status_arr);
                    if (!$re) {
                        return $this->re_msg('请把赠品订单都退款再操作', 401);
                    }
                } else {
                    // 主品和赠品一起下单  不需要做处理

                }
            }
        }
        return $this->re_msg('success');
    }


    /**
     * 赠品券解冻
     * @param $orderCode
     * @param $userId
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function giftUnfreezeCoupon($orderCode, $userId)
    {
        // 查询卡券是否是赠品订单
        $order_gift_model = new BuOrderGift();
        $map              = ['a.parent_order_code' => $orderCode, 'a.is_enable' => 1];
        $field            = 'b.coupon_code,b.coupon_receive_id,b.consume_order_code,b.card_code';
        $list             = $order_gift_model->alias('a')
            ->join('t_bu_card_receive_record b', 'a.card_receive_id=b.id and b.status= 5') // 冻结的订单
            ->where($map)
            ->field($field)
            ->select();
        if (empty($list)) {
            return;
        }
        $net_user = new NetUser();
        foreach ($list as $key => $item) {
            $card_data = [
                'business_order_no' => $orderCode,
                'coupon_code'       => $item['coupon_code'],
                'coupon_receive_id' => $item['coupon_receive_id'],
                'use_vin'           => '', // 用券vin
                'card_code'         => $item['card_code'],
            ];
            $net_user->unfreezeCoupon($userId, $card_data);
        }
    }


    /**
     * 取消售后和拒绝售后 赠品订单解冻
     * @param $orderInfo
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function cancelAfterGiftOrder($orderInfo)
    {
        $order_c_model = new BuOrderCommodity();
        $map           = [
            'order_code'   => $orderInfo['order_code'],
            'gift_card_id' => ['neq', ''],
            'is_gift'      => 0,
        ];
        $commodity     = $order_c_model->where($map)->find();
        if (empty($commodity)) {
            return;
        }
        // 卡券需要解冻
        $order_gift_model = new BuOrderGift();
        $net_user         = new NetUser();
        $gift_list        = $order_gift_model->alias('a')
            ->where(['a.parent_order_code' => $orderInfo['parent_order_code']])
            ->select();

        foreach ($gift_list as $key => $item) {
            $freeze_data = [
                'business_order_no' => $orderInfo['parent_order_code'],
                'coupon_code'       => $item['coupon_code'],
                'card_code'         => $item['card_code'],
            ];
            $net_user->unfreezeCoupon($orderInfo['user_id'], $freeze_data);
        }
    }


    /**
     * 赠品券订单
     * @param $orderInfo
     * @param $giftCardIdArr
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function giftOrder($orderInfo, $giftCardIdArr)
    {
        $data = [
            'is_can_after'   => 1, // 是否可以发起售后  1-可以  0-不可以
            'order_code_arr' => [],
            'msg'            => ''
        ];
        if (empty(array_filter($giftCardIdArr))) {
            return $data;
        }
        $order_gift_model = new BuOrderGift();
        $giftCardIdArr    = array_unique($giftCardIdArr);
        $map              = [
            'a.parent_order_code' => $orderInfo['parent_order_code'],
            'a.card_id'           => ['in', $giftCardIdArr],
            'a.is_enable'         => 1,
            'a.gift_order_code'   => ['neq', $orderInfo['order_code']]
        ];
        $field            = 'b.order_code,b.order_status';
        $list             = $order_gift_model->alias('a')
            ->join('bu_order b', 'a.gift_order_code=b.order_code')
            ->field($field)
            ->where($map)
            ->select();
        $order_code_arr   = [];
        if (!empty($list)) {
            foreach ($list as $key => $item) {
                if (!in_array($item['order_status'], [1, 2, 3, 8, 18])) {
                    $data['is_can_after'] = 0; // 不可以发起售后
                    $data['msg']          = '订单包含的赠品已核销不可退款';
                    return $data;
                }
                if ($item['order_status'] == 2) {
                    // 已支付的可以发起售后
                    $order_code_arr[] = $item['order_code'];
                }
            }
            $data['order_code_arr'] = $order_code_arr;
        }
        return $data;

    }


    /**
     * 查询活动备件
     * @param $activityId
     * @param $skuCodeArr
     * @param $varietyCodeArr
     * @return array|bool|\PDOStatement|string|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function getAssSku($activityId, $skuCodeArr, $varietyCodeArr)
    {
        $ass_sku_model = new DbActivityAssSku();
        $map           = ['activity_id' => $activityId, 'is_enable' => 1];
        return $ass_sku_model->where($map)
            ->where(function ($query) use ($skuCodeArr, $varietyCodeArr) {
                $query->where('sku_code', 'in', $skuCodeArr)
                    ->whereOr('sku_class_code', 'in', $varietyCodeArr);
            })->find();
    }


    /**
     * 创建赠品关联订单
     * @param $orderCode 订单编码
     * @param $cardId 卡券id
     * @param $userId  用户id
     * @param $orderVin  订单vin
     * @param $num
     * @param array $cardCodes
     * @param string $dd_dlr_code
     * @param string $v_code 备件分类编码
     * @param string $id 订单商品表id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function createGiftOrder($orderCode, $cardId, $userId, $orderVin, $num, array $cardCodes = [], $dd_dlr_code = '', $v_code = '', $id = '')
    {

        if ($num <= 0) {
            // 正常流程往下走
            return $this->re_msg('主品数量异常', 200);
        }
        $preCardCode = $cardCodes;
        // 查询卡券领取记录
        $cardNum = count($cardCodes); // 赠品券使用数量
        if ($cardNum > $num) {
            return $this->re_msg('赠品券数量异常', 401);
        }
        // 判断订单是否已经创建过
        $gift_order_model   = new BuOrderGift();
        $gift_map           = [
            'parent_order_code'  => $orderCode,
            'order_commodity_id' => $id,
            'is_enable'          => 1
        ];
        $gift_order_list    = $gift_order_model->where($gift_map)->select();
        $count              = count($gift_order_list);
        $card_receive_model = new BuCardReceiveRecord();
        $time               = date('Y-m-d H:i:s');

        // 未冻结过
        if ($count == 0) {
            $map = [
                'a.card_id'             => $cardId,
                'a.status'              => 7, // 待激活
                'a.is_enable'           => 1,
                'b.activity_time_start' => ['<=', $time],
                'b.activity_time_end'   => ['>=', $time],
            ];
            if ($cardNum == 0) {
                // 未使用赠品券
                $card_code_arr = $card_receive_model->alias('a')
                    ->join('t_db_activity b', 'a.activity_id=b.activity_id')
                    ->where($map)
                    ->where(function ($query) use ($userId, $orderVin) {
                        $query->where('receive_vin', '=', $orderVin)
                            ->whereOr(function ($query) use ($userId, $orderVin) {
                                $query->where('receive_vin', '=', '')
                                    ->where('user_id', '=', $userId);
                            });
                    })->column('card_code');
            } else {
                // 使用赠品券
                if ($num > $cardNum) {
                    // 主品数量 》 赠品券数量
                    $where              = $map;
                    $where['a.card_code'] = ['not in', $cardCodes];
                    $card_code_arr = $card_receive_model->alias('a')
                        ->join('t_db_activity b', 'a.activity_id=b.activity_id')
                        ->where($where)
                        ->where(function ($query) use ($userId, $orderVin) {
                            $query->where('a.receive_vin', '=', $orderVin)
                                ->whereOr(function ($query) use ($userId, $orderVin) {
                                    $query->where('a.receive_vin', '=', '')
                                        ->where('a.user_id', '=', $userId);
                                });
                        })->column('card_code');
                }
            }
            // 激活主品数量
            if (!empty($card_code_arr)) {
                shuffle($card_code_arr);
                $offset        = $num - $cardNum;
                $card_code_arr = array_slice($card_code_arr, 0, $offset);
                $cardCodes     = array_merge($cardCodes, $card_code_arr);
            }

        } else {
            // 再次冻结
            $cardCodes = array_column($gift_order_list, 'card_code');
        }

        if (empty($cardCodes)) {
            return $this->re_msg('赠品卡券核销码不能为空', 401);
        }

        $map  = [
            'a.card_id'             => $cardId,
            'a.is_enable'           => 1,
            'b.activity_time_start' => ['<=', $time],
            'b.activity_time_end'   => ['>=', $time],
            'a.card_code'           => ['in', $cardCodes]
        ];
        $list = $card_receive_model->alias('a')
            ->join('t_db_activity b', 'a.activity_id=b.activity_id')
            ->where($map)
            ->field('a.*')
            ->select();
        if (empty($list)) {
            return $this->re_msg('领券记录数据有误', 401);
        }
        // 判断预站券是否正常
        if (!empty($preCardCode)) {
            $select_card_code = array_column($list, 'card_code');
            //
            if (!empty(array_diff($preCardCode, $select_card_code))) {
                return $this->re_msg('预冻结卡券失败', 401);
            }
        }


        $user          = [];
        $couponCodeArr = [];
        $activityId    = 0;
        $giftOrderId   = [];
        foreach ($list as $key => $value) {
            $activityId      = $value['activity_id'];
            $add             = [
                'parent_order_code'  => $orderCode,
                'activity_id'        => $value['activity_id'],
                'card_id'            => $value['card_id'],
                'card_receive_id'    => $value['id'],
                'card_code'          => $value['card_code'],
                'coupon_code'        => $value['coupon_code'],
                'order_commodity_id' => $id,
                'dd_dlr_code'        => $dd_dlr_code,
            ];
            $couponCodeArr[] = $value['coupon_code'];
            if ($count == 0) {
                $giftOrderId[] = $gift_order_model->insertGetId($add);
            }

            $user = [
                'one_id' => $value['one_id'],
                'vin'    => $value['receive_vin'],
                'phone'  => $value['phone'],
            ];
        }
        $activityCenter = new NetActivityCenter();
        // 激活  冻结 赠品券
        $activity_model = new DbActivity();
        $field          = 'activity_id, brand,behavior_trigger_scene_list';
        $activity_info  = $activity_model->where('activity_id', $activityId)->field($field)->find();

        // 查询主品的sku_code
        $order_c_model  = new BuOrderCommodity();
        $order_c_info   = $order_c_model->getOneByPk($id);
        $varietyCodeArr = explode(',', $v_code);
        if ($order_c_info['mo_id'] > 0) {
            // 组合商品
            $order_c_map        = [
                'parent_order_code' => $order_c_info['parent_order_code'],
                'mo_sub_id'         => $order_c_info['mo_id'],
            ];
            $third_sku_code_arr = $order_c_model->where($order_c_map)->group('third_sku_code')->column('third_sku_code');
        } else {
            // 普通商品
            $third_sku_code_arr = [$order_c_info['third_sku_code']];
        }
        // 查询备件信息
        $ass_sku = $this->getAssSku($activityId, $third_sku_code_arr, $varietyCodeArr);
        if (empty($ass_sku)) {
            return $this->re_msg('活动备件信息缺失', 401);
        }
        if (in_array($ass_sku['sku_type'], [1, 2])) {
            $bindCode = $ass_sku['sku_code'];
        } else {
            $bindCode = $ass_sku['sku_class_code'];
        }


        $requestData[]  = [
            'activityId'           => $activityId,
            'behaviorTriggerScene' => 'ShangChengMaiZengDingDan_DaiZhiFu',
            'requestId'            => $orderCode . '-J' . rand(100, 999),
            'businessOrderNo'      => $orderCode,
            'brand'                => $activity_info['brand'],
            'activateNum'          => $num,  // 主品数量
            'couponCodes'          => $couponCodeArr,  // 赠品核销码
            'bindCodeType'         => $ass_sku['sku_type'],
            'bindCode'             => $bindCode,
            'dealerCode'           => $dd_dlr_code,
        ];
        $card_log_model = new DbCardLog();
        $log            = [
            'user_id'      => $userId,
            'event_type'   => 8, // 激活并冻结
            'card_id'      => $cardId,
            'request_info' => json_encode_cn($requestData),
        ];
        $logId          = $card_log_model->insertGetId($log);

        // 激活并冻结卡券
        $is_success = 0;
        try {
            $re  = $activityCenter->activateCoupon($user, $requestData);
            $msg = json_encode_cn($re);
            if (isset($re['rows']) && $re['rows'][0]['result'] == 1) {
                $is_success = 1;
                // 更新意向门店到领券记录表
                $map = ['card_id' => $cardId, 'coupon_code' => ['in', $couponCodeArr]];
                $upd = [
                    'intention_store'    => $dd_dlr_code,
                    'consume_order_code' => $orderCode,
                    'get_dlr_code'       => $dd_dlr_code,
                ];
                $card_receive_model->where($map)->update($upd);
            }
        } catch (Exception $e) {
            $msg = $e->getMessage();
        }
        $upd = [
            'is_success'    => $is_success,
            'response_info' => $msg
        ];
        $card_log_model->where('id', $logId)->update($upd);
        if ($is_success) {
            return $this->re_msg('success');
        } else {
            $upd = [
                'is_enable' => 0,
                'remarks'   => $msg
            ];
            $gift_order_model->whereIn('id', $giftOrderId)->update($upd);
            return $this->re_msg('赠品券冻结失败', 401);
        }
    }


}
