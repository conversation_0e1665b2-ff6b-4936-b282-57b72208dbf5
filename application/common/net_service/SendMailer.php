<?php


namespace app\common\net_service;


use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDlrInvoice;
use app\common\model\db\DbSpec;
use app\common\model\db\InvoiceApplyModel;
use app\common\model\db\InvoiceRecordModel;
use think\Db;
use think\Log;
use think\Queue;
use tool\PHPMailer\PHPMailer;

class SendMailer extends Common
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * type 类型 1支付成功
     * type 类型
     * 1:支付成功
     * 2:读取订单待接单状态，且状态超过24小时发一次，超过48小时未接单再发
     * 3:订单即将过期前15天、7天推送邮件
     * 4:订单提交退款申请成功推送
     * 5:用户主动取消退款申请成功后推送
     * 6:订单关联售后单状态为退款待审核，且申请时间超24小时发一次超72小时，再发
     * 7:订单触发了核销过期自动退款，成功后推送
     * 8：E3S套餐更新邮件推送
     * 10: 库存预警数据处理
     * 11: 采购预警通知
     * 12: 素材预警通知
     * 13: 成本价高于销售价
     * 14:发送发票邮件
     * 15:发票审核邮件
     * 16:延保服务包变更邮件
     * data {
     * order_code:订单号
     * }
     * @param int $type
     * @param array $data
     * @return mixed|string
     */
    public function send_mail($data)
    {
        $send_data = [];
        if ($data['type'] >= 1 && $data['type'] <= 7) {
            $send_data = $this->createData($data);
        }
        if ($data['type'] == 8) {
            $send_data = $this->E3sCreateData($data);
        }
        if ($data['type'] == 9) {
            $send_data = $this->E3sPackage($data);
        }
        if ($data['type'] == 10) {
            $send_data = $this->stockEarlyWarning($data);
            if (empty($send_data)) {
                return false;
            }
        }
        if ($data['type'] == 11) {
            $send_data = $this->priceEarlyWarning($data);
        }
        if ($data['type'] == 12) {
            $send_data = $this->materialEarlyWarning($data);
        }

        if ($data['type'] == 13) {
            $send_data = $this->costPrice($data);
        }

        if ($data['type'] == 14) {
            $send_data = $this->invoice($data);
        }

        if ($data['type'] == 15) {
            $send_data = $this->invoiceApply($data);
        }
        if ($data['type'] == 16) {
            // 延保服务包
            $send_data = $this->delayInsurance($data);
        }

        if (!empty($send_data)) {
            Queue::push('app\common\queue\Mailer', json_encode($send_data), config('queue_type.mailer'));
        } else {
            return false;
        }
        return true;
    }

    private function createData($data)
    {
        $orderObj = new BuOrder();
        $orderCommodityObj = new BuOrderCommodity();
        $dlrObj = new DbDlr();
        $cb_data = [];
        $order_index_info = $orderObj->where(['order_code' => $data['order_code']])->find();
        if ($order_index_info['promotion_source'] == 3) {
            $data_commodity_list = $orderCommodityObj->alias("a")
                ->where(['parent_order_code' => $order_index_info['parent_order_code'], 'a.mo_id' => 0])
                ->whereIn('a.dd_commodity_type', [9, 10, 1, 3, 4, 41, 6, 7, 8, 11])
                ->field('a.commodity_name')
                ->select();
            $data_list = [];
            foreach ($data_commodity_list as $data_commodity_item) {
                $data_tmp = [];
                $data_tmp['money'] = $order_index_info['money'];
                $data_tmp['integral'] = $order_index_info['integral'];
                $data_tmp['order_code'] = $order_index_info['order_code'];
                $data_tmp['commodity_name'] = $data_commodity_item['commodity_name'];
                $data_tmp['name'] = $order_index_info['name'];
                $data_tmp['phone'] = $order_index_info['phone'];
                $data_tmp['brand'] = $order_index_info['brand'];
                $data_tmp['dd_dlr_code'] = $order_index_info['dd_dlr_code'];
                $data_tmp['id'] = $order_index_info['id'];
                $data_tmp['dq_time'] = $order_index_info['dq_time'];
                $data_list[] = $data_tmp;
            }
        } else {
            $data_list = $orderObj->alias("a")
                ->join("t_bu_order_commodity b", "a.order_code=b.order_code")
                ->where(['a.order_code' => $data['order_code']])
                ->field('a.money,a.integral,a.order_code,b.commodity_name,a.name,a.phone,a.brand,a.dd_dlr_code,a.id,a.dq_time')
                ->select();
        }

        if (!empty($data_list)) {
            $order_index = $data_list[0];
            $dlr_info = $dlrObj->where(['dlr_code' => $order_index['dd_dlr_code'], 'brand_type' => $this->brandToDlrBrand($order_index['brand'])])->find();
            $content_data['emails'] = [];

            if (!empty($dlr_info['email2'])) $content_data['emails'][] = $dlr_info['email2'];
            if (!empty($dlr_info['email1'])) $content_data['emails'][] = $dlr_info['email1'];
            if (empty($content_data['emails'])) return true;

            //退款特别处理
            if ($data['type'] == 4) {
                $afsObj = new DbAfterSaleOrders();
                $afs_info = $afsObj->where(['order_id' => $order_index['id']])->find();

                $content_data['refund_time'] = $afs_info['created_date'];
                $content_data['reason'] = $afs_info['afs_reason'];
                $content_data['money'] = $order_index['money'];
                $content_data['point'] = $order_index['integral'];
            }

            if ($data['type'] == 5) {
                $afsObj = new DbAfterSaleOrders();
                $afs_info = $afsObj->where(['order_id' => $order_index['id']])->find();
                $content_data['refund_time'] = $afs_info['last_updated_date'];
            }

            $goods = '';
            foreach ($data_list as $item) {
                $goods .= $item['commodity_name'] . '、';
            }
            $brand = $order_index['brand'];
            $content_data['order_code'] = $order_index['order_code'];
            $content_data['goods'] = $goods;
            $content_data['name'] = $order_index['name'];
            $content_data['phone'] = $order_index['phone'];
            $content_data['dq_time'] = $order_index['dq_time'];

            if ($data['type'] == 2 && ($brand == 2 || $brand == 1)) {
                return false;
            }
            $cb_data = $this->contents($data['type'], $brand, $content_data);
        }
        return $cb_data;
    }

    private function E3sCreateData($data)
    {
        $content_data['emails'] = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            //            "<EMAIL>",
            //            "<EMAIL>",
            //            "<EMAIL>",
        ];
        $content_data['service_type'] = $data['service_type'];
        $content_data['state'] = '状态变更';
        if (!empty($data['data'])) {
            if (config('app_status') == "develop") {
                $cb_data['from'] = '（业测环境）E3S库有更新啦-更新编码：' . $data['update_no'];
                $cb_data['title'] = '（业测环境）E3S库有更新啦-更新编码：' . $data['update_no'];
            } else {
                $cb_data['from'] = 'E3S库有更新啦-更新编码：' . $data['update_no'];
                $cb_data['title'] = 'E3S库有更新啦-更新编码：' . $data['update_no'];
            }
            $cb_data['emails'] = $content_data['emails'];
            $content = '所属库：' . $data['service_type'] . '<br />';
            foreach ($data['data'] as $key => $value) {
                if (!empty($value['part_no'])) {
                    $content .= $value['part_no'];
                }
                if (!empty($value['dlr_price'])) {
                    $content .= $value['dlr_price'];
                }
                if (!empty($value['sale_price'])) {
                    $content .= $value['sale_price'];
                }
                if (!empty($value['rep_part_no'])) {
                    $content .= $value['rep_part_no'];
                }
                if (!empty($value['part_status'])) {
                    $content .= $value['part_status'];
                }
                if (!empty($value['dlr_order_switch'])) {
                    $content .= $value['dlr_order_switch'];
                }
                if (($key + 1) != count($data['data'])) {
                    $content .= '<hr />';
                }
            }
            if (config('app_status') == "develop") {
                $content .= "<br />业测商城后台地址：" . 'https://uat-wxstore.dongfeng-nissan.com.cn/admin_v2/e3s_update_handle/index?update_no=' . $data['update_no'];
            } else {
                $content .= "<br />商城后台地址：" . 'https://wxstore.dongfeng-nissan.com.cn/admin_v2/e3s_update_handle/index?update_no=' . $data['update_no'];
            }
            $cb_data['content'] = $content;
        }
        return $cb_data;
    }

    private function E3sPackage($data)
    {
        $content_data['emails'] = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            //            "<EMAIL>",
            //            "<EMAIL>",
            //            "<EMAIL>",
        ];
        $content_data['service_type'] = $data['service_type'];
        $content_data['state'] = '状态变更';
        if (!empty($data['data'])) {
            if (config('app_status') == "develop") {
                $cb_data['from'] = '（业测环境）E3S库套餐库更新啦-更新编码：' . $data['update_no'];
                $cb_data['title'] = '（业测环境）E3S库套餐库有更新啦-更新编码：' . $data['update_no'];
            } else {
                $cb_data['from'] = 'E3S保养套餐库有更新啦-更新编码：' . $data['update_no'];
                $cb_data['title'] = 'E3S保养套餐库有更新啦-更新编码：' . $data['update_no'];
            }
            $cb_data['emails'] = $content_data['emails'];
            $content = '所属库：' . $data['service_type'] . '<br />';
            foreach ($data['data'] as $key => $value) {
                $content .= $value['maintain_group_id'];
                $content .= $value['maintain_group_code'];
                $content .= $value['maintain_group_name'];
                $content .= $value['belong_zone_code'];
                $content .= $value['saler_amount'];
                $content .= $value['otter'];
                $content .= $value['maintain_total_count'];
                if (!empty($value['upgrade_type'])) {
                    $content .= $value['upgrade_type'];
                }
                $content .= $value['is_update'];
                $content .= $value['update_time'];
                if (($key + 1) != count($data['data'])) {
                    $content .= '<hr />';
                }
            }
            if (config('app_status') == "develop") {
                $content .= "<br />业测商城后台地址：" . 'https://uat-wxstore.dongfeng-nissan.com.cn/admin_v2/e3s_update_handle/index?update_no=' . $data['update_no'];
            } else {
                $content .= "<br />商城后台地址：" . 'https://wxstore.dongfeng-nissan.com.cn/admin_v2/e3s_update_handle/index?update_no=' . $data['update_no'];
            }
            $cb_data['content'] = $content;
        }
        return $cb_data;
    }

    private function contents($type, $brand, $data)
    {
        $cb_data = [];
        switch ($type) {
            case 1:
                $cb_data = [
                    'from' => '到店订单新订单邮件提醒',
                    'emails' => $data['emails'],
                    'title' => $this->brand($brand) . '到店订单 - 新订单提醒',
                    'content' => '商城：' . $this->brand($brand) . '<br/>
                            订单号：' . $data['order_code'] . '<br/>
                            订单内容：' . $data['goods'] . '<br/>
                            客户姓名电话：' . $data['name'] . ' | ' . $data['phone'] . '<br/>
                            订单详情：' . $this->details(1, $brand),
                ];
                break;
            case 2:
                $cb_data = [
                    'from' => '到店订单未及时接单提醒',
                    'emails' => $data['emails'],
                    'title' => $this->brand($brand) . '到店订单 - 接单提醒',
                    'content' => '商城：' . $this->brand($brand) . '<br/>
                            订单号：' . $data['order_code'] . '<br/>
                            订单内容：' . $data['goods'] . '<br/>
                            订单状态：待接单<br/>
                            客户姓名电话：' . $data['name'] . ' | ' . $data['phone'] . '<br/>
                            订单详情：' . $this->details(2, $brand),
                ];
                break;
            case 3:
                $cb_data = [
                    'from' => '到店订单核销过期提醒',
                    'emails' => $data['emails'],
                    'title' => $this->brand($brand) . '到店订单 - 即将到期提醒',
                    'content' => '商城：' . $this->brand($brand) . '<br/>
                            订单号：' . $data['order_code'] . '<br/>
                            订单内容：' . $data['goods'] . '<br/>
                            订单状态：待核销<br/>
                            核销到期时间：' . $data['dq_time'] . '<br/>
                            客户姓名电话：' . $data['name'] . ' | ' . $data['phone'] . '<br/>
                             订单详情：' . $this->details(3, $brand),
                ];
                break;
            case 4:
                $cb_data = [
                    'from' => '申请退款提醒',
                    'emails' => $data['emails'],
                    'title' => $this->brand($brand) . '到店订单 - 申请退款通知 请尽快处理',
                    'content' => '商城：' . $this->brand($brand) . '<br/>
                            订单号：' . $data['order_code'] . '<br/>
                            订单内容：' . $data['goods'] . '<br/>
                            退款申请时间：' . $data['refund_time'] . '<br/>
                            客户姓名电话：' . $data['name'] . ' | ' . $data['phone'] . '<br/>
                            退款原因：：' . $data['reason'] . '<br/>
                            退款金额：' . $data['money'] . '元 + ' . $data['point'] . '积分<br/>
                            退款审批路径：' . $this->details(4, $brand),
                ];
                break;
            case 5:
                $cb_data = [
                    'from' => '取消退款通知',
                    'emails' => $data['emails'],
                    'title' => $this->brand($brand) . '到店订单 - 取消退款通知',
                    'content' => '商城：' . $this->brand($brand) . '<br/>
                            订单号：' . $data['order_code'] . '<br/>
                            订单内容：' . $data['goods'] . '<br/>
                            取消退款申请时间：' . $data['refund_time'] . '<br/>
                            客户姓名电话：' . $data['name'] . ' | ' . $data['phone'] . '<br/>
                            订单详情：客户已取消该订单退款申请，请尽快确认订单信息并邀约客户回店。',
                ];
                break;
            case 6:
                $cb_data = [
                    'from' => '退款审核提醒',
                    'emails' => $data['emails'],
                    'title' => $this->brand($brand) . '到店订单 - 退款审核未处理提醒',
                    'content' => '商城：' . $this->brand($brand) . '<br/>
                            订单号：' . $data['order_code'] . '<br/>
                            提醒：' . $this->details(6, $brand),
                ];
                break;
            case 7:
                $cb_data = [
                    'from' => '订单核销过期自动退款通知',
                    'emails' => $data['emails'],
                    'title' => $this->brand($brand) . '到店订单 - 过期已自动退款通知',
                    'content' => '商城：' . $this->brand($brand) . '<br/>
                            订单号：' . $data['order_code'] . '<br/>
                            核销到期时间：' . $data['dq_time'] . '<br/>
                            提醒：订单未进行核销并过期，系统已自动退款成功。',
                ];
                break;
            case 8:
                $cb_data = [
                    'from' => 'E3S库有更新啦-' . date('Ymd H:i:s', time()),
                    'emails' => $data['emails'],
                    'title' => 'E3S库有更新啦-' . date('Ymd H:i:s', time()),
                    'content' => '所属库：' . $data['service_type'] . '<br/>
                            状态：' . $data['state'] . '<br/>
                            是否关联商城商品/套餐：' . $data['goods_list'] . '<br/>
                            更新时间：' . $data['created_time'] . '<br/>
                            商城后台地址：https://wxstore.dongfeng-nissan.com.cn/admin_v2/login/index',
                ];
                break;
        }
        return $cb_data;
    }

    private function brand($brand)
    {
        $data = [1 => '日产NI+商城', 2 => '启辰NI+商城', 3 => 'ARIYA商城'];
        return $data[$brand];
    }

    private function brandToDlrBrand($brand)
    {
        $data = [1 => 1, 2 => 2, 3 => 3];
        return $data[$brand];
    }

    public function details($type, $brand)
    {
        $details_info = '';
        if ($type == 1) {
            if ($brand == 3) {
                $details_info = '登录经销商营销平台-业务跟进-订单管理-商城订单 查看 https://wm.dongfeng-nissan.com.cn/dealer/app-management 请尽快接单及处理';
            }
            if ($brand == 1) {
                $details_info = '登录E3S系统- 服务管理-维修服务-电商销售管理-电商订单管控报表 查看订单详细内容 请尽快确认订单信息并邀约客户回店';
            }
            if ($brand == 2) {
                $details_info = '登录E3S系统- 服务管理-维修服务-电商销售管理-电商订单管控报表 查看订单详细内容 请尽快确认订单信息并邀约客户回店';
            }
        }
        if ($type == 2) {
            if ($brand == 3) {
                $details_info = '登录经销商营销平台-业务跟进-订单管理-商城订单-待接单 查看 https://wm.dongfeng-nissan.com.cn/dealer/app-management 请尽快接单处理';
            }
        }
        if ($type == 3) {
            if ($brand == 3) {
                $details_info = '登录E3S系统- 服务管理-维修服务-电商销售管理-电商订单管控报表 查看 请尽快联系客户邀约到店安装核销，逾期未核销系统将自动退款';
            }
            if ($brand == 1) {
                $details_info = '登录E3S系统- 服务管理-维修服务-电商销售管理-电商订单管控报表 查看 请尽快联系客户邀约到店安装核销，逾期未核销系统将自动退款';
            }
            if ($brand == 2) {
                $details_info = '登录E3S系统- 服务管理-维修服务-电商销售管理-电商订单管控报表 查看 请尽快联系客户邀约到店安装核销，逾期未核销系统将自动退款';
            }
        }

        if ($type == 4) {
            if ($brand == 3) {
                $details_info = '店端登录经销商营销平台-业务跟进-订单管理-售后审批-商城售后审批 查看 https://wm.dongfeng-nissan.com.cn/dealer/app-management 完成订单审批，即可退款成功，或联系客户商讨解决方案';
            }
            if ($brand == 1) {
                $details_info = '店端登录经销商营销平台-业务跟进-订单管理-售后审批-商城售后审批 查看 https://wm.dongfeng-nissan.com.cn/dealer/app-management 完成订单审批，即可退款成功，或联系客户商讨解决方案';
            }
            if ($brand == 2) {
                $details_info = '店端登录经销商后台-售后-NI+售后-售后审核 查看 https://wxstore.dongfeng-nissan.com.cn/admin_v2/order_static/index 完成订单审批，即可退款成功，或联系客户商讨解决方案';
            }
        }

        if ($type == 6) {
            if ($brand == 3) {
                $details_info = '已超过24小时/72小时未处理，请尽快登录经销商营销平台-业务跟进-订单管理-售后审批-商城售后审批 查看 https://wm.dongfeng-nissan.com.cn/dealer/app-management 完成订单审批，即可退款成功，或联系客户商讨解决方案';
            }
            if ($brand == 1) {
                $details_info = '已超过24小时/72小时未处理，请尽快登录经销商营销平台-业务跟进-订单管理-售后审批-商城售后审批 查看 https://wm.dongfeng-nissan.com.cn/dealer/app-management 完成订单审批，即可退款成功，或联系客户商讨解决方案';
            }
            if ($brand == 2) {
                $details_info = '已超过24小时/72小时未处理，请尽快登录经销商后台-售后-NI+售后-售后审核 查看 https://wxstore.dongfeng-nissan.com.cn/admin_v2/order_static/index完成订单审批，即可退款成功，或联系客户商讨解决方案';
            }
        }
        return $details_info;
    }


    /**
     * 库存预警数据处理
     * @param $data
     * @return array
     */
    private function stockEarlyWarning($data)
    {
        $service = new EarlyWarning();
        // 处理库存不足数据
        $service->commodityStock($data['stock']);
        // 生成库存不足附件
        $re = $service->makeStockFile($data['info_id']);
        if (!$re['code']) {
            return [];
        }
        // 测试发送邮件
        return [
            'from' => '库存预警通知',
            'emails' => $data['email'],
            'title' => 'NI+商品库存预警通知',
            'content' => date('Y年m月d日') . '商品库存不足，请查阅附件文档。',
            'file' => $re['path'],
            'file_name' => date('Y-m-d') . 'NI+商品库存预警.xls',
        ];

    }


    /**
     * 采购预警通知
     * @param $data
     * @return array
     */
    private function priceEarlyWarning($data)
    {
        return [
            'from' => '采购预警通知',
            'emails' => $data['email'],
            'title' => 'JD商品采购预警通知',
            'content' => date('Y年m月d日') . ',JD商品采购数据有更新，请查阅附件文档。',
            'file' => $data['file'],
            'file_name' => date('Y-m-d') . '采购预警通知.xls',
        ];
    }

    /**
     * 素材预警通知
     * @param $data
     * @return array
     */
    private function materialEarlyWarning($data)
    {
        return [
            'from' => '素材预警通知',
            'emails' => $data['email'],
            'title' => 'JD商品素材预警通知',
            'content' => date('Y年m月d日') . ',JD商品素材数据有更新，请查阅附件文档。',
            'file' => $data['file'],
            'file_name' => date('Y-m-d') . '素材预警通知.xls',
        ];
    }

    private function costPrice($data)
    {
        $commodity = $data['commodity'];
        $sku_list = $data['sku_list'];
        $commodity_set = $data['data'];
        $user = $data['user'];
        $shelves_sources = explode(',', $commodity['shelves_sources']);
        $sources = '';
        $class = Db::name('db_commodity_type')->alias('a')
            ->join('t_db_commodity_type b', 'a.comm_parent_id = b.id')
            ->join('t_db_commodity_type c', 'b.comm_parent_id = c.id')
            ->where(['a.id' => $commodity['comm_type_id']])
            ->field('a.comm_type_name as three_comm_name,b.comm_type_name as two_comm_name,c.comm_type_name as one_comm_name')
            ->find();
        $class_name = '';
        if (!empty($class)) {
            $class_name = $class['one_comm_name'] . '->' . $class['two_comm_name'] . '->' . $class['three_comm_name'];
        }
        foreach (DbCommodity::shelvesSources() as $key => $value) {
            if (in_array($key, $shelves_sources)) {
                $sources .= $value . ',';
            }
        }

        if (config('app_status') == "develop") {
            $content_data['emails'] = [
                "<EMAIL>",
                "<EMAIL>",
            ];
        } else {
            $content_data['emails'] = [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ];
        }
        $cb_data['from'] = '商品上架时成本价高于销售价预警';
        $cb_data['title'] = '商品上架时成本价高于销售价预警';
        $content = '以下商品上架时存在成本价高于销售价，有亏本销售的风险，请关注。<br />';
        $content .= '商品名称：' . $commodity['commodity_name'] . '<br />';
        $content .= '上架源及上架渠道：[' . $sources . '][' . $commodity_set['up_down_channel_name'] . ']<br />';
        $content .= '商品spu：' . $commodity_set['commodity_id'] . '<br />';
        $content .= '商品分类：' . $class_name . '<br />';
        $content .= '操作时间：' . date("Y-m-d H:i:s") . '<br />';
        $content .= '操作人：' . $user['username'] . '<br />';

        $table = '<table>';
        $table .= '<thead>';
        $table .= '<tr><th>sku规格</th><th>规格编码</th><th>成本价</th><th>销售价</th></tr>';
        $table .= '<thead>';
        $table .= '<tbody>';
        $dbSpecObj = new DbSpec();
        foreach ($sku_list as $value) {
            $spec_list = $dbSpecObj->alias("a")->join("t_db_spec_value b", "a.id = b.sp_id")
                ->whereIn('b.id', $value['sp_value_list'])
                ->where(['a.is_enable' => 1, 'b.is_enable' => 1])
                ->field("a.id,a.sp_name,b.id bid,b.sp_value_name")->order("a.sort,b.id")->select();
            $spec_title = '';
            foreach ($spec_list as $val) {
                $spec_title .= $val['sp_name'] . ':' . $val['sp_value_name'] . ";";
            }
            $table .= "<tr>";
            $table .= "<td>" . $spec_title . "</td>";
            $table .= "<td>" . $value['sku_code'] . "</td>";
            $table .= "<td>" . $value['cost_price'] . "</td>";
            $table .= "<td>" . $value['price'] . "</td>";
            $table .= "</tr>";
        }
        $table .= '</tbody>';
        $table .= '</table>';
        $content .= 'sku详情：' . $table;
        $cb_data['content'] = $content;
        $cb_data['emails'] = $content_data['emails'];
        return $cb_data;
    }

    private function invoice($data)
    {
        $taxRecordObj = new InvoiceRecordModel();
        $cb_data = [];
        $result = $taxRecordObj->where(['apply_id' => $data['apply_id'], 'image_url' => ['<>', ""]])->find();
        if (!empty($result)) {
            $cb_data['from'] = '发票通知';
            $cb_data['title'] = '发票通知';
            $cb_data['content_image'] = $result['image_url'] ?? "";
            $cb_data['content'] = "<img src='cid:imgcid'>";
            $cb_data['image_name'] = "invoice.jpg";
            $cb_data['emails'] = [$data['email']];
        }
        return $cb_data;
    }

    private function invoiceApply($data)
    {
        $applyId = $data['apply_id'];
        $invoiceApplyModel = new InvoiceApplyModel();
        $applyInfo = $invoiceApplyModel->alias("a")->where(['a.id' => $applyId, 'a.invoice_type' => 2])->find();
        $data = [];
        $statusArr = [
            101 => '未审核',
            104 => '审核通过',
            107 => '审核不通过'
        ];
        if (!empty($applyInfo)) {
            $relation_good_order_no = $applyInfo['relation_good_order_no'];
            $relation_good_order_no_arr = array_unique(explode(',', $relation_good_order_no));
            $data['count'] = count($relation_good_order_no_arr);
            $data['order_code'] = $relation_good_order_no_arr[0];
            $data['apply_invoice_status_name'] = $statusArr[$applyInfo['apply_invoice_status']];
            $data['apply_time'] = $applyInfo['created_date'];
            $data['tax_free_amount'] = $applyInfo['tax_free_amount'];
            $data['tax'] = $applyInfo['tax'];
            $data['tax_amount'] = $applyInfo['tax_amount'];


            $orderObj = new BuOrder();
            $orderInfo = $orderObj->alias("a")->whereIn('a.order_code', $relation_good_order_no_arr)->find();

            $dbDlrObj = new DbDlr();
            $notifyEmail = $dbDlrObj->alias("b")->join("t_db_dlr_invoice c", "b.id=c.dlr_id and c.is_enable =1")->where(['b.dlr_code' => "GWSM"])->field("c.notify_email")->find();

            $notifyEmail = json_decode($notifyEmail['notify_email'], true);
            if (empty($notifyEmail)) {
                return [];
            }
            $notifyEmailArr = $notifyEmail;
            $data['order_time'] = $orderInfo['created_date'];
            $data['buyer_name'] = $applyInfo['buyer_name'];


            $table = '以下订单申请开具数电专票（电子），请尽快进入商城后台>发票管理>专票申请列表，查看并审核申请。<br/><table style="border: 1px solid black; border-collapse: collapse;">';
            $table .= '<thead>';
            $table .= '<tr><th style="border: 1px solid black; padding: 8px;">订单编号</th><th style="border: 1px solid black; padding: 8px;">审核状态</th><th style="border: 1px solid black; padding: 8px;">下单时间</th><th style="border: 1px solid black; padding: 8px;">申请时间</th><th style="border: 1px solid black; padding: 8px;">购方名称</th><th style="border: 1px solid black; padding: 8px;">不含税金额</th><th style="border: 1px solid black; padding: 8px;">税额</th><th style="border: 1px solid black; padding: 8px;">价税合计</th></tr>';
            $table .= '<thead>';
            $table .= '<tbody>';

            $table .= "<tr>";
            $table .= "<td style='border: 1px solid black; padding: 8px;'>" . $data['order_code'] . "</td>";
            if ($data['count'] > 1) {
                $table .= "<td style='border: 1px solid black; padding: 8px;'>" . $data['order_code'] . "待" . $data['count'] . "个订单</td>";
            } else {
                $table .= "<td style='border: 1px solid black; padding: 8px;'>" . $data['apply_invoice_status_name'] . "</td>";
            }
            $table .= "<td style='border: 1px solid black; padding: 8px;'>" . $data['order_time'] . "</td>";
            $table .= "<td style='border: 1px solid black; padding: 8px;'>" . $data['apply_time'] . "</td>";
            $table .= "<td style='border: 1px solid black; padding: 8px;'>" . $data['buyer_name'] . "</td>";
            $table .= "<td style='border: 1px solid black; padding: 8px;'>" . $data['tax_free_amount'] . "</td>";
            $table .= "<td style='border: 1px solid black; padding: 8px;'>" . $data['tax'] . "</td>";
            $table .= "<td style='border: 1px solid black; padding: 8px;'>" . $data['tax_amount'] . "</td>";
            $table .= "</tr>";

            $table .= '</tbody>';
            $table .= '</table>';

            $cb_data['from'] = 'Ni+商城有新的专票申请待审核';
            $cb_data['title'] = 'Ni+商城有新的专票申请待审核';
            $cb_data['emails'] = $notifyEmailArr;
            $cb_data['content'] = $table;
            return $cb_data;
        } else {
            return [];
        }
    }


    /**
     * 延保服务包
     * @param $data
     * @return array
     */
    private  function delayInsurance($data)
    {
        $content_data['emails'] = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>", // 测试
            "<EMAIL>", // 测试
        ];
        $content_data['service_type'] = $data['service_type'];
        $content_data['state'] = '状态变更';
        $cb_data = [];
        if (!empty($data['data'])) {
            if (config('app_status') == "develop") {
                $cb_data['from'] = '（业测环境）E3S库延保服务包有更新啦-更新编码：' . $data['update_no'];
                $cb_data['title'] = '（业测环境）E3S库延保服务包有更新啦-更新编码：' . $data['update_no'];
            } else {
                $cb_data['from'] = 'E3S库延保服务包有更新啦-更新编码：' . $data['update_no'];
                $cb_data['title'] = 'E3S库延保服务包有更新啦-更新编码：' . $data['update_no'];
            }
            $cb_data['emails'] = $content_data['emails'];
            $content = '所属库：' . $data['service_type'] . '<br />';
            foreach ($data['data'] as $key => $value) {
                $content .= $value['n_product_code'];
                $content .= $value['n_product_name'];
                $content .= $value['use_price'];
                $content .= $value['is_enable'];
                $content .= $value['modified_date'];
                if (($key + 1) != count($data['data'])) {
                    $content .= '<hr />';
                }
            }
            if (config('app_status') == "develop") {
                $content .= "<br />业测商城后台地址：" . 'https://uat-wxstore.dongfeng-nissan.com.cn/admin_v2/e3s_update_handle/index?update_no=' . $data['update_no'];
            } else {
                $content .= "<br />商城后台地址：" . 'https://wxstore.dongfeng-nissan.com.cn/admin_v2/e3s_update_handle/index?update_no=' . $data['update_no'];
            }
            $cb_data['content'] = $content;
        }
        return $cb_data;
    }
}