<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/17
 * Time: 6:29 PM
 */

namespace app\common\net_service;


use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuQyPoster;
use app\common\model\db\DbActivity;
use app\common\model\db\DbActivityCard;
use app\common\model\db\DbActivityCenterLog;
use app\common\model\db\DbAfterSaleOrders;
use app\common\model\db\DbCard;
use app\common\model\db\DbCardLog;
use app\common\model\db\DbDlr;
use app\common\model\db\DbJobsLog;
use app\common\model\db\DbSendCardPageSub;
use app\common\model\db\DbSystemValue;
use app\common\model\db\DbUser;
use app\common\model\db\DbUserCarSeries;
use app\common\model\e3s\E3sCarSeries;
use app\common\model\wlz\WlzCrowdsLogs;
use app\common\port\connectors\Crm;
use app\common\port\connectors\LyPayment;
use app\common\port\connectors\Member;
use app\common\port\connectors\QuickWin;
use app\common\port\connectors\ActivityCenter;
use app\common\port\connectors\Third;
use think\Cache;
use think\Controller;
use think\Exception;
use think\Hook;
use think\Model;
use tool\Logger;

class NetUser extends Common
{


    public function __construct()
    {
        parent::__construct();
    }

    private $pz_dlr_code_str = "'PZ1ASM','PZ1AAPP'";


    /**
     * @param $card_ids
     * @param $act_code
     * @param $source 99==ordersave 订单
     * @return array
     * 领取卡券 需要做修改增加
     */
    public function get_card($card_ids, $act_code, $source, $user, $channel_type, $poster_id = 0, $creator = '', $get_dlr_code = '', $hit_type_code = '', $coupon_date = [], $requestData = [],$rightnow = 0)
    {
        Logger::error('wangetcardin:', ['card' => $card_ids, 'userid' => $user['id']]);
        $this->user         = $user;
        $this->channel_type = $channel_type;
        $this->user_id      = $user['id'];
        $this->unionid      = $this->user['bind_unionid'];
        if ($source == 99) {
            // 订单领取，单个卡券购买多张
            $card_arr = explode(',', $card_ids);
        } else {
            $card_arr = array_unique(explode(',', $card_ids));
        }
        if (in_array($act_code, ['402112151194'])) {
//            Logger::error('');
            return $this->re_msg("请选择正确的卡券", 415);
        }

        if ($card_arr) {

//            if($source<>99 && $this->channel_type != 'COUPON'){
//                $card_arr = $this->canGetCards($this->user, $card_arr,$this->channel_type);
//            }
//
//            if (empty($card_arr)){
//                return $this->re_msg('不符合领取条件', 415);
//            }
            if ($poster_id > 0) {
                $qy_poster_model     = new BuQyPoster();
                $card_page_sub_model = new DbSendCardPageSub();
                $poster              = $qy_poster_model->getOne(['where' => ['id' => $poster_id]]);
                $card_page_sub       = $card_page_sub_model->getOne(['where' => ['send_card_page_id' => $poster['send_card_page_id'], 'module' => 3]]);
                if (empty($poster) || empty($card_page_sub)) {
                    return $this->re_msg("发券页不存在", 415);
                }
            }
            $DbJobsLog    = new DbJobsLog();
            $net_user     = new NetUser();
            $card_model   = new DbCard();
            $card_r_model = new BuCardReceiveRecord();
            $order_model  = new BuOrder();
            $error        = 0;
            $order                   = $order_model->getOne(['where' => ['id' => $act_code]]);
            $channel                 = $order['channel'] ?? '';
            $pick_delivery_card_code = $order['pick_delivery_card_code'] ?? '';
            $pick_up_order_code      = $order['pick_up_order_code'] ?? '';
//            $order_vin               = $order['order_vin'] ?? "";
//            $user_id = $order['user_id'] ?? "";

            $pickDeliveryCard = BuCardReceiveRecord::where('card_code', $pick_delivery_card_code)->find();
            if (!empty($pickDeliveryCard['id']) && !empty($pick_delivery_card_code)) {
                // 修改取送车卡券状态
                $setData = [
                    'use_type'  => 1, //使用类型：1-冻结；2-解冻
                    'vin'       => $pickDeliveryCard['vin'],
                    'card_code' => $pickDeliveryCard['card_code'], // 领券后的卡券号码
                ];
                $res2    = $net_user->setCardUse($this->user, $this->channel_type, $setData);
                if ($res2['code'] <> 200) {
                    $DbJobsLog->insertData([
                        'queue'       => 'card_receive',
                        'source_type' => 'card_receive_get_card',
                        'data_info'   => json_encode($setData),
                        'result_info' => json_encode($res2),
                    ]);
                }
            }
            foreach ($card_arr as $card_id) {
                $where = ['id' => $card_id, 'available_count' => ['>', 0], 'is_enable' => 1];
                $card  = $card_model->getOne(array('where' => $where));
                // get_limit 领取数量
                if ($card) {
                    if ($card['type'] == 1) {
                        return $this->re_msg("错误的卡券类型", 415);
                    }
                    $car = $this->_getCarer('', $this->user['plat_id'], $this->user['one_id'], $this->channel_type);//不用unionid了

                    $status = BuCardReceiveRecord::STATUS_KEY1;

                    if ($card['card_type'] == 7) {
                        $vin          = isset($order['order_vin']) ? $order['order_vin'] : '';
                        $get_dlr_code = '';
                        if (!empty($pick_up_order_code) && in_array($channel, ['3', '5', '7'])) {
                            $status = BuCardReceiveRecord::STATUS_KEY5;
                        }
                    } else {
                        $vin = isset($car['vin']) ? $car['vin'] : '';
                    }
                    if (isset($coupon_date['validity_date_start']) && $coupon_date['validity_date_start']) {
                        $date_start = $coupon_date['validity_date_start'];
                        $date_end   = $coupon_date['validity_date_end'];
                    } else {
                        if ($card['date_type'] == 1) {
                            $date_start = $card['validity_date_start'];
                            $date_end   = $card['validity_date_end'] . ' 23:59:59';
                        } else {
                            //固定时长专用，领取后多少天内有效，单位为天(有效天数)
                            //  `fixed_begin_term` '固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天',
                            $date_start = date('Y-m-d H:i:s', strtotime(sprintf(" +%s day", $card['fixed_begin_term'])));
                            $date_end   = date('Y-m-d H:i:s', strtotime(sprintf(" +%s day", $card['fixed_term'])));
                        }
                    }

                    $data               = array(
                        'user_id'                 => $this->user_id,
                        'vin'                     => $vin,
                        'license_plate'           => isset($car['car_no']) ? $car['car_no'] : '',
                        'name'                    => isset($car['name']) ? $car['name'] : '',
                        'source'                  => $source,
                        'act_id'                  => $act_code,
                        'phone'                   => isset($car['mobile']) ? $car['mobile'] : '',
                        'card_id'                 => $card_id,
                        'dlr_code'                => $this->channel_type,
                        'openid'                  => $this->unionid,
                        'poster_id'               => $poster_id,
                        'creator'                 => $creator,
                        'get_dlr_code'            => $get_dlr_code,
                        'car_18n'                 => isset($user['car_18n']) ? $user['car_18n'] : '',
                        'created_date'            => date('Y-m-d H:i:s'),
                        'coupon_code'             => $coupon_date['coupon_code'] ?? '',
                        'intention_dlr_id'        => $coupon_date['intention_dlr_id'] ?? 0,
                        'validity_date_start'     => $date_start,
                        'validity_date_end'       => $date_end,
                        'consume_order_code'      => $pick_up_order_code,
                        'coupon_center_flag'      => $requestData['coupon_center_flag'] ?? '',
                        'intention_brand_id'      => $requestData['intention_brand_id'] ?? '',
                        'intention_car_series_id' => $requestData['intention_car_series_id'] ?? '',
                        'intention_car_type_id'   => $requestData['intention_car_type_id'] ?? '',
                        'intention_store'         => $requestData['intention_store'] ?? '',
                        'reserve_date'            => $requestData['reserve_date'] ?? '',
                        'reserve_id'              => $requestData['reserve_id'] ?? '',
                        'activity_id'             => $card['activity_id'],

                    );
                    $dbActivityObj      = new DbActivity();
                    if($card['activity_id']>0){
                        $dbActivityCardInfo = $dbActivityObj->where(['activity_id' => $card['activity_id'], 'is_enable' => 1])->find();
                        // $dbActivityCardInfo = $card_model->alias("a")->join("t_db_activity b","a.activity_id=b.activity_id")->where(['a.card_id'=>$card_id,'b.is_enable'=>1,'a.is_enable'=>1])->field("b.select_obj")->find();
//                    print_json($dbActivityCardInfo,$dbActivityObj->getLastSql());
                        if ($dbActivityCardInfo['select_obj'] == 2 || $dbActivityCardInfo['select_obj'] == 3) {
                            $data['receive_vin'] = $user['vin'];
                        }
                    }

                    $card_code             = $this->_getOrderNo($this->channel_type . 'c' . date('YmdHis'), 3);
                    $data['is_get_card']   = 1;
                    $data['card_code']     = $card_code;
                    $data['status']        = $status;
                    $data['hit_type_code'] = $hit_type_code;
                    //通过订单来领券写入订单表卡券号，--sy
                    if ($source == 99) {
                        $order_goods_model = new BuOrderCommodity();
                        $order_model       = new BuOrder();
                        $order             = $order_model->getOneByPk($act_code);
                        if (!empty($order)) {
                            // 到店类卡券及取送车券类订单
                            $order_goods_model->saveData([
                                'goods_card_codes' => ['exp', "TRIM(BOTH ',' FROM concat_ws(',', goods_card_codes, '{$card_code}'))"]
                            ], ['order_code' => $order['order_code'], 'goods_card_ids' => $card_id]);
                        }
                    }
                    $data_all = [];
                    // 发券页过来
                    if (isset($poster)) {
                        // 海报设置信息
                        $poster_card  = json_decode($poster['card_list'], true);
                        $poster_where = ['card_id' => $card_id, 'act_id' => $act_code, 'poster_id' => $poster_id];
                        $poster_count = $card_r_model->where($poster_where)->count();// 海报对应优惠券已领取总数

                        // 发券页设置信息
                        $page_card  = json_decode($card_page_sub['card_list'], true);
                        $_where     = ['card_id' => $card_id, 'user_id' => $this->user_id, 'act_id' => $act_code, 'poster_id' => $poster_id];
                        $card_count = $card_r_model->where($_where)->count();// 发券页对应优惠券用户已领取总数

                        if ($page_card[$card_id] > $card_count) { // 判断用户是否已全部领取
                            $for     = $page_card[$card_id] - $card_count; // 当前可领取数量
                            $surplus = $poster_card[$card_id] - $poster_count; // 海报优惠券剩余库存
                            if ($surplus < $for) {// 判断海报设置的库存是否大于当前可领取数量
                                $for = $surplus;
                            }
                            if ($card['available_count'] < $for) {// 判断优惠券库存是否大于当前可领取数量
                                $for = $card['available_count'];
                            }
                        } else {
                            return $this->re_msg($card_id . '卡券已领取!', 415);
                        }
                        for ($i = 0; $i < $for; $i++) {
                            $data['get_dlr_code'] = $poster['dlr_code'];
                            $data_all[]           = $data;
                        }
                        $get_num = $for; // 领取数量
                    } else {
                        //普通卡券 领取卡券根据卡券get_limit 限购数量
//                        $_where      = ['card_id' => $card_id, 'user_id' => $this->user_id, 'act_id' => $act_code];
//                        $card_record = $card_r_model->where($_where)->find();
//                        $_where      = ['card_id' => $card_id, 'user_id' => $this->user_id];
//                        $card_record = $card_r_model->where($_where)->count();
//                        if ($card_record) {
//                            if($card['get_limit'] && $source <> 88){
//                                if($card['get_limit']<=$card_record){
//                                    $error = 3;//卡券已领取
//                                    return $this->re_msg('有卡券超出领取限制!', 415);
//                                }
//                            }
//                        }
                        $get_num    = 1; // 领取数量
                        $data_all[] = $data;
                    }

                    $res = $card_model->where($where)->setDec('available_count', $get_num);

                    if ($res) {
                        //todo调用活动领券接口
                        //mallActivityReceiveCoupon
                      //  dd($card['activity_id']);
                        if ($card['activity_id'] > 0) {
                            $netActivityCenterObj = new NetActivityCenter();

                            foreach ($data_all as $data_item) {
                                $receiveData = [
                                    "activityId" => $card['activity_id'],
                                    "couponId"   => $card['quick_win_card_id'],
                                    "dlr_code"   => $data_item['get_dlr_code'] ?? '',
                                    "channel_type"   => $this->channel_type
                                ];
                                $userObj = new DbUser();
                                $myuserInfo = $userObj->where(['id'=>$this->user['id'],'is_enable'=>1])->find();
                                $this->user['one_id'] = $myuserInfo['one_id'];

                                $activityCenterRet = $netActivityCenterObj->mallActivityReceiveCoupon($this->user, $receiveData);

                                $row = 0;
                                if ($activityCenterRet['result'] == 1) {
                                    // 需要判断 coupon_code
                                    $coupon_code = $activityCenterRet['rows'];
                                    $data_item['coupon_code'] = $coupon_code;
                                    $map = [
                                        'coupon_code' => $coupon_code,
                                        'is_enable' => 1,
                                    ];
                                    $info = $card_r_model->where($map)->find();
                                    if (!empty($info)) {
                                        $row = $info;
                                        $card_r_model->where('id', $info['id'])->update($data_item);
                                    } else {
                                        $row = $card_r_model->insertData($data_item);
                                    }
                                }
                            }
                        }
                        elseif(in_array($source,[88,99]) || $rightnow == 1 ) {
                            $card_codes = array_column($data_all, 'card_code');
                            //电子卡券，到店代金没有活动ID,走这里
                            usleep(100000);//暂停0.1秒
                            $dbuserObj = new DbUser();
                            $userInfo = $dbuserObj->where(['id'=>$order['user_id'],'is_enable'=>1])->find();
                            $brand = DbDlr::channel_to_brand($this->channel_type);

                            $data_tmp = [
                                "coupon_id"          => $card['quick_win_card_id'],
                                "intention_brand_id" => $brand == 3 ? 9 : $brand, // pz品牌id在卡券中心是9
                                "one_id"             =>$userInfo['one_id'],
                                "receive_channel"    => 1,
                                "receive_source"     => 'nissanminiapp',
                                "reserve_status"     => 0,
                                "user_name"          => $order['name'],
                                "user_phone"         => $order['phone'],
                                "vin"                => $order['order_vin'],
                                'intention_store'    => $get_dlr_code,
                            ];

                            $redis_dlr_key = 'e3s_dlr_id_' . $get_dlr_code;
                            $redis_dlr_id  = redis($redis_dlr_key);
                            if (!empty($redis_dlr_id)) {
                                $data_tmp['intention_store_id'] = $redis_dlr_id;
                            } else {
                                $dlr_list = QuickWin::create('e3s_dlr')->postDlr(['dlrCode' => $get_dlr_code, 'sortType' => '2']);

                                $data_tmp['intention_store_id'] = $dlr_list['rows'][0]['dlrId'] ?? 0;
                                redis($redis_dlr_key, $data_tmp['intention_store_id'], 3600);
                            }


                            $activityObj = new DbActivityCenterLog();
                            $log_data['activity_id'] = 'postCouponReceive';
                            $log_data['request_id'] = $card['quick_win_card_id'];
                            $log_data['request_info'] = json_encode_cn($data_tmp);
                            $log_data['oneid'] = $order['user_id'];
                            $logid = $activityObj->insertGetId($log_data);

                            $res      = QuickWin::create('quick_win')->postCouponReceive($data_tmp);
                            $activityObj->where(['id'=>$logid])->update(['response_info'=>json_encode_cn($res)]);
                            $data_all[0]['coupon_code'] = $res['rows'];
                            if(!empty($res['rows'])){
                                $row        = $card_r_model->insertAll($data_all);
                            } else {
                                return $this->re_msg('卡券发放失败!~~', 415);
                            }
                           // $this->couponReceive($card_codes, $user); // 实时推送给卡券中心 todo gaishishi
//                            $row=1;
                        }else{
                            return $this->re_msg('领取失败!~~', 415);
                        }

                        if (!$row) {
                            $card_model->where($where)->setInc('available_count', $get_num);//加回去
                            $error = 1;//领取失败
                            return $this->re_msg('领取失败!', 415);

                        }
                    } else {
                        $error = 2;//扣库存失败
                        return $this->re_msg('已经被抢光啦～', 415);
                    }

                } else {
                    $error = 4;//卡券库存不足
                    return $this->re_msg('卡券已抢光!', 415);
                }
            }

            return $this->re_msg('ok');

        }
    }

    /**
     * @param $user
     * @param $channel_type
     * @param $requestData
     * 卡券中心
     */
    public function car_center($user, $channel_type, $requestData)
    {
        $this->user_id      = $user['id'];
        $this->channel_type = $channel_type;
        $card_r_model       = new BuCardReceiveRecord();
        $where              = ['a.user_id' => $this->user_id, 'a.status' => ["<>", 2], 'a.is_enable' => 1];

        if (isset($requestData['search'])) {
            $where['b.card_name'] = ['like', "%" . $requestData['search'] . "%"];
        }
        if (!empty($requestData['card_ids']) && is_string($requestData['card_ids'])) {
            $where['b.id'] = ['in', explode(',', $requestData['card_ids'])];
        }
        $where[] = ['exp', sprintf("FIND_IN_SET('%s',b.up_down_channel_dlr)", $this->channel_type)];
        $where[] = ['exp', sprintf("b.card_type<>%s", 7)];
//        $where[]=['exp', sprintf("b.card_type<>%s or a.dlr_code  in (%s)", 7,$this->pz_dlr_code_str)];
        $field     = "a.id cr_id,a.vin,b.*,a.created_date c_date,a.status,a.user_id,a.card_code,a.consume_date,a.get_dlr_code,c.dlr_name,a.act_id act_code,a.coupon_code,a.validity_date_start fixed_begin_date,a.validity_date_end fixed_end_date";
        $cards     = $card_r_model->getCardShopCardFlat(['where' => $where, "field" => $field, 'group' => "a.id", 'order' => 'a.id desc']);
        $card_arr1 = array();//可用
        $card_arr2 = array();//已使用
        $card_arr3 = array();//已过期

        foreach ($cards as $k => $v) {
            //                $fixed_end_date   = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $v['c_date'], $v['fixed_term'])));
//                $fixed_begin_date = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $v['c_date'], $v['fixed_begin_term'])));
//            if ($v['date_type'] == 2) {
//                $cards[$k]['fixed_end_date']   = $fixed_end_date;
//                $cards[$k]['fixed_begin_date'] = $fixed_begin_date;
//            } else {
//                $cards[$k]['fixed_end_date']   = $v['validity_date_end'];
//                $cards[$k]['fixed_begin_date'] = $v['validity_date_start'];
//            }
            $cards[$k]['id']  = (string)$v['id'];
            $fixed_end_date   = $v['fixed_end_date'];
            $fixed_begin_date = $v['fixed_begin_date'];
            if (empty($fixed_end_date)) {
                $cards[$k]['fixed_end_date'] = $v['validity_date_end'];
            }
            if (empty($fixed_begin_date)) {
                $cards[$k]['fixed_begin_date'] = $v['validity_date_start'];
            }
            if ($v['card_type'] == 2) {
                $cards[$k]['qu'] = $v['card_discount'] . '<small>折</small>';
            } else {
                $cards[$k]['qu'] = "<small>¥</small>" . $v['card_quota'];
            }
            if ($v['least_cost'] > 0) {
                $cards[$k]['word'] = sprintf("满%s使用", $v['least_cost']);
            } else {
                $cards[$k]['word'] = sprintf("");
            }
            if ($v['status'] == 3) {
                $card_arr2[$k] = $cards[$k];
            } elseif ($v['status'] == 1) {

//                if (($v['validity_date_end'] < date('Y-m-d') && !empty($v['validity_date_end'])) || ($v['validity_date_start'] > date('Y-m-d') && !empty($v['validity_date_start'])) || ($v['date_type'] == 2 && (date('Y-m-d H:i:s') < $fixed_begin_date || date('Y-m-d H:i:s') > $fixed_end_date))) {
                if (date('Y-m-d H:i:s') < $cards[$k]['fixed_begin_date'] || date('Y-m-d H:i:s') > $cards[$k]['fixed_end_date']) {
                    $card_arr3[$k] = $cards[$k];
                } else {
                    $card_arr1[$k] = $cards[$k];
                }
            }
        }
        return $this->re_msg(['card_1' => array_values($card_arr1), 'card_2' => array_values($card_arr2), 'card_3' => array_values($card_arr3)]);
    }

    /**
     * @param $user
     * @param $channel_type
     * @param $requestData
     * 卡券中心-分页版
     */
    public function card_center_page($user, $channel_type, $requestData)
    {
        $this->user_id      = $user['id'];
        $this->channel_type = $channel_type;
        $card_r_model       = new BuCardReceiveRecord();
        $where              = ['a.user_id' => $this->user_id, 'a.status' => ["<>", 2], 'a.is_enable' => 1];
        if (isset($requestData['search'])) {
            $where['b.card_name'] = ['like', "%" . $requestData['search'] . "%"];
        }

        if (!empty($requestData['created_date'])) {
            $where['a.created_date'] = ['<', $requestData['created_date']];
        }

        if (!empty($requestData['status'])) {
            if ($requestData['status'] == 1) {
                //可使用
                //act_status 2
                //status 1
                $where['b.act_status'] = 2;
                $where['a.status']     = 1;
            } else if ($requestData['status'] == 2) {
                //已使用
                //status 3
                $where['a.status'] = 3;
            } else {
                //已过期 未生效
                //act_status 1和3
                $where[]           = ['exp', sprintf("b.act_status in (1,3)")];
                $where['a.status'] = 1;
            }
        }

        $where[] = ['exp', sprintf("FIND_IN_SET('%s',b.up_down_channel_dlr)", $this->channel_type)];
        $where[] = ['exp', sprintf("b.card_type<>%s", 7)];
//        $where[]=['exp', sprintf("b.card_type<>%s or a.dlr_code  in (%s)", 7,$this->pz_dlr_code_str)];

        $field = "a.id cr_id,a.vin,b.*,a.created_date c_date,a.status,a.user_id,a.card_code,a.consume_date,a.get_dlr_code,c.dlr_name,a.coupon_code,a.act_id act_code,a.validity_date_start fixed_begin_date,a.validity_date_end fixed_end_date";

        $cards = $card_r_model->getCardShopCardFlatPage([
            'where'    => $where,
            "field"    => $field,
            'group'    => "a.id",
            'order'    => 'a.id desc',
            'pagesize' => $requestData['pageSize'] ?? 10,
        ]);

        if (!empty($cards)) {
            foreach ($cards as $k => $v) {
//                $fixed_end_date   = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $v['c_date'], $v['fixed_term'])));
//                $fixed_begin_date = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $v['c_date'], $v['fixed_begin_term'])));
//                if ($v['date_type'] == 2) {
//                    $cards[$k]['fixed_end_date']   = $fixed_end_date;
//                    $cards[$k]['fixed_begin_date'] = $fixed_begin_date;
//                } else {
//                    $cards[$k]['fixed_end_date']   = $v['validity_date_end'];
//                    $cards[$k]['fixed_begin_date'] = $v['validity_date_start'];
//                }
                $fixed_end_date   = $v['fixed_end_date'];
                $fixed_begin_date = $v['fixed_begin_date'];
                if (empty($fixed_end_date)) {
                    $cards[$k]['fixed_end_date'] = $v['validity_date_end'];
                }
                if (empty($fixed_begin_date)) {
                    $cards[$k]['fixed_begin_date'] = $v['validity_date_start'];
                }
                if ($v['card_type'] == 2) {
                    $cards[$k]['qu'] = $v['card_discount'] . '<small>折</small>';
                } else {
                    $cards[$k]['qu'] = "<small>¥</small>" . $v['card_quota'];
                }
                if ($v['least_cost'] > 0) {
                    $cards[$k]['word'] = sprintf("满%s使用", $v['least_cost']);
                } else {
                    $cards[$k]['word'] = sprintf("");
                }
                if ($v['status'] == 3) {
                    $cards[$k]['status']     = 2;
                    $cards[$k]['status_des'] = '已使用';
                } elseif ($v['status'] == 1) {
//                    if (($v['validity_date_end'] < date('Y-m-d') && !empty($v['validity_date_end'])) || ($v['validity_date_start'] > date('Y-m-d') && !empty($v['validity_date_start'])) || ($v['date_type'] == 2 && (date('Y-m-d H:i:s') < $fixed_begin_date || date('Y-m-d H:i:s') > $fixed_end_date))) {
                    if (date('Y-m-d H:i:s') < $cards[$k]['fixed_begin_date'] || date('Y-m-d H:i:s') > $cards[$k]['fixed_end_date']) {
                        $cards[$k]['status']     = 3;
                        $cards[$k]['status_des'] = '已过期';
                    } else {
                        $cards[$k]['status']     = 1;
                        $cards[$k]['status_des'] = '可使用';
                    }
                }
            }
        }


        return $this->re_msg($cards);
    }

    public function user_address($user, $is_default = 0)
    {
        $address_list = Member::create('member')->address($user['plat_id']);
        Logger::error('get_user_address:', ['plat' => $user['plat_id'], 'r' => $address_list]);
        $list = [];
        if ($address_list) {
            $order = array_column($address_list, 'modified_date');
            array_multisort($order, SORT_DESC, $address_list);
            $d_list = [];
            foreach ($address_list as $k => $v) {
                $k                           = $k + 1;
                $list[$k]['id']              = $v['id'];
                $list[$k]['member_id']       = $v['member_id'];
                $list[$k]['name']            = $v['nickname'] ?? '';
                $list[$k]['city']            = $v['province_name'] . $v['city_name'] . $v['district_name'];
                $list[$k]['receipt_address'] = $v['address'];
                $list[$k]['phone']           = $v['phone'];
                $list[$k]['province_id']     = $v['province_id'] ?? '';
                $list[$k]['city_id']         = $v['city_id'] ?? '';
                $list[$k]['county_id']       = $v['district_id'] ?? '';
                $list[$k]['city_arr']        = [$v['province_name'], $v['city_name'], $v['district_name']];
                if ($v['is_default'] == 1) {
                    $list[$k]['checked'] = 'checked';
                    if ($is_default == 1) {
                        $d_list = $list[$k];
                        continue;
                    }
                    $list[0] = $list[$k];
                    unset($list[$k]);
                }
            }
            if ($is_default == 1) {
                $list = $d_list;
            }

            if (empty($is_default)) {
                ksort($list);
                $list = array_values($list);
            }
        }


        return $this->re_msg($list);
//        return $this->setResponseData($list)->send();
    }

    /**
     * @param $user
     * @param $channel_type
     * @param $requestData
     * @return array
     */
    public function getCardAvailable($user, $channel_type, $requestData = [])
    {
        $this->user_id      = $user['id'];
        $this->channel_type = $channel_type;
        //vin码，由于商城默认的vin与APP的可能不相同，所以必须传
        $vin = $requestData['vin'] ?? 'xx';
        //卡劵类型，默认取送车
        $card_type = $requestData['card_type'] ?? DbCard::CARD_TYPE_KEY7;
        //卡劵类型，默认取送车
        $num = $requestData['num'] ?? 1;

        $card_r_model = new BuCardReceiveRecord();
        $where        = [
            'a.user_id'           => $this->user_id,
            'a.vin'               => $vin,
            'b.card_type'         => $card_type,
            'a.status'            => 1,
            'a.validity_date_end' => ['>=', date('Y-m-d H:i:s')]
        ];

        $where[] = ['exp', sprintf("FIND_IN_SET('%s',b.up_down_channel_dlr)", $this->channel_type)];
        $field   = "a.id cr_id,a.act_id,d.id order_id,d.order_code,b.*,a.created_date c_date,a.status,a.user_id,a.card_code,a.consume_date,a.get_dlr_code,c.dlr_name,a.act_id act_code,a.coupon_code,a.validity_date_start fixed_begin_date,a.validity_date_end fixed_end_date";
        $cards   = $card_r_model->getCardShopCardFlat([
            'where' => $where,
            "field" => $field,
            'group' => "a.id",
            'order' => 'a.id asc'
        ]);

        $card_arr1 = [];//可用
        $card_arr3 = [];//已过期
        foreach ($cards as $k => $v) {
            if (empty($v['order_id'])) {
                continue;
            }

//            $fixed_end_date   = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $v['c_date'], $v['fixed_term'])));
//            $fixed_begin_date = date('Y-m-d H:i:s', strtotime(sprintf("%s +%s day", $v['c_date'], $v['fixed_begin_term'])));
//            if ($v['date_type'] == 2) {
//                $cards[$k]['fixed_end_date']   = $fixed_end_date;
//                $cards[$k]['fixed_begin_date'] = $fixed_begin_date;
//            } else {
//                $cards[$k]['fixed_end_date']   = $v['validity_date_end'];
//                $cards[$k]['fixed_begin_date'] = $v['validity_date_start'];
//            }
            $fixed_end_date   = $v['fixed_end_date'];
            $fixed_begin_date = $v['fixed_begin_date'];
            if (empty($fixed_end_date)) {
                $cards[$k]['fixed_end_date'] = $v['validity_date_end'];
            }
            if (empty($fixed_begin_date)) {
                $cards[$k]['fixed_begin_date'] = $v['validity_date_start'];
            }
            if ($v['card_type'] == 2) {
                $cards[$k]['qu'] = $v['card_discount'] . '<small>折</small>';
            } else {
                $cards[$k]['qu'] = "<small>¥</small>" . $v['card_quota'];
            }
            if ($v['least_cost'] > 0) {
                $cards[$k]['word'] = sprintf("满%s使用", $v['least_cost']);
            } else {
                $cards[$k]['word'] = sprintf("限购部分商品");
            }
//            if (
//                ($v['validity_date_end'] < date('Y-m-d') && !empty($v['validity_date_end']))
//                || ($v['validity_date_start'] > date('Y-m-d') && !empty($v['validity_date_start']))
//                || ($v['date_type'] == 2 && (date('Y-m-d H:i:s') < $fixed_begin_date || date('Y-m-d H:i:s') > $fixed_end_date))
//            ) {
            if (date('Y-m-d H:i:s') < $cards[$k]['fixed_begin_date'] || date('Y-m-d H:i:s') > $cards[$k]['fixed_end_date']) {
                $card_arr3[$k] = $cards[$k];
            } else {
                $card_arr1[$k] = $cards[$k];
            }
            if ($num == count($card_arr1)) {
                break;
            }
        }

        sort($card_arr1);

        $data = ($num == 1) ? ($card_arr1[0] ?? []) : $card_arr1;
        return $this->re_msg([
            'card_data' => $data
        ]);
    }


    /**
     * @param $user
     * @param $channel_type
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function setCardUse($user, $channel_type, $requestData = [])
    {
        $this->user_id      = $user['id'];
        $this->channel_type = $channel_type;
        //使用类型：1-冻结；2-解冻；
        $useType = $requestData['use_type'] ?? '';
        $vin     = $requestData['vin'] ?? 'xx';
        //领券后的卡券号码
        $card_code          = $requestData['card_code'] ?? '';
        $act_id             = $requestData['act_id'] ?? '';
        $pick_up_order_code = $requestData['pick_up_order_code'] ?? '';
        Logger::error('usersetCardUsein', json_encode(['rq' => $requestData, 'user' => $this->user]));

        $card_list = [];
        if (!empty($act_id)) {
            $where     = [
                'user_id' => $this->user_id,
                'vin'     => $vin,
                'act_id'  => $act_id,
            ];
            $card_list = BuCardReceiveRecord::all($where);
        }
        if (!empty($card_code)) {
            $where     = [
                'user_id'   => $this->user_id,
                'vin'       => $vin,
                'card_code' => $card_code,
            ];
            $card_info = BuCardReceiveRecord::get($where);
            array_push($card_list, $card_info);
        }

        if (empty($card_list)) {
            return $this->re_msg('卡券不存在', -1);
        }

        $msg = ($useType == 1) ? '冻结' : '解冻';
        foreach ($card_list as $cards) {

            $order = redis('act_id_order' . $cards['act_id']);
            if (!$order) {
                $order = BuOrder::get(['id' => $cards['act_id']]);
                redis('act_id_order' . $cards['act_id'], $order, 10);
            }
            if (!empty($pick_up_order_code) && !empty($cards['act_id'])) {
                $BuOrder = new BuOrder();
                $BuOrder->saveData(['pick_up_order_code' => $pick_up_order_code], ['id' => $order['id']]);
            }

            if (empty($cards['coupon_code'])) {
                return $this->re_msg('卡券领取记录有误，请联系客服', -1);
            }

            $coupon_receive = QuickWin::create('quick_win')->getCouponReceiveRecord(
                [
                    'coupon_code'     => $cards['coupon_code'],
                    'request_channel' => 1
                ]
            );

            $quickWinData = [
                "business_order_no" => $order['order_code'],
                "coupon_code"       => $cards['coupon_code'],
                "coupon_receive_id" => $coupon_receive['rows'][0]['id'] ?? 0
            ];
            if ($useType == 1) {
                $event_type = 5; // 冻结

                $ret = QuickWin::create('quick_win')->postCouponFreezeCoupon($quickWinData);
            } else {
                $event_type = 6; // 解冻
                $ret        = QuickWin::create('quick_win')->postCouponUnfreezeCoupon($quickWinData);
            }

            $is_success = $ret['result'] == '1' ? 1 : 0;
            DbCardLog::create(
                [
                    'user_id'       => $this->user_id,
                    'event_type'    => $event_type,
                    'is_success'    => $is_success,
                    'card_id'       => $cards->card_id,
                    'request_info'  => json_encode($quickWinData),
                    'response_info' => json_encode($ret),
                ]
            );

            if ($ret['result'] != 1) {
                return $this->re_msg($ret['msg'], -1);
            }

            //领取卡券状态：1-领取;；2-未领取；3-核销；4-已删除；5-冻结
            $status   = ($useType == 1) ? BuCardReceiveRecord::STATUS_KEY5 : BuCardReceiveRecord::STATUS_KEY1;
            $saveData = BuCardReceiveRecord::find($cards->id)->save(
                [
                    'status'             => $status,
                    'consume_date'       => date('Y-m-d H:i:s'),
                    'consume_dlr_code'   => $order['dd_dlr_code'] ? $order['dd_dlr_code'] : $order['dlr_code'],
                    'consume_order_code' => $pick_up_order_code,
                ]
            );

            if (!$saveData) {
                return $this->re_msg('卡券' . $msg . '失败', -1);
            }

        }
        return $this->re_msg('卡券' . $msg . '成功');

    }

    /**
     * 核销方法
     * @param $card_codes
     * @param $user
     * @param int $event_type  1领取  2核销
     * @param int $consume_channel 1商城核销   2到店代金券 e3s核销
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function couponReceive($card_codes, $user, $event_type = 1, $consume_channel=1)
    {
//        Logger::error('order-save-in', ['card_code'=>$card_codes,'even_type'=>$event_type, 'time' => date('Y-m-d H:i:s')]);
        if (empty($card_codes)) return true;
        $coupons = (new BuCardReceiveRecord())
            ->alias('a')
            ->join('t_db_card b', 'a.card_id = b.id')
            ->where(['a.card_code' => ['in', $card_codes]])
            ->field('a.*, b.quick_win_card_id, b.consume_condition')
            ->select();
        Logger::error('order-save-in', ['card_code'=>$card_codes,'coupons'=>$coupons,'even_type'=>$event_type, 'time' => date('Y-m-d H:i:s')]);

        $order_model = new BuOrder();
        foreach ($coupons as $item) {
            $lock = 'CardReceiveRecord:' . $item['card_code'] . '-' . $item['status'];
            if (!getRedisLock($lock, mt_rand(40, 80))) {
                continue;
            }
            if ($event_type == 1) {
                $receive_data    = $this->getReceiveChannel($item['dlr_code']);
                $receive_channel = $receive_data['receive_channel'];
                $receive_source  = $receive_data['receive_source'];
                $brand           = DbDlr::channel_to_brand($item['dlr_code']);
                $data            = [
                    "coupon_id"               => $item['quick_win_card_id'],
                    "intention_brand_id"      => $brand == 3 ? 9 : $brand, // pz品牌id在卡券中心是9
                    "one_id"                  => $user['one_id'],
                    "receive_channel"         => $receive_channel,
                    "receive_source"          => $receive_source,
                    "reserve_status"          => 0,
                    "user_name"               => $item['name'],
                    "user_phone"              => $item['phone'],
                    "vin"                     => $item['vin'],
                    "consume_begin_date"      => $item['validity_date_start'],
                    "consume_end_date"        => $item['validity_date_end'],
                    "coupon_center_flag"      => $item['coupon_center_flag'],
                    //                    "intention_brand_id"   => $item['intention_brand_id'],
                    "intention_car_series_id" => $item['intention_car_series_id'],
                    "intention_car_type_id"   => $item['intention_car_type_id'],
                    "intention_store"         => $item['intention_store'],
                    "reserve_date"            => $item['reserve_date'],
                    "reserve_id"              => $item['reserve_id'],


                ];
                if ($item['consume_condition'] == 1) {
                    $data['intention_store']       = $item['get_dlr_code'];
                    $data['intention_car_type_id'] = $item['car_18n'];
                    $redis_dlr_key                 = 'e3s_dlr_id_' . $item['get_dlr_code'];
                    $redis_dlr_id                  = redis($redis_dlr_key);
                    if (!empty($redis_dlr_id)) {
                        $data['intention_store_id'] = $redis_dlr_id;
                    } else {
                        $dlr_list                   = QuickWin::create('e3s_dlr')->postDlr(['dlrCode' => $item['get_dlr_code'], 'sortType' => '2'], $data['intention_brand_id']);
                        $data['intention_store_id'] = $dlr_list['rows'][0]['dlrId'] ?? 0;
                        redis($redis_dlr_key, $data['intention_store_id'], 3600);
                    }
                    $redis_car_key  = 'e3s_car_id_' . $item['car_18n'];
                    $redis_car_info = redis($redis_car_key);
                    if (!empty($redis_car_info)) {
                        $car_info = $redis_car_info;
                    } else {
                        $car_info = E3sCarSeries::where('car_config_code', $item['car_18n'])->find();
                        redis($redis_car_key, $car_info, 3600);
                    }
                    $data['intention_car_series_id'] = $car_info['base_series_code'];
                    BuCardReceiveRecord::where('id', $item['id'])->update(['intention_dlr_id' => $data['intention_store_id']]); // 意向门店id填充进卡券领取表
                }

                $arr1 = ['id' => $item['id'], 'event_type' => $event_type, 'user_id' => $item['user_id'], 'card_id' => $item['card_id'], 'data' => $data];
                Hook::exec('app\\net_small\\behavior\\CardReceiveRecordBehavior', 'run', $arr1);
            }
            if ($event_type == 2) {
                if ($consume_channel == 1) {
                    $map = ['order_code' => $item['consume_order_code']];
                    $dd_dlr_code = $order_model->where($map)->value('dd_dlr_code') ?? '';
                } else {
                    $dd_dlr_code = $item['get_dlr_code'];
                }
                $data = [
                    [
                        "business_order_name"     => "",
                        "business_order_no"       => $item['consume_order_code'],
                        "coupon_code"             => $item['coupon_code'],
                        "coupon_discounted_price" => (int)($item['card_all_yh'] * 100),
                        "coupon_receive_id"       => '',
                        "creator"                 => "",
                        "order_source"            => "",
                        "used_store"              => $dd_dlr_code,
//                        "used_store"              => $item['get_dlr_code'],
//                        "used_store_id"           => $item['intention_dlr_id'],
                        'consume_channel'          => 3, // 核销渠道  3-商城
                    ]
                ];
                $arr1 = ['id' => $item['id'], 'event_type' => $event_type, 'user_id' => $item['user_id'], 'card_id' => $item['card_id'], 'coupon_code' => $item['coupon_code'], 'data' => $data];
                Logger::error('order-save-in', ['order_code' => $item['consume_order_code'], 'arr' => $arr1 , 'time' => date('Y-m-d H:i:s')]);
                Hook::exec('app\\net_small\\behavior\\CardReceiveRecordBehavior', 'run', $arr1);
            }
        }
        return true;
    }

    // 绑定用户默认车型
    // $index_in 首页需要提示
    public function bindUserCar($user, $vin,$index_in=0)
    {
        if (!$user['id']) {
            return $this->re_msg($vin);
        }
        $u_car_s_model = new DbUserCarSeries();
        $channel_type  = $user['channel_type'];
        if (!in_array($user['channel_type'], ['GWSM', 'GWAPP'])) {
            $channel_type = '';
        }
        $user_car = $u_car_s_model->getOne(['where' => ['user_id' => $user['id'], 'vin' => $vin, 'car_brand_code' => $user['brand'], 'channel_type' => $channel_type]]);
        if (empty($user_car)) {
            return $this->re_msg('用户vin码不存在', 400);
        }
        if(!$user_car['is_bind']){
            $u_car_s_model->saveData(['is_bind' => 0, 'modifier' => 'user_c_' . mt_rand(100, 999)], ['user_id' => $user['id'], 'is_bind' => 1, 'car_brand_code' => $user['brand'], 'channel_type' => $channel_type]);
            $u_car_s_model->saveData(['is_bind' => 1, 'modifier' => 'user_c_' . mt_rand(100, 999)], ['user_id' => $user['id'], 'vin' => $vin, 'car_brand_code' => $user['brand'], 'channel_type' => $channel_type]);
            redis(config('cache_prefix.user_car_18n_list') . $user['id'] . $user['channel_type'], null);
            Cache::rm('USER-CAR-OWNER' . $user['bind_unionid'] . $user['member_id'] . $user['one_id'] . $user['channel_type']);
        }

        //新增缓存需要在首页显示
        if($index_in==1 && !$user_car['is_bind']){
            redis(config('cache_prefix.index_tip') . $user['id'] . $user['channel_type'], 1,3600*24);
        }

        return $this->re_msg($vin);
    }

    public function checkCancelUser($m_id)
    {
        $order_model = new BuOrder();
        $user_model  = new DbUser();
        $after_model = new DbAfterSaleOrders();

        $user      = $user_model->getOne(['where' => ['plat_id' => $m_id]]);
        $order_all = $order_model
            ->alias('a')
            ->field('a.id, a.order_status,b.commodity_class')
            ->join('t_bu_order_commodity b', 'a.order_code=b.order_code', 'left')
            ->where(['a.user_id' => $user['id'], 'parent_order_type' => ['in', [1, 2]]])
            ->select();

        if (empty($order_all)) {
            return $this->re_msg('Ni+商城没订单记录');
        }

        foreach ($order_all as $v) {
            if (!in_array($v['order_status'], [1, 3, 5, 7, 8, 9, 14, 18]) || ($v['commodity_class'] == 2 && $v['order_status'] == 2)) {
                return $this->re_msg('Ni+商城存在可疑订购订单', '403');
            }
        }
        $order_ids = array_column($order_all, 'id');
        $after     = $after_model->where(['order_id' => ['in', $order_ids], 'afs_status' => ['not in', [2, 3, 6, 8, 11, 14]]])->find();
        if ($after) {
            return $this->re_msg('Ni+商城存在可疑售后订单', '403');
        }
        return $this->re_msg(true);
    }

    /**
     * 冻结卡券
     * @param $userId
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function freezeCoupon($userId, $requestData)
    {
        $quickWinData = [
            "business_order_no" => $requestData['business_order_no'],
            "coupon_code"       => $requestData['coupon_code'],
//            "coupon_receive_id" => $requestData['coupon_receive_id'] ?? ""
        ];
        $where        = [
//            'user_id'     => $userId,
            'card_code' => $requestData['card_code'],
        ];
        $card_info    = BuCardReceiveRecord::get($where);
//        $order_info   = BuOrder::get(['order_code' => $requestData['business_order_no']]);
        if (empty($card_info)) {
            return $this->re_msg('卡券不存在' . $userId . '||' . $requestData['coupon_code'], -1);
        }
        $card_log_model = new DbCardLog();
        $log = [
            'user_id'       => $userId,
            'event_type'    => 5, // 冻结
            'card_id'       => $card_info['card_id'],
            'request_info'  => json_encode_cn($quickWinData),
        ];
        $logId = $card_log_model->insertGetId($log);
        $is_success = 0;
        $msg = '';
        try {
            $ret        = QuickWin::create('quick_win')->postCouponFreezeCoupon($quickWinData);
            // 2024-12-12 10:21:35 卡券中心-张世科  1 成功   *********** 同单号多次冻结成功
            if (isset($ret['result']) && in_array($ret['result'], [1,***********])) {
                $is_success = 1;
            }
            if (isset($ret['msg'])) {
                $msg = $ret['msg'];
            }
            $upd = [
                'is_success' => $is_success,
                'response_info' => json_encode_cn($ret),
            ];
        } catch (Exception $e) {
            $msg = $e->getMessage();
            $upd = [
                'is_success' => $is_success,
                'response_info' => $msg,
            ];
        }
        $card_log_model->where('id', $logId)->update($upd);

        if (!$is_success) {
            return $this->re_msg($msg, -1);
        }

        $upd = [
            'status'             => 5,
//            'consume_date'       => date('Y-m-d H:i:s'),
            'last_updated_date'  => date('Y-m-d H:i:s'),
            'consume_order_code' => $requestData['business_order_no'] ?? '',
//            'consume_dlr_code'   => $order_info['dd_dlr_code'] ?? $order_info['dlr_code'],
        ];
        //领取卡券状态：1-领取;；2-未领取；3-核销；4-已删除；5-冻结
        $saveData = BuCardReceiveRecord::where('id', $card_info['id'])->update($upd);

        if (!$saveData) {
            return $this->re_msg('卡券冻结失败', -1);
        }
        return $this->re_msg('卡券冻结成功');

    }


    /**
     * 解冻
     * @param $userId
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function unfreezeCoupon($userId, $requestData)
    {
        $quickWinData = [
            "business_order_no" => $requestData['business_order_no'],
            "coupon_code"       => $requestData['coupon_code'],
//            "coupon_receive_id" => $requestData['coupon_receive_id'] ?? ""
        ];
        $where        = [
//            'user_id'     => $userId,
            'card_code' => $requestData['card_code'],
        ];
        $card_info    = BuCardReceiveRecord::get($where);
        if (empty($card_info)) {
            return $this->re_msg('卡券不存在' . $userId . '||' . $requestData['card_code'], -1);
        }
        $card_log_model = new DbCardLog();
        $log = [
            'user_id'       => $userId,
            'event_type'    => 6, // 解冻
            'card_id'       => $card_info['card_id'],
            'request_info'  => json_encode_cn($quickWinData),
        ];
        $logId = $card_log_model->insertGetId($log);
        $is_success = 0;

        $msg = '';
        try {
            $ret        = QuickWin::create('quick_win')->postCouponUnFreezeCoupon($quickWinData);
            // 2024年12月12日 20:40:34 卡券中心 张世科 1-解冻成功  11004014032-已经被解冻  没有找到关联业务单号冻结
            if (isset($ret['result']) && in_array($ret['result'], [1,11004014032])) {
                $is_success = 1;
            }
            $upd = [
                'is_success' => $is_success,
                'response_info' => json_encode_cn($ret),
            ];
            if (isset($ret['msg'])) {
                $msg = $ret['msg'];
            }
        } catch (Exception $e) {
            $msg = $e->getMessage();
            $upd = [
                'is_success' => $is_success,
                'response_info' => $msg,
            ];
        }

        $card_log_model->where('id', $logId)->update($upd);

        if (!$is_success) {
            return $this->re_msg($msg, -1);
        }

        //领取卡券状态：1-领取;；2-未领取；3-核销；4-已删除；5-冻结
        $upd      = ['status' => 1, 'consume_order_code' => '', 'last_updated_date' => date('Y-m-d H:i:s')];
        $saveData = BuCardReceiveRecord::where('id', $card_info['id'])->update($upd);

        if (!$saveData) {
            return $this->re_msg('卡券解冻失败', -1);
        }
        return $this->re_msg('卡券解冻结成功');
    }


    /**
     * 复活
     * @param $user
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function postCouponRevive($user, $requestData)
    {
        $where     = [
            'user_id'     => $user['id'],
            'coupon_code' => $requestData['coupon_code'],
        ];
        $card_info = BuCardReceiveRecord::get($where);

        if (empty($card_info)) {
            return $this->re_msg('卡券不存在' . $user['id'] . '||' . $requestData['coupon_code'], -1);
        }
        $receive_data   = [
            'coupon_code'      => $requestData['coupon_code'],
            'modifier'         => $user['name'],
            'msg_id'           => $requestData['msg_id'] ?? 0,
            'one_id'           => $user['one_id'],
            'phone'            => $user['phone'],
            'vin'              => $user['vin'] ?? '',
            'receive_scene_id' => $requestData['receive_scene_id'] ?? 0
        ];
        $coupon_receive = QuickWin::create('quick_win')->postCouponRevive($receive_data);

        $is_success = $coupon_receive['result'] == '1' ? 1 : 0;
        DbCardLog::create(
            [
                'user_id'       => $user['id'],
                'event_type'    => 4, // 复活
                'is_success'    => $is_success,
                'card_id'       => $card_info['card_id'],
                'request_info'  => json_encode_cn($receive_data),
                'response_info' => json_encode_cn($coupon_receive),
            ]
        );

        if ($coupon_receive['result'] == 1) {
            BuCardReceiveRecord::find('id', $card_info['id'])->save(
                [
                    'status'               => 1,
                    'consume_date'         => "",
                    'quick_win_is_consume' => 0,
                    'is_enable'            => 1,
                    'last_updated_date'    => date('Y-m-d H:i:s'),
                ]
            );
            return $this->re_msg('卡券复活成功');
        } else {
            return $this->re_msg('卡券复活失败', -1);
        }

    }

    /**
     * 卡券反核销
     * @param $user
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function postCouponReviveV3($user, $requestData)
    {
        $where     = [
//            'user_id'     => $user['id'],
            'coupon_code' => $requestData['coupon_code'],
            'status' => 3,
        ];
        $card_r_r_model = new BuCardReceiveRecord();
        $card_info = $card_r_r_model->where($where)->find();

        if (empty($card_info)) {
            return $this->re_msg('卡券不存在' . $user['id'] . '||' . $requestData['coupon_code'], -1);
        }
        $receive_data   = [
            'coupon_code' => $requestData['coupon_code'],
        ];
        $is_success = 0;
        $card_log_model = new DbCardLog();
        $log = [
            'user_id'       => $user['id'],
            'event_type'    => 7, // 反核销
            'is_success'    => $is_success,
            'card_id'       => $card_info['card_id'],
            'request_info'  => json_encode_cn($receive_data),
        ];
        $logId = $card_log_model->insertGetId($log);
        try {
            $coupon_receive = QuickWin::create('quick_win')->postCouponReviveV3($receive_data);
            if ($coupon_receive['result'] == 1) {
                $is_success = 1;
            }
            $msg = json_encode_cn($coupon_receive);
        } catch (Exception $e) {
            $msg = $e->getMessage();
        }
        $upd = [
            'is_success' => $is_success,
            'response_info'  => $msg,
        ];
        $card_log_model->where(['id'=>$logId])->update($upd);

        if ($is_success ) {
            $upd = [
                'status'               => 1,
                'consume_date'         => "",
                'consume_dlr_code'     => '',
                'consume_order_code'   => '',
                'quick_win_is_consume' => 0,
                'last_updated_date'    => date('Y-m-d H:i:s'),
            ];
            $card_r_r_model->where(['id'=>$card_info['id']])->update($upd);
            return $this->re_msg('卡券反核销成功');
        } else {
            return $this->re_msg('卡券反核销失败', -1);
        }

    }

    /**
     * 核销
     * @param $user
     * @param $requestData
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function postCouponConsume($user, $requestData)
    {
        $cardData = BuCardReceiveRecord::where([
            'user_id'            => $user['id'],
            'consume_order_code' => $requestData['business_order_no'],
        ])->select();

        if (empty($cardData)) {
            return $this->re_msg("订单关联卡券不存在" . $user['id'] . "|" . $requestData['business_order_no'], 403);
        }
        /**
         * 状态【status】 1已领取，2,未领取,3已核销,4已删除
         */
        foreach ($cardData as $v) {
            $statusData = BuCardReceiveRecord::STATUS_LIST;
            if (!in_array($v['status'], [BuCardReceiveRecord::STATUS_KEY1, BuCardReceiveRecord::STATUS_KEY5])) {
                $m = $statusData[$v['status']] ?? '不可用';
                return $this->re_msg('订单关联券' . $m . '，订单核销失败', 403);
            }
        }
        foreach ($cardData as $item) {
            if (empty($item['id'])) {
                continue;
            }

            $coupon_receive = QuickWin::create('quick_win')->getCouponReceiveRecord([
                'coupon_code'     => $item['coupon_code'],
                'request_channel' => 1
            ]);
            $data           = [
                [
                    "business_order_name"     => "",
                    "business_order_no"       => $requestData['business_order_no'],
                    "coupon_code"             => $item['coupon_code'],
                    "coupon_discounted_price" => (int)($item['card_all_yh'] * 100),
                    "coupon_receive_id"       => $coupon_receive['rows'][0]['id'] ?? 0,
                    "consume_activity_list"   => $requestData['consume_activity_list'] ?? [],
                    "creator"                 => "",
                    "order_source"            => "",
                    "used_store"              => $item['get_dlr_code'],
                    "used_store_id"           => $item['intention_dlr_id'],
                    'consume_channel'          => 3, // 核销渠道  3-商城
                ]
            ];

            $ret = QuickWin::create('quick_win')->postCouponConsume($data);

            $is_success = $ret['result'] == '1' ? 1 : 0;
            DbCardLog::create(
                [
                    'user_id'       => $user['id'],
                    'event_type'    => 2, // 核销
                    'is_success'    => $is_success,
                    'card_id'       => $item['card_id'],
                    'request_info'  => json_encode($data),
                    'response_info' => json_encode($ret),
                ]
            );
            if ($ret['result'] != 1) {
                return $this->re_msg($ret['msg']);
            }
            BuCardReceiveRecord::where('id', $item['id'])->update(['user_vin' => $user['vin'], 'status' => 3, 'quick_win_is_consume' => 1, 'last_updated_date' => date('Y-m-d H:i:s')]);
        }
        return $this->re_msg('订单核销完成');

    }

    /**
     * 已核销转未核销且待激活
     * @param $requestData
     * @return array|mixed|string
     */
    public function postBatchUnConsumeUnactive($requestData)
    {
        $data_all = [];
        foreach ($requestData as $item) {
            $data = [
                "business_order_no" => $item['order_code'] ?? "",
                "coupon_code" => $item['coupon_code'] ?? "",
                "modifier" => $item['modifier'] ?? "",
            ];
            $data_all[] = $data;
        }
        return QuickWin::create('quick_win')->postBatchUnConsumeUnactive($data_all);
    }


}
