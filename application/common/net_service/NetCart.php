<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/17
 * Time: 6:29 PM
 */

namespace app\common\net_service;


use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuShoppingCart;
use app\common\model\db\DbCard;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbDlr;
use app\common\model\db\DbJobsLog;
use app\common\model\db\DbLimitDiscountCommodity;
use app\common\model\db\DbNDiscount;
use app\common\model\db\DbNDiscountInfo;
use app\common\port\connectors\QuickWin;
use think\Exception;

class NetCart extends Common
{


    public function __construct()
    {
        parent::__construct();
    }
    public function cart_card_bset($cart_ids,$all_cart_list,$user_id,$channle_type, $disabled_card_sku_ids = [],$sku_rel_cards=[]){
        $best=['card_yh_arr'=>[],'card_yh_all_money'=>0];
        $card_model =  new DbCard();
        $fileds           = "a.*,b.commodity_id,d.price,b.count,b.id cid,b.sku_id,GROUP_CONCAT(distinct c.group_sub_commodity_id) AS g_s_goods_id,GROUP_CONCAT(distinct c.set_sku_ids) g_s_sid,c.group_card_type,b.sku_c_json,b.car_series_id,b.order_vin,b.dd_dlr_code,b.gift_card_id,rec.card_code,b.gift_c_json,b.gift_card_id,rec.user_id,rec.receive_vin,b.order_vin,act.up_down_channel_dlr act_upd_dlr,a.up_down_channel_dlr card_upd_dlr,act.select_obj";
        $cart_card_where = ['b.id'=>['in',$cart_ids],'a.is_enable'=>1,'c.is_enable'=>1];
        if (!empty($disabled_card_sku_ids)) {
            $cart_card_where['b.sku_id'] = ['not in', $disabled_card_sku_ids];
        }
        $cart_model =  new BuShoppingCart();
        $cart_list =  $cart_model->getList(['where'=>['id'=>['in',$cart_ids]]]);
        $cart_vin = '';
        if($cart_list){
            foreach ($cart_list as $cart_v){
                if($cart_v['order_vin']){
                    $cart_vin.="'".$cart_v['order_vin']."',";
                }
            }
        }
//        $cart_card_where[]      = ['exp', sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr)", $channle_type)];

        //(FIND_IN_SET(d.id,c.set_sku_ids) || c.set_sku_ids is null)  || c.group_sub_commodity_id>0 只判断普通商品即可
        //where(
        ////            ['a.validity_date_start'=>[['ELT',date('Y-m-d')],['exp','is null'],'or'],'a.validity_date_end'=>[['EGT',date('Y-m-d')],['exp','is null'],'or']]
        //        )

//        $cart_card_where[]      = ['exp', sprintf("(rec.activity_id>0 and  FIND_IN_SET('%s',act.up_down_channel_dlr)) or (rec.activity_id=0 and  FIND_IN_SET('%s',a.up_down_channel_dlr))", $channle_type, $channle_type)];//查出来后在后面过滤
        $cart_card_where['rec.is_enable'] = 1;
        $cart_card_where['rec.status'] = 1;
        $join_order_vin = '';

        if($cart_vin){
//            $join_order_vin.= sprintf("or (rec.receive_vin in (%s) and rec.receive_vin = b.order_vin  and (rec.receive_vin is not null or rec.receive_vin<>''))",trim($cart_vin,','));
        }
        //购物车卡券应该计算可以使用的
        $order_cards =  $card_model->alias('a')->join('t_db_commodity_card c',"c.card_id=a.id")
            ->join('t_db_commodity_flat flat','(FIND_IN_SET( c.class_id, flat.comm_type_id_str ) OR   c.commodity_set_id = flat.commodity_set_id  ) and c.is_enable=1 and flat.is_enable=1 ')
//            ->join('t_db_commodity_flat flat','( c.commodity_set_id = flat.commodity_set_id  ) and c.is_enable=1 and flat.is_enable=1 ')
            ->join("t_db_commodity_set_sku d",sprintf("d.commodity_set_id=flat.commodity_set_id"))// and (FIND_IN_SET(d.id,c.set_sku_ids) || c.set_sku_ids is null || c.group_sub_commodity_id>0)  这个过滤在 card_js 就有了

            ->join('t_bu_shopping_cart b','b.sku_id=d.id')
            ->join('t_bu_card_receive_record rec',sprintf("rec.card_id=a.id and rec.is_enable=1 and ((rec.user_id=%s and rec.user_id>0) %s)",$user_id,$join_order_vin))// or ( rec.receive_vin = b.order_vin  and (rec.receive_vin is not null or rec.receive_vin<>''))--man
            ->join('t_db_activity act',"act.activity_id=rec.activity_id and rec.activity_id>0",'left')
            ->where($cart_card_where)->field($fileds)->group('a.id,b.id')->select();
//        print_json($card_model->getLastSql(),$order_cards);
        $order_vin_str = '';
//        print_json($sku_rel_cards);
        $gift_card_code=[];
        if($order_cards){
            foreach ($order_cards as $uk=>$uv){
                //有活动取活动上下架
                $upd_dlr =  $uv['card_upd_dlr'];
                if($uv['act_upd_dlr']){
                    $upd_dlr = $uv['act_upd_dlr'];
                }
                $upd_dlr_arr =  explode(',',$upd_dlr);
                if(!in_array($channle_type,$upd_dlr_arr)){
                    unset($order_cards[$uk]);
                }
            }
            $card_arr = [];
            foreach ($order_cards as $kk=> $vv){
                if($vv['select_obj']){
                    if($vv['select_obj']==1 && $vv['user_id']!=$user_id){
                        unset($order_cards[$kk]);
                        continue;
                    }
                    if($vv['select_obj']==2 && $vv['receive_vin']!=$vv['order_vin']){
                        unset($order_cards[$kk]);
                        continue;
                    }
                    if($vv['select_obj']==3 && ($vv['user_id']!=$user_id || $vv['receive_vin']!=$vv['order_vin'])){
                        unset($order_cards[$kk]);
                        continue;
                    }
                }else{
                    if($vv['receive_vin'] && $vv['user_id'] && ($vv['user_id']!=$user_id || $vv['receive_vin']!=$vv['order_vin'] )){
                        unset($order_cards[$kk]);
                        continue;
                    }
                }

                $order_cards[$kk]['price'] = $all_cart_list[$vv['cid']];
                if($vv['gift_card_id'] && $vv['gift_card_id']==$vv['id'] ){
//                    $gift_card_code[]=$vv['card_code'];//买赠卡券号不做特殊处理，在上一层+赠品价格
                }
                if($vv['order_vin']){
                    $order_vin_str.="'".$vv['order_vin']."'".',';
                }
                $one_sku_card_arr = [];
                $in_one_sku_card = 1;
                //$sku_rel_cards 配置了活动共用卡券的
                if(isset($sku_rel_cards[$vv['cid']]) && !empty($sku_rel_cards[$vv['cid']])){
                    $one_sku_card_arr = explode(",",$sku_rel_cards[$vv['cid']]);
                    if(!in_array($vv['id'],$one_sku_card_arr)){
                        unset($order_cards[$kk]);
                        $in_one_sku_card=0;
                        continue;

                    }
                }
//                if($vv['id']==19423841699791872){
//                    print_json($vv,$in_one_sku_card);
//                }

                //组合商品，并且设置了sku，进行过滤
                if($vv['sku_c_json'] && $vv['g_s_sid']){
                    $sku_c_json =  json_decode($vv['sku_c_json'],true);
                    $sku_id_key = array_keys($sku_c_json);
                    $in_one_sku_card = $this->_cl_can_card($vv,$sku_id_key);
                    if(!$in_one_sku_card){
                        unset($order_cards[$kk]);
                        continue;
                    }
//                    // && $vv['group_card_type']==2
//                    $g_s_sid_arr = explode(',',$vv['g_s_sid']);
//                    $g_s_goods_id_arr = explode(',',$vv['g_s_goods_id']);
//                    $g_s_goods_id_count =  count(array_unique($g_s_goods_id_arr));
//                    $sku_inter = array_intersect($sku_id_key,$g_s_sid_arr);
//                    //全部满足的需要  交集sku数量==配置的子商品数
//                    if($vv['group_card_type']==2){
//                        if(count($sku_inter)!=$g_s_goods_id_count){
//                            unset($order_cards[$kk]);
//                            $in_one_sku_card=0;
//                        }
//                    }else{
//                        if(!$sku_inter){
//                            unset($order_cards[$kk]);
//                            $in_one_sku_card=0;
//                        }
//                    }
                }
                if($in_one_sku_card){
                    $card_arr[]=$vv['id'];
                }
                $user_gift_count = 0;

                if($vv['gift_card_id'] && $vv['gift_c_json']){
                    $gift_card_arr = json_decode($vv['gift_c_json'],1);
                    foreach ($gift_card_arr as $gg_v){
                        $user_gift_count+=$gg_v['count'];
                    }
//                    print_json($user_gift_count);
                }
                if($user_gift_count){
                    $order_cards[$kk]['count']-=$user_gift_count;
                }
//                if($vv['id']==19423841699791872){
//                    print_json($vv,$in_one_sku_card);
//                }
            }

            $card_list        = $this->_userAllCard($user_id,array_values($card_arr),$channle_type,$gift_card_code,[],trim($order_vin_str,','));//已领取
            $data = $this->card_js($order_cards,$card_list,implode(',',$card_arr));
//            print_json($order_cards,$card_list,$data);


            if($data['all_card_list']){
                $best =$this->best_card($data['all_card_list']);
                return $best;
            }
        }
        return $best;
    }

    public function select_cards($set_sku_id_arr, $user_id, $channle_type, $sku_id_arr)
    {
        $card_model = new DbCard();
        $fileds = "a.*,d.commodity_id,d.price,d.commodity_sku_id sku_id,d.id cid";
        $order_cards = $card_model->alias('a')->join('t_db_commodity_card c', "c.card_id=a.id")
            ->join("t_bu_card_receive_record rec", "rec.card_id=a.id and rec.user_id=$user_id")
            ->join("t_db_commodity_set_sku d", "d.commodity_set_id=c.commodity_set_id")
            ->where(sprintf("FIND_IN_SET('%s',a.up_down_channel_dlr)", $channle_type))
            ->where(['rec.status' => 1, 'a.validity_date_start' => [['ELT', date('Y-m-d')], ['exp', 'is null'], 'or'], 'a.validity_date_end' => [['EGT', date('Y-m-d')], ['exp', 'is null'], 'or']])
            ->where(['d.id' => ['in', $set_sku_id_arr]])->field($fileds)->group('a.id,d.id')->select();
        $data = [];
        if ($order_cards) {
            $card_arr = [];
            foreach ($order_cards as $kk => $vv) {
                $card_arr[] = $vv['id'];
                $order_cards[$kk]['count'] = $sku_id_arr[$vv['cid']];
            }
            $card_list = $this->_userAllCard($user_id, array_values($card_arr)); // 已领取
            $data = $this->card_js($order_cards, $card_list, implode(',', $card_arr));
        }
        return $data;
    }

    public function select_card_bset($set_sku_id_arr,$user_id,$channle_type,$sku_id_arr,$all_cart_list, $disable_card_ids = [],$dis_card_skus=[],$card_sku_use=[],$default_vin='',$dd_dlr_code = ""){
        $best=['card_yh_arr'=>[],'card_yh_all_money'=>0];
        if($dis_card_skus){
            $set_sku_id_arr =  array_diff($set_sku_id_arr,$dis_card_skus);
        }
        $goods_card_where = ['d.id' => ['in', $set_sku_id_arr]];
        if (!empty($disable_card_ids)) {
            $goods_card_where["a.id"] = ['not in', $disable_card_ids];
        }
        $user_ss = session("net-api-user-info");
        if(!$user_id){
            return  $best;
        }

        $card_model =  new DbCard();
        if($default_vin){
//            $user_where      = sprintf("((rec.user_id=%s  and rec.user_id>0) or ( rec.receive_vin in ('%s') ))", $user_id,trim($default_vin,"'"));
            $user_where      = sprintf("(rec.user_id=%s or ( rec.receive_vin in ('%s') ))", $user_id,trim($default_vin,"'"));
        }else{
            $user_where      = sprintf("(rec.user_id=%s )", $user_id);
        }
        // t_bu_card_receive_record 关联了已领券，
        $fileds           =  "'{$dd_dlr_code}' as dd_dlr_code,a.*,d.commodity_id,d.price,d.id sku_id,d.id cid,GROUP_CONCAT(distinct c.group_sub_commodity_id) AS g_s_goods_id,GROUP_CONCAT(distinct c.set_sku_ids) g_s_sid,c.group_card_type,rec.receive_vin,rec.user_id";
        $order_cards =  $card_model->alias('a')->join('t_db_commodity_card c',"c.card_id=a.id")
            ->join('t_db_commodity_flat flat','(FIND_IN_SET( c.class_id, flat.comm_type_id_str ) OR   c.commodity_set_id = flat.commodity_set_id  ) and c.is_enable=1 and flat.is_enable=1 ')
            ->join("t_bu_card_receive_record rec",sprintf("rec.card_id=a.id and %s",$user_where))

            ->join("t_db_commodity_set_sku d","d.commodity_set_id=flat.commodity_set_id")
            ->join('t_db_activity act',"act.activity_id=rec.activity_id and rec.activity_id>0",'left')
            ->where(" a.is_enable = 1 and rec.is_enable = 1 and " . sprintf(" %s and 
            (
            (rec.activity_id>0 and  FIND_IN_SET('%s',act.up_down_channel_dlr)) 
            or (rec.activity_id=0 and  FIND_IN_SET('%s',a.up_down_channel_dlr))
            )",$user_where, $channle_type,$channle_type))
            ->where(['rec.status'=>1,'rec.validity_date_start'=>[['ELT',date('Y-m-d H:i:s')],['exp','is null'],'or'],'rec.validity_date_end'=>[['EGT',date('Y-m-d H:i:s')],['exp','is null'],'or']])
            ->where($goods_card_where)->field($fileds)->group('a.id,d.id')->select();
      //  echo $card_model->getLastSql();exit;
       // print_json($order_cards,$card_model->getLastSql());
        if($order_cards){
            $card_arr = [];
            foreach ($order_cards as $k=>$v){
                $order_vin_str =  sprintf('%s',trim($default_vin,"'"));
                $order_vin_arr =  explode(',',$order_vin_str);
                if($v['receive_vin'] && $default_vin){
                    if(!in_array($v['receive_vin'],$order_vin_arr)){
                        unset($order_cards[$k]);
                        continue;
                    }
                }
                if($v['user_id'] && $v['user_id']!=$user_id){
                    unset($order_cards[$k]);
                    continue;
//                    if(!in_array($v['receive_vin'],$order_vin_arr) || $v['user_id']!=$user_id ){
//                        unset($card_list[$k]);
//                        continue;
//                    }
                }
            }
            foreach ($order_cards as $kk=> $vv){
                $order_cards[$kk]['car_series_id'] = $user_ss['car_series_id'];
                $order_cards[$kk]['order_vin'] = $user_ss['vin'];
                //处理活动可用卡券逻辑
                $one_sku_card_arr = [];
                $in_one_sku_card = 1;
                if(isset($card_sku_use[$vv['cid']]) && !empty($card_sku_use[$vv['cid']])){
                    $one_sku_card_arr = explode(",",$card_sku_use[$vv['cid']]);
                    if(!in_array($vv['id'],$one_sku_card_arr)){
                        unset($order_cards[$kk]);
                        $in_one_sku_card=0;
                    }
                }
                if($in_one_sku_card==1){
                    $card_arr[]=$vv['id'];
                    $order_cards[$kk]['count']=$sku_id_arr[$vv['cid']]['count'];
                    if(isset($sku_id_arr[$vv['cid']]['sku_json']) && $sku_id_arr[$vv['cid']]['sku_json']){
                        $order_cards[$kk]['sku_c_json']=json_encode($sku_id_arr[$vv['cid']]['sku_json']);
                    }else{
                        $order_cards[$kk]['sku_c_json']='';

                    }
                    $order_cards[$kk]['price'] = $all_cart_list[$vv['cid']];
                }

            }
            $card_list  = $this->_userAllCard($user_id,array_values($card_arr),$channle_type,[],[],$default_vin);//已领取
            $data = $this->card_js($order_cards,$card_list,implode(',',$card_arr));

            if($data['all_card_list']){
                $best =$this->best_card($data['all_card_list']);
//                print_json($best);

                return $best;
            }

        }
        return $best;
    }

    //sku+用户，获取最优卡券
    public function card_skus_price($commodity_set_sku_ids,$user_id,$time,$cart_goods_arr){
        $set_sku_model = new DbCommoditySetSku();
        $card_r_model =  new BuCardReceiveRecord();
        //卡券只有使用开始结束时间
        $where = ['d.id'=>['in',$commodity_set_sku_ids],"a.user_id"=>$user_id,"a.status"=>1,'b.validity_date_start'=>['<=',$time],'b.validity_date_end'=>['>=',$time]];
        $card_list =  $card_r_model->alias('a')->join('t_db_card b ',"a.card_id=b.id")->join('t_db_commodity_card c',"c.card_id=b.id")->join("t_db_commodity_set_sku d","d.commodity_set_id=c.commodity_set_id")->where($where)->group('b.id,a.card_id')->field("c.commodity_set_id,card_quota,a.card_code,a.card_id,d.id sku_id,b.card_name,b.*")->select();

//        $fileds           = "a.*,c.commodity_id,c.price,c.count,c.id cid,c.sku_id";

//        $card_list        = $this->_userAllCard($this->user_id);//已领取
//        if (!$card_list) {
//            return [];
//        }


        $card_yh_arr=[];
        $card_yh_all_money=0;
        if($card_list){
            $card_goods_arr = [];
            $card_goods_money=[];
            $goods_have_card=[];
            $one_card_arr=[];//独用卡
            $more_card_arr=[];//公用卡
            foreach ($card_list as $vv){
                //购物车卡券对商品信息.
                //卡券如果能用多张，那还要计算多张。。。
                $card_goods_money[$vv['card_id']][]=$cart_goods_arr[$vv['sku_id']];
                $card_goods_arr[$vv['card_id']]=[
                    'card_id'=>$vv['card_id'],
                    'can_with'=>$vv['can_with'],
                    'card_quota'=>$vv['card_quota'],
                    'card_type'=>$vv['card_type'],
                    'card_discount'=>$vv['card_discount'],
                    'least_cost'=>$vv['least_cost'],
                    'card_code'=>$vv['card_code'],
                    'card_name'=>$vv['card_name'],
                ];
                $goods_have_card[$vv['sku_id']][] = ['card_id'=>$vv['card_id']];
            }
            foreach ($card_goods_arr as $k=> $vv){
                $sum_price = array_sum($card_goods_money[$vv['card_id']]);//一个卡券的商品总金额--满减券使用
                if($vv['least_cost']){
                    if($sum_price<$vv['least_cost']){
                        $card_goods_arr[$k]['card_quota'] =$vv['card_quota']= 0;//不满的话那就价值==0
                    }
                }
                //折扣券
                if($vv['card_type']==2){
                    $card_goods_arr[$k]['card_quota'] =$vv['card_quota']= $sum_price*(1-$vv['card_discount']/10);//卡券价值==总价格*1-折扣
                }
                $card_goods_arr[$k]['sum_goods_money']=$vv['sum_goods_money'] = $sum_price;//
                if($vv['can_with']==1){
                    $more_card_arr[]=$vv;
                }else{
                    $one_card_arr[]=$vv;
                }
            }
            $more_card_money =  array_sum(array_column($more_card_arr,'card_quota'));
            $one_card_arr =  max(array_column($one_card_arr,'card_quota'));
            if($more_card_money>$one_card_arr){
                $card_yh_all_money = $more_card_money;
                $card_yh_arr = $more_card_arr;
            }else{
                $key =  array_column($one_card_arr,'card_quota');//取出数组中serverTime的一列，返回一维数组
                array_multisort($key,SORT_DESC,$one_card_arr);//排序，根据$serverTime 排序
                $card_yh_all_money=$one_card_arr[0]['card_quota'];
                $card_yh_arr = $one_card_arr[0];
            }
        }
        return ['card_yh_arr'=>array_values($card_yh_arr),'card_yh_all_money'=>$card_yh_all_money];
    }

    //卡券积分优惠计算
    public function card_point_yh($all_cart_list,$user_id,$channle_type){
        //            活动类型ID1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减
//        $this->user=$user;
//        $this->user_id=$user['id'];
        $sCom =  new Common();
        $n_dis_info_model   = new DbNDiscountInfo();
        $n_dis_model   = new DbNDiscount();
        $net_goods = new NetGoods();
        $all_segment_membership_dis = 0;
        $all_segment_owner_dis = 0;
        $all_point = 0;
        $all_money = 0;
        $limit_dis = 0;
        $seckill_dis = 0;
        $full_dis = 0;
        $n_dis = 0;
        $gift_act_dis = 0;
        $time =  date('Y-m-d H:i:s');
        $yh_sku_info=[];
        $commodity_set_sku_ids=[];
        $cart_goods_arr=[];
        $work_dis=0;
        $all_work_time=0;
        $dd_dlr_pay=[];
        $all_dd_pay_model = [];
        $cart_id_arr= [];
        $cart_id_price_arr=[];
        $full_yh_sku = [];
        $n_dis_yh_sku = [];
        $cart_goods_18_n =[];
        $sku_rel_cards = [];
        $maintain_dis = 0;
        $disabled_card_sku_ids = [];
        $gift_card_dis = 0;

        foreach ($all_cart_list as $k => $v) {
            //到店专营店信息
            $cart_id_arr[]=$v['cart_id'];
            if($v['car_18n'] && $v['relate_car_ids']){
                $cart_goods_18_n[]=$v['car_18n'];
            }
            if($v['dd_dlr_code']){
                $dlr_model=new DbDlr();

                $dlr =  $dlr_model->getOne(['where'=>['dlr_code'=>$v['dd_dlr_code']]]);
                $dlr_pay_bank_type=$dlr['ly_bank_code'];//01为招行，02为工行
                $dd_dlr_pay[] = $dlr['ly_bank_code'];
            }else{
                $dlr_pay_bank_type="01,02";//01为招行，02为工行
            }
            $all_dd_pay_model[$v['cart_id']]=[
                'cart_id'=>$v['cart_id'],
                'ly_bank_code'=>$dlr_pay_bank_type
            ];
            if (!empty($v['gift_act_id']) && !empty($v['gift_c_json']) && $v['gift_c_json'] <> '[]') {
                $gift_commodities = json_decode($v['gift_c_json'], true);
                $gift_act_row = $this->_gift_act($v['gift_act_id'],$user_id,$channle_type);
                $gift_act_info =  $gift_act_row['gift_info'];
//                $promotion_rel[$v['sku_id']]['card_available'] = $gift_act_info['card_available'];
//                $card_rel_card_ids = explode(",", $gift_act_info['rel_card_ids']);
                $sku_rel_cards[$v['cart_id']] = $gift_act_info['rel_card_ids'];
                foreach ($gift_commodities as $gift_c) {
                    $gift_act_dis += ($gift_c['price'] ?? 0);
                }
                if (empty($gift_act_info['card_available'])) {
                    $disabled_card_sku_ids[] = $v['sku_id'];
                }
            }
            if($v['maintain_dis']){
                $main_one =  bcdiv($v['price'],$v['maintain_dis']/10,0);
                $maintain_dis+= sprintf("%.2f",bcsub($main_one,$v['price'],0));
            }
            $one_g_price= 0;

            if (!empty($v['gift_card_id']) && !empty($v['gift_c_json']) && $v['gift_c_json'] <> '[]') {
                $gift_commodities = json_decode($v['gift_c_json'], true);

                foreach ($gift_commodities as $gift_c) {
                    $gift_card_dis += (round_bcmul($gift_c['price'],$gift_c['count'])  ?? 0);
                }

            }

            //todo
            if($v['sku_c_json_cn'] && $v['sku_c_json_cn']<>'[]'){
                $set_sku_model =  new DbCommoditySetSku();
                $sub_sku = json_decode($v['sku_c_json_cn'],true);
                $sub_sku_id_arr =  array_keys($sub_sku);

                $sub_list = $set_sku_model->getList(['where'=>['id'=>['in',$sub_sku_id_arr]],'field'=>'price,stock,id,is_enable']);
                $one_sub_work_price=0;
                foreach ($sub_list as $vv){
                    $one_sub_price=$vv['price']* $sub_sku[$vv['id']]['count'];
                    $one_g_price+=$one_sub_price;
                    if(isset($sub_sku[$vv['id']]['work_time_price'])){
                        if(isset($sub_sku[$vv['id']]['work_time_number']) && $sub_sku[$vv['id']]['work_time_number']>0){
                            $one_sub_work_price += $sub_sku[$vv['id']]['work_time_price']*$sub_sku[$vv['id']]['work_time_number']* $sub_sku[$vv['id']]['count'];//*$v['count']
                        }
                    }
                    $sub_sku[$vv['id']]['all_goods_price'] =$one_sub_price;

                }
                $work_time_price=$one_sub_work_price;//*$v['count']
                $all_cart_list[$k]['price']=$v['price'] =$v['b_act_price'] = $one_g_price;
            }else{
                $work_time_price=0;
                $work_time = json_decode($v['work_time_json'],true);
                if($work_time){
                    $work_time_price=$work_time['work_time_price']*$work_time['work_time_number'];//单价*数量，工时的价格
                }
            }

            // 商品会员价
            if ($v['use_discount']) {
                $commodity_dis_info = $net_goods->getCommoditySegmentDiscount($v['commodity_id']);
                if ($commodity_dis_info) {
                    $member_price = $net_goods->getCommodityDisFinalPrice($commodity_dis_info, $v['price']);
                    $commodity_dis = bcmul($v['price'] - floatval($member_price), $v['count'], 2);
                    if ($commodity_dis_info['user_segment'] == 1) {
                        $all_segment_membership_dis += $commodity_dis;
                    } else {
                        $all_segment_owner_dis += $commodity_dis;
                    }
                    $all_cart_list[$k]['price'] = $v['price'] = $member_price;
                }
            }

            $commodity_set_sku_ids[]=$v['sku_id'];
            $cart_goods_arr[$v['sku_id']]=$v['price']*$v['count'];
            $yh_data_json_str=[];
            $yh_wi_data_json_str = [];
            if ($v['act_json'] || $v['wi_act_json']) {
//                $yh_data_json_str[$v['sku_id']] = $v['act_json'];
////                echo json_encode($yh_data_json_str);
////                $yh_sku_info[$v['sku_id']] =$v;
//                $yh_sku_info[$v['sku_id']]            = array_values($sCom->yh_info($yh_data_json_str))[0];
//                $yh_sku_info[$v['sku_id']]['price'] =$v['price'];
//                $yh_sku_info[$v['sku_id']]['goods_prices'] =$v['count'] * $v['price'];
//                $yh_sku_info[$v['sku_id']]['commodity_id'] =$v['commodity_id'];
//                $yh_sku_info[$v['sku_id']]['count'] =$v['count'];
//                $yh_sku_info[$v['sku_id']]['sku_id'] =$v['sku_id'];
//                $yh_sku_info[$v['sku_id']]['work_time_price'] =$work_time_price;
//                $yh_sku_info[$v['sku_id']]['pay_style'] =$v['pay_style'];
                $v['act_json'] && $yh_data_json_str[$v['cart_id']] = $v['act_json'];
                $v['wi_act_json'] && $yh_wi_data_json_str[$v['cart_id']] = $v['wi_act_json'];
//                echo json_encode($yh_data_json_str);
//                $yh_sku_info[$v['sku_id']] =$v;
                $yh_sku_info[$v['cart_id']] = $yh_data_json_str ? array_values($sCom->yh_info($yh_data_json_str))[0] : [];
                $yh_sku_info[$v['cart_id']]['wi'] = $yh_wi_data_json_str ? array_values($sCom->yh_wi_info($yh_wi_data_json_str))[0] : [];
                $yh_sku_info[$v['cart_id']]['price'] =$v['price'];
                $yh_sku_info[$v['cart_id']]['goods_prices'] =$v['count'] * $v['price'];
                $yh_sku_info[$v['cart_id']]['commodity_id'] =$v['commodity_id'];
                $yh_sku_info[$v['cart_id']]['sku_c_json_cn'] =$v['sku_c_json_cn'];
                $yh_sku_info[$v['cart_id']]['count'] =$v['count'];
                $yh_sku_info[$v['cart_id']]['sku_id'] =$v['sku_id'];
                $yh_sku_info[$v['cart_id']]['work_time_price'] =$work_time_price;
                $yh_sku_info[$v['cart_id']]['pay_style'] =$v['pay_style'];
                $yh_sku_info[$v['cart_id']]['cart_id'] =$v['cart_id'];

                if (isset($yh_sku_info[$v['cart_id']]['full_id']) || isset($yh_sku_info[$v['cart_id']]['wi']['full_id'])) {
                    $full_yh_sku[$v['sku_id']] = $yh_sku_info[$v['cart_id']];
                }
                if (isset($yh_sku_info[$v['cart_id']]['n_dis_id'])) {
                    $n_dis_yh_sku[$v['sku_id']] = $yh_sku_info[$v['cart_id']];
                }
            }else{
                if($v['pay_style']==3) {
                    $all_point+=($v['price']+$work_time_price)*$v['count']*10;
                }else{
                    $all_money+=($v['price']+$work_time_price)*$v['count'];
                }
            }

            $all_work_time+=$work_time_price*$v['count'];
            $cart_id_price_arr[$v['cart_id']] = $v['price'];
        }
//        print_json($yh_sku_info,$all_cart_list);
        if($dd_dlr_pay){
            $dd_dlr_pay_arr = array_unique($dd_dlr_pay);
            if(count($dd_dlr_pay_arr)>1){
                $dndc_pay_pay_code = config('port')['ly_payment']['ly_pay_default_bank'];
            }else{
                $dndc_pay_pay_code = $dd_dlr_pay_arr[0];
            }
        }else{
            $dndc_pay_pay_code=config('port')['ly_payment']['ly_pay_default_bank'];
        }
//        dd($all_money);
        $all_dd_pay_model = array_values($all_dd_pay_model);
        foreach($all_dd_pay_model as $k=>$v){
            if($v['ly_bank_code']=='01,02'){
                $all_dd_pay_model[$k]['ly_bank_code'] = $dndc_pay_pay_code;
            }
        }
//        $net_cart =  new NetCart();
//        print_json($all_cart_list);
        //$cart_id_price_arr 用于修改单价--组合商品单价计算后
        if($yh_sku_info){
            $all_cart_list_new=[];
            $limit_sku_flag = [];
            $full_sku_flag = [];
            foreach ($yh_sku_info as $k => $v) {
                if (isset($v['full_id'])) {
                    $all_cart_list_new['f'.$v['full_id']]['full_id'] = $v['full_id'];
                    $full_ids[]                              = $v['full_id'];
                    $all_cart_list_new['f'.$v['full_id']]['list'][]  = $v;
                } elseif (isset($v['n_dis_id'])) {
                    $all_cart_list_new['n'.$v['n_dis_id']]['n_dis_id'] = $v['n_dis_id'];
                    $n_dis_ids[]                               = $v['n_dis_id'];
                    $all_cart_list_new['n'.$v['n_dis_id']]['list'][]   = $v;
                } else {
                    $all_cart_list_new['k'.$k]['list'][] = $v;
                }

                if (isset($v['wi']['full_id'])) {
                    $all_cart_list_new['f' . $v['wi']['full_id']]['full_wi_id'] = $v['wi']['full_id'];
                    $all_cart_list_new['f' . $v['wi']['full_id']]['full_wi_list'][] = $v;
                }

                if (!empty($v['sku_c_json_cn'])) {
                    $one_sku_id = '';
                } else {
                    $one_sku_id = $v['sku_id'];
                }
                $limit_info = [];
                $limit_wi_info = [];
                $seckill_info = [];
                if (isset($v['limit_id'])) {
                    $limit_row = $this->_limit($v['commodity_id'], $v['limit_id'], $user_id, $channle_type, $v['price'], $v['work_time_price'], $v['count'], $one_sku_id, 0);
                    $limit_info = $limit_row['limit_info'];
                }
                if (isset($v['wi']['limit_id']) && (!isset($v['limit_id']) || ($v['limit_id'] != $v['wi']['limit_id']))) {
                    $limit_wi_row = $this->_limit_wi($v['commodity_id'], $v['wi']['limit_id'], $user_id, $channle_type, $v['work_time_price'], $v['count'], $one_sku_id);
                    $limit_wi_info = $limit_wi_row['limit_info'];
                }

                if (isset($v['seckill_id'])) {
                    $seckill_row = $this->_seckill($v['commodity_id'], $v['seckill_id'], $user_id, $channle_type, $v['price'], $v['work_time_price'], $v['count'], $one_sku_id, 0);
                    $seckill_info = $seckill_row['seckill_info'];
                    $seckill_work_dis = $seckill_info['work_dis'];
                    $seckill_limit_price = $seckill_info['work_time_after_price_all'];
                    $one_dis_money = 0;
                    if (!empty($seckill_info)) {
                        $one_dis_money = $seckill_info['goods_dis'];
                        $seckill_price = $seckill_info['goods_after_price_all'];
                        //秒杀前购买
                        if($seckill_info['act_status'] == 1) {
                            $seckill_price = $v['price'] * $v['count'];
                            $one_dis_money=0;
                        }
                        if($seckill_info['goods_dis']>0){
                            $sku_rel_cards[$k]=$seckill_info['rel_card_ids'];
                        }
                    }else{
                        $seckill_price = $v['price'] * $v['count'];

                    }
                    $seckill_dis += $one_dis_money * $v['count'];
                    $work_dis += $seckill_work_dis * $v['count'];
                    if ($v['pay_style'] == 3) {
                        $all_point += ($seckill_price+$seckill_limit_price) * 10;
                    } else {
                        $all_money += $seckill_price+$seckill_limit_price;
                    }
                    $cart_id_price_arr[$k] = $seckill_info['goods_after_price'];//限时购价格对应购物车ID
                }

                if (!empty($limit_info) || !empty($limit_wi_info) ) {
                    $limit_work_dis = 0;
                    $one_dis_money = 0;
                    $limit_price = 0;
                    $work_limit_price = 0;

                    if (!empty($limit_info)) {
                        $limit_price = $limit_info['goods_after_price_all'];
                        $one_dis_money = $limit_info['goods_dis'];
                        $limit_work_dis = $limit_info['work_dis'];
                        $work_limit_price = $limit_info['work_time_after_price_all'];
                        $limit_sku_flag[$v['sku_id']]['commodity'] = 1;

                        if (empty($limit_info['card_available'])) {
                            $disabled_card_sku_ids[] = $v['sku_id'];
                        }
                        $cart_id_price_arr[$k] = $limit_info['goods_after_price'];//限时购价格对应购物车ID
                        if($limit_info['goods_dis']>0){
                            $sku_rel_cards[$k]=$limit_info['rel_card_ids'];
                        }
                    }

                    if (!empty($limit_wi_info) && empty($limit_info)) {
                        $limit_price = $v['price'] * $v['count'];
                        $one_dis_money = 0;
                        $limit_work_dis = $limit_wi_info['work_dis'];
                        $work_limit_price = $limit_wi_info['work_time_after_price_all'];
                        $limit_sku_flag[$v['sku_id']]['wi'] = 1;
                    } else if (!empty($limit_wi_row)) {
                        $limit_wi_info = $limit_wi_row['limit_info'];
                        $limit_work_dis = $limit_wi_info['work_dis'];
                        $work_limit_price = $limit_wi_info['work_time_after_price_all'];
                        $limit_sku_flag[$v['sku_id']]['wi'] = 1;
                    }
//                    print_json($one_dis_money,$yh_sku_info,$limit_info);
                    $limit_dis += $one_dis_money * $v['count'];
                    $work_dis += $limit_work_dis * $v['count'];
                    if ($v['pay_style'] == 3) {
                        $all_point += ($limit_price + $work_limit_price) * 10;
                    } else {
                        $all_money += $limit_price + $work_limit_price;
                    }
                }
            }
//            print_json($all_cart_list_new);
            $full_dis_list = [];
            $full_wi_dis_list = [];
            $n_dis_list = [];
            $full_not_dis_sku_ids = [];
            foreach ($all_cart_list_new as $v){
                $one_dis_money=0;
                $com = new Common();
                $all_full_info = [];
                $all_full_wi_info = [];
                if (isset($v['full_id'])) {
                    $all_full_info = $com->mj_yj($v['full_id'], 0, $v['list']);
                }
                if (isset($v['full_wi_id']) && (!isset($v['full_id']) || ($v['full_wi_id'] != $v['full_id']))) {
                    $all_full_wi_info = $com->mj_yj($v['full_wi_id'], 0, $v['full_wi_list']);
                }
                if(!empty($all_full_info) || !empty($all_full_wi_info)) {

                    if (!empty($all_full_info)) {
                        array_push($disabled_card_sku_ids, ...$all_full_info['disabled_card_sku_ids']);

                        foreach ($all_full_info['list'] as $vvv) {
                            $full_dis_list[$v['full_id']][$vvv['sku_id']] = $vvv;
                        }
                    }

                    if (!empty($all_full_wi_info)) {
                        foreach ($all_full_wi_info['list'] as $vvv) {
                            $full_wi_dis_list[$v['full_wi_id']][$vvv['sku_id']] = $vvv;
                        }
                    }
                } elseif (isset($v['n_dis_id'])) {
                    $all_dis_count = 0;
                    foreach ($v['list'] as $vv) {
                        $all_dis_count += $vv['count'];
                    }
                    $all_cart_list[$k]['all_dis_count'] = $all_dis_count;

                    $segment_info = get_user_segment_info();
                    $membership = $segment_info['membership_level'];
                    $owner = $segment_info['brand_owner_label'];
                    $_l_where   = sprintf("user_segment=0 or (user_segment=1 and FIND_IN_SET('%s',user_segment_options)) or (user_segment=2 and FIND_IN_SET('%s',user_segment_options))", $membership, $owner);
                    $n_dis_t_info = $n_dis_model->where(['id' => $v['n_dis_id']])->where($_l_where)->find();
                    if ($n_dis_t_info) {
                        $condition = ['n_id' => $v['n_dis_id']];
                        if ($n_dis_t_info['user_segment'] == 1) {
                            $condition['segment_label'] = $membership;
                        } else if ($n_dis_t_info['user_segment'] == 2) {
                            $condition['segment_label'] = $owner;
                        }
                        $n_dis_info_list = $n_dis_info_model->getList(['where' => $condition, 'order' => "piece desc"]);
                        if ($n_dis_info_list) {
                            $dis_p = 10;
                            foreach ($n_dis_info_list as $n_k => $n_v) {
                                if ($all_dis_count >= $n_v['piece']) {
                                    $dis_p = $n_v['discount'];
                                    $dis_ed = $n_v['piece'];
                                    break;
                                }
                            }
                            foreach ($v['list'] as $vv) {
                                $one_n_money = round($vv['price'] * $dis_p / 10 * $vv['count'], 2);

                                $one_n_dis_money = round($vv['price'] * $vv['count'] - $one_n_money, 2);
                                $vv['yh_money'] = $one_n_dis_money;
                                $n_dis_list[$v['n_dis_id']][$vv['sku_id']] = $vv;
                                $n_dis_list[$v['n_dis_id']][$vv['sku_id']]['n_dis_info'] = $n_dis_t_info;
                                if (empty($n_dis_t_info['card_available'])) {
                                    $disabled_card_sku_ids[] = $vv['sku_id'];
                                }
                            }
                        }
                    }
                }
            }

            foreach ($full_yh_sku as $sku_id => $cart) {
                $yh_money = 0;
                $work_yh_money = 0;
                $full_yh_rel_card_id='';
                if (isset($cart['full_id']) && isset($full_dis_list[$cart['full_id']][$sku_id])) {
                    $yh_money = $full_dis_list[$cart['full_id']][$sku_id]['yh_money'];
                    $full_yh_rel_card_id = $full_dis_list[$cart['full_id']][$sku_id]['rel_card_ids'];
                    $work_yh_money = $full_dis_list[$cart['full_id']][$sku_id]['work_yh_money'];
                    $full_sku_flag[$sku_id]['commodity'] = 1;
                }
                if (isset($cart['wi']['full_id']) && isset($full_wi_dis_list[$cart['wi']['full_id']][$sku_id])) {
                    $work_yh_money = $full_wi_dis_list[$cart['wi']['full_id']][$sku_id]['work_yh_money'];
                    $full_sku_flag[$sku_id]['wi'] = 1;
                }

                if (!empty($limit_sku_flag[$sku_id]['wi'])) {
                    if ($cart['pay_style'] == 3) {
                        $all_point -= $yh_money * 10;
                    } else {
                        $all_money -= $yh_money;
                    }
                    $full_dis += $yh_money;
                } else if (!empty($limit_sku_flag[$sku_id]['commodity'])) {
                    if ($cart['pay_style'] == 3) {
                        $all_point -= $work_yh_money * 10;
                    } else {
                        $all_money -= $work_yh_money;
                    }
                    $work_dis += $work_yh_money;
                } else {
                    $one_full_money = round((($cart['price'] + $cart['work_time_price']) * $cart['count']) - $yh_money - $work_yh_money, 2);
                    $cart_id_price_arr[$cart['cart_id']] = round($one_full_money/$cart['count'],2);//满减价格对应购物车ID
                    if ($cart['pay_style'] == 3) {
                        $all_point += $one_full_money * 10;
                    } else {
                        $all_money += $one_full_money;
                    }
                    $full_dis += $yh_money;
                    $work_dis += $work_yh_money;
                }
                $sku_rel_cards[$cart['cart_id']]=$full_yh_rel_card_id;


            }

            foreach ($n_dis_yh_sku as $sku_id => $cart) {
                $yh_money = 0;
                if (isset($cart['n_dis_id']) && isset($n_dis_list[$cart['n_dis_id']][$sku_id])) {
                    $yh_money = $n_dis_list[$cart['n_dis_id']][$sku_id]['yh_money'];
                    $n_dis_info = $n_dis_list[$cart['n_dis_id']][$sku_id]['n_dis_info'];
                }
                if (!empty($limit_sku_flag[$sku_id]['wi']) || !empty($full_sku_flag[$sku_id]['wi'])) {
                    if ($cart['pay_style'] == 3) {
                        $all_point -= $yh_money * 10;
                    } else {
                        $all_money -= $yh_money;
                    }
                } else {
                    $one_full_money = round((($cart['price'] + $cart['work_time_price']) * $cart['count']) - $yh_money, 3);
                    $cart_id_price_arr[$cart['cart_id']] = round($one_full_money/$cart['count'],3);//N件价格对应购物车ID
                    if ($cart['pay_style'] == 3) {
                        $all_point += $one_full_money * 10;
                    } else {
                        $all_money += $one_full_money;
                    }
                }
                $sku_rel_cards[$cart['cart_id']]=$n_dis_info['rel_card_ids'];
                $n_dis += $yh_money;
            }
        }
//        die();
//        $all_money_total =
//        print_json($sku_rel_cards);
        $card_yh_info =  $this->cart_card_bset($cart_id_arr,$cart_id_price_arr,$user_id,$channle_type, $disabled_card_sku_ids,$sku_rel_cards);
//        print_json($card_yh_info);
        $card_yh_arr = $card_yh_info['card_yh_arr'];
        $card_yh_all_money = $card_yh_info['card_yh_all_money'];
        $card_yh_all_money += $gift_card_dis;
        $all_money += $gift_card_dis;

        $cart_goods_18_n_count = count(array_unique($cart_goods_18_n));

        $params = [
            'all_point'=>($all_point-($card_yh_all_money*10))>0?($all_point-($card_yh_all_money*10)):0,
            'all_money'=>round($all_money-$card_yh_all_money,2)>0?round($all_money-$card_yh_all_money,2):0,
            'limit_dis'=>round($limit_dis,2),
            'seckill_dis'=>round($seckill_dis,2),
            'full_dis'=>round($full_dis,2),
            'n_dis'=>round($n_dis,2),
            'work_dis'=>round($work_dis,2),
            'gift_act_dis' => round($gift_act_dis, 2),
            'card_yh_arr'=>$card_yh_arr,
            'card_yh_all_money'=>round($card_yh_all_money,2),
            'all_work_time'=>round($all_work_time-$work_dis,2),
            'all_dd_pay_model'=>$all_dd_pay_model,
            'all_maintain_dis'=>$maintain_dis,//总保养套餐优惠~
            'cart_goods_18_n_count'=>$cart_goods_18_n_count,
            'all_segment_membership_dis' => $all_segment_membership_dis,
            'all_segment_owner_dis' => $all_segment_owner_dis,
        ];
//        print_json($params);
//        $params['all_money']+=$params['all_work_time'];
        return $params;
    }


    /**
     * 订单取消解冻卡券
     * @param $userId
     * @param $parentOrderCode
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function unfreezeCoupon($userId, $parentOrderCode)
    {
        $order_commodity_model = new BuOrderCommodity();
        $field = 'order_code, card_codes, is_gift,gift_card_id, parent_order_code';
        $order_commodity_list = $order_commodity_model->where('parent_order_code',$parentOrderCode)->field($field)->select();
        if (empty($order_commodity_list)) {
            return ;
        }

        // 获取订单商品表的card_codes
        $card_code_arr = [];
        $parent_order_code = '';
        foreach ($order_commodity_list as $key => $item) {
            // 赠品券订单  赠品不处理
            if (!empty($item['gift_card_id']) && $item['is_gift'] == 1) {
                continue ;
            }
            $parent_order_code = $item['parent_order_code'];
            $arr = explode(',', $item['card_codes']);
            $card_code_arr = array_unique(array_merge($card_code_arr, $arr));
        }
        if (empty($card_code_arr)) {
            return ;
        }
        $card_receive_model = new BuCardReceiveRecord();
        $net_user = new NetUser();
        $map = [
            'card_code' => ['in', $card_code_arr],
            'status'    => 5,
        ];
        $record_list = $card_receive_model->where($map)->select();
        foreach ($record_list as $key => $item) {
            $card_data = [
                'business_order_no' => $parent_order_code,
                'coupon_code'       => $item['coupon_code'],
                'card_code'         => $item['card_code'],
            ];
            $net_user->unfreezeCoupon($userId, $card_data);
        }
    }


    /**
     * 下发卡券中心结算信息
     * @param $params
     * @return array|mixed|string
     */
    public function saveSubsidy($params)
    {
        $data = [
//            'activity_product_outbound_no' => '', // 活动产品出库记录号
            'actual_subsidy_amount' => $params['sett_money'], // 补贴金额-实际,单位元
            'coupon_code' => $params['coupon_code'], // 卡券核销码
//            'e3s_settle_no' => '', // e3s结算单号
//            'flag_activity_amount_zero' => '', // 是否需要活动产品结算金额为0  1是 0否
            'flag_meet_with_subsidy_condition' => 1, // 是否满足补贴条件 1是 0否
            'mall_order_no' => $params['order_code'], // 商城订单号
//            'order_confirm_no' => '', // 订单确认记录号
//            'other_product_outbound_no' => '', // 其它产品出库记录号
//            'reservation_no' => '', // 预约单号
//            'settle_no' => '', // 结算单号(e3s用)
            'theory_subsidy_amount' => $params['sett_money'], // 补贴金额-理论,单位元
        ];
        $result = QuickWin::create('quick_win')->saveSubsidyInfo($data);
        $job_model = new DbJobsLog();
        $add = [
            'queue'       => 'push_order',
            'source_type' => 'save_subsidy',
            'data_info'   => json_encode($data),
            'result_info' => json_encode($result),
        ];
        $job_model->insert($add);

    }

}
