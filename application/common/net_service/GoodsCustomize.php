<?php

namespace app\common\net_service;

use app\common\model\bu\BuCardReceiveRecord;
use app\common\model\bu\BuCheapSuitIndex;
use app\common\model\bu\BuGwRecommendationBannerCommodity;
use app\common\model\db\DbBdpRecommend;
use app\common\model\db\DbBdpRecommendAi;
use app\common\model\db\DbBdpRecommendLog;
use app\common\model\db\DbCard;
use app\common\model\db\DbCardDraw;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbCommoditySet;
use app\common\model\db\DbCommoditySetSku;
use app\common\model\db\DbCrowdfund;
use app\common\model\db\DbCrowdfundAgreement;
use app\common\model\db\DbCrowdfundOrder;
use app\common\model\db\DbDlr;
use app\common\model\db\DbDraw;
use app\common\model\db\DbHomeSm;
use app\common\model\db\DbLimitDiscount;
use app\common\model\db\DbLimitDiscountCommodity;
use app\common\model\db\DbSeckill;
use app\common\model\db\DbSeckillCommodity;
use app\common\model\db\DbSmExpand;
use app\common\model\db\DbUserDraw;
use app\common\model\wlz\WlzCrowdsInfo;
use app\common\model\wlz\WlzCrowdsLogs;
use app\common\port\connectors\HaoWan;
use think\Collection;
use think\Db;
use think\Exception;
use think\Hook;
use think\Queue;
use tool\Logger;

/**
 * 商品解析
 */
class GoodsCustomize extends Common
{
    /**
     * @param $data
     * @return false|string
     */
    public function filerNormal($data, $pageType)
    {
        try {

            $data_json_info = json_decode($data, true);

            foreach ($data_json_info as &$item) {
                //就找第一个商品模块,要和推荐的融合
                if (in_array($item['type'], ['cGoods'])) {
                    //后台推荐的商品数据
                    if (!empty($item['attribute']['show_count'])) {
                        $item['attribute']['goods_data'] = array_slice($item['attribute']['goods_data'], 0, $item['attribute']['show_count']);
                    }
                }
            }

            # 首页只对第一备件部分商品进行筛选，业务说以后调整
            return json_encode($data_json_info);

        } catch (\Exception $e) {
            Logger::debug('get_home_data_error: ', $e->getMessage());
            return $data;
        }
    }


    /**
     *
     * 自定义数据解析
     *
     * 自定义首页 只对第一个商品模块做数据解析与融合
     * 自定义专题页 要对所有商品模块做数据解析与融合  这里注意 : 后台是否要分纯静态类页的
     * @param $vin
     * @param $data
     * @param $sheves_type
     * @param $from
     * @param $data_tj
     * @return mixed|string
     */
    public function analyze($data, $sheves_type, $data_tj, $user, $channel_type, $from = 'home', $lng = '', $lat = '', $kilometer = 0)
    {
        try {
            $data_json_info = json_decode($data, true);
            $n18            = $user['car_18n'] ?? '';

            if (!empty($data_tj['data_bdp']) || !empty($data_tj['data_ads'])) {
                $data_bdp = $data_tj['data_bdp']; // 大数据
                $data_ads = $data_tj['data_ads']; // 数据组
                $data_ads_like = $data_tj['data_ads_like']; // 数据组猜你喜欢
                foreach ($data_json_info as &$item) {
                    $final_pro = [];

                    //就找第一个商品模块,要和推荐的融合
                    if (in_array($item['type'], ['cGoods', 'cWaterfallGoods'])) {
                        //将推荐的商品放到前面
                        $data_ads_tmp  = [];
                        $data_bdp_tmp  = [];
                        $data_ads_data = json_decode($data_ads['goods'] ?? '{}', true);
                        $data_bdp_data = json_decode($data_bdp['goods'] ?? '{}', true);
                        $data_ads_like_data = $data_ads_like['goods']??[];

                        if ($user['brand'] == 1) { // 23-06版本只改日产
                            if (isset($item['attribute']['recommended_logical_type']) && in_array($item['attribute']['recommended_logical_type'],[2,4]) && !empty($item['attribute']['classify_type'])) { // 选择数据组推荐逻辑时需筛选分类
                                foreach ($data_ads_data as $v) {
                                    if ($v['commodity_type'] == $item['attribute']['classify_type']) {
                                        $data_ads_tmp[] = $v;
                                    }
                                }
                            }
                            if (isset($item['attribute']['recommended_logical_type']) && $item['attribute']['recommended_logical_type'] == 3) { // 大数据推荐
                                foreach ($data_bdp_data as $v) {
                                    $data_bdp_tmp[] = $v;
                                }
                            }
                            //加入猜你喜欢商品推荐
                            if (!empty($data_ads_like_data) && $item['attribute']['recommended_logical_type'] == 4) {
                                foreach ($data_ads_like_data as $v) {
                                    if (in_array($v['level3_id'],$item['attribute']['classify_types'])) {
                                        $data_ads_tmp[] = $v;
                                    }
                                }
                            }

                        } else {
                            //1: 读取数据组推荐, 2:读取后台配置, 3:读取大数据+数据备件推荐, 4: 猜你喜欢推荐
                            //新增了非后台配置才把推荐数据加入
                            if($item['attribute']['recommended_logical_type']!=2){
                                //将推荐的商品放到前面
                                if ($from == 'special') {
                                    if (isset($item['attribute']['recommended_logical_type']) && $item['attribute']['recommended_logical_type'] == 1 && !empty($item['attribute']['classify_type'])) { // 选择数据组推荐逻辑时需筛选分类
                                        $data_ads_data = json_decode($data_ads['goods'], true);
                                        foreach ($data_ads_data as $v) {
                                            if ($v['commodity_type'] == $item['attribute']['classify_type']) {
                                                $data_ads_tmp[] = $v;
                                            }
                                        }
                                    }
                                } else {
                                    $data_ads_tmp = $data_ads_data;
                                    $data_bdp_tmp = $data_bdp_data;
                                }
                            }

                        }

                        $data_tc   = [];
                        $data_bj   = [];
                        $data_pt   = [];
                        $have_id   = []; // 去重用
                        $goods_all = array_merge($data_bdp_tmp, $data_ads_tmp);
                        foreach ($goods_all as $vl) { // 大数据+数据组排序
                            if (in_array($vl['commodity_id'], $have_id)) continue;
                            if($item['attribute']['recommended_logical_type'] != 4){
                                if (in_array($vl['dd_commodity_type'], [1, 3, 4, 10])) { // 大数据套餐商品
                                    $data_tc[] = $vl;
                                } elseif ($vl['dd_commodity_type'] == 9) { // 大数据套餐商品
                                    $data_bj[] = $vl;
                                } else {
                                    $data_pt[] = $vl;
                                }
                            }else{
                                //猜你喜欢默认用pt
                                $data_pt[] = $vl;
                            }

                            $have_id[] = $vl['commodity_id'];
                        }

                        $data_all                = array_merge($data_tc, $data_bj);
                        $data_all                = array_merge($data_all, $data_pt);

                        $data_recommend = empty($data_all) ? '{}' : json_encode($data_all);
//                        $data_recommend = $data_all??[];
//                        print_json($data_recommend);

                        $recommend_info          = $this->getRecommend($data_recommend, $n18, $sheves_type, $channel_type);
//                        print_json($recommend_info,$data_recommend);
                        $have_id_all = []; // 去重用
                        if (!empty($recommend_info['list'])) {
                            if (isset($item['attribute']['recommend_order']) && $item['attribute']['recommend_order'] == 2) {
                                $tmp = $item['attribute']['goods_data'];
                                foreach ($recommend_info['list'] as $v) {
                                    if (in_array($v['commodity_id'], $have_id_all)) continue;
                                    $tmp[]         = $v;
                                    $have_id_all[] = $v['id'];
                                }
                            } else {
                                $tmp = $recommend_info['list'];
                                foreach ($item['attribute']['goods_data'] as $v) {
                                    if (in_array($v['id'], $have_id_all)) continue;
                                    $tmp[]         = $v;
                                    $have_id_all[] = $v['id'];
                                }
                            }
                            $final_pro                       = $tmp;
                            $item['attribute']['goods_data'] = $final_pro;
                        }

                    }
                }

            }
//            print_json(0,'',$data_json_info);
            foreach ($data_json_info as $k => &$item) { // 定向人群领券限制
                if (in_array($item['type'], ['cCoupon'])) {
                    $netCard = new NetCard();
                    if (!empty($item['attribute']['range']) && $item['attribute']['range'] == 2 && !empty($item['attribute']['cards_data'])) {

                        $card_ids = [];
                        foreach ($item['attribute']['cards_data'] as $v) {
                            $card_ids[] = $v['card_data']['card_id'] ?? 0;
                        }
                        $can_ids          = (new NetUser())->canGetCards($user, $card_ids, $channel_type);
                        $data             = [
                            'card_str_ids' => implode(',', $can_ids),
                            'channel_type' => $user['channel_type'],
                            'status'       => 0,
                            'poster_id'    => 0,
                        ];
                        $return_card_data = $netCard->getUserCard($user, $data);
                        if (empty($return_card_data['get_card_list'])) {
                            unset($data_json_info[$k]);
                            continue;
                        }
                        $return_card_data = $return_card_data['get_card_list'];
                        $returnCardIds    = array_column($return_card_data, 'id');

                        foreach ($item['attribute']['cards_data'] as $key => $v) {
                            $card_id = $v['card_data']['card_id'] ?? 0;
                            if ($card_id) {
                                $item['attribute']['cards_data'][$key]['card_data']['card_id'] = $card_id;
                            }

                            $is_received = 0;
                            if (in_array($card_id, $returnCardIds)) {
                                foreach ($return_card_data as $datum) {
                                    if ($card_id == $datum['id']) {
//                                        if ($datum['available_count'] <= 0) {
//                                            $component['attribute']['cards_data'] = [];
//                                            return $component;
//                                        }
                                        if ($datum['have_get'] > 0) {
                                            $is_received = 1;
                                        } elseif ($datum['available_quantity'] > 0 && $datum['available_count'] > 0) {
                                            $is_received = 0;
                                        }

                                        $item['attribute']['cards_data'][$key]['is_received'] = $is_received;
                                        $item['attribute']['cards_data'][$key]['vin']         = $datum['vin'];
                                        $item['attribute']['cards_data'][$key]['article']     = $datum['not_bind_article'];
                                        $item['attribute']['cards_data'][$key]['change_car']  = $datum['change_car'];
                                    }
                                }
                            } else {
                                unset($item['attribute']['cards_data'][$key]);
                            }


                            if (!in_array($card_id, $can_ids)) {
                                unset($item['attribute']['cards_data'][$key]);
                            }


                        }
                    }

                    // 旧数据处理
                    $card_ids = [];
                    foreach ($item['attribute']['cards_data'] as $key => $v) {
                        $card_ids[] = $v['card_data']['card_id'] ?? 0;
                    }
                    $data             = [
                        'card_str_ids' => implode(',', array_filter($card_ids)), // 单个卡券
                        'channel_type' => $user['channel_type'],
                        'status'       => 0,
                        'poster_id'    => 0,
                    ];
                    $return_card_data = $netCard->getUserCard($user, $data);
                    if (empty($return_card_data['get_card_list'])) {
                        unset($data_json_info[$k]);
                        continue;
                    }
                    $return_card_data = $return_card_data['get_card_list'];

                    $returnCardIds = array_column($return_card_data, 'id');

                    foreach ($item['attribute']['cards_data'] as $key => $v) {
                        $card_id = $v['card_data']['card_id'] ?? 0;
                        if ($card_id) {
                            $item['attribute']['cards_data'][$key]['card_data']['card_id'] = (string)$card_id;
                        }
                        if (empty($v['img']) || empty($v['img_got']) || empty($v['card_data'])) {
                            unset($item['attribute']['cards_data'][$key]);
                        }
                        $is_received = 0;
                        if (in_array($card_id, $returnCardIds)) {
                            foreach ($return_card_data as $datum) {
                                if ($card_id == $datum['id']) {
                                    if ($datum['have_get'] > 0) {
                                        $is_received = 1;
                                    } elseif ($datum['available_quantity'] > 0 && $datum['available_count'] > 0) {
                                        $is_received = 0;
                                    }
                                    $item['attribute']['cards_data'][$key]['is_received'] = $is_received;
                                    $item['attribute']['cards_data'][$key]['vin']         = $datum['vin'];
                                    $item['attribute']['cards_data'][$key]['article']     = $datum['not_bind_article'];
                                    $item['attribute']['cards_data'][$key]['change_car']  = $datum['change_car'];
                                }
                            }
                        } else {
                            unset($item['attribute']['cards_data'][$key]);
                        }

                    }
                    $item['attribute']['cards_data'] = array_values($item['attribute']['cards_data']);
                    if (empty($item['attribute']['cards_data'])) {
                        unset($data_json_info[$k]);
                    }
                }
            }
            $data_json_info = array_values($data_json_info);
            return $this->filterSpec($data_json_info, $user, $channel_type, $from, $recommend_info['commodity_arr'] ?? [], $lng, $lat, $kilometer);
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            Logger::debug('get_people_vin_data_error: ', $msg);
            return $data;
        }
    }

    /**
     * 和推荐无关系，只是过滤出来符合用户的数据
     *
     * @param $vin
     * @param $data
     * @return false|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function filterSpec($data_json_info, $user = [], $channel_type = '', $from = 'special', $commodity_arr = [], $lng = '', $lat = '', $kilometer = 0)
    {
        try {
            //所有的备件+保养套餐商品数据
            if (is_string($data_json_info)) {
                $data_json_info = json_decode($data_json_info, true);
            }

            $tmp                = [];
            $tmp_pbl            = [];
            $commodity_have_ids = [];
            foreach ($data_json_info as $item) {
                //先去掉 在 activity 处理了
                //                if (in_array($item['type'], ['cGoods', 'cLimit', 'cSeckill', 'cWaterfallGoods',,'cGoods', 'cWaterfallGoods'])) {
                //找到所有商品模块,首页过来的要排除第一个商品模块
                $type_redis =  redis('goods_customize_zt_type');
                $type_redis = false;
                //开关开启商品模块进行实时数据获取
                if($type_redis){
                    $filer_type = [ 'cLimit', 'cSeckill','cGoods', 'cWaterfallGoods'];
                }else{
                    $filer_type = [ 'cLimit', 'cSeckill'];
                }
                if (in_array($item['type'], $filer_type)) {
                    $show_count = $item['attribute']['show_count'] ?? 0; // 商品模块才有

                    $final_pro     = $item['attribute']['goods_data'];
                    $commodity_ids = array_column($final_pro, 'id');

                    $new_where = [];
                    if ($item['type'] == 'cLimit') {
                        $activity_ids = array_column($final_pro, 'activity_id');
                        $sku_ids      = [];
                        $sku_prices   = (new DbLimitDiscountCommodity())
                            ->where(
                                [
                                    'limit_discount_id' => ['in', $activity_ids],
                                    'commodity_id'      => ['in', $commodity_ids]
                                ]
                            )
                            ->column('sku_price');
                        foreach ($sku_prices as $sku_price) {
                            $sku_ids = array_merge($sku_ids, array_keys(json_decode($sku_price, true)));
                        }
                        $new_where['c.id'] = ['in', $sku_ids];
                    }
                    if ($item['type'] == 'cSeckill') {
                        $activity_ids = array_column($final_pro, 'activity_id');
                        $sku_ids      = [];
                        $sku_prices   = (new DbSeckillCommodity())
                            ->where(['seckill_id' => ['in', $activity_ids], 'commodity_id' => ['in', $commodity_ids]])
                            ->column('sku_price');
                        foreach ($sku_prices as $sku_price) {
                            $sku_ids = array_merge($sku_ids, array_keys(json_decode($sku_price, true)));
                        }
                        $new_where['c.id']       = ['in', $sku_ids];
                        $requestData['miao_sha'] = 1;
                    }
                    $requestData['commodity_ids'] = implode(',', $commodity_ids);
                    $requestData['car_id']        = $user['car_series_id'] ?? '';
                    $requestData['db-read']       = true;
                    $requestData['lng']           = $lng;
                    $requestData['lat']           = $lat;
                    $requestData['kilometer']     = $kilometer;
                    $requestData['pageSize']      = count($commodity_ids);
                    $requestData['miao_sha']      = 1;
                    $new_info                     = (new NetGoods())->goodsList($requestData, $user, $channel_type, $new_where, $from, $item['type']);
                    $final_return = [];
                    foreach ($commodity_ids as $commodity_id) {
                        if (!empty($show_count) && (count($final_return) == $show_count) && in_array($item['type'], ['cGoods'])) {
                            break;
                        }
                        foreach ($new_info['msg']['data'] as $item_new) {
                            if ($item_new['commodity_id'] != $commodity_id || (in_array($item['type'], ['cGoods', 'cWaterfallGoods']) && in_array($item_new['commodity_id'], $commodity_have_ids))) continue; // 整个页面商品去重
                            foreach ($final_pro as $k => $item_one) {
                                if ($item_new['commodity_id'] == $item_one['id']) {
                                    $item_one['goods_name']      = $item_new['commodity_name'];
                                    $item_one['image_path']      = $item_new['cover_image'];
                                    $item_one['price']           = $item_new['final_price'];
                                    $item_one['sales_price']     = $item_new['final_price'];
                                    $item_one['original_price']  = $item_new['price'];
                                    $item_one['tag_name']        = $item_new['tag_name'] ?? '';
                                    $item_one['tag']             = $item_new['tag'] ?? '';
                                    $item_one['tag_qcsm']        = $item_new['tag_qcsm'] ?? '';
                                    $item_one['tag_gwapp']       = $item_new['tag_gwapp'] ?? '';
                                    $item_one['tag_qcapp']       = $item_new['tag_qcapp'] ?? '';
                                    $item_one['tag_pz1asm']      = $item_new['tag_pz1asm'] ?? '';
                                    $item_one['tag_pz1aapp']     = $item_new['tag_pz1aapp'] ?? '';
                                    $item_one['commodity_label'] = $item_new['commodity_label'] ?? '';
                                    // 定向人群会员价
                                    $item_one['current_price']                  = $item_new['current_price'] ?? '';
                                    $item_one['commodity_dis_user_segment']     = $item_new['commodity_dis_user_segment'] ?? '';
                                    $item_one['commodity_dis_act_user_segment'] = $item_new['commodity_dis_act_user_segment'] ?? '';

                                    $item_one['commodity_dis_label']    = $item_new['commodity_dis_label'] ?? '';
                                    $item_one['commodity_dis_label_cn'] = $item_new['commodity_dis_label_cn'] ?? '';
                                    $item_one['pay_style']              = $item_new['pay_style'] ?? '';
                                    $item_one['count_stock']            = $item_one['stock'] = $item_new['count_stock'] ?? 0;
                                    $item_one['pay_type']               = $item_new['pay_type'] ?? '';
//                                    $item_one                   = $item_one + $item_new;
                                    $final_return[] = $item_one;

                                    if (in_array($item['type'], ['cGoods', 'cWaterfallGoods'])) {
                                        $commodity_have_ids[] = $item_one['id'];
                                    }
                                    break;
                                }
                            }
                        }

                    }

                    if (!empty($show_count) && (count($final_return) > $show_count) && in_array($item['type'], ['cGoods'])) {
                        $final_return = array_slice($final_return, 0, $show_count);
                    }

                    if (!empty($final_return)) {
                        $item['attribute']['goods_data'] = array_values($final_return);
                        if ($item['type'] == 'cWaterfallGoods') {
                            $tmp_pbl = $item;
                        } else {
                            $tmp[] = $item;
                        }
                    }
                } else {
                    $tmp[] = $item;
                }
            }

            if (!empty($tmp_pbl)) {
                $tmp[] = $tmp_pbl;
            }

            $data_json_info = $tmp;
            return json_encode($data_json_info);
        } catch (\Exception $e) {
            Logger::debug('get_special_people_vin_data_error: ', $e->getMessage() . ' Line:' . $e->getLine() . ' File:' . $e->getFile());
            if (is_array($data_json_info)) {
                return json_encode($data_json_info);
            } else {
                return $data_json_info;
            }

        }
    }

    /**
     * goods_data: [
     * goods_name: "ROYDX莱德斯时尚水纹直身杯B"  //商品名称
     * id: 3619   //商品id
     * image_path: "http://wx-dealer.oss-cn-shenzhen.aliyuncs.com/commodity/20201030/44fbbb1f215f399ed7e49e2336076dcf.jpg"  //商品展示图片地址
     * one: "" //一级分类
     * original_price: "278.00"  //商品原价
     * price: "278.00"          //商品现价
     * sales_price: "278.00"    //同上
     * special: ""    //商品亮点
     * tag_name: "新品"   //商品标签
     * three: "生活家居" //三级分类
     * two: "品质生活"   //二级分类
     * pay_style: 1 // 1现金+积分 2现金 3积分
     * max_point: 10 // 最大可用积分,0为不限制
     * ]
     *
     * dd_commodity_type 是 9
     *
     * @param $vin
     * @return void
     */
    public function getRecommend($data_bdp, $n18, $pageType, $channel_type)
    {
        //获取所有推荐的商品信息

        if (is_array($data_bdp)) {
            $bdp_commodity_arr = $data_bdp;
        } else {
            $data_goods_info   = json_decode($data_bdp, true);
            $bdp_commodity_arr = array_column($data_goods_info, 'commodity_id');
        }

        $this->channel_type = $channel_type;
        if (empty($bdp_commodity_arr)) {
            return false;
        }

        $bdp_key = md5(serialize($bdp_commodity_arr));

        $tag = 'a.tag';
        if ($this->channel_type == 'GWAPP') {
            $tag = 'a.tag_gwapp';
        } else if ($this->channel_type == 'QCAPP') {
            $tag = 'a.tag_qcapp';
        } else if ($this->channel_type == 'QCSM') {
            $tag = 'a.tag_qcsm';
        }

        $cache_rec = 'cache_rec2' . $n18 . $pageType . $tag . $bdp_key;
        $list      = redis($cache_rec);

        if (empty($list) || getRedisLock($cache_rec . '-lock', mt_rand(40, 60))) {
            $g_where['a.commodity_id'] = ['in', $bdp_commodity_arr];
            $g_where['a.shelves_type'] = $pageType;#过滤出来符合对应渠道的商品数据
            $field                     = "a.commodity_id as id,a.commodity_name as goods_name,a.cover_image as image_path,a.price as sales_price,a.final_price as price,a.price as original_price,b.pay_style,b.max_point,a.dd_commodity_type,a.is_grouped," . $tag;

            $params = array(
                'where'    => $g_where,
                'field'    => $field,
                'group'    => 'a.commodity_id ',
                'pagesize' => count($bdp_commodity_arr),
            );

            $flat = new DbCommodityFlat();
            $list = $flat->getCommodityList($params);
//            print_json($flat->getLastSql(),$list);
            redis($cache_rec, $list, 200);
        }

        if (empty(count($list))) {
            return false;
        }

        $return = [];
        foreach ($bdp_commodity_arr as $one) {
            foreach ($list as $value) {
                if ($value['id'] == $one) {
                    $return[] = $value;
                    break;
                }
            }
        }

        return ['commodity_arr' => $bdp_commodity_arr, 'list' => collection($return)->toArray()];
    }

    /**
     * 所有到店商品id
     * @return array|false|mixed|string
     */
    public function allDD($sheves_type)
    {
        $key       = config('cache_prefix.all_dd_commodity') . $sheves_type;
        $data_info = redis($key) ?? '';
        if (empty($data_json) || getRedisLock($key . '-lock', mt_rand(60, 800))) {
            // 备件 + 组合商品
            $data_info9 = (new DbCommodityFlat())->where('shelves_type', $sheves_type)->where('dd_commodity_type', 9)->column('commodity_id');
            $data_infog = (new DbCommodityFlat())->where('shelves_type', $sheves_type)->where('is_grouped', 1)->column('commodity_id');
            $data_info  = array_merge($data_info9, $data_infog);
            redis($key, $data_info, 1000);
        }
        return $data_info;
    }


    private function getLowPrice($data_item, $n18)
    {
        $set_sku_redis_key = 'set_sku_redis_key_cid_18n' . $data_item['id'] . $n18;
        $low_price         = redis($set_sku_redis_key) ?? 0;

        if (empty($low_price) || getRedisLock($set_sku_redis_key . '-lock', mt_rand(80, 110))) {


            //普通备件商品
            if (empty($data_item['is_grouped'])) {

                $low_price = (new DbCommoditySetSku())->getOne(
                    [
                        'where'   => [
                            'commodity_id'   => $data_item['id'],
                            'relate_car_18n' => ['like', "%{$n18}%"],
                        ],
                        'order'   => 'price asc',
                        'db-read' => true
                    ]
                );

                $low_price = $low_price['price'] ?? 0;
            } else {
                //组合商品，调用忠哥接口获得接口
                $net_goods  = new NetGoods();
                $group_info = $net_goods->getGroupCommodityPrice($data_item['id'], $n18);
                $low_price  = $group_info['sum_min_money'] ?? '';
            }

            redis($set_sku_redis_key, $low_price, 120);
        }

        return ['price' => number_format($low_price, 2)];
    }

    /**
     * bdp 在同步过来数据（更新）后，调用方法将缓存清理掉,清理所有渠道
     * @param $vin
     * @return bool
     */
    public function cacheClearAi($vin)
    {
        $bdp   = new DbBdpRecommendAi();
        $where = ['vin' => $vin];
        //$data  = $bdp->getOne(['where' => $where, 'db-read' => true]);
        $data = $bdp->getOne(['where' => $where]);

        if (!empty($data)) {

            $all_page = DbHomeSm::getPageTypes();

            //清理，这个vin对应的几个首页数据缓存
            foreach ($all_page as $page_item) {
                $this->_clear('cache_prefix.more_people_more_home', $vin . $page_item); #首页缓存
            }

            $this->_clear('cache_prefix.special', $vin, 'cache_prefix.more_people_more_sp_set', $vin); #专题页缓存
        }

        return true;
    }

    /**
     * bdp 在同步过来数据（更新）后，调用方法将缓存清理掉,清理所有渠道
     * @param $vin
     * @return bool
     */
    public function cacheClear($vin)
    {
        $bdp   = new DbBdpRecommend();
        $where = ['vin' => $vin];
        $data  = $bdp->getOne(['where' => $where, 'db-read' => true]);

        if (!empty($data)) {

            $all_page = DbHomeSm::getPageTypes();

            //清理，这个vin对应的几个首页数据缓存
            foreach ($all_page as $page_item) {
                $this->_clear('cache_prefix.more_people_more_home', $vin . $page_item); #首页缓存
            }

            $this->_clear('cache_prefix.special', $vin, 'cache_prefix.more_people_more_sp_set', $vin); #专题页缓存
        }

        return true;
    }


    public function newbdpCacheClear($vin, $data_goods)
    {
        $bdp                  = (new DbBdpRecommend())->getOne(['where' => ['vin' => $vin]]);
        $dbCommoditySetSkuObj = new DbCommoditySetSku();
        $goods                = [];
        foreach ($data_goods as $k => $data_item) {
            if (empty($data_item['goods'])) continue;
            $goods_tmp = [];
            foreach ($data_item['goods'] as $gooditem) {
                if (empty($gooditem['skuList'])) continue;
                $sku_arr        = [];
                $default_arr    = [];
                $no_default_arr = [];
                foreach ($gooditem['skuList'] as $skuitem) {
                    if ($skuitem['isDefault'] == 1) {
                        $default_arr[] = $skuitem['setSkuId'];
                        array_unshift($sku_arr, $skuitem['setSkuId']);
                    } else {
                        $no_default_arr[] = $skuitem['setSkuId'];
                        $sku_arr[]        = $skuitem['setSkuId'];
                    }
                }

                $goods_tmp['dd_commodity_type'] = $gooditem['ddCommodityType'];
                $goods_tmp['commodity_id']      = $gooditem['commodityId'];
//                $goods_tmp['set_sku_id'] = implode(',',$sku_arr);
                if ($default_arr) {
                    $setskulist     = $dbCommoditySetSkuObj->whereIn('id', $default_arr)->order("price asc")->select();
                    $tmp_sku_id_arr = [];
                    foreach ($setskulist as $skuitem) {
                        $tmp_sku_id_arr[] = $skuitem['id'];
                    }
                    $default_arr = $tmp_sku_id_arr;
                }
                if ($no_default_arr) {
                    $setskulist     = $dbCommoditySetSkuObj->whereIn('id', $no_default_arr)->order("price asc")->select();
                    $tmp_sku_id_arr = [];
                    foreach ($setskulist as $skuitem) {
                        $tmp_sku_id_arr[] = $skuitem['id'];
                    }
                    $no_default_arr = $tmp_sku_id_arr;
                }
                $goods_sku_ids           = array_merge($default_arr, $no_default_arr);
                $goods_tmp['set_sku_id'] = implode(',', $goods_sku_ids);

//                if(!empty($goods_tmp['set_sku_id'])){
//                   $setskulist =  $dbCommoditySetSkuObj->whereIn('id',$goods_tmp['set_sku_id'])->order("price asc")->select();
//                   $tmp_sku_id_arr = [];
//                   foreach($setskulist as $skuitem){
//                       $tmp_sku_id_arr[] = $skuitem['id'];
//                   }
//                   $goods_tmp['set_sku_id'] = implode(',',$tmp_sku_id_arr);
//                }
                $goods[] = $goods_tmp;
            }
        }


        if (empty($bdp)) {
            DbBdpRecommend::create(['vin' => $vin, 'goods' => json_encode($goods)]);
        } else {
            DbBdpRecommend::where('vin', $vin)->update(['goods' => json_encode($goods), 'last_updated_date' => date('Y-m-d H:i:s')]);
        }
        return true;
    }

    public function bdpaiCacheClear($vin, $res = [], $data = [])
    {
        //实时请求的数据
        if (isset($res['code']) && $res['code'] == 0) {
            if (!isset($res['data'])) {
                return false;
            }
            $data_goods = $res['data'];
            try {
                $DbBdpRecommendAi = new DbBdpRecommendAi();
                if (!empty($data_goods)) {
                    $this->newbdpCacheClear($vin, $data_goods);
                    $str = json_encode($data_goods);
//                    $bdp = (new DbBdpRecommendAi())->getOne(['where' => ['vin' => $vin], 'db-read' => true]);
                    $bdp = (new DbBdpRecommendAi())->getOne(['where' => ['vin' => $vin]]);
                    if (empty($bdp)) {
                        DbBdpRecommendAi::create(['vin' => $vin, 'goods' => $str]);
                    } else {
                        DbBdpRecommendAi::where('vin', $vin)->update(['goods' => $str, 'last_updated_date' => date('Y-m-d H:i:s')]);

                    }
                    //  $this->cacheClearAi($vin);
                }
            } catch (\Exception $e) {
                Logger::error('bdpaierror:' . $e->getMessage());
                return false;
            }

            return true;
        } else {
            //推送过来的数据
            // $this->cacheClearAi($data['vin']);
            return true;
        }
        return false;
    }

    public function bdpCacheClear($res = [], $data = [])
    {
        //实时请求的数据
        if (!empty($res['data']['issueList'])) {

            $data_goods = $res['data']['issueList'];
            $goods      = [];

            try {
                foreach ($data_goods as $data_good) {
                    if (empty($data_good['ddCommodityType'])) {
                        continue;
                    }
                    $set_sku_ids = array_column($data_good['skuList'], 'setSkuId');
                    if (empty($set_sku_ids)) {
                        continue;
                    }
                    $goods[] = [
                        'dd_commodity_type' => $data_good['ddCommodityType'],
                        'commodity_id'      => $data_good['commodityId'],
                        'set_sku_id'        => implode(',', $set_sku_ids),
                    ];
                }

                if (!empty($goods)) {
                    $str = json_encode($goods);
                    //$bdp = (new DbBdpRecommend())->getOne(['where' => ['vin' => $data['vin']], 'db-read' => true]);
                    $bdp = (new DbBdpRecommend())->getOne(['where' => ['vin' => $data['vin']]]);
                    if (empty($bdp)) {
                        DbBdpRecommend::create(['vin' => $data['vin'], 'goods' => $str]);
                    } else {
                        DbBdpRecommend::where('vin', $data['vin'])->update(['goods' => $str]);
                    }
                    $goods_log  = ['goods' => $goods, 'date_time' => date('Y-m-d H:i:s')];
                    $log_date   = date('Ym');
                    $bdp_log    = (new DbBdpRecommendLog())->getOne(['where' => ['vin' => $data['vin'], 'log_date' => $log_date]]);
                    $msg_list   = json_decode($bdp_log['msg_list'], true) ?? [];
                    $msg_list[] = $goods_log;
                    if (empty($bdp_log)) {
                        DbBdpRecommendLog::create(['vin' => $data['vin'], 'log_date' => $log_date, 'msg_list' => json_encode($msg_list)]);
                    } else {
                        DbBdpRecommendLog::where('id', $bdp_log['id'])->update(['msg_list' => json_encode($msg_list)]);
                    }
                    $this->cacheClear($data['vin']);
//                    Queue::push('app\common\queue\BdpGoodsLog', json_encode(['vin' => $data['vin'], 'goods' => $goods]), config('queue_type.after_sale'));
                }
            } catch (\Exception $e) {
                Logger::error('bdp error:' . $e->getMessage());
                return false;
            }

            return true;
        } else {
            //推送过来的数据
            $this->cacheClear($data['vin']);
            return true;
        }
        return false;
    }


//$live_param = [
//'key' => 'cache_prefix.special', 'suffix' => $id,
//'set' => 'cache_prefix.more_people_more_sp_set', 'set_suffix' => $id];
//Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $live_param);

    private function _clear($key, $suffix, $set = '', $set_suffix = '')
    {
        $params = ['key' => $key, 'suffix' => $suffix, 'set' => $set, 'set_suffix' => $set_suffix];
        Hook::exec('app\\net_small\\behavior\\CacheClear', 'run', $params);
        return true;
    }

    public function activity($data_json, $user, $channel_type = '', $lng = '', $lat = '', $kilometer = 0, $from = 'home', $data_tj = [])
    {
        $data        = json_decode($data_json, true) ?? [];
        $return_data = [];
        $info_user   = new WlzCrowdsLogs();
        $ads_like = $data_tj['data_ads_like'] ?? []; // 数据组推荐商品
        $banner_commodity_list = []; // 后台设置banner
        if (!empty($ads_like)) {
            $have_ads =0;
            foreach ($data as $k => $v_be) {
                if ($v_be['type'] == 'cCarousel') {
                    $attribute_be = $v_be['attribute'];
                    $recommended_logical_types_be = $attribute_be['recommended_logical_types'] ?? [];
                    if (!empty($recommended_logical_types_be)) {
                        foreach ($recommended_logical_types_be as  $logical_type_be) {
                            if($logical_type_be==4){
                                $have_ads=1;
                                break;
                            }
                        }
                    }
                }
            }
            if($have_ads==1){
                $commodityIdArr = array_column($ads_like['goods'], 'commodity_id');
                $commodityIds = implode(',', $commodityIdArr);


                $requestData = [
                    'pageSize' => count($commodityIdArr),
                    'order_by' => "field(a.commodity_id, $commodityIds)",
                    'commodity_ids' => $commodityIds,
                    'user' => $user['id'],
                ];
                $cache_key = 'tmp_goods_list_ads:'.md5(implode("_", $requestData));
                $goodsListResult = redis($cache_key);
                if (empty($goodsListResult)) {
                    $goodsListResult = (new NetGoods())->goodsList($requestData, $user, $channel_type, [], 'home', 'cGoods');
                    redis($cache_key, $goodsListResult, mt_rand(300, 600));
                }


                if (!empty($goodsListResult['msg']['data'])) {
                    $commodityIdArr = array_column($goodsListResult['msg']['data'], 'commodity_id');
                    $condition              = 'a.banner_id=b.id and b.start_date <="' . date('Y-m-d H:i:s') . '" and (end_type = 1 or end_date > "' . date('Y-m-d H:i:s') . '" ) and b.is_enable=1';
                    $field                  = 'a.commodity_id,cover_image,image_type,a.is_enable';
                    $banner_commodity_model = new BuGwRecommendationBannerCommodity();
                    $banner_map             = ['a.is_enable' => 1, 'a.commodity_id' => ['in', $commodityIdArr]];

                    $banner_commodity_list = $banner_commodity_model->alias('a')
                        ->join('t_bu_gw_recommendation_banner b', $condition)
                        ->where($banner_map)
                        ->field($field)
                        ->order("field(a.commodity_id, $commodityIds)")
                        ->group('commodity_id,image_type')
                        ->select();
                }
            }

        }

//        $userGroupCrowdIds = $this->getUserGroups($user);
        $goods_aids = [];
        $goods_new_msg = [];
        foreach ($data as $kk=>$vv){
            $attribute = $vv['attribute'];
            if (in_array($vv['type'], ['cGoods', 'cWaterfallGoods'])) {
                $goods_list = $attribute['goods_data'];
                $commodity_one_ids = array_column($goods_list, 'id');
                if($goods_aids){
                    $goods_aids =  array_merge($goods_aids,$commodity_one_ids);
                }else{
                    $goods_aids = $commodity_one_ids;
                }
            }
        }

        $requestData   = [
            'commodity_ids' => implode(',', $goods_aids),
            'db-read'       => true,
            'lng'           => $lng,
            'lat'           => $lat,
            'kilometer'     => $kilometer,
            'pageSize'      => count($goods_aids),
            'miao_sha'      => 1,
            'user' => $user['id'],
        ];
        //不准，会有遗漏
        if($user['id'] && !empty($goods_aids)){
            $cache_key = 'tmp_goods_list:'.md5(implode("_", $requestData));
            $new_info = redis($cache_key);
            if (empty($new_info)) {
                $new_info = (new NetGoods())->goodsList($requestData, $user, $channel_type);
                redis($cache_key, $new_info, mt_rand(300, 600));
            }
            if (!empty($new_info['msg']['data'])) {
                foreach ($new_info['msg']['data'] as $item_new) {
                    $goods_new_msg[$item_new['commodity_id']] = $item_new;
                }
            }
            unset($new_info);
        }

        // floatingWindow
        foreach ($data as $k => $v) {

            if ($from == 'home') {
                $array = ['cGoods', 'cLottery', 'cSeckill', 'cCarousel', 'cAdvertising2', 'cAdvertising1', 'cAdvertising', 'cTabs', 'cSuit', 'cWaterfallGoods', 'cLimit', 'cCoupon', 'cCrowdfunding', 'cFloatWindow', 'cFloatingWindow'];

            } else {
                $array = ['cGoods', 'cLottery', 'cSeckill', 'cCarousel', 'cAdvertising2', 'cAdvertising1', 'cAdvertising', 'cSuit', 'cWaterfallGoods', 'cLimit', 'cCoupon', 'cCrowdfunding', 'cFloatWindow', 'cFloatingWindow','cCouponAndGoods'];

            }
            if (!in_array($v['type'], $array)) {
                $return_data[] = $v;
                continue;
            }

            $attribute = $v['attribute'];

            if ($v['type'] == 'cLimit') {
                $goods_data = $attribute['goods_data'];
                foreach ($goods_data as $ky => $vl) {
                    $activity_now = $this->check_activity($v['type'], $vl['activity_id']);
                    if (empty($activity_now)) {
                        unset($goods_data[$ky]);
                        continue;
                    }
                    $goods_data[$ky]['activity'] = [
                        'id'         => $activity_now['id'] ?? '',
                        'title'      => $activity_now['title'] ?? '',
                        'start_time' => $activity_now['start_time'] ?? '',
                        'end_time'   => $activity_now['end_time'] ?? '',
                    ];
                }
                if (!empty($goods_data)) {
                    $v['attribute']['goods_data'] = array_values($goods_data);
                    $return_data[]                = $v;
                }
                continue;
            }

            if ((in_array($v['type'], ['cGoods', 'cWaterfallGoods']) && empty($attribute['goods_data'])) || ($v['type'] == 'cSuit' && empty($attribute['list']))) { // 这几个组件没有商品就不返回
                continue;
            }

            if (in_array($v['type'], ['cCoupon', 'cCouponAndGoods'])) {
                $param         = [
                    'lng'       => $lng,
                    'lat'       => $lat,
                    'kilometer' => $kilometer,
                ];
                $re            = $this->componentCoupon($v, $param, $user, $channel_type);
                $return_data[] = $re;

            }


            // 浮窗
            if (in_array($v['type'], ['cFloatWindow', 'cFloatingWindow'])) {
                // 类型
                if ($attribute['type'] == 1) {
                    $cardId = $attribute['cards_data'][0]['card_data']['card_id'];

                    // 判断用户是否可以获取卡券
                    $card_arr = $this->canGetCards($user, [$cardId], $this->channel_type);
                    if (!in_array($cardId, $card_arr)) {
                        unset($data[$k]);
                        continue;
                    }

                    // 单张优惠券
                    $params      = [
                        'card_str_ids' => $cardId,
                        'channel_type' => $user['channel_type'],
                        'status'       => 0,
                        'poster_id'    => 0,
                    ];
                    $netCard     = new NetCard();
                    $card_data   = $netCard->getUserCard($user, $params, $v['type']);
                    $is_received = 0;
                    if (!empty($card_data['get_card_list'])) {
                        $card_data = $card_data['get_card_list'];
                        if ($card_data[0]['available_count'] <= 0) {
                            unset($data[$k]);
                            continue;
                        }
                        if ($card_data[0]['have_get'] > 0) {
                            $is_received = 1;
                        } elseif ($card_data[0]['available_quantity'] > 0 && $card_data[0]['available_count'] > 0) {
                            $is_received = 0;
                        }
                        $data[$k]['attribute']['cards_data'][0]['card_data']['card_id'] = (string)$cardId;
                        $data[$k]['attribute']['cards_data'][0]['is_received']          = $is_received;
                        $data[$k]['attribute']['cards_data'][0]['vin']                  = $card_data[0]['vin'];
                        $data[$k]['attribute']['cards_data'][0]['article']              = $card_data[0]['not_bind_article'];
                        $data[$k]['attribute']['cards_data'][0]['change_car']           = $card_data[0]['change_car'];

                    } else {
                        unset($data[$k]);
                        continue;
                    }

                }

                $return_data[] = $data[$k];

            }

            if (in_array($v['type'], ['cGoods', 'cWaterfallGoods'])) {
                $goods_list = $attribute['goods_data'];
                if (isset($attribute['show_time'])) {
                    if (time() < strtotime($attribute['show_time']['start_time'])) {
                        unset($data[$k]);
                        continue;
                    }
                    if (!empty($attribute['show_time']['end_time'])) {
                        if (time() > strtotime($attribute['show_time']['end_time'])) {
                            unset($data[$k]);
                            continue;
                        }
                    }
                }
                $nnn_info=1;//往下走
//                if (!empty($user['id'])) {
                if ($nnn_info) {
                    //防止上面不准
                    $commodity_ids = array_column($goods_list, 'id');
                    $is_ss_datas = redis('home-sp-a-goods-list-gc-act');
                    $requestData   = [
                        'commodity_ids' => implode(',', $commodity_ids),
                        'db-read'       => true,
                        'lng'           => $lng,
                        'lat'           => $lat,
                        'kilometer'     => $kilometer,
                        'pageSize'      => count($commodity_ids),
                        'miao_sha'      => 1,
                    ];
//                    if(!$is_ss_datas){
//                        if($user['id']){
//                            $new_info = (new NetGoods())->goodsList($requestData, $user, $channel_type
//                            );
//                            $goods_new_msg = [];
//                            foreach ($new_info['msg']['data'] as $item_new) {
//                                $goods_new_msg[$item_new['commodity_id']] = $item_new;
//                            }
//                        }
//                    }


                    if (empty($nnn_info)) {
                        unset($data[$k]);
                        continue;
                    } else {

                        $final_return = [];
                        foreach ($goods_list as $key => $item_one) {
                            if (!isset($goods_new_msg[$item_one['id']])){
                                if($user['id']) {
                                    continue ;
                                }
                                $item_one['price'] = $item_one['sales_price'] =  formatNumber($item_one['price']);

                            }else{
                                $item_new                    = $goods_new_msg[$item_one['id']];
                                $item_one['image_path']     = $item_new['cover_image'];
                                $item_one['goods_name']     = $item_new['commodity_name'];
                                $item_one['price']          = $item_new['final_price'];
                                $item_one['sales_price']    = $item_new['final_price'];
                                $item_one['original_price'] = $item_new['price'];
                                $item_one['tag_name']       = $item_new['tag_name'] ?? '';
                                $item_one['tag']             = $item_new['tag'] ?? '';
                                $item_one['tag_qcsm']        = $item_new['tag_qcsm'] ?? '';
                                $item_one['tag_gwapp']       = $item_new['tag_gwapp'] ?? '';
                                $item_one['tag_qcapp']       = $item_new['tag_qcapp'] ?? '';
                                $item_one['tag_pz1asm']      = $item_new['tag_pz1asm'] ?? '';
                                $item_one['tag_pz1aapp']     = $item_new['tag_pz1aapp'] ?? '';
                                $item_one['commodity_label'] = $item_new['commodity_label'] ?? '';
                                // 定向人群会员价
                                $item_one['current_price']                  = $item_new['current_price'] ?? '';
                                $item_one['commodity_dis_user_segment']     = $item_new['commodity_dis_user_segment'] ?? '';
                                $item_one['commodity_dis_act_user_segment'] = $item_new['commodity_dis_act_user_segment'] ?? '';
                                $item_one['commodity_dis_label']            = $item_new['commodity_dis_label'] ?? '';
                                $item_one['commodity_dis_label_cn']         = $item_new['commodity_dis_label_cn'] ?? '';
                            }

                            $final_return[]                             = $item_one;
                        }
//                        $goods_data = array_values($v['attribute']['goods_data']);
                        // 获取指定数量商品
                        if (!empty($attribute['show_count'])) {
                            $final_return = array_slice($final_return, 0, $attribute['show_count']);
                        }

                        $v['attribute']['goods_data'] = $final_return;
                    }
                }
                if ($v['type'] == 'cWaterfallGoods') {
                    $final_return = $v['attribute']['goods_data'];
                    $redis_key    = 'cWaterfallGoods' . $user['member_id'] . $user['vin'] . $user['sm_type'] . '-' . $user['set_id']; // 缓存瀑布流商品
                    redis($redis_key, json_encode($final_return), 3600);
                    $v['attribute']['total']        = count($final_return);
                    $v['attribute']['per_page']     = 12;
                    $v['attribute']['current_page'] = 1;
                    $v['attribute']['all_page']     = ceil(count($final_return) / 12);
                    $v['attribute']['goods_data']   = array_slice($final_return, 0, 12);
                }
                $return_data[] = $v;
            }


            if ($v['type'] == 'cLottery') {
                if (empty($attribute['activity'])) continue;
                $game = (new DbDraw())->getOne(['where' => ['id' => $attribute['activity']['id']]]);
                if ($game['end_time'] < date('Y-m-d H:i:s') || $game['is_enable'] == 0) {
                    unset($data[$k]);
                    continue;
                }
                $data[$k]['attribute']['activity']['game_id']    = $game['game_id'];
                $data[$k]['attribute']['activity']['draw_url']   = $game['draw_url'];
                $data[$k]['attribute']['activity']['start_time'] = $game['start_time'];
                $data[$k]['attribute']['activity']['end_time']   = $game['end_time'];
                if (empty($user) || empty($attribute['activity']['id'])) {
                    $draw_num = 0;
                } else {
                    # 查询抽奖次数
                    $re = HaoWan::create('hao_wan')->getLotteryNum($user['member_id'], $game['game_id']);
                    if ($re->isSuccess()) {
                        $draw_num = $re->getData()['times'];
                    } else {
                        $draw_num = 0;
                    }
                    $data[$k]['attribute']['activity']['game_id']    = $game['game_id'];
                    $data[$k]['attribute']['activity']['draw_url']   = $game['draw_url'];
                    $data[$k]['attribute']['activity']['start_time'] = $game['start_time'];
                    $data[$k]['attribute']['activity']['end_time']   = $game['end_time'];
                    if (empty($user) || empty($attribute['activity']['id'])) {
                        $draw_num = 0;
                    } else {
                        # 查询抽奖次数
                        $re = HaoWan::create('hao_wan')->getLotteryNum($user['member_id'], $game['game_id']);
                        if ($re->isSuccess()) {
                            $draw_num = $re->getData()['times'];
                        } else {
                            $draw_num = 0;
                        }
                    }
                    $data[$k]['attribute']['activity']['draw_num'] = $draw_num ?? 0;
                    $return_data[]                                 = $data[$k];
                }
            }

            //秒杀活动
            if ($v['type'] == 'cSeckill') {

                $activity        = $attribute['activity'];
                $seckill_service = new Seckill();
                if (!empty($activity)) {
                    $new_activity                      = $seckill_service->getSeckillTime($attribute['activity']['id']);
                    $data[$k]['attribute']['activity'] = $new_activity;
                }

                $final_pro = $attribute['goods_data'];
                if (!empty($final_pro)) {
                    foreach ($final_pro as $final_key => $final) {
//                        $activity = (new DbSeckill())->getOne(['where' => ['id' => $final['activity_id']]]);
                        $activity_new                      = $seckill_service->getSeckillTime($final['activity_id']);
                        $final_pro[$final_key]['activity'] = $activity_new;
                        $end_time                          = $activity_new['end_time'];
                        // 重复秒杀
//                        if ($activity['seckill_type'] == 2) {
//                            $end_time = date('Y-m-d ',strtotime($activity['end_time'])).$activity['day_end_time'];
//                        }
                        if ($end_time < date('Y-m-d H:i:s') || $activity_new['is_enable'] == 0 || !in_array($activity_new['act_status'], [1, 2])) {
                            unset($final_pro[$final_key]);
                        }
                    }
                    if (empty($final_pro)) continue;
                    $commodity_ids = array_column($final_pro, 'id');
                    $new_info      = (new NetGoods())->goodsList(
                        [
                            'miao_sha'      => 1,
                            'commodity_ids' => implode(',', $commodity_ids),
                            'db-read'       => true,
                            'lng'           => $lng,
                            'lat'           => $lat,
                            'kilometer'     => $kilometer,
                            'pageSize'      => count($commodity_ids),
                        ], $user, $channel_type
                    );
                    if ($new_info['code'] == 200) {
                        Logger::error('home-seckill-goods', ['goods_data' => $data[$k]['attribute']['goods_data'], 'data' => $data[$k]]);
                        if (!empty($new_info['msg']['data'])) {
                            $count        = count($new_info['msg']['data']);
                            $seckill_good = [];
                            foreach ($new_info['msg']['data'] as $key => $val) {
                                if ($val['is_pp'] == 1) {
                                    $data[$k]['attribute']['goods_data'][$key]['tag_name']        = $val['tag_name'];
                                    $data[$k]['attribute']['goods_data'][$key]['commodity_label'] = $val['commodity_label'];
                                    if (!isset($val['limited_stock'])) {
                                        $limited_stock = 0;
                                    } else {
                                        $limited_stock = $val['limited_stock'];
                                    }
                                    foreach ($final_pro as $i => $final) {
                                        if ($final_pro[$i]['commodity_id'] == $val['commodity_id']) {
                                            $seckill_good[$i]                  = $final_pro[$i];
                                            $seckill_good[$i]['price']         = $val['final_price'];
                                            $seckill_good[$i]['stock']         = $val['count_stock'];
                                            $seckill_good[$i]['limited_stock'] = $limited_stock ?? 0;
                                            break;
                                        }
                                    }
                                }
                            }
                            $final_return_tmp1 = [];
                            $final_return_tmp2 = [];
                            foreach ($seckill_good as $final) {
                                if ($final['stock'] == 0) {
                                    $final_return_tmp2[] = $final;
                                } else {
                                    $final_return_tmp1[] = $final;
                                }
                            }
                            $seckill_good                        = array_merge($final_return_tmp1, $final_return_tmp2);
                            $data[$k]['attribute']['goods_data'] = array_values($seckill_good);
                        } else {
                            $seckill_model = new DbSeckill();
                            foreach ($v['attribute']['goods_data'] as $kkk => $vv) {
                                // 获取秒杀类型
                                $field        = 'id,seckill_type,day_start_time,day_end_time';
                                $seckill_info = $seckill_model->where('id', $vv['activity_id'])->field($field)->find();
                                $screening    = '';
                                if ($seckill_info['seckill_type'] == 2) {
                                    $time = date('H:i:s');
                                    if ($time > $seckill_info['day_end_time']) {
                                        // 明天场次
                                        $screening = date("Y-m-d", strtotime("+1 day"));
                                    } else {
                                        // 当天场次
                                        $screening = date('Y-m-d');
                                    }
                                }
                                $data[$k]['attribute']['goods_data'][$kkk]['limited_stock'] = $this->kill_count($vv['activity_id'], $vv['id'], $screening);
                                $data[$k]['attribute']['goods_data'][$kkk]['stock']         = $this->kill_count($vv['activity_id'], $vv['id'], $screening);
                            }
                        }
                    }

                    $data[$k]['ap_time'] = date('Y-m-d H:i:s');
                    $return_data[]       = $data[$k];
                }
            }

            //轮播广告
            if ($v['type'] == 'cCarousel') {

                $currentTime       = time();

                // recommended_logical_types 推荐逻辑
                $styleTemplate = $attribute['styleTemplate'] ?? 0; // // 1: 常规轮播广告,  2: 组合轮播广告
                $recommended_logical_types = $attribute['recommended_logical_types'] ?? [];


                if (isset($attribute['imgs'])) {

                    $img = [];

                    if (!empty($recommended_logical_types) && $styleTemplate == 1) {
                        foreach ($recommended_logical_types as $logical_index => $logical_type) {
                            // 每个轮播图位置的推荐逻辑. 2:仅后台配置 4: 猜你喜欢推荐
                            if ($logical_type == 2) {
                                // 获取同一个位置的数据
                                foreach ($attribute['imgs'] as $imageIndex => $imageList) {
                                    $img_val      = [];
                                    $img_val_sort = [];
                                    if ($logical_index == $imageIndex) {
                                        // 获取同一个位置的数据
                                        foreach ($imageList as $image_index => $image) {
                                            if (!empty($image['show_time']['start_time']) && $currentTime < strtotime($image['show_time']['start_time'])) {
                                                continue;
                                            }
                                            if (!empty($image['show_time']['end_time']) && $currentTime > strtotime($image['show_time']['end_time'])) {
                                                continue;
                                            }
                                            if (!empty($image['userGroups'])) {
                                                $info_count = $info_user->where(
                                                    [
                                                        'crowd_id' => array('in', $image['userGroups']),
                                                        'user_id'  => $user['id'] ?? 0,
                                                    ]
                                                )->count();

                                                if ($info_count == 0) {

                                                    continue;
                                                }
                                                $img_val_sort[] = $image_index + 1;
                                            } else {
                                                $img_val_sort[] = $image_index + 999;
                                            }
                                            $img_val[] = $image;
                                        }
                                        array_multisort($img_val_sort, SORT_ASC, $img_val);
                                        if (in_array($channel_type, ['GWAPP', 'GWSM'])) {
                                            if (isset($img_val[0])) {
                                                $img[] = $img_val[0];
                                            }
                                        } else {
                                            $img = $img_val;
                                        }

                                        if (!$img) {
                                            unset($data[$k]['attribute']['imgs'][$imageIndex]);
                                        }

                                    }

                                }

                            } elseif ($logical_type == 4) {
                                // 猜你喜欢
                                $images = [];
                                if (!empty($banner_commodity_list)) {
                                    $bannerSize = $attribute['bannerSize'] ?? 0;
                                    foreach ($banner_commodity_list as $banner_index => &$banner) {
                                        // 非顶部导航
                                        if ($bannerSize == $banner['image_type'] && $banner['is_enable'] == 1) {
                                            $images = [
                                                'src' => $banner['cover_image'],
                                                'savePath' => [
                                                    "type" => "1", //跳转类型：1:商品
                                                    "id"=> $banner['commodity_id'] ,  //type=1,2，根椐id跳指定商品
                                                ],
                                            ];
                                            $banner['is_enable'] = 0;
                                            $img[] = $images;
                                            break;
                                        }
                                    }
                                }
                                // 猜你喜欢没有匹配到数据组推荐的数据  选用默认数据
                                if (empty($images)) {
                                    foreach ($attribute['imgs'] as $imageIndex => $imageList) {
                                        $img_val      = [];
                                        $img_val_sort = [];
                                        // 获取同一个位置的数据
                                        if ($logical_index == $imageIndex) {
                                            foreach ($imageList as $image_index =>  $image) {
                                                if (!empty($image['show_time']['start_time']) && $currentTime < strtotime($image['show_time']['start_time'])) {
                                                    continue;
                                                }
                                                if (!empty($image['show_time']['end_time']) && $currentTime > strtotime($image['show_time']['end_time'])) {
                                                    continue;
                                                }

                                                if (!empty($image['userGroups'])) {
                                                    $info_count = $info_user->where(
                                                        [
                                                            'crowd_id' => array('in', $image['userGroups']),
                                                            'user_id'  => $user['id'] ?? 0,
                                                        ]
                                                    )->count();

                                                    if ($info_count == 0) {

                                                        continue;
                                                    }
                                                    $img_val_sort[] = $image_index + 1;
                                                } else {
                                                    $img_val_sort[] = $image_index + 999;
                                                }
                                                $img_val[] = $image;

                                            }
                                            array_multisort($img_val_sort, SORT_ASC, $img_val);
                                            if (in_array($channel_type, ['GWAPP', 'GWSM'])) {
                                                if (isset($img_val[0])) {
                                                    $img[] = $img_val[0];
                                                }
                                            } else {
                                                $img = $img_val;
                                            }
                                            if (!$img) {
                                                unset($data[$k]['attribute']['imgs'][$imageIndex]);
                                            }


                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        // 兼容旧数据
                        foreach ($attribute['imgs'] as $key => $val) {
                            $img_val      = [];
                            $img_val_sort = [];
                            if (!isset($val[0])) {
                                unset($data[$k]['attribute']['imgs'][$key]);
                                continue;
                            }
                            foreach ($val as $kkk_v => $va) {
                                if (isset($va['show_time'])) {
                                    if (time() < strtotime($va['show_time']['start_time'])) {
                                        unset($data[$k]['attribute']['imgs'][$key]);
                                        continue;
                                    }
                                    if (!empty($va['show_time']['end_time'])) {
                                        if (time() > strtotime($va['show_time']['end_time'])) {
                                            unset($data[$k]['attribute']['imgs'][$key]);
                                            continue;
                                        }
                                    }
                                }
                                if (!empty($va['userGroups'])) {
                                    $info_count = $info_user->where(
                                        [
                                            'crowd_id' => array('in', $va['userGroups']),
                                            'user_id'  => $user['id'] ?? 0,
                                        ]
                                    )->count();

                                    if ($info_count == 0) {

                                        continue;
                                    }
                                    $img_val_sort[] = $kkk_v + 1;
                                } else {
                                    $img_val_sort[] = $kkk_v + 999;
                                }
                                $img_val[] = $va;
                            }
                            array_multisort($img_val_sort, SORT_ASC, $img_val);
                            if (in_array($channel_type, ['GWAPP', 'GWSM'])) {
                                if (isset($img_val[0])) {
                                    $img[] = $img_val[0];
                                }
                            } else {
                                $img = $img_val;
                            }
                            if (!$img) {
                                unset($data[$k]['attribute']['imgs'][$key]);
                            }

                        }
                    }
                    $data[$k]['attribute']['imgs'] = $img;


                    if (!empty($data[$k]['attribute']['imgs'])) {
                        $data[$k]['attribute']['imgs'] = array_values($data[$k]['attribute']['imgs']);
                        $return_data[]                 = $data[$k];
                    } else {
                        unset($data[$k]);
                    }

                }
            }

            //用户自定义广告
            if ($v['type'] == 'cAdvertising1') {
                if (isset($attribute['imgs'])) {
                    foreach ($attribute['imgs'] as $key => $val) {
                        if (isset($val['show_time'])) {
                            if (!empty($val['show_time']['start_time']) || !empty($val['show_time']['end_time'])) {
                                if (time() < strtotime($val['show_time']['start_time'])) {
                                    unset($data[$k]['attribute']['imgs'][$key]);
                                    continue;
                                }
                                if (!empty($val['show_time']['end_time'])) {
                                    if (time() > strtotime($val['show_time']['end_time'])) {
                                        unset($data[$k]['attribute']['imgs'][$key]);
                                        continue;
                                    }
                                }
                            }
                        }
                        if (!empty($val['userGroups'])) {
                            $info_count = $info_user->where(
                                [
                                    'crowd_id' => array('in', $val['userGroups']),
                                    'user_id'  => $user['id'] ?? 0,
                                ]
                            )->count();
                            if ($info_count == 0) {
                                unset($data[$k]['attribute']['imgs'][$key]);
                            }
                        }
                    }
                    if (!empty($data[$k]['attribute']['imgs'])) {
                        $data[$k]['attribute']['imgs'] = array_values($data[$k]['attribute']['imgs']);
                        $return_data[]                 = $data[$k];
                    } else {
                        unset($data[$k]);
                    }
                } else {
                    $return_data[] = $data[$k];
                }

            }

            if ($v['type'] == 'cAdvertising2') {
                if (isset($attribute['imgs'])) {
                    foreach ($attribute['imgs'] as $key => $val) {
                        if (isset($val['show_time'])) {
                            if (!empty($val['show_time']['start_time']) || !empty($val['show_time']['end_time'])) {
                                if (time() < strtotime($val['show_time']['start_time'])) {
                                    unset($data[$k]['attribute']['imgs'][$key]);
                                    continue;
                                }
                                if (!empty($val['show_time']['end_time'])) {
                                    if (time() > strtotime($val['show_time']['end_time'])) {
                                        unset($data[$k]['attribute']['imgs'][$key]);
                                        continue;
                                    }
                                }
                            }
                        }
                        if (!empty($val['userGroups'])) {
                            $info_count = $info_user->where(
                                [
                                    'crowd_id' => array('in', $val['userGroups']),
                                    'user_id'  => $user['id'] ?? 0,
                                ]
                            )->count();
                            if ($info_count == 0) {
                                unset($data[$k]['attribute']['imgs'][$key]);
                            }
                        }
                    }
                    if (!empty($data[$k]['attribute']['imgs'])) {
                        $data[$k]['attribute']['imgs'] = array_values($data[$k]['attribute']['imgs']);
                        $return_data[]                 = $data[$k];
                    } else {
                        unset($data[$k]);
                    }
                } else {
                    $return_data[] = $data[$k];
                }

            }

            //首页广告位 用户标签
            if ($v['type'] == 'cAdvertising') {
                if (isset($attribute['imgs'])) {
                    foreach ($attribute['imgs'] as $key => $val) {
                        if (isset($val['show_time'])) {
                            if (!empty($val['show_time']['start_time']) || !empty($val['show_time']['end_time'])) {
                                if (time() < strtotime($val['show_time']['start_time'])) {
                                    unset($data[$k]['attribute']['imgs'][$key]);
                                    continue;
                                }
                                if (!empty($val['show_time']['end_time'])) {
                                    if (time() > strtotime($val['show_time']['end_time'])) {
                                        unset($data[$k]['attribute']['imgs'][$key]);
                                        continue;
                                    }
                                }
                            }
                        }
                        if (!empty($val['userGroups'])) {
                            $info_count = $info_user->where(
                                [
                                    'crowd_id' => array('in', $val['userGroups']),
                                    'user_id'  => $user['id'] ?? 0,
                                ]
                            )->count();
                            if ($info_count == 0) {
                                unset($data[$k]['attribute']['imgs'][$key]);
                            }
                        }
                    }
                    if (!empty($data[$k]['attribute']['imgs'])) {
                        $data[$k]['attribute']['imgs'] = array_values($data[$k]['attribute']['imgs']);
                        $return_data[]                 = $data[$k];
                    } else {
                        unset($data[$k]);
                    }
                } else {
                    $return_data[] = $data[$k];
                }
            }

            if ($v['type'] == 'cTabs') {
                $key        = config('cache_prefix.catalog') . $channel_type;
                $class_info = redis($key);
                $goods_ser  = new NetGoods();
                $redis      = \think\Cache::redisHandler();
                if (!in_array($channel_type, ['GWSM', "GWAPP", "QCSM", "QCAPP"])) {
                    if (empty($class_info) || getRedisLock($key . '-lock', mt_rand(7200, 17200))) {
                        $class_info = $goods_ser->goodsClass($channel_type, 0, []);
                        redis($key, $class_info, 864000);
                        $redis->sadd(config('cache_prefix.goods_class_set'), $key);
                    }
                } else {
                    if (empty($class_info) || getRedisLock($key . '-lock', mt_rand(7200, 17200))) {
                        $class_info = $goods_ser->newGoodsClass($channel_type, 0, []);
                        redis($key, $class_info, 864000);
                        $redis->sadd(config('cache_prefix.goods_class_set'), $key);
                    }
                }
                if (empty($class_info)) {
                    $data[$k]['is_classify'] = 0;
                } else {
                    $data[$k]['is_classify'] = 1;
                }
                $return_data[] = $data[$k];
            }

            //套装
            if ($v['type'] == 'cSuit') {
                $suit       = $attribute['list'];
                $net_goods  = new NetGoods();
                $suit_model = new BuCheapSuitIndex();
                $suit_data  = [];
                foreach ($suit as $key => $value) {

                    $activity_now = $this->check_activity($v['type'], $value['id']);
                    if (empty($activity_now)) {
                        unset($data[$k]['attribute']['list'][$key]);
                        continue;
                    }
                    $find_suit = $suit_model->where(['id' => $value['id'], 'e_time' => array('egt', time()), 'is_enable' => 1, 'act_status' => ['in', [1, 2]]])->count();
                    if (!empty($find_suit)) {
                        $res = $net_goods->suitList(['suit_ids' => $value['id']], $user, $channel_type);

                        if ($res['code'] == 200) {
                            if (!empty($res['msg']['list'])) {
                                $data[$k]['attribute']['list'][$key]['price']         = formatNumber($res['msg']['list'][0]['suit_price']);
                                $data[$k]['attribute']['list'][$key]['all_use_point'] = $res['msg']['list'][0]['all_use_point'];
                                $imgs                                                 = [];
                                foreach ($res['msg']['list'][0]['suit_list'] as $item) {
                                    if (empty($item['activity_image'])) {
                                        $imgs[] = $item['cover_image'];
                                    } else {
                                        $imgs[] = $item['activity_image'];
                                    }
                                }
                                $data[$k]['attribute']['list'][$key]['imgs'] = $imgs;
                            }
                        } else {
                            unset($data[$k]['attribute']['list'][$key]);
                        }
//                        $suit_data[] = $data[$k]['attribute']['list'][$key];
                    } else {
                        unset($data[$k]['attribute']['list'][$key]);
                    }
                }
                $data[$k]['attribute']['list'] = array_values($data[$k]['attribute']['list']);//重置key从0开始
                if (!empty($data[$k]['attribute']['list'])) {
                    $return_data[] = $data[$k];
                }
            }

            //众筹
            if ($v['type'] == 'cCrowdfunding') {
                $goods_list = $v['attribute']['goods_data'];
                if (!empty($v['attribute']['activity'])) {
                    $crowd_id = $v['attribute']['activity']['id'];
                    if (!empty($goods_list)) {
                        $commodity_id = [];
                        foreach ($goods_list as $val) {
                            $commodity_id[] = $val['id'];
                        }
                        $res = $this->crowdfundList($crowd_id, $commodity_id, $channel_type);
                        if ($res) {
                            $goods_lists = [];
                            foreach ($goods_list as $key => $value) {
                                if (isset($res[$value['id']])) {
                                    $goods_lists[$key]['tag']            = $res[$value['id']]['tag'];
                                    $goods_lists[$key]['price']          = $res[$value['id']]['price'];
                                    $goods_lists[$key]['original_price'] = $res[$value['id']]['price'];
                                    $goods_lists[$key]['tag_gwapp']      = $res[$value['id']]['tag_gwapp'];
                                    $goods_lists[$key]['tag_qcsm']       = $res[$value['id']]['tag_qcsm'];
                                    $goods_lists[$key]['tag_qcapp']      = $res[$value['id']]['tag_qcapp'];
                                    $goods_lists[$key]['tag_pz1asm']     = $res[$value['id']]['tag_pz1asm'];
                                    $goods_lists[$key]['tag_pz1aapp']    = $res[$value['id']]['tag_pz1aapp'];
                                    $goods_lists[$key]['tag_gwnet']      = $res[$value['id']]['tag_gwnet'];
                                    $goods_lists[$key]['goods_name']     = $value['goods_name'];
                                    if (!empty($res[$value['id']]['activity_image'])) {
                                        $goods_lists[$key]['image_path'] = $res[$value['id']]['activity_image'];
                                    } else {
                                        // 2025年2月24日 17:52:39 产品说没有众筹首图展示商品图片
                                        $goods_lists[$key]['image_path'] = $value['image_path'];
                                    }
                                    $goods_lists[$key]['id']              = $value['commodity_id'];
                                    $goods_lists[$key]['commodity_id']    = $value['commodity_id'];
                                    $goods_lists[$key]['tag_name']        = $res[$value['id']]['tag_name'];
                                    $goods_lists[$key]['commodity_label'] = $res[$value['id']]['commodity_label'];
                                    unset($res[$value['id']]['tag_gwapp'], $res[$value['id']]['tag'], $res[$value['id']]['tag_qcsm'], $res[$value['id']]['tag_qcapp'], $res[$value['id']]['tag_pz1asm'], $res[$value['id']]['tag_pz1aapp'], $res[$value['id']]['tag_gwnet'], $res[$value['id']]['tag_name'], $res[$value['id']]['commodity_label']);
                                    $goods_lists[$key]['crowdfund_info'] = $res[$value['id']];
                                    $goods_lists[$key]['savePath']       = $value['savePath'];
                                    $goods_lists[$key]['ap_time']        = date('Y-m-d H:i:s');
                                }
                            }
                            $data[$k]['attribute']['goods_data'] = array_values($goods_lists);
                            $return_data[]                       = $data[$k];
                        } else {
                            unset($data[$k]);
                        }
                    } else {
                        unset($data[$k]);
                    }
                } else {
                    unset($data[$k]);
                }
            }
        }


        foreach ($return_data as $key => $datum) {
            $return_data[$key]['component_type'] = $datum['type'];
            $return_data[$key]['component_id']   = $datum['type'] . '_' . $key;
        }
        return json_encode($return_data);
    }



    protected function getUserGroups($user)
    {
        if (empty($user['id'])) {
            return [];
        }
        $userGroupModel    = new WlzCrowdsLogs();
        $userGroup         = $userGroupModel->where(['user_id' => $user['id']])->field('id,crowd_id,user_id')->select();
        $userGroupCrowdIds = [];
        foreach ($userGroup as $group) {
            $userGroupCrowdIds[] = $group['crowd_id'];
        }
        return $userGroupCrowdIds;
    }





    /**
     * 组件不缓存
     * @param $data_json
     * @param $user
     * @param string $channel_type
     * @param string $lng
     * @param string $lat
     * @param int $kilometer
     * @param string $from
     * @return false|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function activityNoCache($data_json, $user, $channel_type = '', $lng = '', $lat = '', $kilometer = 0, $from = 'home')
    {
        $data        = json_decode($data_json, true) ?? [];
        $return_data = [];

        foreach ($data as $k => $v) {
            if ($from == 'home') {
                $array = ['cFloatWindow'];
            } else {
                $array = ['cFloatWindow'];
            }
            if (!in_array($v['type'], $array)) {
                $return_data[] = $v;
                continue;
            }
            $attribute = $v['attribute'];
            // 浮窗
            if ($v['type'] == 'cFloatWindow' && in_array($channel_type, ['QCAPP'])) {
                // 类型
                if ($attribute['type'] == 1) {
                    $cardId = $attribute['cards_data'][0]['card_data']['card_id'];

                    // 判断用户是否可以获取卡券
                    $card_arr = $this->canGetCards($user, [$cardId], $this->channel_type);
                    if (!in_array($cardId, $card_arr)) {
                        unset($data[$k]);
                        continue;
                    }

                    // 单张优惠券
                    // 查询卡券是否已过期
                    $map = ['id' => $cardId, 'act_status' => 2, 'is_enable' => 1];
                    // 判断当前用户是否已领取
                    $db_card_model = new DbCard();
                    $cardInfo      = $db_card_model->where($map)->find();

                    if (empty($cardInfo)) {
                        unset($data[$k]);
                        continue;
                    }

                    // 判断用户是否已核销
                    $is_get_card               = 0; // 未领取
                    $map                       = ['user_id' => $user['id'], 'card_id' => $cardId];
                    $card_receive_record_model = new BuCardReceiveRecord();
                    $records                   = $card_receive_record_model->where($map)->select();
                    if (!empty($records)) {
                        $status_arr = array_column(collection($records)->toArray(), 'status');
                        // 领取数量等于每人可领取数量
                        if (count($records) == $cardInfo['get_limit']) {
                            $is_get_card    = 1; // 已领取
                            $specifiedValue = 3; // 已核销

                            $filteredValues = array_filter($status_arr, function ($value) use ($specifiedValue) {
                                return $value == $specifiedValue;
                            });

                            if (count($status_arr) === count($filteredValues)) {
                                unset($data[$k]); // 卡券已核销  删除浮窗
                                continue;
                            }
                        } else {
                            // 库存不足
                            if ($cardInfo['available_count'] <= 0) {
                                $re = array_diff($status_arr, [3, 4, 5]);
                                // 都不可以使用
                                if (empty($re)) {
                                    unset($data[$k]); // 卡券不可用  删除浮窗
                                    continue;
                                } else {
                                    // 还有可以使用的卡券
                                    $is_get_card = 1;
                                }
                            }

                        }
                    } else {
                        // 未领取卡券
                        // 判断当前卡券库存呢
                        if ($cardInfo['available_count'] <= 0) {
                            unset($data[$k]); // 卡券不可用  删除浮窗
                            continue;
                        }

                    }
                    $data[$k]['attribute']['is_get_card'] = $is_get_card;
                }

                $return_data[] = $data[$k];
            } else {
                unset($data[$k]); // 卡券不可用  删除浮窗
                continue;
            }
        }
        return json_encode($return_data);

    }

    // 排除关闭的活动
    private function check_activity($type, $id)
    {
        $re = false;
        switch ($type) {
            case 'cLimit':
                $re = (new DbLimitDiscount())->getOne(['where' => ['id' => $id, 'is_enable' => 1, 'act_status' => 2]]);
                break;
            case 'cSeckill':
                $re = (new DbSeckill())->getOne(['where' => ['id' => $id, 'is_enable' => 1, 'act_status' => ['in', [1, 2]]]]);
                break;
            case 'cLottery':
                $re = (new DbDraw())->getOne(['where' => ['id' => $id, 'is_enable' => 1, 'act_status' => ['in', [1, 2]]]]);
                break;
            case 'cSuit':
                $re = (new BuCheapSuitIndex())->getOne(['where' => ['id' => $id, 'is_enable' => 1, 'act_status' => ['in', [2]]]]);
                break;
            case 'cCoupon':
                $re = (new DbCard())->getOne(['where' => ['id' => $id, 'is_enable' => 1, 'act_status' => ['in', [1, 2]]]]);
                if ($re) {
                    $re['id'] = "'" . $re['id'] . "'";
                }
                break;
        }
        return $re;
    }

    //众筹明细信息
    public function crowdfundList($crowd_id = 0, $commodity_id = [], $channel_type)
    {
        $crowd_model = new DbCrowdfund();
        $fileds      = "b.crowdfund_id,a.title,a.start_time,a.end_time,a.target,a.target_val,a.alr_crowd,a.purchase_num,a.is_use_ticket,a.ticket_ids,a.brand,a.up_down_channel,a.un_stand_standard,a.refund_set,a.gather_id,a.is_pv_subsidy,a.theme_name,a.act_status,b.plan_status,g.tag,g.tag_gwnet,g.tag_gwapp,g.tag_pz1asm,g.tag_pz1aapp,g.tag_qcapp,g.tag_qcsm,g.commodity_id,e.commodity_label,b.meddle_price,b.meddle_count,b.meddle_people,b.meddle_order,min(c.price) as price,b.meddle_type,g.activity_image";
        $where       = ['a.is_enable' => 1, 'c.is_enable' => 1, 'c.stock' => array('egt', 0), 'e.listing_type' => 2, 'a.act_status' => ['in', [1, 2]]];
        if ($crowd_id) {
            $where['a.id'] = $crowd_id;
        }
        if ($commodity_id) {
            $where['b.commodity_id'] = array('in', $commodity_id);
        }
        $crowd_info = $crowd_model->alias("a")
            ->join("t_db_crowdfund_commodity b", "a.id=b.crowdfund_id")
            ->join('t_db_commodity_flat g', 'g.commodity_id=b.commodity_id and FIND_IN_SET("' . $channel_type . '", g.up_down_channel_dlr)')
            ->join('t_db_commodity_set e', 'g.commodity_set_id=e.id')
            ->join('t_db_commodity_set_sku c', 'c.commodity_set_id=e.id')
            ->where($where)
            ->field($fileds)->order('a.created_date desc')->group('g.commodity_id')->select();
//        $crowd_info =  $crowd_model->getAllCrowdfund(['where' => $where,'field'=>$fileds,'order'=>"a.created_date desc"]);
        if ($crowd_info) {
            $data           = [];
            $cr_order_model = new DbCrowdfundOrder();
            foreach ($crowd_info as $value) {
                $crowd_info_one             = $value;
                $stat                       = $crowd_model->getActStatus($crowd_info_one['start_time'], $crowd_info_one['end_time']);
                $crowd_info_one->act_status = $stat;
                if (!in_array($value->plan_status, [0, 1, 2])) {
                    $crowd_info_one->act_status = 3;
                }
                $crowd_info_one->tag_name     = $this->tag_name($value, $channel_type);
                $data[$value['commodity_id']] = $crowd_info_one;
            }

            $order_field = "crowdfund_id,commodity_id,sum(num) all_num,sum(price) sp,count(distinct(user_id)) pp_count";
            $od_where    = ['crowdfund_id' => $crowd_id, 'commodity_id' => array('in', $commodity_id), 'is_enable' => 1, 'pay_status' => 1];
            $od_info     = $cr_order_model->getList(['where' => $od_where, 'field' => $order_field, 'group' => "commodity_id"]);
            if ($od_info) {
                if (count($commodity_id) == count($od_info)) {
                    foreach ($od_info as $value) {
                        $data[$value['commodity_id']]['money']     = $value['sp'] + $data[$value['commodity_id']]['meddle_price'];
                        $data[$value['commodity_id']]['sum_num']   = $value['all_num'] + $data[$value['commodity_id']]['meddle_count'];
                        $data[$value['commodity_id']]['peo_count'] = $value['pp_count'] + $data[$value['commodity_id']]['meddle_people'];
                    }
                } else {
                    foreach ($od_info as $value) {
                        $data[$value['commodity_id']]['money']     = $value['sp'] + $data[$value['commodity_id']]['meddle_price'];
                        $data[$value['commodity_id']]['sum_num']   = $value['all_num'] + $data[$value['commodity_id']]['meddle_count'];
                        $data[$value['commodity_id']]['peo_count'] = $value['pp_count'] + $data[$value['commodity_id']]['meddle_people'];
                        unset($commodity_id[array_search($value['commodity_id'], $commodity_id)]);
                    }
                    if (!empty($commodity_id)) {
                        foreach ($commodity_id as $value) {
                            if (isset($data[$value])) {
                                $data[$value]['money']     = 0 + $data[$value]['meddle_price'];
                                $data[$value]['sum_num']   = 0 + $data[$value]['meddle_count'];
                                $data[$value]['peo_count'] = 0 + $data[$value]['meddle_people'];
                            }
                        }
                    }
                }

                return $data;
            } else {
                foreach ($commodity_id as $value) {
                    if (isset($data[$value])) {
                        $data[$value]['money']     = sprintf("%.2f", 0 + $data[$value]['meddle_price']);
                        $data[$value]['sum_num']   = 0 + $data[$value]['meddle_count'];
                        $data[$value]['peo_count'] = 0 + $data[$value]['meddle_people'];
                    }
                }
                return $data;
            }


        } else {
            return false;
        }
    }

    public function tag_name($v, $channel_type)
    {
        $tag_name = [];
        if ($channel_type == 'GWNET') {
            $tag_column = $v['tag_gwnet'];
        } else if ($channel_type == 'GWAPP') {
            $tag_column = $v['tag_gwapp'];
        } else if ($channel_type == 'GWSM') {
            $tag_column = $v['tag'];
        } else if ($channel_type == 'PZ1AAPP') {
            $tag_column = $v['tag_pz1aapp'];
        } else if ($channel_type == 'PZ1ASM') {
            $tag_column = $v['tag_pz1asm'];
        } else if ($channel_type == 'QCSM') {
            $tag_column = $v['tag_qcsm'];
        } else if ($channel_type == 'QCAPP') {
            $tag_column = $v['tag_qcapp'];
        }
        $flat = new DbCommodityFlat();
        if ($tag_column) {
            $tags = explode(',', $tag_column);
            foreach ($tags as $kk => $vv) {
                $tag_info      = $flat::tagArr($vv);
                $tag_name[$vv] = $tag_info;
            }

            // 23-06UI改版 tag_name排序
            $sort_arr = [16, 12, 11, 14, 13, 10, 17, 15, 18];
            $tag_keys = array_keys($tag_name);
            foreach ($sort_arr as $sort_key => $sort) {
                if (!in_array($sort, $tag_keys)) {
                    unset($sort_arr[$sort_key]);
                }
            }
            $tag_name = array_values(array_replace(array_flip($sort_arr), $tag_name));
            return $tag_name;
        }

        return $tag_name;
    }

    public function componentHandle($component, $param, $user, $channelType)
    {
        switch ($component['type']) {
            case 'cLottery':
                return $this->componentLottery($component, $user);
            case 'cSeckill':
                return $this->componentSeckill($component, $user, $channelType);
            case 'cLimit':
                return $this->componentLimit($component, $user, $channelType);
            case 'cCrowdfunding':
                return $this->componentCrowdfunding($component, $channelType);
            case 'cSuit':
                return $this->componentSuit($component, $user, $channelType);
            case 'cCoupon':
            case 'cCouponAndGoods':
                return $this->componentCoupon($component, $param, $user, $channelType);
            case 'cFloatWindow':
            case 'cFloatingWindow':
                return $this->componentFloatWindow($component, $user, $channelType);
            case 'cGoods':
            case 'cWaterfallGoods':
                return $this->componentGoods($component, $param, $user, $channelType);

            case 'cCarousel':
                return $this->componentCCarousel($component, $param, $user, $channelType);
            case 'cAdvertising':
            case 'cAdvertising1':
            case 'cAdvertising2':
                return $this->componentCAdvertising($component, $param, $user, $channelType);

        }
        return $component;
    }

    public function componentFloatWindow($component, $user, $channelType)
    {
        // 类型
        $attribute = $component['attribute'];
        // 1单张优惠券 2其他类型
        if ($attribute['type'] == 1 && !empty($user['id'])) {
            unset($component['attribute']['other_data']);
            if (!isset($attribute['cards_data'][0]['card_data']['card_id'])) {
                $component['attribute']['cards_data'] = [];
                return $component;
            }
            $cardId = $attribute['cards_data'][0]['card_data']['card_id'];

//            // 判断用户是否可以获取卡券
//            $card_arr = $this->canGetCards($user, [$cardId], $channelType);
//            if (!in_array($cardId, $card_arr)) {
//                $component['attribute']['cards_data'] = [];
//                return $component;
//            }

            // 单张优惠券
            // 查询卡券是否已过期
            $map = ['id' => $cardId, 'act_status' => 2, 'is_enable' => 1];
            // 判断当前用户是否已领取
            $db_card_model = new DbCard();
            $cardInfo      = $db_card_model->where($map)->find();
            if (empty($cardInfo)) {
                $component['attribute']['cards_data'] = [];
                return $component;
            }

            // 判断用户是否已核销
            /*$is_get_card = 0; // 未领取
            $map = ['user_id' => $user['id'], 'card_id' => $cardId];
            $card_receive_record_model = new BuCardReceiveRecord();
            $records = $card_receive_record_model->where($map)->select();
            if (!empty($records)) {
                $status_arr = array_column(collection($records)->toArray(), 'status');
                // 领取数量等于每人可领取数量
                if (count($records) == $cardInfo['get_limit']) {
                    $is_get_card = 1; // 已领取
                    $specifiedValue = 3; // 已核销

                    $filteredValues = array_filter($status_arr, function ($value) use ($specifiedValue) {
                        return $value == $specifiedValue;
                    });

                    if (count($status_arr) === count($filteredValues)) {
                        $component['attribute']['cards_data'] = [];
                        return $component;
                    }
                } else {
                    // 库存不足
                    if ($cardInfo['available_count'] <= 0) {
                        $re = array_diff($status_arr, [3, 4, 5]);
                        // 都不可以使用
                        if (empty($re)) {
                            $component['attribute']['cards_data'] = [];
                            return $component;
                        } else {
                            // 还有可以使用的卡券
                            $is_get_card = 1;
                        }
                    }

                }
            } else {
                // 未领取卡券
                // 判断当前卡券库存呢
                if ($cardInfo['available_count'] <= 0) {
                    $component['attribute']['cards_data'] = [];
                    return $component;
                }
            }
            $component['attribute']['is_get_card'] = $is_get_card;
            */

            $data        = [
                'card_str_ids' => $cardId,
                'channel_type' => $user['channel_type'],
                'status'       => 0,
                'poster_id'    => 0,
            ];
            $netCard     = new NetCard();
            $card_data   = $netCard->getUserCard($user, $data);
            $is_received = 0;
            if (!empty($card_data['get_card_list'])) {
                $card_data = $card_data['get_card_list'];

                // 已抢光的卡券，也要正常显示出来
//                if ($card_data[0]['available_count'] <= 0) {
//                    $component['attribute']['cards_data'] = [];
//                    return $component;
//                }
                if ($card_data[0]['have_get'] > 0) {
                    $is_received = 1;
                } elseif ($card_data[0]['available_quantity'] > 0 && $card_data[0]['available_count'] > 0) {
                    $is_received = 0;
                }

            } else {
                $component['attribute']['cards_data'] = [];
                return $component;
            }
            $component['attribute']['cards_data'][0]['card_data']['card_id'] = (string)$cardId;
            $component['attribute']['cards_data'][0]['is_received']          = $is_received;
            $component['attribute']['cards_data'][0]['vin']                  = $card_data[0]['vin'];
            $component['attribute']['cards_data'][0]['article']              = $card_data[0]['not_bind_article'];
            $component['attribute']['cards_data'][0]['change_car']           = $card_data[0]['change_car'];
        } else {
            unset($component['attribute']['cards_data']);
        }
        return $component;
    }

    public function componentGoods($component, $param, $user, $channelType)
    {
        $allUniqueGoodsIdString = $param['find_commodity_ids'] ?? '';
        $allUniqueGoodsIds      = explode(',', $allUniqueGoodsIdString);
        $attribute              = $component['attribute'];
        $goodsList              = $attribute['goods_data'] ?? [];

        // 追加推荐商品
        // recommended_logical_type 1 数据组推荐 2 后台配置 3 大数据+数据组 4:猜你喜欢推荐，
        if (!empty($user['id']) && $user['brand'] == 1 && !empty($attribute['recommended_logical_type'])
            && in_array($attribute['recommended_logical_type'], [1, 3,4])) {
            // 查询车主信息
            $comService      = new Common();
            $userCarInfo     = $comService->user_car_type_redis($user);
            $userOwnerStatus = $userCarInfo['user_status'] ?? 0;
            if ($userOwnerStatus == 2) {
                $bdp = getCacheBdpVin($userCarInfo['user_vin']['vin']);

                $ads_like =getAbsDataNew($userCarInfo['user_vin']['vin'],$user['member_id'],0,1);
            }
            $ads = getCacheAbsMall($userCarInfo['user_vin']['vin'] ?? '', $user['member_id']);
            // 获取推荐数据
            $dataAds = json_decode_assoc($ads['goods'] ?? '{}');
            $dataLikeAds = $ads_like['goods']??[];
            $dataBdp = json_decode_assoc($bdp['goods'] ?? '{}');

            $dataRecommend = [];
            //&& $attribute['recommended_logical_type'] == 1
            if (!empty($dataAds && in_array($attribute['recommended_logical_type'],[1,3]))) {
                foreach ($dataAds as $v) {
                    if ($v['commodity_type'] == $attribute['classify_type']) {
                        $dataRecommend[] = $v;
                    }
                }
            }
//            print_json($dataLikeAds,$attribute['classify_types']);
            //加入猜你喜欢商品推荐
            if (!empty($dataLikeAds) && $attribute['recommended_logical_type'] == 4) {
                foreach ($dataLikeAds as $v) {
                    if (in_array($v['level3_id'],$attribute['classify_types'])) {
                        $dataRecommend[] = $v;
                    }
                }
            }
            if (!empty($dataBdp) && $attribute['recommended_logical_type'] == 3) {
                array_push($dataRecommend, ...$dataBdp);
            }


            if (!empty($dataRecommend)) {
                // 套餐和备件 排序到最前
                foreach ($dataRecommend as &$recommendItem) {
                    if($attribute['recommended_logical_type'] == 4){
                        $recommendItem['sort_order'] = 1;
                    }else{
                        if (in_array($recommendItem['dd_commodity_type'], [1, 3, 4, 10])) {
                            $recommendItem['sort_order'] = 1;
                        } else if ($recommendItem['dd_commodity_type'] == 9) {
                            $recommendItem['sort_order'] = 2;
                        } else {
                            $recommendItem['sort_order'] = 255;
                        }
                    }


                }
                if($attribute['recommended_logical_type'] != 4){
                    $dataRecommend     = collection($dataRecommend)->sort("arraySortOrder")->toArray();
                }
                $dataRecommend     = collection($dataRecommend)->sort("arraySortOrder")->toArray();
                $recommendGoodsIds = array_column($dataRecommend, 'commodity_id');
                $shavesType        = DbDlr::channel_to_shelves($channelType);
                $recommendGoods    = $this->getRecommend($recommendGoodsIds, $user['car_18n'] ?? '', $shavesType, $channelType);

                if (!empty($recommendGoods['list'])) {
                    if (isset($attribute['recommend_order']) && $attribute['recommend_order'] == 2) {
                        // 排序：后台配置->推荐商品
                        array_push($goodsList, ...$recommendGoods['list']);
                    } else {
                        // 排序：推荐商品->后台配置
                        array_push($recommendGoods['list'], ...$goodsList);
                        $goodsList = $recommendGoods['list'];
                    }
                }
            }
        }

        // 商品去重
        $goodsList                            = $this->uniqueGoods($goodsList, $allUniqueGoodsIds);
        $component['attribute']['goods_data'] = $goodsList;

        if (!empty($component['attribute']['goods_data'])) {
            $commodity_ids                = array_column($component['attribute']['goods_data'], 'id');
            $requestData['commodity_ids'] = implode(',', $commodity_ids);
            $requestData['car_id']        = $user['car_series_id'] ?? '';
            $requestData['lng']           = $param['lng'] ?? '';
            $requestData['lat']           = $param['lat'] ?? '';
            $requestData['kilometer']     = $param['kilometer'] ?? '';
            $requestData['page']          = 1;
            $requestData['pageSize']      = count($commodity_ids);
            $requestData['miao_sha']      = 1;
            $goodsListResult              = (new NetGoods())->goodsList($requestData, $user, $channelType, [], 'home', 'cGoods');
            $newGoodsList                 = [];
            foreach ($component['attribute']['goods_data'] as $item) {
                foreach ($goodsListResult['msg']['data'] as $goodsItem) {
                    if ($goodsItem['commodity_id'] == $item['id']) {
                        $item['image_path']      = $goodsItem['cover_image'];
//                        $item['price']           = number_format($goodsItem['final_price'], 2, '.', '');
//                        $item['sales_price']     = number_format($goodsItem['final_price'], 2, '.', '');
//                        $item['original_price']  = number_format($goodsItem['price'], 2, '.', '');
                        // 2025年2月25日 16:56:59 修改价格格式
                        $item['price']           = $goodsItem['final_price'];
                        $item['sales_price']     = $goodsItem['final_price'];
                        $item['original_price']  = $goodsItem['price'];
                        $item['tag_name']        = $goodsItem['tag_name'] ?? '';
                        $item['tag']             = $goodsItem['tag'] ?? '';
                        $item['tag_qcsm']        = $goodsItem['tag_qcsm'] ?? '';
                        $item['tag_gwapp']       = $goodsItem['tag_gwapp'] ?? '';
                        $item['tag_qcapp']       = $goodsItem['tag_qcapp'] ?? '';
                        $item['tag_pz1asm']      = $goodsItem['tag_pz1asm'] ?? '';
                        $item['tag_pz1aapp']     = $goodsItem['tag_pz1aapp'] ?? '';
                        $item['commodity_label'] = $goodsItem['commodity_label'] ?? '';
                        // 定向人群会员价
                        $item['current_price']                  = $goodsItem['current_price'] ?? '';
                        $item['commodity_dis_user_segment']     = $goodsItem['commodity_dis_user_segment'] ?? '';
                        $item['commodity_dis_act_user_segment'] = $goodsItem['commodity_dis_act_user_segment'] ?? '';

                        $item['commodity_dis_label']    = $goodsItem['commodity_dis_label'] ?? '';
                        $item['commodity_dis_label_cn'] = $goodsItem['commodity_dis_label_cn'] ?? '';
                        $item['pay_style']              = $goodsItem['pay_style'] ?? '';
                        $item['pay_type']               = $goodsItem['pay_type'] ?? '';
                        $item['stock']                  = $item['count_stock'] = $goodsItem['count_stock'] ?? 0;
                        $newGoodsList[]                 = $item;
                        break;
                    }
                }
            }

            // 获取指定数量商品
            if (!empty($attribute['show_count']) && $component['type'] == 'cGoods') {
                $newGoodsList = array_slice($newGoodsList, 0, $attribute['show_count']);
            }
            // 数据分页
            if ($component['type'] == 'cWaterfallGoods') {
                $page     = $param['current_page'] ?? 1;
                $pageSize = $param['page_size'] ?? 12;

                $component['attribute']['current_page'] = $page;
                $component['attribute']['total']        = count($newGoodsList);
                $component['attribute']['all_page']     = ceil($component['attribute']['total'] / $pageSize);
                $newGoodsList                           = array_slice($newGoodsList, ($page - 1) * $pageSize, $pageSize);
            }
            $component['attribute']['goods_data'] = $newGoodsList;
        }
        return $component;
    }

    protected function uniqueGoods($goods, $allUniqueGoodsIds)
    {
        $uniqueGoodsId = [];
        return collection($goods)->filter(function ($goods) use (&$uniqueGoodsId, $allUniqueGoodsIds) {
            if (in_array($goods['id'], $uniqueGoodsId) || in_array($goods['id'], $allUniqueGoodsIds)) {
                return false;
            }
            $uniqueGoodsId[] = $goods['id'];
            return true;
        })->toArray();
    }

    public function componentCoupon($component, $param, $user, $channelType)
    {
        // 定向人群过滤
        if (!empty($component['attribute']['range']) && $component['attribute']['range'] == 2
            && !empty($component['attribute']['cards_data'])) {
            $cardIds = [];
            foreach ($component['attribute']['cards_data'] as $v) {
                $cardIds[] = $v['card_data']['card_id'] ?? 0;
            }
            $canReceiveIds = (new NetUser())->canGetCards($user, $cardIds, $channelType);
            foreach ($component['attribute']['cards_data'] as $key => $v) {
                $cardId = $v['card_data']['card_id'] ?? 0;
                if (!in_array($cardId, $canReceiveIds)) {
                    unset($component['attribute']['cards_data'][$key]);
                }
            }
        }
        $cardIds = [];
        foreach ($component['attribute']['cards_data'] as $key => $val) {
            // 卡券组件判断图片
            if (($component['type'] == 'cCoupon') && (empty($val['img']) || empty($val['img_got']) || empty($val['card_data']))) {
                unset($component['attribute']['cards_data'][$key]);
            } else {

                if (isset($val['card_data']['card_id'])) {
                    $coupon = $this->check_activity('cCoupon', $val['card_data']['card_id']);
                    if (empty($coupon)) {
                        unset($component['attribute']['cards_data'][$key]);
                    } else {
                        $cardIds[] = $val['card_data']['card_id'];
                    }
                }
            }
        }
        if (!empty($component['attribute']['cards_data'])) {
            if (!empty($user['id'])) {
                // 判断卡券是否已领取
//                $netUser          = new NetUser();
//                $data['card_ids'] = implode(',', $cardIds);
//                $result           = $netUser->car_center($user, $channelType, $data);
                $receivedCardIds = [];
//                if (!empty($result['msg']['card_1']) && is_array($result['msg']['card_1'])) {
//                    $receivedCardIds = array_column($result['msg']['card_1'], 'id');
//                }
//                foreach ($component['attribute']['cards_data'] as &$card) {
//                    $card['card_data']['is_received'] = in_array($card['card_data']['card_id'], $receivedCardIds) ? 1 : 0;
//                }

                // 卡券关联商品
                if ($component['type'] == 'cCouponAndGoods') {
//                    print_json($component['attribute']['cards_data']);
                    $commodityIds = [];
                    foreach ($component['attribute']['cards_data'] as $value) {
                        foreach ($value['goods_data'] as $val) {
                            $commodityIds[] = $val['id'];
                        }
                    }

                    $requestData['commodity_ids'] = implode(',', $commodityIds);
                    $requestData['car_id']        = $user['car_series_id'] ?? '';
                    $requestData['lng']           = $param['lng'] ?? '';
                    $requestData['lat']           = $param['lat'] ?? '';
                    $requestData['kilometer']     = $param['kilometer'] ?? '';
                    $requestData['page']          = 1;
                    $requestData['pageSize']      = count($commodityIds);
                    $requestData['miao_sha']      = 1;
                    $goodsListResult              = (new NetGoods())->goodsList($requestData, $user, $channelType, [], 'home', 'cCouponAndGoods');
                    $goodsList                    = $goodsListResult['msg']['data'];
                    foreach ($component['attribute']['cards_data'] as $key => $value) {
                        $limitGoodsData = [];
                        foreach ($value['goods_data'] as $val) {
                            foreach ($goodsList as $goods) {
                                if ($val['id'] == $goods['commodity_id']) {
                                    $limitGoods                   = $val;
                                    // 2025年2月25日 16:53:11  价格处理
                                    $limitGoods['image_path']     = $goods['cover_image'];
                                    $limitGoods['price']          = $goods['final_price'];
                                    $limitGoods['sales_price']    = $goods['final_price'];
                                    $limitGoods['original_price'] = $goods['price'];
                                    $limitGoodsData[]             = $limitGoods;
                                    break;
                                }
                            }
                        }
                        $component['attribute']['cards_data'][$key]['goods_data'] = $limitGoodsData;
                    }
                }

                $cardIds = array_unique($cardIds);
                $data    = [
                    'card_str_ids' => implode(',', $cardIds),
                    'channel_type' => $user['channel_type'],
                    'status'       => 0,
                    'poster_id'    => 0,
                ];
                $netCard = new NetCard();
//                $card_data = $netCard->getUsablecard($user, $data, 3);
                $card_data = $netCard->getUserCard($user, $data);
                if (empty($card_data['get_card_list'])) {
                    $component['attribute']['cards_data'] = [];
                    return $component;
                }
                $card_data = $card_data['get_card_list'];
                foreach ($component['attribute']['cards_data'] as $key => &$card) {
                    $is_received   = 0;
                    $returnCardIds = array_column($card_data, 'id');
                    if (!empty($card['card_data']['card_id']) && in_array($card['card_data']['card_id'], $returnCardIds)) {
                        foreach ($card_data as $datum) {
                            if ($card['card_data']['card_id'] == $datum['id']) {
                                $card['card_data']['card_quota'] = formatNumber($datum['card_quota']);
                                $card['card_data']['card_discount'] = formatNumber($datum['card_discount']);
                                // 已抢光的卡券，也要正常显示出来
//                                if ($datum['available_count'] <= 0) {
//                                    $component['attribute']['cards_data'] = [];
//                                    return $component;
//                                }
                                if ($datum['have_get'] > 0) {
                                    $is_received = 1;
                                } elseif ($datum['available_quantity'] > 0 && $datum['available_count'] > 0) {
                                    $is_received = 0;
                                }

                                $card['is_received'] = $is_received;
                                $card['vin']         = $datum['vin'];
                                $card['article']     = $datum['not_bind_article'];
                                $card['change_car']  = $datum['change_car'];
                            }
                        }
                    } else {
                        unset($component['attribute']['cards_data'][$key]);
                    }

                }
            }
            $component['attribute']['cards_data'] = array_values($component['attribute']['cards_data']);
        }
        return $component;
    }

    public function componentCrowdfunding($component, $channelType)
    {
        if (empty($component['attribute']['activity']) || empty($component['attribute']['goods_data'])) {
            $component['attribute'] = [];
            return $component;
        }
        $goods_list   = $component['attribute']['goods_data'];
        $crowd_id     = $component['attribute']['activity']['id'];
        $commodity_id = [];
        foreach ($goods_list as $val) {
            $commodity_id[] = $val['id'];
        }
        $res = $this->crowdfundList($crowd_id, $commodity_id, $channelType);
        if (empty($res)) {
            $component['attribute']['goods_data'] = [];
            return $component;
        }
        $goods_items = [];
        foreach ($goods_list as $key => $value) {
            if (isset($res[$value['id']])) {
                $goods_items[$key]['tag']            = $res[$value['id']]['tag'];
                $goods_items[$key]['price']          = $res[$value['id']]['price'];
                $goods_items[$key]['original_price'] = $res[$value['id']]['price'];
                $goods_items[$key]['tag_gwapp']      = $res[$value['id']]['tag_gwapp'];
                $goods_items[$key]['tag_qcsm']       = $res[$value['id']]['tag_qcsm'];
                $goods_items[$key]['tag_qcapp']      = $res[$value['id']]['tag_qcapp'];
                $goods_items[$key]['tag_pz1asm']     = $res[$value['id']]['tag_pz1asm'];
                $goods_items[$key]['tag_pz1aapp']    = $res[$value['id']]['tag_pz1aapp'];
                $goods_items[$key]['tag_gwnet']      = $res[$value['id']]['tag_gwnet'];
                $goods_items[$key]['goods_name']     = $value['goods_name'];
                if (!empty($res[$value['id']]['activity_image'])) {
                    $goods_items[$key]['image_path'] = $res[$value['id']]['activity_image'];
                } else {
                    // 2025年2月24日 17:52:39 产品说没有众筹首图展示商品图片
                    $goods_items[$key]['image_path'] = $value['image_path'];
                }
                $goods_items[$key]['id']              = $value['commodity_id'];
                $goods_items[$key]['commodity_id']    = $value['commodity_id'];
                $goods_items[$key]['tag_name']        = $res[$value['id']]['tag_name'];
                $goods_items[$key]['commodity_label'] = $res[$value['id']]['commodity_label'];
                unset($res[$value['id']]['tag_gwapp'], $res[$value['id']]['tag'], $res[$value['id']]['tag_qcsm'], $res[$value['id']]['tag_qcapp'], $res[$value['id']]['tag_pz1asm'], $res[$value['id']]['tag_pz1aapp'], $res[$value['id']]['tag_gwnet'], $res[$value['id']]['tag_name'], $res[$value['id']]['commodity_label']);
                $goods_items[$key]['crowdfund_info'] = $res[$value['id']];
                $goods_items[$key]['savePath']       = $value['savePath'];
                $goods_items[$key]['ap_time']        = date('Y-m-d H:i:s');
            }
        }
        $component['attribute']['goods_data'] = array_values($goods_items);
        return $component;
    }

    public function componentSuit($component, $user, $channelType)
    {
        $netGoods  = new NetGoods();
        $suitModel = new BuCheapSuitIndex();
        foreach ($component['attribute']['list'] as $key => $value) {
            $activity = $this->check_activity('cSuit', $value['id']);
            if (empty($activity)) {
                unset($component['attribute']['list'][$key]);
                continue;
            }
            $findSuit = $suitModel->where(['id' => $value['id'], 'e_time' => array('egt', time()), 'is_enable' => 1, 'act_status' => ['in', [1, 2]]])
                ->count();
            if (empty($findSuit)) {
                unset($component['attribute']['list'][$key]);
                continue;
            }
            $res = $netGoods->suitList(['suit_ids' => $value['id']], $user, $channelType);
            if (empty($res['msg']['list'])) {
                unset($component['attribute']['list'][$key]);
                continue;
            }
            $component['attribute']['list'][$key]['price']         = formatNumber($res['msg']['list'][0]['suit_price']);
            $component['attribute']['list'][$key]['all_use_point'] = $res['msg']['list'][0]['all_use_point'];
            $images                                                = [];
            foreach ($res['msg']['list'][0]['suit_list'] as $item) {
                if (empty($item['activity_image'])) {
                    $images[] = $item['cover_image'];
                } else {
                    $images[] = $item['activity_image'];
                }
            }
            $component['attribute']['list'][$key]['imgs'] = $images;
        }
        $component['attribute']['list'] = array_values($component['attribute']['list']);
        return $component;
    }


    public function componentLimit($component, $user, $channelType)
    {
        $goodsDataList = $component['attribute']['goods_data'];
        $activities    = [];
        foreach ($goodsDataList as $goodsIdx => $goods) {
            if (!isset($activities[$goods['activity_id']])) {
                $activities[$goods['activity_id']] = $this->check_activity('cLimit', $goods['activity_id']);
            }
            $activity = $activities[$goods['activity_id']];
            if (empty($activity)) {
                unset($goodsDataList[$goodsIdx]);
            } else {
                $component['attribute']['goods_data'][$goodsIdx]['activity'] = [
                    'id'         => $activity['id'] ?? '',
                    'title'      => $activity['title'] ?? '',
                    'start_time' => $activity['start_time'] ?? '',
                    'end_time'   => $activity['end_time'] ?? '',
                ];
            }
        }

        $activityIds  = array_column($goodsDataList, 'activity_id');
        $commodityIds = array_column($goodsDataList, 'id');

        $skuIds    = [];
        $skuPrices = (new DbLimitDiscountCommodity())
            ->where(['limit_discount_id' => ['in', $activityIds], 'commodity_id' => ['in', $commodityIds]])
            ->column('sku_price');
        foreach ($skuPrices as $skuPrice) {
            $skuIds = array_merge($skuIds, array_keys(json_decode($skuPrice, true)));
        }
        $newWhere['c.id'] = ['in', $skuIds];
        $goodsListResult  = (new NetGoods())->goodsList([
            'commodity_ids' => implode(',', $commodityIds),
            'db-read'       => true,
            'lng'           => $param['lng'] ?? '',
            'lat'           => $param['lat'] ?? '',
            'kilometer'     => $param['kilometer'] ?? '',
            'pageSize'      => count($commodityIds),
            'miao_sha'      => 1,
        ], $user, $channelType, $newWhere);

        $goodsList      = $goodsListResult['msg']['data'];
        $limitGoodsData = [];
        foreach ($component['attribute']['goods_data'] as $goodsData) {
            foreach ($goodsList as $goods) {
                if ($goodsData['commodity_id'] == $goods['commodity_id']) {
                    $limitGoods                    = $goodsData;
                    $limitGoods['image_path']      = $goods['cover_image'];
//                    $limitGoods['price']           = number_format($goods['final_price'], 2, '.', '');
//                    $limitGoods['sales_price']     = number_format($goods['final_price'], 2, '.', '');
//                    $limitGoods['original_price']  = number_format($goods['price'], 2, '.', '');
                    $limitGoods['price']           = $goods['final_price'];
                    $limitGoods['sales_price']     = $goods['final_price'];
                    $limitGoods['original_price']  = $goods['price'];
                    $limitGoods['tag_name']        = $goods['tag_name'] ?? '';
                    $limitGoods['tag']             = $goods['tag'] ?? '';
                    $limitGoods['tag_qcsm']        = $goods['tag_qcsm'] ?? '';
                    $limitGoods['tag_gwapp']       = $goods['tag_gwapp'] ?? '';
                    $limitGoods['tag_qcapp']       = $goods['tag_qcapp'] ?? '';
                    $limitGoods['tag_pz1asm']      = $goods['tag_pz1asm'] ?? '';
                    $limitGoods['tag_pz1aapp']     = $goods['tag_pz1aapp'] ?? '';
                    $limitGoods['commodity_label'] = $goods['commodity_label'] ?? '';
                    // 定向人群会员价
                    $limitGoods['current_price']                  = $goods['current_price'] ?? '';
                    $limitGoods['commodity_dis_user_segment']     = $goods['commodity_dis_user_segment'] ?? '';
                    $limitGoods['commodity_dis_act_user_segment'] = $goods['commodity_dis_act_user_segment'] ?? '';
                    $limitGoods['commodity_dis_label']            = $goods['commodity_dis_label'] ?? '';
                    $limitGoods['commodity_dis_label_cn']         = $goods['commodity_dis_label_cn'] ?? '';
                    $limitGoods['pay_style']                      = $goods['pay_style'] ?? '';
                    $limitGoods['pay_type']                       = $goods['pay_type'] ?? '';
                    $limitGoodsData[]                             = $limitGoods;
                    break;
                }
            }
        }
        $component['attribute']['goods_data'] = $limitGoodsData;
        return $component;
    }

    public function componentSeckill($component, $user, $channelType)
    {
        $attribute = $component['attribute'];
        if (!empty($component['attribute']['activity'])) {
            $seckillService        = new Seckill();
            $newActivity           = $seckillService->getSeckillTime($attribute['activity']['id']);
            $attribute['activity'] = $newActivity;

            if (!empty($attribute['goods_data'])) {
                foreach ($attribute['goods_data'] as $goodsIndex => $goods) {
                    if ($goods['activity_id'] == $attribute['activity']['id']) {
                        $activity = $newActivity;
                    } else {
                        $activity = $seckillService->getSeckillTime($goods['activity_id']);
                    }
                    $attribute['goods_data'][$goodsIndex]['activity'] = $activity;
                    $end_time                                         = $activity['end_time'];
                    if ($end_time < date('Y-m-d H:i:s') || $activity['is_enable'] == 0 || !in_array($activity['act_status'], [1, 2])) {
                        unset($attribute['goods_data'][$goodsIndex]);
                    }
                }
                if (!empty($attribute['goods_data'])) {
                    $commodityIds = array_column($attribute['goods_data'], 'id');
                    $activityIds  = array_column($attribute['goods_data'], 'activity_id');
                    $skuIds       = [];
                    $skuPrices    = (new DbSeckillCommodity())
                        ->where(['seckill_id' => ['in', $activityIds], 'commodity_id' => ['in', $commodityIds]])
                        ->column('sku_price');
                    foreach ($skuPrices as $skuPrice) {
                        $skuIds = array_merge($skuIds, array_keys(json_decode($skuPrice, true)));
                    }
                    $newWhere['c.id'] = ['in', $skuIds];
                    $goodsListResult  = (new NetGoods())->goodsList([
                        'miao_sha'      => 1,
                        'commodity_ids' => implode(',', $commodityIds),
                        'db-read'       => true,
                        'lng'           => $param['lng'] ?? '',
                        'lat'           => $param['lat'] ?? '',
                        'kilometer'     => $param['kilometer'] ?? '',
                        'pageSize'      => count($commodityIds),
                    ], $user, $channelType, $newWhere);
                    // 商品下架活动处理
                    if (empty($goodsListResult['msg']['data'])) {
                        $seckillModel = new DbSeckill();
                        foreach ($attribute['goods_data'] as $kkk => $vv) {
                            // 获取秒杀类型
                            $field       = 'id,seckill_type,day_start_time,day_end_time';
                            $seckillInfo = $seckillModel->where('id', $vv['activity_id'])->field($field)->find();
                            $screening   = '';
                            if ($seckillInfo['seckill_type'] == 2) {
                                $time = date('H:i:s');
                                if ($time > $seckillInfo['day_end_time']) {
                                    // 明天场次
                                    $screening = date("Y-m-d", strtotime("+1 day"));
                                } else {
                                    // 当天场次
                                    $screening = date('Y-m-d');
                                }
                            }
                            $attribute['goods_data'][$kkk]['limited_stock'] = $this->kill_count($vv['activity_id'], $vv['id'], $screening);
                            $attribute['goods_data'][$kkk]['stock']         = $this->kill_count($vv['activity_id'], $vv['id'], $screening);
                        }
                    } else {
                        $goodsList        = $goodsListResult['msg']['data'];
                        $seckillGoodsData = [];
                        foreach ($attribute['goods_data'] as $goodsData) {
                            foreach ($goodsList as $goods) {
                                if ($goods['is_pp'] == 1 && $goodsData['commodity_id'] == $goods['commodity_id']) {
                                    $seckillGoods                  = $goodsData;
                                    $seckillGoods['price']         = $goods['final_price'];
                                    $seckillGoods['stock']         = $goods['count_stock'];
                                    $seckillGoods['limited_stock'] = $goods['limited_stock'] ?? 0;

                                    $seckillGoods['image_path'] = $goods['cover_image'];
                                    // $seckillGoods['price'] = number_format($goods['final_price'], 2, '.', '');
//                                    $seckillGoods['sales_price']     = number_format($goods['final_price'], 2, '.', '');
//                                    $seckillGoods['original_price']  = number_format($goods['price'], 2, '.', '');
                                    $seckillGoods['sales_price']     = $goods['final_price'];
                                    $seckillGoods['original_price']  = $goods['price'];
                                    $seckillGoods['tag_name']        = $goods['tag_name'] ?? '';
                                    $seckillGoods['tag']             = $goods['tag'] ?? '';
                                    $seckillGoods['tag_qcsm']        = $goods['tag_qcsm'] ?? '';
                                    $seckillGoods['tag_gwapp']       = $goods['tag_gwapp'] ?? '';
                                    $seckillGoods['tag_qcapp']       = $goods['tag_qcapp'] ?? '';
                                    $seckillGoods['tag_pz1asm']      = $goods['tag_pz1asm'] ?? '';
                                    $seckillGoods['tag_pz1aapp']     = $goods['tag_pz1aapp'] ?? '';
                                    $seckillGoods['commodity_label'] = $goods['commodity_label'] ?? '';
                                    // 定向人群会员价
                                    $seckillGoods['current_price']                  = $goods['current_price'] ?? '';
                                    $seckillGoods['commodity_dis_user_segment']     = $goods['commodity_dis_user_segment'] ?? '';
                                    $seckillGoods['commodity_dis_act_user_segment'] = $goods['commodity_dis_act_user_segment'] ?? '';
                                    $seckillGoods['commodity_dis_label']            = $goods['commodity_dis_label'] ?? '';
                                    $seckillGoods['commodity_dis_label_cn']         = $goods['commodity_dis_label_cn'] ?? '';
                                    $seckillGoods['pay_style']                      = $goods['pay_style'] ?? '';
                                    $seckillGoods['pay_type']                       = $goods['pay_type'] ?? '';
                                    $seckillGoodsData[]                             = $seckillGoods;
                                    break;
                                }
                            }
                        }
                        // 无库存秒杀商品排序到后面
                        $goods_data = collection($seckillGoodsData)->sort(function ($a, $b) {
                            if (($a['stock'] == $b['stock']) || ($a['stock'] > 0 && $b['stock'] > 0)) {
                                return 0;
                            }
                            return ($a['stock'] < $b['stock']) ? -1 : 1;
                        })->toArray();
                        $attribute['goods_data'] = array_values($goods_data);
                    }
                }
            }
            $attribute['goods_data'] = array_values($attribute['goods_data']);
            $component['ap_time']   = date('Y-m-d H:i:s');
            $component['attribute'] = $attribute;
        }
        return $component;
    }

    public function componentLottery($component, $user)
    {
        $attribute = $component['attribute'];
        if (empty($attribute['activity'])) {
            $component['attribute'] = [];
            return $component;
        }
        $game = (new DbDraw())->getOne(['where' => ['id' => $attribute['activity']['id']]]);
        if (empty($game) || $game['end_time'] < date('Y-m-d H:i:s') || $game['is_enable'] == 0) {
            $component['attribute'] = [];
            return $component;
        }
        $attribute['activity']['game_id']    = $game['game_id'];
        $attribute['activity']['draw_url']   = $game['draw_url'];
        $attribute['activity']['start_time'] = $game['start_time'];
        $attribute['activity']['end_time']   = $game['end_time'];

        if (!empty($user['member_id'])) {
            # 查询抽奖次数
            $re = HaoWan::create('hao_wan')->getLotteryNum($user['member_id'], $game['game_id']);
            if ($re->isSuccess()) {
                $draw_num = $re->getData()['times'];
            } else {
                $draw_num = 0;
            }
            $attribute['activity']['draw_num'] = $draw_num;
        }
        $component['attribute'] = $attribute;
        return $component;
    }


    /**
     * 广告位
     * @param $component
     * @param $param
     * @param $user
     * @param $channelType
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function componentCAdvertising($component, $param, $user, $channelType)
    {
        if (!isset($param['act_code'])) {

        }
        $attribute = $component['attribute'];
        $imgs      = $attribute['imgs'];
        $cardIds   = '';
        foreach ($imgs as $key => $item) {

            if (isset($item['act_code']) && $item['act_code'] == $param['act_code']) {
                // 获取卡券id
                $cardIds = implode(',', array_column($item['couponList'], 'card_id'));
            }
        }

        if (empty($cardIds)) {
            return [];
        }

        return $this->cardDraw($param['act_code'], $cardIds, $user, $channelType);
    }


    /**
     * 卡券抽奖
     * @param $act_code
     * @param $cardIds
     * @param $user
     * @param $channelType
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function cardDraw($act_code, $cardIds, $user, $channelType)
    {
        $card_draw_model = new DbCardDraw();
        $net_user        = new NetUser();
        $is_click        = 0;
        $act_id          = 'card_draw_' . $act_code;

        $map = [
            'act_code' => $act_code,
            'user_id'  => $user['id'],
        ];
        $num = $card_draw_model->where($map)->count();
        if ($num > 0) {
            $is_click = 1;
        }

        // 未点击过的
        if ($is_click == 0) {
            $data      = [
                'card_str_ids' => $cardIds,
                'channel_type' => $channelType,
            ];
            $netCard   = new NetCard();
            $card_data = $netCard->getUserCard($user, $data);
            if (empty($card_data['get_card_list'])) {

                $add = [
                    'act_code' => $act_code,
                    'user_id'  => $user['id'],
                    'channel'  => $channelType,
                ];
                $card_draw_model->insertGetId($add);

                return [
                    'is_click'   => $is_click,
                    'couponList' => [],
                ];
            }
            $card_data = $card_data['get_card_list'];
            foreach ($card_data as $k => $datum) {
                if ($datum['available_quantity'] > 0 && $datum['available_count'] > 0 && $datum['change_car'] == 0) {
                    $add    = [
                        'act_code' => $act_code,
                        'user_id'  => $user['id'],
                        'card_id'  => $datum['card_id'],
                        'channel'  => $channelType,
                    ];
                    $drawId = $card_draw_model->insertGetId($add);
                    // 领取卡券
                    $act_id = 'card_draw_' . $act_code;
                    $re     = $net_user->get_card($datum['card_id'], $act_id, BuCardReceiveRecord::SOURCE_KEY27, $user, $channelType);
                    if ($re['code'] == 200) {
                        $upd = ['is_received' => 1];
                    } else {
                        $upd = ['is_received' => 2];
                    }
                    $card_draw_model->where('id', $drawId)->update($upd);
                }
            }
        }


        $map   = [
            'a.is_enable' => 1,
            'act_code'    => $act_code,
            'a.user_id'   => $user['id'],
            'b.act_id'    => $act_id,
            'is_received' => 1,
            'b.status'    => ['in', [1, 3]]
        ];
        $field = 'a.card_id,c.card_name,c.card_type,card_quota,card_discount,least_type,least_cost,b.id as receiveId';
        $field .= ',b.status,b.coupon_code,b.source as couponSource,b.validity_date_start,b.validity_date_end';

        $a    = "a.card_id=b.card_id and a.user_id=b.user_id";
        $list = $card_draw_model->alias('a')
            ->join('t_bu_card_receive_record b', $a, 'left')
            ->join('t_db_card c', 'a.card_id=c.id')
            ->where($map)
            ->field($field)
            ->order('b.validity_date_end')
            ->select();
        $time = time();
        foreach ($list as $key => $item) {
            // 卡券过期
            if (($item['status'] == 1) && (strtotime($item['validity_date_end']) < $time)) {
                $list[$key]['status'] = 2;
            }
        }

        return [
            'is_click'   => $is_click,
            'couponList' => $list,
        ];
    }


    /**
     * 轮播广告
     * @param $component
     * @param $param
     * @param $user
     * @param $channelType
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function componentCCarousel($component, $param, $user, $channelType)
    {
        $attribute = $component['attribute'];
        $imgs      = $attribute['imgs'];
        $cardIds   = '';
        foreach ($imgs as $key => $val) {
            foreach ($val as $k => $datum) {
                if ($datum['savePath']['type'] == 8 && isset($datum['act_code']) && $datum['act_code'] == $param['act_code']) {
                    // 卡券抽奖
                    // 获取卡券id
                    $cardIds = implode(',', array_column($datum['couponList'], 'card_id'));
                }
            }
        }
        if (empty($cardIds)) {
            return [];
        }

        return $this->cardDraw($param['act_code'], $cardIds, $user, $channelType);
    }
}
