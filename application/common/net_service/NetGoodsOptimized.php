<?php
/**
 * 优化后的商品服务类
 * 主要优化：
 * 1. 消除N+1查询问题
 * 2. 优化SQL查询结构
 * 3. 改进缓存策略
 * 4. 批量数据处理
 */

namespace app\common\net_service;

use app\common\model\db\DbCommodityFlat;
use think\Cache;
use tool\Logger;

class NetGoodsOptimized extends NetGoods
{
    /**
     * 优化后的商品列表方法 - 保留完整业务逻辑
     * @param array $requestData 请求参数
     * @param array $user 用户信息
     * @param string $channel_type 渠道类型
     * @param array $where 额外查询条件
     * @param string $from 来源
     * @param string $type 类型
     * @return array
     */
    public function goodsListOptimized($requestData, $user, $channel_type, $where = [], $from = '', $type = '')
    {
        $api_start_at = microtime(true);
        $this->user = $user;
        $this->channel_type = $channel_type;

        if ($user) {
            $this->user_id = $user['id'];
            $this->unionid = $user['bind_unionid'];
        }

        // 解析请求参数 - 保留所有原始参数
        $params = $this->parseCompleteRequestParams($requestData);

        // 构建缓存键
        $cache_key = $this->buildOptimizedCacheKey($params, $user, $channel_type, $where, $from, $type);

        // 尝试从缓存获取数据 - 缓存时间缩短避免数据不一致
        $cached_result = Cache::get($cache_key);
        if ($cached_result !== false) {
            Logger::info('goodsList cache hit', ['cache_key' => $cache_key]);
            return $this->re_msg($cached_result);
        }

        try {
            // 执行完整的业务逻辑，但优化查询方式
            $result = $this->executeOptimizedGoodsList($requestData, $user, $channel_type, $where, $from, $type, $params, $api_start_at);

            // 缓存结果 - 使用较短的缓存时间确保数据一致性
            Cache::set($cache_key, $result, 60); // 缓存1分钟

            Logger::info('goodsList optimized completed', [
                'execution_time' => number_format(microtime(true) - $api_start_at, 4),
                'goods_count' => count($result['data'] ?? []),
                'user_id' => $user['id'] ?? 0
            ]);

            return $this->re_msg($result);

        } catch (\Exception $e) {
            Logger::error('goodsList optimized error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $params
            ]);

            // 降级到原方法
            return $this->goodsList($requestData, $user, $channel_type, $where, $from, $type);
        }
    }

    /**
     * 解析完整的请求参数 - 保留所有原始参数
     */
    private function parseCompleteRequestParams($requestData)
    {
        return [
            'page' => $requestData['page'] ?? 1,
            'page_size' => $requestData['pageSize'] ?? 30,
            'comm_type_id' => $requestData['comm_type_id'] ?? '',
            'com_s_types' => $requestData['com_s_types'] ?? '',
            'car_id' => $requestData['car_id'] ?? '',
            'search' => $requestData['search'] ?? '',
            'commodity_ids' => trim($requestData['commodity_ids'] ?? '', ','),
            'card_id' => $requestData['card_id'] ?? '',
            'price_start' => $requestData['price_start'] ?? '',
            'price_end' => $requestData['price_end'] ?? '',
            'ask_at' => $requestData['ask_at'] ?? 0,
            'dd_dlr_code' => $requestData['dd_dlr_code'] ?? 0,
            'kilometer' => $requestData['kilometer'] ?? '',
            'lng' => $requestData['lng'] ?? '',
            'lat' => $requestData['lat'] ?? '',
            'n_dis_id' => $requestData['n_dis_id'] ?? 0,
            'full_cut_id' => $requestData['full_cut_id'] ?? 0,
            'sku_ids' => $requestData['sku_ids'] ?? 0,
            'new_comm_type_id' => $requestData['new_comm_type_id'] ?? '',
            'order_by' => $requestData['order_by'] ?? '',
            'use_gift_card' => $requestData['use_gift_card'] ?? 0,
            'gift_card_main' => $requestData['gift_card_main'] ?? 0,
            'new_order' => $requestData['new_order'] ?? '',
            'fu_pin' => $requestData['fu_pin'] ?? null,
            'miao_sha' => $requestData['miao_sha'] ?? null,
            'suit_list' => $requestData['suit_list'] ?? null,
            'card_gift' => $requestData['card_gift'] ?? null
        ];
    }

    /**
     * 构建优化的缓存键
     */
    private function buildOptimizedCacheKey($params, $user, $channel_type, $where, $from, $type)
    {
        $cache_data = [
            'params' => $params,
            'user_id' => $user['id'] ?? 0,
            'car_series_id' => $user['car_series_id'] ?? 0,
            'channel_type' => $channel_type,
            'where' => $where,
            'from' => $from,
            'type' => $type,
            'version' => 'v2.1' // 版本号，用于缓存失效
        ];

        return 'goods_list_optimized_v2_' . md5(json_encode($cache_data));
    }

    /**
     * 执行优化的商品列表逻辑 - 保留完整业务逻辑但优化查询
     */
    private function executeOptimizedGoodsList($requestData, $user, $channel_type, $where, $from, $type, $params, $api_start_at)
    {
        // 初始化必要的变量和模型
        $g_must_delete = [];
        $time = date('Y-m-d H:i:s');

        // 构建基础查询条件 - 保留原始逻辑
        $where = $this->buildBaseWhereConditions($params, $where, $user, $channel_type, $time);
        $count_where = $where; // 复制用于计数查询

        // 处理特殊业务逻辑
        $business_data = $this->handleSpecialBusinessLogic($params, $user, $channel_type, $where, $count_where);

        // 获取商品列表 - 优化查询但保留完整逻辑
        $list_data = $this->getOptimizedGoodsList($params, $where, $count_where, $business_data, $api_start_at);

        if (empty($list_data['list'])) {
            return ['data' => [], 'total' => 0];
        }

        // 处理商品数据 - 保留完整的业务逻辑
        $processed_list = $this->processCompleteGoodsData($list_data, $user, $channel_type, $params, $from, $type, $api_start_at, $business_data);

        return [
            'data' => $processed_list['data'],
            'total' => $list_data['total'],
            'user_info' => $processed_list['user_info'] ?? [],
            'get_card_list' => $processed_list['get_card_list'] ?? [],
            'goods_card_rule' => $processed_list['goods_card_rule'] ?? [],
            'all_card' => $processed_list['all_card'] ?? [],
            'no_list_tip' => $processed_list['no_list_tip'] ?? ''
        ];
    }

    /**
     * 获取基础商品数据
     */
    private function getBaseGoodsData($params, $additional_where = [])
    {
        $flat = new DbCommodityFlat();
        
        // 构建基础查询条件
        $where = array_merge([
            'a.is_enable' => 1,
            'b.is_enable' => 1,
            'b.count_stock' => ['>', 0]
        ], $additional_where);

        // 渠道过滤
        $where[] = ['exp', sprintf("find_in_set('%s', a.up_down_channel_dlr)", $this->channel_type)];

        // 搜索条件
        if (!empty($params['search'])) {
            if (preg_match('/^\d+$/', $params['search'])) {
                $where['a.commodity_id'] = $params['search'];
            } else {
                $where['a.commodity_name'] = ['like', '%' . $params['search'] . '%'];
            }
        }

        // 分类条件
        if (!empty($params['comm_type_id'])) {
            $comm_type_all_id = $this->_goods_type_arr(intval($params['comm_type_id']));
            $where['a.comm_type_id'] = ['in', $comm_type_all_id];
        }

        // 商品ID条件
        if (!empty($params['commodity_ids'])) {
            $where[] = ['exp', sprintf("a.commodity_id in (%s)", $params['commodity_ids'])];
        }

        // 价格条件
        if (!empty($params['price_start'])) {
            $where[] = ['exp', "b.discount_price_range_start >= " . $params['price_start']];
        }
        if (!empty($params['price_end'])) {
            $where[] = ['exp', "b.discount_price_range_end <= " . $params['price_end']];
        }

        // 排序
        $order = $this->buildOrderClause($params);

        // 字段
        $field = "a.commodity_id,a.commodity_name,a.cover_image,a.card_id,a.tag,a.is_grouped,
                 b.commodity_set_id,b.count_stock,b.max_point,b.pay_style,b.listing_type,
                 b.discount_price_range_start as price,b.discount_price_range_end as final_price,
                 a.dd_commodity_type,a.comm_type_id,a.activity_image";

        // 执行查询
        $result = $flat->alias('a')
            ->join('t_db_commodity_set b', 'a.commodity_set_id=b.id and b.is_enable=1')
            ->where($where)
            ->field($field)
            ->order($order)
            ->paginate($params['page_size'], false, ['query' => ['page' => $params['page']]]);

        return [
            'data' => $result->items(),
            'total' => $result->total()
        ];
    }

    /**
     * 构建排序子句
     */
    private function buildOrderClause($params)
    {
        $order = 'a.last_updated_date asc';
        
        if (!empty($params['new_order'])) {
            switch ($params['new_order']) {
                case 'sale_number':
                    $order = 'b.front_sale_num desc';
                    break;
                case 'price_asc':
                    $order = 'b.discount_price_range_start asc';
                    break;
                case 'price_desc':
                    $order = 'b.discount_price_range_start desc';
                    break;
                case 'new_goods':
                case 'new_good':
                    $order = 'b.created_date desc';
                    break;
            }
        }
        
        return $order;
    }





    /**
     * 处理商品标签
     */
    private function processGoodsTags($goods, $params)
    {
        $tags = [];

        // 参数预留用于后续扩展: $params
        // 根据渠道获取标签字段
        $tag_column = '';
        switch ($this->channel_type) {
            case 'GWNET':
                $tag_column = $goods['tag_gwnet'] ?? '';
                break;
            case 'GWAPP':
                $tag_column = $goods['tag_gwapp'] ?? '';
                break;
            case 'GWSM':
                $tag_column = $goods['tag'] ?? '';
                break;
            default:
                $tag_column = $goods['tag'] ?? '';
        }

        if ($tag_column) {
            $tag_ids = explode(',', $tag_column);
            foreach ($tag_ids as $tag_id) {
                if ($tag_id) {
                    $tag_info = DbCommodityFlat::tagArr($tag_id);
                    if ($tag_info) {
                        $tags[] = $tag_info;
                    }
                }
            }
        }

        return $tags;
    }



    /**
     * 获取分类ID下所有子类ID
     * 从原NetGoods类复制的方法
     */
    private function _goods_type_arr($type_id, $type = 0)
    {
        $type_level = 3;
        if ($type == 1) {
            $goods_type_key = 'cache_goods_type_arr' . $this->channel_type . $type_id;
            $return = redis($goods_type_key);

            if (empty($return) || getRedisLock($goods_type_key . '-lock', 60)) {
                $type_model = new \app\common\model\db\DbHomeType();
                $model = new \app\common\model\db\DbHomeTypeCommodity();
                $type_info = $type_model->getOneByPk($type_id);
                if($type_info['category_level']==2){
                    $three_type = $type_model->getList(['where'=>['comm_parent_id'=>$type_id,'is_enable'=>1]]);
                    $three_type_id_arr = array_column($three_type,'id');
                }
                if ($type_info['category_level'] == $type_level) {
                    $three_type_id_arr = [$type_id];
                }

                if(!empty($three_type_id_arr)){
                    $commodity_id_arr = $model->getColumn(['where'=>['new_comm_type_id'=>['in',$three_type_id_arr],'is_enable'=>1],'column'=>'commodity_id']);
                    $return = array_values($commodity_id_arr);
                    redis($goods_type_key, $return, 80);
                }
            }

            return $return;
        } else {
            $goods_type_key = 'cache_goods_type_arr' . $type_id;
            $return = redis($goods_type_key);

            if (empty($return) || getRedisLock($goods_type_key . '-lock', 60)) {
                $type_model = new \app\common\model\db\DbCommodityType();
                $type_info = $type_model->getOneByPk($type_id);
                if ($type_info['level'] == $type_level) {
                    redis($goods_type_key, $type_id, 80);
                    return $type_id;
                }
                if ($type_level - $type_info['level'] == 1) {
                    $type_two_info = $type_model->getColumn(['where' => ['comm_parent_id' => $type_id], "column" => "id"]);
                    redis($goods_type_key, $type_two_info, 80);
                    return array_values($type_two_info);
                }
                if ($type_level - $type_info['level'] == 2) {
                    $type_3_info = $type_model->alias("a")->join("t_db_commodity_type b", "a.id=b.comm_parent_id")->join("t_db_commodity_type c", "b.id=c.comm_parent_id")->where(['a.id' => $type_id])->column("c.id");
                    redis($goods_type_key, $type_3_info, 80);
                    return array_values($type_3_info);
                }
            }
        }

        return $return;
    }

    /**
     * 构建基础查询条件 - 保留原始逻辑，包含所有关键条件
     */
    private function buildBaseWhereConditions($params, $where, $user, $channel_type, $time)
    {
        // 基础条件
        $where['a.is_enable'] = 1;
        $where['c.is_enable'] = 1;

        // 库存条件
        if (!isset($params['fu_pin']) && !isset($params['miao_sha']) && !isset($params['suit_list']) && !isset($params['card_gift'])) {
            $where['b.count_stock'] = ['>', 0];
        }

        // 渠道条件
        $where[] = ['exp', sprintf("find_in_set('%s', a.up_down_channel_dlr)", $channel_type)];

        // 搜索条件
        if (!empty($params['search'])) {
            if (preg_match('/^\d+$/', $params['search'])) {
                $where['a.commodity_id'] = $params['search'];
            } else {
                $flat = new DbCommodityFlat();
                $flat_goods_ids = $flat->getColumn(['where' => ['commodity_name' => ['like', '%' . $params['search'] . '%']], 'column' => 'commodity_id']);
                $where['a.commodity_id'] = ['in', $flat_goods_ids];
            }
        }

        // 分类条件
        if (!empty($params['comm_type_id'])) {
            $comm_type_all_id = $this->_goods_type_arr(intval($params['comm_type_id']));
            $where['a.comm_type_id'] = ['in', $comm_type_all_id];
        }

        // 新版分类条件
        if (!empty($params['new_comm_type_id'])) {
            $comm_type_all_id = $this->_goods_type_arr(intval($params['new_comm_type_id']), 1);
            $where['a.commodity_id'] = ['in', $comm_type_all_id];
        }

        // com_s_types 条件
        if (!empty($params['com_s_types'])) {
            if (is_numeric($params['com_s_types'])) {
                $com_s_types = $this->_goods_type_arr(intval($params['com_s_types']));
            } else {
                $com_s_types = $params['com_s_types'];
            }
            $where['a.comm_type_id'] = ['in', $com_s_types];
        }

        // 商品ID条件
        if (!empty($params['commodity_ids'])) {
            $where[] = ['exp', sprintf("a.commodity_id in (%s)", $params['commodity_ids'])];
        }

        return $where;
    }

    /**
     * 处理特殊业务逻辑 - 包含所有关键的车型和保养逻辑
     */
    private function handleSpecialBusinessLogic($params, $user, $channel_type, &$where, &$count_where)
    {
        $business_data = [];

        // 处理不显示的商品
        $commoditySet = new \app\common\model\db\DbCommoditySet();
        $not_show_key = 'not_show_dlr_tmp';
        $not_show_ids = redis($not_show_key);
        if (empty($not_show_ids) || getRedisLock($not_show_key . '-lock', 60)) {
            $not_show_ids = $commoditySet->getColumn(['where' => "FIND_IN_SET('{$channel_type}', not_show_dlr)", 'column' => 'commodity_id']);
            redis($not_show_key, $not_show_ids, 80);
        }
        if ($not_show_ids) {
            $where['a.commodity_id'] = $count_where['a.commodity_id'] = ['not in', $not_show_ids];
        }

        // 处理用户车辆和保养相关逻辑
        $tc_qz = $this->tc_zg($user, $params['lng'], $params['lat'], $params['kilometer'], $params['dd_dlr_code']);
        $business_data['dlr_code'] = $tc_qz['dlr_code'];
        $business_data['dlr_level'] = $tc_qz['dlr_level'];
        $business_data['maintain_type'] = $tc_qz['maintain_type'];
        $business_data['user_info'] = $tc_qz['user_info'];
        $business_data['user_is_sb'] = $tc_qz['user_is_sb'] ?? false;
        $business_data['xy_can_buy'] = $tc_qz['xy_can_buy'] ?? false;
        $business_data['yb_can_sku_code'] = $tc_qz['yb_can_sku_code'] ?? [];

        // 添加城市级别和经销商条件
        $where[] = ['exp', sprintf("(find_in_set('%s',d.city_type) || d.city_type='' || d.city_type is null)", $business_data['dlr_level'])];
        if ($business_data['dlr_code']) {
            $where[] = ['exp', sprintf("(find_in_set('%s',d.relate_dlr_code) || d.relate_dlr_code='' || d.relate_dlr_code is null)", $business_data['dlr_code'])];
        }

        // 关键：车型关联条件 - relate_car_ids
        $car_s_id = $user['car_series_id'] ?? 0;
        if ($car_s_id && !in_array($channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
            $car_s_id = intval($car_s_id);
            $car_where_arr = ['exp', sprintf("((find_in_set(%s,d.relate_car_ids) || d.relate_car_ids='' || d.relate_car_ids is null))", $car_s_id)];
            $where[] = $car_where_arr;
            $count_where[] = $car_where_arr;
        }

        // 关键：保养折扣条件 - maintain_q
        $maintain_type = $business_data['maintain_type'];
        if ($business_data['user_is_sb']) {
            $sb_main_q = '7.0';
        } else {
            $sb_main_q = '99';
        }

        // 保养套餐和焕新产品的maintain_q过滤
        $maintain_where = ['exp', sprintf("((d.maintain_q='%s' and a.dd_commodity_type=3) || (d.maintain_q='%s' and a.dd_commodity_type=12) || a.dd_commodity_type not in (3,12))", $maintain_type, $sb_main_q)];
        $where[] = $maintain_where;
        $count_where[] = $maintain_where;

        // 延保产品SKU过滤
        if (!empty($business_data['yb_can_sku_code'])) {
            $yb_sku_codes = implode(',', array_map(function($v) { return "'".$v."'"; }, $business_data['yb_can_sku_code']));
            $where[] = ['exp', sprintf("(d.sku_code in (%s) || a.commodity_class<>9)", $yb_sku_codes)];
            $count_where[] = ['exp', sprintf("(d.sku_code in (%s) || a.commodity_class<>9)", $yb_sku_codes)];
        }

        // 机油类型过滤
        $user_oil_type = $user['18_oil_type'] ?? 4;
        $user_oil_type_s = $user_oil_type;
        if ($user_oil_type == 4) {
            $user_oil_type_s = '3.5,4';
        }
        $where[] = ['exp', sprintf("(d.oli_liters in (%s) || d.oli_liters ='')", $user_oil_type_s)];
        $count_where[] = ['exp', sprintf("(d.oli_liters in (%s) || d.oli_liters ='')", $user_oil_type_s)];

        // 众筹条件
        $where[] = ['exp', sprintf("((b.listing_type=2 and a.crowdfund_dis like '%%%s%%') || b.listing_type=1)", $channel_type)];
        $count_where[] = ['exp', sprintf("((b.listing_type=2 and a.crowdfund_dis like '%%%s%%') || b.listing_type=1)", $channel_type)];

        // 关键：处理card_id相关逻辑
        $card_business_data = $this->handleCardIdLogic($params, $user, $where, $count_where);
        $business_data = array_merge($business_data, $card_business_data);

        // 关键：处理遗漏的重要查询条件
        $this->handleMissingConditions($params, $where, $count_where, $business_data);

        return $business_data;
    }

    /**
     * 处理card_id相关的复杂业务逻辑
     * 包括卡券领取状态、车辆VIN匹配、商品/SKU关联等
     */
    private function handleCardIdLogic($params, $user, &$where, &$count_where)
    {
        $card_business_data = [
            'no_list_tip' => '',
            'card_use_goods_arr' => [],
            'card_set_sku_ids' => [],
            'and_where' => ''
        ];

        $card_id = $params['card_id'] ?? '';
        if (!$card_id) {
            return $card_business_data;
        }

        $card_id = (string)$card_id;

        // 检查用户卡券领取状态和车辆匹配
        if ($user['id']) {
            $card_r_model = new \app\common\model\db\BuCardReceiveRecord();
            $card_res = $card_r_model->getCardRes($card_id, $user['vin_list'], $user['id']);
            $card_no_vin = [];

            if ($card_res) {
                foreach ($card_res as $c_r_v) {
                    if ($c_r_v['receive_vin']) {
                        if ($user['vin'] != $c_r_v['receive_vin']) {
                            $card_no_vin[] = $c_r_v['receive_vin'];
                        } else {
                            // 有匹配的话不需要显示了
                            $card_no_vin = [];
                            break;
                        }
                    }
                }
            } else {
                $card_business_data['no_list_tip'] = '当前券暂无适用于您车辆的商品，如有疑问，可联系客服';
            }

            if ($card_no_vin) {
                $u_car_s_model = new \app\common\model\db\DbUserCarSeries();
                $card_no_vin = array_unique($card_no_vin);
                $u_car_list = $u_car_s_model->getList([
                    'where' => [
                        'user_id' => $user['id'],
                        'vin' => ['in', $card_no_vin]
                    ]
                ]);
                $u_no_vin_str = '';
                foreach ($u_car_list as $uu_v) {
                    $u_no_vin_str .= sprintf("%s %s,", $uu_v['vin'], $uu_v['car_series_name'] . $uu_v['car_type_name']);
                }

                $card_business_data['no_list_tip'] = sprintf('当前券适用于您车架号 %s 车辆，请切换车辆用券', $u_no_vin_str);
            }
        }

        // 添加卡券商品过滤条件
        $where[] = ['exp', sprintf("find_in_set(%s, a.card_id)", $card_id)];
        $count_where[] = ['exp', sprintf("find_in_set(%s, card_id)", $card_id)];

        $card_business_data['and_where'] = sprintf(" and find_in_set(%s, card_c.card_id) ", $card_id);

        // 处理卡券商品/SKU关联配置
        $this->processCardGoodsRelation($card_id, $where, $count_where, $card_business_data);

        return $card_business_data;
    }

    /**
     * 处理卡券与商品/SKU的关联关系
     * 卡券可以关联：1.商品 2.SKU 3.分类
     */
    private function processCardGoodsRelation($card_id, &$where, &$count_where, &$card_business_data)
    {
        $card_goods_model = new \app\common\model\db\DbCommodityCard();
        $card_info_where = ['card_id' => $card_id, 'is_enable' => 1];
        $card_info_list = $card_goods_model->getList(['where' => $card_info_where]);

        if (!$card_info_list) {
            return;
        }

        $card_set_sku_ids = [];
        $card_cc_goods_ids = [];
        $card_class_str = '';
        $card_use_goods_arr = [];

        foreach ($card_info_list as $cc_gv) {
            // 处理SKU关联
            if ($cc_gv['set_sku_ids']) {
                foreach (explode(',', $cc_gv['set_sku_ids']) as $cc_gv_sku) {
                    if ($cc_gv_sku) {
                        $card_use_goods_arr[$cc_gv['card_id']][$cc_gv['commodity_id']][] = $cc_gv_sku;
                        $card_set_sku_ids[] = $cc_gv_sku;
                    }
                }
            }
            // 处理商品关联
            elseif ($cc_gv['commodity_id']) {
                $card_use_goods_arr[$cc_gv['card_id']][$cc_gv['commodity_id']][] = $this->te_card_num ?? 999;
                $card_cc_goods_ids[] = $cc_gv['commodity_id'];
            }

            // 处理分类关联
            if ($cc_gv['class_id']) {
                $card_class_str .= sprintf("find_in_set('%s', a.comm_type_id_str) ||", $cc_gv['class_id']);
            }
        }

        // 构建查询条件
        $card_set_sku_ids_str = implode(',', $card_set_sku_ids);
        $card_cc_goods_ids_sql = '';

        if ($card_cc_goods_ids) {
            $card_cc_goods_ids_arr = implode(',', $card_cc_goods_ids);
            if ($card_class_str) {
                $card_cc_goods_ids_sql = sprintf(" || a.commodity_id in (%s)", $card_cc_goods_ids_arr);
            } else {
                $card_cc_goods_ids_sql = sprintf("a.commodity_id in (%s)", $card_cc_goods_ids_arr);
            }
        }

        // 应用查询条件
        if ($card_set_sku_ids) {
            // 如果有SKU配置，在后续处理中过滤，这里不添加WHERE条件
            $card_business_data['card_set_sku_ids'] = $card_set_sku_ids;
        } else {
            // 只有商品或分类关联时，直接添加WHERE条件
            if ($card_class_str || $card_cc_goods_ids_sql) {
                $condition = sprintf("(%s %s)", trim($card_class_str, '||'), $card_cc_goods_ids_sql);
                $where[] = ['exp', $condition];
                $count_where[] = ['exp', $condition];
            }
        }

        $card_business_data['card_use_goods_arr'] = $card_use_goods_arr;
    }

    /**
     * 处理遗漏的重要查询条件
     * 包括：n_dis_id, full_cut_id, 价格区间, sku_ids 等
     */
    private function handleMissingConditions($params, &$where, &$count_where, &$business_data)
    {
        $time = date('Y-m-d H:i:s');

        // 1. 处理 n_dis_id (N件N折活动)
        if (!empty($params['n_dis_id'])) {
            $com_id_list = $this->getNDisCountNid($params['n_dis_id']);
            if ($com_id_list) {
                $dis_model = new \app\common\model\db\DbNDiscount();
                $n_dis = $dis_model->getOneByPk($params['n_dis_id']);
                if ($n_dis) {
                    $business_data['act_title'] = $n_dis['title'];
                }
                // 将活动商品ID添加到商品ID条件中
                $existing_commodity_ids = $params['commodity_ids'] ?? '';
                $params['commodity_ids'] = $existing_commodity_ids . ',' . $com_id_list['g_ids'];
                $where[] = ['exp', sprintf("a.commodity_id in (%s)", trim($params['commodity_ids'], ','))];
            }
        }

        // 2. 处理 full_cut_id (满减活动)
        if (!empty($params['full_cut_id'])) {
            $cut_model = new \app\common\model\db\DbFullDiscount();
            $cut_where = [
                "a.id" => $params['full_cut_id'],
                'a.start_time' => ['<=', $time],
                'a.end_time' => ['>=', $time]
            ];
            $cut_where[] = ['exp', sprintf("find_in_set('%s', a.up_down_channel_dlr)", $this->channel_type)];
            $full_cut_info = $cut_model->getOneU([
                "where" => $cut_where,
                'field' => "a.id,GROUP_CONCAT(b.commodity_id SEPARATOR ',') commodity_ids,a.activity_title"
            ]);

            if ($full_cut_info && $full_cut_info['commodity_ids']) {
                $existing_commodity_ids = $params['commodity_ids'] ?? '';
                $params['commodity_ids'] = $existing_commodity_ids . ',' . $full_cut_info['commodity_ids'];
                $where[] = ['exp', sprintf("a.commodity_id in (%s)", trim($params['commodity_ids'], ','))];

                $business_data['act_title'] = $full_cut_info['activity_title'] ?: '满减活动';
            }
        }

        // 3. 处理价格区间过滤
        if (!empty($params['price_start'])) {
            $where[] = ['exp', sprintf("c.price >= %s", $params['price_start'])];
            $count_where[] = ['exp', sprintf("final_price >= %s", $params['price_start'])];
            $business_data['price_start'] = $params['price_start'];
        }

        if (!empty($params['price_end'])) {
            $where[] = ['exp', sprintf("c.price <= %s", $params['price_end'])];
            $count_where[] = ['exp', sprintf("final_price <= %s", $params['price_end'])];
            $business_data['price_end'] = $params['price_end'];
        }

        // 4. 处理 sku_ids 条件
        if (!empty($params['sku_ids'])) {
            if (is_string($params['sku_ids'])) {
                $sku_ids = explode(',', $params['sku_ids']);
            } else {
                $sku_ids = (array)$params['sku_ids'];
            }
            $where['c.id'] = ['in', $sku_ids];
            $business_data['sku_ids'] = $sku_ids;
        }

        // 5. 处理销售渠道条件
        if (!isset($params['miao_sha']) && empty($params['card_id'])) {
            $where[] = ['exp', "find_in_set(1, a.sales_channel)"];
        }
    }

    /**
     * 获取优化的商品列表
     */
    private function getOptimizedGoodsList($params, $where, $count_where, $business_data, $api_start_at)
    {
        $flat = new DbCommodityFlat();

        // 构建字段和排序 - 包含所有关键字段
        $field = "a.commodity_id,a.commodity_name,a.tag,a.tag_gwnet,a.tag_gwapp,a.is_pure,a.cover_image,a.card_id,
                 b.count_stock,a.sales_channel,a.cheap_dis,a.group_dis,a.full_dis,a.limit_dis,a.seckill_dis,a.n_dis,a.pre_dis,
                 a.car_series_id,a.seckill_dis,min(c.price) price,min(c.price) final_price,b.max_point,b.pay_style,
                 a.tag_pz1asm,a.tag_pz1aapp,a.tag_qcsm,a.tag_qcapp,a.is_grouped,b.commodity_label,
                 GROUP_CONCAT(c.id) gc_id,GROUP_CONCAT(c.price) gc_price,b.group_commodity_ids_info,b.is_sp_associated,
                 a.commodity_set_id,b.qsc_group,b.qsc_group_price,b.first_free_price,b.qsc_group_num,b.qsc_group_name,
                 a.gift_dis,a.dd_commodity_type,b.is_store,a.comm_type_id,GROUP_CONCAT(c.stock) gc_stock,
                 c.relate_car_ids,b.listing_type,a.activity_image,a.comm_type_id_str,b.tag_zdy,b.mail_type mail_method,
                 GROUP_CONCAT(d.sku_code) d_sku_code,GROUP_CONCAT(d.rep_part_no) d_rep_part_no,
                 GROUP_CONCAT(CONCAT(d.commodity_id,d.sp_value_list) SEPARATOR ';') ss_plist,
                 GROUP_CONCAT(d.price) gc_old_price,GROUP_CONCAT(d.maintain_q) gc_maintain_q,
                 GROUP_CONCAT(d.variety_code) d_v_code";

        $order = $this->buildOrderClause($params);

        // 构建查询参数
        $query_params = [
            'where' => $where,
            'group' => "a.commodity_id",
            'pagesize' => $params['page_size'],
            'order' => $order,
            'field' => $field,
            'query' => ['page' => $params['page']]
        ];

        // 执行查询
        Logger::error($api_start_at . '-goodslistuntime-1.1', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds");
        $count = $flat->alias('a')->where($count_where)->count();
        $query_params['count'] = $count;

        Logger::error($api_start_at . '-goodslistuntime-2', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds");
        $list = $flat->getCommodityListUSku($query_params);

        return ['list' => $list, 'total' => $count];
    }

    /**
     * 处理完整的商品数据 - 保留所有原始业务逻辑
     */
    private function processCompleteGoodsData($list_data, $user, $channel_type, $params, $from, $type, $api_start_at, $business_data = [])
    {
        $list = $list_data['list'];
        if (empty($list)) {
            return ['data' => []];
        }

        // 收集商品相关ID
        $card_id_arr = [];
        $goods_set_id_arr = [];
        foreach ($list as $kk => $tmp_vv) {
            $list[$kk]['is_pp'] = 1;
            $old_cover_image = $list[$kk]['cover_image'];
            if (!empty($tmp_vv['activity_image'])) {
                $list[$kk]['cover_image'] = $tmp_vv['activity_image'];
            }

            if (in_array($from, ['special', 'home']) && $type == 'cGoods') {
                $list[$kk]['cover_image'] = $old_cover_image;
            }

            if ($tmp_vv['card_id']) {
                $card_id_arr = array_merge($card_id_arr, explode(',', $tmp_vv['card_id']));
            }
            $goods_set_id_arr[] = $tmp_vv['commodity_set_id'];
        }

        // 获取卡券数据 - 保留原始逻辑
        $card_get_use = $this->getCardDataOptimized($goods_set_id_arr, $card_id_arr, $user, $channel_type, $params, $from);

        // 批量获取SKU数据 - 优化N+1查询
        $all_gc_ids = [];
        foreach ($list as $v) {
            if ($v['gc_id']) {
                $all_gc_ids .= $v['gc_id'] . ',';
            }
        }

        $more_sku_data = [];
        if ($all_gc_ids) {
            $more_sku_data = $this->getBatchSkuData(trim($all_gc_ids, ','));
        }

        // 预处理组合商品，收集需要删除的商品ID（与原始代码逻辑一致）
        $g_must_delete = $this->preProcessGroupedGoods($list, $user, $business_data);

        // 处理每个商品 - 保留完整业务逻辑，包含卡券SKU过滤
        $processed_list = [];
        foreach ($list as $k => $v) {
            // 组合商品必选商品过滤（与原始代码逻辑一致）
            if (in_array($v['commodity_id'], $g_must_delete)) {
                continue; // 跳过不符合组合商品条件的商品
            }

            // 卡券SKU过滤逻辑
            if (!empty($business_data['card_set_sku_ids']) && !empty($params['card_id'])) {
                $gc_id_arr = explode(',', $v['gc_id']);
                $has_valid_sku = false;
                foreach ($gc_id_arr as $gc_id) {
                    if (in_array($gc_id, $business_data['card_set_sku_ids'])) {
                        $has_valid_sku = true;
                        break;
                    }
                }
                if (!$has_valid_sku) {
                    continue; // 跳过不符合卡券SKU条件的商品
                }
            }

            $processed_item = $this->processGoodsItem($v, $more_sku_data, $card_get_use, $user, $params, $business_data);
            if ($processed_item) {
                $processed_list[] = $processed_item;
            }
        }

        Logger::error($api_start_at . '-goodslistuntime-complete', number_format(microtime(true) - $api_start_at, 10, '.', '') . " seconds");

        return [
            'data' => $processed_list,
            'user_info' => $card_get_use['user_info'] ?? [],
            'get_card_list' => $card_get_use['get_card_list'] ?? [],
            'goods_card_rule' => $card_get_use['goods_card_rule'] ?? [],
            'all_card' => $card_get_use['all_card'] ?? [],
            'no_list_tip' => $business_data['no_list_tip'] ?? ''
        ];
    }

    /**
     * 获取卡券数据 - 优化版本但保留完整逻辑
     */
    private function getCardDataOptimized($goods_set_id_arr, $card_id_arr, $user, $channel_type, $params, $from)
    {
        if (!$from) {
            $from = 'goodslist';
        }

        $card_in_attr = [];
        if ($params['card_id']) {
            $card_in_attr = $params['card_id'];
        } else {
            if ($card_id_arr) {
                $card_in_attr = $card_id_arr;
            }
        }

        if ($card_in_attr) {
            return $this->card_get_use($goods_set_id_arr, [], $user, $channel_type, $card_in_attr, '', 1, [], '', $from, '', 99, $params['use_gift_card']);
        } else {
            return [
                'get_card_list' => [],
                'all_card' => [],
                'card_rules' => [],
                'goods_card_rule' => [],
                'user_info' => $this->getFriendBaseInfo($user)
            ];
        }
    }

    /**
     * 批量获取SKU数据 - 优化N+1查询
     */
    private function getBatchSkuData($gc_ids_str)
    {
        $cache_key = 'batch_sku_data_' . md5($gc_ids_str);
        $cached_data = Cache::get($cache_key);
        if ($cached_data !== false) {
            return $cached_data;
        }

        $sku_model = new \app\common\model\db\DbCommoditySku();
        $more_sku_data = $sku_model->alias('a')
            ->join('t_db_commodity_set_sku b', 'a.id=b.commodity_sku_id and a.is_enable=1 and b.is_enable=1')
            ->where(['b.id' => ['in', explode(',', $gc_ids_str)]])
            ->field("a.id,a.sp_value_list,b.price,b.id,b.id bid,a.sku_code,a.variety_code,a.maintain_q,b.commodity_id")
            ->order('b.price DESC,a.id DESC,b.id DESC')
            ->group('b.commodity_id,a.id')
            ->select();

        $grouped_data = [];
        if ($more_sku_data) {
            foreach ($more_sku_data as $item) {
                $grouped_data[$item['commodity_id']][] = $item;
            }
        }

        Cache::set($cache_key, $grouped_data, 300); // 缓存5分钟
        return $grouped_data;
    }

    /**
     * 处理单个商品项 - 保留完整业务逻辑，包含maintain_q和relate_car_ids处理
     */
    private function processGoodsItem($goods, $more_sku_data, $card_get_use, $user, $params, $business_data = [])
    {
        $commodity_id = $goods['commodity_id'];
        $de_sku_id_arr = $more_sku_data[$commodity_id] ?? [];

        if (!$de_sku_id_arr) {
            return null; // 没有SKU数据的商品跳过
        }

        // 处理SKU数据
        $gift_card_sku_code = [];
        $gift_card_variety_code = [];
        $de_sku_id_list = [];
        $gc_price_arr_new = [];
        $gc_maintain_q_arr_new = [];

        foreach ($de_sku_id_arr as $de_v) {
            if ($de_v['sku_code']) {
                foreach (explode(',', $de_v['sku_code']) as $sku_c_v) {
                    if ($sku_c_v) {
                        $gift_card_sku_code[] = $sku_c_v;
                    }
                }
                foreach (explode(',', $de_v['variety_code']) as $sku_c_v) {
                    if ($sku_c_v) {
                        $gift_card_variety_code[] = $sku_c_v;
                    }
                }
            }
            $de_sku_id_list[$de_v['sp_value_list']] = $de_v['bid'];
            $gc_price_arr_new[$de_v['sp_value_list']] = $de_v['price'];
            $gc_maintain_q_arr_new[$de_v['sp_value_list']] = $de_v['maintain_q'];
        }

        if (empty($gc_price_arr_new)) {
            return null;
        }

        $one_price = min($gc_price_arr_new);
        $re_m_q_sku_k = array_search($one_price, $gc_price_arr_new);
        $maintain_q = $gc_maintain_q_arr_new[$re_m_q_sku_k] ?? '';

        // 关键：处理保养折扣价格 - maintain_q逻辑
        $display_price = $one_price;
        if (in_array($goods['dd_commodity_type'], [1, 3, 4, 41]) && $maintain_q && $maintain_q > 0) {
            $display_price = sprintf("%.2f", bcdiv($one_price, $maintain_q / 10, 0));
        }

        // 处理车型适配 - relate_car_ids逻辑
        $is_have_car = 1;
        $car_s_id = $user['car_series_id'] ?? 0;
        if (!empty($goods['relate_car_ids']) && empty($car_s_id)) {
            $is_have_car = 0;
        }

        // 构建商品基础信息
        $processed_item = [
            'commodity_id' => $goods['commodity_id'],
            'commodity_name' => $goods['commodity_name'],
            'cover_image' => $goods['cover_image'],
            'commodity_set_id' => $goods['commodity_set_id'],
            'price' => formatNumber($display_price),
            'final_price' => formatNumber($display_price),
            'current_price' => formatNumber($display_price),
            'count_stock' => $goods['count_stock'],
            'is_grouped' => $goods['is_grouped'],
            'dd_commodity_type' => $goods['dd_commodity_type'],
            'listing_type' => $goods['listing_type'],
            'maintain_q' => $maintain_q,
            'de_sku_id' => $de_sku_id_list[$re_m_q_sku_k] ?? '',
            'is_pp' => 1,
            'is_have_car' => $is_have_car,
            'tag_name' => $this->processGoodsTags($goods, $params),
            'card_list' => [],
            'gift_card_sku_code' => $gift_card_sku_code,
            'gift_card_variety_code' => $gift_card_variety_code
        ];

        // 处理卡券信息
        $card_rules = $card_get_use['goods_card_rule'] ?? [];
        $card_rule = $card_rules[$goods['commodity_set_id']] ?? [];
        if ($card_rule) {
            $processed_item['card_list'] = $card_rule;
        }

        // 处理组合商品的车型适配
        if ($goods['is_grouped']) {
            $processed_item = $this->processGroupedGoods($processed_item, $goods, $params, $user, $business_data);
            if (!$processed_item) {
                return null; // 组合商品不符合显示条件
            }
        }

        // 处理价格过滤 - 包含价格区间过滤
        if (!$this->checkPriceFilter($processed_item, $params)) {
            return null;
        }

        return $processed_item;
    }

    /**
     * 处理组合商品 - 完整实现必选/非必选商品逻辑
     */
    private function processGroupedGoods($processed_item, $goods, $params, $user, $business_data)
    {
        $processed_item['is_grouped'] = 1;

        // 解析组合商品配置信息
        $group_commodity_ids_info = json_decode($goods['group_commodity_ids_info'], true);
        if (!$group_commodity_ids_info) {
            return null; // 没有组合配置的组合商品不显示
        }

        // 获取当前商品的SKU ID数组
        $gc_id_arr = explode(',', $goods['gc_id']);

        // 查询匹配的子商品SKU
        $set_sku_group = $this->set_sku_model->getColumn([
            'where' => ['id' => ['in', array_values(array_unique($gc_id_arr))]],
            'column' => 'group_sub_set_sku_id'
        ]);

        $goods_id_group = $this->set_sku_model->getColumn([
            'where' => ['id' => ['in', array_values(array_unique($gc_id_arr))]],
            'column' => 'group_sub_commodity_id'
        ]);

        // 分析必选和非必选商品
        $analysis_result = $this->analyzeGroupedGoodsRequirements(
            $group_commodity_ids_info,
            $set_sku_group,
            $user,
            $business_data
        );

        if (!$analysis_result['can_display']) {
            return null; // 不符合显示条件的组合商品
        }

        // 查询实际可用的子商品
        $available_sub_goods = $this->getAvailableSubGoods($goods, $gc_id_arr, $user, $business_data);

        // 验证必选商品是否都有可用的子商品
        if (!$this->validateRequiredSubGoods($analysis_result['required_goods'], $available_sub_goods)) {
            return null; // 必选商品不满足条件
        }

        // 验证全部为非必选商品的情况
        if ($analysis_result['all_optional'] && empty($available_sub_goods)) {
            return null; // 全部为非必选商品但没有可用子商品
        }

        $processed_item['group_analysis'] = $analysis_result;
        $processed_item['available_sub_goods_count'] = count($available_sub_goods);

        return $processed_item;
    }

    /**
     * 预处理组合商品，收集需要删除的商品ID
     * 与原始代码中的 g_must_delete 逻辑完全一致
     */
    private function preProcessGroupedGoods($list, $user, $business_data)
    {
        $g_must_delete = [];
        $car_s_id = $user['car_series_id'] ?? 0;

        foreach ($list as $v) {
            if (!$v['is_grouped']) {
                continue; // 跳过非组合商品
            }

            $group_commodity_ids_info = json_decode($v['group_commodity_ids_info'], true);
            if (!$group_commodity_ids_info) {
                continue;
            }

            $gc_id_arr = explode(',', $v['gc_id']);

            // 查询匹配的子商品SKU
            $set_sku_group = $this->set_sku_model->getColumn([
                'where' => ['id' => ['in', array_values(array_unique($gc_id_arr))]],
                'column' => 'group_sub_set_sku_id'
            ]);

            $g_all_goods_id = [];
            $isallcancount = 0;
            $isallcancountids = [];

            foreach ($group_commodity_ids_info as $g_vv) {
                // 处理1L机油商品的特殊逻辑
                if ($g_vv['machine_oil_type'] == 1) {
                    if ($car_s_id) {
                        if ($user['18_oil_type'] > 4) {
                            $g_vv['initial_num'] = $user['18_oil_type'] - 4;
                        } else {
                            $g_vv['initial_num'] = 0;
                        }
                    } else {
                        $g_vv['initial_num'] = 1;
                    }
                }

                // 收集必选商品ID
                if ($g_vv['initial_num'] > 0 && !$g_vv['can_select']) {
                    $g_all_goods_id[] = $g_vv['commodity_id'];
                }

                $set_sku_id_arr = array_column($g_vv['sku_list'], 'group_sub_set_sku_id');
                $sku_jj = array_intersect($set_sku_id_arr, $set_sku_group);

                // 检查库存
                $must_info = $this->set_sku_model->whereIn('id', $set_sku_id_arr)
                    ->where(['is_enable' => 1, "stock" => ['>', 0]])
                    ->find();

                // 必选商品逻辑
                if ($g_vv['can_select'] == 0 && $g_vv['initial_num'] > 0) {
                    if (!empty($set_sku_id_arr)) {
                        if (!$sku_jj) {
                            $g_must_delete[] = $v['commodity_id'];
                        } else {
                            if (empty($must_info)) {
                                $g_must_delete[] = $v['commodity_id'];
                            }
                        }
                    }
                } else {
                    // 非必选商品
                    $isallcancount++;
                    foreach ($g_vv['sku_list'] as $item) {
                        $isallcancountids[] = $item['group_sub_set_sku_id'];
                    }

                    // 特殊渠道的处理
                    if (in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
                        if (!empty($set_sku_id_arr)) {
                            if (empty($must_info)) {
                                $g_must_delete[] = $v['commodity_id'];
                            }
                        }
                    }
                }
            }

            // 全部为非必选商品的情况
            if ($isallcancount == count($group_commodity_ids_info)) {
                $must_info = $this->set_sku_model->whereIn('id', $isallcancountids)
                    ->where(['is_enable' => 1, "stock" => ['>', 0]])
                    ->find();
                if (empty($must_info)) {
                    $g_must_delete[] = $v['commodity_id'];
                }
            }

            // 检查必选商品数量与实际可用子商品数量
            $group_where = [
                'commodity_id' => $v['commodity_id'],
                'is_enable' => 1,
                'id' => ['in', $gc_id_arr]
            ];

            if ($car_s_id && !in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
                $car_s_id_int = intval($car_s_id);
                $group_where[] = ['exp', sprintf("(find_in_set(%s,relate_car_ids) || relate_car_ids='')", $car_s_id_int)];
            }

            $group_list = $this->set_sku_model->getList([
                'where' => $group_where,
                'field' => 'group_sub_commodity_id',
                'group' => 'group_sub_commodity_id'
            ]);

            $list_sub_goods_arr = array_column($group_list, 'group_sub_commodity_id');

            // 如果必选商品数量大于实际可用子商品数量，则不显示
            if (count($g_all_goods_id) > count($list_sub_goods_arr)) {
                $g_must_delete[] = $v['commodity_id'];
            }
        }

        return array_unique($g_must_delete);
    }

    /**
     * 分析组合商品的必选/非必选商品要求
     * 核心逻辑：如果必选子商品不匹配或者没库存那么整个组合商品不显示
     * 如果全部为非必选商品那么没有子商品就要不显示组合商品
     */
    private function analyzeGroupedGoodsRequirements($group_commodity_ids_info, $set_sku_group, $user, $business_data)
    {
        $required_goods = []; // 必选商品ID
        $optional_goods = []; // 非必选商品ID
        $all_goods = [];
        $can_display = true;
        $car_s_id = $user['car_series_id'] ?? 0;

        foreach ($group_commodity_ids_info as $g_vv) {
            $commodity_id = $g_vv['commodity_id'];
            $all_goods[] = $commodity_id;

            // 处理1L机油商品的特殊逻辑
            if ($g_vv['machine_oil_type'] == 1) {
                if ($car_s_id) {
                    if ($user['18_oil_type'] > 4) {
                        $g_vv['initial_num'] = $user['18_oil_type'] - 4;
                    } else {
                        $g_vv['initial_num'] = 0;
                    }
                } else {
                    $g_vv['initial_num'] = 1;
                }
            }

            // 判断是否为必选商品：initial_num > 0 且 can_select = 0
            if ($g_vv['initial_num'] > 0 && !$g_vv['can_select']) {
                $required_goods[] = $commodity_id;

                // 检查必选商品是否有匹配的SKU
                $set_sku_id_arr = array_column($g_vv['sku_list'], 'group_sub_set_sku_id');
                $sku_jj = array_intersect($set_sku_id_arr, $set_sku_group);

                // 如果必选商品没有匹配的SKU，整个组合商品不显示
                if (!$sku_jj) {
                    $can_display = false;
                    break;
                }

                // 检查必选商品是否有库存
                $must_info = $this->set_sku_model->whereIn('id', $set_sku_id_arr)
                    ->where(['is_enable' => 1, "stock" => ['>', 0]])
                    ->find();

                if (empty($must_info)) {
                    $can_display = false;
                    break;
                }
            } else {
                $optional_goods[] = $commodity_id;
            }
        }

        // 如果全部为非必选商品，需要特殊处理
        $all_optional = (count($required_goods) == 0);

        return [
            'can_display' => $can_display,
            'required_goods' => $required_goods,
            'optional_goods' => $optional_goods,
            'all_goods' => $all_goods,
            'all_optional' => $all_optional
        ];
    }

    /**
     * 获取可用的子商品
     */
    private function getAvailableSubGoods($goods, $gc_id_arr, $user, $business_data)
    {
        $car_s_id = $user['car_series_id'] ?? 0;

        $group_where = [
            'commodity_id' => $goods['commodity_id'],
            'is_enable' => 1,
            'id' => ['in', $gc_id_arr]
        ];

        $group_field = 'min(price) ff_price,group_sub_commodity_id,GROUP_CONCAT(group_sub_set_sku_id) sub_sku_ids,relate_car_ids,GROUP_CONCAT(id) set_sku_ids';

        // 添加车型过滤条件
        if ($car_s_id && !in_array($this->channel_type, ['PZ1ASM', 'PZ1AAPP'])) {
            $car_s_id = intval($car_s_id);
            $group_where[] = ['exp', sprintf("(find_in_set(%s,relate_car_ids) || relate_car_ids='')", $car_s_id)];
        }

        $group_list = $this->set_sku_model->getList([
            'where' => $group_where,
            'field' => $group_field,
            'group' => 'group_sub_commodity_id'
        ]);

        return $group_list ?: [];
    }

    /**
     * 验证必选商品是否都有可用的子商品
     */
    private function validateRequiredSubGoods($required_goods, $available_sub_goods)
    {
        if (empty($required_goods)) {
            return true; // 没有必选商品，验证通过
        }

        $available_goods_ids = array_column($available_sub_goods, 'group_sub_commodity_id');

        // 检查所有必选商品是否都在可用子商品中
        foreach ($required_goods as $required_id) {
            if (!in_array($required_id, $available_goods_ids)) {
                return false; // 有必选商品不可用
            }
        }

        return true;
    }

    /**
     * 检查价格过滤条件 - 包含价格区间过滤
     */
    private function checkPriceFilter($processed_item, $params)
    {
        // 价格区间过滤 - 与原始代码逻辑一致
        if (!empty($params['price_start'])) {
            $final_price = floatval(str_replace(',', '', $processed_item['final_price']));
            if ($final_price < $params['price_start']) {
                return false;
            }
        }

        if (!empty($params['price_end'])) {
            $final_price = floatval(str_replace(',', '', $processed_item['final_price']));
            if ($final_price > $params['price_end']) {
                return false;
            }
        }

        return true;
    }
}
