<?php


namespace app\common\validate;


use think\Validate;

class CrowdfundValidate extends Validate
{

    protected $rule = [
        'id'                => 'number',
        'title'             => 'require',
        'start_time'        => 'require',
        'end_time'          => 'require',
        'target'            => 'require|number|in:1,2',
        'target_val'        => 'require|number',
        'alr_crowd'         => 'require',
        'purchase_num'      => 'number',
        'is_use_ticket'     => 'number',
        'ticket_ids'        => 'max:100',
        'brand'             => 'require|number|in:1,2,3',
        'up_down_channel'   => 'require',
        'un_stand_standard' => 'require|number|in:0,1',
        'refund_set'        => 'require|number|in:0,1,2',
        'gather_id'         => 'number',
        'theme_name'        => 'max:100',
        'is_pv_subsidy'     => 'number',
        'commodity'         => 'require|array'
    ];
}