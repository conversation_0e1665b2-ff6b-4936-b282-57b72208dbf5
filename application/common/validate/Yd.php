<?php

namespace app\common\validate;

use app\common\port\connectors\YdPay;
use think\Validate;

class Yd extends Validate
{
    protected $rule = [
        'mobile'           => 'number|length:11|require',
        'type'             => 'length:0,1000|require',
        'coupon_no'        => 'length:0,1000|require',
        'points'           => 'number|require',
        'session_id'       => 'length:0,1000|require',
        'finger_print'     => 'require',
        'order_id'         => 'length:0,1000|require',
        'good_order_id'    => 'length:0,1000|require',
        'sms_code'         => 'length:0,1000|require',
        'machine_type'     => 'length:0,1000|require',
//        'sequence_id'      => 'length:0,1000|require',
        'goods_num'        => 'number',
        'callbackUrl'      => 'length:0,1000|require',
        'sku_ids'          => 'require|length:0,1000',
        'numbers'          => 'require|length:0,1000',
        'mail_method'      => 'require|length:0,1000',
        'promotion_source' => 'require|in:6',
        'name'             => 'length:0,1000',
        'phone'            => 'alphaDash',
        'address'          => 'length:0,1000',
        'goods_price'      => 'require',
        'is_nev'           => 'number|in:0,1',
    ];
    protected $scene = [
        'auth'          => ['mobile', 'type', 'callbackUrl', 'session_id', 'finger_print'],
        'place-order'   => ['mobile', 'type', 'coupon_no', 'session_id', 'finger_print', 'goods_num', 'goods_price'],
        'send-sms'      => ['mobile', 'type', 'order_id'],
        'dec-order'     => ['type', 'mobile', 'orderId', 'good_order_id', 'sms_code', 'points', 'session_id', 'machine_type', 'fingerprint', 'mobile'],
        'fill'          => ['sku_ids', 'numbers', 'mail_method', 'promotion_source', 'name', 'mobile', 'phone', 'points', 'address'],
        'order-confirm' => ['sku_ids'],
        'order-detail'  => ['order_id'],
    ];

    protected $message = [
        'mobile.require'        => '手机号必填',
        'type.require'          => '客户端类型必填',
        'coupon_no.require'     => '商品编号必填',
        'points.require'        => '扣减积分必填',
        'session_id.require'    => '通付盾参数必填',
        'finger_print.require'  => '同盾设备指纹必填',
        'order_id.require'      => '订单号必填',
        'good_order_id.require' => '商品订单号必填',
        'sms_code.require'      => '短信验证码必填',
        'machine_type.require'  => '设备类型必填',
        'sequence_id.require'   => '下单id必填',
        'goods_num.require'     => '商品数量必填',
        'callbackUrl.require'   => '回调地址必填',
        'points.number'         => '扣减积分有误',
        'sku_ids.require'       => '规格必填',
        'sku_ids.length'        => '规格长度限制',
        'numbers.require'       => '数量必填',
        'numbers.length'        => '数量长度限制',
        'mail_method.in'        => '快递类型有误',
        'mail_method.require'   => '快递类型必填',
        'goods_price.require'   => '价格必填',
    ];
}