<?php

namespace app\common\validate;

use think\Validate;

/**
 * 商品相关 验证器
 * Class Goods
 * @package app\common\validate
 */
class Goods extends Validate
{
    /**
     * @var string[]
     */
    protected $rule = [
        'page'             => 'number|gt:0',
        'pageSize'         => 'number|gt:0',
        'comm_type_id'     => 'number|gt:0',
        'com_s_types'      => 'length:0,1000',
        'car_id'           => 'number',
        'search'           => 'chsDash|length:0,1000',
        'commodity_ids'    => 'length:0,1000',
        'card_id'          => 'number|gt:0',
        'price_start'      => 'float',
        'price_end'        => 'float',
        'n_dis_id'         => 'number|gt:0',
        'full_cut_id'      => 'number|gt:0',
        'goods_id'         => 'require|number|gt:0',
        'group_id'         => 'number|gt:0',
        'group_order_code' => 'chsDash|length:0,1000',
        'order_id'         => 'require|number|gt:0',
        'afs_id'           => 'number|gt:0',
        'address'          => 'require',
        'sku_key'          => 'require',
        'is_group'         => 'number|between:1,0',
        'is_suit'          => 'number|between:1,0',
        'suit_id'          => 'number|gt:0',
        'dlr_code'         => 'length:0,1000',
        'car_series_id'    => 'number|gt:0',
        'sale_num'         => 'number|gt:0',
        'ask_at'           => 'number',
        'car_18n'          => 'length:0,1000',
        'work_hour_type'   => 'length:0,1000',
        'sku_ids'   => 'length:0,1000',
        'sku_num'   => 'length:0,1000',
        'dd_dlr_code'      => 'length:0,10000',
        'sub_goods_id'      => 'number',
        'lng'    => 'float|length:0,1000',
        'lat'    => 'float|length:0,1000',
        'hit_type_code'  =>'length:0,100',
        'kilometer'         => 'number',
//        'activity_id'         => 'require|number|gt:0',
        'gift_id'           => 'number',
        'get_dd_commodity_type'           => 'number',//是否只获取dd_commodity_type
        'commodity_id' => 'require',
        'sku_id' => 'number',
        'set_sku_id'    => 'number',
        'old_price'     => 'require',
        'activity_price'=> 'number',
        'member_price'=> 'number',
        'activity_id'=> 'number',
        'activity_type'=> 'number',
         'count' => 'number',
        'nvi_vin'=> 'length:0,20',
        'price'=> 'float',
        'new_comm_type_id'     => 'number|gt:0',
        'use_discount' => 'number',
        'entry_from' => 'number',
        'act_type_id' => 'number',
        'r_set_sku_id' => 'number',
        'source_special'        => 'length:0,1000',
        'sku_json'        => 'length:0,1000',
        'cart_id'        => 'number',
        'gift_card_id'        => 'number',
        'gift_card_main'        => 'number',
        'new_order'             => 'length:0,1000'

    ];


    protected $scene = [
        'list'            => ['page', 'pageSize', 'comm_type_id', 'search', 'commodity_ids', 'card_id', 'price_start', 'price_end', 'n_dis_id', 'full_cut_id', 'com_s_types', 'ask_at','dd_dlr_code','kilometer','lng','lat','new_comm_type_id','gift_card_main','new_order'],
        'detail'          => ['nvi_vin','goods_id', 'group_id', 'group_order_code','dd_dlr_code','lng','lat','kilometer','get_dd_commodity_type','r_set_sku_id','source_special'],
        'waybill'         => ['order_id', 'afs_id'],
        'suit'            => ['goods_id'=>'length:0,1000','suit_id','dd_dlr_code','lng','lat','kilometer','sku_ids'],
        'suit_price'            => ['goods_id'=>'require|length:0,1000','suit_id','dd_dlr_code','lng','lat','kilometer','sku_ids'],
        'suit_detail'            => ['suit_id'],
        'mail_price'      => ['goods_id', 'address'],
        'save_collection' => ['goods_id','hit_type_code'],
        'change_suit'     => ['goods_id', 'suit_id','dd_dlr_code','sub_goods_id', 'use_discount', 'act_type_id', 'entry_from','nvi_vin','cart_id','gift_card_id'],
        'sku_info'        => ['goods_id', 'sku_key', 'is_group', 'is_suit'],
        'goodsHot'        => ['pageSize', 'dlr_code'],
        'goodsCarMate'    => ['car_series_id', 'goods_id'],
        'one_list'        => ['page', 'pageSize', 'search', 'commodity_ids', 'sale_num'],
        'getWiPrice'      => ['dlr_code' => 'require', 'car_18n' => 'require', 'sku_ids' => 'require', 'sku_num' => 'require'],
        'giftList'        => ['goods_id','activity_id','dd_dlr_code'],
        'seckill_user'    => ['goods_id'],
        'sku_detail'      => ['commodity_id' => 'require','sku_id' => 'require'],
        'best_activity_card'=>['count','commodity_id','set_sku_id','old_price','activity_price','member_price','activity_id',"activity_type",'sku_json'],
        'point_js'=>['goods_id','price'=>'require|float'],
        'gift_card'=>['card_id'=>'require'],
        'gift_card_num'=>['sku_json','sku_id'],
        'check_gift_card'=>['sku_id'],
    ];

    /**
     * @var string[]
     */
    protected $message = [
        'page.number'          => '分页有误',
        'pageSize.number'      => '分页数据有误',
        'comm_type_id.number'  => '分类ID有误',
        'car_id.number'        => '车型ID有误',
        'search.length'        => '搜索关键字长度不在范围内',
        'search.chsDash'       => '搜索关键字不合法',
        'commodity_ids.length' => '商品ID长度不在范围内',
        'com_s_types.length'   => '类别ID长度不在范围内',
        'dlr_code.length'      => '编码长度不在范围内',
        'card_id.number'       => '优惠券ID有误',
        'gift_card_id.number'       => '赠券ID有误',
        'price_start.float'    => '开始价格格式有误',
        'price_end.float'      => '结束价格格式有误',
        'n_dis_id.number'      => '活动ID格式有误',
        'full_cut_id.number'   => '活动ID格式有误',

        'goods_id.require'        => '商品ID必填',
        'goods_id.number'         => '商品ID格式有误',
        'group_id.number'         => '团购ID格式有误',
        'group_order_code.length' => '团购ID长度不在范围内',
        'dd_dlr_code.length' => '到店专营店不在范围内',

        'order_id.require' => '订单ID必填',
        'order_id.number'  => '订单ID格式有误',

        'afs_id.number' => '售后ID格式有误',

        'address.require' => '省市地址必填',

        'sku_key.require'        => '规格值必填',
        'is_group.number'        => '是否团购格式有误',
        'is_suit.number'         => '是否套餐格式有误',
        'suit_id.number'         => '套餐格式有误',
        'sale_num'               => '销量格式有误',
        'car_series_id.number'   => '车型格式有误',
        'dlr_code.require'       => '经销商编码必填',
        'work_hour_type.require' => '工时业务类别必填',
        'car_18n.require'        => '18位码必填',
        'sku_ids.require'        => 'sku_ids必填',
        'sku_num.require'        => 'sku_num必填',
        'sub_goods_id.number'        => 'sub_goods_id错误',
        'lng.length' => '经度错误',
        'lat.length' => '纬度错误',
        'hit_type_code.number'=>'埋点编码有误',
        'kilometer.number'=>'公里数有误',
        'activity_id.number' => '买赠ID必传',
        'get_dd_commodity_type.number' => '求类型必传',
        'set_sku_id.require'=> '上架ID必传',
        'old_price.require'          => '原价必传',
        'activity_price.require'     => '活动价必传',
        'member_price.require'       => '会员价必传',
        'activity_id.require'        => '活动id',
        'activity_type.require'      => '活动类型',
        'commodity_id.require' => '商品尖必须',
        'price.float'      => '价格格式有误',
        'r_set_sku_id.number'      => '分页数据有误',
        'source_special.length'       => '专题来源异常',
        'sku_json.length'       => 'sku异常',
        'cart_id.number'       => '购物车ID异常',
        'gift_card_main.number'       => '主品待激活券',
        'new_order.length'                 => '排序长度不在范围内',
    ];

}
