<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/17
 * Time: 10:26 AM
 */

namespace app\test\controller;

use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\model\bu\BuUserCollection;
use app\common\model\db\DbCommodityFlat;
use app\common\model\db\DbNDiscountCommodity;
use app\common\model\db\DbSystemValue;
use app\common\net_service\NetGoods;
use think\Request;
use ForkModules\Traits\ResponseTrait;
use app\common\validate\Goods as GoodsValidate;
use app\net_small\controller\Common;
use hg\apidoc\annotation as Apidoc;

/**
 * 商品
 * @Apidoc\Group("mall")
 */
class Goods extends Common
{
    protected $order_model;
    protected $user;
    protected $unionid;
    private $point_times = 10;//积分倍数

    private $car_vip = ['会员金卡', '会员金卡(VIP)', '会员金卡（VIP）', '会员银卡', '会员银卡VIP', '会员银卡（VIP）', '员工卡', '铂金卡', '黑卡'];//金银卡会员登记，用于积分兑换
    private $jk_goods = [2780, 2782, 2784, 2786, 2788, 2790];
    private $pk_goods = [2779, 2781, 2783, 2785, 2787, 2789];
    //口罩商品ID
    private $kz_goods = [3790];
    private $kz_dlr = "JSJY01";//口罩专营店

    private $jk_one_point_good;

    use ResponseTrait;

    public function __construct(Request $request)
    {
        parent::__construct($request);
        $this->order_model = new BuOrder();

    }

    private function sj_ccs()
    {
        if (config('app_status') == "develop") {
            $ids = [2940, 2941];
        } else {
            $ids = [3610, 3611];
        }
        return $ids;
    }

    /**
     * @Apidoc\Title("商品列表与搜索")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/list")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 搜索 列表 NI+ PZ1A 0214")
     *
     * @Apidoc\Param("page", type="int(10)",require=false, default="1", desc="页码，默认1" )
     * @Apidoc\Param("pageSize", type="int(10)",require=false, default="20", desc="当前页显示数量,不传默认20" )
     * @Apidoc\Param("comm_type_id", type="int(10)",require=false, desc="分类ID" )
     * @Apidoc\Param("new_comm_type_id", type="int(10)",require=false, desc="新版分类ID（日产，启辰修改,pz继续使用comm_type_id）" )
     * @Apidoc\Param("car_id", type="int(10)",require=false, desc="车型ID" )
     * @Apidoc\Param("search", type="string",require=false, desc="关键词 模糊搜索" )
     * @Apidoc\Param("commodity_ids", type="int(10)",require=false, desc="商品ID，多个用英文,隔开" )
     * @Apidoc\Param("card_id", type="int(10)",require=false, desc="卡券ID，通过卡券搜索关联商品" )
     * @Apidoc\Param("price_start", type="int(10)",require=false, desc="最低价" )
     * @Apidoc\Param("price_end", type="int(10)",require=false, desc="最高价" )
     * @Apidoc\Param("n_dis_id", type="int(10)",require=false, desc="N件N折ID" )
     * @Apidoc\Param("full_cut_id", type="int(10)",require=false, desc="满减ID" )
     * @Apidoc\Param("com_s_types", type="string",require=false, desc="多个三级分类,隔开" )
     * @Apidoc\Param("ask_at", type="int",require=false, desc="请求来源，1:Pz1a首页(目前只有pz1a)" )
     * @Apidoc\Param("dd_dlr_code", type="varchar",require=false, desc="到店专营店--用户改店时候传" )
     * @Apidoc\Param("kilometer",type="string",require=false,desc="公里数")
     * @Apidoc\Param("lng", type="varchar",require=false, desc="经度" )
     * @Apidoc\Param("lat", type="varchar",require=false, desc="纬度" )
     * @Apidoc\Param("gift_card_main", type="varchar",require=false, desc="是否主品待激活,1是，跟card_id一起用" )
     * @Apidoc\Param("new_order", type="varchar",require=false, desc="新排序，sale_number 销量,price_asc 价格升序,price_desc价格降序,new_goods新品")
     *
     * @Apidoc\Returned("total", type="int(10)", desc="总条数" )
     * @Apidoc\Returned("per_page",type="int(10)",desc="页条数")
     * @Apidoc\Returned("current_page",type="int(10)",desc="当前页数")
     * @Apidoc\Returned("search_tip",type="varchar",desc="搜索分类标题")
     * @Apidoc\Returned("data",type="array/json",desc="商品列表",
     *     @Apidoc\Returned ("commodity_name",type="varchar(200)",desc="商品名称"),
     *     @Apidoc\Returned ("commodity_id",type="int(10)",desc="商品ID"),
     *     @Apidoc\Returned ("commodity_set_id",type="int(10)",desc="商品上架ID"),
     *     @Apidoc\Returned ("comm_type_id",type="int(10)",desc="商品分类ID"),
     *     @Apidoc\Returned ("car_series_id",type="int(10)",desc="车系ID"),
     *     @Apidoc\Returned ("dd_commodity_type",type="int(10)",desc="1保养套餐-老友惠保养套餐,3保养套餐-心悦保养套餐,4保养套餐-双保专属权益套餐,0非到店备件"),
     *     @Apidoc\Returned ("tag",type="string",desc="商品标签: 1热销;2推荐;3新品;4促销;5预售;10:优惠套装;11:满优惠;12:限时优惠;13:多人拼团;14:N件N折;15:预售活动;16:秒杀活动;17:赠送(0214新增)"),
     *     @Apidoc\Returned ("tag_name",type="array/json",desc="标签数组"),
     *     @Apidoc\Returned ("is_pure",type="tinyint(2)",desc="是否纯正精品1是0否"),
     *     @Apidoc\Returned ("cover_image",type="string",desc="首图"),
     *     @Apidoc\Returned ("final_price",type="decimal(10,2)",desc="最终售价"),
     *     @Apidoc\Returned ("price",type="decimal(10,2)",desc="原价"),
     *     @Apidoc\Returned ("max_final_price",type="decimal(10,2)",desc="扣最高可用积分最高价"),
     *     @Apidoc\Returned ("max_price",type="decimal(10,2)",desc="扣最高可用积分原价"),
     *     @Apidoc\Returned ("max_point",type="int(10)",desc="最高可用积分"),
     *     @Apidoc\Returned ("sort_order",type="int(10)",desc="排序"),
     *     @Apidoc\Returned ("card_id",type="string",desc="卡券ID"),
     *     @Apidoc\Returned("limit_dis", type="object", desc="限时优惠json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为限时折扣id" )
     *     ),
     *     @Apidoc\Returned("n_dis", type="json", desc="N件N折json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为N件N折id（数组）" )
     *     ),
     *     @Apidoc\Returned("group_dis", type="json", desc="团购json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为团购id（数组）" )
     *     ),
     *     @Apidoc\Returned("pre_dis", type="json", desc="预售json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为预售id（数组）" )
     *     ),
     *     @Apidoc\Returned("cheap_dis", type="json", desc="套餐json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为套餐id（数组）" )
     *     ),
     *     @Apidoc\Returned("full_dis", type="json", desc="满减json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为满减id（数组）" )
     *     ),
     *     @Apidoc\Returned("seckill_dis", type="json", desc="秒杀json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为秒杀id（数组）" )
     *     ),
     *     @Apidoc\Returned("gift_dis", type="json", desc="买赠json活动信息(0214)",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为买赠id（数组）" )
     *     ),
     *     @Apidoc\Returned ("com_s_types",type="varchar(200)",desc="三级分类ID逗号隔开多个"),
     *     @Apidoc\Returned ("commodity_label",type="varchar(20)",desc="商品标签"),
     *
     *     @Apidoc\Returned ("commodity_dis_user_segment",type="string",desc="商品折扣：0-无/1-会员价/2-车主价(0423)"),
     *     @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *     @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *     @Apidoc\Returned ("current_price",type="varchar(20)",desc="商品现价，活动非会员价(0423版本)")
     * )，
     *
     */
    public function goodsList(GoodsValidate $validate)
    {


//
    }

    /**
     * @Apidoc\Title("赠品列表")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/gift_card")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("赠品列表 搜索 列表 ")
     *

     * @Apidoc\Param("card_id", type="int(10)",require=true, desc="卡券ID" )
     *
     * @Apidoc\Returned("total", type="int(10)", desc="总条数" )
     * @Apidoc\Returned("per_page",type="int(10)",desc="页条数")
     * @Apidoc\Returned("current_page",type="int(10)",desc="当前页数")
     * @Apidoc\Returned("data",type="array/json",desc="商品列表",
     *     @Apidoc\Returned ("de_sku_id",type="varchar(200)",desc="skuid"),
     *     @Apidoc\Returned ("sp_name",type="varchar(200)",desc="规格名"),
     *     @Apidoc\Returned ("commodity_name",type="varchar(200)",desc="商品名称"),
     *     @Apidoc\Returned ("commodity_id",type="int(10)",desc="商品ID"),
     *     @Apidoc\Returned ("commodity_set_id",type="int(10)",desc="商品上架ID"),
     *     @Apidoc\Returned ("comm_type_id",type="int(10)",desc="商品分类ID"),
     *     @Apidoc\Returned ("car_series_id",type="int(10)",desc="车系ID"),
     *     @Apidoc\Returned ("dd_commodity_type",type="int(10)",desc="1保养套餐-老友惠保养套餐,3保养套餐-心悦保养套餐,4保养套餐-双保专属权益套餐,0非到店备件"),
     *     @Apidoc\Returned ("tag",type="string",desc="商品标签: 1热销;2推荐;3新品;4促销;5预售;10:优惠套装;11:满优惠;12:限时优惠;13:多人拼团;14:N件N折;15:预售活动;16:秒杀活动;17:赠送(0214新增)"),
     *     @Apidoc\Returned ("tag_name",type="array/json",desc="标签数组"),
     *     @Apidoc\Returned ("is_pure",type="tinyint(2)",desc="是否纯正精品1是0否"),
     *     @Apidoc\Returned ("cover_image",type="string",desc="首图"),
     *     @Apidoc\Returned ("final_price",type="decimal(10,2)",desc="最终售价"),
     *     @Apidoc\Returned ("price",type="decimal(10,2)",desc="原价"),
     *     @Apidoc\Returned ("max_final_price",type="decimal(10,2)",desc="扣最高可用积分最高价"),
     *     @Apidoc\Returned ("max_price",type="decimal(10,2)",desc="扣最高可用积分原价"),
     *     @Apidoc\Returned ("max_point",type="int(10)",desc="最高可用积分"),
     *     @Apidoc\Returned ("sort_order",type="int(10)",desc="排序"),
     *     @Apidoc\Returned ("card_id",type="string",desc="卡券ID"),
     *     @Apidoc\Returned("limit_dis", type="object", desc="限时优惠json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为限时折扣id" )
     *     ),
     *     @Apidoc\Returned("n_dis", type="json", desc="N件N折json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为N件N折id（数组）" )
     *     ),
     *     @Apidoc\Returned("group_dis", type="json", desc="团购json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为团购id（数组）" )
     *     ),
     *     @Apidoc\Returned("pre_dis", type="json", desc="预售json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为预售id（数组）" )
     *     ),
     *     @Apidoc\Returned("cheap_dis", type="json", desc="套餐json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为套餐id（数组）" )
     *     ),
     *     @Apidoc\Returned("full_dis", type="json", desc="满减json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为满减id（数组）" )
     *     ),
     *     @Apidoc\Returned("seckill_dis", type="json", desc="秒杀json活动信息",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为秒杀id（数组）" )
     *     ),
     *     @Apidoc\Returned("gift_dis", type="json", desc="买赠json活动信息(0214)",
     *         @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为买赠id（数组）" )
     *     ),
     *     @Apidoc\Returned ("com_s_types",type="varchar(200)",desc="三级分类ID逗号隔开多个"),
     *     @Apidoc\Returned ("commodity_label",type="varchar(20)",desc="商品标签"),
     *
     *     @Apidoc\Returned ("commodity_dis_user_segment",type="string",desc="商品折扣：0-无/1-会员价/2-车主价(0423)"),
     *     @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *     @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *     @Apidoc\Returned ("current_price",type="varchar(20)",desc="商品现价，活动非会员价(0423版本)")
     * )，
     *
     */
    public function giftCard(GoodsValidate $validate)
    {


//
    }

    /**
     * @Apidoc\Title("商品热销")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/hot")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 热销 NI+")
     *
     * @Apidoc\Param("dlr_code", type="int(10)",require=false, desc="店热销，不传显示热门关注" )
     * @Apidoc\Param("pageSize", type="int(10)",require=false, default="5", desc="页数默认5" )
     *
     * @Apidoc\Returned("commodity_id", type="int(10)", desc="商品id" )
     * @Apidoc\Returned("commodity_name", type="varchar(200)", desc="商品名称" )
     * @Apidoc\Returned("cover_image", type="varchar(200)", desc="封面图" )
     * @Apidoc\Returned("price", type="decimal(10,2)", desc="商品初始价格" )
     * @Apidoc\Returned("final_price", type="decimal(10,2)", desc="商品最终价格" )
     * @Apidoc\Returned("sale_num", type="int(10)", desc="销售数量" )
     *
     */
    public function goodsHot(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("goodsHot")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $net_goods =  new NetGoods();
        $res = $net_goods->goodsSale($requestData);

        if($res['code']<>200){
            return $this->setResponseError($res['msg'],$res['code'])->send();
        }else{
            return $this->setResponseData($res['msg'])->send();
        }

//
    }

    /**
     * @Apidoc\Title("商品分类")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/class")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 分类 NI+")
     *
     * @Apidoc\Returned("aname", type="varchar(200)", desc="一级分类名称" )
     * @Apidoc\Returned("aid", type="int(10)", desc="一级分类ID" )
     * @Apidoc\Returned("b_type", type="array/json", desc="一级分类ID",
     *     @Apidoc\Returned("twoname", type="varchar(200)", desc="二级分类名称" ),
     *     @Apidoc\Returned("twoid", type="int(10)", desc="二级分类ID" ),
     *     @Apidoc\Returned("list", type="array/json", desc="三级分类列表",
     *         @Apidoc\Returned("bname", type="varchar(200)", desc="三级分类名称" ),
     *         @Apidoc\Returned("bid", type="int(10)", desc="三级分类ID" ),
     *         @Apidoc\Returned("img", type="varchar(200)", desc="三级分类图" )
     *     )
     * )
     *
     */
    public function goodsClass(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $channel_type = $this->channel_type;
        $key          = config('cache_prefix.catalog') . $channel_type;
        $class_info   = redis($key);
        if (empty($class_info)) {
            $goods_ser = new NetGoods();
            $class_info     = $goods_ser->goodsClass($channel_type,0,$requestData);
            redis($key, $class_info, mt_rand(1800, 3600) * 10);
        }

        return $this->setResponseData($class_info)->send();
    }



    /**
     * @Apidoc\Title("获取估算价")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/goods/best-activity-card")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("商品 分类 NI+")
     *
     * @Apidoc\Param("set_sku_id", type="int(10)",require=true, desc="上架id，sku卡券版本之后需要传" )
     * @Apidoc\Param("old_price", type="string",require=true, desc="原价" )
     * @Apidoc\Param("activity_price", type="string",require=true, desc="现价价" )
     * @Apidoc\Param("member_price", type="string",require=false, desc="会员价" )
     * @Apidoc\Param("activity_id", type="int(10)",require=false, desc="活动id" )
     * @Apidoc\Param("activity_type", type="int(10)",require=false, desc="活动类型ID" )
     * @Apidoc\Param("commodity_id", type="int(10)",require=true, desc="活动类型ID" )
     * @Apidoc\Param("count", type="int(10)",require=false, desc="默认是1" )
     * @Apidoc\Param("dd_dlr_code", type="string",require=false, desc="到店专营店" )
     * @Apidoc\Param("sku_json", type="数组",require=false, desc="数组，包含sku_id，group_sub_commodity_id" )
     *
     * @Apidoc\Returned("estimate_price", type="varchar(200)", desc="估算价" )
     * @Apidoc\Returned("have_card", type="int(10)", desc="0无卡券1有卡券" )
     *
     *
     */
    public function bestActivityCard(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $channel_type = $this->channel_type;
        $key          = config('cache_prefix.catalog') . $channel_type;
        $class_info   = redis($key);
        if (empty($class_info)) {
            $goods_ser = new NetGoods();
            $class_info     = $goods_ser->goodsClass($channel_type,0,$requestData);
            redis($key, $class_info, mt_rand(1800, 3600) * 10);
        }

        return $this->setResponseData($class_info)->send();
    }

    /**
     * @Apidoc\Title("商品详情")
     * @Apidoc\Author("lzx")
     * @Apidoc\Method("GET")
     * @Apidoc\Url("/net-small/goods/detail")
     * @Apidoc\Tag("商品 NI+ 0214")
     *
     * @Apidoc\Param("goods_id", type="int(10)",require=true, desc="商品ID即commodity_id" )
     * @Apidoc\Param("group_id", type="int(10)",require=false, desc="团购ID" )
     * @Apidoc\Param("group_order_code", type="int(10)",require=false, desc="团购订单编号从别人分享的团进来才有")
     * @Apidoc\Param("lng", type="varchar",require=false, desc="经度" )
     * @Apidoc\Param("lat", type="varchar",require=false, desc="纬度" )
     * @Apidoc\Param("is_gift", type="tinyint(1)",require=false, desc="是否赠品 1赠品 0不是赠品 赠品查看详情必传(0214新增)" )
     * @Apidoc\Param("gift_id", type="int(11)",require=false, desc="买赠活动id 赠品详情必传" )
     * @Apidoc\Param("dd_dlr_code", type="varchar",require=false, desc="到店专营店--用户改店时候传" )
     * @Apidoc\Param("kilometer",type="int",require=false,desc="公里数")
     * @Apidoc\Param("nvi_vin",type="varchar",require=false,desc="vin")
     * @Apidoc\Param("r_set_sku_id",type="varchar",require=false,desc="r_set_sku_id 检测页传过来保证两边价格一致")
     * @Apidoc\Param("buy_car_no",type="varchar",require=false,desc="购车订单号 交车页过来的时候需要带")
     * @Apidoc\Param("car_config_code",type="varchar",require=false,desc="18位码 交车页过来的时候需要带")
     *
     *
     * @Apidoc\Returned("pre_sale", type="array/json", desc="预售信息列表",
     *     @Apidoc\Returned("purchase_number", type="int(10)", desc="限购数量" ),
     *     @Apidoc\Returned("front_money", type="decimal(10,2)", desc="定金" ),
     *     @Apidoc\Returned("dedu_money", type="decimal(10,2)", desc="抵扣金额" ),
     *     @Apidoc\Returned("id", type="int(11)", desc="预售活动id" ),
     *     @Apidoc\Returned("dec", type="text", desc="活动描述" ),
     *     @Apidoc\Returned("front_s_time", type="datetime", desc="定金支付起始时间" ),
     *     @Apidoc\Returned("front_e_time", type="datetime", desc="定金支付结束时间" ),
     *     @Apidoc\Returned("balance_s_time", type="datetime", desc="尾款支付起始时间" ),
     *     @Apidoc\Returned("balance_e_time", type="datetime", desc="尾款支付结束时间" ),
     *     @Apidoc\Returned("pre_status", type="tinyint(1)", desc="0不在任何时间内，1支付定金时间，2支付尾款时间" ),
     *     @Apidoc\Returned("wk_times", type="string", desc="尾款支付时间段" ),
     *     @Apidoc\Returned("yh_money", type="decimal(10,2)", desc="优惠金额" ),
     *     @Apidoc\Returned("can_buy", type="int(10)", desc="可购买数量" ),
     *     @Apidoc\Returned("yd_times", type="string", desc="预定时间段" )
     * )
     * @Apidoc\Returned("hide_numer", type="tinyint(1)", desc="是否隐藏购买数量：0否1是" )
     * @Apidoc\Returned("liability_clause", type="string",desc="延保服务包协议名"),
     * @Apidoc\Returned("liability_clause_pdf", type="string",desc="延保服务包协议pdf"),
     * @Apidoc\Returned("buy_word", type="string", desc="购买按钮词" )
     * @Apidoc\Returned("stop_time", type="string", desc="购买截至时间" )
     * @Apidoc\Returned("canot_buy", type="array", desc="不能购买状态跟文案",
     *     @Apidoc\Returned("status", type="tinyint(1)", desc="状态0可以买1不能买" ),
     *     @Apidoc\Returned("word", type="string", desc="不能买的文案" )
     * )
     * @Apidoc\Returned("is_not_cart", type="tinyint(1)", desc="不能放入购物车，0可以1不可以" )
     * @Apidoc\Returned("integral", type="int(11)", desc="积分" )
     * @Apidoc\Returned("car_info", type="varchar(100)", desc="车型信息（车系、排量、年份、轮胎）")
     * @Apidoc\Returned("json_data", type="array/json", desc="sku_list等数据",
     *     @Apidoc\Returned("sku_list", type="array/json", desc="当前sku以sp_list映射通过主键ID获得对应数组",
     *          @Apidoc\Returned("price", type="decimal(10,2)", desc="价格" ),
     *          @Apidoc\Returned("limit_dis_price", type="decimal(10,2)", desc="限时折扣sku价格/无活动==price" ),
     *          @Apidoc\Returned("stock", type="int(10)", desc="库存" ),
     *          @Apidoc\Returned("set_sku_id", type="int(10)", desc="上架id上架规格取这个" ),
     *          @Apidoc\Returned("sku_id", type="int(10)", desc="规格ID" ),
     *          @Apidoc\Returned("image", type="varchar(200)", desc="图片" ),
     *          @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="工时数量"),
     *          @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="工时编码"),
     *          @Apidoc\Returned ("work_time_price",type="varchar(50)",desc="工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *          @Apidoc\Returned("sp_value_arr", type="array/json", desc="规格主键列表（无字段只有值）" ),
     *          @Apidoc\Returned ("is_gift",type="tinyint(1)",desc="是否买赠 1是 0否(0214版本)"),
     *          @Apidoc\Returned ("detail_content",type="text",desc="规格详情内容(4月版本)"),
     *          @Apidoc\Returned ("commodity_dis_user_segment",type="string",desc="商品折扣：0-无/1-会员价/2-车主价(0423)"),
     *          @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *          @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *          @Apidoc\Returned ("current_price",type="varchar(20)",desc="商品现价，活动非会员价(0423版本)"),
     *          @Apidoc\Returned ("can_most_point",type="int(10)",desc="最高可用积分"),
     *          @Apidoc\Returned ("card_can_get",type="int(10)",desc="可领券"),
     *          @Apidoc\Returned ("card_can_use",type="int(10)",desc="可用券，用券优先级>领券"),
     *     ),
     *     @Apidoc\Returned("sku_image", type="array/json", desc="规格图片列表（无字段只有值）" ),
     *     @Apidoc\Returned("sp_list_group", type="array/json", desc="团购时的规格列表，格式同sku_list" )
     * )
     * @Apidoc\Returned("sp_list", type="array/json", desc="所有当前所有规格",
     *     @Apidoc\Returned("sp_id", type="int(10)", desc="规格组名ID" ),
     *     @Apidoc\Returned("sp_name", type="varchar(200)", desc="规格组名中文" ),
     *     @Apidoc\Returned("sp_value_list", type="array/json", desc="详细规格",
     *          @Apidoc\Returned("sp_value_id", type="int(11)", desc="规格值ID" ),
     *          @Apidoc\Returned("sp_value_name", type="varchar(200)", desc="规格中文" )
     *     )
     * )
     * @Apidoc\Returned("suit", type="array/json", desc="套装信息",
     *     @Apidoc\Returned("goods_count", type="int(10)", desc="商品数量--套餐新增" ),
     *     @Apidoc\Returned("suit_price", type="float(10)", desc="套餐价格--套餐新增" ),
     *     @Apidoc\Returned("suit_count", type="int(10)", desc="款数--套餐新增" ),
     *     @Apidoc\Returned("suit_list", type="array/json", desc="套装列表",
     *          @Apidoc\Returned("index_id", type="int(11)", desc="优惠套装主表id" ),
     *          @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *          @Apidoc\Returned("cover_image", type="varchar(200)", desc="封面图" ),
     *          @Apidoc\Returned("price", type="decimal(11,2)", desc="现价" ),
     *          @Apidoc\Returned("old_price", type="decimal(11,2)", desc="原价" ),
     *     )
     * )
     * @Apidoc\Returned("group_order_code", type="varchar(50)", desc="当前团购订单号" )
     * @Apidoc\Returned("group_info", type="array/json", desc="团购信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="拼团活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="拼团活动名称" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("people_number", type="int(11)", desc="参团人数" ),
     *     @Apidoc\Returned("purchase_number", type="int(11)", desc="限购数量" ),
     *     @Apidoc\Returned("buy_hour", type="int(11)", desc="参团时间" ),
     *     @Apidoc\Returned("rule", type="text", desc="规则" ),
     *     @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *     @Apidoc\Returned("lowest_price", type="decimal(11,2)", desc="最低价" )
     * )
     * @Apidoc\Returned("sp_list_group", type="array/json", desc="团购时所有当前所有规格，格式同sp_list")
     * @Apidoc\Returned("group_id", type="int(11)", desc="团购ID" )
     * @Apidoc\Returned("group_act_list", type="array/json", desc="当前团购列表",
     *     @Apidoc\Returned("count", type="int(11)", desc="数量" ),
     *     @Apidoc\Returned("openid", type="varchar(32)", desc="openid" ),
     *     @Apidoc\Returned("group_code", type="varchar(32)", desc="团购编码，一个团一个编码" ),
     *     @Apidoc\Returned("group_start_time", type="datetime", desc="开团时间" ),
     *     @Apidoc\Returned("nickname", type="varchar(50)", desc="微信昵称" ),
     *     @Apidoc\Returned("headimg", type="varchar(200)", desc="微信头像" ),
     *     @Apidoc\Returned("number", type="int(11)", desc="还差几人可成团" ),
     *     @Apidoc\Returned("time", type="varchar", desc="剩余拼团时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     * )
     * @Apidoc\Returned("can_number", type="int(11)", desc="可以购买数量" )
     * @Apidoc\Returned("can_change_dlr", type="int(11)", desc="能否修改门店1能2不能" )
     * @Apidoc\Returned("sku_image", type="array/json", desc="规格图片列表（无字段只有值）" )
     * @Apidoc\Returned("goods", type="array/json", desc="商品信息",
     *     @Apidoc\Returned("commodity_id", type="int(11)", desc="商品ID" ),
     *     @Apidoc\Returned("commodity_name", type="varchar(200)", desc="商品名称" ),
     *     @Apidoc\Returned("car_series_id", type="int(11)", desc="车系id（车系表ID）" ),
     *     @Apidoc\Returned("is_pure", type="tinyint(1)", desc="是否纯正精品1是0否" ),
     *     @Apidoc\Returned("is_integral", type="tinyint(1)", desc="是否支持积分支付1是0否" ),
     *     @Apidoc\Returned("comm_type_id", type="int(11)", desc="分类ID" ),
     *     @Apidoc\Returned("comm_type_two_id", type="int(11)", desc="二级分类ID" ),
     *     @Apidoc\Returned("comm_type_one_id", type="int(11)", desc="一级分类ID" ),
     *     @Apidoc\Returned("commodity_type", type="varchar(10)", desc="分类名" ),
     *     @Apidoc\Returned("original_price_range_start", type="decimal(10,2)", desc="原价开始价" ),
     *     @Apidoc\Returned("original_price_range_end", type="decimal(10,2)", desc="原价结束价" ),
     *     @Apidoc\Returned("discount_price_range_star", type="decimal(10,2)", desc="现价开始价" ),
     *     @Apidoc\Returned("discount_price_range_end", type="decimal(10,2)", desc="现价结束价" ),
     *     @Apidoc\Returned("card_id", type="varchar(20)", desc="卡券ID" ),
     *     @Apidoc\Returned("is_mail", type="tinyint(1)", desc="是否直邮，1是0否" ),
     *     @Apidoc\Returned("factory_points", type="int(11)", desc="厂家积分" ),
     *     @Apidoc\Returned("count_stock", type="int(11)", desc="总库存" ),
     *     @Apidoc\Returned("commodity_attr", type="tinyint(1)", desc="商品属性1热销2推荐3新品4促销" ),
     *     @Apidoc\Returned("mail_price", type="decimal(10,2)", desc="运费" ),
     *     @Apidoc\Returned("commodity_dlr_type_id", type="int(11)", desc="专营店分类ID" ),
     *     @Apidoc\Returned("point_discount", type="decimal(10,2)", desc="积分折扣比例为0不折扣" ),
     *     @Apidoc\Returned("integral_price", type="int(11)", desc="积分价格" ),
     *     @Apidoc\Returned("template_guid", type="int(11)", desc="运费模板ID" ),
     *     @Apidoc\Returned("detail_content", type="text", desc="详情内容" ),
     *     @Apidoc\Returned("sku_image", type="array/json", desc="规格图片列表（无字段只有值）" ),
     *     @Apidoc\Returned("unit", type="varchar(3)", desc="单位" ),
     *     @Apidoc\Returned("cover_image", type="varchar(150)", desc="首图" ),
     *     @Apidoc\Returned("commodity_class", type="tinyint(4)", desc="商品类型1实物2卡券3虚拟" ),
     *     @Apidoc\Returned("commodity_card_ids", type="varchar(500)", desc="商品电子卡券id，英文逗号隔开" ),
     *     @Apidoc\Returned("sale_num", type="int(11)", desc="销售数量" ),
     *     @Apidoc\Returned("commodity_set_id", type="int(11)", desc="商品上架ID" ),
     *     @Apidoc\Returned("video", type="varchar(250)", desc="视频" ),
     *     @Apidoc\Returned("is_mate", type="tinyint(4)", desc="是否匹配车型，1是0否2用户没车型" ),
     *     @Apidoc\Returned("collected", type="tinyint(4)", desc="是否收藏，1是0否" ),
     *     @Apidoc\Returned("is_store", type="tinyint(4)", desc="是否到店，1是0否" ),
     *     @Apidoc\Returned("mail_method", type="tinyint(4)", desc="1到店2快递3都有弹出用户选择" ),
     *     @Apidoc\Returned("is_repeat", type="tinyint(4)", desc="是否可以使用多张，1是0否" ),
     *     @Apidoc\Returned("is_preview", type="tinyint(4)", desc="是否预览，1是0否" ),
     *     @Apidoc\Returned("home_page_index", type="int(2)", desc="首页索引" ),
     *     @Apidoc\Returned("is_shopping_cart", type="tinyint(2)", desc="是否支持购物车，1是0否" ),
     *     @Apidoc\Returned("is_shop", type="tinyint(4)", desc="是否商城商品，1是0否" ),
     *     @Apidoc\Returned("commodity_code", type="varchar(50)", desc="商品编码" ),
     *     @Apidoc\Returned("shelves_sources", type="tinyint(4)", desc="上架来源，1平台自营2专营店3官微4活动" ),
     *     @Apidoc\Returned("brands_id", type="int(11)", desc="品牌id" ),
     *     @Apidoc\Returned("favourable_introduction", type="varchar(255)", desc="优惠简称" ),
     *     @Apidoc\Returned("favourable_detail", type="varchar(255)", desc="优惠详情" ),
     *     @Apidoc\Returned("is_card_available", type="tinyint(4)", desc="是否可领取卡券1是0否" ),
     *     @Apidoc\Returned("user_car_series", type="int", desc="用户车系信息" ),
     *     @Apidoc\Returned("de_sku_id", type="int", desc="默认skuid" ),
     *     @Apidoc\Returned("q_qz", type="varchar(50)", desc="前缀" ),
     *     @Apidoc\Returned("q_hz", type="varchar(50)", desc="后缀" ),
     *     @Apidoc\Returned("act_code", type="varchar(200)", desc="领取时可用" ),
     *     @Apidoc\Returned("max_point", type="int(11)", desc="最高可用积分-" ),
     *     @Apidoc\Returned("is_grouped", type="int(11)", desc="是否组合商品1是" ),
     *     @Apidoc\Returned("is_qsc_group", type="int(11)", desc="是否取送车服务包 1是0否" ),
     *     @Apidoc\Returned("is_sp_associated", type="int(11)", desc="组合子商品是否存在规格联动，1-存在/0-不存在" ),
     *     @Apidoc\Returned("service_channel_labels", type="array", desc="服务标签" ),
     *     @Apidoc\Returned("maintain_can_buy", type="varchat", desc="商品可否购买1能，0不能" ),
     *     @Apidoc\Returned("maintain_can_buy_word", type="varchat", desc="商品不可购买说明" ),
     *     @Apidoc\Returned("is_vin_car", type="int",  desc="0无vin车用户；1有vin车但非默认车型用户；2默认车型为vin车用户"),
     *     @Apidoc\Returned("sp_associated_data", type="array/json", desc="组合子商品规格联动" ,
     *          @Apidoc\Returned("sub_commodity_id", type="int", desc="组合子商品id" ),
     *          @Apidoc\Returned("sp_id", type="int", desc="规格id" ),
     *          @Apidoc\Returned("sp_value_id", type="int", desc="规格值id" ),
     *          @Apidoc\Returned("assoc_sub_commodity_id", type="int", desc="联动组合子商品id" ),
     *          @Apidoc\Returned("assoc_sp_id", type="int", desc="联动规格id" ),
     *          @Apidoc\Returned("assoc_sp_value_id", type="int", desc="联动规格值id" ),
     *     ),
     *     @Apidoc\Returned ("work_time_price",type="varchar(50)",desc="商品工时单价--子商品单价跟这个是一样的。工时单价跟着主商品走"),
     *     @Apidoc\Returned("commodity_label",type="varchar(20)",desc="商品标签"),
     *     @Apidoc\Returned("groups_data", type="array/json", desc="组合子商品" ,
     *        @Apidoc\Returned("sub_commodity_list", type="array/json", desc="组合子商品列表",
     *           @Apidoc\Returned("count_stock", type="int", desc="子商品的总库存" ),
     *           @Apidoc\Returned("stock", type="int", desc="库存" ),
     *           @Apidoc\Returned("is_mate", type="int", desc="车型是否可以购买1可以，其他不行" ),
     *           @Apidoc\Returned("sp_value_list", type="string", desc="规格值" ),
     *           @Apidoc\Returned("min_price", type="float", desc="最低的sku价格" ),
     *           @Apidoc\Returned("commodity_id", type="int", desc="组合主商品id" ),
     *           @Apidoc\Returned("group_sub_commodity_id", type="int", desc="组合子商品id" ),
     *           @Apidoc\Returned("commodity_name", type="string", desc="组合子商品名" ),
     *           @Apidoc\Returned("image", type="string", desc="组合子商品图片" ),
     *           @Apidoc\Returned("sp_value_name", type="string", desc="组合子商品规值" ),
     *           @Apidoc\Returned("sp_value_list", type="string", desc="规格值" ),
     *           @Apidoc\Returned("set_sku_id", type="int", desc="上架id--规格ID，下单\加购都用这个" ),
     *           @Apidoc\Returned("initial_num", type="int", desc="初始数量" ),
     *           @Apidoc\Returned("user_can_des", type="int", desc="用户是否可增减数量" ),
     *           @Apidoc\Returned("can_select", type="int", desc="是否可选" ),
     *           @Apidoc\Returned ("relate_car_ids",type="text",desc="关联车系ID"),
     *           @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="子工时数量"),
     *           @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="子工时编码"),
     *           @Apidoc\Returned ("work_time_price",type="varchar(50)",desc="子工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *           @Apidoc\Returned ("sp_name",type="varchar(50)",desc="规格标题--弹窗时候选择更多那里"),
     *          ),
     *        @Apidoc\Returned("commodity_dis_info", type="array/json", desc="组合商品折扣信息",
     *           @Apidoc\Returned ("discount_type",type="varchar(50)",desc="折扣方式，1打折，2立减"),
     *           @Apidoc\Returned ("discount",type="varchar(50)",desc="折扣额度"),
     *           @Apidoc\Returned ("max_discount_amount",type="varchar(50)",desc="折扣上限"),
     *          )
     *     ),
     *     @Apidoc\Returned("qsc_groups_data", type="array/json", desc="取送车服务包列表" ,
     *         @Apidoc\Returned("price",type="varchar(50)",desc="售价"),
     *         @Apidoc\Returned("stock", type="int(11)", desc="库存" ),
     *         @Apidoc\Returned("sku_id", type="int(11)", desc="sku_id" ),
     *         @Apidoc\Returned("id", type="int(11)", desc="商品id" ),
     *         @Apidoc\Returned("commodity_name", type="string(50)", desc="商品名称" ),
     *         @Apidoc\Returned("cover_image", type="string(150)", desc="商品首图" ),
     *         @Apidoc\Returned("buy_num", type="int(4)", desc="购买数量" ),
     *         @Apidoc\Returned("mail_method", type="tinyint(4)", desc="1到店2快递3都有弹出用户选择" ),
     *         @Apidoc\Returned("dlr_code", type="varchar", desc="到店专营店" ),
     *     ),
     *     @Apidoc\Returned("directions1", type="varchar(50)", desc="服务说明1" ),
     *     @Apidoc\Returned("directions2", type="varchar(50)", desc="服务说明2" ),
     *     @Apidoc\Returned("remark", type="varchar(500)", desc="温馨提示" ),
     *     @Apidoc\Returned("act_code", type="varchar(200)", desc="领取时可用" ),
     *     @Apidoc\Returned("max_point", type="int(11)", desc="最高可用积分" ),
     *     @Apidoc\Returned("maintain_dis", type="string", desc="保养套餐不匹配车型时的折扣没有deskuid时候使用这个显示" ),
     *     @Apidoc\Returned ("pay_com",type="array/json",desc="最高可用积分内容",
     *          @Apidoc\Returned ("pay_rule", type="int(11)", desc="支付规则 1 至少支付N元 2按%比"),
     *          @Apidoc\Returned ("pay_price", type="float", desc="金额或者百分比"),
     *     ),
     * )
     * @Apidoc\Returned("full_list", type="array/json", desc="满减列表",
     *     @Apidoc\Returned("id", type="int(11)", desc="满减优惠活动id" ),
     *     @Apidoc\Returned("money", type="decimal(10,2)", desc="活动金额" ),
     *     @Apidoc\Returned("is_preferential_money", type="tinyint(4) ", desc="是否支持立减(1支持0否）" ),
     *     @Apidoc\Returned("is_preferential_card", type="tinyint(4) ", desc="是否支持送券(1支持0否）" ),
     *     @Apidoc\Returned("preferential_money", type="decimal(10,2)", desc="'下单立减（优惠方式为1时）" ),
     *     @Apidoc\Returned("preferential_card_id", type="int", desc="优惠券编号（优惠方式为2时有效）" ),
     *     @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *     @Apidoc\Returned("full_discount_rules", type="varchar(255)", desc="满减规则,1个活动最多3个规则,如满5减3,满2减1,满1减0.5的数据[[5,3],[2,1],[1,0.5]]" ),
     *     @Apidoc\Returned("word", type="varchar(50)", desc="活动名称" ),
     *     @Apidoc\Returned("url", type="varchar(200)", desc="满减信息链接" ),
     *     @Apidoc\Returned("can_buy_number", type="varchar(200)", desc="满减活动，可参与次数，大于0表示可参与活动" ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * )
     * @Apidoc\Returned("n_dis_info", type="array/json", desc="N件N折优惠信息",
     *     @Apidoc\Returned("n_id", type="int(11)", desc="N件N折活动id" ),
     *     @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("des", type="varchar(60)", desc="活动描述" ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * )
     * @Apidoc\Returned("limit_info", type="array/json", desc="限时优惠信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="限时折扣活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("purchase_number", type="int(11)", desc="限购数量" ),
     *     @Apidoc\Returned("des", type="varchar(60)", desc="活动描述" ),
     *     @Apidoc\Returned("stop_time", type="datetime", desc="停止时间" ),
     *     @Apidoc\Returned("discount_type", type="int(11)", desc="优惠类型：1仅商品；2仅工时；3商品+工时" ),
     *     @Apidoc\Returned("discount", type="int(11)", desc="折扣/直减" ),
     *     @Apidoc\Returned("non_member_discount", type="int(11)", desc="非会员折扣 折扣/直减" ),
     *     @Apidoc\Returned("dis_type", type="int(11)", desc="1限时折扣 2立减" ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * )
     * @Apidoc\Returned("seckill_info", type="array/json", desc="秒杀优惠信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="秒杀折扣活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("purchase_number", type="int(11)", desc="限购数量" ),
     *     @Apidoc\Returned("des", type="varchar(60)", desc="活动描述" ),
     *     @Apidoc\Returned("stop_time", type="datetime", desc="停止时间" ),
     *     @Apidoc\Returned("discount_type", type="int(11)", desc="优惠类型：1仅商品；2仅工时；3商品+工时" ),
     *     @Apidoc\Returned("act_status", type="int(11)", desc="1未开始，2进行中，3已结束" ),
     *     @Apidoc\Returned("discount", type="int(11)", desc="折扣/直减" ),
     *     @Apidoc\Returned("non_member_discount", type="int(11)", desc="非会员折扣 折扣/直减" ),
     *     @Apidoc\Returned("dis_type", type="int(11)", desc="1限时折扣 2立减" ),
     *     @Apidoc\Returned("miaosha_stock", type="int(11)", desc="秒杀库存" ),
     *     @Apidoc\Returned("seckill_type", type="int(11)", desc="秒杀类型 1:单场秒杀  2:重复秒杀" ),
     *     @Apidoc\Returned("day_start_time", type="varchar(20)", desc="秒杀当天开始时间" ),
     *     @Apidoc\Returned("day_end_time", type="varchar(20)", desc="秒杀当天结束时间" ),
     *     @Apidoc\Returned("sm_start_time", type="varchar(20)", desc="秒杀开始时间" ),
     *     @Apidoc\Returned("sm_end_time", type="varchar(20)", desc="秒杀结束时间" ),
     *     @Apidoc\Returned("next_start_time", type="varchar(20)", desc="下场秒杀开始时间" ),
     *     @Apidoc\Returned("next_end_time", type="varchar(20)", desc="下场秒杀结束时间" ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * )
     * @Apidoc\Returned("limit_wi_info", type="array/json", desc="限时优惠信息-工时",
     *     @Apidoc\Returned("id", type="int(11)", desc="限时折扣活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("purchase_number", type="int(11)", desc="限购数量" ),
     *     @Apidoc\Returned("des", type="varchar(60)", desc="活动描述" ),
     *     @Apidoc\Returned("stop_time", type="datetime", desc="停止时间" ),
     *     @Apidoc\Returned("discount_type", type="int(11)", desc="优惠类型：1仅商品；2仅工时；3商品+工时" ),
     *     @Apidoc\Returned("discount", type="int(11)", desc="折扣/直减" ),
     *     @Apidoc\Returned("dis_type", type="int(11)", desc="1限时折扣 2立减" )
     * )
     *
     * @Apidoc\Returned("crowdfund_info", type="array/json", desc="众筹信息",
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("target", type="int(11)", desc="活动目标 1众筹金额 2众筹数量" ),
     *     @Apidoc\Returned("target_val", type="varchar(60)", desc="活动目标值" ),
     *     @Apidoc\Returned("alr_crowd", type="varchar(60)", desc="已筹显示 1已筹金额 2已筹数量 逗号分割，比如1,2就是两个都要显示" ),
     *     @Apidoc\Returned("purchase_num", type="int(11)", desc="限购数量" ),
     *     @Apidoc\Returned("theme_name", type="varchar(200)", desc="主题活动名称" ),
     *     @Apidoc\Returned("act_status", type="int(11)", desc="活动状态:1未开始;2进行中;3结束" ),
     *     @Apidoc\Returned("plan_status", type="int(11)", desc="进度状态:0未开始;1进行中-未达标;2进行中-已达标;3项目成功-自动;4项目成功-人为;5项目失败-自动;6项目失败-人为;7项目失败-待处理" ),
     *     @Apidoc\Returned("money", type="int(11)", desc="已筹金额" ),
     *     @Apidoc\Returned("sum_num", type="int(11)", desc="已筹数量" ),
     *     @Apidoc\Returned("peo_count", type="int(11)", desc="参与人数" ),
     *     @Apidoc\Returned("crowdfund_agn", type="text", desc="众筹协议" ),
     *     @Apidoc\Returned("refund_text", type="text", desc="众筹退款说明" ),
     * )
     * @Apidoc\Returned("full_wi_list", type="array/json", desc="满减列表-工时",
     *     @Apidoc\Returned("id", type="int(11)", desc="满减优惠活动id" ),
     *     @Apidoc\Returned("money", type="decimal(10,2)", desc="活动金额" ),
     *     @Apidoc\Returned("is_preferential_money", type="tinyint(4) ", desc="是否支持立减(1支持0否）" ),
     *     @Apidoc\Returned("is_preferential_card", type="tinyint(4) ", desc="是否支持送券(1支持0否）" ),
     *     @Apidoc\Returned("preferential_money", type="decimal(10,2)", desc="'下单立减（优惠方式为1时）" ),
     *     @Apidoc\Returned("preferential_card_id", type="int", desc="优惠券编号（优惠方式为2时有效）" ),
     *     @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *     @Apidoc\Returned("full_discount_rules", type="varchar(255)", desc="满减规则,1个活动最多3个规则,如满5减3,满2减1,满1减0.5的数据[[5,3],[2,1],[1,0.5]]" ),
     *     @Apidoc\Returned("word", type="varchar(50)", desc="活动名称" ),
     *     @Apidoc\Returned("url", type="varchar(200)", desc="满减信息链接" ),
     *     @Apidoc\Returned("can_buy_number", type="varchar(200)", desc="满减活动，可参与次数，大于0表示可参与活动" )
     * )
     * @Apidoc\Returned("g_info", type="array/json", desc="团购信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="团购活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="团购活动名称" )
     * )
     * @Apidoc\Returned("card_list", type="array/json", desc="卡券列表",
     *     @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *     @Apidoc\Returned("set_type", type="int(10)", desc="应用类型：1平台端2专营店3集团" ),
     *     @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *     @Apidoc\Returned("type", type="int(10)", desc="微信卡券：1商城卡券2" ),
     *     @Apidoc\Returned("card_name", type="varchar(200)", desc="卡劵名称" ),
     *     @Apidoc\Returned("card_id", type="varchar(28)", desc="微信卡劵id" ),
     *     @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券)" ),
     *     @Apidoc\Returned("wx_card_type", type="varchar(255)", desc="微信优惠券类型" ),
     *     @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *     @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *     @Apidoc\Returned("least_type", type="int(1)", desc="最低消费类型：1金额2指定商品" ),
     *     @Apidoc\Returned("least_cost", type="varchar(20)", desc="表示起用金额，如果无起用门槛则填0" ),
     *     @Apidoc\Returned("validity_date_start", type="datetime", desc="固定日期区间，专用有效期开始" ),
     *     @Apidoc\Returned("validity_date_end", type="datetime", desc="固定日期区间，专用有效期结束" ),
     *     @Apidoc\Returned("date_type", type="int(1)", desc="有效期类型：1表示固定日期区间2表示固定时长" ),
     *     @Apidoc\Returned("fixed_term", type="int(5)", desc="固定时长专用，领取后多少天内有效，单位为天" ),
     *     @Apidoc\Returned("fixed_begin_term", type="int(5)", desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天" ),
     *     @Apidoc\Returned("receive_range", type="tinyint(1)", desc="领取范围：1所有人2车主3指定用户4指定用户除外" ),
     *     @Apidoc\Returned("count", type="int(10)", desc="数量" ),
     *     @Apidoc\Returned("available_count", type="int(10)", desc="可用数量" ),
     *     @Apidoc\Returned("available_quantity", type="int(10)", desc="可领取数量" ),
     *     @Apidoc\Returned("default_detail", type="text", desc="优惠说明，字数上限300个汉字" ),
     *     @Apidoc\Returned("use_des", type="text", desc="使用须知，字数上限为1024个汉字" ),
     *     @Apidoc\Returned("get_limit", type="int(5)", desc="每人可领券的数量限制，不填写默认为1" ),
     *     @Apidoc\Returned("apply_des", type="varchar(30)", desc="适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *     @Apidoc\Returned("not_apply_des", type="varchar(30)", desc="不适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *     @Apidoc\Returned("apply_dlr_code", type="text", desc="适用门店" ),
     *     @Apidoc\Returned("commodity_use_type", type="int(1)", desc="优惠券可用的商品范围：1全部商品2指定商品" ),
     *     @Apidoc\Returned("act_name", type="varchar(255)", desc="活动名称" ),
     *     @Apidoc\Returned("page_id", type="varchar(100)", desc="page_id" ),
     *     @Apidoc\Returned("is_enable", type="int(1)", desc="是否可用" ),
     *     @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *     @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *     @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *     @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *     @Apidoc\Returned("color", type="varchar(10)", desc="颜色编码" ),
     *     @Apidoc\Returned("icon_image", type="varchar(300)", desc="封面图" ),
     *     @Apidoc\Returned("abstract", type="varchar(300)", desc="封面说明" ),
     *     @Apidoc\Returned("brand_name", type="varchar(30)", desc="商户名称" ),
     *     @Apidoc\Returned("code_type", type="varchar(100)", desc="code_type类型" ),
     *     @Apidoc\Returned("center_title", type="varchar(255)", desc="卡劵顶部按钮名称" ),
     *     @Apidoc\Returned("center_url", type="varchar(300)", desc="卡劵顶部按钮跳转URL" ),
     *     @Apidoc\Returned("custom_url_name", type="varchar(10)", desc="自定义跳转外链的入口名字" ),
     *     @Apidoc\Returned("custom_url", type="varchar(150)", desc="自定义跳转的URL" ),
     *     @Apidoc\Returned("custom_url_sub_title", type="varchar(20)", desc="自定义跳转外链右侧提示语" ),
     *     @Apidoc\Returned("promotion_url_name", type="varchar(10)", desc="营销场景自定义入口名字" ),
     *     @Apidoc\Returned("promotion_url", type="varchar(150)", desc="营销场景跳转的URL" ),
     *     @Apidoc\Returned("promotion_url_sub_title", type="varchar(20)", desc="营销场景入口右侧提示语" ),
     *     @Apidoc\Returned("off_line", type="int(1)", desc="是否线下使用，0否1是" ),
     *     @Apidoc\Returned("act_status", type="int(1)", desc="varchar(32)", desc="渠道名" ),
     *     @Apidoc\Returned("up_down_channel_name", type="varchar(200)", desc="渠道名" ),
     *     @Apidoc\Returned("up_down_channel_dlr", type="text", desc="上架专营店编码" ),
     *     @Apidoc\Returned("is_succeed", type="int(1)", desc="是否创券成功0否1是" ),
     *     @Apidoc\Returned("can_with", type="int(1)", desc="是否可与其他券共用0否1是" ),
     *     @Apidoc\Returned("can_with_ids", type="varchar(256)", desc="可共同使用的优惠券id" ),
     *     @Apidoc\Returned("can_get_in_detail", type="int(1)", desc="是否可领取：0否1是" ),
     *     @Apidoc\Returned("shelves_type", type="int(2)", desc="上架来源：1.平台自营 2.专营店 3.官微 4.活动 5.车生活" ),
     *     @Apidoc\Returned("coupon_activate_scene_list", type="json", desc="激活场景码，如果多条取第一条" )
     * )
     * @Apidoc\Returned("h_card_list", type="array/json", desc="组合商品用券说明",
     *     @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *     @Apidoc\Returned("card_name", type="varchar(200)", desc="卡劵名称" ),
     *     @Apidoc\Returned("list", type="array/json", desc="商品列表" ,
     *        @Apidoc\Returned("goods_list", type="array/json", desc="每组列表sku组合" ,
     *          @Apidoc\Returned("sp_value_name", type="varchar(200)", desc="规格名") ,
     *          @Apidoc\Returned("commodity_name", type="varchar(200)", desc="商品名") ,
     *          @Apidoc\Returned("set_sku_id", type="int(200)", desc="规格ID,") ,
     *          @Apidoc\Returned("price", type="decimal(10,2)", desc="价格" ),
     *          @Apidoc\Returned("limit_dis_price", type="decimal(10,2)", desc="限时折扣sku价格/无活动==price" ),
     *          @Apidoc\Returned("stock", type="int(10)", desc="库存" ),
     *          @Apidoc\Returned("set_sku_id", type="int(10)", desc="上架id上架规格取这个" ),
     *          @Apidoc\Returned("sku_id", type="int(10)", desc="规格ID" ),
     *          @Apidoc\Returned("image", type="varchar(200)", desc="图片" ),
     *          @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="工时数量"),
     *          @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="工时编码"),
     *          @Apidoc\Returned ("work_time_price",type="varchar(50)",desc="工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *          @Apidoc\Returned("sp_value_arr", type="array/json", desc="规格主键列表（无字段只有值）" ),
     *          @Apidoc\Returned ("is_gift",type="tinyint(1)",desc="是否买赠 1是 0否(0214版本)"),
     *          @Apidoc\Returned ("detail_content",type="text",desc="规格详情内容(4月版本)"),
     *          @Apidoc\Returned ("commodity_dis_user_segment",type="string",desc="商品折扣：0-无/1-会员价/2-车主价(0423)"),
     *          @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *          @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *          @Apidoc\Returned ("current_price",type="varchar(20)",desc="商品现价，活动非会员价(0423版本)"),
     *          @Apidoc\Returned ("sp_value_list",type="varchar(20)",desc="sp_value_list"),
     *          @Apidoc\Returned ("can_most_point",type="int(10)",desc="最高可用积分"),
     *          @Apidoc\Returned ("group_sub_commodity_id",type="int(10)",desc="子商品商品ID"),
     *          ),
     *     ),
     *     @Apidoc\Returned("shelves_type", type="int(2)", desc="上架来源：1.平台自营 2.专营店 3.官微 4.活动 5.车生活" )
     * )
     * @Apidoc\Returned("shop_cart_count", type="int(11)", desc="购物车数量" )
     * @Apidoc\Returned("comment", type="array/json", desc="评论列表",
     *     @Apidoc\Returned("id", type="int(11)", desc="评价表id" ),
     *     @Apidoc\Returned("order_code", type="varchar(100)", desc="订单编码" ),
     *     @Apidoc\Returned("order_commodity_id", type="int(11)", desc="订单商品表主键" ),
     *     @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *     @Apidoc\Returned("openid", type="int(11)", desc="openid" ),
     *     @Apidoc\Returned("user_id", type="int(11)", desc="用户id" ),
     *     @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *     @Apidoc\Returned("stars", type="int(11)", desc="星星1-5" ),
     *     @Apidoc\Returned("images", type="string", desc="图片url，用,隔开" ),
     *     @Apidoc\Returned("content", type="text", desc="评价内容" ),
     *     @Apidoc\Returned("reply_content", type="text", desc="回复内容" ),
     *     @Apidoc\Returned("is_enable", type="int(11)", desc="是否可用" ),
     *     @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *     @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *     @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *     @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *     @Apidoc\Returned("likes", type="int(11)", desc="点赞数" ),
     *     @Apidoc\Returned("nickname", type="varchar(200)", desc="昵称" ),
     *     @Apidoc\Returned("headimg", type="varchar(300)", desc="头像" ),
     *     @Apidoc\Returned("comment_star", type="int(11)", desc="星星1-5" ),
     *     @Apidoc\Returned("sku_info", type="varchar(200)", desc="sku信息，颜色等" ),
     *     @Apidoc\Returned("price", type="decimal(10,2)", desc="实际价格" ),
     *     @Apidoc\Returned("pic", type="varchar(200)", desc="商品图" ),
     *     @Apidoc\Returned("commodity_name", type="varchar(200)", desc="商品名" ),
     *     @Apidoc\Returned("is_comment", type="int(11)", desc="是否修改评价，0未修改1已修改" ),
     *     @Apidoc\Returned("one_id", type="varchar(255)", desc="oneid" ),
     *     @Apidoc\Returned("member_id", type="varchar(255)", desc="用户member_id" ),
     *     @Apidoc\Returned("is_anonymous", type="int(1)",default="0",desc="是否匿名，0不匿名，1匿名"),
     * )
     * @Apidoc\Returned("comment_count", type="int(11)", desc="商品点评数" )
     * @Apidoc\Returned("ap_time", type="timestamp", desc="服务器时间" )
     * @Apidoc\Returned("user", type="array/json", desc="用户信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="用户id" ),
     *     @Apidoc\Returned("phone", type="varchar(20)", desc="手机号" ),
     *     @Apidoc\Returned("name", type="varchar(30)", desc="姓名" ),
     *     @Apidoc\Returned("car_series_id", type="int(11)", desc="车系id" ),
     *     @Apidoc\Returned("address_id", type="int(11)", desc="收货地址id" ),
     *     @Apidoc\Returned("openid", type="varchar(50)", desc="openid" ),
     *     @Apidoc\Returned("plat_id", type="int(11)", desc="平台ID" ),
     *     @Apidoc\Returned("bind_unionid", type="varchar(50)", desc="绑定unionid" ),
     *     @Apidoc\Returned("unionid", type="varchar(50)", desc="unionid" ),
     *     @Apidoc\Returned("member_id", type="varchar(50)", desc="member_id" ),
     * ),
     * @Apidoc\Returned("limit_dis_enents", type="array/json", desc="预购限时购",
     *     @Apidoc\Returned("qd_title", type="varchar(50)", desc="预购文案" )
     * ),
     *
     * @Apidoc\Returned("gift_info", type="array/json", desc="买赠信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="买赠活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("des", type="text", desc="活动规则" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("purchase_number", type="int(11)", desc="剩余次数" ),
     *     @Apidoc\Returned("optional_number", type="int(11)", desc="可选择N件" ),
     *     @Apidoc\Returned("act_status", type="tinyint(1)", desc="1未开始，2进行中，3已结束" ),
     *     @Apidoc\Returned ("is_participate",type="tinyint(1)", desc="是否参加 1参加 0未参加"),
     *     @Apidoc\Returned ("gift_imgs_arr", type="array/json", desc="买赠主图",
     *          @Apidoc\Returned("is_choose", type="tinyint(1)", desc="是否可选 1可选 0不可选" ),
     *          @Apidoc\Returned("cover_image", type="varchar(60)", desc="赠品主图" )
     *     ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * ),
     *
     * @Apidoc\Returned("des_info", type="array/json", desc="商品说明",
     *          @Apidoc\Returned("id", type="int(11)", desc="说明表id" ),
     *          @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *          @Apidoc\Returned("title", type="varchar(200)", desc="标题" ),
     *          @Apidoc\Returned("commodity_type", type="varchar(200)", desc="商品分类" ),
     *          @Apidoc\Returned("display_area", type="int(10)", desc="显示区域 1 商品详情下方；2 商品详情上方" ),
     *          @Apidoc\Returned("display_channel", type="varchar(200)", desc="显示渠道 5.日产商城(小程序、app、官网) 6.PZ1A商城(小程序、app) 7.启辰商城(小程序、app) 8.to B商城(PC端)" ),
     *          @Apidoc\Returned("is_display", type="int(10)", desc="可见性 1可见0不可见" ),
     *          @Apidoc\Returned("des", type="text", desc="正文" )
     *
     * )
     * @Apidoc\Returned("is_vin_car", type="int",  desc="0无vin车用户；1有vin车但非默认车型用户；2默认车型为vin车用户")
     * @Apidoc\Returned("maintain_times", type="int",  desc="保养套餐升级次数")
     * @Apidoc\Returned("car_number", type="int",  desc="vin车数量")
     * @Apidoc\Returned("image_advert", type="json/array",  desc="图片广告",
     *     @Apidoc\Returned("cover_image", type="varchar(150)", desc="图片" ),
     *     @Apidoc\Returned("jump_type", type="int(1)", desc="跳转类型1：商品 2：商品分类 3：路径 4：H5链接；5：APP页面路径；6：小程序路径" ),
     *     @Apidoc\Returned("jump_url", type="int(1)", desc="对应跳转类型为1时为商品id；2时为三级分类id；3时为跳转路径；4时为H5链接；5时为APP页面路径；6时小程序路径" ),
     * ),
     *
     * @Apidoc\Returned("poster_show_type", type="int(1)", desc="广告图展示样式：1单行单图；2单行双图；3单行多图（全部展示同一行，滑动展示）" ),
     * @Apidoc\Returned("poster_list", type="array", desc="广告图列表",
     *     @Apidoc\Returned("poster_image", type="array", desc="广告图" ),
     *     @Apidoc\Returned("poster_jump_type", type="array", desc="广告图跳转类型：1跳转路径；2H5链接；3页面路径；4小程序路径；5商品详情；6商品分类" ),
     *     @Apidoc\Returned("poster_type_value", type="array", desc="广告图跳转类型对应的数据" ),
     * ),
     * @Apidoc\Returned("cap_info", type="array", desc="cap活动信息",
     *     @Apidoc\Returned("is_activity_commodity", type="int", desc="是否活动商品 0-否 1-是" ),
     *     @Apidoc\Returned("is_activity_user", type="int", desc="是否活动用户 0-否 1-是"  ),
     *     @Apidoc\Returned("order_id", type="int", desc="order_id" ),
     *     @Apidoc\Returned("order_code", type="string", desc="order_code" )
     * )
     * @Apidoc\Returned("pending_activation_cards", type="array", desc="待激活券列表",
     *     @Apidoc\Returned("id", type="string", desc="卡券ID" ),
     *     @Apidoc\Returned("card_name", type="string", desc="卡券名称" ),
     *     @Apidoc\Returned("card_type", type="int", desc="卡券类型" ),
     *     @Apidoc\Returned("card_quota", type="string", desc="卡券面额" ),
     *     @Apidoc\Returned("least_cost", type="string", desc="最低消费" ),
     *     @Apidoc\Returned("is_gift_card", type="int", desc="是否赠品券：1是0否" ),
     *     @Apidoc\Returned("show_switch_vehicle", type="bool", desc="是否显示切换车辆按钮" ),
     *     @Apidoc\Returned("activation_scenes", type="array", desc="激活场景列表" ),
     *     @Apidoc\Returned("activation_button_text", type="string", desc="激活按钮文本" )
     * )
     * @Apidoc\Returned("unpaid_order_cards", type="array", desc="待支付订单占用卡券信息",
     *     @Apidoc\Returned("order_id", type="int", desc="订单ID" ),
     *     @Apidoc\Returned("order_code", type="string", desc="订单编号" ),
     *     @Apidoc\Returned("total_price", type="string", desc="订单总价" ),
     *     @Apidoc\Returned("locked_cards", type="array", desc="锁定的卡券列表" )
     * )
     */
    public function detail(GoodsValidate $validate)
    {



    }

    /**
     * @Apidoc\Title("优惠套装")
     * @Apidoc\Desc("退货,换货afs_type:2:退货3:换货")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/suit")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 优惠 套装 NI+")
     *
     * @Apidoc\Param("goods_id", type="int(11)",require=true, desc="商品ID多个可以用,隔开" )
     * @Apidoc\Param("suit_id", type="int(11)",require=false, desc="套装ID 不传时返回几个列表" )
     * @Apidoc\Param("dd_dlr_code", type="varchar",require=false, desc="到店专营店--用户改店时候传" )
     * @Apidoc\Param("kilometer",type="int",require=false, desc="公里数")
     * @Apidoc\Param("sku_ids",type="varchar",require=false, desc="setskuid列表,隔开这个值与goods_id对应，个数对应sku也是商品对应的，传了goods_id+sku_ids将会返回实际的优惠价等信息，也就是用户修改了规格之后需要重新加载整个接口，并且把用户选择的skuid对应商品传给接口")
     * @Apidoc\Param("lng", type="float",require=false, desc="经度" )
     * @Apidoc\Param("lat", type="float",require=false, desc="纬度" )
     *
     * @Apidoc\Returned("is_vin_car", type="int",  desc="0无vin车用户；1有vin车但非默认车型用户；2默认车型为vin车用户")
     * @Apidoc\Returned("list", type="array/json", desc="套装列表",
     *     @Apidoc\Returned("suit_count", type="varchar(20)", desc="套装数量" ),
     *     @Apidoc\Returned("suit_price", type="decimal(10,2)", desc="套装价格" ),
     *     @Apidoc\Returned("suit_all_old_price", type="decimal(10,2)", desc="套装原价格" ),
     *     @Apidoc\Returned("need_dlr", type="int(11)", desc="是否需要专营店1需要0不需要" ),
     *     @Apidoc\Returned("need_car", type="int(11)", desc="是否需要车主1需要0不需要" ),
     *     @Apidoc\Returned("need_ki", type="int(11)", desc="是否需要公里数1需要0不需要" ),
     *     @Apidoc\Returned("need_18n", type="int(11)", desc="是否关联18位麻1是0否" ),
     *     @Apidoc\Returned("can_refund", type="int(11)", desc="是否能退1是0否" ),
     *     @Apidoc\Returned("have_content", type="int(11)", desc="是否有说明1是0否" ),
     *     @Apidoc\Returned("all_use_point", type="int(11)", desc="是否使用全积分1是0否--只有这个值==1的时候才显示积分，不显示现金" ),
     *     @Apidoc\Returned("goods_count", type="int(11)", desc="商品数量" ),
     *     @Apidoc\Returned("suit_list", type="array/json", desc="套装商品列表" ,
     *      @Apidoc\Returned("name", type="varchar(20)", desc="活动名" ),
     *      @Apidoc\Returned("index_id", type="int(11)", desc="优惠套装主表id" ),
     *      @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *      @Apidoc\Returned("sku", type="int(11)", desc="商品setskuid" ),
     *      @Apidoc\Returned("price", type="decimal(10,2)", desc="优惠价格" ),
     *      @Apidoc\Returned("cover_image", type="varchar(150)", desc="封面图" ),
     *      @Apidoc\Returned("commodity_name", type="varchar(200)", desc="商品名称" ),
     *      @Apidoc\Returned("sp_value_list", type="string", desc="规格值ID" ),
     *      @Apidoc\Returned("commodity_set_id", type="int(11)", desc="上架id" ),
     *      @Apidoc\Returned("old_price", type="decimal(10,2)", desc="价格" ),
     *      @Apidoc\Returned("stock", type="int(11)", desc="库存" ),
     *      @Apidoc\Returned("count_stock", type="int(11)", desc="总库存--判断是否有货" ),
     *      @Apidoc\Returned("is_mail", type="int(11)", desc="是否快递，1是0否" ),
     *      @Apidoc\Returned("need_car", type="int(11)", desc="是否需要车主，1是0否" ),
     *      @Apidoc\Returned("is_store", type="int(11)", desc="是否门店自提，1是0否，默认是" ),
     *      @Apidoc\Returned("limit_dis", type="varchar(200)", desc="限时优惠json活动信息" ),
     *      @Apidoc\Returned("sp_name", type="varchar(200)", desc="规格" ),
     *      @Apidoc\Returned("mail_method", type="int(11)", desc="1到店2快递3都有" ),
     *      @Apidoc\Returned("count", type="int(11)", desc="数量" ),
     *      @Apidoc\Returned("can_buy", type="varchat", desc="商品可否购买1能，0不能" ),
     *      @Apidoc\Returned("can_buy_word", type="varchat", desc="商品不可购买说明" ),
     *     )
     * )
     * @Apidoc\Returned("need_dlr", type="int(11)", desc="是否需要专营店1需要0不需要" ),
     * @Apidoc\Returned("need_18n", type="int(11)", desc="是否关联18位麻1是0否" ),
     * @Apidoc\Returned("dlr_code", type="varchar(11)", desc="专营店编码" ),
     * @Apidoc\Returned("dlr_name", type="varchar(11)", desc="专营店" ),
     * @Apidoc\Returned("ap_time", type="timestamp", desc="服务器时间" )
     * @Apidoc\Returned("user_info", type="array/json", desc="用户信息",
     *     @Apidoc\Returned("user_status", type="int(11)", desc="0无vin车用户；1有vin车但非默认车型用户；2默认车型为vin车用户" ),
     *     @Apidoc\Returned("car_type_name", type="varchar(30)", desc="用户车型" ),
     * )
     *
     */
    public function suit()
    {

    }

    /**
     * @Apidoc\Title("优惠套装--选择规格后的价格")
     * @Apidoc\Desc("退货,换货afs_type:2:退货3:换货")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/suit-price")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 优惠 套装 NI+")
     *
     * @Apidoc\Param("goods_id", type="int(11)",require=true, desc="商品ID多个可以用,隔开" )
     * @Apidoc\Param("suit_id", type="int(11)",require=false, desc="套装ID 不传时返回几个列表" )
     * @Apidoc\Param("dd_dlr_code", type="varchar",require=false, desc="到店专营店--用户改店时候传" )
     * @Apidoc\Param("kilometer",type="int",require=false, desc="公里数")
     * @Apidoc\Param("sku_ids",type="varchar",require=false, desc="setskuid列表,隔开这个值与goods_id对应，个数对应sku也是商品对应的，传了goods_id+sku_ids将会返回实际的优惠价等信息，也就是用户修改了规格之后需要重新加载整个接口，并且把用户选择的skuid对应商品传给接口")
     * @Apidoc\Param("lng", type="float",require=false, desc="经度" )
     * @Apidoc\Param("lat", type="float",require=false, desc="纬度" )
     *
     * @Apidoc\Returned("list", type="array/json", desc="套装列表",
     *     @Apidoc\Returned("suit_count", type="varchar(20)", desc="套装数量" ),
     *     @Apidoc\Returned("suit_price", type="decimal(10,2)", desc="套装价格" ),
     *     @Apidoc\Returned("suit_all_old_price", type="decimal(10,2)", desc="套装原价格" ),
     *     @Apidoc\Returned("goods_count", type="int(11)", desc="商品数量" ),
     *     @Apidoc\Returned("need_dlr", type="int(11)", desc="是否需要专营店1需要0不需要" ),
     *     @Apidoc\Returned("need_car", type="int(11)", desc="是否需要车主1需要0不需要" ),
     *     @Apidoc\Returned("need_18n", type="int(11)", desc="是否关联18位麻1是0否" ),
     *     @Apidoc\Returned("can_refund", type="int(11)", desc="是否能退1是0否" ),
     *     @Apidoc\Returned("have_content", type="int(11)", desc="是否有说明1是0否" ),
     *     @Apidoc\Returned("all_use_point", type="int(11)", desc="是否使用全积分1是0否--只有这个值==1的时候才显示积分，不显示现金" ),
     *     @Apidoc\Returned("suit_list", type="array/json", desc="套装商品列表" ,
     *      @Apidoc\Returned("name", type="varchar(20)", desc="活动名" ),
     *      @Apidoc\Returned("index_id", type="int(11)", desc="优惠套装主表id" ),
     *      @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *      @Apidoc\Returned("sku", type="int(11)", desc="商品setskuid" ),
     *      @Apidoc\Returned("price", type="decimal(10,2)", desc="优惠价格" ),
     *      @Apidoc\Returned("cover_image", type="varchar(150)", desc="封面图" ),
     *      @Apidoc\Returned("commodity_name", type="varchar(200)", desc="商品名称" ),
     *      @Apidoc\Returned("sp_value_list", type="string", desc="规格值ID" ),
     *      @Apidoc\Returned("commodity_set_id", type="int(11)", desc="上架id" ),
     *      @Apidoc\Returned("old_price", type="decimal(10,2)", desc="价格" ),
     *      @Apidoc\Returned("stock", type="int(11)", desc="库存" ),
     *      @Apidoc\Returned("count_stock", type="int(11)", desc="总库存--判断是否有货" ),
     *      @Apidoc\Returned("is_mail", type="int(11)", desc="是否快递，1是0否" ),
     *      @Apidoc\Returned("need_car", type="int(11)", desc="是否需要车主，1是0否" ),
     *      @Apidoc\Returned("is_store", type="int(11)", desc="是否门店自提，1是0否，默认是" ),
     *      @Apidoc\Returned("limit_dis", type="varchar(200)", desc="限时优惠json活动信息" ),
     *      @Apidoc\Returned("sp_name", type="varchar(200)", desc="规格" ),
     *      @Apidoc\Returned("mail_method", type="int(11)", desc="1到店2快递3都有" ),
     *      @Apidoc\Returned("count", type="int(11)", desc="数量" ),
     *      @Apidoc\Returned("can_buy", type="varchat", desc="商品可否购买1能，0不能" ),
     *      @Apidoc\Returned("can_buy_word", type="varchat", desc="商品不可购买说明" ),
     *     )
     * )
     *
     */
    public function suitPrice()
    {

    }
    /**
     * @Apidoc\Title("优惠套装详细")
     * @Apidoc\Desc("大概就一个描述")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/suit-detail")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 优惠 套装 NI+")
     *
     * @Apidoc\Param("suit_id", type="int(11)",require=true, desc="套装id" )
     *
     * @Apidoc\Returned("name", type="varchar(50)",  desc="套装名")
     * @Apidoc\Returned("detail_content", type="text",  desc="明细")
     *
     */
    public function suitDetail(GoodsValidate $validate)
    {
    }


    /**
     * @Apidoc\Title("通过商品ID拉取商品规格列表")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/change-suit")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 规格 NI+ 0214")
     *
     * @Apidoc\Param("goods_id", type="int(11)",require=true, desc="商品ID/主商品ID" )
     * @Apidoc\Param("sub_goods_id", type="int(11)",require=false, desc="子商品ID" )
     * @Apidoc\Param("suit_id", type="int(11)",require=false, desc="套装ID" )
     * @Apidoc\Param("dd_dlr_code", type="varchar",require=false, desc="到店专营店" )
     * @Apidoc\Param("kilometer", type="int",require=false, desc="公里数" )
     * @Apidoc\Param("use_discount",type="int(11)", require=false, desc="是否使用商品会员/车主价，1-使用，0-不使用(0423)")
     * @Apidoc\Param("entry_from",type="int(11)", require=false, desc="查询页面：1-购车页，2-商品详情")
     * @Apidoc\Param("act_type_id",type="int(11)", require=false, desc="当前活动：1-限时折扣 3-满减 6-N件N折 10-秒杀")
     *
     * @Apidoc\Returned("json_data", type="array/json", desc="sku数据",
     *     @Apidoc\Returned("price", type="decimal(10,2)", desc="默认价格" ),
     *     @Apidoc\Returned("stock", type="int(11)", desc="库存" ),
     *     @Apidoc\Returned("limit_dis_price", type="decimal(10,2)", desc="限时折扣sku价格" ),
     *     @Apidoc\Returned("sku_id", type="int(11)", desc="sku_id" ),
     *     @Apidoc\Returned("image", type="varchar(150)", desc="图片" ),
     *     @Apidoc\Returned("sp_value_arr", type="array/json", desc="规格值id列表（只有值没有字段）" ),
     *     @Apidoc\Returned("is_gift", type="tinyint(1)", desc="是否买赠 1是 0否(0214版本)" ),
     *     @Apidoc\Returned ("commodity_dis_user_segment",type="string",desc="商品折扣：0-无/1-会员价/2-车主价(0423)"),
     *     @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *     @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *     @Apidoc\Returned ("current_price",type="varchar(20)",desc="商品现价，活动非会员价(0423版本)")
     * )
     * @Apidoc\Returned("sp_list", type="array/json", desc="所有当前所有规格",
     *     @Apidoc\Returned("sp_name", type="varchar(150)", desc="规格名称" ),
     *     @Apidoc\Returned("sp_id", type="int(11)", desc="规格id" ),
     *     @Apidoc\Returned("sp_value_list", type="array/json", desc="详细规格",
     *         @Apidoc\Returned("sp_value_id", type="int(11)", desc="规格值ID" ),
     *         @Apidoc\Returned("sp_value_name", type="varchar(150)", desc="规格值名称" )
     *     )
     * )
     * @Apidoc\Returned("goods", type="array/json", desc="商品信息",
     *      @Apidoc\Returned("id", type="int(11)", desc="商品ID" ),
     *      @Apidoc\Returned("commodity_name", type="varchar(200)", desc="商品名称" ),
     *      @Apidoc\Returned("comm_type_id", type="int(11)", desc="分类ID" ),
     *      @Apidoc\Returned("commodity_type", type="int(11)", desc="分类名" ),
     *      @Apidoc\Returned("cover_image", type="varchar(200)", desc="首图" ),
     *      @Apidoc\Returned("detail_content", type="text", desc="详情内容" ),
     *      @Apidoc\Returned("is_pure", type="int(11)", desc="是否纯正精品1是0否" ),
     *      @Apidoc\Returned("is_integral", type="int(11)", desc="是否支持积分支付1是0否" ),
     *      @Apidoc\Returned("original_price_range_start", type="decimal(10,2)", desc="原价开始价" ),
     *      @Apidoc\Returned("original_price_range_end", type="decimal(10,2)", desc="原价结束价" ),
     *      @Apidoc\Returned("discount_price_range_star", type="decimal(10,2)", desc="现价开始价" ),
     *      @Apidoc\Returned("discount_price_range_end", type="decimal(10,2)", desc="现价结束价" ),
     *      @Apidoc\Returned("card_id", type="int(11)", desc="卡券ID" ),
     *      @Apidoc\Returned("is_mail", type="int(11)", desc="是否直邮1是0否" ),
     *      @Apidoc\Returned("count_stock", type="int(11)", desc="总库存" ),
     *      @Apidoc\Returned("commodity_attr", type="int(11)", desc="商品属性：1热销2推荐3新品4促销" ),
     *      @Apidoc\Returned("mail_price", type="int(11)", desc="运费" ),
     *      @Apidoc\Returned("is_store", type="int(11)", desc="是否到店自取1是0否" ),
     *      @Apidoc\Returned("favourable_introduction", type="varchar(200)", desc="优惠简称" ),
     *      @Apidoc\Returned("favourable_detail", type="text", desc="优惠详情" ),
     *      @Apidoc\Returned("point_discount", type="int(11)", desc="积分折扣比例为0不折扣" ),
     *      @Apidoc\Returned("integral_price", type="int(11)", desc="积分价格" ),
     *      @Apidoc\Returned("template_guid", type="int(11)", desc="运费模板ID" ),
     *      @Apidoc\Returned("sku_image", type="array/json", desc="规格图片列表（无字段只有值）" ),
     *      @Apidoc\Returned("unit", type="varchar(20)", desc="单位" ),
     *      @Apidoc\Returned("commodity_class", type="int(11)", desc="商品类型：1实物2卡券3虚拟" ),
     *      @Apidoc\Returned("commodity_card_ids", type="varchar(500)", desc="商品电子卡券id，英文逗号隔开" ),
     *      @Apidoc\Returned("sale_num", type="int(11)", desc="销售数量" ),
     *      @Apidoc\Returned("commodity_set_id", type="int(11)", desc="商品上架ID" ),
     *      @Apidoc\Returned("dlr_code", type="varchar(11)", desc="专营店编码" ),
     *      @Apidoc\Returned("dlr_name", type="varchar(11)", desc="专营店" ),
     *      @Apidoc\Returned("is_vin_car", type="int",  desc="0无vin车用户；1有vin车但非默认车型用户；2默认车型为vin车用户"),
     *      @Apidoc\Returned("video", type="varchar(250)", desc="视频" ),
     *      @Apidoc\Returned("off_shelf", type="int", desc="是否下架 有这个字段并且==1就是下架，一般是子商品" )
     * )
     *
     */
    public function changeSuit(GoodsValidate $validate)
    {

    }

    /**
     * @Apidoc\Title("快递查询")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/waybill")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 快递 NI+")
     *
     * @Apidoc\Param("order_id", type="int(11)",require=true, desc="订单ID" )
     * @Apidoc\Param("afs_id", type="int(11)",require=false, desc="售后单ID,售后再次发货时必传" )
     *
     * @Apidoc\Returned("status", type="int(11)", desc="0正常，201 快递单号为空 ，202 快递公司为空，203 快递公司不存在，204 快递公司识别失败，205 没有信息，206 快递单号错误" )
     * @Apidoc\Returned("com", type="varchar(250)", desc="承运公司" )
     * @Apidoc\Returned("number", type="varchar(250)", desc="承运编码" )
     * @Apidoc\Returned("phone", type="varchar(250)", desc="承运电话" )
     * @Apidoc\Returned("goods_img", type="array",desc="商品图片"),
     * @Apidoc\Returned("list", type="array/json", desc="状态明细",
     *     @Apidoc\Returned("status", type="int(11)", desc="状态" ),
     *     @Apidoc\Returned("time", type="datetime", desc="时间" )
     * )
     *
     */
    public function waybill(GoodsValidate $validate)
    {
//        $b_stat = array(1 => '在途中', 2 => '派件中', 3 => '已签收', 4 => '派件失败');
        $b_stat = BuOrderCommodity::$delivery_status_array;

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("waybill")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }


        $id     = $requestData['order_id'];//订单ID
        $order  = $this->order_model->getOneByPk($id);
        $empty  = 0;
        if (!$order) {
            $empty = 1;
        }
        if (!$order['waybill_number']) {
            $empty = 1;
        }
        if (input('test') == 1) {
            $way_bill = getExpressInfo($order['waybill_number'], $order['common_carrier'], $order['phone'] ?? '');
//            var_dump($way_bill);
        } else {
            $way_bill = getExpressInfo($order['waybill_number'], $order['common_carrier'], $order['phone'] ?? '');

        }
        if (!$way_bill) {
            $empty            = 1;
            $way_bill['list'] = [];
        } else {
            if ($way_bill['status'] != 0) {
                $empty            = 1;
                $way_bill['list'] = [];
            } else {
                $way_bill              = $way_bill['result'];
                $way_bill['red_class'] = '';
                $way_bill['on_class']  = '';
                if ($way_bill['deliverystatus'] != 3) {
                    $way_bill['red_class'] = "f-red";
                    $way_bill['on_class']  = 'on';
                }
                $way_bill['status'] = $b_stat[$way_bill['deliverystatus']];
                $sys_model          = new DbSystemValue();
                $way_com            = $sys_model->getOne(['where' => ['value_type' => 13, 'value_code' => $order['common_carrier']]]);
                $way_bill['com']    = $way_com['county_name'];
                $way_bill['phone']  = $way_com['remark'];
            }
        }
        return $this->setResponseData($way_bill)->send();
        print_json(0, 'success', $way_bill);
//        $this->assign('empty', $empty);
//        $this->assign('title', "物流信息");
//        $this->assign('way_bill', $way_bill);
//        return $this->fetch('Goods/waybill');
    }

    /**
     * @Apidoc\Title("运费查询")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/mail-price")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 运费 NI+")
     *
     * @Apidoc\Param("goods_id", type="int(11)",require=true, desc="商品ID" )
     * @Apidoc\Param("address", type="string",require=true, desc="省" )
     *
     * @Apidoc\Returned("message", type="string", desc="消息描述" )
     * @Apidoc\Returned("mail_price", type="decimal", desc="运费" )
     *
     */
    public function goodsMailPrice(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("mail_price")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_goods =  new NetGoods();
        $res = $net_goods->goodsMailPrice($requestData,$this->channel_type);
        if($res['code']<>200){
            return $this->setResponseError($res['msg'],$res['code'])->send();
        }else{
            return $this->setResponseData($res['msg'])->send();
        }

    }

    /**
     * @Apidoc\Title("获取sku信息")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/sku-info")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 规格 NI+")
     *
     * @Apidoc\Param("goods_id", type="int(11)",require=true, desc="商品ID" )
     * @Apidoc\Param("sku_key", type="string",require=true, desc="商品sku_key" )
     * @Apidoc\Param("is_group", type="int(11)",require=false, desc="是否团购1是0否" )
     * @Apidoc\Param("is_suit", type="int(11)",require=false, desc="是否团购套餐1是0否" )
     *
     * @Apidoc\Returned("price", type="decimal(10,2)", desc="默认价格" )
     * @Apidoc\Returned("stock", type="int(11)", desc="库存" )
     * @Apidoc\Returned("limit_dis_price", type="decimal(10,2)", desc="限时折扣sku价格" )
     * @Apidoc\Returned("sku_id", type="int(11)", desc="sku_id" )
     * @Apidoc\Returned("image", type="varchar(150)", desc="图片" )
     * @Apidoc\Returned("sp_value_arr", type="array/json", desc="规格值id列表（只有值没有字段）" )
     *
     */
    public function goodsSkuInfo(GoodsValidate $validate)
    {

    }

    /**
     * @Apidoc\Title("保存收藏")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/collection")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("商品 收藏 NI+")
     *
     * @Apidoc\Param("goods_id", type="int(11)",require=true, desc="商品ID" )
     * @Apidoc\Param("hit_type_code",type="string", require=true,desc="埋点编码(ver:1020:add)")
     *
     * @Apidoc\Returned("message", type="string",desc="ok则为收藏成功" )
     *
     */
    public function saveCollection(GoodsValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("save_collection")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $id = $requestData['goods_id'];
        if (!$id) {
            return $this->setResponseError('收藏失败,请联系客服')->send();
        }
        $source = $this->source;
        $coll_model =  new BuUserCollection();
        $where =  array('user_id' => $this->user_id, 'commodity_id' => $id,  'dlr_code' => $this->channel_type);
        $c_res = $coll_model->getOne(['where'=>$where]);
        $car_info = $this->_getCarer($this->user['bind_unionid'],$this->user['member_id']);

        if (!$c_res) {
            $data = array(
                'openid' => $this->unionid,
                'user_id' => $this->user_id,
                'commodity_id' => $id,
                'dlr_code' => $this->channel_type,
                'source' => $source,
                'vin' => isset($car_info['vin']) ? $car_info['vin'] : '',
                'license_plate' => isset($car_info['car_no']) ? $car_info['car_no'] : '',
                'name' => isset($car_info['name']) ? $car_info['name'] : '',
                'phone' => isset($car_info['mobile']) ? $car_info['mobile'] : '',
                'car_series_id' => $this->user['car_series_id'],
            );
            $res = $coll_model->insertData($data);
        } else {
            if($c_res['is_enable']==1){
                $data = array(
                    'is_enable' => 0,
                    'last_updated_date' => date('Y-m-d H:i:s'),

                );
            }else{
                $data = array(
                    'is_enable' => 1,
                    'last_updated_date' => date('Y-m-d H:i:s'),

                );
            }
            $where = array(
                'user_id' => $this->user_id,
                'commodity_id' => $id,
            );

            $res = $coll_model->saveData($data, $where);
        }

        if ($res) {
            return $this->setResponseData('ok')->send();
        } else {
            return $this->setResponseError("收藏失败")->send();
        }
    }

    /**
     * @Apidoc\Title("车型是否匹配商品--")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/mate")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 车型 NI+")
     *
     * @Apidoc\Param("goods_id", type="int(11)",require=true, desc="商品ID" )
     * @Apidoc\Param("car_series_id", type="int(11)",require=true, desc="车型ID" )
     *
     * @Apidoc\Returned("is_mate", type="tinyint(1)",desc="是否匹配(0不匹配1匹配2用户没车型)" )
     *
     */
    public function goodsCarMate(GoodsValidate $validate){
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("goodsCarMate")->check($requestData);
        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $goods_id =  $requestData['goods_id'];
        $car_s_id =  $requestData['car_series_id'];
        $flat_model =  new DbCommodityFlat();
        $where = ['commodity_id'=>$goods_id];
        $where[] = ['exp', " (find_in_set('{$this->channel_type}',up_down_channel_dlr)) "];
        $goods_flat =  $flat_model->getOne(['where'=>$where,'field'=>"commodity_id,car_series_id"]);
        if(!$goods_flat){
            return $this->setResponseError("商品异常")->send();
        }
        $is_mate =1;
        if(!empty($goods_flat['car_series_id'])){
            $car_s_arr = explode(",",$goods_flat['relate_car_ids']);
            if(!in_array($car_s_id,$car_s_arr)){
                $is_mate =0;
            }
        }
        return $this->setResponseData(['is_mate'=>$is_mate])->send();
    }
    /**
     * @Apidoc\Title("客服小I")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/xi-kf")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("客服 NI+")
     * @Apidoc\Param("unique_sm",type="string", require=false,desc="")
     * @Apidoc\Param("open_sm",type="string", require=false,desc="")
     * @Apidoc\Param("random_sm",type="string", require=false,desc="")
     * @Apidoc\Param("time_sm",type="string", require=false,desc="")
     * @Apidoc\Param("channel_sm",type="string", require=false,desc="")
     *
     *     @Apidoc\Returned("c_service", type="varchar",desc="客服url" )
     *
     */
    public function XiKf(){
        if (!$this->user_id) {
            return $this->setResponseError("需要登录", 404)->send();
        }
        $net_goods =  new NetGoods();
        $row =  $net_goods->XiKf( $this->user, $this->channel_type);
        return $this->setResponseData(['c_service'  => $row['msg']])->send();
    }


    /**
     * @Apidoc\Title("客服小I小程序原生版")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/xi-kf-sm")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("客服 sm win NI+")
     * @Apidoc\Param("unique_sm",type="string", require=false,desc="")
     * @Apidoc\Param("open_sm",type="string", require=false,desc="")
     * @Apidoc\Param("random_sm",type="string", require=false,desc="")
     * @Apidoc\Param("time_sm",type="string", require=false,desc="")
     * @Apidoc\Param("channel_sm",type="string", require=false,desc="")
     *
     *     @Apidoc\Returned("c_service", type="varchar",desc="客服参数" )
     *
     */
    public function XiKfSm(){

    }

    /**
     * @Apidoc\Title("计算积分值--组合商品必用")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/point-js")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("商品 分类 NI+")
     *
     * @Apidoc\Param("goods_id", type="int(10)",require=true, desc="商品ID" )
     * @Apidoc\Param("price", type="float",require=true, desc="价格" )
     *
     * @Apidoc\Returned("point", type="int", desc="最高可用积分" ),
     * @Apidoc\Returned ("pay_com",type="array/json",desc="最高可用积分内容",
     *     @Apidoc\Returned ("pay_rule", type="int(11)", desc="支付规则 1 至少支付N元 2按%比"),
     *     @Apidoc\Returned ("pay_price", type="float", desc="金额或者百分比"),
     * ),
     *
     *
     */
    public function point_js()
    {

    }
    //N件N折折扣信息 通过Nid获取商品列表
    public function getNDisCountNid($n_id)
    {
        $n_dis_goods_model = new DbNDiscountCommodity();
        $time              = date('Y-m-d H:i:s');
        $where             = [
            'b.start_time' => ['<=', $time], 'b.end_time' => ['>=', $time], 'b.is_enable' => 1, 'a.is_enable' => 1, "b.id" => $n_id
        ];
        $param['where']    = $where;
        $param['group']    = "b.id";
        $param['field']    = "GROUP_CONCAT(a.commodity_id SEPARATOR ',') g_ids";
        $row               = $n_dis_goods_model->getNdisInfo($param);
//        echo $n_dis_goods_model->getLastSql();
        return $row;
    }

    /**
     * @Apidoc\Title("价格区间列表")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/goods/get-section")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("无权 NI+ 价格")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Returned ("section_name",type="varchar(100)",desc="区间名称")
     * @Apidoc\Returned ("section_price_start",type="int(10)",desc="区间开始价格")
     * @Apidoc\Returned ("section_price_end",type="int(10)",desc="区间结束价格")
     *
     */
    public function getSection(){}


    /**
     * @Apidoc\Title("获取工时价格原价信息")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/goods/get-wi-price")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 工时 价格 win")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("dlr_code",type="string", require=true,desc="经销商编号")
     * @Apidoc\Param("car_18n",type="string", require=true,desc="车型18位码")
     * @Apidoc\Param("sku_ids",type="string", require=true,desc="sku_id，多个用,隔开，需要和sku_num一一对应")
     * @Apidoc\Param("sku_num",type="string", require=true,desc="当前sku购买数量，多个用,隔开，需要和sku_ids一一对应")
     *
     * @Apidoc\Returned ("wi_price_list",type="array/json",desc="工时价格列表",
     *     @Apidoc\Returned ("sku_id",type="int(10)",desc="sku_id"),
     *     @Apidoc\Returned ("sku_num",type="int(10)",desc="当前sku购买数量"),
     *     @Apidoc\Returned ("commodity_id",type="int(10)",desc="商品id"),
     *     @Apidoc\Returned ("work_time_number",type="int(10)",desc="工时数量"),
     *     @Apidoc\Returned ("work_time_code",type="int(10)",desc="工时编码"),
     *     @Apidoc\Returned ("work_time_price",type="decimal(10,2)",desc="当前sku工时单价"),
     *     @Apidoc\Returned ("wi_price_all",type="decimal(10,2)",desc="当前sku总工时价格"),
     * )
     * @Apidoc\Returned ("wi_price_all",type="decimal(10,2)",desc="所有sku总工时价格")
     *
     */
    public function getWiPrice(){}

    /**
     * @Apidoc\Title("获取赠品列表")
     * @Apidoc\Author("cww")
     * @Apidoc\Url("/net-small/goods/get-gift-list")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ win 0214")
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("goods_id", type="int(11)",require=true, desc="主品商品ID" )
     * @Apidoc\Param("activity_id", type="int(11)",require=true, desc="买赠活动ID" )
     * @Apidoc\Param("dd_dlr_code", type="varchar",require=false, desc="主品选择的专营店编码" )
     *
     * @Apidoc\Returned("des", type="varchar(200)",desc="活动描述" )
     * @Apidoc\Returned("optional_number", type="int(10)",desc="可选件数" )
     * @Apidoc\Returned("mail_method", type="tinyint(1)",desc="主商品 1到店 2快递" )
     * @Apidoc\Returned("user_car_series", type="tinyint(1)",desc="用户当前车型" )
     * @Apidoc\Returned("dlr_code", type="tinyint(1)",desc="专营店编码" )
     * @Apidoc\Returned("dlr_name", type="tinyint(1)",desc="专营店名称" )
     * @Apidoc\Returned("goods_list", type="array/json",desc="赠品商品列表",
     *     @Apidoc\Returned ("commodity_name",type="varchar(200)",desc="商品名称"),
     *     @Apidoc\Returned ("commodity_id",type="int(10)",desc="商品ID"),
     *     @Apidoc\Returned ("commodity_set_id",type="int(10)",desc="商品上架ID"),
     *     @Apidoc\Returned ("cover_image",type="string",desc="首图"),
     *     @Apidoc\Returned("commodity_class", type="int(11)", desc="商品类型：1实物2卡券3虚拟" ),
     *     @Apidoc\Returned("de_sku_id", type="int(11)", desc="默认skuid"),
     *     @Apidoc\Returned("mail_method", type="tinyint(1)",desc="赠品类型 1到店 2快递" ),
     *     @Apidoc\Returned("is_mate", type="int", desc="车型是否可以购买1可以，其他不行" ),
     *     @Apidoc\Returned("count_stock", type="int", desc="总库存" ),
     *     @Apidoc\Returned("de_sku_stock", type="int", desc="默认规格库存" ),
     *     @Apidoc\Returned("json_data", type="array/json", desc="sku_list等数据",
     *     @Apidoc\Returned("sku_list", type="array/json", desc="当前sku以sp_list映射通过主键ID获得对应数组",
     *          @Apidoc\Returned("price", type="decimal(10,2)", desc="价格" ),
     *          @Apidoc\Returned("limit_dis_price", type="decimal(10,2)", desc="限时折扣sku价格/无活动==price" ),
     *          @Apidoc\Returned("stock", type="int(10)", desc="库存" ),
     *          @Apidoc\Returned("set_sku_id", type="int(10)", desc="上架id上架规格取这个" ),
     *          @Apidoc\Returned("sku_id", type="int(10)", desc="规格ID" ),
     *          @Apidoc\Returned("image", type="varchar(200)", desc="图片" ),
     *          @Apidoc\Returned("sp_value_arr", type="array/json", desc="规格主键列表（无字段只有值）" ),
     *     ),
     *     @Apidoc\Returned("sku_image", type="array/json", desc="规格图片列表（无字段只有值）" ),
     *     @Apidoc\Returned("sp_list_group", type="array/json", desc="团购时的规格列表，格式同sku_list" ),
     *      @Apidoc\Returned("groups_data", type="array/json", desc="组合子商品" ,
     *        @Apidoc\Returned("sub_commodity_list", type="array/json", desc="组合子商品列表",
     *           @Apidoc\Returned("stock", type="int", desc="库存" ),
     *           @Apidoc\Returned("is_mate", type="int", desc="车型是否可以购买1可以，其他不行" ),
     *           @Apidoc\Returned("sp_value_list", type="string", desc="规格值" ),
     *           @Apidoc\Returned("min_price", type="float", desc="最低的sku价格" ),
     *           @Apidoc\Returned("commodity_id", type="int", desc="组合主商品id" ),
     *           @Apidoc\Returned("group_sub_commodity_id", type="int", desc="组合子商品id" ),
     *           @Apidoc\Returned("commodity_name", type="string", desc="组合子商品名" ),
     *           @Apidoc\Returned("image", type="string", desc="组合子商品图片" ),
     *           @Apidoc\Returned("sp_value_name", type="string", desc="组合子商品规值" ),
     *           @Apidoc\Returned("sp_value_list", type="string", desc="规格值" ),
     *           @Apidoc\Returned("set_sku_id", type="int", desc="上架id--规格ID，下单\加购都用这个" ),
     *           @Apidoc\Returned("initial_num", type="int", desc="初始数量" ),
     *           @Apidoc\Returned("user_can_des", type="int", desc="用户是否可增减数量" ),
     *           @Apidoc\Returned("can_select", type="int", desc="是否可选" ),
     *           @Apidoc\Returned ("relate_car_ids",type="text",desc="关联车系ID"),
     *           @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="子工时数量"),
     *           @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="子工时编码"),
     *           @Apidoc\Returned ("work_time_price",type="varchar(50)",desc="子工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *              )
     *        ),
     *     ),
     * @Apidoc\Returned("sp_list", type="array/json", desc="所有当前所有规格",
     *     @Apidoc\Returned("sp_name", type="varchar(150)", desc="规格名称" ),
     *     @Apidoc\Returned("sp_id", type="int(11)", desc="规格id" ),
     *     @Apidoc\Returned("sp_value_list", type="array/json", desc="详细规格",
     *         @Apidoc\Returned("sp_value_id", type="int(11)", desc="规格值ID" ),
     *         @Apidoc\Returned("sp_value_name", type="varchar(150)", desc="规格值名称" )
     *     )
     * )
     * )
     * )
     *
     *
     * )
     *
     */
    public function getGiftList(){}




    /**
     * @Apidoc\Title("获取秒杀用户信息")
     * @Apidoc\Author("yang")
     * @Apidoc\Url("/net-small/goods/get-seckill-user")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("秒杀 0315")
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("goods_id",type="string", require=true,desc="商品id")
     *
     * @Apidoc\Returned ("key",type="array/json",desc="工时价格列表",
     *     @Apidoc\Returned ("copy_writing",type="string",desc="文案"),
     *     @Apidoc\Returned ("order_code",type="string",desc="订单编码"),
     * )
     *
     */

    public function getSeckillUser(){}

    /**
     * @Apidoc\Title("规格对应详情")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/goods/sku-detail-content")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("sku 详情")
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("commodity_id",type="int", require=true,desc="商品id")
     * @Apidoc\Param("sku_id",type="int", require=true,desc="商品sku_id")
     *
     * @Apidoc\Returned ("commodity_id",type="int",desc="商品id"),
     * @Apidoc\Returned ("sku_id",type="int",desc="商品sku_id"),
     * @Apidoc\Returned ("commodity_sku_detail",type="text",desc="商品sku_id对应详情"),
     *
     */
    public function skuDetailContent(){}


    /**
     * @Apidoc\Title("获取赠品券剩余次数--搭配选择用户选择商品数使用")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/goods/gift_card_num")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("sku 详情")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("sku_json", type="array", childrenType="object", desc="sku列表",
     *      @Apidoc\Param("sku_id", type="string", desc="set_sku_id，组合商品就传多个，普通商品就传[{sku_id:}]"),
     * ),
     *
     * @Apidoc\Returned ("number",type="int",desc="次数")
     * @Apidoc\Returned ("card_id",type="int",desc="卡券ID")
     * @Apidoc\Returned ("card_desc",type="string",desc="卡券描述")
     *
     */
    public function getGiftCardNum(){}

    /**
     * @Apidoc\Title("判断赠品是否有券--sku_id")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/check_gift_card")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("sku 详情")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("sku_id", type="inst", desc="set_sku_id，组合商品不请求"),
     *
     * @Apidoc\Returned ("have_gift_card",type="int",desc="是否有赠品券  1有 0无")
     * @Apidoc\Returned ("goods_id",type="string",desc="商品ID，多个商品用,隔开")
     *
     */
    public function checkGiftCard(){}



    /**
     * @Apidoc\Title("检查VIN与SKU匹配")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/goods/check-vin-sku")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("VIN SKU 匹配")
     *
     * @Apidoc\Param("sku_code", type="string", require=true, desc="商品SKU编码，多个用逗号分隔")
     * @Apidoc\Param("kilometer", type="int", require=false, desc="车辆公里数")
     *
     * @Apidoc\Returned("result", type="int", desc="1-匹配 0-不匹配")
     */
    public function checkVinSku(GoodsValidate $validate){}

}
