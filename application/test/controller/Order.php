<?php
/**
 * Created by PhpStorm.
 * User: lzx
 * Date: 2020/12/22
 * Time: 3:11 PM
 */

namespace app\test\controller;


use api\wechat\Carer;
use app\common\model\bu\BuOrderRefund;
use app\common\model\bu\BuPhoneCheck;
use app\common\model\bu\BuQyUserAll;
use app\common\model\db\DbCommodity;
use app\common\model\db\DbCommodityDlrType;
use app\common\model\db\DbDlr;
use app\common\model\db\DbFightGroup;
use app\common\model\db\DbOrderRefundReason;
use app\common\net_service\NetOrder;
use app\common\model\act\AcGroup;
use app\common\model\bu\BuOrder;
use app\common\model\bu\BuOrderCommodity;
use app\common\net_service\PayCenter;
use app\common\port\connectors\Crm;
use app\common\port\connectors\Member;
use app\common\port\connectors\Payment;
use ForkModules\Traits\ResponseTrait;
use app\common\validate\Order as OrderValidate;
use tool\Logger;
use app\net_small\controller\Common;
use hg\apidoc\annotation as Apidoc;


/**
 * 订单中心
 * @Apidoc\Group("mall")
 */
class Order extends Common
{
    use ResponseTrait;

    protected $order_model = [];
    protected $order_commodity_model = [];
    private $point_times = 10;//积分倍数

    private $car_vip = ['会员金卡', '会员金卡(VIP)', '会员金卡（VIP）', '会员银卡', '会员银卡VIP', '会员银卡（VIP）', '员工卡', '铂金卡', '黑卡'];//金银卡会员登记，用于积分兑换
    private $jk_goods = [2780, 2782, 2784, 2786, 2788, 2790];
    private $pk_goods = [2779, 2781, 2783, 2785, 2787, 2789];
    //口罩商品ID
    private $kz_goods = [3790];
    private $kz_dlr = "JSJY01";//口罩专营店

    private function sj_ccs()
    {
        if (config('app_status') == "develop") {
            $ids = [2940, 2941];
        } else {
            $ids = [3610, 3611];
        }
        return $ids;
    }

    public function __construct()
    {

        parent::__construct();
        $this->order_model           = new BuOrder();
        $this->order_commodity_model = new BuOrderCommodity();
        if (!$this->user_id) {
            return $this->setResponseError('请登录!')->send();
        }

    }


    /**
     * @Apidoc\Title("填充订单")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/fill")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("订单 下单 NI+ 0214")
     *
     *
     * @Apidoc\Param("sku_ids", type="string",require=true, desc="商品SKUID多个用,分开" )
     * @Apidoc\Param("cart_id", type="string",require=true, desc="购物车多个用,分开" )
     * @Apidoc\Param("is_subs", type="string",require=true, desc="是否组合商品，包含了普通商品+组合商品下单时候使用，最多只能有一个组合商品，sku_ids传组合商品主skuid+普通商品id，is_subs对应传 1或者0 ，比如：0,0,1则前面两个为普通商品，最后一个为组合商品，数量与sku_ids一致" )
     * @Apidoc\Param("numbers", type="string",require=true, desc="商品对应数量，用,分开，必须与商品ID一致" )
     * @Apidoc\Param("order_source", type="int",require=true, desc="订单来源：请查看开发必读-》订单说明-》来源列表" )
     * @Apidoc\Param("promotion_source", type="int",require=true, desc="订单促销渠道：0无，1团购，2套装，3臻享服务包，4 保养推荐，5保养套餐类 6移动积分兑换7双11随心配" )
     * @Apidoc\Param("is_by_tc", type="int",require=true, desc="0普通订单；1老优惠；2N延保 3心悦套餐；4五年双保；5车联服务包" )
     * @Apidoc\Param("name", type="string",require=true, desc="姓名，老友惠保养套餐填写" )
     * @Apidoc\Param("phone", type="string",require=true, desc="电话，老友惠保养套餐填写" )
     * @Apidoc\Param("distance", type="string",require=true, desc="里程数，心悦保养套餐填写" )
     * @Apidoc\Param("channel_type", type="string",require=true, desc="渠道" )
     * @Apidoc\Param("act_type_id", type="int",require=true, desc="活动类型ID：1限时折扣2团购3满减4全积分折扣5套装6N件N折7预售8立减9臻享服务包10:秒杀11:买赠" )
     * @Apidoc\Param("act_id", type="int",require=true, desc="活动类型对应ID一个商品参与一个活动" )
     * @Apidoc\Param("wi_act_type_id", type="int",require=true, desc="工时活动类型ID：1限时折扣3满减" )
     * @Apidoc\Param("wi_act_id", type="int",require=true, desc="工时活动类型对应ID一个商品参与一个活动" )
     * @Apidoc\Param("nick_name", type="string",require=true, desc="昵称，团购要传" )
     * @Apidoc\Param("head_img", type="string",require=true, desc="头像，团购要传" )
     * @Apidoc\Param("dd_dlr_code", type="string",require=false, desc="到店专营店，到店类型要传" )
     * @Apidoc\Param("group_order_code", type="string",require=true, desc="团长的group_code，同商品详情接口" )
     * @Apidoc\Param("mail_method", type="int",require=true, desc="快递或者到店：1到店2快递" )
     * @Apidoc\Param("work_time_json",type="array", require=false, desc="单商品工时json二维（sku_id:set_sku_id,work_time_number:工时数量，work_time_code:工时code,work_time_price:工时单价）从sku_list中获取，work_time_price或者work_time_number等于0时候不要传")
     * @Apidoc\Param("com_json",type="array", require=false, desc="子商品列表JSON（count:数量，sku:set_sku_id,mail_method:快递或者到店,work_time_number:工时数量，work_time_code:工时code,work_time_price:工时单价）不存在时候不要传")
     * @Apidoc\Param("gift_act_id",type="int(11)", require=false, desc="参与赠品活动ID，不参与可以传0"),
     * @Apidoc\Param("gift_card_id",type="int(11)", require=false, desc="赠品券ID，不参与可以传0。多个商品时候用,隔开跟number一样"),
     * @Apidoc\Param("gift_c_json", type="array", childrenType="object", desc="赠品商品列表数组",
     *     @Apidoc\Param("commodity_id", type="string", desc="赠品商品ID"),
     *     @Apidoc\Param("set_sku_id", type="string", desc="赠品商品set_sku_id"),
     *     @Apidoc\Param("count", type="string", desc="数量"),
     *     @Apidoc\Param("mail_method", type="string", desc="送货类型，1到店/2快递"),
     *     @Apidoc\Param("dd_dlr_code", type="string", desc="到店门店编码"),
     *     @Apidoc\Param("com_json", type="array", childrenType="object", desc="赠品为组合商品，没有组合时候不要传",
     *         @Apidoc\Param("count", type="string", desc="数量"),
     *         @Apidoc\Param("sku", type="string", desc="set_skuid"),
     *         @Apidoc\Param("mail_method", type="string", desc="送货类型:快递或者到店"),
     *     ),
     * ),
     * @Apidoc\Param("gift_c_json_list", type="array", childrenType="object", desc="赠品券商品列表数组--多维数组--key==主品ID--保养推荐使用",
     *     @Apidoc\Param("commodity_id", type="string", desc="赠品商品ID"),
     *     @Apidoc\Param("set_sku_id", type="string", desc="赠品商品set_sku_id"),
     *     @Apidoc\Param("count", type="string", desc="数量"),
     *     @Apidoc\Param("mail_method", type="string", desc="送货类型，1到店/2快递"),
     *     @Apidoc\Param("dd_dlr_code", type="string", desc="到店门店编码"),
     *     @Apidoc\Param("gift_card_id", type="int", desc="赠品券ID"),
     * ),
     * @Apidoc\Param("hit_type_code",type="string", require=false,desc="埋点编码,下单时必传(ver:1020:add)"),
     * @Apidoc\Param("pick_up_order_type",type="int(11)", require=false,desc="否取送车0非取送车1旧版取送车2新版取送车"),
     * @Apidoc\Param("pick_up_order_vin",type="string", require=false,desc="是否取送车VIN"),
     * @Apidoc\Param("pick_up_order_prices",type="string", require=false,desc="取送车规格对应价格,分开，与sku_ids对应")
     * @Apidoc\Param("pick_up_order_sku_nums",type="string", require=false,desc="取送车备件库数量,分开，与sku_ids对应")
     * @Apidoc\Param("pick_up_order_sku_codes",type="string", require=false,desc="取送车备件号,分开，与sku_ids对应")
     * @Apidoc\Param("use_discount",type="int(11)", require=false, desc="预估价是否使用商品会员/车主价，1-使用，0-不使用(0423)"),
     * @Apidoc\Param("buy_car_no",type="varchar", require=false, desc="购车合同号，交车页过来需要带"),
     * @Apidoc\Param("car_config_code",type="varchar", require=false, desc="18位码，交车页过来需要带"),
     * @Apidoc\Param("vehicle_order_no",type="varchar", require=false, desc="整车订单号"),
     * @Apidoc\Returned ("order_id",type="int(11)",desc="订单ID后续都用此ID")
     *
     */
//$pick_up_order_type = $requestData['pick_up_order_type']??0;//是否取送车0非取送车1旧版取送车2新版取送车
//$pick_up_order_vin = $requestData['pick_up_order_vin']??'';//取送车VIN，有这个就代替了order_vin
//$pick_up_order_prices = $requestData['pick_up_order_prices']??'';//取送车传价格
    public function fill()
    {
    }

    /**
     * @Apidoc\Title("订单确认")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/confirm")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("订单 下单 NI+ 0214")
     *
     * @Apidoc\Param("order_id", type="int",require=true, desc="订单ID" )
     * @Apidoc\Param("x_timestamp", type="int",require=true, desc="时间戳(10位)" )
     * @Apidoc\Param("x_nonce_str", type="string",require=true, desc="随机字符串" )
     * @Apidoc\Param("x_sign", type="string",require=true, desc="签名结果，算法：md5( [order_id] + x_nonce_str + x_timestamp + x_key) KEY: FbDOCNQMwawE1MKALGoCGyoEcBMBGAZgBZgYw4pcg" )
     *
     * @Apidoc\Returned("order", type="array/json", desc="订单信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="订单id" ),
     *     @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     *     @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *     @Apidoc\Returned("openid", type="varchar(50)", desc="openid" ),
     *     @Apidoc\Returned("vin", type="varchar(20)", desc="vin" ),
     *     @Apidoc\Returned("license_plate", type="varchar(30)", desc="车牌" ),
     *     @Apidoc\Returned("ic_card_no", type="varchar(30)", desc="会员号" ),
     *     @Apidoc\Returned("name", type="varchar(30)", desc="姓名" ),
     *     @Apidoc\Returned("phone", type="varchar(20)", desc="手机号码" ),
     *     @Apidoc\Returned("source", type="varchar(32)", desc="来源" ),
     *     @Apidoc\Returned("total_money", type="decimal(11,2)", desc="总金额" ),
     *     @Apidoc\Returned("money", type="decimal(11,2)", desc="实付金额" ),
     *     @Apidoc\Returned("card_id", type="varchar(200)", desc="卡劵" ),
     *     @Apidoc\Returned("card_code", type="varchar(50)", desc="卡劵code" ),
     *     @Apidoc\Returned("card_money", type="decimal(11,2)", desc="卡劵金额" ),
     *     @Apidoc\Returned("integral", type="int(11)", desc="会员当前积分【厂家积分】" ),
     *     @Apidoc\Returned("payment_method", type="varchar(20)", desc="支付方式（1现金；2积分；3卡劵；4现金+积分；5现金+卡劵；6积分+卡劵；7现金+积分+卡劵）" ),
     *     @Apidoc\Returned("order_status", type="tinyint(1)", desc="订单状态：请查看开发必读-》订单说明-》状态列表" ),
     *     @Apidoc\Returned("verification_user", type="varchar(10)", desc="核销人员" ),
     *     @Apidoc\Returned("settlement_user", type="varchar(10)", desc="结算人员" ),
     *     @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用，1是0否" ),
     *     @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *     @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *     @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *     @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *     @Apidoc\Returned("logistics_mode", type="tinyint(4)", desc="物流方式：1自提2快递" ),
     *     @Apidoc\Returned("common_carrier", type="varchar(30)", desc="承运公司" ),
     *     @Apidoc\Returned("waybill_number", type="varchar(30)", desc="运单编号" ),
     *     @Apidoc\Returned("delivery_time", type="datetime", desc="发货时间" ),
     *     @Apidoc\Returned("commodity_dlr_type_id", type="int(10)", desc="商品专营店分类ID" ),
     *     @Apidoc\Returned("mail_price", type="decimal(10,2)", desc="运费" ),
     *     @Apidoc\Returned("receipt_address", type="varchar(100)", desc="收货地址" ),
     *     @Apidoc\Returned("pay_order_code", type="varchar(32)", desc="支付订单号" ),
     *     @Apidoc\Returned("zt_status", type="tinyint(4)", desc="自提状态（1供应商已发货；2货已到专营店，请等待专营店预约到店安装；3发货中）" ),
     *     @Apidoc\Returned("order_source", type="tinyint(4)", desc="订单来源：请查看开发必读-》订单说明-》来源列表" ),
     *     @Apidoc\Returned("settlement_date", type="datetime", desc="扎帐时间" ),
     *     @Apidoc\Returned("dlr_integral", type="int(11)", desc="专营店积分" ),
     *     @Apidoc\Returned("sub_commission_id", type="int(11)", desc="分佣id" ),
     *     @Apidoc\Returned("full_cut_card_id", type="varchar(200)", desc="满减卡券" ),
     *     @Apidoc\Returned("full_id", type="int(11)", desc="满减ID" ),
     *     @Apidoc\Returned("new_car_bag_ac_id", type="int(11)", desc="礼包活动表主键" ),
     *     @Apidoc\Returned("remark", type="varchar(255)", desc="备注" ),
     *     @Apidoc\Returned("sale_source", type="int(11)", desc="销售来源：2普通商城，5官微商城" ),
     *     @Apidoc\Returned("ca_id", type="varchar(50)", desc="ca id" ),
     *     @Apidoc\Returned("user_id", type="int(11)", desc="用户表主键" ),
     *     @Apidoc\Returned("is_all_comment", type="tinyint(1)", desc="是否都评价了：1都评价了0未评价" ),
     *     @Apidoc\Returned("shop_lifting_id", type="tinyint(4)", desc="到店礼id" ),
     *     @Apidoc\Returned("ticket_no", type="varchar(500)", desc="券号" ),
     *     @Apidoc\Returned("jifen_user_id", type="int(11)", desc="对应福利商城用户id" ),
     *     @Apidoc\Returned("shop_integral", type="decimal(10,2)", desc="商城积分" ),
     *     @Apidoc\Returned("address_area_ids", type="varchar(30)", desc="地址id拼接" ),
     *     @Apidoc\Returned("refund_money", type="decimal(11,2)", desc="退款金额" ),
     *     @Apidoc\Returned("pay_time", type="datetime", desc="支付时间" ),
     *     @Apidoc\Returned("gift_score", type="varchar(10)", desc="赠送积分" ),
     *     @Apidoc\Returned("province_id", type="int(11)", desc="省ID，用于运费模板" ),
     *     @Apidoc\Returned("is_by_tc", type="tinyint(1)", desc="0普通订单；1老优惠；2N延保 3心悦套餐；4五年双保；5车联服务包" ),
     *     @Apidoc\Returned("product_card_pwd", type="string", desc="卡券密码" ),
     *     @Apidoc\Returned("product_card_money", type="decimal(11,2)", desc="卡券金额" ),
     *     @Apidoc\Returned("is_cc_ok", type="tinyint(1)", desc="0积分卡券核销成功，1积分扣失败，2卡券核销失败" ),
     *     @Apidoc\Returned("act_code", type="varchar(42)", desc="活动编码" ),
     *     @Apidoc\Returned("n_dis_id", type="int(11)", desc="N件N折ID" ),
     *     @Apidoc\Returned("is_send_e3s", type="tinyint(1)", desc="是否下发e3s，0未1已下发" ),
     *     @Apidoc\Returned("recharge_time", type="datetime", desc="充值时间" ),
     *     @Apidoc\Returned("pay_order_code2", type="varchar(50)", desc="定金支付编码" ),
     *     @Apidoc\Returned("front_money", type="decimal(11,2)", desc="定金" ),
     *     @Apidoc\Returned("front_money_dedu", type="decimal(11,2)", desc="定金抵扣，定金膨胀" ),
     *     @Apidoc\Returned("pre_sale_id", type="int(11)", desc="预售ID" ),
     *     @Apidoc\Returned("pre_point", type="decimal(11,2)", desc="预售使用积分" ),
     *     @Apidoc\Returned("comment_star", type="tinyint(1)", desc="星星 1-5" ),
     *     @Apidoc\Returned("distance", type="varchar(30)", desc="公里数" ),
     *     @Apidoc\Returned("pay_dlr_code", type="varchar(100)", desc="特殊支付账号" ),
     *     @Apidoc\Returned("cashier_trade_no", type="varchar(50)", desc="财务中心支付单号" ),
     *     @Apidoc\Returned("cashier_trade_no2", type="varchar(50)", desc="预售财务中心支付单号" ),
     *     @Apidoc\Returned("is_plat_settlement", type="tinyint(1)", desc="是否平台结算0未1已结算" ),
     *     @Apidoc\Returned("parent_order_code", type="varchar(50)", desc="父单号" ),
     *     @Apidoc\Returned("channel", type="tinyint(1)", desc="销售渠道，1小程序2官网3app" ),
     *     @Apidoc\Returned("parent_order_type", type="tinyint(1)", desc="3父订，正常显示为1原单2子单" ),
     *     @Apidoc\Returned("b_act_goods_price", type="decimal(11,2)", desc="商品原价总价" ),
     *     @Apidoc\Returned("cashier_settlement_no", type="varchar(50)", desc="中心结算单号" ),
     *     @Apidoc\Returned("is_anonymous", type="tinyint(1)", desc="是否匿名，0不匿名，1匿名" ),
     *     @Apidoc\Returned("plat_order_id", type="varchar(50)", desc="平台返回业务系统ID" ),
     *     @Apidoc\Returned("cashier_settlement_no2", type="varchar(50)", desc="预售中心结算单号" ),
     *     @Apidoc\Returned("is_mail", type="tinyint(1)", desc="是否邮寄：1是0否" ),
     *     @Apidoc\Returned("is_store", type="tinyint(1)", desc="是否到店：1是0否" ),
     *     @Apidoc\Returned("use_pv_point", type="decimal(11,2)", desc="需使用积分" ),
     *     @Apidoc\Returned("all_work_time_money", type="decimal(11,2)", desc="工时可用总现金" ),
     *     @Apidoc\Returned("all_work_time_point", type="decimal(11,2)", desc="工时可用总积分" ),
     *     @Apidoc\Returned("all_work_actual_time", type="decimal(11,2)", desc="工时总折后价" ),
     *     @Apidoc\Returned("all_work_time_dis", type="int", desc="订单工时总优惠 " ),
     *     @Apidoc\Returned("all_work_time", type="int", desc="订单工时总原价 " ),
     *     @Apidoc\Returned("all_maintain_dis", type="string",desc="保养套餐优惠"),
     *     @Apidoc\Returned("all_segment_membership_dis", type="decimal(11,2)", desc="会员优惠" ),
     *     @Apidoc\Returned("all_segment_owner_dis", type="decimal(11,2)", desc="车主优惠" ),
     *     @Apidoc\Returned("cant_pv_point", type="tinyint(1)", desc="是否可使用积分，1是0否" ),
     *     @Apidoc\Returned("pay_btn_word", type="string", desc="支付按钮描述" ),
     *     @Apidoc\Returned("cannt_c_dlr", type="tinyint(1)", desc="是否可选择专营店，1是0否" ),
     *     @Apidoc\Returned("cannot_change_add", type="tinyint(1)", desc="是否可选择专营店，1是0否" ),
     *     @Apidoc\Returned("most_use_pv_point", type="decimal(11,2)", desc="最高使用可使用积分" ),
     *     @Apidoc\Returned("point_least", type="decimal(11,2)", desc="最少要使用积分" ),
     *     @Apidoc\Returned("must_address", type="int(10)", desc="1需要显示地址0不需要-" ),
     *     @Apidoc\Returned("have_work_price", type="int(10)", desc="0不需要显示工时1需要显示工时-" ),
     *     @Apidoc\Returned("have_discounts", type="int(10)", desc="可进行优惠活动/卡券选择，1-是，0-否" ),
     *     @Apidoc\Returned("is_forwarded", type="int(10)", desc="是否已经确认优惠方案，1-确认，0-未确认" ),
     *     @Apidoc\Returned("choose_card_ids", type="int(10)", desc="选中的卡券IDs，多个逗号分隔" ),
     *     @Apidoc\Returned("addr_tips", type="varchar(200)", desc="充电桩地址提醒" ),
     *     @Apidoc\Returned("tips", type="varchar(200)", desc="订单温馨提示" ),
     *     @Apidoc\Returned("crowdfund_agn", type="string",desc="众筹协议"),
     *     @Apidoc\Returned("liability_clause", type="string",desc="延保服务包协议名"),
     *     @Apidoc\Returned("liability_clause_pdf", type="string",desc="延保服务包协议pdf"),
     *      @Apidoc\Returned("choose_promotion_json", type="array", childrenType="object", desc="选中的优惠活动",
     *          @Apidoc\Returned("commodity_id", type="string", desc="商品ID"),
     *          @Apidoc\Returned("set_sku_id", type="string", desc="商品set_sku_id"),
     *          @Apidoc\Returned("act_id", type="string", desc="活动ID"),
     *          @Apidoc\Returned("act_type", type="string", desc="活动类型，1-限时折扣 3-满减  6-N件N折 10-秒杀"),
     *      ),
     *     @Apidoc\Returned("ly_bank_code_choose", type="varchar", desc="店支付行，选择专营店需要此字段进行过滤" ),
     *     @Apidoc\Returned("address_default", type="array", desc="用户默认地址,订单确认页默认显示用户默认地址,如故没有就添加地址",
     *          @Apidoc\Returned("name", type="string", desc="收件人" ),
     *          @Apidoc\Returned("phone", type="string", desc="用户手机号" ),
     *          @Apidoc\Returned("receipt_address", type="string", desc="收货地址" )
     *      ),
     *      @Apidoc\Returned("pay_method", type="array", desc="订单支付银行",
     *          @Apidoc\Returned("source_code", type="string", desc="银行编码" ),
     *          @Apidoc\Returned("source_name", type="string", desc="银行名" )
     *      ),
     *     @Apidoc\Returned("order_type_arr", type="array/json", desc="订单类型信息",
     *          @Apidoc\Returned("order_type", type="int(10)", desc="订单类型id1直邮、2到店安装、3合单、4拼团、5预售、6虚拟、7电子卡券、8CCS、9N延保" ),
     *          @Apidoc\Returned("order_type_name", type="varchar(20)", desc="订单类型中文" ),
     *     ),
     *     @Apidoc\Returned("card_list", type="array/json", desc="卡券列表",
     *          @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *          @Apidoc\Returned("set_type", type="int(10)", desc="应用类型：1平台端2专营店3集团" ),
     *          @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *          @Apidoc\Returned("type", type="int(10)", desc="1微信卡券；2商城卡券,目前只有2商城卡券了" ),
     *          @Apidoc\Returned("card_name", type="varchar(200)", desc="卡劵名称" ),
     *          @Apidoc\Returned("card_id", type="varchar(28)", desc="微信卡劵id" ),
     *          @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券),目前只有前两种" ),
     *          @Apidoc\Returned("wx_card_type", type="varchar(255)", desc="微信优惠券类型" ),
     *          @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *          @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *          @Apidoc\Returned("least_type", type="int(1)", desc="最低消费类型：1金额2指定商品" ),
     *          @Apidoc\Returned("least_cost", type="varchar(20)", desc="表示起用金额，如果没有设置,前端要做对应的数值隐藏" ),
     *          @Apidoc\Returned("validity_date_start", type="datetime", desc="固定日期区间，专用有效期开始" ),
     *          @Apidoc\Returned("validity_date_end", type="datetime", desc="固定日期区间，专用有效期结束" ),
     *          @Apidoc\Returned("date_type", type="int(1)", desc="有效期类型：1表示固定日期区间2表示固定时长" ),
     *          @Apidoc\Returned("fixed_term", type="int(5)", desc="固定时长专用，领取后多少天内有效，单位为天" ),
     *          @Apidoc\Returned("fixed_begin_term", type="int(5)", desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天" ),
     *          @Apidoc\Returned("receive_range", type="tinyint(1)", desc="领取范围：1所有人2车主3指定用户4指定用户除外" ),
     *          @Apidoc\Returned("count", type="int(10)", desc="数量" ),
     *          @Apidoc\Returned("available_count", type="int(10)", desc="可用数量" ),
     *          @Apidoc\Returned("default_detail", type="text", desc="优惠说明，字数上限300个汉字" ),
     *          @Apidoc\Returned("use_des", type="text", desc="使用须知，字数上限为1024个汉字" ),
     *          @Apidoc\Returned("get_limit", type="int(5)", desc="每人可领券的数量限制，不填写默认为1" ),
     *          @Apidoc\Returned("apply_des", type="varchar(30)", desc="适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *          @Apidoc\Returned("not_apply_des", type="varchar(30)", desc="不适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *          @Apidoc\Returned("apply_dlr_code", type="text", desc="适用门店" ),
     *          @Apidoc\Returned("commodity_use_type", type="int(1)", desc="优惠券可用的商品范围：1全部商品2指定商品" ),
     *          @Apidoc\Returned("act_name", type="varchar(255)", desc="活动名称" ),
     *          @Apidoc\Returned("page_id", type="varchar(100)", desc="page_id" ),
     *          @Apidoc\Returned("is_enable", type="int(1)", desc="是否可用" ),
     *          @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *          @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *          @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *          @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *          @Apidoc\Returned("color", type="varchar(10)", desc="颜色编码" ),
     *          @Apidoc\Returned("icon_image", type="varchar(300)", desc="封面图" ),
     *          @Apidoc\Returned("abstract", type="varchar(300)", desc="封面说明" ),
     *          @Apidoc\Returned("brand_name", type="varchar(30)", desc="商户名称" ),
     *          @Apidoc\Returned("code_type", type="varchar(100)", desc="code_type类型" ),
     *          @Apidoc\Returned("center_title", type="varchar(255)", desc="卡劵顶部按钮名称" ),
     *          @Apidoc\Returned("center_url", type="varchar(300)", desc="卡劵顶部按钮跳转URL" ),
     *          @Apidoc\Returned("custom_url_name", type="varchar(10)", desc="自定义跳转外链的入口名字" ),
     *          @Apidoc\Returned("custom_url", type="varchar(150)", desc="自定义跳转的URL" ),
     *          @Apidoc\Returned("custom_url_sub_title", type="varchar(20)", desc="自定义跳转外链右侧提示语" ),
     *          @Apidoc\Returned("promotion_url_name", type="varchar(10)", desc="营销场景自定义入口名字" ),
     *          @Apidoc\Returned("promotion_url", type="varchar(150)", desc="营销场景跳转的URL" ),
     *          @Apidoc\Returned("promotion_url_sub_title", type="varchar(20)", desc="营销场景入口右侧提示语" ),
     *          @Apidoc\Returned("off_line", type="int(1)", desc="是否线下使用，0否1是" ),
     *          @Apidoc\Returned("act_status", type="int(1)", desc="varchar(32)", desc="渠道名" ),
     *          @Apidoc\Returned("up_down_channel_dlr", type="text", desc="上架专营店编码" ),
     *          @Apidoc\Returned("is_succeed", type="int(1)", desc="是否创券成功0否1是" ),
     *          @Apidoc\Returned("can_with", type="int(1)", desc="是否可与其他券共用0否1是" ),
     *          @Apidoc\Returned("can_with_ids", type="varchar(256)", desc="可共同使用的优惠券id" ),
     *          @Apidoc\Returned("can_get_in_detail", type="int(1)", desc="是否可领取：0否1是" ),
     *          @Apidoc\Returned("shelves_type", type="int(2)", desc="上架来源：1.平台自营 2.专营店 3.官微 4.活动 5.车生活" ),
     *          @Apidoc\Returned("commodity_set_id", type="int(10)", desc="商品设置id" ),
     *          @Apidoc\Returned("commodity_id", type="int(10)", desc="商品id" ),
     *          @Apidoc\Returned("price", type="decimal(10,2)", desc="原价" ),
     *          @Apidoc\Returned("cid", type="int(10)", desc="订单商品表id" ),
     *          @Apidoc\Returned("card_code", type="varchar(50)", desc="卡券编码" ),
     *          @Apidoc\Returned("value", type="int(10)", desc="面额" ),
     *          @Apidoc\Returned("card_date", type="decimal(10,2)", desc="卡券有效期" ),
     *          @Apidoc\Returned("word", type="text", desc="卡券使用范围描述" ),
     *          @Apidoc\Returned("car_s_name", type="varchar(50)", desc="适用车系" ),
     *          @Apidoc\Returned("car_config_cn", type="varchar(50)", desc="适用车型" ),
     *          @Apidoc\Returned("use_vin", type="varchar(50)", desc="适用车架号" ),
     *          @Apidoc\Returned("apply_dlr_name", type="text", desc="适用专营店" ),
     *          @Apidoc\Returned("max_discount", type="varchar", desc="最高折扣价" ),
     *          @Apidoc\Returned("goods_res", type="array/json", desc="订单商品信息",
     *               @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *               @Apidoc\Returned("cid", type="int(10)", desc="商品设置id" ),
     *               @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券)" ),
     *               @Apidoc\Returned("commodity_id", type="int(10)", desc="商品id" ),
     *               @Apidoc\Returned("commodity_set_id", type="int(10)", desc="商品设置id" ),
     *               @Apidoc\Returned("price", type="decimal(10,2)", desc="原价" ),
     *               @Apidoc\Returned("count", type="int(10)", desc="数量" ),
     *               @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *               @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *               @Apidoc\Returned("least_cost", type="decimal(10,2)", desc="表示起用金额，如果无起用门槛则填0" ),
     *               @Apidoc\Returned("yh_money", type="decimal(10,2)", desc="优惠金额" ),
     *               @Apidoc\Returned("card_all_price", type="decimal(10,2)", desc="总价" )
     *          )
     *     ),
     *     @Apidoc\Returned("best_card", type="array/json", desc="最优卡券列表--格式同card_list只是取了最优的卡券合集",
     *          @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *          @Apidoc\Returned("set_type", type="int(10)", desc="应用类型：1平台端2专营店3集团" ),
     *          @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *          @Apidoc\Returned("type", type="int(10)", desc="1微信卡券；2商城卡券,目前只有2商城卡券了" ),
     *          @Apidoc\Returned("card_name", type="varchar(200)", desc="卡劵名称" ),
     *          @Apidoc\Returned("card_id", type="varchar(28)", desc="微信卡劵id" ),
     *          @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券),目前只有前两种" ),
     *          @Apidoc\Returned("wx_card_type", type="varchar(255)", desc="微信优惠券类型" ),
     *          @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *          @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *          @Apidoc\Returned("least_type", type="int(1)", desc="最低消费类型：1金额2指定商品" ),
     *          @Apidoc\Returned("least_cost", type="varchar(20)", desc="表示起用金额，如果没有设置,前端要做对应的数值隐藏" ),
     *          @Apidoc\Returned("validity_date_start", type="datetime", desc="固定日期区间，专用有效期开始" ),
     *          @Apidoc\Returned("validity_date_end", type="datetime", desc="固定日期区间，专用有效期结束" ),
     *          @Apidoc\Returned("date_type", type="int(1)", desc="有效期类型：1表示固定日期区间2表示固定时长" ),
     *          @Apidoc\Returned("fixed_term", type="int(5)", desc="固定时长专用，领取后多少天内有效，单位为天" ),
     *          @Apidoc\Returned("fixed_begin_term", type="int(5)", desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天" ),
     *          @Apidoc\Returned("receive_range", type="tinyint(1)", desc="领取范围：1所有人2车主3指定用户4指定用户除外" ),
     *          @Apidoc\Returned("count", type="int(10)", desc="数量" ),
     *          @Apidoc\Returned("available_count", type="int(10)", desc="可用数量" ),
     *          @Apidoc\Returned("default_detail", type="text", desc="优惠说明，字数上限300个汉字" ),
     *          @Apidoc\Returned("use_des", type="text", desc="使用须知，字数上限为1024个汉字" ),
     *          @Apidoc\Returned("get_limit", type="int(5)", desc="每人可领券的数量限制，不填写默认为1" ),
     *          @Apidoc\Returned("apply_des", type="varchar(30)", desc="适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *          @Apidoc\Returned("not_apply_des", type="varchar(30)", desc="不适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *          @Apidoc\Returned("apply_dlr_code", type="text", desc="适用门店" ),
     *          @Apidoc\Returned("commodity_use_type", type="int(1)", desc="优惠券可用的商品范围：1全部商品2指定商品" ),
     *          @Apidoc\Returned("act_name", type="varchar(255)", desc="活动名称" ),
     *          @Apidoc\Returned("page_id", type="varchar(100)", desc="page_id" ),
     *          @Apidoc\Returned("is_enable", type="int(1)", desc="是否可用" ),
     *          @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *          @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *          @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *          @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *          @Apidoc\Returned("color", type="varchar(10)", desc="颜色编码" ),
     *          @Apidoc\Returned("icon_image", type="varchar(300)", desc="封面图" ),
     *          @Apidoc\Returned("abstract", type="varchar(300)", desc="封面说明" ),
     *          @Apidoc\Returned("brand_name", type="varchar(30)", desc="商户名称" ),
     *          @Apidoc\Returned("code_type", type="varchar(100)", desc="code_type类型" ),
     *          @Apidoc\Returned("center_title", type="varchar(255)", desc="卡劵顶部按钮名称" ),
     *          @Apidoc\Returned("center_url", type="varchar(300)", desc="卡劵顶部按钮跳转URL" ),
     *          @Apidoc\Returned("custom_url_name", type="varchar(10)", desc="自定义跳转外链的入口名字" ),
     *          @Apidoc\Returned("custom_url", type="varchar(150)", desc="自定义跳转的URL" ),
     *          @Apidoc\Returned("custom_url_sub_title", type="varchar(20)", desc="自定义跳转外链右侧提示语" ),
     *          @Apidoc\Returned("promotion_url_name", type="varchar(10)", desc="营销场景自定义入口名字" ),
     *          @Apidoc\Returned("promotion_url", type="varchar(150)", desc="营销场景跳转的URL" ),
     *          @Apidoc\Returned("promotion_url_sub_title", type="varchar(20)", desc="营销场景入口右侧提示语" ),
     *          @Apidoc\Returned("off_line", type="int(1)", desc="是否线下使用，0否1是" ),
     *          @Apidoc\Returned("act_status", type="int(1)", desc="varchar(32)", desc="渠道名" ),
     *          @Apidoc\Returned("up_down_channel_dlr", type="text", desc="上架专营店编码" ),
     *          @Apidoc\Returned("is_succeed", type="int(1)", desc="是否创券成功0否1是" ),
     *          @Apidoc\Returned("can_with", type="int(1)", desc="是否可与其他券共用0否1是" ),
     *          @Apidoc\Returned("can_with_ids", type="varchar(256)", desc="可共同使用的优惠券id" ),
     *          @Apidoc\Returned("can_get_in_detail", type="int(1)", desc="是否可领取：0否1是" ),
     *          @Apidoc\Returned("shelves_type", type="int(2)", desc="上架来源：1.平台自营 2.专营店 3.官微 4.活动 5.车生活" ),
     *          @Apidoc\Returned("commodity_set_id", type="int(10)", desc="商品设置id" ),
     *          @Apidoc\Returned("commodity_id", type="int(10)", desc="商品id" ),
     *          @Apidoc\Returned("price", type="decimal(10,2)", desc="原价" ),
     *          @Apidoc\Returned("cid", type="int(10)", desc="订单商品表id" ),
     *          @Apidoc\Returned("card_code", type="varchar(50)", desc="卡券编码" ),
     *          @Apidoc\Returned("value", type="int(10)", desc="面额" ),
     *          @Apidoc\Returned("card_date", type="decimal(10,2)", desc="卡券有效期" ),
     *          @Apidoc\Returned("word", type="text", desc="卡券使用范围描述" ),
     *          @Apidoc\Returned("car_s_name", type="varchar(50)", desc="适用车系" ),
     *          @Apidoc\Returned("car_config_cn", type="varchar(50)", desc="适用车型" ),
     *          @Apidoc\Returned("use_vin", type="varchar(50)", desc="适用车架号" ),
     *          @Apidoc\Returned("apply_dlr_name", type="text", desc="适用专营店" ),
     *          @Apidoc\Returned("max_discount", type="varchar", desc="最高折扣价" ),
     *          @Apidoc\Returned("goods_res", type="array/json", desc="订单商品信息",
     *               @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *               @Apidoc\Returned("cid", type="int(10)", desc="商品设置id" ),
     *               @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券)" ),
     *               @Apidoc\Returned("commodity_id", type="int(10)", desc="商品id" ),
     *               @Apidoc\Returned("commodity_set_id", type="int(10)", desc="商品设置id" ),
     *               @Apidoc\Returned("price", type="decimal(10,2)", desc="原价" ),
     *               @Apidoc\Returned("count", type="int(10)", desc="数量" ),
     *               @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *               @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *               @Apidoc\Returned("least_cost", type="decimal(10,2)", desc="表示起用金额，如果无起用门槛则填0" ),
     *               @Apidoc\Returned("yh_money", type="decimal(10,2)", desc="优惠金额" ),
     *               @Apidoc\Returned("card_all_price", type="decimal(10,2)", desc="总价" )
     *          )
     *     ),
     *     @Apidoc\Returned("no_card_list", type="array/json", desc="不可用卡券列表",
     *          @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *          @Apidoc\Returned("card_name", type="varchar(200)", desc="卡劵名称" ),
     *          @Apidoc\Returned("word", type="varchar(200)", desc="卡券说明" ),
     *          @Apidoc\Returned("card_date", type="varchar(200)", desc="卡券日期说明" ),
     *          @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券),目前只有前两种" ),
     *          @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *          @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *          @Apidoc\Returned("least_cost", type="varchar(20)", desc="表示起用金额，如果没有设置,前端要做对应的数值隐藏" ),
     *          @Apidoc\Returned("validity_date_start", type="datetime", desc="固定日期区间，专用有效期开始" ),
     *          @Apidoc\Returned("validity_date_end", type="datetime", desc="固定日期区间，专用有效期结束" ),
     *          @Apidoc\Returned("date_type", type="int(1)", desc="有效期类型：1表示固定日期区间2表示固定时长" ),
     *          @Apidoc\Returned("fixed_term", type="int(5)", desc="固定时长专用，领取后多少天内有效，单位为天" ),
     *          @Apidoc\Returned("fixed_begin_term", type="int(5)", desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天" ),
     *          @Apidoc\Returned("default_detail", type="text", desc="优惠说明，字数上限300个汉字" ),
     *          @Apidoc\Returned("use_des", type="text", desc="使用须知，字数上限为1024个汉字" ),
     *          @Apidoc\Returned("not_use_word", type="text", desc="不可用提示文案" ),
     *     ),
     *     @Apidoc\Returned("is_default_vin", type="int(10)", desc="是否是默认车，1是 0不是" ),
     *     @Apidoc\Returned("is_mt_optimal_choice", type="int(10)", desc="是否保养套餐最优选择，1是 0不是" ),
     *     @Apidoc\Returned("car_info", type="array/json", desc="车主信息",
     *          @Apidoc\Returned("ic_card_no", type="varchar(200)", desc="会员卡号" ),
     *          @Apidoc\Returned("vin", type="varchar(100)", desc="vin" ),
     *          @Apidoc\Returned("car_no", type="varchar(100)", desc="车牌号" ),
     *          @Apidoc\Returned("name", type="varchar(100)", desc="姓名" ),
     *          @Apidoc\Returned("mobile", type="varchar(100)", desc="手机" ),
     *          @Apidoc\Returned("car_series_code", type="varchar(100)", desc="车系" ),
     *          @Apidoc\Returned("car_series_name", type="varchar(100)", desc="车系中文名" ),
     *          @Apidoc\Returned("card_degree_code", type="varchar(100)", desc="卡级别" ),
     *          @Apidoc\Returned("card_degree_name", type="varchar(100)", desc="卡级别中文" ),
     *          @Apidoc\Returned("relation_dlr_code", type="varchar(100)", desc="交车专营店" ),
     *          @Apidoc\Returned("car_type_code", type="varchar(100)", desc="车系类别编码" ),
     *          @Apidoc\Returned("member_type", type="varchar(100)", desc="1保客2潜客3潜客车主0非会员" ),
     *          @Apidoc\Returned("pv_point", type="varchar(100)", desc="总积分" ),
     *          @Apidoc\Returned("el_point", type="varchar(100)", desc="电车积分" ),
     *          @Apidoc\Returned("oil_point_list", type="array/json", desc="油车积分列表",
     *               @Apidoc\Returned("cardNo", type="varchar(100)", desc="会员卡号" ),
     *               @Apidoc\Returned("vin", type="varchar(100)", desc="vin" ),
     *               @Apidoc\Returned("pvCardUsePoints", type="varchar(100)", desc="可用厂家积分" ),
     *               @Apidoc\Returned("cardBalance", type="varchar(100)", desc="会员卡余额" )
     *          )
     *     ),
     *     @Apidoc\Returned("pre_info", type="array/json", desc="预售信息列表",
     *          @Apidoc\Returned("purchase_number", type="int(10)", desc="限购数量" ),
     *          @Apidoc\Returned("front_money", type="decimal(10,2)", desc="定金" ),
     *          @Apidoc\Returned("dedu_money", type="decimal(10,2)", desc="抵扣金额" ),
     *          @Apidoc\Returned("id", type="int", desc="预售id" ),
     *          @Apidoc\Returned("can_use_card", type="int", desc="是否支持用券，1支持，0否" ),
     *          @Apidoc\Returned("dec", type="string", desc="活动描述" ),
     *          @Apidoc\Returned("front_s_time", type="datetime", desc="定金支付起始时间" ),
     *          @Apidoc\Returned("front_e_time", type="datetime", desc="定金支付结束时间" ),
     *          @Apidoc\Returned("balance_s_time", type="datetime", desc="尾款支付起始时间" ),
     *          @Apidoc\Returned("balance_e_time", type="datetime", desc="尾款支付结束时间" ),
     *          @Apidoc\Returned("pre_status", type="int", desc="0不在任何时间内，1支付定金时间，2支付尾款时间" ),
     *          @Apidoc\Returned("wk_times", type="string", desc="尾款支付时间段" ),
     *          @Apidoc\Returned("yh_money", type="decimal(10,2)", desc="优惠金额" ),
     *          @Apidoc\Returned("can_buy", type="int(10)", desc="可购买数量" ),
     *          @Apidoc\Returned("old_price", type="decimal(10,2)", desc="原价" ),
     *          @Apidoc\Returned("wk", type="decimal(10,2)", desc="尾款" )
     *      ),
     *      @Apidoc\Returned("giveaway_remarks", type="string", desc="赠品使用说明(0214版本)" ),
     *      @Apidoc\Returned("is_show_consume", type="int", desc="是否展示会员卡区域文案 1展示 0不展示" ),
     *      @Apidoc\Returned("empower_show_msg", type="string", desc="下单提示" ),
     *     @Apidoc\Returned("crowdfund_info", type="array/json", desc="众筹信息",
     *          @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *          @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *          @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *          @Apidoc\Returned("target", type="int(11)", desc="活动目标 1众筹金额 2众筹数量" ),
     *          @Apidoc\Returned("target_val", type="varchar(60)", desc="活动目标值" ),
     *          @Apidoc\Returned("alr_crowd", type="varchar(60)", desc="已筹显示 1已筹金额 2已筹数量 逗号分割，比如1,2就是两个都要显示" ),
     *          @Apidoc\Returned("purchase_num", type="int(11)", desc="限购数量" ),
     *          @Apidoc\Returned("theme_name", type="varchar(200)", desc="主题活动名称" ),
     *          @Apidoc\Returned("act_status", type="int(11)", desc="活动状态:1未开始;2进行中;3结束" ),
     *          @Apidoc\Returned("plan_status", type="int(11)", desc="进度状态:0未开始;1进行中-未达标;2进行中-已达标;3项目成功-自动;4项目成功-人为;5项目失败-自动;6项目失败-人为;7项目失败-待处理" ),
     *          @Apidoc\Returned("money", type="int(11)", desc="已筹金额" ),
     *          @Apidoc\Returned("sum_num", type="int(11)", desc="已筹数量" ),
     *          @Apidoc\Returned("crowdfund_agn", type="text", desc="众筹协议" ),
     *          @Apidoc\Returned("refund_text", type="text", desc="众筹退款说明" ),
     *      )
     * )
     * @Apidoc\Returned("order_goods", type="array", desc="订单商品信息,会根据商品的 到店/快递 进行分组,到店组的需要前端有选择专营店的操作,快递组需要有快递地址的操作",
     *     @Apidoc\Returned("order_mail_type", type="int(1)", desc="订单商品快递或者到店：1到店；2快递" ),
     *     @Apidoc\Returned("order_mail_type_name", type="string", desc="快递/到店" ),
     *     @Apidoc\Returned("tip_dd_dlr_code", type="string", desc="提示专营店编码" ),
     *     @Apidoc\Returned("dlr_code", type="string", desc="专营店编码" ),
     *     @Apidoc\Returned("dlr_name", type="string", desc="专营店名称" ),
     *     @Apidoc\Returned("list", type="array/json", desc="订单商品列表",
     *          @Apidoc\Returned("stock", type="int(11)", desc="库存" ),
     *          @Apidoc\Returned("sp_value_list", type="array/json", desc="规格列表",
     *               @Apidoc\Returned("id", type="int(11)", desc="规格值ID" ),
     *               @Apidoc\Returned("sp_value_name", type="varchar(100)", desc="规格值名称" ),
     *               @Apidoc\Returned("sp_id", type="int(11)", desc="商品规格id" ),
     *               @Apidoc\Returned("sp_name", type="varchar(100)", desc="商品规格名称" ),
     *               @Apidoc\Returned("sort", type="int(11)", desc="排序" )
     *          ),
     *          @Apidoc\Returned("image", type="varchar(300)", desc="规格图片" ),
     *          @Apidoc\Returned("is_sku_out", type="int(1)", desc="规格是否可用，1是0否" ),
     *          @Apidoc\Returned("sku_code", type="varchar(250)", desc="规格编码" ),
     *          @Apidoc\Returned("is_sold_out", type="int(1)", desc="商品是否可用，1是0否" ),
     *          @Apidoc\Returned("commodity_name", type="varchar(250)", desc="商品名称" ),
     *          @Apidoc\Returned("cover_image", type="varchar(300)", desc="商品封面图" ),
     *          @Apidoc\Returned("commodity_class", type="int(1)", desc="商品种类：1-实物商品；2-虚拟商品；3-电子卡劵；4-CSS流量套餐；5-平台卡券；6-取送车券；" ),
     *          @Apidoc\Returned("commodity_card_ids", type="varchar(500)", desc="商品电子卡券id，英文逗号隔开" ),
     *          @Apidoc\Returned("card_id", type="varchar(50)", desc="卡券ID" ),
     *          @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *          @Apidoc\Returned("is_mail", type="int(1)", desc="是否直邮，1是0否" ),
     *          @Apidoc\Returned("is_store", type="int(1)", desc="是否到店，1是0否" ),
     *          @Apidoc\Returned("favourable_introduction", type="varchar(200)", desc="优惠简称" ),
     *          @Apidoc\Returned("favourable_detail", type="text", desc="优惠详情" ),
     *          @Apidoc\Returned("pay_style", type="int(1)", desc="支付方式：1现金+积分 2现金 3积分" ),
     *          @Apidoc\Returned("price", type="decimal(11,2)", desc="原价" ),
     *          @Apidoc\Returned("limit_dis", type="object", desc="限时优惠json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为限时折扣id（数组）" )
     *          ),
     *          @Apidoc\Returned("n_dis", type="object", desc="N件N折json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为N件N折id（数组）" )
     *          ),
     *          @Apidoc\Returned("group_dis", type="object", desc="团购json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为团购id（数组）" )
     *          ),
     *          @Apidoc\Returned("pre_dis", type="object", desc="预售json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为预售id（数组）" )
     *          ),
     *          @Apidoc\Returned("cheap_dis", type="object", desc="套餐json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为套餐id（数组）" )
     *          ),
     *          @Apidoc\Returned("full_dis", type="object", desc="满减json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为满减id（数组）" )
     *          ),
     *          @Apidoc\Returned("serv_pack_dis", type="decimal(10,2)", desc="臻享服务包活动优惠金额" ),
     *          @Apidoc\Returned("gift_act_dis", type="decimal(10,2)", desc="买赠活动优惠金额(0214版本)" ),
     *          @Apidoc\Returned("commodity_dlr_type_id", type="int(11)", desc="专营店分类ID" ),
     *          @Apidoc\Returned("is_card_multic", type="int(1)", desc="是否支持多张优惠券（1是0否）" ),
     *          @Apidoc\Returned("mail_price", type="decimal(10,2)", desc="快递费" ),
     *          @Apidoc\Returned("divided_into", type="decimal(10,2)", desc="分成比例" ),
     *          @Apidoc\Returned("install_fee", type="decimal(10,2)", desc="安装费" ),
     *          @Apidoc\Returned("commodity_set_id", type="int(11)", desc="商品设置id" ),
     *          @Apidoc\Returned("max_point", type="decimal(10,2)", desc="最多使用积分" ),
     *          @Apidoc\Returned("factory_points", type="int(1)", desc="是否支持厂家积分(1是，0否）" ),
     *          @Apidoc\Returned("dlr_points", type="int(1)", desc="是否支持专营店积分（1是0否）" ),
     *          @Apidoc\Returned("pay_dlr_code", type="varchar(100)", desc="特殊支付账号" ),
     *          @Apidoc\Returned("spr_id", type="int(11)", desc="分账规则id" ),
     *          @Apidoc\Returned("point_discount", type="decimal(10,2)", desc="积分折扣" ),
     *          @Apidoc\Returned("sku_image", type="varchar(300)", desc="规格显示图片" ),
     *          @Apidoc\Returned("divided_price", type="decimal(10,2)", desc="分成金额" ),
     *          @Apidoc\Returned("limit_discount_id", type="int(11)", desc="限时折扣id" ),
     *          @Apidoc\Returned("work_time_actual_money", type="int(11)", desc="工时实际价格" ),
     *          @Apidoc\Returned("work_time_money", type="int(11)", desc="工时价格" ),
     *          @Apidoc\Returned("actual_price", type="decimal(10,2)", desc="实际价格" ),
     *          @Apidoc\Returned("sku_info", type="varchar(200)", desc="sku信息，颜色等，用,分隔" ),
     *          @Apidoc\Returned("commodity_pic", type="varchar(300)", desc="商品图片" ),
     *          @Apidoc\Returned("sku_value", type="varchar(200)", desc="规格名称+规格值名称" ),
     *          @Apidoc\Returned("order_mail_type", type="int(1)", desc="订单商品快递或者到店：1到店；2快递" ),
     *          @Apidoc\Returned("count", type="int(11)", desc="数量" ),
     *          @Apidoc\Returned("can_open_goods", type="int(1)", desc="是否可以进入商品详情页1是0否" ),
     *          @Apidoc\Returned("cannt_c_dlr", type="tinyint(1)", desc="是否禁用选择专营店，1是0否" ),
     *          @Apidoc\Returned("service_channel_labels", type="array", desc="服务标签" ),
     *          @Apidoc\Returned("q_qz", type="varchar(20)", desc="前缀" ),
     *          @Apidoc\Returned("q_hz", type="varchar(20)", desc="后缀" ),
     *          @Apidoc\Returned("all_dis", type="decimal(11,2)", desc="活动总优惠金额" ),
     *          @Apidoc\Returned ("sku_c_json",type="array/json",desc="子商品信息",
     *              @Apidoc\Returned ("title",type="decimal(11,2)",desc="子商品商品名"),
     *              @Apidoc\Returned ("count",type="int(10)",desc="子商品数量"),
     *              @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="工时数量"),
     *              @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="工时编码"),
     *              @Apidoc\Returned ("work_time_price",type="varchar(500)",desc="工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *              @Apidoc\Returned ("sku_cn",type="array/json",desc="规格")
     *           ),
     *          @Apidoc\Returned ("work_time_json",type="array/json",desc="工时数据，有数据或者为空或者为NULL",
     *              @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="工时数量"),
     *              @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="工时编码"),
     *              @Apidoc\Returned ("work_time_price",type="varchar(500)",desc="工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *           ),
     *      @Apidoc\Returned("is_gift", type="tinyint(1)", desc="是否赠品 1是 0否(0214版本)" ),
     *      @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *      @Apidoc\Returned ("is_cap_commodity",type="int",desc="是否是cap活动商品 0-否 1-是"),
     *     ),
     * )
     * @Apidoc\Returned("unpaid_order_warning", type="array", desc="待支付订单占用卡券警告信息",
     *     @Apidoc\Returned("type", type="int", desc="警告类型：1-赠品券，1-普通券" ),
     *     @Apidoc\Returned("message", type="string", desc="警告信息" ),
     *     @Apidoc\Returned("orders", type="array", desc="占用卡券的订单列表" ),
     *     @Apidoc\Returned("jump_url", type="string", desc="跳转URL" )
     * )
     */

    public function orderConfirm(OrderValidate $validate)
    {

    }

    public function orderCard(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("mail_price")->check($requestData);
    }

    /**
     * @Apidoc\Title("订单运费")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/mail")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 订单 运费")
     *
     * @Apidoc\Param("order_id", type="int",require=true, desc="订单ID" )
     * @Apidoc\Param("address", type="string",require=false, desc="省" )
     *
     * @Apidoc\Returned("message", type="string", desc="消息描述" )
     * @Apidoc\Returned("mail_price", type="decimal", desc="运费" )
     * @Apidoc\Returned("mail_goods", type="array", desc="运费商品信息",
     *     @Apidoc\Returned("price", type="string", desc="运费" ),
     *     @Apidoc\Returned("list", type="array", desc="列表信息",
     *          @Apidoc\Returned("commodity_name", type="string", desc="商品名" ),
     *          @Apidoc\Returned("commodity_pic", type="string", desc="商品图" )
     *     ),
     * ),
     *
     */
    public function orderMail(OrderValidate $validate)
    {
        //a.count,d.first_count,d.first_fee,d.continue_count,d.continue_fee,a.id,d.id did,d.template_type,d.gradient_start,d.gradient_end,a.price,a.actual_price,a.commodity_name,a.commodity_pic
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("mail_price")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order_id    = $requestData['order_id'];
        $address     = $requestData['address'];
        $order_model = new BuOrder();
        $order       = $order_model->getOneByPk($order_id);
        if (!$order) {
            return $this->setResponseError("订单不存在", 20001)->send();
        }
        $res = $this->_mail_price($order['order_code'], $address, 'mail_ajax');
        if ($res['error'] == 1) {
            return $this->setResponseError($res['msg'])->send();
            print_json($res['error'], $res['msg']);
        } else {
            return $this->setResponseData($res['data'])->send();

            print_json(0, 'ok', $res['data']);

        }
    }

    /**
     * @Apidoc\Title("去支付")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/go-pay")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("NI+ 订单 支付")
     *
     * @Apidoc\Param("order_code", type="int",require=true, desc="订单code--" )
     * @Apidoc\Param("name", type="string",require=false, desc="姓名" )
     * @Apidoc\Param("phone", type="string",require=false, desc="电话" )
     * @Apidoc\Param("address", type="string",require=false, desc="地址" )
     * @Apidoc\Param("address_area_ids", type="string",require=false, desc="province_id,city_id,county_id用逗号链接" )
     * @Apidoc\Param("cards", type="string",require=false, desc="卡券ID，多个,隔开" )
     * @Apidoc\Param("card_money", type="decimal",require=false, desc="卡券总金额数据会进行核对匹配" )
     * @Apidoc\Param("money", type="decimal",require=false, desc="实际支付金额可以是0" )
     * @Apidoc\Param("logistics_mode", type="int",require=false, desc="物流方式：1自提，2快递" )
     * @Apidoc\Param("point", type="int",require=false, desc="厂家积分" )
     * @Apidoc\Param("dlr_point", type="int",require=false, desc="专营店积分" )
     * @Apidoc\Param("choose_dlr_code", type="string",require=true, desc="选择专营店到店类型必填" )
     * @Apidoc\Param("pay_type", type="string",require=false, desc="支付方式" )
     * @Apidoc\Param("point_js", type="string",require=true, desc="电车+油车积分json【只有pz1a才有的字段】：{el_point:10,//电车积分oil_point_list:{[ic_card_no:'',//卡号vin:'',//vin oil_point:'',//使用厂家积分]}}" )
     * @Apidoc\Param("return_url", type="url",require=false, desc="web支付完成回跳地址" )
     * @Apidoc\Param("invoice_json", type="string",require=false, desc=" {'invoiceType':'发票类型:1-普通发票2-专用发票','invoiceHeaderType':'发票抬头类型1-个人2-企业','invoiceHeader':'发票抬头','email':'邮箱','mobile':'手机号码','taxCode':'纳税识别号','registerAddress':'注册地址','registerMobile':'注册电话','bank':'开户银行','bankAccount':'银行账号','invoiceMaterialsImage':'专票申请资料图片-[图片1,图片2]'}" )
      *
     * @Apidoc\Returned ("message",type="string",desc="提示信息")
     * @Apidoc\Returned ("show_popup",type="array/json",desc="弹窗信息（如有此字段优先弹窗展示）",
     *     @Apidoc\Returned ("popup_msg",type="string",desc="弹窗内容"),
     *     @Apidoc\Returned ("popup_btn_word",type="string",desc="弹窗按钮文字描述"),
     * )
     *
     */
    public function goPay(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("go_pay")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_order = new NetOrder();
        $res       = $net_order->go_pay($requestData, $this->user, $this->channel_type);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }


    /**
     * @Apidoc\Title("订单详情")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/detail")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 订单 取送车 0214")
     *
     * @Apidoc\Param("order_id", type="int",require=true, desc="订单ID" )
     *
     * @Apidoc\Returned("order", type="array/json", desc="订单信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="订单id" ),
     *     @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     *     @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *     @Apidoc\Returned("openid", type="varchar(50)", desc="openid" ),
     *     @Apidoc\Returned("vin", type="varchar(20)", desc="vin" ),
     *     @Apidoc\Returned("license_plate", type="varchar(30)", desc="车牌" ),
     *     @Apidoc\Returned("ic_card_no", type="varchar(30)", desc="会员号" ),
     *     @Apidoc\Returned("name", type="varchar(30)", desc="姓名" ),
     *     @Apidoc\Returned("phone", type="varchar(20)", desc="手机号码" ),
     *     @Apidoc\Returned("source", type="varchar(32)", desc="来源" ),
     *     @Apidoc\Returned("total_money", type="decimal(11,2)", desc="总金额" ),
     *     @Apidoc\Returned("money", type="decimal(11,2)", desc="实付金额" ),
     *     @Apidoc\Returned("card_id", type="varchar(200)", desc="卡劵" ),
     *     @Apidoc\Returned("card_code", type="varchar(200)", desc="卡劵code" ),
     *     @Apidoc\Returned("card_money", type="int(11)", desc="卡劵金额" ),
     *     @Apidoc\Returned("integral", type="varchar(20)", desc="积分" ),
     *     @Apidoc\Returned("payment_method", type="tinyint(1)", desc="支付方式（1现金；2积分；3卡劵；4现金+积分；5现金+卡劵；6积分+卡劵；7现金+积分+卡劵）" ),
     *     @Apidoc\Returned("order_status", type="string", desc="订单状态：请查看开发必读-》订单说明-》状态列表" ),
     *     @Apidoc\Returned("validity_date_start", type="datetime", desc="有效期开始" ),
     *     @Apidoc\Returned("validity_date_end", type="datetime", desc="有效期结束" ),
     *     @Apidoc\Returned("verification_user", type="varchar(10)", desc="核销人员" ),
     *     @Apidoc\Returned("settlement_user", type="varchar(10)", desc="结算人员" ),
     *     @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用：1是0否" ),
     *     @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *     @Apidoc\Returned("creator", type="varchar(32)", desc="创建人" ),
     *     @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *     @Apidoc\Returned("modifier", type="varchar(32)", desc="修改人" ),
     *     @Apidoc\Returned("logistics_mode", type="tinyint(4)", desc="物流方式：1自提，2快递" ),
     *     @Apidoc\Returned("common_carrier", type="varchar(30)", desc="承运公司" ),
     *     @Apidoc\Returned("waybill_number", type="varchar(30)", desc="运单编号" ),
     *     @Apidoc\Returned("delivery_time", type="datetime", desc="发货时间" ),
     *     @Apidoc\Returned("commodity_dlr_type_id", type="int(10)", desc="商品专营店分类ID" ),
     *     @Apidoc\Returned("mail_price", type="decimal(11,2)", desc="运费" ),
     *     @Apidoc\Returned("receipt_address", type="varchar(100)", desc="收货地址" ),
     *     @Apidoc\Returned("pay_order_code", type="varchar(32)", desc="支付订单号" ),
     *     @Apidoc\Returned("zt_status", type="tinyint(4)", desc="自提状态（1供应商已发货；2货已到专营店，请等待专营店预约到店安装；3发货中）" ),
     *     @Apidoc\Returned("order_source", type="int(1)", desc="订单来源：请查看开发必读-》订单说明-》来源列表" ),
     *     @Apidoc\Returned("settlement_date", type="datetime", desc="扎帐时间" ),
     *     @Apidoc\Returned("dlr_integral", type="int(11)", desc="专营店积分" ),
     *     @Apidoc\Returned("sub_commission_id", type="int(11)", desc="分佣id" ),
     *     @Apidoc\Returned("full_cut_card_id", type="varchar(200)", desc="满减卡券" ),
     *     @Apidoc\Returned("full_id", type="int(11)", desc="满减ID" ),
     *     @Apidoc\Returned("new_car_bag_ac_id", type="int(11)", desc="礼包活动表主键" ),
     *     @Apidoc\Returned("remark", type="varchar(255)", desc="备注" ),
     *     @Apidoc\Returned("sale_source", type="int(11)", desc="销售来源：2普通商城，5官微商城" ),
     *     @Apidoc\Returned("ca_id", type="varchar(50)", desc="ca" ),
     *     @Apidoc\Returned("user_id", type="int(11)", desc="用户表主键" ),
     *     @Apidoc\Returned("is_all_comment", type="tinyint(1)", desc="是否都评价了：1都评价了0未评价" ),
     *     @Apidoc\Returned("shop_lifting_id", type="tinyint(4)", desc="到店礼id" ),
     *     @Apidoc\Returned("ticket_no", type="varchar(500)", desc="券号" ),
     *     @Apidoc\Returned("jifen_user_id", type="int(11)", desc="对应福利商城用户id" ),
     *     @Apidoc\Returned("shop_integral", type="decimal(10,2)", desc="商城积分" ),
     *     @Apidoc\Returned("address_area_ids", type="varchar(30)", desc="地址id拼接" ),
     *     @Apidoc\Returned("refund_money", type="decimal(11,2)", desc="退款金额" ),
     *     @Apidoc\Returned("pay_time", type="varchar(20)", desc="支付时间" ),
     *     @Apidoc\Returned("gift_score", type="varchar(10)", desc="赠送积分" ),
     *     @Apidoc\Returned("province_id", type="int(11)", desc="省ID，用于运费模板" ),
     *     @Apidoc\Returned("is_by_tc", type="tinyint(2)", desc="0普通订单；1老优惠；2N延保 3心悦套餐；4五年双保；5车联服务包" ),
     *     @Apidoc\Returned("product_card_pwd", type="varchar(50)", desc="卡券密码" ),
     *     @Apidoc\Returned("product_card_money", type="decimal(11,2)", desc="卡券金额" ),
     *     @Apidoc\Returned("is_cc_ok", type="tinyint(2)", desc="0积分卡券核销成功，1积分扣失败，2卡券核销失败" ),
     *     @Apidoc\Returned("act_code", type="varchar(42)", desc="活动编码" ),
     *     @Apidoc\Returned("n_dis_id", type="int(11)", desc="N件N折ID" ),
     *     @Apidoc\Returned("is_send_e3s", type="tinyint(2)", desc="是否下发e3s，0未1已下发" ),
     *     @Apidoc\Returned("recharge_time", type="datetime", desc="充值时间" ),
     *     @Apidoc\Returned("pay_order_code2", type="varchar(50)", desc="定金支付编码" ),
     *     @Apidoc\Returned("front_money", type="decimal(11,2)", desc="定金" ),
     *     @Apidoc\Returned("all_use_point", type="int(11)", desc="订单总使用积分" ),
     *     @Apidoc\Returned("front_money_dedu", type="decimal(11,2)", desc="定金抵扣，定金膨胀" ),
     *     @Apidoc\Returned("pre_sale_id", type="int(11)", desc="预售ID" ),
     *     @Apidoc\Returned("pre_point", type="decimal(11,2)", desc="预售使用积分" ),
     *     @Apidoc\Returned("comment_star", type="tinyint(1)", desc="星星 1-5" ),
     *     @Apidoc\Returned("distance", type="varchar(30)", desc="公里数" ),
     *     @Apidoc\Returned("pay_dlr_code", type="varchar(100)", desc="特殊支付账号" ),
     *     @Apidoc\Returned("cashier_trade_no", type="varchar(50)", desc="财务中心支付单号" ),
     *     @Apidoc\Returned("cashier_trade_no2", type="varchar(50)", desc="预售财务中心支付单号" ),
     *     @Apidoc\Returned("is_plat_settlement", type="tinyint(1)", desc="是否平台结算0未1已结算" ),
     *     @Apidoc\Returned("parent_order_code", type="varchar(50)", desc="父单号" ),
     *     @Apidoc\Returned("channel", type="tinyint(2)", desc="销售渠道，1小程序2官网3app" ),
     *     @Apidoc\Returned("parent_order_type", type="tinyint(3)", desc="3父订，正常显示为1原单2子单" ),
     *     @Apidoc\Returned("b_act_goods_price", type="decimal(11,2)", desc="商品原价总价" ),
     *     @Apidoc\Returned("cashier_settlement_no", type="varchar(50)", desc="中心结算单号" ),
     *     @Apidoc\Returned("is_anonymous", type="tinyint(1)", desc="是否匿名，0不匿名，1匿名" ),
     *     @Apidoc\Returned("plat_order_id", type="varchar(50)", desc="平台返回业务系统ID" ),
     *     @Apidoc\Returned("cashier_settlement_no2", type="varchar(50)", desc="预售中心结算单号" ),
     *     @Apidoc\Returned("is_change_comment", type="tinyint(1)", desc="是否修改过评价，0未修改1已修改" ),
     *     @Apidoc\Returned("is_comment_time", type="datetime", desc="点评时间" ),
     *     @Apidoc\Returned("receive_time", type="datetime", desc="收货时间" ),
     *     @Apidoc\Returned("state", type="varchar(300)", desc="订单状态描述" ),
     *     @Apidoc\Returned("pay_type", type="varchar(300)", desc="支付方式描述" ),
     *     @Apidoc\Returned("pre_use_money", type="decimal(11,2)", desc="定金支付钱" ),
     *     @Apidoc\Returned("front_pay_time", type="datetime", desc="定金支付时间" ),
     *     @Apidoc\Returned("car_no", type="varchar(300)", desc="车牌号" ),
     *     @Apidoc\Returned("qrc_img", type="varchar(300)", desc="二维码" ),
     *     @Apidoc\Returned("can_buy", type="tinyint(1)", desc="是否可购买：1是0否" ),
     *     @Apidoc\Returned("yh_money", type="decimal(11,2)", desc="优惠金额" ),
     *     @Apidoc\Returned("card_yh_money", type="decimal(11,2)", desc="卡券总优惠金额" ),
     *     @Apidoc\Returned("pay_btn_word", type="varchar(100)", desc="支付状态按钮文案" ),
     *     @Apidoc\Returned("dq_time", type="datetime", desc="订单过期时间" ),
     *     @Apidoc\Returned("pay_end_time", type="datetime", desc="最晚支付时间" ),
     *     @Apidoc\Returned("dlr_name", type="varchar(100)", desc="专营店名称" ),
     *     @Apidoc\Returned("dlr_phone", type="varchar(20)", desc="专营店电话" ),
     *     @Apidoc\Returned("dlr_address", type="varchar(255)", desc="专营店地址" ),
     *     @Apidoc\Returned("order_after_id", type="int(11)", desc="售后id" ),
     *     @Apidoc\Returned("order_afs_type", type="int(11)", desc="售后类型,1:仅退款,2:退货,3:换货" ),
     *     @Apidoc\Returned("can_after", type="tinyint(1)", desc="是否可售后：1是0否" ),
     *     @Apidoc\Returned("show_after_type", type="tinyint(1)", desc="（此字段仅服务包、到店代金券、到店电子券、取送车券订单使用）显示售后提示按钮类型：0不显示；1服务包售后按钮；2到店代金券、到店电子券、取送车券售后按钮" ),
     *     @Apidoc\Returned("can_change_comment", type="int", desc="是否可修改售后，1是0否" ),
     *     @Apidoc\Returned("is_jump_draw", type="tinyint(1)", desc="是否跳转至抽奖页：1是0否" ),
     *     @Apidoc\Returned("all_work_time", type="decimal(11,2)", desc="工时总价-" ),
     *     @Apidoc\Returned("car_info", type="varchar", desc="用户下单车型-" ),
     *     @Apidoc\Returned("is_dq", type="int(10)", desc="1过期0没过期-" ),
     *     @Apidoc\Returned("must_address", type="int(10)", desc="1需要显示地址0不需要-" ),
     *     @Apidoc\Returned("have_work_price", type="int(10)", desc="0不需要显示工时1需要显示工时-" ),
     *     @Apidoc\Returned("all_maintain_dis", type="string",desc="保养套餐优惠"),
     *     @Apidoc\Returned("crowdfund_agn", type="string",desc="众筹协议"),
     *     @Apidoc\Returned("order_type_arr", type="array/json", desc="订单类型信息",
     *          @Apidoc\Returned("order_type", type="int(10)", desc="订单类型id1直邮、2到店安装、3合单、4拼团、5预售、6虚拟、7电子卡券、8CCS、9N延保" ),
     *          @Apidoc\Returned("order_type_name", type="varchar(20)", desc="订单类型中文" ),
     *     ),
     *     @Apidoc\Returned("invoice_info", type="array/json", desc="发票信息",
     *     @Apidoc\Returned ("show_invoice",type="tinyint(2)",desc="是否显示发票模块：0-不可以 1-可以"),
     *     @Apidoc\Returned ("button_type",type="varchar(50)",desc="apply-申请 edit-修改 show-查看"),
     *     @Apidoc\Returned ("bottom_article",type="varchar(50)",desc="底部文案"),
     *     @Apidoc\Returned ("show_article",type="varchar(50)",desc="按钮交互文案"),
     * ),
     *     @Apidoc\Returned("sub_order", type="array/json", desc="子单列表，只有套餐才显示",
     *          @Apidoc\Returned("order_code", type="varchar(10)", desc="订单号阿" ),
     *          @Apidoc\Returned("order_id", type="int(10)", desc="订单ID" ),
     *     ),
     *    @Apidoc\Returned("is_old_ariya", type="int(1)", desc="是否ariya好物商城订单" ),
     *    @Apidoc\Returned("see_jd", type="int(1)", desc="是否查看进度 1是 字段不存在：否" ),
     *    @Apidoc\Returned("pro_gress", type="int(1)", desc="投保进度，0=>'',1=>'同意合同',2=>'已支付+待受理',3=>'受理中',4=>'合同已寄出',5=>'合同回寄',6=>'投保成功',99=>'不展示'" ),
     *    @Apidoc\Returned("pro_gress", type="array/json", desc="投保信息",
     *          @Apidoc\Returned("order_code", type="varchar(100)", desc="订单号" ),
     *          @Apidoc\Returned("waybill_number", type="varchar(100)", desc="运单编号" ),
     *          @Apidoc\Returned("common_carrier", type="varchar(100)", desc="承运公司" ),
     *          @Apidoc\Returned("delivery_time", type="datetime", desc="发货时间" ),
     *          @Apidoc\Returned("return_waybill_number", type="varchar(100)", desc="回寄运单编号" ),
     *          @Apidoc\Returned("return_common_carrier", type="varchar(100)", desc="回寄承运公司" ),
     *          @Apidoc\Returned("return_carrier_code", type="varchar(100)", desc="回寄承运公司编码" ),
     *          @Apidoc\Returned("return_delivery_time", type="datetime", desc="回寄发货时间" ),
     *          @Apidoc\Returned("return_receipt_address", type="varchar(100)", desc="回寄收货地址" ),
     *          @Apidoc\Returned("return_name", type="varchar(100)", desc="回寄收货姓名" ),
     *          @Apidoc\Returned("return_phone", type="varchar(100)", desc="回寄手机号码" ),
     *          @Apidoc\Returned("contract_sent_text", type="varchar(100)", desc="合同已寄出文案" ),
     *          @Apidoc\Returned("contract_return_text", type="varchar(100)", desc="合同回寄文案" ),
     *          @Apidoc\Returned("is_agree", type="int(1)", desc="是否勾选协议 1是0否" ),
     *    ),
     * )
     * @Apidoc\Returned("order_goods", type="array", desc="订单商品信息",
     *     @Apidoc\Returned("order_mail_type", type="int", desc="订单商品快递或者到店：1到店；2快递" ),
     *     @Apidoc\Returned("order_mail_type_name", type="varchar(20)", desc="快递/到店" ),
     *     @Apidoc\Returned("list", type="array/json", desc="订单商品列表",
     *          @Apidoc\Returned("id", type="int(11)", desc="订单商品id" ),
     *          @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     *          @Apidoc\Returned("dlr_code", type="varchar(255)", desc="专营店编码" ),
     *          @Apidoc\Returned("commodity_id", type="int(11)", desc="商品编码" ),
     *          @Apidoc\Returned("price", type="decimal(11,2)", desc="原价" ),
     *          @Apidoc\Returned("count", type="int(11)", desc="数量" ),
     *          @Apidoc\Returned("sku_id", type="int(11)", desc="sku_id" ),
     *          @Apidoc\Returned("car_info", type="varchar(100)", desc="车辆信息（车系、排量、年份、轮胎）" ),
     *          @Apidoc\Returned("sku_info", type="varchar(200)", desc="sku信息，颜色等" ),
     *          @Apidoc\Returned("validity_date_start", type="datetime", desc="有效期开始" ),
     *          @Apidoc\Returned("validity_date_end", type="datetime", desc="有效期结束" ),
     *          @Apidoc\Returned("commodity_name", type="varchar(255)", desc="订单商品名" ),
     *          @Apidoc\Returned("commodity_pic", type="varchar(255)", desc="商品图" ),
     *          @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用：1是0否" ),
     *          @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *          @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *          @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *          @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *          @Apidoc\Returned("mail_price", type="decimal(10,2)", desc="快递费" ),
     *          @Apidoc\Returned("divided_price", type="decimal(10,2)", desc="分成价格" ),
     *          @Apidoc\Returned("install_fee", type="decimal(10,2)", desc="安装费" ),
     *          @Apidoc\Returned("sales_source", type="tinyint(2)", desc="销售来源：1平台自营销售，2专营店销售，3官微销" ),
     *          @Apidoc\Returned("commodity_class", type="tinyint(2)", desc="商品种类：1实物商品，2虚拟商品，3电子卡劵" ),
     *          @Apidoc\Returned("ticket_no", type="varchar(32)", desc="券号" ),
     *          @Apidoc\Returned("card_ids", type="varchar(500)", desc="卡券ID，多个用,隔开" ),
     *          @Apidoc\Returned("limit_id", type="int(11)", desc="限时折扣表ID" ),
     *          @Apidoc\Returned("suit_id", type="int(11)", desc="套装ID" ),
     *          @Apidoc\Returned("comment_id", type="int(11)", desc="点评表主键，0未点评" ),
     *          @Apidoc\Returned("point_discount", type="decimal(5,0", desc="积分折扣" ),
     *          @Apidoc\Returned("b_act_price", type="decimal(11,2)", desc="活动前原价" ),
     *          @Apidoc\Returned("full_id", type="int(11)", desc="满减ID" ),
     *          @Apidoc\Returned("n_dis_id", type="int(11)", desc="N件N折ID" ),
     *          @Apidoc\Returned("group_id", type="int(11)", desc="团购ID" ),
     *          @Apidoc\Returned("limit_dis_money", type="decimal(11,2)", desc="限时折扣优惠金额" ),
     *          @Apidoc\Returned("pre_sale_id", type="int(11)", desc="团购ID" ),
     *          @Apidoc\Returned("full_dis_money", type="decimal(11,2)", desc="满减优惠金额" ),
     *          @Apidoc\Returned("n_dis_money", type="decimal(11,2)", desc="n件N折优惠金额" ),
     *          @Apidoc\Returned("pre_sale_dis_money", type="decimal(11,2)", desc="预售优惠金额" ),
     *          @Apidoc\Returned("group_dis_money", type="decimal(11,2)", desc="拼团优惠金额" ),
     *          @Apidoc\Returned("suit_dis_money", type="decimal(11,2)", desc="套餐优惠金额" ),
     *          @Apidoc\Returned("all_dis", type="decimal(11,2))", desc="活动总优惠金额" ),
     *          @Apidoc\Returned("commodity_set_id", type="int(11)", desc="商品编码" ),
     *          @Apidoc\Returned("actual_price", type="int(11)", desc="实际价格" ),
     *          @Apidoc\Returned("card_all_dis", type="decimal(10,2)", desc="卡券总优惠" ),
     *          @Apidoc\Returned("card_codes", type="decimal(11,2)", desc="卡券号码多个用,隔开" ),
     *          @Apidoc\Returned("card_yh", type="varchar(500)", desc="每个卡券优惠了多少" ),
     *          @Apidoc\Returned("pay_dlr_code", type="varchar(500)", desc="特殊支付账号" ),
     *          @Apidoc\Returned("order_mail_type", type="int(2)", desc="订单商品快递或者到店：1到店；2快递" ),
     *          @Apidoc\Returned("parent_order_code", type="tinyint(1)", desc="父单号" ),
     *          @Apidoc\Returned("max_point", type="varchar(50)", desc="最多可用积分" ),
     *          @Apidoc\Returned("work_time_dis", type="int(11)", desc="工时优惠" ),
     *          @Apidoc\Returned("work_time_money", type="int(11)", desc="工时原价单价" ),
     *          @Apidoc\Returned("work_time_actual_money", type="int(11)", desc="工时现价单价" ),
     *          @Apidoc\Returned("actual_point", type="int(11)", desc="实际使用积分" ),
     *          @Apidoc\Returned("actual_use_money", type="int(11)", desc="实际使用金额" ),
     *          @Apidoc\Returned("service_channel_labels", type="array", desc="服务标签" ),
     *          @Apidoc\Returned("spr_id", type="decimal(11,2)", desc="分账规则id" ),
     *          @Apidoc\Returned("is_comment", type="int(10)", desc="是否修改评价：0未修改1已修改" ),
     *          @Apidoc\Returned("can_go_url", type="tinyint(1)", desc="是否能进入商品详情：1是2否" ),
     *          @Apidoc\Returned("url", type="string", desc="商品详情页跳转链接" ),
     *          @Apidoc\Returned("gift_act_dis_money", type="decimal(11,2)", desc="赠品优惠金额(0214版本)" ),
     *          @Apidoc\Returned("is_gift", type="string", desc="是否赠品商品(0214版本)" )
     *     )
     * )
     * @Apidoc\Returned("mail_price", type="decimal(10,2)", desc="运费" )
     * @Apidoc\Returned("pre_sale", type="array/json", desc="预售信息列表",
     *     @Apidoc\Returned("purchase_number", type="int(10)", desc="限购数量" ),
     *     @Apidoc\Returned("front_money", type="decimal(10,2)", desc="定金" ),
     *     @Apidoc\Returned("dedu_money", type="decimal(10,2)", desc="抵扣金额" ),
     *     @Apidoc\Returned("id", type="int(11)", desc="预售id" ),
     *     @Apidoc\Returned("can_use_card", type="int", desc="是否支持用券，1支持，0否" ),
     *     @Apidoc\Returned("dec", type="text", desc="活动描述" ),
     *     @Apidoc\Returned("front_s_time", type="datetime", desc="定金支付起始时间" ),
     *     @Apidoc\Returned("front_e_time", type="datetime", desc="定金支付结束时间" ),
     *     @Apidoc\Returned("balance_s_time", type="datetime", desc="尾款支付起始时间" ),
     *     @Apidoc\Returned("balance_e_time", type="datetime", desc="尾款支付结束时间" ),
     *     @Apidoc\Returned("pre_status", type="int", desc="0不在任何时间内，1支付定金时间，2支付尾款时间" ),
     *     @Apidoc\Returned("wk_times", type="string", desc="尾款支付时间段" ),
     *     @Apidoc\Returned("yh_money", type="decimal(10,2)", desc="优惠金额" ),
     *     @Apidoc\Returned("sf_money", type="decimal(10,2)", desc="实付总价" ),
     *     @Apidoc\Returned("can_buy", type="int(10)", desc="可购买数量" ),
     *     @Apidoc\Returned("yd_times", type="string", desc="预定时间段" )
     * ),
     * @Apidoc\Returned("goods_card_info", type="array/json", desc="卡券商品列表，有卡券时显示",
     *     @Apidoc\Returned("id", type="int(10)", desc="卡券ID通过这个id去搜索商品的" ),
     *     @Apidoc\Returned("card_name", type="sting", desc="卡券名称" ),
     *     @Apidoc\Returned("card_type", type="sting", desc="卡券类型（暂时不需要）" ),
     *     @Apidoc\Returned("fixed_term", type="sting", desc="固定时长专用，领取后多少天内有效" ),
     *     @Apidoc\Returned("fixed_begin_term", type="sting", desc="固定时长专用，表示自领取后多少天开始生效" ),
     *     @Apidoc\Returned("least_cost", type="sting", desc="表示起用金额" ),
     *     @Apidoc\Returned("date_type", type="sting", desc="有效期类型：1表示固定日期区间，2表示固定时长" ),
     *     @Apidoc\Returned("word", type="sting", desc="副标题，在卡券名称下面" ),
     *     @Apidoc\Returned("qu", type="sting", desc="没用到" ),
     *     @Apidoc\Returned("card_discount", type="decimal(10,2)", desc="卡券折扣" ),
     *     @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="劵额度（减免金额）" ),
     *     @Apidoc\Returned("c_date", type="datetime", desc="卡券领取时间" ),
     *     @Apidoc\Returned("validity_date_end", type="datetime", desc="同fixed_end_date" ),
     *     @Apidoc\Returned("validity_date_start", type="datetime", desc="同fixed_begin_date" ),
     *     @Apidoc\Returned("fixed_end_date", type="datetime", desc="有效期结束时间" ),
     *     @Apidoc\Returned("fixed_begin_date", type="datetime", desc="有效期开始时间" ),
     *     @Apidoc\Returned("use_des", type="string", desc="使用规则" ),
     *     @Apidoc\Returned("coupon_code", type="string", desc="核销码"),
     *     @Apidoc\Returned("dlr_name", type="string", desc="适用门店"),
     * ),
     * @Apidoc\Returned ("yy_info",type="array/json",desc="预约信息",
     *      @Apidoc\Returned ("can_yy",type="tinyint(2)",desc="是否能够预约0不可1可以"),
     *      @Apidoc\Returned ("yy_type",type="tinyint(2)",desc="预约类型,1:快速保养,2定期保养,4:预约维修,5预约钣喷(1,2还需要其他判断)"),
     *      @Apidoc\Returned("list", type="array/json", desc="预约二维数组",
     *          @Apidoc\Returned("id", type="int(11)", desc="预约ID" ),
     *          @Apidoc\Returned("subscribe_code", type="varchar(255)", desc="预约单号" ),
     *          @Apidoc\Returned("order_id", type="int(11)", desc="订单表ID" ),
     *          @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     *          @Apidoc\Returned("subscribe_time", type="timestamp", desc="预约时间" ),
     *          @Apidoc\Returned("mileage", type="varchar(50)", desc="行驶里程" ),
     *          @Apidoc\Returned("car_no", type="varchar(50)", desc="车牌" ),
     *          @Apidoc\Returned("car_name", type="varchar(50)", desc="车主姓名" ),
     *          @Apidoc\Returned("car_phone", type="varchar(20)", desc="车主电话" ),
     *          @Apidoc\Returned("car_series_code", type="varchar(50)", desc="车系编码" ),
     *          @Apidoc\Returned("car_series_name", type="varchar(50)", desc="车系名称" ),
     *          @Apidoc\Returned("car_type_code", type="varchar(50)", desc="车型编码" ),
     *          @Apidoc\Returned("adviser_id", type="int(11)", desc="服务顾问ID" ),
     *          @Apidoc\Returned("adviser_name", type="varchar(50)", desc="服务顾问名" ),
     *          @Apidoc\Returned("technician_id", type="varchar(50)", desc="金牌技师ID" ),
     *          @Apidoc\Returned("technician_name", type="varchar(50)", desc="金牌技师名" ),
     *          @Apidoc\Returned("subscribe_type", type="tinyint(2)", desc="同yy_type" ),
     *          @Apidoc\Returned("reason_id", type="tinyint(2)", desc="取消原因id" ),
     *          @Apidoc\Returned("reason_desc", type="varchar(255)", desc="取消原因说明" ),
     *          @Apidoc\Returned("status", type="tinyint(1)", desc="预约单状态,2：已预约 3：已受理 4：取消 5：完成 6：可取消（估价单后SA更新为可取消） 7:进行中" ),
     *          @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用：1是0否" ),
     *     )
     * ),
     *
     * @Apidoc\Returned ("astrict_data",type="array/json",desc="订单详情增加限制操作",
     *     @Apidoc\Returned("after_sale", type="array/json", desc="申请退款限制",
     *          @Apidoc\Returned ("is_astrict",type="tinyint(2)",desc="是否限制：0-不限制；1-限制；,默认为0"),
     *          @Apidoc\Returned ("msg",type="varchar(50)",desc="限制提示"),
     *     )
     * ),
     *  @Apidoc\Returned("remark", type="string", desc="温馨提示 有则展示" ),
     * @Apidoc\Returned("gift_order_tips", type="array", childrenType="string", desc="赠品说明提示" ),
     * @Apidoc\Returned("gift_commodities", type="array", childrenType="object", desc="主品订单的赠品列表",
     *     @Apidoc\Returned("order_mail_type", type="int(1)", desc="订单商品快递或者到店：1到店；2快递" ),
     *     @Apidoc\Returned("order_mail_type_name", type="string", desc="快递/到店" ),
     *     @Apidoc\Returned("list", type="array/json", desc="订单商品列表",
     *          @Apidoc\Returned("id", type="int(11)", desc="订单商品id" ),
     *          @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     *          @Apidoc\Returned("dlr_code", type="varchar(255)", desc="专营店编码" ),
     *          @Apidoc\Returned("commodity_id", type="int(11)", desc="商品编码" ),
     *          @Apidoc\Returned("price", type="decimal(11,2)", desc="原价" ),
     *          @Apidoc\Returned("count", type="int(11)", desc="数量" ),
     *          @Apidoc\Returned("sku_id", type="int(11)", desc="sku_id" ),
     *          @Apidoc\Returned("car_info", type="varchar(100)", desc="车辆信息（车系、排量、年份、轮胎）" ),
     *          @Apidoc\Returned("sku_info", type="varchar(200)", desc="sku信息，颜色等" ),
     *          @Apidoc\Returned("validity_date_start", type="datetime", desc="有效期开始" ),
     *          @Apidoc\Returned("validity_date_end", type="datetime", desc="有效期结束" ),
     *          @Apidoc\Returned("commodity_name", type="varchar(255)", desc="订单商品名" ),
     *          @Apidoc\Returned("commodity_pic", type="varchar(255)", desc="商品图" ),
     *          @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用：1是0否" ),
     *          @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *          @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *          @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *          @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *          @Apidoc\Returned("mail_price", type="decimal(10,2)", desc="快递费" ),
     *          @Apidoc\Returned("divided_price", type="decimal(10,2)", desc="分成价格" ),
     *          @Apidoc\Returned("install_fee", type="decimal(10,2)", desc="安装费" ),
     *          @Apidoc\Returned("sales_source", type="tinyint(2)", desc="销售来源：1平台自营销售，2专营店销售，3官微销" ),
     *          @Apidoc\Returned("commodity_class", type="tinyint(2)", desc="商品种类：1实物商品，2虚拟商品，3电子卡劵" ),
     *          @Apidoc\Returned("ticket_no", type="varchar(32)", desc="券号" ),
     *          @Apidoc\Returned("card_ids", type="varchar(500)", desc="卡券ID，多个用,隔开" ),
     *          @Apidoc\Returned("limit_id", type="int(11)", desc="限时折扣表ID" ),
     *          @Apidoc\Returned("suit_id", type="int(11)", desc="套装ID" ),
     *          @Apidoc\Returned("comment_id", type="int(11)", desc="点评表主键，0未点评" ),
     *          @Apidoc\Returned("point_discount", type="decimal(5,0", desc="积分折扣" ),
     *          @Apidoc\Returned("b_act_price", type="decimal(11,2)", desc="活动前原价" ),
     *          @Apidoc\Returned("full_id", type="int(11)", desc="满减ID" ),
     *          @Apidoc\Returned("n_dis_id", type="int(11)", desc="N件N折ID" ),
     *          @Apidoc\Returned("group_id", type="int(11)", desc="团购ID" ),
     *          @Apidoc\Returned("limit_dis_money", type="decimal(11,2)", desc="限时折扣优惠金额" ),
     *          @Apidoc\Returned("pre_sale_id", type="int(11)", desc="团购ID" ),
     *          @Apidoc\Returned("full_dis_money", type="decimal(11,2)", desc="满减优惠金额" ),
     *          @Apidoc\Returned("n_dis_money", type="decimal(11,2)", desc="n件N折优惠金额" ),
     *          @Apidoc\Returned("pre_sale_dis_money", type="decimal(11,2)", desc="预售优惠金额" ),
     *          @Apidoc\Returned("group_dis_money", type="decimal(11,2)", desc="拼团优惠金额" ),
     *          @Apidoc\Returned("suit_dis_money", type="decimal(11,2)", desc="套餐优惠金额" ),
     *          @Apidoc\Returned("all_dis", type="decimal(11,2))", desc="活动总优惠金额" ),
     *          @Apidoc\Returned("commodity_set_id", type="int(11)", desc="商品编码" ),
     *          @Apidoc\Returned("actual_price", type="int(11)", desc="实际价格" ),
     *          @Apidoc\Returned("card_all_dis", type="decimal(10,2)", desc="卡券总优惠" ),
     *          @Apidoc\Returned("card_codes", type="decimal(11,2)", desc="卡券号码多个用,隔开" ),
     *          @Apidoc\Returned("card_yh", type="varchar(500)", desc="每个卡券优惠了多少" ),
     *          @Apidoc\Returned("pay_dlr_code", type="varchar(500)", desc="特殊支付账号" ),
     *          @Apidoc\Returned("order_mail_type", type="int(2)", desc="订单商品快递或者到店：1到店；2快递" ),
     *          @Apidoc\Returned("parent_order_code", type="tinyint(1)", desc="父单号" ),
     *          @Apidoc\Returned("max_point", type="varchar(50)", desc="最多可用积分" ),
     *          @Apidoc\Returned("work_time_dis", type="int(11)", desc="工时优惠" ),
     *          @Apidoc\Returned("work_time_money", type="int(11)", desc="工时原价单价" ),
     *          @Apidoc\Returned("work_time_actual_money", type="int(11)", desc="工时现价单价" ),
     *          @Apidoc\Returned("actual_point", type="int(11)", desc="实际使用积分" ),
     *          @Apidoc\Returned("actual_use_money", type="int(11)", desc="实际使用金额" ),
     *          @Apidoc\Returned("service_channel_labels", type="array", desc="服务标签" ),
     *          @Apidoc\Returned("spr_id", type="decimal(11,2)", desc="分账规则id" ),
     *          @Apidoc\Returned("is_comment", type="int(10)", desc="是否修改评价：0未修改1已修改" ),
     *          @Apidoc\Returned("can_go_url", type="tinyint(1)", desc="是否能进入商品详情：1是2否" ),
     *          @Apidoc\Returned("url", type="string", desc="商品详情页跳转链接" ),
     *          @Apidoc\Returned("gift_act_dis_money", type="decimal(11,2)", desc="赠品优惠金额(0214版本)" ),
     *          @Apidoc\Returned("is_coupon_commodity", type="tinyint(1)", desc="是否赠品券类商品，1是、0否(0214版本)" ),
     *          @Apidoc\Returned("order", type="object", desc="赠品商品订单(0214版本)",
     *              @Apidoc\Returned("id", type="string", desc="订单ID"),
     *              @Apidoc\Returned("order_code", type="string", desc="订单编号"),
     *              @Apidoc\Returned("is_effected_gift_order", type="string", desc="订单是否生效，0无效，1有效"),
     *          ),
     *     ),

     *    )，
 */
    public function detail(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("detail")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_order = new NetOrder();
        $res       = $net_order->detail($requestData, $this->user, $this->channel_type);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }

    /**
     * @Apidoc\Title("订单列表")
     * @Apidoc\Author("lzx")
     * @Apidoc\Desc("创单时间倒排")
     * @Apidoc\Url("/net-small/order/list")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 订单 取送车")
     *
     * @Apidoc\Param("page", type="int",require=true, desc="页码" )
     * @Apidoc\Param("pageSize", type="int",require=false, default=20, desc="当前页显示数量,不传默认20" )
     * @Apidoc\Param("order_status", type="int",require=false, desc="订单状态：请查看开发必读-》订单说明-》状态列表，不传就是所有订单" )
     * @Apidoc\Param("created_date", type="datetime",require=false, desc="创单时间,作为过滤参数,请求返回该时间前的订单" )
     * @Apidoc\Param("order_code", type="string",require=false, desc="订单编码" )
     * @Apidoc\Param("list_type", type="int",require=false, desc="官网渠道订单列表专用1:可申请售后列表2:已申请售后列表" )
     *
     * @Apidoc\Returned("id", type="int(11)", desc="订单id" ),
     * @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     * @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     * @Apidoc\Returned("openid", type="varchar(50)", desc="openid" ),
     * @Apidoc\Returned("vin", type="varchar(20)", desc="vin" ),
     * @Apidoc\Returned("license_plate", type="varchar(30)", desc="车牌" ),
     * @Apidoc\Returned("ic_card_no", type="varchar(30)", desc="会员号" ),
     * @Apidoc\Returned("name", type="varchar(30)", desc="姓名" ),
     * @Apidoc\Returned("phone", type="varchar(20)", desc="手机号码" ),
     * @Apidoc\Returned("source", type="varchar(32)", desc="来源" ),
     * @Apidoc\Returned("total_money", type="decimal(11,2)", desc="总金额" ),
     * @Apidoc\Returned("money", type="decimal(11,2)", desc="实付金额" ),
     * @Apidoc\Returned("card_id", type="varchar(200)", desc="卡劵" ),
     * @Apidoc\Returned("card_code", type="char(200)", desc="卡劵code" ),
     * @Apidoc\Returned("card_money", type="decimal(11,2)", desc="卡劵金额" ),
     * @Apidoc\Returned("integral", type="int(11)", desc="积分" ),
     * @Apidoc\Returned("is_group_order", type="int(11)", desc="1为团购订单" ),
     * @Apidoc\Returned("payment_method", type="varchar(20)", desc="支付方式（1现金；2积分；3卡劵；4现金+积分；5现金+卡劵；6积分+卡劵；7现金+积分+卡劵）" ),
     * @Apidoc\Returned("order_status", type="tinyint(1)", desc="订单状态：请查看开发必读-》订单说明-》状态列表" ),
     * @Apidoc\Returned("verification_user", type="varchar(10)", desc="核销人员" ),
     * @Apidoc\Returned("settlement_user", type="varchar(10)", desc="结算人员" ),
     * @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用：1是0否" ),
     * @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     * @Apidoc\Returned("creator", type="varchar(32)", desc="创建人" ),
     * @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     * @Apidoc\Returned("modifier", type="varchar(32)", desc="修改人" ),
     * @Apidoc\Returned("logistics_mode", type="tinyint(4)", desc="物流方式：1自提，2快递" ),
     * @Apidoc\Returned("common_carrier", type="varchar(30)", desc="承运公司" ),
     * @Apidoc\Returned("waybill_number", type="varchar(30)", desc="运单编号" ),
     * @Apidoc\Returned("delivery_time", type="datetime", desc="发货时间" ),
     * @Apidoc\Returned("commodity_dlr_type_id", type="int(10)", desc="商品专营店分类ID" ),
     * @Apidoc\Returned("mail_price", type="decimal(11,2)", desc="运费" ),
     * @Apidoc\Returned("receipt_address", type="varchar(100)", desc="收货地址" ),
     * @Apidoc\Returned("pay_order_code", type="varchar(32)", desc="支付订单号" ),
     * @Apidoc\Returned("zt_status", type="tinyint(4)", desc="自提状态（1供应商已发货；2货已到专营店，请等待专营店预约到店安装；3发货中）" ),
     * @Apidoc\Returned("order_source", type="int(1)", desc="订单来源：请查看开发必读-》订单说明-》来源列表" ),
     * @Apidoc\Returned("settlement_date", type="datetime", desc="扎帐时间" ),
     * @Apidoc\Returned("dlr_integral", type="int(11)", desc="专营店积分" ),
     * @Apidoc\Returned("sub_commission_id", type="int(11)", desc="分佣id" ),
     * @Apidoc\Returned("full_cut_card_id", type="varchar(200)", desc="满减卡券" ),
     * @Apidoc\Returned("full_id", type="int(11)", desc="满减ID" ),
     * @Apidoc\Returned("new_car_bag_ac_id", type="int(11)", desc="礼包活动表主键" ),
     * @Apidoc\Returned("remark", type="varchar(255)", desc="备注" ),
     * @Apidoc\Returned("sale_source", type="int(11)", desc="销售来源：2普通商城，5官微商城" ),
     * @Apidoc\Returned("ca_id", type="varchar(50)", desc="ca" ),
     * @Apidoc\Returned("user_id", type="int(11)", desc="用户表主键" ),
     * @Apidoc\Returned("is_all_comment", type="tinyint(1)", desc="是否都评价了：1都评价了0未评价" ),
     * @Apidoc\Returned("shop_lifting_id", type="tinyint(4)", desc="到店礼id" ),
     * @Apidoc\Returned("ticket_no", type="varchar(500)", desc="券号" ),
     * @Apidoc\Returned("jifen_user_id", type="int(11)", desc="对应福利商城用户id" ),
     * @Apidoc\Returned("shop_integral", type="decimal(10,2)", desc="商城积分" ),
     * @Apidoc\Returned("address_area_ids", type="varchar(30)", desc="地址id拼接" ),
     * @Apidoc\Returned("refund_money", type="decimal(11,2)", desc="退款金额" ),
     * @Apidoc\Returned("pay_time", type="varchar(20)", desc="支付时间/尾款支付时间" ),
     * @Apidoc\Returned("gift_score", type="varchar(10)", desc="赠送积分" ),
     * @Apidoc\Returned("province_id", type="int(11)", desc="省ID，用于运费模板" ),
     * @Apidoc\Returned("is_by_tc", type="tinyint(2)", desc="0普通订单；1老优惠；2N延保 3心悦套餐；4五年双保；5车联服务包" ),
     * @Apidoc\Returned("product_card_pwd", type="varchar(50)", desc="卡券密码" ),
     * @Apidoc\Returned("product_card_money", type="decimal(11,2)", desc="卡券金额" ),
     * @Apidoc\Returned("is_cc_ok", type="tinyint(2)", desc="0积分卡券核销成功，1积分扣失败，2卡券核销失败" ),
     * @Apidoc\Returned("act_code", type="varchar(42)", desc="活动编码" ),
     * @Apidoc\Returned("n_dis_id", type="int(11)", desc="N件N折ID" ),
     * @Apidoc\Returned("is_send_e3s", type="tinyint(2)", desc="是否下发e3s，0未1已下发" ),
     * @Apidoc\Returned("recharge_time", type="datetime", desc="充值时间" ),
     * @Apidoc\Returned("pay_order_code2", type="varchar(50)", desc="定金支付编码" ),
     * @Apidoc\Returned("front_money", type="decimal(11,2)", desc="定金" ),
     * @Apidoc\Returned("front_money_dedu", type="decimal(11,2)", desc="定金抵扣，定金膨胀" ),
     * @Apidoc\Returned("pre_sale_id", type="int(11)", desc="预售ID" ),
     * @Apidoc\Returned("pre_point", type="decimal(11,2)", desc="预售使用积分" ),
     * @Apidoc\Returned("comment_star", type="tinyint(1)", desc="星星 1-5" ),
     * @Apidoc\Returned("distance", type="varchar(30)", desc="公里数" ),
     * @Apidoc\Returned("pay_dlr_code", type="varchar(100)", desc="特殊支付账号" ),
     * @Apidoc\Returned("cashier_trade_no", type="varchar(50)", desc="财务中心支付单号" ),
     * @Apidoc\Returned("cashier_trade_no2", type="varchar(50)", desc="预售财务中心支付单号" ),
     * @Apidoc\Returned("is_plat_settlement", type="tinyint(1)", desc="是否平台结算0未1已结算" ),
     * @Apidoc\Returned("parent_order_code", type="varchar(50)", desc="父单号" ),
     * @Apidoc\Returned("channel", type="tinyint(2)", desc="销售渠道，1小程序2官网3app" ),
     * @Apidoc\Returned("parent_order_type", type="tinyint(3)", desc="3父订，正常显示为1原单2子单" ),
     * @Apidoc\Returned("b_act_goods_price", type="decimal(11,2)", desc="商品原价总价" ),
     * @Apidoc\Returned("cashier_settlement_no", type="varchar(50)", desc="中心结算单号" ),
     * @Apidoc\Returned("is_anonymous", type="tinyint(1)", desc="是否匿名，0不匿名，1匿名" ),
     * @Apidoc\Returned("plat_order_id", type="varchar(50)", desc="平台返回业务系统ID" ),
     * @Apidoc\Returned("cashier_settlement_no2", type="varchar(50)", desc="预售中心结算单号" ),
     * @Apidoc\Returned("is_change_comment", type="tinyint(1)", desc="是否修改过评价，0未修改1已修改" ),
     * @Apidoc\Returned("is_comment_time", type="timestamp", desc="点评时间" ),
     * @Apidoc\Returned("receive_time", type="timestamp", desc="收货时间" ),
     * @Apidoc\Returned("buy_again", type="string", desc="再次购买按钮文案" ),
     * @Apidoc\Returned("waybill", type="string", desc="查看物流按钮文案" ),
     * @Apidoc\Returned("refund", type="string", desc="申请退款按钮文案" ),
     * @Apidoc\Returned("cancel", type="string", desc="取消按钮文案" ),
     * @Apidoc\Returned("go_group", type="string", desc="拼单按钮文案" ),
     * @Apidoc\Returned("go_pay", type="string", desc="支付按钮文案" ),
     * @Apidoc\Returned("get_card", type="string", desc="领取卡券按钮文案" ),
     * @Apidoc\Returned("is_old_ariya", type="int(1)", desc="是否ariya好物商城订单" ),
     * @Apidoc\Returned("goods", type="array", desc="订单商品列表",
     *      @Apidoc\Returned("id", type="int(11)", desc="订单商品id" ),
     *      @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     *      @Apidoc\Returned("dlr_code", type="varchar(255)", desc="专营店编码" ),
     *      @Apidoc\Returned("commodity_id", type="int(11)", desc="商品编码" ),
     *      @Apidoc\Returned("price", type="decimal(11,2)", desc="原价" ),
     *      @Apidoc\Returned("count", type="int(11)", desc="数量" ),
     *      @Apidoc\Returned("sku_id", type="int(11)", desc="sku_id" ),
     *      @Apidoc\Returned("car_info", type="varchar(100)", desc="车辆信息（车系、排量、年份、轮胎）" ),
     *      @Apidoc\Returned("sku_info", type="varchar(200)", desc="sku信息，颜色等" ),
     *      @Apidoc\Returned("commodity_name", type="varchar(255)", desc="订单商品名" ),
     *      @Apidoc\Returned("commodity_pic", type="varchar(255)", desc="商品图" ),
     *      @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用" ),
     *      @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *      @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *      @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *      @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *      @Apidoc\Returned("mail_price", type="decimal(10,2)", desc="快递费" ),
     *      @Apidoc\Returned("divided_price", type="decimal(10,2)", desc="分成价格" ),
     *      @Apidoc\Returned("install_fee", type="decimal(10,2)", desc="安装费" ),
     *      @Apidoc\Returned("sales_source", type="tinyint(2)", desc="销售来源：1平台自营销售，2专营店销售，3官微销" ),
     *      @Apidoc\Returned("commodity_class", type="tinyint(2)", desc="商品种类：1实物商品，2虚拟商品，3电子卡劵" ),
     *      @Apidoc\Returned("ticket_no", type="varchar(32)", desc="券号" ),
     *      @Apidoc\Returned("card_ids", type="varchar(500)", desc="卡券ID，多个用,隔开" ),
     *      @Apidoc\Returned("limit_id", type="int(11)", desc="限时折扣表ID" ),
     *      @Apidoc\Returned("suit_id", type="int(11)", desc="套装ID" ),
     *      @Apidoc\Returned("comment_id", type="int(11)", desc="点评表主键，0未点评" ),
     *      @Apidoc\Returned("point_discount", type="decimal(5,0", desc="积分折扣" ),
     *      @Apidoc\Returned("b_act_price", type="decimal(11,2)", desc="活动前原价" ),
     *      @Apidoc\Returned("full_id", type="int(11)", desc="满减ID" ),
     *      @Apidoc\Returned("n_dis_id", type="int(11)", desc="N件N折ID" ),
     *      @Apidoc\Returned("group_id", type="int(11)", desc="团购ID" ),
     *      @Apidoc\Returned("limit_dis_money", type="decimal(11,2)", desc="限时折扣优惠金额" ),
     *      @Apidoc\Returned("pre_sale_id", type="int(11)", desc="团购ID" ),
     *      @Apidoc\Returned("full_dis_money", type="decimal(11,2)", desc="满减优惠金额" ),
     *      @Apidoc\Returned("n_dis_money", type="decimal(11,2)", desc="n件N折优惠金额" ),
     *      @Apidoc\Returned("pre_sale_dis_money", type="decimal(11,2)", desc="预售优惠金额" ),
     *      @Apidoc\Returned("group_dis_money", type="decimal(11,2)", desc="预售优惠金额" ),
     *      @Apidoc\Returned("suit_dis_money", type="decimal(11,2)", desc="套餐优惠金额" ),
     *      @Apidoc\Returned("all_dis", type="decimal(11,2)", desc="活动总优惠金额" ),
     *      @Apidoc\Returned("commodity_set_id", type="int(11)", desc="商品编码" ),
     *      @Apidoc\Returned("actual_price", type="decimal(10,2)", desc="实际价格" ),
     *      @Apidoc\Returned("card_all_dis", type="decimal(11,2)", desc="卡券总优惠" ),
     *      @Apidoc\Returned("card_codes", type="varchar(500)", desc="卡券号码多个用,隔开" ),
     *      @Apidoc\Returned("card_yh", type="varchar(500)", desc="每个卡券优惠了多少" ),
     *      @Apidoc\Returned("pay_dlr_code", type="varchar(100)", desc="特殊支付账号" ),
     *      @Apidoc\Returned("order_mail_type", type="tinyint(1)", desc="订单商品快递或者到店1快递2到店支付到不同账号" ),
     *      @Apidoc\Returned("parent_order_code", type="varchar(50)", desc="父单号" ),
     *      @Apidoc\Returned("max_point", type="int(11)", desc="最多可用积分" ),
     *      @Apidoc\Returned("actual_point", type="int(11)", desc="实际使用积分" ),
     *      @Apidoc\Returned("actual_use_money", type="decimal(11,2)", desc="实际使用金额" ),
     *      @Apidoc\Returned("spr_id", type="int(10)", desc="分账规则id" ),
     *      @Apidoc\Returned("is_comment", type="tinyint(1)", desc="是否修改评价：0未修改1已修改" )
     * ),
     * @Apidoc\Returned("get_card_id", type="string", desc="可领取卡券id" ),
     * @Apidoc\Returned("stat", type="string", desc="状态中文名" ),
     * @Apidoc\Returned("goods_count", type="int(11)", desc="商品数量" ),
     * @Apidoc\Returned("dlr_name", type="varchar(100)", desc="专营店名称" ),
     * @Apidoc\Returned("all_money_word", type="decimal(11,2)", desc="订单总金额" ),
     * @Apidoc\Returned("can_buy", type="tinyint(1)", desc="是否可购买：1是0否" ),
     * @Apidoc\Returned("order_after_id", type="int(11)", desc="售后订单id" ),
     * @Apidoc\Returned("order_afs_type", type="int(11)", desc="售后订单类型1:仅退款2:退货3:换货" ),
     * @Apidoc\Returned("afs_service_id", type="string", desc="售后单编码" ),
     * @Apidoc\Returned("afs_status", type="int(11)", desc="售后状态" ),
     * @Apidoc\Returned("afs_created_date", type="string", desc="售后申请时间" ),
     * @Apidoc\Returned("can_after", type="tinyint(1)", desc="是否可售后，1是0否" ),
     * @Apidoc\Returned("show_invoice", type="tinyint(1)", desc="是否显示发票模块，1是0否" ),
     * @Apidoc\Returned("show_after_type", type="tinyint(1)", desc="（此字段仅服务包、到店代金券、到店电子券、取送车券订单使用）显示售后提示按钮类型：0不显示；1服务包售后按钮；2到店代金券、到店电子券、取送车券售后按钮" ),
     * @Apidoc\Returned("group_qrc_code", type="string", desc="团购二维码" ),
     * @Apidoc\Returned("pre_sale_info", type="array/json", desc="预售信息",
     *      @Apidoc\Returned("purchase_number", type="int(10)", desc="限购数量" ),
     *      @Apidoc\Returned("front_money", type="decimal(10,2)", desc="定金" ),
     *      @Apidoc\Returned("dedu_money", type="decimal(10,2)", desc="抵扣金额" ),
     *      @Apidoc\Returned("id", type="int(11)", desc="预售id" ),
     *      @Apidoc\Returned("can_use_card", type="int(1)", desc="是否支持用券，1支持，0否" ),
     *      @Apidoc\Returned("dec", type="text", desc="活动描述" ),
     *      @Apidoc\Returned("front_s_time", type="datetime", desc="定金支付起始时间" ),
     *      @Apidoc\Returned("front_e_time", type="datetime", desc="定金支付结束时间" ),
     *      @Apidoc\Returned("balance_s_time", type="datetime", desc="尾款支付起始时间" ),
     *      @Apidoc\Returned("balance_e_time", type="datetime", desc="尾款支付结束时间" ),
     *      @Apidoc\Returned("pre_status", type="int(1)", desc="0不在任何时间内，1支付定金时间，2支付尾款时间" ),
     *      @Apidoc\Returned("wk_times", type="string", desc="尾款支付时间段" ),
     *      @Apidoc\Returned("yh_money", type="decimal(10,2)", desc="优惠金额" ),
     *      @Apidoc\Returned("wk_money", type="decimal(10,2)", desc="尾款金额" ),
     *      @Apidoc\Returned("wk_use_money", type="decimal(10,2)", desc="尾款使用现金(列表页显示)" ),
     *      @Apidoc\Returned("front_use_money", type="decimal(10,2)", desc="定金使用现金(列表页显示)" ),
     *      @Apidoc\Returned("wk_money", type="decimal(10,2)", desc="尾款金额" ),
     *      @Apidoc\Returned("can_buy", type="int(10)", desc="可购买数量" ),
     *      @Apidoc\Returned("yd_times", type="string", desc="预定时间段" )
     * ),
     * @Apidoc\Returned("can_del", type="int", desc="订单是否可删除：1是 0否" )
     *
     * @Apidoc\Returned ("astrict_data",type="array/json",desc="订单详情增加限制操作",
     *     @Apidoc\Returned("after_sale", type="array/json", desc="申请退款限制",
     *          @Apidoc\Returned ("is_astrict",type="tinyint(2)",desc="是否限制：0-不限制；1-限制；,默认为0"),
     *          @Apidoc\Returned ("msg",type="varchar(50)",desc="限制提示"),
     *     )
     * ),
     * @Apidoc\Returned("gift_order", type="array/json", desc="赠品券订单",
     *     @Apidoc\Returned ("is_can_after",type="tinyint(2)",desc="是否可以发起售后：0-不可以 1-可以"),
     *     @Apidoc\Returned ("order_code_arr",type="array/json",desc="赠券品订单编码"),
     *     @Apidoc\Returned ("msg",type="varchar(50)",desc="不可以发起售后提示"),
     * ),
     *
     * @Apidoc\Returned("invoice_info", type="array/json", desc="发票信息",
     *     @Apidoc\Returned ("show_invoice",type="tinyint(2)",desc="是否显示发票模块：0-不可以 1-可以"),
     *     @Apidoc\Returned ("button_type",type="varchar(50)",desc="apply-申请 edit-修改 show-查看"),
     *     @Apidoc\Returned ("bottom_article",type="varchar(50)",desc="底部文案"),
     *     @Apidoc\Returned ("show_article",type="varchar(50)",desc="按钮交互文案"),
     * ),
     *
     */
    public function orderList(OrderValidate $validate)
    {

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("list")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $net_order = new NetOrder();
        $res       = $net_order->order_list($requestData, $this->user, $this->channel_type);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }

    }

    /**
     * @Apidoc\Title("取消订单")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/cancel")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Tag("NI+ 订单 取消")
     *
     * @Apidoc\Param("order_id", type="int(11)",require=true, desc="订单ID" )
     *
     * @Apidoc\Returned("message", type="string", desc="提示信息" )
     *
     */
    public function cancel(OrderValidate $validate)
    {

        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("cancel")->check($requestData);

        //校验失败,返回异常--
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order_no = $requestData['order_id'];
        $where    = ['id' => $order_no];
        $order    = $this->order_model->getOne(array('where' => $where));
        if (!$order) {
            return $this->setResponseError("订单不存在", 403)->send();
        } else {
            $data = array(
                'order_status'      => 3,
                'modifier'          => 'qd_re',
                'last_updated_date' => date('Y-m-d H:i:s'),
            );
            $res  = $this->order_model->saveData($data, $where);
            if ($res) {
                return $this->setResponseData('ok')->send();
            } else {
                return $this->setResponseError("取消订单失败", 405)->send();
            }
        }
    }

    /**
     * @Apidoc\Title("订单完成")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/finish")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 订单")
     *
     * @Apidoc\Param("order_id", type="int(11)",require=true, desc="订单ID" )
     *
     * @Apidoc\Returned("order", type="array/json", desc="订单信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="订单id" ),
     *     @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     *     @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *     @Apidoc\Returned("openid", type="varchar(50)", desc="openid" ),
     *     @Apidoc\Returned("vin", type="varchar(20)", desc="vin" ),
     *     @Apidoc\Returned("license_plate", type="varchar(30)", desc="车牌" ),
     *     @Apidoc\Returned("ic_card_no", type="varchar(30)", desc="会员号" ),
     *     @Apidoc\Returned("name", type="varchar(30)", desc="姓名" ),
     *     @Apidoc\Returned("phone", type="varchar(20)", desc="手机号码" ),
     *     @Apidoc\Returned("source", type="varchar(32)", desc="来源" ),
     *     @Apidoc\Returned("total_money", type="decimal(11,2)", desc="总金额" ),
     *     @Apidoc\Returned("money", type="decimal(11,2)", desc="实付金额" ),
     *     @Apidoc\Returned("card_id", type="varchar(200)", desc="卡劵" ),
     *     @Apidoc\Returned("card_code", type="char(200)", desc="卡劵code" ),
     *     @Apidoc\Returned("card_money", type="decimal(11,2)", desc="卡劵金额" ),
     *     @Apidoc\Returned("integral", type="int(11)", desc="积分" ),
     *     @Apidoc\Returned("payment_method", type="varchar(20)", desc="支付方式（1现金；2积分；3卡劵；4现金+积分；5现金+卡劵；6积分+卡劵；7现金+积分+卡劵）" ),
     *     @Apidoc\Returned("order_status", type="tinyint(1)", desc="订单状态：请查看开发必读-》订单说明-》状态列表" ),
     *     @Apidoc\Returned("verification_user", type="varchar(10)", desc="核销人员" ),
     *     @Apidoc\Returned("settlement_user", type="varchar(10)", desc="结算人员" ),
     *     @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用：1是0否" ),
     *     @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *     @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *     @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *     @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *     @Apidoc\Returned("logistics_mode", type="tinyint(4)", desc="物流方式：1自提，2快递" ),
     *     @Apidoc\Returned("common_carrier", type="varchar(30)", desc="承运公司" ),
     *     @Apidoc\Returned("waybill_number", type="varchar(30)", desc="运单编号" ),
     *     @Apidoc\Returned("delivery_time", type="datetime", desc="发货时间" ),
     *     @Apidoc\Returned("commodity_dlr_type_id", type="int(10)", desc="商品专营店分类ID" ),
     *     @Apidoc\Returned("mail_price", type="decimal(11,2)", desc="运费" ),
     *     @Apidoc\Returned("receipt_address", type="varchar(100)", desc="收货地址" ),
     *     @Apidoc\Returned("pay_order_code", type="varchar(32)", desc="支付订单号/尾款支付订单号" ),
     *     @Apidoc\Returned("zt_status", type="tinyint(4)", desc="自提状态（1供应商已发货；2货已到专营店，请等待专营店预约到店安装；3发货中）" ),
     *     @Apidoc\Returned("order_source", type="int(1)", desc="订单来源：请查看开发必读-》订单说明-》来源列表" ),
     *     @Apidoc\Returned("settlement_date", type="datetime", desc="扎帐时间" ),
     *     @Apidoc\Returned("dlr_integral", type="int(11)", desc="专营店积分" ),
     *     @Apidoc\Returned("sub_commission_id", type="int(11)", desc="分佣id" ),
     *     @Apidoc\Returned("full_cut_card_id", type="varchar(200)", desc="满减卡券" ),
     *     @Apidoc\Returned("full_id", type="int(11)", desc="满减ID" ),
     *     @Apidoc\Returned("new_car_bag_ac_id", type="int(11)", desc="礼包活动表主键" ),
     *     @Apidoc\Returned("remark", type="varchar(255)", desc="备注" ),
     *     @Apidoc\Returned("sale_source", type="int(11)", desc="销售来源：2普通商城，5官微商城" ),
     *     @Apidoc\Returned("ca_id", type="varchar(50)", desc="ca" ),
     *     @Apidoc\Returned("user_id", type="int(11)", desc="用户表主键" ),
     *     @Apidoc\Returned("is_all_comment", type="tinyint(1)", desc="是否都评价了：1都评价了0未评价" ),
     *     @Apidoc\Returned("shop_lifting_id", type="tinyint(4)", desc="到店礼id" ),
     *     @Apidoc\Returned("ticket_no", type="varchar(500)", desc="券号" ),
     *     @Apidoc\Returned("jifen_user_id", type="int(11)", desc="对应福利商城用户id" ),
     *     @Apidoc\Returned("shop_integral", type="decimal(10,2)", desc="商城积分" ),
     *     @Apidoc\Returned("address_area_ids", type="varchar(30)", desc="地址id拼接" ),
     *     @Apidoc\Returned("refund_money", type="decimal(11,2)", desc="退款金额" ),
     *     @Apidoc\Returned("pay_time", type="varchar(20)", desc="支付时间" ),
     *     @Apidoc\Returned("gift_score", type="varchar(10)", desc="赠送积分" ),
     *     @Apidoc\Returned("province_id", type="int(11)", desc="省ID，用于运费模板" ),
     *     @Apidoc\Returned("is_by_tc", type="tinyint(2)", desc="0普通订单；1老优惠；2N延保 3心悦套餐；4五年双保；5车联服务包" ),
     *     @Apidoc\Returned("product_card_pwd", type="varchar(50)", desc="卡券密码" ),
     *     @Apidoc\Returned("product_card_money", type="decimal(11,2)", desc="卡券金额" ),
     *     @Apidoc\Returned("is_cc_ok", type="tinyint(2)", desc="0积分卡券核销成功，1积分扣失败，2卡券核销失败" ),
     *     @Apidoc\Returned("act_code", type="varchar(42)", desc="活动编码" ),
     *     @Apidoc\Returned("n_dis_id", type="int(11)", desc="N件N折ID" ),
     *     @Apidoc\Returned("is_send_e3s", type="tinyint(2)", desc="是否下发e3s，0未1已下发" ),
     *     @Apidoc\Returned("recharge_time", type="datetime", desc="充值时间" ),
     *     @Apidoc\Returned("pay_order_code2", type="varchar(50)", desc="定金支付编码" ),
     *     @Apidoc\Returned("front_money", type="decimal(11,2)", desc="定金" ),
     *     @Apidoc\Returned("front_money_dedu", type="decimal(11,2)", desc="定金抵扣，定金膨胀" ),
     *     @Apidoc\Returned("pre_sale_id", type="int(11)", desc="预售ID" ),
     *     @Apidoc\Returned("pre_point", type="decimal(11,2)", desc="预售使用积分" ),
     *     @Apidoc\Returned("comment_star", type="tinyint(1)", desc="星星 1-5" ),
     *     @Apidoc\Returned("distance", type="varchar(30)", desc="公里数" ),
     *     @Apidoc\Returned("pay_dlr_code", type="varchar(100)", desc="特殊支付账号" ),
     *     @Apidoc\Returned("cashier_trade_no", type="varchar(50)", desc="财务中心支付单号" ),
     *     @Apidoc\Returned("cashier_trade_no2", type="varchar(50)", desc="预售财务中心支付单号" ),
     *     @Apidoc\Returned("is_plat_settlement", type="tinyint(1)", desc="是否平台结算0未1已结算" ),
     *     @Apidoc\Returned("parent_order_code", type="varchar(50)", desc="父单号" ),
     *     @Apidoc\Returned("channel", type="tinyint(2)", desc="销售渠道，1小程序2官网3app" ),
     *     @Apidoc\Returned("parent_order_type", type="tinyint(3)", desc="3父订，正常显示为1原单2子单" ),
     *     @Apidoc\Returned("b_act_goods_price", type="decimal(11,2)", desc="商品原价总价" ),
     *     @Apidoc\Returned("cashier_settlement_no", type="varchar(50)", desc="中心结算单号" ),
     *     @Apidoc\Returned("is_anonymous", type="tinyint(1)", desc="是否匿名，0不匿名，1匿名" ),
     *     @Apidoc\Returned("plat_order_id", type="varchar(50)", desc="平台返回业务系统ID" ),
     *     @Apidoc\Returned("cashier_settlement_no2", type="varchar(50)", desc="预售中心结算单号" ),
     *     @Apidoc\Returned("get_card", type="string", desc="领取卡券按钮文案" ),
     *     @Apidoc\Returned("is_jump_draw", type="tinyint(1)", desc="是否跳转至抽奖页：1是0否" ),
     *     @Apidoc\Returned("dd_dlr_code", type="string", desc="到店专营店编码" ),
     *     @Apidoc\Returned("is_cap_commodity", type="tinyint(1)", desc="是否是cap活动商品 0-否 1-是" ),
     * @Apidoc\Returned ("yy_info",type="array/json",desc="预约信息",
     *      @Apidoc\Returned ("can_yy",type="tinyint(2)",desc="是否能够预约0不可1可以"),
     *      @Apidoc\Returned ("yy_type",type="tinyint(2)",desc="预约类型,1:快速保养,2定期保养,4:预约维修,5预约钣喷(1,2还需要其他判断)"),
     *      @Apidoc\Returned("list", type="array/json", desc="预约二维数组",
     *          @Apidoc\Returned("id", type="int(11)", desc="预约ID" ),
     *          @Apidoc\Returned("subscribe_code", type="varchar(255)", desc="预约单号" ),
     *          @Apidoc\Returned("order_id", type="int(11)", desc="订单表ID" ),
     *          @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     *          @Apidoc\Returned("subscribe_time", type="timestamp", desc="预约时间" ),
     *          @Apidoc\Returned("mileage", type="varchar(50)", desc="行驶里程" ),
     *          @Apidoc\Returned("car_name", type="varchar(50)", desc="车主姓名" ),
     *          @Apidoc\Returned("car_phone", type="varchar(20)", desc="车主电话" ),
     *          @Apidoc\Returned("car_series_code", type="varchar(20)", desc="车系编码" ),
     *          @Apidoc\Returned("car_series_name", type="varchar(20)", desc="车系名称" ),
     *          @Apidoc\Returned("car_type_code", type="varchar(20)", desc="车型编码" ),
     *          @Apidoc\Returned("adviser_id", type="int(11)", desc="服务顾问ID" ),
     *          @Apidoc\Returned("adviser_name", type="varchar(50)", desc="服务顾问名" ),
     *          @Apidoc\Returned("technician_id", type="varchar(50)", desc="金牌技师ID" ),
     *          @Apidoc\Returned("technician_name", type="varchar(50)", desc="金牌技师名" ),
     *          @Apidoc\Returned("subscribe_type", type="tinyint(2)", desc="同yy_type" ),
     *          @Apidoc\Returned("reason_id", type="tinyint(2)", desc="取消原因id" ),
     *          @Apidoc\Returned("reason_desc", type="varchar(255)", desc="取消原因说明" ),
     *          @Apidoc\Returned("status", type="tinyint(1)", desc="预约单状态,2：已预约 3：已受理 4：取消 5：完成 6：可取消（估价单后SA更新为可取消） 7:进行中" ),
     *          @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用：1是0否" ),
     *     )
     * ),
     * )
     * @Apidoc\Returned("group_order", type="array/json", desc="团购订单发起信息",
     *      @Apidoc\Returned("id", type="int(11)", desc="自动递增字段" ),
     *      @Apidoc\Returned("openid", type="varchar(32)", desc="openid" ),
     *      @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编号--对应订单表的order_code" ),
     *      @Apidoc\Returned("group_code", type="varchar(32)", desc="团购编码，一个团一个编码" ),
     *      @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *      @Apidoc\Returned("group_id", type="int(10)", desc="团购表ID" ),
     *      @Apidoc\Returned("status", type="int(2)", desc="状态:1下单，2待支付，3已支付,4待发货,5已发货,6已退款(没有),7已取消" ),
     *      @Apidoc\Returned("number", type="int(11)", desc="数量" ),
     *      @Apidoc\Returned("commodity_id", type="int(11)", desc="商品ID" ),
     *      @Apidoc\Returned("group_start_time", type="datetime", desc="开团时间" ),
     *      @Apidoc\Returned("is_enable", type="varchar(2)", desc="是否可用" ),
     *      @Apidoc\Returned("creator", type="varchar(50)", desc="创建人" ),
     *      @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *      @Apidoc\Returned("modifier", type="varchar(50)", desc="更新人" ),
     *      @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *      @Apidoc\Returned("user_id", type="int(11)", desc="用户表主键" ),
     *      @Apidoc\Returned("nickname", type="varchar(50)", desc="微信昵称" ),
     *      @Apidoc\Returned("headimg", type="varchar(200)", desc="微信头像" ),
     *      @Apidoc\Returned("cover_image", type="varchar(200)", desc="商品封面图" ),
     *      @Apidoc\Returned("title", type="varchar(20)", desc="拼团活动名称" ),
     *      @Apidoc\Returned("inc_number", type="int(11)", desc="拼团剩余人数" )
     * )
     * @Apidoc\Returned("group_list", type="array/json", desc="团购参与人信息列表（字段同group_order）")
     * @Apidoc\Returned("group_info", type="array/json", desc="团购活动信息",
     *      @Apidoc\Returned("id", type="int(11)", desc="拼团活动id"),
     *      @Apidoc\Returned("title", type="varchar(20)", desc="活动名称"),
     *      @Apidoc\Returned("start_time", type="datetime", desc="开始时间"),
     *      @Apidoc\Returned("end_time", type="datetime", desc="结束时间"),
     *      @Apidoc\Returned("people_number", type="int(11)", desc="参团人数"),
     *      @Apidoc\Returned("purchase_number", type="int(11)", desc="限购数量"),
     *      @Apidoc\Returned("buy_hour", type="int(11)", desc="参团时间"),
     *      @Apidoc\Returned("rule", type="text", desc="规则"),
     *      @Apidoc\Returned("commodity_id", type="tinyint(4)", desc="商品id"),
     *      @Apidoc\Returned("lowest_price", type="varchar(255)", desc="最低价"),
     * @Apidoc\Returned("shelves_type", type="int(10)", desc="上架来源：1.平台自营 2.专营店 3.官微 4.活动 5.车生活" )
     * ),
     */
    public function finish(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("cancel")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $id       = $requestData['order_id'];
        $dlr_code = $this->channel_type;
        if ($dlr_code == 'YGDLR') {
            $dlr_code = 'NISSAN';
        }
        $order = $this->order_model->getOneByPk($id);
        if (!$order) {
            return $this->setResponseError("订单不存在", 403)->send();
        }
        $group_order = [];
        $group_list  = [];
        $group_info  = [];
        if ($order['order_source'] == 2) {
            $order_code     = $order['order_code'];
            $ac_group_model = new AcGroup();
            $group_order    = $ac_group_model->getOne(['where' => ['order_code' => $order_code]]);
            if (!$group_order) {
                return $this->setResponseError("团购订单未开始或者已结束", 403)->send();
            }
            $group_id         = $group_order['group_id'];
            $group_order_code = $group_order['group_code'];
            //显示支付以上的
            $group_list  = $ac_group_model->getList(['where' => ['group_code' => $group_order['group_code'], 'commodity_id' => $group_order['commodity_id'], 'status' => ['in', '3,4,5']]]);
            $group_model = new DbFightGroup();
            $time        = date('Y-m-d H:i:s');
            $group_info  = $group_model->getGroupInfo(['where' => ['a.id' => $group_id, 'a.is_enable' => 1],
                                                       'field' => "a.id,a.title,a.start_time,a.end_time,a.people_number,a.purchase_number,a.buy_hour,a.rule,b.commodity_id,b.lowest_price,d.shelves_type"]);
            if (!$group_info) {
                return $this->setResponseError("团购订单未开始或者已结束", 403)->send();
            } else {
                if ($group_list) {
                    foreach ($group_list as $k => $v) {
                        if ($v['status'] == 4) {
                            $group_order['status'] = 4;
                        }
                        $group_list[$k]['nickname'] = $v['nickname'];
                        $group_list[$k]['pic']      = $v['headimg'];
                    }
                }
                $goods_model                = new DbCommodity();
                $goods                      = $goods_model->getOneByPk($group_info['commodity_id']);
                $group_order['cover_image'] = $goods['cover_image'];
                $group_order['title']       = $group_info['title'];

                if (strtotime(sprintf("%s +%s hours", $group_order['group_start_time'], $group_info['buy_hour'])) < time()) {
                    return $this->setResponseError("团购订单未开始或者已结束", 403)->send();
                }
                $number = $group_info['people_number'] - count($group_list);
                if ($number < 1) {
                    $group_order['status'] = 4;
                }
                $group_order['inc_number'] = $number;
            }
        } else {
            //跳转这个页面得时候判断是否已经支付过了。更新支付状态---暂时判断快递类的
            if (in_array($order['order_status'], [1, 8]) && $order['money'] > 0) {
                $order_stt = NetOrder::queryorder($order['pay_order_code']);
                if ($order_stt == 'SUCCESS') {
                    $this->order_model->saveData(['order_status' => 2, 'last_updated_date' => date('Y-m-d H:i:s'), 'modifier' => 'o_p_finis'], ['id' => $id]);
                }
            }
            //团购没计入满减啊领卡圈
            $order_get_card = NetOrder::full_card($order['order_code']);
            $order['get_card'] = $order_get_card;
        }
        $all_data = [
            'order'       => $order,
            'group_order' => $group_order,
            'group_list'  => $group_list,
            'group_info'  => $group_info,
        ];

//        if ($order['full_id']) {
//            $full_model = new DbFullDiscount();
//            $card_r_model = new BuCardReceiveRecord();
//            $full_info = $full_model->getOneByPk($order['full_id']);
//            $card_r = $card_r_model->getOne(['where' => ['status' => ['<>', 2], 'card_id' => $full_info['preferential_card_id'], 'act_id' => $order['order_code']]]);
//            if ($full_info) {
//                if ($full_info['is_preferential_card'] == 1 && !$card_r) {
//                    $order['get_card'] = "领取卡券";
//                    $order['get_card_id'] = $full_info['preferential_card_id'];
//                }
//            }
//        }
        return $this->setResponseData($all_data)->send();
//        $redis_name = "user-pay-finish-" . $dlr_code;
//        $qr_code = redis($redis_name);
//        $qrc = 0;
//        if (!$wx_user) {
//            if (!$qr_code) {
//                $QRC = new \api\wechat\QrCode();
//                $qrc_id = 20001;
//                $qr_code = $QRC::createQrCodeLimit($dlr_code, $qrc_id);
//                if ($qr_code) {
//                    $qr_code = $qr_code['qrcode'];
//                    redis($redis_name, $qr_code, 3600);
//                }
//            }
//            $qrc = 1;
//        }

    }

    /**
     * @Apidoc\Title("卡券冲突选择")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/card-clash")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 订单 卡券")
     *
     * @Apidoc\Param("choose_card", type="string",require=true, desc="已选卡券ID,隔开" )
     * @Apidoc\Param("all_card", type="string",require=true, desc="所有卡券ID,隔开" )
     *
     * @Apidoc\Returned("can", type="array", desc="可使用卡券id列表" )
     * @Apidoc\Returned("can_not", type="array", desc="不可使用卡券id列表" )
     */
    public function card_clash(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("card_clash")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $choose_card = $requestData['choose_card'];
        $all_card    = $requestData['all_card'];

        $net_order = new NetOrder();
        $res       = $net_order->card_clash($choose_card, $all_card);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }
    }

    /**
     * @Apidoc\Title("获取订单可用的卡券和活动优惠列表")
     * @Apidoc\Author("hechao")
     * @Apidoc\Url("/net-small/order/calc-discounts")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("NI+ 订单 卡券 PZ1A")
     *
     * @Apidoc\Param("choose_promotion_json", type="array", childrenType="object", desc="选中的优惠活动",
     *     @Apidoc\Param("commodity_id", type="string", desc="商品ID"),
     *     @Apidoc\Param("set_sku_id", type="string", desc="商品set_sku_id"),
     *     @Apidoc\Param("act_id", type="string", desc="活动ID"),
     *     @Apidoc\Param("act_type", type="string", desc="活动类型，1-限时折扣 3-满减  6-N件N折 10-秒杀"),
     * ),
     * @Apidoc\Param("choose_card_ids", type="string",require=true, desc="已选卡券ID,隔开" )
     * @Apidoc\Param("choose_card_codes", type="string",require=true, desc="已选卡券code,隔开" )
     * @Apidoc\Param("is_choose", type="string",require=true, desc="操作状态：1-选中/0-取消" )
     * @Apidoc\Param("order_code", type="string",require=true, desc="订单编码" )
     *
     * @Apidoc\Returned("y", type="object", desc="可用优惠列表",
     *     @Apidoc\Returned("promotions", type="array", childrenType="object", desc="优惠活动",
     *         @Apidoc\Returned("commodity_id", type="string", desc="商品ID"),
     *         @Apidoc\Returned("commodity_name", type="string", desc="商品名称"),
     *         @Apidoc\Returned("set_sku_id", type="string", desc="商品set_sku_id"),
     *         @Apidoc\Returned("act_id", type="string", desc="活动ID"),
     *         @Apidoc\Returned("act_name", type="string", desc="活动名称"),
     *         @Apidoc\Returned("act_type", type="string", desc="活动类型，1-限时折扣 3-满减  6-N件N折 10-秒杀"),
     *         @Apidoc\Returned("selected", type="string", desc="是否选中，1选中，0不选中"),
     *         @Apidoc\Returned("disabled", type="string", desc="是否禁用，1禁用，0不禁用"),
     *         @Apidoc\Returned("disabled_remark", type="string", desc="禁用备注"),
     *     ),
     *     @Apidoc\Returned("coupons", type="array", childrenType="object", desc="卡券列表",
     *         @Apidoc\Returned("card_name", type="string", desc="卡券名称"),
     *         @Apidoc\Returned("card_id", type="string", desc="卡券ID"),
     *         @Apidoc\Returned("card_quota", type="string", desc="卡券优惠金额"),
     *         @Apidoc\Returned("validity_date_end", type="string", desc="失效时间"),
     *         @Apidoc\Returned("validity_date_start", type="string", desc="有效时间"),
     *         @Apidoc\Returned("selected", type="string", desc="是否选中，1选中，0不选中"),
     *         @Apidoc\Returned("disabled", type="string", desc="是否禁用，1禁用，0不禁用"),
     *         @Apidoc\Returned("disabled_remark", type="string", desc="禁用备注"),
     *     ),
     * ),
     * @Apidoc\Returned("n", type="object", desc="不可用优惠列表",
     *     @Apidoc\Returned("promotions", type="array", childrenType="object", desc="优惠活动",
     *         @Apidoc\Returned("commodity_id", type="string", desc="商品ID"),
     *         @Apidoc\Returned("commodity_name", type="string", desc="商品名称"),
     *         @Apidoc\Returned("set_sku_id", type="string", desc="商品 set_sku_id"),
     *         @Apidoc\Returned("act_id", type="string", desc="活动ID"),
     *         @Apidoc\Returned("act_name", type="string", desc="活动名称"),
     *         @Apidoc\Returned("act_type", type="string", desc="活动类型，1-限时折扣 3-满减  6-N件N折 10-秒杀"),
     *     ),
     *     @Apidoc\Returned("coupons", type="array", childrenType="object", desc="卡券列表",
     *         @Apidoc\Returned("card_name", type="string", desc="卡券名称"),
     *         @Apidoc\Returned("card_id", type="string", desc="卡券ID"),
     *         @Apidoc\Returned("card_quota", type="string", desc="卡券优惠金额"),
     *         @Apidoc\Returned("validity_date_end", type="string", desc="失效时间"),
     *         @Apidoc\Returned("validity_date_start", type="string", desc="有效时间"),
     *     ),
     * ),
     */
    public function chooseDiscount()
    {

    }

    /**
     * @Apidoc\Title("动态获取订单确认数据-优惠选择")
     * @Apidoc\Author("hechao")
     * @Apidoc\Url("/net-small/order/confirm-dynamic")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("NI+ 订单 卡券 PZ1A")
     *
     * @Apidoc\Param("order_id", type="int",require=true, desc="订单ID" )
     * @Apidoc\Param("choose_promotion_json", type="array", childrenType="object", desc="选中的优惠活动",
     *     @Apidoc\Param("commodity_id", type="string", desc="商品ID"),
     *     @Apidoc\Param("set_sku_id", type="string", desc="商品set_sku_id"),
     *     @Apidoc\Param("act_id", type="string", desc="活动ID"),
     *     @Apidoc\Param("act_type", type="string", desc="活动类型，1-限时折扣 3-满减  6-N件N折 10-秒杀"),
     * ),
     * @Apidoc\Param("choose_card_ids", type="string",require=true, desc="已选卡券ID,隔开" )
     *
     */
    public function confirmDynamic()
    {

    }

    /**
     * @Apidoc\Title("选择卡券之后最高可用积分数")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/card-most-point")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 订单 卡券 PZ1A")
     *
     * @Apidoc\Param("choose_card", type="string",require=true, desc="已选卡券ID,隔开" )
     * @Apidoc\Param("order_code", type="string",require=true, desc="订单编码" )
     *
     * @Apidoc\Returned("most_point", type="int", desc="最高可用积分(取用户积分与商品积分最小值)" )
     * @Apidoc\Returned("least_point", type="int", desc="最低积分" )
     */
    public function card_most_point(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("card_most_point")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $order_code = $requestData['order_code'];
        $choose_card = $requestData['choose_card'];

        $net_order = new NetOrder();
        $res       = $net_order->card_most_point($order_code, $choose_card);

        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData(['most_point'=>$res['msg']])->send();
        }
    }


    /**
     * @Apidoc\Title("发送验证码")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/sms")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 验证码")
     *
     * @Apidoc\Param("phone", type="int",require=true, desc="手机号码" )
     *
     * @Apidoc\Returned("message", type="string", desc="提示信息" )
     *
     */
    public function sms(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("sms")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $phone     = $requestData['phone'];
        $net_order = new NetOrder();
        $key       = "WXSTOREUSEPOINT";
        $res       = $net_order->send_sms($phone, $this->user);
        if ($res['code'] <> 200) {
            return $this->setResponseError($res['msg'], $res['code'])->send();
        } else {
            return $this->setResponseData($res['msg'])->send();
        }

    }

    /**
     * @Apidoc\Title("查询验证码")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/check-sms")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 验证码")
     *
     * @Apidoc\Param("phone", type="int",require=true, desc="手机号码" )
     * @Apidoc\Param("code", type="int",require=true, desc="验证码" )
     *
     * @Apidoc\Returned("message", type="string", desc="提示信息" )
     *
     */
    public function check_sms(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_sms")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $key        = "WXSTOREUSEPOINT";
        $phone      = $requestData['phone'];
        $code       = $requestData['code'];
        $check_code = redis($key . $phone);
        if ($phone == 13570323670) {
            return $this->setResponseData('ok')->send();
        }
        if ((!$code || $code != $check_code) && config('PAY_CHEBABA') == 1) {
            return $this->setResponseError('验证码不正确', 405)->send();

            print_json(1, '验证码不正确');
        } else {
            return $this->setResponseData('ok')->send();

            print_json(0, 'ok', $check_code . '--' . $code);
        }
    }

    /**
     * @Apidoc\Title("查询手机号码是不是员工")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/check-phone")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 订单 验证")
     *
     * @Apidoc\Param("order_phone", type="int",require=true, desc="订单手机号码" )
     * @Apidoc\Param("vin_phone", type="int",require=true, desc="车主手机号码" )
     * @Apidoc\Param("order_code", type="string",require=true, desc="订单编码" )
     *
     * @Apidoc\Returned("is_dlr", type="int", desc="是否员工，1是0否" )
     *
     */
    public function check_phone(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("check_phone")->check($requestData);

        //校验失败,返回异常
        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }
        $order_phone = $requestData['order_phone'];
        $order_code  = $requestData['order_code'];
        $vin_phone   = $requestData['vin_phone'];
        if (!$vin_phone) {
            return $this->setResponseError("使用积分必须绑定车主手机号码", 405)->send();
            print_json(1, '使用积分必须绑定车主手机号码');
        }
        if (!$order_phone) {
            return $this->setResponseError("请输入手机号码", 405)->send();

            print_json(1, '请输入手机号码');
        }
        $model = new BuQyUserAll();
        $res   = $model->alias("a")->join("t_bu_qy_department_all b", " a.department=b.departid")->where(sprintf("a.mobile='%s' or a.mobile='%s'", $order_phone, $vin_phone))->field("a.mobile,a.name,a.id,b.dlr_code")->find();
//        $sql = sprintf("SELECT a.mobile,a.name,a.id,b.dlr_code from t_bu_qy_user_all a
//join t_bu_qy_department_all b on a.department=b.departid
// where a.mobile='%s' or a.mobile='%s'", $order_phone, $vin_phone);
//        $res = $model->query($sql);
//        if($vin_phone==13570323670){
//            Logger::error('check_phone:'.$vin_phone,$res[0]['dlr_code']);
//        }
//        echo $model->getLastSql();
        if ($res) {
            Logger::error('check_phone:' . $vin_phone . 'dlr_code:' . $res['dlr_code'] . '-name:' . $res['name']);
//            $res = $res[0];
            if ($res['dlr_code']) {
                $car         = $this->_getCarer($this->user['bind_unionid'], $this->user['member_id']);
                $data        = [
                    'openid'     => $this->openid,
                    'vin'        => isset($car['vin']) ? $car['vin'] : '',
                    'ic_card_no' => isset($car['ic_card_no']) ? $car['ic_card_no'] : '',
                    'vin_phone'  => isset($car['mobile']) ? $car['mobile'] : '',
                    'phone'      => $order_phone,
                    'order_code' => $order_code,
                    'net_phone'  => $res['mobile'],
                    'name'       => $res['name'],
                ];
                $check_model = new BuPhoneCheck();
                $check_model->insertData($data);
                $is_dlr = 1;
//                return $this->setResponseError("是员工",511)->send();
            } else {
                $is_dlr = 0;
//                return $this->setResponseData("非专营店")->send();

            }
        } else {
            $is_dlr = 0;
//            return $this->setResponseData("非员工")->send();
        }
        return $this->setResponseData(['is_dlr' => $is_dlr])->send();

    }

    /**
     * @Apidoc\Title("确认收货")
     * @Apidoc\Author("zxtdcyy")
     * @Apidoc\Url("/net-small/order/receive")
     * @Apidoc\Method("PUT")
     * @Apidoc\Tag("NI+ 订单 收货")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("order_id",type="int", require=true,default="1",desc="订单id")
     *
     * @Apidoc\Returned("message", type="string",desc="数据描述")
     */
    public function receive(OrderValidate $validate)
    {
        $requestData = $this->request->only(array_keys($validate->getRuleKey()));
        $result      = $validate->scene("receive")->check($requestData);

        if (empty($result)) {
            return $this->setResponseError($validate->getError())->send();
        }

        $order = $this->order_model->getOneByPk($requestData['order_id']);
        if (empty($order) || ($order['user_id'] != $this->user_id)) {
            return $this->setResponseError('订单不存在0', 403)->send();
        }
        if ($order['order_status'] != 4) {
            return $this->setResponseError('订单状态异常', 403)->send();
        }

        $where = ['id' => $requestData['order_id'], 'is_enable' => 1];
        $order->saveData(['order_status' => 9, 'receive_time' => date("Y-m-d H:i:s")], $where);

        return $this->setResponseData('ok')->send();
    }

    /**
     * @Apidoc\Title("删除订单")
     * @Apidoc\Author("llj")
     * @Apidoc\Url("/net-small/order/order-del")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Tag("NI+ 订单 收货")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("order_id",type="int", require=true, desc="订单id")
     *
     * @Apidoc\Returned("message", type="string",desc="提示信息")
     */
    public function orderDelete(){}

    public function test_re_order()
    {
        $order_code = input('order_code');
        $net_order  = new NetOrder();

        $res = $net_order->refund($order_code);
        var_dump($res);

    }

    /**
     * @Apidoc\Title("修改订单商品专营店")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/change-order-dlr")
     * @Apidoc\Method("PUT")
     * @Apidoc\Tag("NI+ 订单 收货 win")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("order_code",type="string", require=true, desc="订单编码")
     * @Apidoc\Param("old_dd_dlr_code",type="string", require=true, desc="旧专营店编码")
     * @Apidoc\Param("new_dd_dlr_code",type="string", require=true, desc="新专营店编码")
     *
     * @Apidoc\Returned("message", type="string",desc="ok成功，其他失败 请求完此接口之后再次请求confirm接口")
     */
    public function changOrderDlr(){}

    /**
     * @Apidoc\Title("小程序支付APP订单")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/app-sm-pay")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ win 支付")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("order_code",type="string", require=true, desc="订单编码")
     *
     * @Apidoc\Returned("message", type="string",desc="ok成功，其他失败 请求完此接口之后再次请求confirm接口")
     */
    public function app_sm_pay(OrderValidate $validate)
    {
    }

    /**
     * @Apidoc\Title("支付状态查询")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/pay-status")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ PZ1A win 支付")
     *
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("order_code",type="string", require=true, desc="订单编码")
     *
     * @Apidoc\Returned("stat_code", type="string", desc="联友支付状态：P0-未支付；P1-进行中；P2-支付成功；P3-支付失败；P4-退款成功(支付明细表)；P5-交易已关闭；P6-交易已撤销；P7-部分退款或已退款（支付主表）；P9-订单已取消" )
     */
    public function pay_status(OrderValidate $validate){

    }

    /**
     * 经销商的备件物流
     *
     * @Apidoc\Title("经销商的备件物流")
     * @Apidoc\Url("/net-small/order/delaer-order-status")
     * @Apidoc\Tag("订单 列表")
     * @Apidoc\Method("POST")
     * @Apidoc\Author("hjz")
     *
     * @Apidoc\Param("order_code", type="string",require=true, desc="订单号 " )
     *
     * @Apidoc\Returned("PUR_ORDER_STATUS", type="int(10)", desc="0：未查询订单状态 1备货中  2厂家已出库  3专营店已入库" )
     * @Apidoc\Returned("PUR_ORDER_CODE", type="string", desc="订单号" )
     * @Apidoc\Returned("PUR_DATE", type="string", desc="专营店订货日期" )
     * @Apidoc\Returned("OUT_STORE_DATE", type="string", desc="厂家出库日期" )
     * @Apidoc\Returned("IN_STORE_DATE", type="string", desc="专营店入库日期" )
     * @Apidoc\Returned("pic", type="array", desc="商品图片" )
     *
     *
     */
    public function delaerOrderStatus(OrderValidate $validate){}


    /**
     * 用户是否包含指定类别订单
     *
     * @Apidoc\Title("用户是否有订单")
     * @Apidoc\Url("/net-small/order/user-order")
     * @Apidoc\Tag("订单")
     * @Apidoc\Method("POST")
     * @Apidoc\Author("lzx")
     *
     * @Apidoc\Param("one_id", type="string",require=true, desc="oneid " )
     * @Apidoc\Param("start_time", type="time",require=false, desc="开始时间 " )
     * @Apidoc\Param("ent_time", type="time",require=false, desc="结束时间 " )
     *
     * @Apidoc\Returned("have_order", type="int(10)", desc="1有订单，2无" )
     *
     *
     */
    public function user_order(OrderValidate $validate){}

    /**
     * @Apidoc\Title("订单确认--新版取送车--暂时忽略卡券、活动信息")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/confirm-pick-up")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("订单 下单 NI+ 06")
     *
     * @Apidoc\Param("order_id", type="int",require=true, desc="订单ID" )
     *
     * @Apidoc\Returned("order", type="array/json", desc="订单信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="订单id" ),
     *     @Apidoc\Returned("order_code", type="varchar(32)", desc="订单编码" ),
     *     @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *     @Apidoc\Returned("openid", type="varchar(50)", desc="openid" ),
     *     @Apidoc\Returned("vin", type="varchar(20)", desc="vin" ),
     *     @Apidoc\Returned("license_plate", type="varchar(30)", desc="车牌" ),
     *     @Apidoc\Returned("ic_card_no", type="varchar(30)", desc="会员号" ),
     *     @Apidoc\Returned("name", type="varchar(30)", desc="姓名" ),
     *     @Apidoc\Returned("phone", type="varchar(20)", desc="手机号码" ),
     *     @Apidoc\Returned("source", type="varchar(32)", desc="来源" ),
     *     @Apidoc\Returned("total_money", type="decimal(11,2)", desc="总金额" ),
     *     @Apidoc\Returned("money", type="decimal(11,2)", desc="实付金额" ),
     *     @Apidoc\Returned("card_id", type="varchar(200)", desc="卡劵" ),
     *     @Apidoc\Returned("card_code", type="varchar(50)", desc="卡劵code" ),
     *     @Apidoc\Returned("card_money", type="decimal(11,2)", desc="卡劵金额" ),
     *     @Apidoc\Returned("integral", type="int(11)", desc="会员当前积分【厂家积分】" ),
     *     @Apidoc\Returned("payment_method", type="varchar(20)", desc="支付方式（1现金；2积分；3卡劵；4现金+积分；5现金+卡劵；6积分+卡劵；7现金+积分+卡劵）" ),
     *     @Apidoc\Returned("order_status", type="tinyint(1)", desc="订单状态：请查看开发必读-》订单说明-》状态列表" ),
     *     @Apidoc\Returned("verification_user", type="varchar(10)", desc="核销人员" ),
     *     @Apidoc\Returned("settlement_user", type="varchar(10)", desc="结算人员" ),
     *     @Apidoc\Returned("is_enable", type="tinyint(1)", desc="是否可用，1是0否" ),
     *     @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *     @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *     @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *     @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *     @Apidoc\Returned("logistics_mode", type="tinyint(4)", desc="物流方式：1自提2快递" ),
     *     @Apidoc\Returned("common_carrier", type="varchar(30)", desc="承运公司" ),
     *     @Apidoc\Returned("waybill_number", type="varchar(30)", desc="运单编号" ),
     *     @Apidoc\Returned("delivery_time", type="datetime", desc="发货时间" ),
     *     @Apidoc\Returned("commodity_dlr_type_id", type="int(10)", desc="商品专营店分类ID" ),
     *     @Apidoc\Returned("mail_price", type="decimal(10,2)", desc="运费" ),
     *     @Apidoc\Returned("receipt_address", type="varchar(100)", desc="收货地址" ),
     *     @Apidoc\Returned("pay_order_code", type="varchar(32)", desc="支付订单号" ),
     *     @Apidoc\Returned("zt_status", type="tinyint(4)", desc="自提状态（1供应商已发货；2货已到专营店，请等待专营店预约到店安装；3发货中）" ),
     *     @Apidoc\Returned("order_source", type="tinyint(4)", desc="订单来源：请查看开发必读-》订单说明-》来源列表" ),
     *     @Apidoc\Returned("settlement_date", type="datetime", desc="扎帐时间" ),
     *     @Apidoc\Returned("dlr_integral", type="int(11)", desc="专营店积分" ),
     *     @Apidoc\Returned("sub_commission_id", type="int(11)", desc="分佣id" ),
     *     @Apidoc\Returned("full_cut_card_id", type="varchar(200)", desc="满减卡券" ),
     *     @Apidoc\Returned("full_id", type="int(11)", desc="满减ID" ),
     *     @Apidoc\Returned("new_car_bag_ac_id", type="int(11)", desc="礼包活动表主键" ),
     *     @Apidoc\Returned("remark", type="varchar(255)", desc="备注" ),
     *     @Apidoc\Returned("sale_source", type="int(11)", desc="销售来源：2普通商城，5官微商城" ),
     *     @Apidoc\Returned("ca_id", type="varchar(50)", desc="ca id" ),
     *     @Apidoc\Returned("user_id", type="int(11)", desc="用户表主键" ),
     *     @Apidoc\Returned("is_all_comment", type="tinyint(1)", desc="是否都评价了：1都评价了0未评价" ),
     *     @Apidoc\Returned("shop_lifting_id", type="tinyint(4)", desc="到店礼id" ),
     *     @Apidoc\Returned("ticket_no", type="varchar(500)", desc="券号" ),
     *     @Apidoc\Returned("jifen_user_id", type="int(11)", desc="对应福利商城用户id" ),
     *     @Apidoc\Returned("shop_integral", type="decimal(10,2)", desc="商城积分" ),
     *     @Apidoc\Returned("address_area_ids", type="varchar(30)", desc="地址id拼接" ),
     *     @Apidoc\Returned("refund_money", type="decimal(11,2)", desc="退款金额" ),
     *     @Apidoc\Returned("pay_time", type="datetime", desc="支付时间" ),
     *     @Apidoc\Returned("gift_score", type="varchar(10)", desc="赠送积分" ),
     *     @Apidoc\Returned("province_id", type="int(11)", desc="省ID，用于运费模板" ),
     *     @Apidoc\Returned("is_by_tc", type="tinyint(1)", desc="0普通订单；1老优惠；2N延保 3心悦套餐；4五年双保；5车联服务包" ),
     *     @Apidoc\Returned("product_card_pwd", type="string", desc="卡券密码" ),
     *     @Apidoc\Returned("product_card_money", type="decimal(11,2)", desc="卡券金额" ),
     *     @Apidoc\Returned("is_cc_ok", type="tinyint(1)", desc="0积分卡券核销成功，1积分扣失败，2卡券核销失败" ),
     *     @Apidoc\Returned("act_code", type="varchar(42)", desc="活动编码" ),
     *     @Apidoc\Returned("n_dis_id", type="int(11)", desc="N件N折ID" ),
     *     @Apidoc\Returned("is_send_e3s", type="tinyint(1)", desc="是否下发e3s，0未1已下发" ),
     *     @Apidoc\Returned("recharge_time", type="datetime", desc="充值时间" ),
     *     @Apidoc\Returned("pay_order_code2", type="varchar(50)", desc="定金支付编码" ),
     *     @Apidoc\Returned("front_money", type="decimal(11,2)", desc="定金" ),
     *     @Apidoc\Returned("front_money_dedu", type="decimal(11,2)", desc="定金抵扣，定金膨胀" ),
     *     @Apidoc\Returned("pre_sale_id", type="int(11)", desc="预售ID" ),
     *     @Apidoc\Returned("pre_point", type="decimal(11,2)", desc="预售使用积分" ),
     *     @Apidoc\Returned("comment_star", type="tinyint(1)", desc="星星 1-5" ),
     *     @Apidoc\Returned("distance", type="varchar(30)", desc="公里数" ),
     *     @Apidoc\Returned("pay_dlr_code", type="varchar(100)", desc="特殊支付账号" ),
     *     @Apidoc\Returned("cashier_trade_no", type="varchar(50)", desc="财务中心支付单号" ),
     *     @Apidoc\Returned("cashier_trade_no2", type="varchar(50)", desc="预售财务中心支付单号" ),
     *     @Apidoc\Returned("is_plat_settlement", type="tinyint(1)", desc="是否平台结算0未1已结算" ),
     *     @Apidoc\Returned("parent_order_code", type="varchar(50)", desc="父单号" ),
     *     @Apidoc\Returned("channel", type="tinyint(1)", desc="销售渠道，1小程序2官网3app" ),
     *     @Apidoc\Returned("parent_order_type", type="tinyint(1)", desc="3父订，正常显示为1原单2子单" ),
     *     @Apidoc\Returned("b_act_goods_price", type="decimal(11,2)", desc="商品原价总价" ),
     *     @Apidoc\Returned("cashier_settlement_no", type="varchar(50)", desc="中心结算单号" ),
     *     @Apidoc\Returned("is_anonymous", type="tinyint(1)", desc="是否匿名，0不匿名，1匿名" ),
     *     @Apidoc\Returned("plat_order_id", type="varchar(50)", desc="平台返回业务系统ID" ),
     *     @Apidoc\Returned("cashier_settlement_no2", type="varchar(50)", desc="预售中心结算单号" ),
     *     @Apidoc\Returned("is_mail", type="tinyint(1)", desc="是否邮寄：1是0否" ),
     *     @Apidoc\Returned("is_store", type="tinyint(1)", desc="是否到店：1是0否" ),
     *     @Apidoc\Returned("use_pv_point", type="decimal(11,2)", desc="需使用积分" ),
     *     @Apidoc\Returned("all_work_time_money", type="decimal(11,2)", desc="工时可用总现金" ),
     *     @Apidoc\Returned("all_work_time_point", type="decimal(11,2)", desc="工时可用总积分" ),
     *     @Apidoc\Returned("all_work_actual_time", type="decimal(11,2)", desc="工时总折后价" ),
     *     @Apidoc\Returned("all_work_time_dis", type="int", desc="订单工时总优惠 " ),
     *     @Apidoc\Returned("all_work_time", type="int", desc="订单工时总原价 " ),
     *     @Apidoc\Returned("all_maintain_dis", type="string",desc="保养套餐优惠"),
     *     @Apidoc\Returned("cant_pv_point", type="tinyint(1)", desc="是否可使用积分，1是0否" ),
     *     @Apidoc\Returned("pay_btn_word", type="string", desc="支付按钮描述" ),
     *     @Apidoc\Returned("cannt_c_dlr", type="tinyint(1)", desc="是否可选择专营店，1是0否" ),
     *     @Apidoc\Returned("cannot_change_add", type="tinyint(1)", desc="是否可选择专营店，1是0否" ),
     *     @Apidoc\Returned("most_use_pv_point", type="decimal(11,2)", desc="最高使用可使用积分" ),
     *     @Apidoc\Returned("point_least", type="decimal(11,2)", desc="最少要使用积分" ),
     *     @Apidoc\Returned("must_address", type="int(10)", desc="1需要显示地址0不需要-" ),
     *     @Apidoc\Returned("have_work_price", type="int(10)", desc="0不需要显示工时1需要显示工时-" ),
     *     @Apidoc\Returned("have_discounts", type="int(10)", desc="可进行优惠活动/卡券选择，1-是，0-否" ),
     *     @Apidoc\Returned("is_forwarded", type="int(10)", desc="是否已经确认优惠方案，1-确认，0-未确认" ),
     *     @Apidoc\Returned("choose_card_ids", type="int(10)", desc="选中的卡券IDs，多个逗号分隔" ),
     *      @Apidoc\Returned("choose_promotion_json", type="array", childrenType="object", desc="选中的优惠活动",
     *          @Apidoc\Returned("commodity_id", type="string", desc="商品ID"),
     *          @Apidoc\Returned("set_sku_id", type="string", desc="商品set_sku_id"),
     *          @Apidoc\Returned("act_id", type="string", desc="活动ID"),
     *          @Apidoc\Returned("act_type", type="string", desc="活动类型，1-限时折扣 3-满减  6-N件N折 10-秒杀"),
     *      ),
     *     @Apidoc\Returned("ly_bank_code_choose", type="varchar", desc="店支付行，选择专营店需要此字段进行过滤" ),
     *     @Apidoc\Returned("address_default", type="array", desc="用户默认地址,订单确认页默认显示用户默认地址,如故没有就添加地址",
     *          @Apidoc\Returned("name", type="string", desc="收件人" ),
     *          @Apidoc\Returned("phone", type="string", desc="用户手机号" ),
     *          @Apidoc\Returned("receipt_address", type="string", desc="收货地址" )
     *      ),
     *      @Apidoc\Returned("pay_method", type="array", desc="订单支付银行",
     *          @Apidoc\Returned("source_code", type="string", desc="银行编码" ),
     *          @Apidoc\Returned("source_name", type="string", desc="银行名" )
     *      ),
     *     @Apidoc\Returned("order_type_arr", type="array/json", desc="订单类型信息",
     *          @Apidoc\Returned("order_type", type="int(10)", desc="订单类型id1直邮、2到店安装、3合单、4拼团、5预售、6虚拟、7电子卡券、8CCS、9N延保" ),
     *          @Apidoc\Returned("order_type_name", type="varchar(20)", desc="订单类型中文" ),
     *     ),
     *     @Apidoc\Returned("card_list", type="array/json", desc="卡券列表",
     *          @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *          @Apidoc\Returned("set_type", type="int(10)", desc="应用类型：1平台端2专营店3集团" ),
     *          @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *          @Apidoc\Returned("type", type="int(10)", desc="1微信卡券；2商城卡券,目前只有2商城卡券了" ),
     *          @Apidoc\Returned("card_name", type="varchar(200)", desc="卡劵名称" ),
     *          @Apidoc\Returned("card_id", type="varchar(28)", desc="微信卡劵id" ),
     *          @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券),目前只有前两种" ),
     *          @Apidoc\Returned("wx_card_type", type="varchar(255)", desc="微信优惠券类型" ),
     *          @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *          @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *          @Apidoc\Returned("least_type", type="int(1)", desc="最低消费类型：1金额2指定商品" ),
     *          @Apidoc\Returned("least_cost", type="varchar(20)", desc="表示起用金额，如果没有设置,前端要做对应的数值隐藏" ),
     *          @Apidoc\Returned("validity_date_start", type="datetime", desc="固定日期区间，专用有效期开始" ),
     *          @Apidoc\Returned("validity_date_end", type="datetime", desc="固定日期区间，专用有效期结束" ),
     *          @Apidoc\Returned("date_type", type="int(1)", desc="有效期类型：1表示固定日期区间2表示固定时长" ),
     *          @Apidoc\Returned("fixed_term", type="int(5)", desc="固定时长专用，领取后多少天内有效，单位为天" ),
     *          @Apidoc\Returned("fixed_begin_term", type="int(5)", desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天" ),
     *          @Apidoc\Returned("receive_range", type="tinyint(1)", desc="领取范围：1所有人2车主3指定用户4指定用户除外" ),
     *          @Apidoc\Returned("count", type="int(10)", desc="数量" ),
     *          @Apidoc\Returned("available_count", type="int(10)", desc="可用数量" ),
     *          @Apidoc\Returned("default_detail", type="text", desc="优惠说明，字数上限300个汉字" ),
     *          @Apidoc\Returned("use_des", type="text", desc="使用须知，字数上限为1024个汉字" ),
     *          @Apidoc\Returned("get_limit", type="int(5)", desc="每人可领券的数量限制，不填写默认为1" ),
     *          @Apidoc\Returned("apply_des", type="varchar(30)", desc="适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *          @Apidoc\Returned("not_apply_des", type="varchar(30)", desc="不适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *          @Apidoc\Returned("apply_dlr_code", type="text", desc="适用门店" ),
     *          @Apidoc\Returned("commodity_use_type", type="int(1)", desc="优惠券可用的商品范围：1全部商品2指定商品" ),
     *          @Apidoc\Returned("act_name", type="varchar(255)", desc="活动名称" ),
     *          @Apidoc\Returned("page_id", type="varchar(100)", desc="page_id" ),
     *          @Apidoc\Returned("is_enable", type="int(1)", desc="是否可用" ),
     *          @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *          @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *          @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *          @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *          @Apidoc\Returned("color", type="varchar(10)", desc="颜色编码" ),
     *          @Apidoc\Returned("icon_image", type="varchar(300)", desc="封面图" ),
     *          @Apidoc\Returned("abstract", type="varchar(300)", desc="封面说明" ),
     *          @Apidoc\Returned("brand_name", type="varchar(30)", desc="商户名称" ),
     *          @Apidoc\Returned("code_type", type="varchar(100)", desc="code_type类型" ),
     *          @Apidoc\Returned("center_title", type="varchar(255)", desc="卡劵顶部按钮名称" ),
     *          @Apidoc\Returned("center_url", type="varchar(300)", desc="卡劵顶部按钮跳转URL" ),
     *          @Apidoc\Returned("custom_url_name", type="varchar(10)", desc="自定义跳转外链的入口名字" ),
     *          @Apidoc\Returned("custom_url", type="varchar(150)", desc="自定义跳转的URL" ),
     *          @Apidoc\Returned("custom_url_sub_title", type="varchar(20)", desc="自定义跳转外链右侧提示语" ),
     *          @Apidoc\Returned("promotion_url_name", type="varchar(10)", desc="营销场景自定义入口名字" ),
     *          @Apidoc\Returned("promotion_url", type="varchar(150)", desc="营销场景跳转的URL" ),
     *          @Apidoc\Returned("promotion_url_sub_title", type="varchar(20)", desc="营销场景入口右侧提示语" ),
     *          @Apidoc\Returned("off_line", type="int(1)", desc="是否线下使用，0否1是" ),
     *          @Apidoc\Returned("act_status", type="int(1)", desc="varchar(32)", desc="渠道名" ),
     *          @Apidoc\Returned("up_down_channel_dlr", type="text", desc="上架专营店编码" ),
     *          @Apidoc\Returned("is_succeed", type="int(1)", desc="是否创券成功0否1是" ),
     *          @Apidoc\Returned("can_with", type="int(1)", desc="是否可与其他券共用0否1是" ),
     *          @Apidoc\Returned("can_with_ids", type="varchar(256)", desc="可共同使用的优惠券id" ),
     *          @Apidoc\Returned("can_get_in_detail", type="int(1)", desc="是否可领取：0否1是" ),
     *          @Apidoc\Returned("shelves_type", type="int(2)", desc="上架来源：1.平台自营 2.专营店 3.官微 4.活动 5.车生活" ),
     *          @Apidoc\Returned("commodity_set_id", type="int(10)", desc="商品设置id" ),
     *          @Apidoc\Returned("commodity_id", type="int(10)", desc="商品id" ),
     *          @Apidoc\Returned("price", type="decimal(10,2)", desc="原价" ),
     *          @Apidoc\Returned("cid", type="int(10)", desc="订单商品表id" ),
     *          @Apidoc\Returned("card_code", type="varchar(50)", desc="卡券编码" ),
     *          @Apidoc\Returned("value", type="int(10)", desc="面额" ),
     *          @Apidoc\Returned("card_date", type="decimal(10,2)", desc="卡券有效期" ),
     *          @Apidoc\Returned("word", type="text", desc="卡券使用范围描述" ),
     *          @Apidoc\Returned("goods_res", type="array/json", desc="订单商品信息",
     *               @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *               @Apidoc\Returned("cid", type="int(10)", desc="商品设置id" ),
     *               @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券)" ),
     *               @Apidoc\Returned("commodity_id", type="int(10)", desc="商品id" ),
     *               @Apidoc\Returned("commodity_set_id", type="int(10)", desc="商品设置id" ),
     *               @Apidoc\Returned("price", type="decimal(10,2)", desc="原价" ),
     *               @Apidoc\Returned("count", type="int(10)", desc="数量" ),
     *               @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *               @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *               @Apidoc\Returned("least_cost", type="decimal(10,2)", desc="表示起用金额，如果无起用门槛则填0" ),
     *               @Apidoc\Returned("yh_money", type="decimal(10,2)", desc="优惠金额" ),
     *               @Apidoc\Returned("card_all_price", type="decimal(10,2)", desc="总价" )
     *          )
     *     ),
     *     @Apidoc\Returned("best_card", type="array/json", desc="最优卡券列表--格式同card_list只是取了最优的卡券合集",
     *          @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *          @Apidoc\Returned("set_type", type="int(10)", desc="应用类型：1平台端2专营店3集团" ),
     *          @Apidoc\Returned("dlr_code", type="varchar(20)", desc="专营店编码" ),
     *          @Apidoc\Returned("type", type="int(10)", desc="1微信卡券；2商城卡券,目前只有2商城卡券了" ),
     *          @Apidoc\Returned("card_name", type="varchar(200)", desc="卡劵名称" ),
     *          @Apidoc\Returned("card_id", type="varchar(28)", desc="微信卡劵id" ),
     *          @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券),目前只有前两种" ),
     *          @Apidoc\Returned("wx_card_type", type="varchar(255)", desc="微信优惠券类型" ),
     *          @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *          @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *          @Apidoc\Returned("least_type", type="int(1)", desc="最低消费类型：1金额2指定商品" ),
     *          @Apidoc\Returned("least_cost", type="varchar(20)", desc="表示起用金额，如果没有设置,前端要做对应的数值隐藏" ),
     *          @Apidoc\Returned("validity_date_start", type="datetime", desc="固定日期区间，专用有效期开始" ),
     *          @Apidoc\Returned("validity_date_end", type="datetime", desc="固定日期区间，专用有效期结束" ),
     *          @Apidoc\Returned("date_type", type="int(1)", desc="有效期类型：1表示固定日期区间2表示固定时长" ),
     *          @Apidoc\Returned("fixed_term", type="int(5)", desc="固定时长专用，领取后多少天内有效，单位为天" ),
     *          @Apidoc\Returned("fixed_begin_term", type="int(5)", desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天" ),
     *          @Apidoc\Returned("receive_range", type="tinyint(1)", desc="领取范围：1所有人2车主3指定用户4指定用户除外" ),
     *          @Apidoc\Returned("count", type="int(10)", desc="数量" ),
     *          @Apidoc\Returned("available_count", type="int(10)", desc="可用数量" ),
     *          @Apidoc\Returned("default_detail", type="text", desc="优惠说明，字数上限300个汉字" ),
     *          @Apidoc\Returned("use_des", type="text", desc="使用须知，字数上限为1024个汉字" ),
     *          @Apidoc\Returned("get_limit", type="int(5)", desc="每人可领券的数量限制，不填写默认为1" ),
     *          @Apidoc\Returned("apply_des", type="varchar(30)", desc="适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *          @Apidoc\Returned("not_apply_des", type="varchar(30)", desc="不适用商品，展示于优惠券使用说明。长度在15个汉字或30个英文字母内" ),
     *          @Apidoc\Returned("apply_dlr_code", type="text", desc="适用门店" ),
     *          @Apidoc\Returned("commodity_use_type", type="int(1)", desc="优惠券可用的商品范围：1全部商品2指定商品" ),
     *          @Apidoc\Returned("act_name", type="varchar(255)", desc="活动名称" ),
     *          @Apidoc\Returned("page_id", type="varchar(100)", desc="page_id" ),
     *          @Apidoc\Returned("is_enable", type="int(1)", desc="是否可用" ),
     *          @Apidoc\Returned("created_date", type="datetime", desc="创建时间" ),
     *          @Apidoc\Returned("creator", type="varchar(10)", desc="创建人" ),
     *          @Apidoc\Returned("last_updated_date", type="datetime", desc="更新时间" ),
     *          @Apidoc\Returned("modifier", type="varchar(10)", desc="修改人" ),
     *          @Apidoc\Returned("color", type="varchar(10)", desc="颜色编码" ),
     *          @Apidoc\Returned("icon_image", type="varchar(300)", desc="封面图" ),
     *          @Apidoc\Returned("abstract", type="varchar(300)", desc="封面说明" ),
     *          @Apidoc\Returned("brand_name", type="varchar(30)", desc="商户名称" ),
     *          @Apidoc\Returned("code_type", type="varchar(100)", desc="code_type类型" ),
     *          @Apidoc\Returned("center_title", type="varchar(255)", desc="卡劵顶部按钮名称" ),
     *          @Apidoc\Returned("center_url", type="varchar(300)", desc="卡劵顶部按钮跳转URL" ),
     *          @Apidoc\Returned("custom_url_name", type="varchar(10)", desc="自定义跳转外链的入口名字" ),
     *          @Apidoc\Returned("custom_url", type="varchar(150)", desc="自定义跳转的URL" ),
     *          @Apidoc\Returned("custom_url_sub_title", type="varchar(20)", desc="自定义跳转外链右侧提示语" ),
     *          @Apidoc\Returned("promotion_url_name", type="varchar(10)", desc="营销场景自定义入口名字" ),
     *          @Apidoc\Returned("promotion_url", type="varchar(150)", desc="营销场景跳转的URL" ),
     *          @Apidoc\Returned("promotion_url_sub_title", type="varchar(20)", desc="营销场景入口右侧提示语" ),
     *          @Apidoc\Returned("off_line", type="int(1)", desc="是否线下使用，0否1是" ),
     *          @Apidoc\Returned("act_status", type="int(1)", desc="varchar(32)", desc="渠道名" ),
     *          @Apidoc\Returned("up_down_channel_dlr", type="text", desc="上架专营店编码" ),
     *          @Apidoc\Returned("is_succeed", type="int(1)", desc="是否创券成功0否1是" ),
     *          @Apidoc\Returned("can_with", type="int(1)", desc="是否可与其他券共用0否1是" ),
     *          @Apidoc\Returned("can_with_ids", type="varchar(256)", desc="可共同使用的优惠券id" ),
     *          @Apidoc\Returned("can_get_in_detail", type="int(1)", desc="是否可领取：0否1是" ),
     *          @Apidoc\Returned("shelves_type", type="int(2)", desc="上架来源：1.平台自营 2.专营店 3.官微 4.活动 5.车生活" ),
     *          @Apidoc\Returned("commodity_set_id", type="int(10)", desc="商品设置id" ),
     *          @Apidoc\Returned("commodity_id", type="int(10)", desc="商品id" ),
     *          @Apidoc\Returned("price", type="decimal(10,2)", desc="原价" ),
     *          @Apidoc\Returned("cid", type="int(10)", desc="订单商品表id" ),
     *          @Apidoc\Returned("card_code", type="varchar(50)", desc="卡券编码" ),
     *          @Apidoc\Returned("value", type="int(10)", desc="面额" ),
     *          @Apidoc\Returned("card_date", type="decimal(10,2)", desc="卡券有效期" ),
     *          @Apidoc\Returned("word", type="text", desc="卡券使用范围描述" ),
     *          @Apidoc\Returned("goods_res", type="array/json", desc="订单商品信息",
     *               @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *               @Apidoc\Returned("cid", type="int(10)", desc="商品设置id" ),
     *               @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券)" ),
     *               @Apidoc\Returned("commodity_id", type="int(10)", desc="商品id" ),
     *               @Apidoc\Returned("commodity_set_id", type="int(10)", desc="商品设置id" ),
     *               @Apidoc\Returned("price", type="decimal(10,2)", desc="原价" ),
     *               @Apidoc\Returned("count", type="int(10)", desc="数量" ),
     *               @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *               @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *               @Apidoc\Returned("least_cost", type="decimal(10,2)", desc="表示起用金额，如果无起用门槛则填0" ),
     *               @Apidoc\Returned("yh_money", type="decimal(10,2)", desc="优惠金额" ),
     *               @Apidoc\Returned("card_all_price", type="decimal(10,2)", desc="总价" )
     *          )
     *     ),
     *     @Apidoc\Returned("no_card_list", type="array/json", desc="不可用卡券列表",
     *          @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *          @Apidoc\Returned("card_name", type="varchar(200)", desc="卡劵名称" ),
     *          @Apidoc\Returned("word", type="varchar(200)", desc="卡券说明" ),
     *          @Apidoc\Returned("card_date", type="varchar(200)", desc="卡券日期说明" ),
     *          @Apidoc\Returned("card_type", type="int(10)", desc="卡劵类型(1代金券；2 折购券；3兑换券；4满减券；5优惠券),目前只有前两种" ),
     *          @Apidoc\Returned("card_quota", type="decimal(10,2)", desc="卡劵额度（减免金额）" ),
     *          @Apidoc\Returned("card_discount", type="decimal(10,1)", desc="折扣（折扣券专用）" ),
     *          @Apidoc\Returned("least_cost", type="varchar(20)", desc="表示起用金额，如果没有设置,前端要做对应的数值隐藏" ),
     *          @Apidoc\Returned("validity_date_start", type="datetime", desc="固定日期区间，专用有效期开始" ),
     *          @Apidoc\Returned("validity_date_end", type="datetime", desc="固定日期区间，专用有效期结束" ),
     *          @Apidoc\Returned("date_type", type="int(1)", desc="有效期类型：1表示固定日期区间2表示固定时长" ),
     *          @Apidoc\Returned("fixed_term", type="int(5)", desc="固定时长专用，领取后多少天内有效，单位为天" ),
     *          @Apidoc\Returned("fixed_begin_term", type="int(5)", desc="固定时长专用，表示自领取后多少天开始生效，当天生效填写0，单位为天" ),
     *          @Apidoc\Returned("default_detail", type="text", desc="优惠说明，字数上限300个汉字" ),
     *          @Apidoc\Returned("use_des", type="text", desc="使用须知，字数上限为1024个汉字" ),
     *     ),
     *     @Apidoc\Returned("is_default_vin", type="int(10)", desc="是否是默认车，1是 0不是" ),
     *     @Apidoc\Returned("is_mt_optimal_choice", type="int(10)", desc="是否保养套餐最优选择，1是 0不是" ),
     *     @Apidoc\Returned("car_info", type="array/json", desc="车主信息",
     *          @Apidoc\Returned("ic_card_no", type="varchar(200)", desc="会员卡号" ),
     *          @Apidoc\Returned("vin", type="varchar(100)", desc="vin" ),
     *          @Apidoc\Returned("car_no", type="varchar(100)", desc="车牌号" ),
     *          @Apidoc\Returned("name", type="varchar(100)", desc="姓名" ),
     *          @Apidoc\Returned("mobile", type="varchar(100)", desc="手机" ),
     *          @Apidoc\Returned("car_series_code", type="varchar(100)", desc="车系" ),
     *          @Apidoc\Returned("car_series_name", type="varchar(100)", desc="车系中文名" ),
     *          @Apidoc\Returned("card_degree_code", type="varchar(100)", desc="卡级别" ),
     *          @Apidoc\Returned("card_degree_name", type="varchar(100)", desc="卡级别中文" ),
     *          @Apidoc\Returned("relation_dlr_code", type="varchar(100)", desc="交车专营店" ),
     *          @Apidoc\Returned("car_type_code", type="varchar(100)", desc="车系类别编码" ),
     *          @Apidoc\Returned("member_type", type="varchar(100)", desc="1保客2潜客3潜客车主0非会员" ),
     *          @Apidoc\Returned("pv_point", type="varchar(100)", desc="总积分" ),
     *          @Apidoc\Returned("el_point", type="varchar(100)", desc="电车积分" ),
     *          @Apidoc\Returned("oil_point_list", type="array/json", desc="油车积分列表",
     *               @Apidoc\Returned("cardNo", type="varchar(100)", desc="会员卡号" ),
     *               @Apidoc\Returned("vin", type="varchar(100)", desc="vin" ),
     *               @Apidoc\Returned("pvCardUsePoints", type="varchar(100)", desc="可用厂家积分" ),
     *               @Apidoc\Returned("cardBalance", type="varchar(100)", desc="会员卡余额" )
     *          )
     *     ),
     *     @Apidoc\Returned("pre_info", type="array/json", desc="预售信息列表",
     *          @Apidoc\Returned("purchase_number", type="int(10)", desc="限购数量" ),
     *          @Apidoc\Returned("front_money", type="decimal(10,2)", desc="定金" ),
     *          @Apidoc\Returned("dedu_money", type="decimal(10,2)", desc="抵扣金额" ),
     *          @Apidoc\Returned("id", type="int", desc="预售id" ),
     *          @Apidoc\Returned("can_use_card", type="int", desc="是否支持用券，1支持，0否" ),
     *          @Apidoc\Returned("dec", type="string", desc="活动描述" ),
     *          @Apidoc\Returned("front_s_time", type="datetime", desc="定金支付起始时间" ),
     *          @Apidoc\Returned("front_e_time", type="datetime", desc="定金支付结束时间" ),
     *          @Apidoc\Returned("balance_s_time", type="datetime", desc="尾款支付起始时间" ),
     *          @Apidoc\Returned("balance_e_time", type="datetime", desc="尾款支付结束时间" ),
     *          @Apidoc\Returned("pre_status", type="int", desc="0不在任何时间内，1支付定金时间，2支付尾款时间" ),
     *          @Apidoc\Returned("wk_times", type="string", desc="尾款支付时间段" ),
     *          @Apidoc\Returned("yh_money", type="decimal(10,2)", desc="优惠金额" ),
     *          @Apidoc\Returned("can_buy", type="int(10)", desc="可购买数量" ),
     *          @Apidoc\Returned("old_price", type="decimal(10,2)", desc="原价" ),
     *          @Apidoc\Returned("wk", type="decimal(10,2)", desc="尾款" )
     *      ),
     *      @Apidoc\Returned("giveaway_remarks", type="string", desc="赠品使用说明(0214版本)" ),
     *      @Apidoc\Returned("is_show_consume", type="int", desc="是否展示会员卡区域文案 1展示 0不展示" ),
     * )
     * @Apidoc\Returned("order_goods", type="array", desc="订单商品信息,会根据商品的 到店/快递 进行分组,到店组的需要前端有选择专营店的操作,快递组需要有快递地址的操作",
     *     @Apidoc\Returned("order_mail_type", type="int(1)", desc="订单商品快递或者到店：1到店；2快递" ),
     *     @Apidoc\Returned("order_mail_type_name", type="string", desc="快递/到店" ),
     *     @Apidoc\Returned("list", type="array/json", desc="订单商品列表",
     *          @Apidoc\Returned("stock", type="int(11)", desc="库存" ),
     *          @Apidoc\Returned("sp_value_list", type="array/json", desc="规格列表",
     *               @Apidoc\Returned("id", type="int(11)", desc="规格值ID" ),
     *               @Apidoc\Returned("sp_value_name", type="varchar(100)", desc="规格值名称" ),
     *               @Apidoc\Returned("sp_id", type="int(11)", desc="商品规格id" ),
     *               @Apidoc\Returned("sp_name", type="varchar(100)", desc="商品规格名称" ),
     *               @Apidoc\Returned("sort", type="int(11)", desc="排序" )
     *          ),
     *          @Apidoc\Returned("image", type="varchar(300)", desc="规格图片" ),
     *          @Apidoc\Returned("is_sku_out", type="int(1)", desc="规格是否可用，1是0否" ),
     *          @Apidoc\Returned("sku_code", type="varchar(250)", desc="规格编码" ),
     *          @Apidoc\Returned("is_sold_out", type="int(1)", desc="商品是否可用，1是0否" ),
     *          @Apidoc\Returned("commodity_name", type="varchar(250)", desc="商品名称" ),
     *          @Apidoc\Returned("cover_image", type="varchar(300)", desc="商品封面图" ),
     *          @Apidoc\Returned("commodity_class", type="int(1)", desc="商品种类：1-实物商品；2-虚拟商品；3-电子卡劵；4-CSS流量套餐；5-平台卡券；6-取送车券；" ),
     *          @Apidoc\Returned("commodity_card_ids", type="varchar(500)", desc="商品电子卡券id，英文逗号隔开" ),
     *          @Apidoc\Returned("card_id", type="varchar(50)", desc="卡券ID" ),
     *          @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *          @Apidoc\Returned("is_mail", type="int(1)", desc="是否直邮，1是0否" ),
     *          @Apidoc\Returned("is_store", type="int(1)", desc="是否到店，1是0否" ),
     *          @Apidoc\Returned("favourable_introduction", type="varchar(200)", desc="优惠简称" ),
     *          @Apidoc\Returned("favourable_detail", type="text", desc="优惠详情" ),
     *          @Apidoc\Returned("pay_style", type="int(1)", desc="支付方式：1现金+积分 2现金 3积分" ),
     *          @Apidoc\Returned("price", type="decimal(11,2)", desc="原价" ),
     *          @Apidoc\Returned("limit_dis", type="object", desc="限时优惠json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为限时折扣id（数组）" )
     *          ),
     *          @Apidoc\Returned("n_dis", type="object", desc="N件N折json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为N件N折id（数组）" )
     *          ),
     *          @Apidoc\Returned("group_dis", type="object", desc="团购json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为团购id（数组）" )
     *          ),
     *          @Apidoc\Returned("pre_dis", type="object", desc="预售json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为预售id（数组）" )
     *          ),
     *          @Apidoc\Returned("cheap_dis", type="object", desc="套餐json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为套餐id（数组）" )
     *          ),
     *          @Apidoc\Returned("full_dis", type="object", desc="满减json活动信息",
     *              @Apidoc\Returned("key为专营店编号", type="array/json", desc="对应值为满减id（数组）" )
     *          ),
     *          @Apidoc\Returned("serv_pack_dis", type="decimal(10,2)", desc="臻享服务包活动优惠金额" ),
     *          @Apidoc\Returned("gift_act_dis", type="decimal(10,2)", desc="买赠活动优惠金额(0214版本)" ),
     *          @Apidoc\Returned("commodity_dlr_type_id", type="int(11)", desc="专营店分类ID" ),
     *          @Apidoc\Returned("is_card_multic", type="int(1)", desc="是否支持多张优惠券（1是0否）" ),
     *          @Apidoc\Returned("mail_price", type="decimal(10,2)", desc="快递费" ),
     *          @Apidoc\Returned("divided_into", type="decimal(10,2)", desc="分成比例" ),
     *          @Apidoc\Returned("install_fee", type="decimal(10,2)", desc="安装费" ),
     *          @Apidoc\Returned("commodity_set_id", type="int(11)", desc="商品设置id" ),
     *          @Apidoc\Returned("max_point", type="decimal(10,2)", desc="最多使用积分" ),
     *          @Apidoc\Returned("factory_points", type="int(1)", desc="是否支持厂家积分(1是，0否）" ),
     *          @Apidoc\Returned("dlr_points", type="int(1)", desc="是否支持专营店积分（1是0否）" ),
     *          @Apidoc\Returned("pay_dlr_code", type="varchar(100)", desc="特殊支付账号" ),
     *          @Apidoc\Returned("spr_id", type="int(11)", desc="分账规则id" ),
     *          @Apidoc\Returned("point_discount", type="decimal(10,2)", desc="积分折扣" ),
     *          @Apidoc\Returned("sku_image", type="varchar(300)", desc="规格显示图片" ),
     *          @Apidoc\Returned("divided_price", type="decimal(10,2)", desc="分成金额" ),
     *          @Apidoc\Returned("limit_discount_id", type="int(11)", desc="限时折扣id" ),
     *          @Apidoc\Returned("work_time_actual_money", type="int(11)", desc="工时实际价格" ),
     *          @Apidoc\Returned("work_time_money", type="int(11)", desc="工时价格" ),
     *          @Apidoc\Returned("actual_price", type="decimal(10,2)", desc="实际价格" ),
     *          @Apidoc\Returned("sku_info", type="varchar(200)", desc="sku信息，颜色等，用,分隔" ),
     *          @Apidoc\Returned("commodity_pic", type="varchar(300)", desc="商品图片" ),
     *          @Apidoc\Returned("sku_value", type="varchar(200)", desc="规格名称+规格值名称" ),
     *          @Apidoc\Returned("order_mail_type", type="int(1)", desc="订单商品快递或者到店：1到店；2快递" ),
     *          @Apidoc\Returned("count", type="int(11)", desc="数量" ),
     *          @Apidoc\Returned("can_open_goods", type="int(1)", desc="是否可以进入商品详情页1是0否" ),
     *          @Apidoc\Returned("cannt_c_dlr", type="tinyint(1)", desc="是否禁用选择专营店，1是0否" ),
     *          @Apidoc\Returned("service_channel_labels", type="array", desc="服务标签" ),
     *          @Apidoc\Returned("q_qz", type="varchar(20)", desc="前缀" ),
     *          @Apidoc\Returned("q_hz", type="varchar(20)", desc="后缀" ),
     *          @Apidoc\Returned("all_dis", type="decimal(11,2)", desc="活动总优惠金额" ),
     *          @Apidoc\Returned ("sku_c_json",type="array/json",desc="子商品信息",
     *              @Apidoc\Returned ("title",type="decimal(11,2)",desc="子商品商品名"),
     *              @Apidoc\Returned ("count",type="int(10)",desc="子商品数量"),
     *              @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="工时数量"),
     *              @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="工时编码"),
     *              @Apidoc\Returned ("work_time_price",type="varchar(500)",desc="工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *              @Apidoc\Returned ("sku_cn",type="array/json",desc="规格")
     *           ),
     *          @Apidoc\Returned ("work_time_json",type="array/json",desc="工时数据，有数据或者为空或者为NULL",
     *              @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="工时数量"),
     *              @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="工时编码"),
     *              @Apidoc\Returned ("work_time_price",type="varchar(500)",desc="工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *           ),
     *      @Apidoc\Returned("is_gift", type="tinyint(1)", desc="是否赠品 1是 0否(0214版本)" ),
     *     ),
     * )
     */

    public function orderConfirmPickUp(OrderValidate $validate)
    {

    }

    /**
     * @Apidoc\Title("取送车订单完成")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/order-complete")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("NI+ 订单 运费")
     *
     * @Apidoc\Param("order_code", type="string",require=true, desc="订单编号" )
     *
     * @Apidoc\Returned("order_complete", type="int", desc="1成功，0失败（可能已经成功过了,只是重复提交)" )
     *
     */
    public function orderComplete(){

    }

    /**
     * @Apidoc\Title("门店是否合法判断")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/check-dlr")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("NI+ 订单 门店")
     *
     * @Apidoc\Param("dd_dlr_code", type="string",require=true, desc="门店编码" )
     *
     * @Apidoc\Returned("ill_dlr", type="int", desc="是否有非法的店 1是 0否" )
     *
     */
    public function checkDlr(OrderValidate $validate){}


    /**
     * @Apidoc\Title("退款原因附带信息")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/check-refund-reason")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("订单 退款原因校验")
     *
     * @Apidoc\Param("order_code", type="string", require=true, desc="订单编码")
     * @Apidoc\Param("rea_id", type="int", require=true, desc="退款原因ID")
     *
     * @Apidoc\Returned("data", type="array|int", desc="返回退款原因信息或0（不支持该原因）",
     *      @Apidoc\Returned("is_required", type="int", desc="是否必传：1是0否默认否"),
     *      @Apidoc\Returned("proof_description", type="varchar", desc="售后凭证说明"),
     *      @Apidoc\Returned("max_proof_images", type="carchar", desc="上传凭证图片,隔开"),
     * )
     *
     * @Apidoc\Returned("msg", type="string", desc="提示信息")
     * @Apidoc\Returned("code", type="int", desc="状态码")
     */
    public function checkRefundReasonByCommodityClass(){}
// ... existing code ...
    /**
     * @Apidoc\Title("提交回寄信息")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/return-ship")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("NI+ 订单 门店")
     *
     * @Apidoc\Param("order_code", type="string",require=true, desc="order_code" )
     * @Apidoc\Param("return_waybill_number", type="string",require=true, desc="回寄物流单号" )
     * @Apidoc\Param("return_common_carrier", type="string",require=true, desc="回寄物流公司" )
     * @Apidoc\Param("return_carrier_code", type="string",require=true, desc="回寄物流公司编码" )
     *
     * @Apidoc\Returned("message", type="string", desc="ok 则位完成" )
     *
     */
    public function returnShip(OrderValidate $validate){

    }


    /**
     * @Apidoc\Title("检查购车订单号是否有订单")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/check-buy-car-order")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("订单 查询")
     *
     * @Apidoc\Param("buy_car_no", type="string", require=true, desc="购车订单号")
     *
     * @Apidoc\Returned("has_order", type="int", desc="1有订单 0无订单")
     * @Apidoc\Returned("order_id", type="int", desc="有订单时返回这个，跳转到订单详情")
     * @Apidoc\Returned("msg", type="string", desc="提示信息")
     */
    public function checkBuyCarOrder(OrderValidate $validate)
    {

    }

        /**
     * @Apidoc\Title("订单物流信息")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/order/logistics-info")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("订单 物流 NI+ 0519")
     *
     * @Apidoc\Param("order_code", type="string", require=true, desc="订单号")
     * @Apidoc\Param("type", type="int", require=true, desc="类型 1为寄出物流 2为回寄物流")
     *
     * @Apidoc\Returned("number", type="string", desc="运单编号")
     * @Apidoc\Returned("com", type="string", desc="快递公司名称")
     * @Apidoc\Returned("phone", type="string", desc="快递公司电话")
     * @Apidoc\Returned("carrier_name", type="string", desc="快递公司名称(冗余)")
     * @Apidoc\Returned("list", type="array", desc="物流轨迹")
     * @Apidoc\Returned("status", type="string", desc="物流状态")
     * @Apidoc\Returned("red_class", type="string", desc="高亮样式")
     * @Apidoc\Returned("on_class", type="string", desc="高亮样式")
     */
    public function logisticsInfo(){}




}
