<?php
/**
 * Created by PhpStorm.
 * Date: 2020/12/22
 * Time: 3:11 PM
 */
namespace app\test\controller;

use app\common\model\bu\BuGwFloor;
use app\common\model\bu\BuGwBannerSet;
use app\common\model\bu\BuGwType;
use app\common\model\bu\BuOrder;
use app\common\model\db\DbUser;
use ForkModules\Traits\ResponseTrait;
use app\common\model\bu\BuGwHotWord;
use app\common\model\bu\BuGwRecommendation;
use app\common\model\bu\BuGwFloorProduct;
use app\common\model\db\DbCommodityFlat;
use app\common\validate\GwSet as GwSetValid;
use app\net_small\controller\Common;
use hg\apidoc\annotation as Apidoc;
use think\Model;


/**
 * 保养推荐相关 win
 * @Apidoc\Group("mall")
 */
class Maintainance extends Common{
    use ResponseTrait;

    public function __construct(){
        parent::__construct();
    }

    /**
     * @Apidoc\Title("保养推荐-实时刷新")
     * @Apidoc\Author("zxtdcyy")
     * @Apidoc\Url("/net-small/maintainance/actual-recommend")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("保养推荐 套餐 实时 win")
     * @Apidoc\Desc("通过Vin + 公里数据实时请求大数据平台，用来更新原来的保养推荐数据")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("vin", type="string",require=true, default="", desc="vin码" )
     * @Apidoc\Param("miles", type="string",require=true, default="", desc="公里数" )
     *
     * @Apidoc\Returned("message", type="string",desc="提示信息")
     *
     */
    public function actualRecommend(){}

    /**
     * @Apidoc\Title("智能推荐-实时刷新")
     * @Apidoc\Author("zxtdcyy")
     * @Apidoc\Url("/net-small/maintainance/actual-recommend-ai")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("智能推荐 套餐 实时 win")
     * @Apidoc\Desc("通过Vin + 公里数据实时请求大数据平台，用来更新原来的保养推荐数据")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("vin", type="string",require=true, default="", desc="vin码" )
     * @Apidoc\Param("miles", type="string",require=true, default="", desc="公里数" )
     * @Apidoc\Param("dlr_code", type="string",require=true, default="", desc="门店编码" )
     * @Apidoc\Returned("message", type="string",desc="提示信息")
     *
     */
    public function actualRecommendAi(){}


    /**
     * @Apidoc\Title("保养推荐-优惠明细")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/maintainance/recommend-discount-detail")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("保养推荐 套餐 实时 win")
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("set_sku_data", type="json",require=true, default="", desc="[{'have_work_time':1,'set_sku_id':32312,'act_id':342,'type':1,'count':1,'use_discount':1}]have_work_time 1有勾选工时0没有勾选工时 set_sku_id商品set_sku_id,act_id 参与的活动id，type 1限时活动2满减3N件4秒杀 count 商品数量 use_discount 是否使用商品会员价，1使用，0不使用" )
     * @Apidoc\Param("dlr_code", type="string",require=false, default="", desc="专营店编码" )
     * @Apidoc\Param("vin", type="string",require=false, default="", desc="vin" )
     * @Apidoc\Param("gift_card_set_sku_ids", type="string",require=false, default="", desc="传set_sku_id 多个时逗号隔开" )
     *
     * @Apidoc\Returned("limit_dis_money", type="float",desc="限时优惠")
     * @Apidoc\Returned("full_dis_money", type="float",desc="满减优惠")
     * @Apidoc\Returned("ndis_dis_money", type="float",desc="N件优惠")
     * @Apidoc\Returned("card_dis_money", type="float",desc="卡券优惠")
     * @Apidoc\Returned("work_time_price", type="float",desc="工时优惠")
     * @Apidoc\Returned("kills_time_price", type="float",desc="秒杀优惠")
     * @Apidoc\Returned("all_maintain_dis", type="float",desc="套餐优惠")
     *
     *
     */
    public function discountDetail(MaintainanceValidate $validate){}


    /**
     * @Apidoc\Title("保养sku列表")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/maintainance/get-sku-combo")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("保养推荐 套餐 win")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("commodity_id", type="int",require=true, default="", desc="商品id" )
     * @Apidoc\Param("dlr_name", type="string",require=true, default="", desc="专营店名" )
     * @Apidoc\Param("dlr_code", type="string",require=true, default="", desc="专营店编码" )
     * @Apidoc\Param("dd_commodity_type", type="int",require=true, default="", desc="推荐分类id" )
     *
     *@Apidoc\Returned("sp_value_arr",type="array",desc="规格数组",
     *    @Apidoc\Returned ("sp_id",type="int(11)",desc="规格id"),
     *    @Apidoc\Returned ("sp_name",type="varchar(50)",desc="规格名"),
     *    @Apidoc\Returned("val",type="array",desc="规格数组",
     *          @Apidoc\Returned ("value_id",type="int(11)",desc="值id"),
     *          @Apidoc\Returned ("value_name",type="varchar(50)",desc="值名称"),
     *          @Apidoc\Returned ("sp_id",type="int(11)",desc="规格id"),
     *    ),
     *    @Apidoc\Returned("price_image",type="array",desc="格价图片数组",
     *          @Apidoc\Returned("规格",type="array",desc="规格格价图片数组",
     *                @Apidoc\Returned ("commodity_name",type="varchar(50)",desc="商品名"),
     *                @Apidoc\Returned ("image",type="varchar(50)",desc="图片"),
     *                @Apidoc\Returned ("price",type="float",desc="价格"),
     *                @Apidoc\Returned ("price",type="float",desc="原价"),
     *                @Apidoc\Returned ("sku_id",type="int(11)",desc="sku_id"),
     *                @Apidoc\Returned ("stock",type="int(11)",desc="库存"),
     *                @Apidoc\Returned ("commodity_set_id",type="int(11)",desc="商品上架ID"),
     *                @Apidoc\Returned ("commodity_id",type="int(11)",desc="商品id"),
     *                @Apidoc\Returned ("work_time_price",type="float",desc="工时价格"),
     *                @Apidoc\Returned ("old_work_time_price",type="float",desc="原工时价格"),
     *                @Apidoc\Returned ("work_time_code",type="string",desc="工时编码"),
     *                @Apidoc\Returned ("work_time_number",type="int(11)",desc="工时数量"),
     *          ),
     *
     *    )
     *)
     * @Apidoc\Returned ("dlr_code",type="varchar(50)",desc="专营店编码"),
     * @Apidoc\Returned ("dlr_name",type="varchar(50)",desc="专营店名称"),
     * @Apidoc\Returned ("is_mate",type="int",desc="1配置车型，0不配置车型"),
     * @Apidoc\Returned ("mail_method",type="varchar(50)",desc="1是到店 2是快递 3是快递和到店都有"),
     * @Apidoc\Returned ("ac_dis_type",type="string",desc="活动折扣类型1折扣2立减"),
     * @Apidoc\Returned ("ac_dis_count",type="string",desc="活动折扣值"),
     *
     */
    public function getComboSkuByCommodityid(){}

    /**
     * @Apidoc\Title("备件sku列表")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/maintainance/get-sku-part")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("保养推荐 套餐 win")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("commodity_id", type="int",require=true, default="", desc="商品id" )
     * @Apidoc\Param("dlr_name", type="string",require=true, default="", desc="专营店名" )
     * @Apidoc\Param("dlr_code", type="string",require=true, default="", desc="专营店编码" )
     * @Apidoc\Param("dd_commodity_type", type="int",require=true, default="", desc="推荐分类id" )
     * @Apidoc\Param("num", type="int",require=true, default="1", desc="数量" )
     *
     *@Apidoc\Returned("sp_value_arr",type="array",desc="规格数组",
     *    @Apidoc\Returned ("sp_id",type="int(11)",desc="规格id"),
     *    @Apidoc\Returned ("sp_name",type="varchar(50)",desc="规格名"),
     *    @Apidoc\Returned("val",type="array",desc="规格数组",
     *          @Apidoc\Returned ("value_id",type="int(11)",desc="值id"),
     *          @Apidoc\Returned ("value_name",type="varchar(50)",desc="值名称"),
     *          @Apidoc\Returned ("sp_id",type="int(11)",desc="规格id"),
     *    ),
     *    @Apidoc\Returned("price_image",type="array",desc="格价图片数组",
     *          @Apidoc\Returned("规格",type="array",desc="规格格价图片数组",
     *                @Apidoc\Returned ("commodity_name",type="varchar(50)",desc="商品名"),
     *                @Apidoc\Returned ("image",type="varchar(50)",desc="图片"),
     *                @Apidoc\Returned ("price",type="float",desc="价格"),
     *                @Apidoc\Returned ("old_price",type="float",desc="原价"),
     *                @Apidoc\Returned ("sku_id",type="int(11)",desc="sku_id"),
     *                @Apidoc\Returned ("stock",type="int(11)",desc="库存"),
     *                @Apidoc\Returned ("work_time_price",type="float",desc="工时价格"),
     *                @Apidoc\Returned ("old_work_time_price",type="float",desc="原工时价格"),
     *                @Apidoc\Returned ("commodity_set_id",type="int(11)",desc="商品上架ID"),
     *                @Apidoc\Returned ("commodity_id",type="int(11)",desc="商品id"),
     *                @Apidoc\Returned ("work_time_price",type="float",desc="工时价格"),
     *                @Apidoc\Returned ("work_time_code",type="string",desc="工时编码"),
     *                @Apidoc\Returned ("work_time_number",type="int(11)",desc="工时数量"),
     *          ),
     *
     *    )
     *)
     * @Apidoc\Returned ("dlr_code",type="varchar(50)",desc="专营店编码"),
     * @Apidoc\Returned ("dlr_name",type="varchar(50)",desc="专营店名称"),
     * @Apidoc\Returned ("is_mate",type="int",desc="1配置车型，0不配置车型"),
     * @Apidoc\Returned ("mail_method",type="varchar(50)",desc="1是到店 2是快递 3是快递和到店都有"),
     */
    public function getPartSkuByCommodityid(){}

    /**
     * @Apidoc\Title("推荐列表")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/maintainance/recommend-base")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("保养备件推荐 套餐 win")
     * @Apidoc\ParamType("json")
     *
     *
     * @Apidoc\Param("longitude", type="float",require=false, default="", desc="经度" )
     * @Apidoc\Param("latitude", type="float",require=false, default="", desc="纬度" )
     * @Apidoc\Param("type_id", type="int",require=false, default="", desc="分类ID" )
     * @Apidoc\Param("page", type="int",require=false, default="1", desc="页码" )
     * @Apidoc\Param("page_size", type="int",require=false, default="5", desc="每页大小" )
     * @Apidoc\Param("is_dfs", type="int",require=false, default="", desc="其它渠道跳过来 1南方商城" )
     *
     *@Apidoc\Returned ("is_vin_car",type="int",desc="0无vin车用户；1有vin车但非默认车型用户；2默认车型为vin车用户")
     *@Apidoc\Returned ("dlr_code",type="string",desc="专营店编码")
     *@Apidoc\Returned ("dlr_name",type="string",desc="专营店名")
     *@Apidoc\Returned ("goods_ids",type="string",desc="所有商品IDs")
     *@Apidoc\Returned("card_list",type="array",desc="卡券列表",
     *           @Apidoc\Returned ("card_id",type="int(11)",desc="卡券id"),
     *           @Apidoc\Returned ("card_type",type="int(11)",desc="卡劵类型(1立减券；2 折购券；3兑换券；4满减券；5优惠券；6到店代金券)"),
     *           @Apidoc\Returned ("name",type="string",desc="卡券名"),
     *           @Apidoc\Returned ("card_quota",type="decimal(10,2)",desc="卡劵额度（减免金额）"),
     *           @Apidoc\Returned ("card_discount",type="decimal(10,1)",desc="折扣，折扣券专用"),
     *           @Apidoc\Returned ("least_cost",type="int(11)",desc="最低消费类型，1金额2指定商品"),
     *           @Apidoc\Returned ("least_type",type="string",desc="表示起用金额,如果无起用门槛则填0。"),
     *           @Apidoc\Returned ("validity_date_start",type="string",desc="有效开始时间"),
     *           @Apidoc\Returned ("validity_date_end",type="string",desc="有效结束时间"),
     *           @Apidoc\Returned ("use_des",type="string",desc="使用规则"),
     *           @Apidoc\Returned ("state",type="int(11)",desc="0可领取"),
     *           @Apidoc\Returned ("available_quantity",type="int(11)",desc="available_quantity = 0 就是已领取"),
     *           @Apidoc\Returned ("available_count",type="int(11)",desc="available_count = 0 就是已抢光"),
     *           @Apidoc\Returned ("date_type",type="int",desc="有效期类型：1表示固定日期区间，2表示固定时长"),
     * )
     *@Apidoc\Returned("product_by_list",type="array",desc="保养推荐列表",
     *     @Apidoc\Returned ("commodity_name",type="string",desc="商品名"),
     *     @Apidoc\Returned ("work_hour_type",type="int",desc="工时类型编码"),
     *     @Apidoc\Returned ("gift_card_ids",type="string",desc="赠品券id,多个时以逗号隔开"),
     *     @Apidoc\Returned ("gift_card_type",type="int",desc="赠品券类型，0 ：不是赠品券，1主品"),
     *
     *     @Apidoc\Returned ("dd_commodity_type",type="int",desc="到店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-双保专属权益套餐 6保养套餐-其他 7到店代金券 41保养套餐-五年双保升级权益套餐全合成"),
     *     @Apidoc\Returned ("wi_qty",type="int(11)",desc="工时数量"),
     *     @Apidoc\Returned ("wi_code",type="string",desc="工时编码"),
     *     @Apidoc\Returned ("sku_code",type="string",desc="商品sku"),
     *     @Apidoc\Returned ("commodity_set_id",type="int",desc="商品上架id"),
     *     @Apidoc\Returned ("limit_dis",type="string",desc="限时活动"),
     *     @Apidoc\Returned ("full_dis",type="string",desc="满减活动"),
     *     @Apidoc\Returned ("n_dis",type="string",desc="N件N折活动"),
     *     @Apidoc\Returned ("commodity_id",type="int",desc="商品id"),
     *     @Apidoc\Returned ("sku_id",type="int",desc="sku id"),
     *     @Apidoc\Returned ("set_sku_price",type="decimal(10,2)",desc="售价"),
     *     @Apidoc\Returned ("commodity_sku_id",type="int",desc="商品id"),
     *     @Apidoc\Returned ("sp_value_list",type="string",desc="规格值"),
     *     @Apidoc\Returned ("image",type="string",desc="图片"),
     *     @Apidoc\Returned ("comm_type_id",type="string",desc="二级分类id"),
     *     @Apidoc\Returned ("comm_type_name",type="int",desc="二级分类名"),
     *     @Apidoc\Returned ("dis_price",type="decimal(10,2)",desc="商品省下多少钱"),
     *     @Apidoc\Returned ("old_price",type="decimal(10,2)",desc="原价"),
     *     @Apidoc\Returned ("selected",type="int",desc="1选中0未选中"),
     *     @Apidoc\Returned ("work_time_price",type="decimal(10,2)",desc="现工时价"),
     *     @Apidoc\Returned ("old_work_time_price",type="decimal(10,2)",desc="原工时价"),
     *     @Apidoc\Returned ("work_time_dis_price",type="decimal(10,2)",desc="工时省下多少钱"),
     *     @Apidoc\Returned ("dd_commodity_type_name",type="string",desc="推荐名"),
     *     @Apidoc\Returned("spec_list",type="array",desc="规各值",
     *           @Apidoc\Returned ("value_id",type="int(11)",desc="值id"),
     *           @Apidoc\Returned ("sp_value_name",type="string",desc="值名称"),
     *           @Apidoc\Returned ("sp_id",type="int(11)",desc="规格id"),
     *           @Apidoc\Returned ("sp_name",type="string",desc="规格名"),
     *     ),
     *     @Apidoc\Returned("ac_list",type="array",desc="活动列表",
     *           @Apidoc\Returned ("id",type="int(11)",desc="活动id"),
     *           @Apidoc\Returned ("title",type="string",desc="活动名称"),
     *           @Apidoc\Returned ("start_time",type="string",desc="活动开始时间"),
     *           @Apidoc\Returned ("end_time",type="string",desc="活动结束时间"),
     *           @Apidoc\Returned ("type",type="int(11)",desc="1限时活动2满减3N件N折4秒杀")
     *     ),
     *    @Apidoc\Returned ("commodity_dis_user_segment",type="string",desc="商品折扣：0-无/1-会员价/2-车主价(0423)"),
     *    @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *    @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *    @Apidoc\Returned ("ac_dis_type",type="string",desc="活动折扣类型1折扣2立减"),
     *    @Apidoc\Returned ("ac_dis_count",type="string",desc="活动折扣值"),
     *
     * ),
     *@Apidoc\Returned("product_bj_list",type="array",desc="备件推荐列表",
     *   @Apidoc\Returned("current_page",type="int",desc="当前页"),
     *   @Apidoc\Returned("page_size",type="int",desc="每页大小"),
     *   @Apidoc\Returned("total",type="int",desc="总记录数"),
     *   @Apidoc\Returned("all_page",type="array",desc="总页数"),
     *   @Apidoc\Returned("data",type="array",desc="备件推荐列表",
     *     @Apidoc\Returned ("is_mail",type="int",desc="是否 快递 （1是 0否)"),
     *     @Apidoc\Returned ("is_store",type="int",desc="是否门店自提（1是，0否）默认是"),
     *     @Apidoc\Returned ("mail_type",type="int",desc="1到店2快递3都有"),
     *     @Apidoc\Returned ("commodity_name",type="string",desc="商品名"),
     *     @Apidoc\Returned ("work_hour_type",type="int",desc="工时类型编码"),
     *     @Apidoc\Returned ("dd_commodity_type",type="int",desc="到店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-五年双保升级权益套餐 6保养套餐-其他 7到店代金券 41保养套餐-五年双保升级权益套餐全合成"),
     *     @Apidoc\Returned ("wi_qty",type="int(11)",desc="工时数量"),
     *     @Apidoc\Returned ("wi_code",type="string",desc="工时编码"),
     *     @Apidoc\Returned ("sku_code",type="string",desc="商品sku"),
     *     @Apidoc\Returned ("commodity_set_id",type="int",desc="商品上架id"),
     *     @Apidoc\Returned ("limit_dis",type="string",desc="限时活动"),
     *     @Apidoc\Returned ("full_dis",type="string",desc="满减活动"),
     *     @Apidoc\Returned ("n_dis",type="string",desc="N件N折活动"),
     *     @Apidoc\Returned ("commodity_id",type="int",desc="商品id"),
     *     @Apidoc\Returned ("sku_id",type="int",desc="sku id"),
     *     @Apidoc\Returned ("set_sku_price",type="decimal(10,2)",desc="售价"),
     *     @Apidoc\Returned ("set_sku_id",type="int",desc="上架商品id"),
     *     @Apidoc\Returned ("sp_value_list",type="string",desc="规格值"),
     *     @Apidoc\Returned ("image",type="string",desc="图片"),
     *     @Apidoc\Returned ("comm_type_id",type="string",desc="二级分类id"),
     *     @Apidoc\Returned ("comm_type_name",type="int",desc="二级分类名"),
     *     @Apidoc\Returned ("dis_price",type="decimal(10,2)",desc="商品省下多少钱"),
     *     @Apidoc\Returned ("old_price",type="decimal(10,2)",desc="原价"),
     *     @Apidoc\Returned ("selected",type="int",desc="1选中0未选中"),
     *     @Apidoc\Returned ("work_time_price",type="decimal(10,2)",desc="现工时价"),
     *     @Apidoc\Returned ("old_work_time_price",type="decimal(10,2)",desc="原工时价"),
     *     @Apidoc\Returned ("work_time_dis_price",type="decimal(10,2)",desc="工时省下多少钱"),
     *     @Apidoc\Returned ("dd_commodity_type_name",type="string",desc="推荐名"),
     *     @Apidoc\Returned ("in_activity",type="int",desc="商品是否参与活动1参与0不参与(ver:1020:add)"),
     *     @Apidoc\Returned ("work_in_activity",type="int",desc="工时是否参与活动1参与0不参与(ver:1020:add)"),
     *     @Apidoc\Returned("spec_list",type="array",desc="规各值",
     *           @Apidoc\Returned ("value_id",type="int(11)",desc="值id"),
     *           @Apidoc\Returned ("sp_value_name",type="string",desc="值名称"),
     *           @Apidoc\Returned ("sp_id",type="int(11)",desc="规格id"),
     *           @Apidoc\Returned ("sp_name",type="string",desc="规格名"),
     *     ),
     *     @Apidoc\Returned("gift_card_count",type="array",desc="赠品券信息",
     *           @Apidoc\Returned ("number",type="int(11)",desc="赠品券数量"),
     *           @Apidoc\Returned ("card_id",type="string",desc="赠品券id"),
     *           @Apidoc\Returned ("card_desc",type="string",desc="赠品券描述"),
     *     ),
     *     @Apidoc\Returned("ac_list",type="array",desc="活动列表",
     *           @Apidoc\Returned ("id",type="int(11)",desc="活动id"),
     *           @Apidoc\Returned ("title",type="string",desc="活动名称"),
     *           @Apidoc\Returned ("start_time",type="string",desc="开始时间"),
     *           @Apidoc\Returned ("end_time",type="string",desc="结束时间"),
     *           @Apidoc\Returned ("is_limit",type="int(11)",desc="1可参与，0为不可参与"),
     *           @Apidoc\Returned ("purchase_number",type="int(11)",desc="限购数量：0为无限购"),
     *           @Apidoc\Returned ("type",type="int(11)",desc="1限时活动2满减3N件N折4秒杀"),
     *          @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *          @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *
     *     ),
          @Apidoc\Returned("request_data",type="array",desc="请求数据",
     *           @Apidoc\Returned ("dlr_code",type="int(11)",desc="专营店编码"),
     *           @Apidoc\Returned ("kilometer",type="int(11)",desc="公里数")
     *     ),
     *    @Apidoc\Returned ("commodity_dis_user_segment",type="string",desc="商品折扣：0-无/1-会员价/2-车主价(0423)"),
     *    @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *    @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *    @Apidoc\Returned ("commodity_dis_act_segment_price",type="string",desc="商品折扣后活动折扣价(0423)"),
     *    @Apidoc\Returned ("commodity_dis_act_original_price",type="string",desc="商品原价活动折扣价(0423)"),
     *    @Apidoc\Returned("groups_data", type="array/json", desc="组合子商品" ,
     *        @Apidoc\Returned("sub_commodity_list", type="array/json", desc="组合子商品列表",
     *           @Apidoc\Returned("count_stock", type="int", desc="子商品的总库存" ),
     *           @Apidoc\Returned("stock", type="int", desc="库存" ),
     *           @Apidoc\Returned("is_mate", type="int", desc="车型是否可以购买1可以，其他不行" ),
     *           @Apidoc\Returned("sp_value_list", type="string", desc="规格值" ),
     *           @Apidoc\Returned("min_price", type="float", desc="最低的sku价格" ),
     *           @Apidoc\Returned("commodity_id", type="int", desc="组合主商品id" ),
     *           @Apidoc\Returned("group_sub_commodity_id", type="int", desc="组合子商品id" ),
     *           @Apidoc\Returned("commodity_name", type="string", desc="组合子商品名" ),
     *           @Apidoc\Returned("image", type="string", desc="组合子商品图片" ),
     *           @Apidoc\Returned("sp_value_name", type="string", desc="组合子商品规值" ),
     *           @Apidoc\Returned("sp_value_list", type="string", desc="规格值" ),
     *           @Apidoc\Returned("set_sku_id", type="int", desc="上架id--规格ID，下单\加购都用这个" ),
     *           @Apidoc\Returned("initial_num", type="int", desc="初始数量" ),
     *           @Apidoc\Returned("user_can_des", type="int", desc="用户是否可增减数量" ),
     *           @Apidoc\Returned("can_select", type="int", desc="是否可选" ),
     *           @Apidoc\Returned ("relate_car_ids",type="text",desc="关联车系ID"),
     *           @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="子工时数量"),
     *           @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="子工时编码"),
     *           @Apidoc\Returned ("work_time_price",type="varchar(50)",desc="子工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *          ),
     *        @Apidoc\Returned("commodity_dis_info", type="array/json", desc="组合商品折扣信息",
     *           @Apidoc\Returned ("discount_type",type="varchar(50)",desc="折扣方式，1打折，2立减"),
     *           @Apidoc\Returned ("discount",type="varchar(50)",desc="折扣额度"),
     *           @Apidoc\Returned ("max_discount_amount",type="varchar(50)",desc="折扣上限"),
     *          ),
     *        @Apidoc\Returned("h_card_list", type="array/json", desc="组合商品用券说明",
     *           @Apidoc\Returned("id", type="int(10)", desc="卡券id" ),
     *           @Apidoc\Returned("card_name", type="varchar(200)", desc="卡劵名称" ),
     *           @Apidoc\Returned("list", type="array/json", desc="商品列表" ,
     *              @Apidoc\Returned("goods_list", type="array/json", desc="每组列表sku组合" ,
     *                  @Apidoc\Returned("sp_value_name", type="varchar(200)", desc="规格名") ,
     *                  @Apidoc\Returned("commodity_name", type="varchar(200)", desc="商品名") ,
     *                  @Apidoc\Returned("set_sku_id", type="int(200)", desc="规格ID,") ,
     *                  @Apidoc\Returned("price", type="decimal(10,2)", desc="价格" ),
     *                  @Apidoc\Returned("limit_dis_price", type="decimal(10,2)", desc="限时折扣sku价格/无活动==price" ),
     *                  @Apidoc\Returned("stock", type="int(10)", desc="库存" ),
     *                  @Apidoc\Returned("set_sku_id", type="int(10)", desc="上架id上架规格取这个" ),
     *                  @Apidoc\Returned("sku_id", type="int(10)", desc="规格ID" ),
     *                  @Apidoc\Returned("image", type="varchar(200)", desc="图片" ),
     *                  @Apidoc\Returned ("work_time_number",type="varchar(50)",desc="工时数量"),
     *                  @Apidoc\Returned ("work_time_code",type="varchar(50)",desc="工时编码"),
     *                  @Apidoc\Returned ("work_time_price",type="varchar(50)",desc="工时单价需要数量*工时数量*购物车数量才是总商品工时"),
     *                  @Apidoc\Returned("sp_value_arr", type="array/json", desc="规格主键列表（无字段只有值）" ),
     *                  @Apidoc\Returned ("is_gift",type="tinyint(1)",desc="是否买赠 1是 0否(0214版本)"),
     *                  @Apidoc\Returned ("detail_content",type="text",desc="规格详情内容(4月版本)"),
     *                  @Apidoc\Returned ("commodity_dis_user_segment",type="string",desc="商品折扣：0-无/1-会员价/2-车主价(0423)"),
     *                  @Apidoc\Returned ("commodity_dis_act_user_segment",type="string",desc="活动折扣：0-无/1-会员价/2-车主价(0423)"),
     *                  @Apidoc\Returned ("commodity_dis_label",type="string",desc="会员/车主价标签(0423)"),
     *                  @Apidoc\Returned ("current_price",type="varchar(20)",desc="商品现价，活动非会员价(0423版本)"),
     *                  @Apidoc\Returned ("sp_value_list",type="varchar(20)",desc="sp_value_list"),
     *                  @Apidoc\Returned ("can_most_point",type="int(10)",desc="最高可用积分"),
     *                  @Apidoc\Returned ("group_sub_commodity_id",type="int(10)",desc="子商品商品ID"),
     *          ),
     *     ),
     *     @Apidoc\Returned("shelves_type", type="int(2)", desc="上架来源：1.平台自营 2.专营店 3.官微 4.活动 5.车生活" )
     * )
     *     ),
     *     @Apidoc\Returned("sp_associated_data", type="array/json", desc="组合子商品规格联动" ,
     *          @Apidoc\Returned("sub_commodity_id", type="int", desc="组合子商品id" ),
     *          @Apidoc\Returned("sp_id", type="int", desc="规格id" ),
     *          @Apidoc\Returned("sp_value_id", type="int", desc="规格值id" ),
     *          @Apidoc\Returned("assoc_sub_commodity_id", type="int", desc="联动组合子商品id" ),
     *          @Apidoc\Returned("assoc_sp_id", type="int", desc="联动规格id" ),
     *          @Apidoc\Returned("assoc_sp_value_id", type="int", desc="联动规格值id" ),
     *     ),
     *     @Apidoc\Returned("is_sp_associated", type="int(11)", desc="组合子商品是否存在规格联动，1-存在/0-不存在" ),
     *     @Apidoc\Returned("full_list", type="array/json", desc="满减列表",
     *     @Apidoc\Returned("id", type="int(11)", desc="满减优惠活动id" ),
     *     @Apidoc\Returned("money", type="decimal(10,2)", desc="活动金额" ),
     *     @Apidoc\Returned("is_preferential_money", type="tinyint(4) ", desc="是否支持立减(1支持0否）" ),
     *     @Apidoc\Returned("is_preferential_card", type="tinyint(4) ", desc="是否支持送券(1支持0否）" ),
     *     @Apidoc\Returned("preferential_money", type="decimal(10,2)", desc="'下单立减（优惠方式为1时）" ),
     *     @Apidoc\Returned("preferential_card_id", type="int", desc="优惠券编号（优惠方式为2时有效）" ),
     *     @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *     @Apidoc\Returned("full_discount_rules", type="varchar(255)", desc="满减规则,1个活动最多3个规则,如满5减3,满2减1,满1减0.5的数据[[5,3],[2,1],[1,0.5]]" ),
     *     @Apidoc\Returned("word", type="varchar(50)", desc="活动名称" ),
     *     @Apidoc\Returned("url", type="varchar(200)", desc="满减信息链接" ),
     *     @Apidoc\Returned("can_buy_number", type="varchar(200)", desc="满减活动，可参与次数，大于0表示可参与活动" ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * ),
     * @Apidoc\Returned("n_dis_info", type="array/json", desc="N件N折优惠信息",
     *     @Apidoc\Returned("n_id", type="int(11)", desc="N件N折活动id" ),
     *     @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("des", type="varchar(60)", desc="活动描述" ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * ),
     * @Apidoc\Returned("limit_info", type="array/json", desc="限时优惠信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="限时折扣活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("purchase_number", type="int(11)", desc="限购数量" ),
     *     @Apidoc\Returned("des", type="varchar(60)", desc="活动描述" ),
     *     @Apidoc\Returned("stop_time", type="datetime", desc="停止时间" ),
     *     @Apidoc\Returned("discount_type", type="int(11)", desc="优惠类型：1仅商品；2仅工时；3商品+工时" ),
     *     @Apidoc\Returned("discount", type="int(11)", desc="折扣/直减" ),
     *     @Apidoc\Returned("non_member_discount", type="int(11)", desc="非会员折扣 折扣/直减" ),
     *     @Apidoc\Returned("dis_type", type="int(11)", desc="1限时折扣 2立减" ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * ),
     * @Apidoc\Returned("seckill_info", type="array/json", desc="秒杀优惠信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="秒杀折扣活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("purchase_number", type="int(11)", desc="限购数量" ),
     *     @Apidoc\Returned("des", type="varchar(60)", desc="活动描述" ),
     *     @Apidoc\Returned("stop_time", type="datetime", desc="停止时间" ),
     *     @Apidoc\Returned("discount_type", type="int(11)", desc="优惠类型：1仅商品；2仅工时；3商品+工时" ),
     *     @Apidoc\Returned("act_status", type="int(11)", desc="1未开始，2进行中，3已结束" ),
     *     @Apidoc\Returned("discount", type="int(11)", desc="折扣/直减" ),
     *     @Apidoc\Returned("non_member_discount", type="int(11)", desc="非会员折扣 折扣/直减" ),
     *     @Apidoc\Returned("dis_type", type="int(11)", desc="1限时折扣 2立减" ),
     *     @Apidoc\Returned("miaosha_stock", type="int(11)", desc="秒杀库存" ),
     *     @Apidoc\Returned("seckill_type", type="int(11)", desc="秒杀类型 1:单场秒杀  2:重复秒杀" ),
     *     @Apidoc\Returned("day_start_time", type="varchar(20)", desc="秒杀当天开始时间" ),
     *     @Apidoc\Returned("day_end_time", type="varchar(20)", desc="秒杀当天结束时间" ),
     *     @Apidoc\Returned("sm_start_time", type="varchar(20)", desc="秒杀开始时间" ),
     *     @Apidoc\Returned("sm_end_time", type="varchar(20)", desc="秒杀结束时间" ),
     *     @Apidoc\Returned("next_start_time", type="varchar(20)", desc="下场秒杀开始时间" ),
     *     @Apidoc\Returned("next_end_time", type="varchar(20)", desc="下场秒杀结束时间" ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * ),
     * @Apidoc\Returned("limit_wi_info", type="array/json", desc="限时优惠信息-工时",
     *     @Apidoc\Returned("id", type="int(11)", desc="限时折扣活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("purchase_number", type="int(11)", desc="限购数量" ),
     *     @Apidoc\Returned("des", type="varchar(60)", desc="活动描述" ),
     *     @Apidoc\Returned("stop_time", type="datetime", desc="停止时间" ),
     *     @Apidoc\Returned("discount_type", type="int(11)", desc="优惠类型：1仅商品；2仅工时；3商品+工时" ),
     *     @Apidoc\Returned("discount", type="int(11)", desc="折扣/直减" ),
     *     @Apidoc\Returned("dis_type", type="int(11)", desc="1限时折扣 2立减" )
     * ),
     * @Apidoc\Returned("full_wi_list", type="array/json", desc="满减列表-工时",
     *     @Apidoc\Returned("id", type="int(11)", desc="满减优惠活动id" ),
     *     @Apidoc\Returned("money", type="decimal(10,2)", desc="活动金额" ),
     *     @Apidoc\Returned("is_preferential_money", type="tinyint(4) ", desc="是否支持立减(1支持0否）" ),
     *     @Apidoc\Returned("is_preferential_card", type="tinyint(4) ", desc="是否支持送券(1支持0否）" ),
     *     @Apidoc\Returned("preferential_money", type="decimal(10,2)", desc="'下单立减（优惠方式为1时）" ),
     *     @Apidoc\Returned("preferential_card_id", type="int", desc="优惠券编号（优惠方式为2时有效）" ),
     *     @Apidoc\Returned("commodity_id", type="int(11)", desc="商品id" ),
     *     @Apidoc\Returned("full_discount_rules", type="varchar(255)", desc="满减规则,1个活动最多3个规则,如满5减3,满2减1,满1减0.5的数据[[5,3],[2,1],[1,0.5]]" ),
     *     @Apidoc\Returned("word", type="varchar(50)", desc="活动名称" ),
     *     @Apidoc\Returned("url", type="varchar(200)", desc="满减信息链接" ),
     *     @Apidoc\Returned("can_buy_number", type="varchar(200)", desc="满减活动，可参与次数，大于0表示可参与活动" )
     * ),
     * @Apidoc\Returned("g_info", type="array/json", desc="团购信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="团购活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="团购活动名称" )
     * ),
     * @Apidoc\Returned("gift_info", type="array/json", desc="买赠信息",
     *     @Apidoc\Returned("id", type="int(11)", desc="买赠活动id" ),
     *     @Apidoc\Returned("title", type="varchar(20)", desc="活动名称" ),
     *     @Apidoc\Returned("des", type="text", desc="活动规则" ),
     *     @Apidoc\Returned("start_time", type="datetime", desc="开始时间" ),
     *     @Apidoc\Returned("end_time", type="datetime", desc="结束时间" ),
     *     @Apidoc\Returned("purchase_number", type="int(11)", desc="剩余次数" ),
     *     @Apidoc\Returned("optional_number", type="int(11)", desc="可选择N件" ),
     *     @Apidoc\Returned("act_status", type="tinyint(1)", desc="1未开始，2进行中，3已结束" ),
     *     @Apidoc\Returned ("is_participate",type="tinyint(1)", desc="是否参加 1参加 0未参加"),
     *     @Apidoc\Returned ("gift_imgs_arr", type="array/json", desc="买赠主图",
     *          @Apidoc\Returned("is_choose", type="tinyint(1)", desc="是否可选 1可选 0不可选" ),
     *          @Apidoc\Returned("cover_image", type="varchar(60)", desc="赠品主图" )
     *     ),
     *     @Apidoc\Returned("commodity_dis_act_user_segment", type="int(11)", desc="活动折扣：0-无/1-会员价/2-车主价(0423)" ),
     *     @Apidoc\Returned("commodity_dis_label", type="string", desc="会员/车主价标签(0423)" ),
     * ),
     *   ),
     * ),
     */
    public function redommendBase(MaintainanceValidate $validate){}

    /**
     * @Apidoc\Title("智能列表")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/maintainance/recommend-base-ai")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("智能备件推荐 套餐 win")
     * @Apidoc\ParamType("json")
     *
     *
     * @Apidoc\Param("longitude", type="float",require=false, default="", desc="经度" )
     * @Apidoc\Param("latitude", type="float",require=false, default="", desc="纬度" )
     * @Apidoc\Param("nvi_vin", type="string",require=false, default="", desc="vin" )
     * @Apidoc\Param("dlr_code", type="string",require=false, default="", desc="专营店编码" )
     *
     *@Apidoc\Returned("product_list",type="array",desc="商品推荐列表",
     *
     *            @Apidoc\Returned ("nvi_vin",type="string",desc="nvi的vin"),
     *           @Apidoc\Returned ("longitude",type="int",desc="经度"),
     *          @Apidoc\Returned ("latitude",type="string",desc="纬度"),
     *           @Apidoc\Returned ("mName",type="string",desc="大类名"),
     *           @Apidoc\Returned ("mCode",type="string",desc="大类编码"),
     *           @Apidoc\Returned ("dName",type="int(11)",desc="小类名"),
     *           @Apidoc\Returned ("dCode",type="string",desc="小类编码"),
     *           @Apidoc\Returned ("lastMileage",type="string",desc="上次保养/维护项目的里程"),
     *           @Apidoc\Returned ("sdtMileage",type="string",desc="保养/维护项目的里程标准"),
     *           @Apidoc\Returned ("mileRatio",type="string",desc="里程系数(保养或维护项目后已使用里程/里程标准) doubule  健康状态分值a"),
     *           @Apidoc\Returned ("nextFixMile",type="string",desc="下次保养/维护项目里程"),
     *           @Apidoc\Returned ("lastDay",type="string",desc="上次保养/维护项目时间"),
     *           @Apidoc\Returned ("sdtDays",type="string",desc="保养/维护项目的天数标准"),
     *           @Apidoc\Returned ("dayRatio",type="string",desc="时间系数(保养或维护项目后已使用时间/天数标准)"),
     *           @Apidoc\Returned ("nextFixDay",type="string",desc="下次保养/维护项目时间"),
     *           @Apidoc\Returned ("copywriting",type="string",desc="文案:健康/紧急"),
     *           @Apidoc\Returned ("nextMileRatio",type="string",desc="下次保养时此项目的里程系数健康状态分值a"),
     *          @Apidoc\Returned ("nextDayRatio",type="string",desc="下次保养时此项目的时间系数健康状态分值b"),
     *          @Apidoc\Returned ("nextCopywriting",type="string",desc="下次保养时此项目的文案:健康/紧急"),
     *          @Apidoc\Returned("projectIntroduction",type="string",desc="项目介绍项目介绍"),
     *         @Apidoc\Returned("goods",type="array",desc="商品信息",
     *            @Apidoc\Returned ("maintain_dis",type="string",desc="折扣"),
     *            @Apidoc\Returned ("ac_dis_count",type="string",desc="折上减"),
     *            @Apidoc\Returned ("ac_dis_type",type="string",desc="活动折扣类型1折扣2立减"),
     *            @Apidoc\Returned ("commodity_id",type="string",desc="商品ID"),
     *            @Apidoc\Returned ("commodity_name",type="string",desc="商品名称"),
     *            @Apidoc\Returned ("set_sku_id",type="string",desc="商品上架ID"),
     *            @Apidoc\Returned ("dd_commodity_type",type="string",desc="到店商品类型 9到店备件 1保养套餐-老友惠保养套餐 3保养套餐-心悦保养套餐 4保养套餐-五年双保升级权益套餐 6保养套餐-其他 7到店代金券 41保养套餐-五年双保升级权益套餐全合成"),
     *            @Apidoc\Returned ("set_sku_price",type="string",desc="售价"),
     *            @Apidoc\Returned ("old_price",type="string",desc="原价"),
     *            @Apidoc\Returned ("cover_image",type="string",desc="商品图片"),
     *            @Apidoc\Returned ("value_ids",type="string",desc="规格值id"),
     *            @Apidoc\Returned ("value_namnes",type="string",desc="规格值"),
     *
     *
     *            @Apidoc\Returned("act_list",type="array",desc="活动信息",
         *            @Apidoc\Returned ("id",type="string",desc="活动id"),
         *            @Apidoc\Returned ("title",type="string",desc="活动名称"),
         *            @Apidoc\Returned ("type",type="string",desc="1限时活动2满减3N件N折4秒杀5拼团6套装7预售"),
     *             ),
     *            @Apidoc\Returned("card_list",type="array",desc="卡券信息",
         *            @Apidoc\Returned ("id",type="string",desc="卡券id"),
         *            @Apidoc\Returned ("card_name",type="string",desc="卡券名称"),
         *            @Apidoc\Returned ("card_type",type="string",desc="卡劵类型(1立减券；2 折购券；3兑换券；4满减券；5优惠券；6到店代金券)"),
         *         ),
     *        ),
     *
     *
     * )
     *
     *
     */
    public function redommendBaseAi(MaintainanceValidate $validate){}

    /**
     * @Apidoc\Title("确认选择sku")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/maintainance/recommend-selected")
     * @Apidoc\Method("GET")
     * @Apidoc\Tag("保养推荐 套餐 win")
     * @Apidoc\ParamType("json")
     *
     * @Apidoc\Param("num", type="int",require=false, default="1", desc="数量" )
     * @Apidoc\Param("sku_id", type="int",require=true, default="", desc="sku id" )
     * @Apidoc\Param("commodity_set_id", type="int",require=true, default="", desc="上架id" )
     * @Apidoc\Param("commodity_id", type="int",require=true, default="", desc="商品id" )
     * @Apidoc\Param("type", type="int",require=true, default="", desc="1限时2满减3n件" )
     * @Apidoc\Param("dd_commodity_type", type="int",require=true, default="", desc="商品类型" )
     *
     *
     *@Apidoc\Returned("commodity_name",type="string",desc="商品名")
     *@Apidoc\Returned("commodity_id",type="id",desc="商品ID")
     *@Apidoc\Returned("commodity_set_id",type="id",desc="上架id")
     *@Apidoc\Returned("stock",type="ind",desc="库存")
     *@Apidoc\Returned("sku_id",type="int",desc="sku_id")
     *@Apidoc\Returned("wi_code",type="string",desc="工时编码")
     *@Apidoc\Returned("wi_qty",type="int",desc="工时数量")
     *@Apidoc\Returned("image",type="string",desc="图片")
     *@Apidoc\Returned("sp_value_list",type="string",desc="规格值")
     *@Apidoc\Returned("n_dis",type="string",desc="n件n折")
     *@Apidoc\Returned("limit_dis",type="string",desc="限时id")
     *@Apidoc\Returned("full_dis",type="string",desc="满减id")
     *@Apidoc\Returned("set_sku_id",type="string",desc="商品上架ID")
     *
     *@Apidoc\Returned("dd_commodity_type_name",type="string",desc="分类名")
     *@Apidoc\Returned("dd_commodity_type",type="int",desc="分类ID")
     *@Apidoc\Returned("dis_price",type="decimal(10,2)",desc="限时省了多少钱")
     *@Apidoc\Returned("old_price",type="decimal(10,2)",desc="原价")
     *@Apidoc\Returned("selected",type="int",desc="1选中")
     *@Apidoc\Returned("work_time_price",type="decimal(10,2)",desc="工时价格")
     *@Apidoc\Returned("old_work_time_price",type="decimal(10,2)",desc="工时价格原价")
     *@Apidoc\Returned("work_time_dis_price",type="decimal(10,2)",desc="工时价格省了多少钱")
     *@Apidoc\Returned("work_hour_type",type="string",desc="工时编码")
     *
     *@Apidoc\Returned("ac_list",type="array",desc="活动id",
     *       @Apidoc\Returned ("sp_id",type="int(11)",desc="规格id"),
     *       @Apidoc\Returned ("sp_name",type="string",desc="规格名"),
     *       @Apidoc\Returned ("value_id",type="int(11)",desc="值ID"),
     *       @Apidoc\Returned ("sp_value_name",type="string",desc="规格值")
     *)
     *
     *
     *@Apidoc\Returned("spec_list",type="array",desc="规格数组",
     *       @Apidoc\Returned ("type",type="int(11)",desc="1限时活动2满减活3n件活动4秒杀"),
     *       @Apidoc\Returned ("id",type="int",desc="活动id"),
     *       @Apidoc\Returned ("title",type="string",desc="活动名")
     *    )
     * @Apidoc\Returned ("ac_dis_type",type="string",desc="活动折扣类型1折扣2立减"),
     * @Apidoc\Returned ("ac_dis_count",type="string",desc="活动折扣值"),
     *
     */
    public function selectedSku(MaintainanceValidate $validate){}


    /**
     * @Apidoc\Title("保养推荐-切换活动")
     * @Apidoc\Author("hjz")
     * @Apidoc\Url("/net-small/maintainance/activity-change")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("保养推荐 套餐 实时 win")
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("dd_commodity_type", type="int",require=true, default="", desc="商品类型id" )
     * @Apidoc\Param("type", type="int",require=true, default="", desc="活动类型1限时2满减3n件4秒杀" )
     * @Apidoc\Param("set_sku_id", type="int",require=true, default="", desc="商品上架id" )
     * @Apidoc\Param("commodity_id", type="int",require=true, default="", desc="商品id" )
     * @Apidoc\Param("use_discount", type="int",require=true, default="", desc="是否使用会员价，1使用，0不使用" )
     *
     * @Apidoc\Returned("old_price", type="float",desc="原价")
     * @Apidoc\Returned("set_sku_price", type="float",desc="现价")
     * @Apidoc\Returned("dis_price", type="float",desc="优惠金额")
     * @Apidoc\Returned("wi_act_id", type="int",desc="活动id")
     * @Apidoc\Returned("wi_act_type_id", type="float",desc="活动类型 1限时2满减3n件")
     * @Apidoc\Returned("work_time_price", type="float",desc="工时现价")
     * @Apidoc\Returned("old_work_time_price", type="float",desc="工时原价")
     * @Apidoc\Returned ("commodity_dis_act_segment_price",type="string",desc="商品折扣后活动折扣价(0423)"),
     * @Apidoc\Returned ("commodity_dis_act_original_price",type="string",desc="商品原价活动折扣价(0423)"),
     * @Apidoc\Returned ("commodity_dis_use_discount",type="string",desc="当前是否使用会员价，1使用，0不使用(0423)"),
     * @Apidoc\Returned ("ac_dis_type",type="string",desc="活动折扣类型1折扣2立减"),
     * @Apidoc\Returned ("ac_dis_count",type="string",desc="活动折扣值"),
     *
     *
     */
    public function activityChange(MaintainanceValidate $validate){}


    /**
     * @Apidoc\Title("保养推荐-查询组合商品是否匹配券")
     * @Apidoc\Author("lzx")
     * @Apidoc\Url("/net-small/maintainance/check-group-card")
     * @Apidoc\Method("POST")
     * @Apidoc\Tag("保养推荐 套餐 实时 win")
     * @Apidoc\ParamType("json")
     * @Apidoc\Param("sku_json", type="json",require=true, default="", desc="二维数组，包含sku_id,group_sub_commodity_id" )
     *
     * @Apidoc\Returned("can_use_card", type="int",desc=" 可用券类型，1可用券，2其他规格可用，0无券")
     *
     *
     */
    public function checkGroupCard(MaintainanceValidate $validate){}

}
