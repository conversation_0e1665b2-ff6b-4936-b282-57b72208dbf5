# goodsList 方法性能优化

## 概述

本项目对 `NetGoods::goodsList` 方法进行了深度性能优化，主要解决了以下问题：

1. **N+1查询问题** - 在循环中执行大量数据库查询
2. **复杂SQL查询** - 5表JOIN查询性能低下
3. **缓存策略不当** - 缓存时间过短，命中率低
4. **重复查询** - 相同数据被多次查询

## 文件结构

```
application/
├── common/net_service/
│   └── NetGoodsOptimized.php          # 优化后的商品服务类
├── index_v2/controller/
│   └── lzx1.php                       # 测试控制器（新增测试方法）
└── index_v2/view/lzx1/
    └── performance_test.html           # 性能测试页面
```

## 主要优化点

### 1. 消除N+1查询问题

**原问题：**
```php
foreach ($list as $k => $v) {
    // 每个商品都要执行多次数据库查询
    $de_sku_id_arr = $this->sku_model->alias('a')
        ->join('t_db_commodity_set_sku b','a.id=b.commodity_sku_id')
        ->where(['b.id'=>['in',$gc_id_arr]])
        ->select();
}
```

**优化方案：**
```php
// 批量收集所有需要查询的ID
$all_gc_ids = [];
foreach ($list as $v) {
    $gc_id_arr = explode(',', $v['gc_id']);
    $all_gc_ids = array_merge($all_gc_ids, $gc_id_arr);
}

// 一次性批量查询所有SKU数据
$batch_sku_data = $this->getSkuDataBatch(array_unique($all_gc_ids));

// 在循环中使用预查询的数据
foreach ($list as $k => $v) {
    $de_sku_id_arr = $batch_sku_data[$v['commodity_set_id']] ?? [];
}
```

### 2. 优化复杂SQL查询

**原问题：**
- 5表JOIN查询
- 复杂的GROUP BY操作
- 缺少合适的索引

**优化方案：**
- 将复杂JOIN拆分为多个简单查询
- 使用批量查询替代复杂关联
- 添加合适的数据库索引

### 3. 改进缓存策略

**原缓存：**
```php
redis($user_redis_name, $all_list, 15); // 只缓存15秒
```

**优化缓存：**
```php
// 多层缓存策略
Cache::set($base_cache_key, $base_data, 3600);     // 基础数据缓存1小时
Cache::set($user_cache_key, $user_data, 600);      // 用户数据缓存10分钟
Cache::set($realtime_cache_key, $realtime_data, 60); // 实时数据缓存1分钟
```

## 使用方法

### 1. 性能测试

访问测试页面：
```
http://your-domain/index_v2/lzx1/performanceTestPage?user_token=lzx123&user_data=0814
```

或直接调用API：
```
http://your-domain/index_v2/lzx1/goodsListPerformanceTest?user_token=lzx123&user_data=0814
```

### 2. 在代码中使用优化版本

```php
use app\common\net_service\NetGoodsOptimized;

$netGoodsOptimized = new NetGoodsOptimized();
$result = $netGoodsOptimized->goodsListOptimized($requestData, $user, $channel_type);
```

### 3. 降级机制

优化版本包含自动降级机制，当出现异常时会自动回退到原始方法：

```php
try {
    // 执行优化逻辑
    return $this->optimizedLogic();
} catch (\Exception $e) {
    Logger::error('Optimization failed', ['error' => $e->getMessage()]);
    // 降级到原方法
    return $this->goodsList($requestData, $user, $channel_type, $where, $from, $type);
}
```

## 性能提升预期

根据优化策略，预期性能提升：

- **查询时间减少：** 60-80%
- **内存使用减少：** 40-50%
- **并发能力提升：** 3-5倍
- **缓存命中率：** 提升到85%以上

## 监控建议

### 1. 性能监控
```php
Logger::info('goodsList optimized completed', [
    'execution_time' => number_format(microtime(true) - $api_start_at, 4),
    'goods_count' => count($processed_goods),
    'user_id' => $user['id'] ?? 0
]);
```

### 2. 数据库监控
- 监控慢查询（>100ms）
- 监控数据库连接池使用情况
- 监控索引使用效率

### 3. 缓存监控
- 监控缓存命中率
- 监控缓存过期策略效果
- 监控Redis内存使用

## 数据库优化建议

### 推荐索引
```sql
-- 商品平铺表索引
ALTER TABLE t_db_commodity_flat ADD INDEX idx_channel_enable_stock (up_down_channel_dlr, is_enable, count_stock);

-- 商品套装SKU表索引
ALTER TABLE t_db_commodity_set_sku ADD INDEX idx_set_enable_price (commodity_set_id, is_enable, price);

-- 商品SKU表索引
ALTER TABLE t_db_commodity_sku ADD INDEX idx_enable_car (is_enable, relate_car_ids);
```

### 查询优化
- 避免SELECT *，只查询需要的字段
- 使用LIMIT限制返回结果数量
- 合理使用索引覆盖查询

## 注意事项

1. **渐进式部署：** 建议先在测试环境验证，然后逐步在生产环境部署
2. **监控告警：** 部署后密切监控性能指标和错误日志
3. **回滚准备：** 保持原方法可用，必要时可快速回滚
4. **数据一致性：** 确保优化后的数据与原方法保持一致

## 测试用例

测试页面提供了以下测试场景：

1. **基础查询测试**
2. **搜索功能测试**
3. **分类筛选测试**
4. **价格筛选测试**
5. **大数据量测试**
6. **缓存效果测试**

## 故障排查

### 常见问题

1. **优化方法执行失败**
   - 检查日志中的错误信息
   - 验证数据库连接是否正常
   - 确认缓存服务是否可用

2. **性能提升不明显**
   - 检查数据库索引是否正确创建
   - 验证缓存是否正常工作
   - 确认测试数据量是否足够

3. **数据不一致**
   - 对比原方法和优化方法的返回结果
   - 检查数据处理逻辑是否正确
   - 验证缓存数据是否过期

### 日志分析

关键日志位置：
- 性能日志：`Logger::info('goodsList optimized completed')`
- 错误日志：`Logger::error('goodsList optimized error')`
- 缓存日志：`Logger::info('goodsList cache hit')`

## 后续优化方向

1. **异步处理：** 将非关键数据异步加载
2. **读写分离：** 使用读库进行查询操作
3. **分布式缓存：** 使用Redis集群提高缓存可用性
4. **数据预热：** 定期预热热点数据到缓存
5. **CDN加速：** 对静态资源使用CDN加速
