#!/bin/bash

# 合并当前分支到 feat-sit 的便捷脚本
# 使用方法: ./merge-to-sit.sh "提交信息"

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否提供了提交信息
if [ $# -eq 0 ]; then
    print_error "请提供提交信息"
    echo "使用方法: $0 \"提交信息\""
    exit 1
fi

COMMIT_MESSAGE="$1"

# 获取当前分支名
CURRENT_BRANCH=$(git branch --show-current)
print_info "当前分支: $CURRENT_BRANCH"

# 检查是否在 feat-sit 分支上
if [ "$CURRENT_BRANCH" = "feat-sit" ]; then
    print_error "当前已在 feat-sit 分支上，请切换到要合并的分支"
    exit 1
fi

# 检查工作目录状态
if ! git diff-index --quiet HEAD --; then
    print_info "检测到未提交的更改，正在添加并提交..."
    
    # 显示将要提交的文件
    print_info "将要提交的文件:"
    git status --porcelain
    
    # 添加所有更改
    git add -A
    
    # 提交更改
    git commit -m "$COMMIT_MESSAGE"
    print_success "已提交更改到分支 $CURRENT_BRANCH"
else
    print_info "工作目录干净，无需提交"
fi

# 推送当前分支到远程（如果需要）
print_info "推送当前分支到远程..."
if git push origin "$CURRENT_BRANCH" 2>/dev/null; then
    print_success "已推送 $CURRENT_BRANCH 到远程"
else
    print_warning "推送失败或分支已是最新"
fi

# 切换到 feat-sit 分支
print_info "切换到 feat-sit 分支..."
git checkout feat-sit

# 拉取最新的 feat-sit 分支
print_info "拉取最新的 feat-sit 分支..."
git pull origin feat-sit

# 合并当前分支到 feat-sit
print_info "合并 $CURRENT_BRANCH 到 feat-sit..."
if git merge "$CURRENT_BRANCH" --no-edit; then
    print_success "成功合并 $CURRENT_BRANCH 到 feat-sit"
else
    print_error "合并失败，可能存在冲突"
    print_info "请手动解决冲突后运行: git commit"
    exit 1
fi

# 推送 feat-sit 分支到远程
print_info "推送 feat-sit 分支到远程..."
if git push origin feat-sit; then
    print_success "已推送 feat-sit 到远程"
else
    print_error "推送 feat-sit 失败"
    exit 1
fi

# 切换回原分支
print_info "切换回原分支 $CURRENT_BRANCH..."
git checkout "$CURRENT_BRANCH"

print_success "操作完成！"
print_info "总结:"
echo "  ✓ 提交了 $CURRENT_BRANCH 分支的更改"
echo "  ✓ 将 $CURRENT_BRANCH 合并到 feat-sit"
echo "  ✓ 推送了 feat-sit 到远程"
echo "  ✓ 切换回 $CURRENT_BRANCH 分支"
