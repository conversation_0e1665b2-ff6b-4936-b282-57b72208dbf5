#!/bin/bash

# 快速合并到 feat-sit 的简化脚本
# 使用方法: ./quick-merge.sh "提交信息"

if [ $# -eq 0 ]; then
    echo "使用方法: $0 \"提交信息\""
    exit 1
fi

COMMIT_MESSAGE="$1"
CURRENT_BRANCH=$(git branch --show-current)

echo "🚀 开始合并流程..."
echo "📍 当前分支: $CURRENT_BRANCH"

# 如果有未提交的更改，则提交
if ! git diff-index --quiet HEAD --; then
    echo "📝 提交当前更改..."
    git add -A && git commit -m "$COMMIT_MESSAGE"
fi

# 推送当前分支
echo "⬆️  推送当前分支..."
git push origin "$CURRENT_BRANCH" || true

# 切换并合并
echo "🔄 切换到 feat-sit 并合并..."
git checkout feat-sit
git pull origin feat-sit
git merge "$CURRENT_BRANCH" --no-edit

# 推送 feat-sit
echo "⬆️  推送 feat-sit..."
git push origin feat-sit

# 切换回原分支
git checkout "$CURRENT_BRANCH"

echo "✅ 完成！已将 $CURRENT_BRANCH 合并到 feat-sit"
