-- order-detail 新增  pro_gress  [0=>'',1=>'同意合同',2=>'已支付+待受理',3=>'受理中',4=>'合同已寄出',5=>'合同回寄',6=>'投保成功',99=>'不展示']  -- ==4的时候订单详情可显示查看物流
-- order-detail 新增 pro_gress_info
-- order-fill (交车页过来）新增 buy_car_no 合同号  car_config_code：18位码 ;is_nev 是否电车：0-否，1-是
-- order/confirm 新增返参 order.liability_clause ,order.liability_clause_pdf
-- goods/detail 新增返参 liability_clause ,liability_clause_pdf  协议
-- 商品详情新增入参（通过交车页过来） buy_car_no 购车订单号，car_config_code 18位码 dd_dlr_code 到店
-- 商品详情新增返回 can_change_dlr 1可以修改门店0不能
-- /net-small/express 承运公司列表使用
-- order_source 42=>'日产充电桩',43=>'延保服务包商品'
-- 新增 /net-small/order/return-ship 回寄接口
-- 新增 net-small/order/checkBuyCarOrder 检查购车订单号是否有订单
-- 新增 /net-small/order/logistics-info  延保查看物流 接口，文档新增 物流信息跟订单信息无关了、
--新增 /net-small/goods/check-vin-sku判断规格是否可买


ALTER TABLE `t_db_commodity`
    ADD COLUMN `liability_clause` varchar(100) NOT NULL DEFAULT '' COMMENT '责任条款名称(NEV车型)',
    ADD COLUMN `liability_clause_pdf` text NOT NULL COMMENT '责任条款(NEV车型)',
    ADD COLUMN `no_nev_liability_clause`     varchar(100) NOT NULL DEFAULT '' COMMENT '责任条款名称(非NEV车型)',
    ADD COLUMN `no_nev_liability_clause_pdf` text NOT NULL COMMENT '责任条款(非NEV车型)';

alter table t_bu_order
    add COLUMN `car_config_code` varchar(128) NOT NULL DEFAULT '' COMMENT '18位码',
    ADD COLUMN `is_nev` int(1) NOT NULL DEFAULT '0' COMMENT '是否电车：0-否，1-是',
    add COLUMN `contract_no` varchar(128) NOT NULL DEFAULT '' COMMENT '交车订单号--延保',
    ADD COLUMN vehicle_order_no varchar(128) NOT NULL DEFAULT '' COMMENT '整车订单号--充电桩';
alter table t_bu_order_change
    add COLUMN `car_config_code` varchar(128) NOT NULL DEFAULT '' COMMENT '18位码',
    ADD COLUMN `is_nev` int(1) NOT NULL DEFAULT '0' COMMENT '是否电车：0-否，1-是',
    add COLUMN `contract_no` varchar(128) NOT NULL DEFAULT '' COMMENT '交车订单号--延保',
    ADD COLUMN vehicle_order_no varchar(128) NOT NULL DEFAULT '' COMMENT '整车订单号--充电桩';

ALTER TABLE t_bu_shopping_cart
    ADD COLUMN vehicle_order_no varchar(128) NOT NULL DEFAULT '' COMMENT '整车订单号';

CREATE TABLE `t_bu_order_insure`
(
    `id`                    bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `order_code`            varchar(52)  NOT NULL COMMENT '订单编码',
    `waybill_number`        varchar(50)  NOT NULL DEFAULT '' COMMENT '运单编号',
    `common_carrier`        varchar(30)  NOT NULL DEFAULT '' COMMENT '承运公司',
    `carrier_code`          varchar(30)  NOT NULL DEFAULT '' COMMENT '承运公司编码',
    `delivery_time`         datetime              DEFAULT NULL COMMENT '发货时间',
    `return_waybill_number` varchar(50)  NOT NULL DEFAULT '' COMMENT '回寄运单编号',
    `return_common_carrier` varchar(30)  NOT NULL DEFAULT '' COMMENT '回寄承运公司',
    `return_carrier_code`   varchar(30)  NOT NULL DEFAULT '' COMMENT '回寄承运公司编码',
    `return_delivery_time`  datetime              DEFAULT NULL COMMENT '回寄发货时间',
    `return_address`        varchar(150) NOT NULL DEFAULT '' COMMENT '回寄收货地址',
    `return_name`           varchar(30)  NOT NULL DEFAULT '' COMMENT '回寄收货姓名',
    `return_phone`          varchar(20)  NOT NULL DEFAULT '' COMMENT '回寄手机号码',
    `is_agree`              tinyint(4) DEFAULT '0' COMMENT '是否勾选协议 1是0否',
    `is_enable`             tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '是否有效 0失效 1有效',
    `creator`               varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人',
    `created_date`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`              varchar(50)  NOT NULL DEFAULT '' COMMENT '修改人',
    `modified_date`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `order_code_idx` (`order_code`),
    KEY                     `waybill_number_idx` (`waybill_number`),
    KEY                     `return_waybill_number_idx` (`return_waybill_number`)
) ENGINE=InnoDB COMMENT='订单维保发货表';

CREATE TABLE `t_bu_order_return_address`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `return_name`    varchar(30)  NOT NULL DEFAULT '' COMMENT '回寄收货姓名',
    `return_phone`   varchar(20)  NOT NULL DEFAULT '' COMMENT '回寄手机号码',
    `return_address` varchar(150) NOT NULL DEFAULT '' COMMENT '回寄收货地址',
    `is_enable`      tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '是否有效 0失效 1有效',
    `creator`        varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人',
    `created_date`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`       varchar(50)  NOT NULL DEFAULT '' COMMENT '修改人',
    `modified_date`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
)ENGINE=InnoDB COMMENT='订单维保回寄地址表';


CREATE TABLE `t_e3s_delay_insurance`
(
    `id`                          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `car_series_code`             varchar(20)  NOT NULL DEFAULT '' COMMENT '车系编码',
    `car_series_name`             varchar(20)  NOT NULL DEFAULT '' COMMENT '延保的车系名称',
    `n_car_type`                  varchar(20)  NOT NULL DEFAULT '' COMMENT '延保适用车型',
    `n_car_type_remark`           varchar(200) NOT NULL DEFAULT '' COMMENT '适用车型说明',
    `product_id`                  varchar(20)  NOT NULL DEFAULT '' COMMENT '延保产品id id存code',
    `n_product_code`              varchar(50)  NOT NULL DEFAULT '' COMMENT '延保产品编号 code存id',
    `n_product_name`              varchar(30)  NOT NULL DEFAULT '' COMMENT '延保产品名称',
    `guarantee_month_num`         varchar(30)  NOT NULL DEFAULT '' COMMENT '延保时间（月）',
    `n_guarantee_condition`       varchar(200) NOT NULL DEFAULT '' COMMENT '延保购买条件',
    `n_guarantee_content`         varchar(200) NOT NULL DEFAULT '' COMMENT '延保内容',
    `dlr_price`                   decimal(11, 2) unsigned NOT NULL COMMENT '专营店价格',
    `use_price`                   decimal(11, 2) unsigned NOT NULL COMMENT '用户价',
    `pro_guarantee_type_code`     varchar(30)  NOT NULL DEFAULT '' COMMENT '延保产品种类编码',
    `pro_guarantee_pro_type_name` varchar(30)  NOT NULL DEFAULT '' COMMENT '延保产品种类名称',
    `service_kind_code`           varchar(30)  NOT NULL DEFAULT '' COMMENT '服务种类编码',
    `service_kind_name`           varchar(30)  NOT NULL DEFAULT '' COMMENT '服务种类名称',
    `part_no`                     varchar(20)  NOT NULL DEFAULT '' COMMENT '备件编码',
    `start_month_qty`             varchar(20)  NOT NULL DEFAULT '' COMMENT '适用月数开始',
    `end_month_qty`               varchar(20)  NOT NULL DEFAULT '' COMMENT '适用月数结束',
    `start_mile_qty`              varchar(20)  NOT NULL DEFAULT '' COMMENT '适用历程数开始',
    `end_mile_qty`                varchar(20)  NOT NULL DEFAULT '' COMMENT '适用历程数结束',
    `car_brand_code`              varchar(20)  NOT NULL DEFAULT '' COMMENT '品牌编码',
    `car_brand_cn`                varchar(20)  NOT NULL DEFAULT '' COMMENT '品牌名称',
    `e3s_update_no`               varchar(100) NOT NULL DEFAULT '' COMMENT '更新编号',
    `is_enable`                   tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '是否有效 0失效 1有效',
    `creator`                     varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人',
    `created_date`                datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`                    varchar(50)  NOT NULL DEFAULT '' COMMENT '修改人',
    `modified_date`               datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY                           `e3s_update_no` (`e3s_update_no`) USING BTREE,
    UNIQUE KEY `idx_car_series_code_n_product_code` (`car_series_code`,`n_product_code`) USING BTREE
)ENGINE=InnoDB COMMENT='延保服务套餐';


CREATE TABLE `t_e3s_delay_insurance_log`
(
    `id`                          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `car_series_code`             varchar(20)  NOT NULL DEFAULT '' COMMENT '车系编码',
    `car_series_name`             varchar(20)  NOT NULL DEFAULT '' COMMENT '延保的车系名称',
    `n_car_type`                  varchar(20)  NOT NULL DEFAULT '' COMMENT '延保适用车型',
    `n_car_type_remark`           varchar(200) NOT NULL DEFAULT '' COMMENT '适用车型说明',
    `product_id`                  varchar(20)  NOT NULL DEFAULT '' COMMENT '延保产品id id存code',
    `n_product_code`              varchar(50)  NOT NULL DEFAULT '' COMMENT '延保产品编号 code存id',
    `n_product_name`              varchar(30)  NOT NULL DEFAULT '' COMMENT '延保产品名称',
    `guarantee_month_num`         varchar(30)  NOT NULL DEFAULT '' COMMENT '延保时间（月）',
    `n_guarantee_condition`       varchar(200) NOT NULL DEFAULT '' COMMENT '延保购买条件',
    `n_guarantee_content`         varchar(200) NOT NULL DEFAULT '' COMMENT '延保内容',
    `dlr_price`                   decimal(11, 2) unsigned NOT NULL COMMENT '专营店价格',
    `use_price`                   decimal(11, 2) unsigned NOT NULL COMMENT '用户价',
    `pro_guarantee_type_code`     varchar(30)  NOT NULL DEFAULT '' COMMENT '延保产品种类编码',
    `pro_guarantee_pro_type_name` varchar(30)  NOT NULL DEFAULT '' COMMENT '延保产品种类名称',
    `service_kind_code`           varchar(30)  NOT NULL DEFAULT '' COMMENT '服务种类编码',
    `service_kind_name`           varchar(30)  NOT NULL DEFAULT '' COMMENT '服务种类名称',
    `part_no`                     varchar(20)  NOT NULL DEFAULT '' COMMENT '备件编码',
    `start_month_qty`             varchar(20)  NOT NULL DEFAULT '' COMMENT '适用月数开始',
    `end_month_qty`               varchar(20)  NOT NULL DEFAULT '' COMMENT '适用月数结束',
    `start_mile_qty`              varchar(20)  NOT NULL DEFAULT '' COMMENT '适用历程数开始',
    `end_mile_qty`                varchar(20)  NOT NULL DEFAULT '' COMMENT '适用历程数结束',
    `car_brand_code`              varchar(20)  NOT NULL DEFAULT '' COMMENT '品牌编码',
    `car_brand_cn`                varchar(20)  NOT NULL DEFAULT '' COMMENT '品牌名称',
    `e3s_update_no`               varchar(100) NOT NULL DEFAULT '' COMMENT '更新编号',
    `is_enable`                   tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '是否有效 0失效 1有效',
    `creator`                     varchar(50)  NOT NULL DEFAULT '' COMMENT '创建人',
    `created_date`                datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modifier`                    varchar(50)  NOT NULL DEFAULT '' COMMENT '修改人',
    `modified_date`               datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY                           `n_product_code` (`n_product_code`) USING BTREE,
    KEY                           `e3s_update_no` (`e3s_update_no`) USING BTREE
)ENGINE=InnoDB COMMENT='延保服务套餐日志表';


