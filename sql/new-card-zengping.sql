-- 先判断用户有没有那两种售后券，然后再判断售后券有没有关联活动，然后再判断关联的活动有没有关联主品
-- 新增接口goods/gift_card 返参同goodslist,新增de_sku_id, sp_name
-- 新增接口 /net-small/goods/gift_card_num 返回可选赠品数，
-- change-suit新增 gift_card_id 入参 ==买赠券ID
-- 购物车两个cart新增 gift_card_id
-- 购物车列表新增  is_gift_card , gift_card_desc ,gift_card_change_info
-- fill参数新增 gift_card_id ,gift_c_json_list
-- 购物车列表：gift_c_json+gift_act_id 显示赠品样式  gift_c_json+gift_card_id 显示买赠券赠品列表
-- 主品只要有赠品券，就需要传赠品gift_card_id 到fill


CREATE TABLE `t_db_activity_ass_sku` (
         `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
         `activity_id` varchar(20) NOT NULL DEFAULT '' COMMENT '活动id',
         `sku_code` varchar(32) NOT NULL DEFAULT '' COMMENT '关联备件编码-E3S -存1备件，2套餐 ',
         `sku_class_code` varchar(32) NOT NULL DEFAULT '' COMMENT '关联备件分类-E3S',
         `sku_type` int(11) DEFAULT '1' COMMENT '关联类型  1备件，2套餐 3商品 4分类',
         `is_enable` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '是否有效 0失效 1有效',
         `creator` varchar(10) DEFAULT NULL COMMENT '创建人',
         `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
         `modifier` varchar(10) DEFAULT NULL COMMENT '修改人',
         `modified_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
         `commodity_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联商品id',
         PRIMARY KEY (`id`),
         KEY `activity_id_idx` (`activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1  COMMENT='活动关联主品备件分类信息';



ALTER TABLE `t_bu_order_commodity` ADD COLUMN `gift_card_type` int(3) NULL DEFAULT 0 COMMENT '是否买赠券商品：0-否；1-主品；2-赠品';
ALTER TABLE `t_bu_order_commodity` ADD COLUMN   `pre_card_code` varchar(500) DEFAULT '' COMMENT '预占卡券code--买赠券';
ALTER TABLE t_bu_order_commodity add COLUMN   `gift_card_id` VARCHAR(58)  NOT NULL DEFAULT '' COMMENT '赠品券ID';

ALTER TABLE t_bu_card_receive_record add INDEX r_vin (`receive_vin`);

ALTER TABLE t_bu_shopping_cart add COLUMN   `gift_card_id` VARCHAR(58)  NOT NULL DEFAULT '' COMMENT '赠品券ID';


alter TABLE `t_e3s_spare_part_log`
    add COLUMN `variety_code` varchar(50) NOT NULL DEFAULT '' COMMENT '备件分类编码',
    add COLUMN `variety_name` varchar(50) NOT NULL DEFAULT '' COMMENT '备件分类名称';

alter TABLE `t_e3s_spare_part`
    add COLUMN `variety_code` varchar(50) NOT NULL DEFAULT '' COMMENT '备件分类编码',
    add COLUMN `variety_name` varchar(50) NOT NULL DEFAULT '' COMMENT '备件分类名称';


alter TABLE `t_db_card`
    add COLUMN `is_gift_card` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否是赠品券 0-否  1-是',
    add COLUMN `variety_code`  text  COMMENT '备件分类编码,多个以，隔开';


alter TABLE `t_db_commodity_sku`
    add COLUMN `variety_code`  varchar(50) NOT NULL DEFAULT '' COMMENT '备件分类编码',
    add COLUMN `variety_name` varchar(50) NOT NULL DEFAULT '' COMMENT '备件分类名称';


ALTER TABLE `t_db_commodity_sku` ADD INDEX idx_variety_code (variety_code);


-- e3s备件基础数据同步完成后  执行php脚本   php think add_variety_code  同步商品sku表的备件分类编码


CREATE TABLE `t_bu_order_gift` (
     `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
     `parent_order_code` varchar(52) NOT NULL COMMENT '主品订单编码',
     `activity_id` varchar(20) NOT NULL DEFAULT '' COMMENT '活动id',
     `card_id` varchar(32) NOT NULL DEFAULT '' COMMENT '卡券id',
     `card_receive_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '卡券领取记录id',
     `order_commodity_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单商品表主品id',
     `card_code` varchar(32) NOT NULL DEFAULT '' COMMENT '领券后的卡券号码',
     `coupon_code` varchar(50) NOT NULL DEFAULT '' COMMENT '卡券中心核销码',
     `gift_order_code` varchar(52) NOT NULL DEFAULT '' COMMENT '赠品订单编码',
     `dd_dlr_code` varchar(10) NOT NULL DEFAULT '' COMMENT '到店专营店编码',
     `remarks` text DEFAULT '' COMMENT '备注',
     `is_enable` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '是否有效 0失效 1有效',
     `creator` varchar(10) NOT NULL DEFAULT '' COMMENT '创建人',
     `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `modifier` varchar(10) NOT NULL DEFAULT '' COMMENT '修改人',
     `modified_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
     PRIMARY KEY (`id`),
     KEY `idx_parent_order_code` (`parent_order_code`),
     KEY `idx_card_id` (`card_id`),
     KEY `idx_card_receive_id` (`card_receive_id`),
     KEY `idx_order_commodity_id` (`order_commodity_id`),
     KEY `idx_card_code` (`card_code`),
     KEY `idx_gift_order_code` (`gift_order_code`)
) ENGINE=InnoDB COMMENT='赠品订单';


ALTER TABLE `t_bu_card_receive_record` ADD INDEX idx_activity_id (activity_id);
alter table t_bu_shopping_cart add COLUMN   `gift_card_c_json` text COMMENT '赠品信息JSON';
alter table t_bu_shopping_cart_change
    add COLUMN   `gift_card_c_json` text COMMENT '赠品信息JSON',
    add COLUMN   `gift_card_id` varchar(58) NOT NULL DEFAULT '' COMMENT '赠品券ID',
    add COLUMN   `source_special` varchar(100) DEFAULT '' COMMENT '专题来源';

CREATE TABLE `t_db_commodity_sub` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `commodity_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品id',
    `commodity_set_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品上架id',
    `group_sub_commodity_id` int(11) NOT NULL DEFAULT '0' COMMENT '子商品id',
    `group_sub_commodity_set_id` int(11) NOT NULL DEFAULT '0' COMMENT '子商品上架id',
    `is_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可用',
    `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator` varchar(10) DEFAULT NULL COMMENT '创建人',
    `last_updated_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `modifier` varchar(10) DEFAULT NULL COMMENT '修改人',
    PRIMARY KEY (`id`),
    KEY `idx_commodity_id` (`commodity_id`),
    KEY `idx_commodity_set_id` (`commodity_set_id`),
    KEY `idx_group_sub_commodity_id` (`group_sub_commodity_id`)
) ENGINE=InnoDB  COMMENT='主-子商品对应表';


ALTER TABLE `t_db_after_sale_orders_change`
add COLUMN `jd_warehouse_after_id` varchar(100) NOT NULL DEFAULT '0' COMMENT 'JD退货id',
add COLUMN `jd_warehouse_send_id` varchar(100) NOT NULL DEFAULT '0' COMMENT 'JD发货id',
add COLUMN `jd_ware_remark` varchar(500) NOT NULL DEFAULT '' COMMENT '云仓备注';



ALTER TABLE `t_db_activity`
ADD COLUMN `coupon_invalid_scene_list` text  NULL COMMENT '失效场景编码',
ADD COLUMN `push_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '推送类型 0-无 1-精推 2-全推';

ALTER TABLE `t_bu_order` ADD COLUMN `e3s_vin` varchar(30) NOT NULL DEFAULT '' COMMENT 'e3s vin';
ALTER TABLE `t_bu_order_change` ADD COLUMN `e3s_vin` varchar(30) NOT NULL DEFAULT '' COMMENT 'e3s vin';
