/**
 * Created by lyj on 2017/11/23.
 */



    //选择时间清除选择的商品
    $(".datetimepicker3").datetimepicker({
        format:"YYYY-MM-DD HH:mm:ss",
        locale: moment.locale('zh-cn'),
    }).on('dp.change', function(e){
        if(e.date){
            if( $("table #haved-commodtiy tr:visible").length ){
                var index = layer.open({
                    title: ['操作提醒'],
                    btn: ['确认', '取消'],
                    content:"<div style='font-size: 15px'>更改活动时间会清空下方添加商品列表</div>",
                    yes: function (res) {
                        layer.close(index)
                        $("#haved-commodtiy tr").remove()
                        var start_time = $("#start_time").val();
                        var end_time = $("#end_time").val();
                        var url = ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time
                        initComm(url);
                        // initComm(ajaxCommUrl);
                    }
                })
            }else{
                var start_time = $("#start_time").val();
                var end_time = $("#end_time").val();
                var url = ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time
                initComm(url);
            }
        }
    });

    // 秒杀持续时间段
    $(".daypicker3").datetimepicker({
        format:"YYYY-MM-DD",
        locale: moment.locale('zh-cn'),
    }).on('dp.change', function(e){
        if(e.date){
            if( $("table #haved-commodtiy tr:visible").length ){
                var index = layer.open({
                    title: ['操作提醒'],
                    btn: ['确认', '取消'],
                    content:"<div style='font-size: 15px'>更改活动时间会清空下方添加商品列表</div>",
                    yes: function (res) {
                        layer.close(index)
                        $("#haved-commodtiy tr").remove()
                        var start_time = $("#repetition_start_time").val();
                        var end_time = $("#repetition_end_time").val();
                        var url = ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time
                        initComm(url);
                        // initComm(ajaxCommUrl);
                    }
                })
            }else{
                var start_time = $("#repetition_start_time").val();
                var end_time = $("#repetition_end_time").val();
                var url = ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time
                initComm(url);
            }
        }
    });

    // 秒杀重复时间
    $(".timepicker3").datetimepicker({
        format:"HH:mm:ss",
        locale: moment.locale('zh-cn'),
    }).on('dp.change', function(e){
        if(e.date){
            if( $("table #haved-commodtiy tr:visible").length ){
                var index = layer.open({
                    title: ['操作提醒'],
                    btn: ['确认', '取消'],
                    content:"<div style='font-size: 15px'>更改活动时间会清空下方添加商品列表</div>",
                    yes: function (res) {
                        layer.close(index)
                        $("#haved-commodtiy tr").remove()
                        var start_time = $("#repetition_start_time").val();
                        var end_time = $("#repetition_end_time").val();
                        var url = ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time
                        initComm(url);
                        // initComm(ajaxCommUrl);
                    }
                })
            }else{
                var start_time = $("#repetition_start_time").val();
                var end_time = $("#repetition_end_time").val();
                var url = ajaxCommUrl+'&start_time='+start_time+'&end_time='+end_time
                initComm(url);
            }
        }
    });

    //初始化
    $(".default-select2").select2({width:'150px'});


    /*------------------- begin 添加商品、分类 -----------------------*/
    // initComm(ajaxCommUrl);
    //操作类型切换
    $("[name='operation_type']").on('change',function(){
        $(this).closest('.with-border').find("[name='operation_type_data']").val('');
    });

    var comm_checked_id = [];       //商品id
    var comm_tab_tr = [];       //商品表单tr

    //搜索商品列表
    $("#comm-type-search").on('click',function(){
        var obj = $(this).parents('.form-group');
        var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
        var sub_comm_type_id = obj.find("select[name='sub_comm_type_id']").val();
        var three_comm_type_id = obj.find("select[name='three_comm_type_id']").val();
        var commodity_name = obj.find("input[name='commodity_name']").val();
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var start_time = $("#start_time").val();
        var end_time = $("#end_time").val();
        var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+ sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+ commodity_name+'&commodity_class='+commodity_class+'&start_time='+start_time+'&end_time='+end_time;
        initComm(param);
    });

    function initComm(url) {
        var user_segment = $("*[name='user_segment']:checked").val();
        if (user_segment != undefined) {
            url = url+'&user_segment='+user_segment;
        }

        $.getJSON(url, null, function (resData) {
            createPageComm(5, 10, resData.data.list.total, url);//创建翻页功能按钮，翻
            $("#add-comm-tbody").empty();
            if (resData.data.list.total > 0) {                          //页向后台请求连接
                setComm(url);
            }
        });
    }
    /*  function initCommType(url) {
     $.getJSON(url, null, function (resData) {
     createPageCommType(5, 10, resData.data.total, url);//创建翻页功能按钮，翻
     $("#add-comm-type-tbody").empty();
     if (resData.data.total > 0) {                          //页向后台请求连接
     setCommType(url);
     }
     });
     }*/
    function createPageComm(pageSize, buttons, total, url) {        //contracts_url为点击
        $("#comm-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
            pageSize : pageSize,
            total : total,
            maxPageButton:buttons,
            onPageClicked: function(obj, page) {    //分页事件
                $("#add-comm-tbody").empty();
                setComm(url+"&page="+(page+1));
            }
        });
    }
    /* function createPageCommType(pageSize, buttons, total, url) {        //contracts_url为点击
     $("#comm-type-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
     pageSize : pageSize,
     total : total,
     maxPageButton:buttons,
     onPageClicked: function(obj, page) {    //分页事件
     $("#add-comm-type-tbody").empty();
     setCommType(url+"&page="+(page+1));
     }
     });
     }*/
    //获取并设置商品
    function setComm(param){
        var url = ajaxCommUrl;
        if(param!=null) url=param;
        var user_segment = $("*[name='user_segment']:checked").val();

        $.get(url,function(res){
            var html='';
            var list = res.data.list.data;
            console.log('list:',list);
            var comm_set_id_arr=gethavedCommodityId();

            $.each(list,function(i,val){
                var checkbox_obj = '';

                var index= Number($.inArray(val.commodity_set_id, comm_set_id_arr));
                if (index>-1){
                    var button='<button data-comm-set-id="'+val.commodity_set_id+'" home="'+val.home+'" data-comm-id="'+val.commodity_id+'" '+
                        ' class=" btn btn-default active btn-sm btn-fight" disabled>已添加</button>';
                } else {
                    if(val.is_fight == 1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与团购</button>';
                    }else if(val.is_pre==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与预售</button>';
                    }else if(val.is_limit==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与其他限时折扣</button>';
                    }else if(val.is_seckill==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与其他秒杀活动</button>';
                    }else if(val.is_full ==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与其他满优惠活动</button>';
                    }else if(val.is_n_discount ==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与n件n折活动</button>';
                    }else if(val.is_cheap ==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'"home="'+val.home+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与套装活动</button>';
                    }else if(val.is_gift ==1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与['+val.activity_name+']买赠活动</button>';
                    }else if(user_segment != undefined && val.is_user_segment==2) {
                        var button = '<button data-comm-set-id="' + val.commodity_set_id + '" data-grouped="' + val.is_grouped + '" data-comm-id="' + val.commodity_id + '"' +
                            ' class=" btn btn-white active btn-sm " disabled >定向人群不一致</button>';
                    }else{
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" home="'+val.home+'" data-comm-id="'+val.commodity_id+'" '+
                            ' class=" btn btn-white btn-sm  btn-fight">选择添加</button>';
                    }
                }

                html += '<tr id="comm_tab_tr_'+val.commodity_id+'">' +
                    '<td class="text-left"><image class="cover-image" src="'+val.cover_image+'">'+
                    '<a  class="init-commodity-preview" data-dlr-code="'+val.dlr_code+'" data-commodity-id="'+val.commodity_id+'"> '+val.commodity_name+' </a>' +
                    '</td>' +
                    '<td class="text-center">'+ val.up_down_channel_name +'</td>' +
                    '<td class="text-center">'+ val.price +'</td>' +
                    '<td class="text-center">'+ val.count_stock +'</td>' +
                    '<td class="text-center">' + button +
                    '</td> </tr>';
            });
            $("#add-comm-tbody").html(html);

        },'json');
    }
    //添加商品
    $("#add-comm-tbody").on('click',".btn-fight",function (e) {
        console.log('选择添加')
        e.preventDefault();
        var obj=$(this);
        var home = $(this).attr('home');

        var fhome = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
        if(fhome != home){
            if(fhome == 1){
//                layer.msg('只能添加“快递到家“的商品，该商品为“到店安装”');
//                return false;
            }
            if(fhome == 0){
//                layer.msg('只能添加“到店安装“的商品，该商品为“快递到家”');
//                return false;
            }
        }

        $("#modal_home").val("");
        $("#modal_home").val(home);

        $.getJSON(getSkuUrl,{commodity_id:$(this).data('comm-id'),commodity_set_id:$(this).data('comm-set-id')},function (res) {
            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            $("#comm-name-header").html(res.data.commodity_row.commodity_name);
            var html='';
            console.log('sku_list:',sku_list)
            $.each(sku_list,function(i,val){
                var sku_check='<input type="checkbox" checked class="sku-ckeck" disabled value="6" name="">';
                if(dis_type == 0){
                    var sku_price= '<div class="input-group " style="width: 100px;">'+
                        '<input  type="text" class="form-control sku-price input-sm" value="'+val.price+'" readonly="readonly" value="" data-parsley-required="true"  data-parsley-min="0" data-parsley-pattern-message="格式不正确,请输入不小于0的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'+
                        '<span class="input-group-addon">元</span> </div>';
                }else{
                    var sku_price= '<div class="input-group " style="width: 100px;">'+
                        '<input  type="text" class="form-control sku-price input-sm" readonly="readonly" value="" data-parsley-required="true"  data-parsley-min="0" data-parsley-pattern-message="格式不正确,请输入不小于0的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'+
                        '<span class="input-group-addon">元</span> </div>';
                }
                var stock = '<div>'+
                    '<input  type="hidden" class="form-control stock input-sm" name="stock" value="'+val.stock+'">'+ val.stock+
                    '</div>';
                html += '<tr set_sku_id="'+val.id+'">' +
                    '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                    '<td id="td_price">'+ val.price +'</td>' +
                    '<td style="display: none">'+sku_price+'</td>' +
                    '<td >' + stock +
                    '</td> </tr>';
            });
            $("#sku-modal").find("[name='comm_discount']").val('');
            $("#sku-modal").data("comm-id",obj.data('comm-id'));
            $("#sku-modal").data("comm-set-id",obj.data('comm-set-id'));
            $("#sku-modal").data('type','add');
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');
            //显示立减或者折扣
            if(dis_type==1){
                $("#sku-modal .modal-body").find("[name='comm_discount']").val($("[name='discount']").val());

            }else if(dis_type==2){
                $("#sku-modal .modal-body").find("[name='comm_discount']").val($("[name='dis_money']").val());

            }else if(dis_type==3){
                $("#sku-modal .modal-body").find("[name='comm_discount']").val($("[name='seckill_money']").val());
                // $("#sku-modal .modal-body .form-group").hide();
            }
            //更新折扣价格
            update_sku_price($("#sku-modal").find(".modal-body"));
            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val("").data("sku-dlr","");
        });
    });
    //折扣价设置，自动计算折扣价格
    $("body").on("change","[name='comm_discount']",function(){
        var obj_body = $(this).parents(".modal-body");
        update_sku_price(obj_body);
    });
    //更新折扣价格
    function update_sku_price(obj_body){
        var comm_discount = obj_body.find("[name='comm_discount']").val();
        if(dis_type!=3 && (comm_discount==null || comm_discount<=0)) return false;
        var obj_tr = obj_body.find(".sku-tb").find("tr");
        for(var i=0;i<obj_tr.length;i++){
            var this_val = obj_tr.eq(i);
            //console.log(this_val.html());
            //console.log(this_val.f3    ind("#td_price").text());
            if(dis_type==3){
                if (discount_type == 2){
                    this_val.find(".sku-price").val(this_val.find("#td_price").text());
                }else{
                    this_val.find(".sku-price").val(comm_discount);
                }
            }else {
                if (dis_type == 1) {
                    var sku_price = ((comm_discount / 10) * this_val.find("#td_price").text());

                } else if (dis_type == 2) {
                    var sku_price = this_val.find("#td_price").text() - comm_discount;
                }
                switch (discount_type) {
                    case 1: $('#dis_t_word').text(dis_t_word+'（仅商品）');break;
                    case 2: $('#dis_t_word').text(dis_t_word+'（仅商品工时）');
                        sku_price = this_val.find("#td_price").text();break;
                    case 3: $('#dis_t_word').text(dis_t_word+'（商品+工时）');break;
                }
                this_val.find(".sku-price").val(Math.round(sku_price * 100) / 100);      //保留两位小数
            }
        }
    }

    //查看商品
    $("body").on("click",".btn-fight-view",function (e) {

        e.preventDefault();
        var set_sku_list=$(this).data('sku-list');
        console.log('set_sku_list:',set_sku_list);
        var sku_price_list=set_sku_list.sku_list;
        var sku_stock_list = set_sku_list.sku_stock;
        var seckill_stock = set_sku_list.seckill_stock;
        console.log('seckill_stock:',seckill_stock);
        if( seckill_stock === undefined){
            $("#sku-modal").find("[name='seckill_stock']").val('')
            $("#sku-modal").find("[name='sku_stock']").val(sku_stock_list)
            $("#sku-modal").find("[name='sku_stock']").removeAttr('readonly')
        }else{
            console.log('seckill_stock----'+seckill_stock)
            $("#sku-modal").find("[name='seckill_stock']").val(seckill_stock)
            $("#sku-modal").find("[name='sku_stock']").val(sku_stock_list)
            $("#sku-modal").find("[name='sku_stock']").attr('readonly','true')
        }
        if(dis_type==1){
            $("#sku-modal").find("[name='comm_discount']").val($("[name='discount']").val());
        }else if(dis_type==2){
            $("#sku-modal").find("[name='comm_discount']").val($("[name='dis_money']").val());
        }else if(dis_type==3){
            $("#sku-modal").find("[name='comm_discount']").val($("[name='seckill_money']").val());
        }
        var obj=$(this);
        $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {
            var sku_list=res.data.sku_list;
            console.log('sku_list_2:',sku_list)
            console.log('sku_price_list2:',sku_price_list)
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            var html='';

            $.each(sku_list,function(i,val){
                var price='';
                var checked='checked'
                if(sku_price_list[val.id]){
                    var comm_discount = $("#sku-modal").find("[name='comm_discount']").val();
                    if (dis_type == 1) {
                        price =  Math.round(((comm_discount / 10) * val.price) * 100) / 100;
                    } else if (dis_type == 2) {
                        price =  Math.round((val.price - comm_discount) * 100) / 100;

                    }else if(dis_type == 3){
                        price = comm_discount;
                    } else{
                        price  = sku_price_list[val.id];
                    }
                    checked='checked';
                }
                var sku_check='<input type="checkbox" ' +checked+'  class="sku-ckeck" disabled value="6" name="">';
                var sku_price= '<div class="input-group " style="width: 100px;">'+
                    '<input  type="text" class="form-control sku-price input-sm" readonly="readonly" value="'+price+'" data-parsley-required="true"  data-parsley-min="0" data-parsley-pattern-message="格式不正确,请输入不小于0的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'+
                    '<span class="input-group-addon">元</span> </div>';
                var stock = '<div>'+
                    '<input  type="hidden" class="form-control stock input-sm" name="stock" value="'+val.stock+'">'+ val.stock+
                    '</div>';
                html += '<tr set_sku_id="'+val.id+'">' +
                    '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                    '<td id="td_price">'+ val.price +'</td>' +
                    '<td style="display: none">'+sku_price+'</td>' +
                    '<td >' + stock +
                    '</td> </tr>';
            });


            $("#sku-modal").data("comm-id",set_sku_list.commodity_id);
            $("#sku-modal").data("comm-set-id",set_sku_list.commodity_set_id);
            $("#comm-name-header").html(set_sku_list.commodity_name);
            var index=$(".btn-fight-view").index(obj);
            $("#sku-modal").data('type',index);
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');

            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val(set_sku_list.dlr_name).attr("data-sku-dlr",set_sku_list.dlr_code);
        });
        //alert($(this).data("sku-list"));
    })

    //批量设置价格
    $('#all_price').editable({
        success: function(response, newValue) {
            $(".sku-price").val(newValue);

            $(".editable-cancel").click();
            return false;
        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9/.]*$/).test(value)) {
                return '格式有误';
            }
        }
    });

    var commodity_list =new Object();

    //sku 点击确认
    $("#sku-confirm").on('click',function (e) {
        var sku_modal=$(this).parents("#sku-modal");
        var home =  sku_modal.find("#modal_home").val();
        var seckill_stock = sku_modal.find("[name='seckill_stock']").val();
        console.log('sku-confirm:seckill_stock',seckill_stock);
        if (seckill_stock !== '') {
            commodity_list.seckill_stock = seckill_stock;
        }
        commodity_list.commodity_id    =sku_modal.data('comm-id');
        commodity_list.commodity_set_id=sku_modal.data('comm-set-id');
        commodity_list.dlr_code        =sku_modal.find(".sku-dlr").attr("data-sku-dlr");
        commodity_list.dlr_name        =sku_modal.find(".sku-dlr").val();
        commodity_list.comm_discount  =sku_modal.find("[name='comm_discount']").val();
        commodity_list.sku_stock  =sku_modal.find("[name='sku_stock']").val();
        var commodity_name = sku_modal.find("#comm-name-header").html();
        var escapedString = commodity_name.replace(/'/g, "&#39;").replace(/"/g, '&quot;');
        commodity_list.commodity_name  = escapedString;
        var image=sku_modal.find(".cover-image").attr("src");
        var  commodity_name=sku_modal.find(".sku-comm").html();
        var sku_list={};
        var sku_stock={};
        var check_price=true;

        var user_segment = $("*[name='user_segment']:checked").val();
        console.log('user_segment:',user_segment)
        var is_stock = 0;
        $(".sku-tb tr").each(function () {
            if($(this).find(".sku-ckeck").attr("checked")){
                var set_sku_id=$(this).attr("set_sku_id");
                // var reg=/^0\.([1-9]|\d[0-9])$|^[1-9]\d{0,8}\.\d{0,2}$|^[1-9]\d{0,8}$/
                //if(!reg.test($(this).find(".sku-price").val())){
                var a = parseFloat($(this).find(".sku-price").val());
                console.log('a:',a)
                var reg = /^\d+(?=\.{0,1}\d+$|$)/;
                if(!reg.test(a) && user_segment == undefined){
                    layer.msg('价格格式不对');
                    check_price=false;
                    return false;
                }
                if(a.toFixed(2)<0 && user_segment == undefined){
                    layer.msg('价格格式不对');
                    check_price=false;
                    return false;
                }
                var sku_stock_val = sku_modal.find("[name='sku_stock']").val();
                var stock = parseFloat($(this).find(".stock").val());
                if (!(/(^[1-9]\d*$)/.test(sku_stock_val))) {
                    layer.msg('只能填写1-99999的正整数');
                    check_price=false;
                    return false;
                }
                if(sku_stock_val < stock){
                    is_stock +=1;
                }
                // else{
                    sku_list[set_sku_id]=$(this).find(".sku-price").val();
                //     if($(this).find(".sku_stock").val() == ''){
                //         sku_stock[set_sku_id]=$(this).find(".stock").val();
                //     }else{
                //         sku_stock[set_sku_id]=$(this).find(".sku_stock").val();
                //     }
                // }
            }
        });
        if (!check_price){
            return;
        }
        console.log('sku_list:',sku_list)
        if(JSON.stringify(sku_list) == "{}"){
            layer.msg('请选择规格');
            return;
        }
        console.log('is_stock:'+is_stock)
        if(is_stock == 0){
            layer.msg('秒杀库存不能大于现有库存');
            return;
        }

        // 判断是否是特定人群
      if (user_segment == undefined) {
        if (commodity_list.comm_discount=='' && dis_type!=0){
            layer.msg('请输入优惠力度');
            return ;
        }else if ((commodity_list.comm_discount<0.1 || commodity_list.comm_discount>9.9 || isNaN(parseFloat(commodity_list.comm_discount))) && dis_type==1){
            // console.info(dis_type);
            layer.msg('请输入0.1-9.9范围的折扣价');
            return ;
        }
       }
        if(sku_modal.find("[name='sku_stock']").val() == ''){
            layer.msg('库存不能为空');
            return ;
        }
        commodity_list.sku_list=sku_list;
        //新增数据添加到后面
        var json_st=JSON.stringify(commodity_list);
        var type   =sku_modal.data('type');
        //添加

        if (type=='add'){
            var html='<tr class="info haved-add" data><td style="width: 350px;"><input type="hidden" name="home"  class="home" value="'+home+'"/><img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button></td></tr>';
            //alert(html);
            $("#haved-commodtiy").append(html);
            $("#sku-modal").find("[name='sku_stock']").val('')
            console.log("添加商品："+html)

        }else {  //查看
            //
            var html='<td style="width: 350px;"><img class="cover-image" src="'+image+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list ">删除</button></td>';
            $(".btn-fight-view").eq(type).parents('tr').html(html);
            $("#sku-modal").find("[name='sku_stock']").val('')
            console.log('sku-confirm:查看')
        }

        //专营店 禁用添加按钮
        if(admin_type==2 || admin_type==1){
            var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
            btn_obj.removeClass("btn-white").addClass("btn-default active").attr("disabled",true).html('已添加');
            //btn_obj.parents("button").html('已添加');
            console.log(btn_obj);
        }
        $("#sku-modal").modal('hide');

        console.log(commodity_list);
    })

    /**********删除sku************/
    $("body").on("click",".del-sku-list",function (e) {
        e.preventDefault();
        var sku_list=$(this).parents("tr").find(".btn-fight-view").data("sku-list");
        var type=$(".del-sku-list").index($(this));
        $("#del-sku-modal").find("#del-data-id").val(type).data('comm-set-id',sku_list.commodity_set_id);
        $("#del-sku-modal").modal('show');
    })

    $("#del-confirm").on("click",function () {
        $(".del-sku-list").eq($("#del-sku-modal").find("#del-data-id").val()).parents('tr').remove();
        $("#del-sku-modal").modal('hide');
        //专营店端修改添加按钮
        if(admin_type==2 || admin_type==1){
            var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+$("#del-sku-modal").find("#del-data-id").data('comm-set-id')+"']");
            btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');
        }
    });

    /**********删除sku************/

    //遍历已添加数据,获取专营店
    function getdlr(commodity_set_id) {
        console.log(commodity_set_id);
        var dl_code_list=[]
        $('#haved-commodtiy tr').each(function () {
            var sku_list=$(this).find('.btn-fight-view').data('sku-list');
            console.log(sku_list);
            if(commodity_set_id==sku_list.commodity_set_id){
                dl_code_list= dl_code_list.concat(sku_list.dlr_code.split(','));
            }
        });
        console.log(dl_code_list);
        return dl_code_list;
    }

    $(".datetimepicker3").datetimepicker({
        format:"YYYY-MM-DD HH:mm:00",
        locale: moment.locale('zh-cn'),
    });

    /* 专营店端获取添加id*/
    function gethavedCommodityId() {
        var comm_set_id_arr=[];
        $("#haved-commodtiy tr").each(function () {
            var sku_list=$(this).find(".btn-fight-view").data("sku-list");
            comm_set_id_arr.push(sku_list.commodity_set_id);
        });
        return comm_set_id_arr;
    }

    //获取并设置商品分类
    function setCommType(param){
        var url = ajaxCommTypeUrl;
        if(param!=null) url=param;
        var data_id = $('#addCommTypeModal').find("[name='text_click_id']").attr('data-id');
        $.get(url,function(res){
            var html='';
            var list = res.data.data;
            $.each(list,function(i,val){
                var checkbox_obj = '';
                if(val.id==data_id) {
                    checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id +'" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="" checked>选择 </label>';
                }else{
                    checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id + '" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="">选择 </label>';
                }
                html += '<tr id="comm_tab_tr_'+val.id+'">' +
                    '<td class="text-center">'+ val.comm_type_name +'</td>' +
                    '<td class="text-center">' + checkbox_obj +
                    '</td> </tr>';
            });
            $("#add-comm-type-tbody").html(html);

        },'json');
    }

    //搜索商品分类列表
    /* $("#comm-type-search").on('click',function(){
     var obj = $(this).parent();
     var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
     var comm_type_name = obj.find("input[name='comm_type_name']").val();
     var param = ajaxCommTypeUrl + '&comm_parent_id='+comm_parent_id+'&comm_type_name='+comm_type_name;
     initCommType(param);
     });*/

    //选择商品-取消
    $("#add-comm-no").on('click',function(){
        $("#addCommModal").click();
    });
    /*------------------- end 添加商品、分类 -----------------------*/

    function getSkuList() {
        var sku_list=[];
        $("#haved-commodtiy tr").each(function () {
            //alert($(this).find(".btn-fight-view").data("sku-list"));
            sku_list.push($(this).find(".btn-fight-view").data("sku-list"));
        })
        return sku_list;
    }


    // 获取新的规则
    function get_data_new() {
        var user_segment = $("input[name='user_segment']:checked").val();
        var dis_type = $("[name='dis_type']:checked").val();

        // 会员折扣
        console.log('user_segment:',user_segment)
        if (user_segment == 1) {
            var is_user_die_z = 0;
            var is_user_die_l = 0;
            var is_user_die_g = 0;

            var data = {};
            $('#member_box').find('.level_item').each(function (index,value){
                var old_num = 0;
                $(this).find('.form-z').each(function (key,val){
                    var num = $(this).find('.v_dis_type').val();
                    console.log('num:',num);
                    var value_code = $(this).find('.v_dis_type').attr('value_code');
                    console.log('value_code:',value_code);

                    // 限时折扣
                    if (dis_type == 1) {
                        if (key > 0 && old_num < num * 100) {
                            is_user_die_z = 1;
                        } else {
                            // 赋值
                            old_num = num * 100;
                        }
                    }
                    // 限时立减
                    if (dis_type == 2) {
                        if (key > 0 && old_num > num * 100) {
                            is_user_die_l = 1;
                        } else {
                            // 赋值
                            old_num = num * 100;
                        }
                    }
                    // 限时秒杀价
                    if (dis_type == 3) {
                        if (key > 0 && old_num < num * 100) {
                            is_user_die_g = 1;
                        } else {
                            // 赋值
                            old_num = num * 100;
                        }
                    }


                    data[value_code] = num
                })
            })

            if (is_user_die_z) {
                layer.msg('低级别会员限时折扣应大于高级别会员');
                return false
            }
            if (is_user_die_l) {
                layer.msg('低级别会员限时立减应小于高级别会员');
                return false
            }
            if (is_user_die_g) {
                layer.msg('低级别会员固定秒杀价应大于高级别会员');
                return false
            }

        }
        // 车主折扣
        if (user_segment == 2) {
            var data = {};
            var is_die_z = 0; // 限时折扣
            var is_die_l = 0; // 限时立减
            var is_die_g = 0; // 固定秒杀价
            $('#car_onwer_box').find('.level_item').each(function (index,value){
                // var jian = $(this).find('.form-j').val();
                var no_car = $(this).find('.carv_dis_type_1').val();
                var car = $(this).find('.carv_dis_type_2').val();
                // 判断dis_type
                if (no_car !== '') {
                    // 限时折扣
                    if (dis_type == 1) {
                        if (no_car * 100 < car * 100) {
                            is_die_z = 1;
                        }
                    }
                    // 立减
                    if (dis_type == 2) {
                        if (no_car * 100 > car * 100) {
                            is_die_l = 1;
                        }
                    }
                    // 固定秒杀
                    if (dis_type == 3) {
                        if (no_car * 100 < car * 100) {
                            is_die_g = 1;
                        }
                    }
                }

                // 日产
                if (setType == 5) {
                    data = {'NONE':no_car,'N':car};
                }
                // 启辰
                if (setType == 7) {
                    data = {'NONE':no_car,'V':car};
                }

                if (no_car != '') {
                    // 限时折扣
                    if (dis_type == 1) {
                        if (no_car * 100 < car * 100) {
                            is_die_z = 1;
                        }
                    }
                    // 立减
                    if (dis_type == 2) {
                        if (no_car * 100 > car * 100) {
                            is_die_l = 1;
                        }
                    }
                    // 固定秒杀
                    if (dis_type == 3) {
                        if (no_car * 100 < car * 100) {
                            is_die_g = 1;
                        }
                    }
                }


                // pz1a
                if  (setType == 6) {
                    var a_car = $(this).find('.carv_dis_type_3').val();
                    data = {'NONE':no_car,'N':car, 'P':a_car};
                    if (no_car !== '') {
                        // 限时折扣
                        if (dis_type == 1) {
                            if (no_car * 100 < a_car * 100) {
                                is_die_z = 1;
                            }
                        }
                        // 立减
                        if (dis_type == 2) {
                            if (no_car * 100 > a_car * 100) {
                                is_die_l = 1;
                            }
                        }
                        // 固定秒杀
                        if (dis_type == 3) {
                            if (no_car * 100 < a_car * 100) {
                                is_die_g = 1;
                            }
                        }
                    }
                }

            });
            if (is_die_z) {
                layer.msg('非车主限时折扣应大于车主折扣');
                return false
            }
            if (is_die_l) {
                layer.msg('非车主限时立减应小于车主立减');
                return false
            }
            if (is_die_g) {
                layer.msg('非车主固定秒杀价应大于车主秒杀价');
                return false
            }
        }
        console.log('data:',data);
        return data;
    }


    $("#put-form").on('click',function(){
        var $form=$("#fight-form");
        // dealer_select 有hidden, 就清空 dlr_hide 的value
        if($("#dealer_select").hasClass("hidden")){
            $("#dlr_hide").attr('value','');
        }else{
            var dlr_value = $("#dlr_hide").val();
            //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
            $(".checkbox-inline input:not(:checked)").each(function(i,v){
                var reg = new RegExp(this.value,"g");//g,表示全部替换。
                dlr_value = dlr_value.replace(reg,"");
            })
            $("#dlr_hide").attr('value',dlr_value);
        }
        var up_down_channel_name = getUpDownChannel();
        // 定向人群判断
        var user_segment = $("[name='user_segment']:checked").val();
        var dis_info = '';
        if (user_segment != undefined) {
            // 获取
            dis_info = get_data_new();
            console.log('dis_info:',JSON.stringify(dis_info));
            if (dis_info == false) {
                return false;
            }
            if (dis_info === '') {
                layer.msg('折扣信息不能为空');
                return false;
            }
        }

        var validate=$form.psly().validate();  //表单验证
        if(!validate) return false;
        var form_data=$form.serialize();
        var home = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
        console.log('form_data:'+form_data);

        var sku_list= getSkuList();
        if (sku_list.length === 0) {
            layer.msg('秒杀商品不能为空');
            return false;
        }
        console.log('sku_list:',sku_list)


        var data=$.param({sku_list:JSON.stringify(sku_list),'up_down_channel_name':up_down_channel_name,'home':home,'dis_info':dis_info}) + '&' + form_data;
        Custom.ajaxPost(save_url,data,null,index_url);
    });

    $(".sku-modal-close").on('click', function () {
        $("#sku-modal").find("[name='sku_stock']").val('')
        var seckill_stock = $("#sku-modal").find("[name='seckill_stock']");
        if (seckill_stock === undefined) {
            $("#sku-modal").find("[name='seckill_stock']").removeAttr('readonly')
        }
        console.log(seckill_stock)
        $("#sku-modal").find("[name='seckill_stock']").val('')
        $("#sku-modal").find("[name='sku_stock']").removeAttr('readonly')

        // $("#sku-modal").find("[name='seckill_stock']").removeAttr('readonly')
    })

    $(".look-close").click(function (){
        console.log('查看-关闭')
        $("#sku-modal").find("[name='sku_stock']").val('')
    })

    var user_segment_index = 0;
    $(".user_segment").click(function(){
        if (!$.isEmptyObject(commodity_list)) {
            var index = layer.open({
                title: ['操作提醒'],
                btn: ['确认', '取消'],
                content:"<div style='font-size: 15px'>更改人群类型会清空下方添加商品列表</div>",
                yes: function (res) {
                    layer.close(index)
                    $("#haved-commodtiy tr").remove()
                    commodity_list = {}
                    console.log('commodity_list_2:',commodity_list);
                }
            })
        }


        $("#rule_new").show()
        $("#rule_old").hide()
        if($(this).val() == 1){
            $("#user_level_box").show();
            $("#car_onwer_box").hide();
            $("#member_box").show();
            user_segment_index = 1;
        }else{
            $("#user_level_box").hide();
            $("#member_box").hide();
            $("#car_onwer_box").show();
            user_segment_index = 2
        }
        initComm(ajaxCommUrl);

    })

// 选择会员级别 规则跟着变动
var new_user_level = {}
$('.user_segment_options').click(function () {
    var _this = $(this);
    var order_no = _this.attr('id');
    console.log('order_no:',order_no);
    console.log('user_level_list_1:',user_level_list)

    refresh_user_level(order_no);
})


// 刷新会员列表
var refresh_user_level =  function (order_no) {
    new_user_level = user_level_list.filter((val) => {
        return val.order_no >= order_no;
    });
    console.log('user_level_list_3:',new_user_level)

    var dis_type = $("[name='dis_type']:checked").val();
    console.log('dis_type:',dis_type)

    var c_dis_type_1 = '';
    var c_dis_type = '';
    if (dis_type == 1) {
        c_dis_type_1 = '';
        c_dis_type = '折';
    }
    if (dis_type == 2) {
        c_dis_type_1 = '立减';
        c_dis_type = '元';
    }
    if (dis_type == 3) {
        c_dis_type_1 = '';
        c_dis_type = '元';
    }

    // 标题
    var html = '';
    $.each(new_user_level,function(i,val){
        html += '<div class="stylelt user_level county_name" data-title="' + val.value_code +'" id="' + val.order_no + '"  data-code="' + val.value_code +'">'+ val.county_name +'</div>'
    })
    // 输入框
    var k_html = '';
    $.each(new_user_level, function (i, val) {
        k_html += '<div class="stylelt level_jian form-z value_code"><span class="c_dis_type_1">'+ c_dis_type_1 +'</span><input size="4" class="v_dis_type" data-parsley-required="true" value_code="' + val.value_code + '"  /><span class="c_dis_type">'+c_dis_type+'</span></div>'
    })

    $(".county_name").remove();
    $("#county_name").append(html);
    $(".value_code").remove();
    $("#value_code").append(k_html);
    // 清除追加的规则
    $('.user_level_1').remove();
    $('.user_level_2').remove();
}

