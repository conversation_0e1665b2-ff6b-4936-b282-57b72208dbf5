/**
 * Created by lyj on 2017/11/23.
 */

$(function () {

    //初始化

    $(".default-select2").select2({width:'150px'});

    /*------------------- begin 添加商品、分类 -----------------------*/

    initComm(ajaxCommUrl);

    //操作类型切换
    $("[name='operation_type']").on('change',function(){
        $(this).closest('.with-border').find("[name='operation_type_data']").val('');
    });

    var comm_checked_id = [];		//商品id
    var comm_tab_tr = [];		//商品表单tr

    //搜索商品列表
    $("#comm-type-search").on('click',function(){
        var obj = $(this).parents('.form-group');
        var comm_parent_id = obj.find("select[name='comm_parent_id']").val();
        var sub_comm_type_id = obj.find("select[name='sub_comm_type_id']").val();
        var three_comm_type_id = obj.find("select[name='three_comm_type_id']").val();
        var commodity_name = obj.find("input[name='commodity_name']").val();
        var commodity_class = obj.find("select[name='commodity_class']").val();
        var param = ajaxCommUrl + '&comm_parent_id='+comm_parent_id+'&sub_comm_type_id='+sub_comm_type_id+'&three_comm_type_id='+three_comm_type_id+'&commodity_name='+commodity_name+'&commodity_class='+commodity_class;
        initComm(param);
    });

    function initComm(url) {
        $.getJSON(url, null, function (resData) {
            createPageComm(5, 10, resData.data.list.total, url);//创建翻页功能按钮，翻
            $("#add-comm-tbody").empty();
            if (resData.data.list.total > 0) {                          //页向后台请求连接
                setComm(url);
            }
        });
    }

    function createPageComm(pageSize, buttons, total, url) {        //contracts_url为点击
        $("#comm-pagination").jBootstrapPage({      //功能按钮后需要访问action路径
            pageSize : pageSize,
            total : total,
            maxPageButton:buttons,
            onPageClicked: function(obj, page) {    //分页事件
                $("#add-comm-tbody").empty();
                setComm(url+"&page="+(page+1));
            }
        });
    }

    //获取并设置商品
    function setComm(param){
        var url = ajaxCommUrl;
        if(param!=null) url=param;

        $.get(url,function(res){
            var html='';
            var list = res.data.list.data;
            var comm_set_id_arr=gethavedCommodityId();

            $.each(list,function(i,val){
                var checkbox_obj = '';

                var index= $.inArray(val.commodity_set_id, comm_set_id_arr);
                if (index>-1){
                    var button='<button data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                        '" data-dlr-code="'+val.dlr_code+'"  class=" btn btn-default active btn-sm btn-fight" disabled>已添加</button>';
                }else{
                    if(val.is_fight == 1){
                        var button='<button data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                            '" data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled >已参与其他拼团</button>';
                    }else if(val.is_pre == 1){
                        var button='<button data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                            '" data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled>已参与预售</button>';
                    }else if(val.is_limit == 1){
                        var button='<button data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                            '" data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled>已参与限时折扣</button>';
                    }else if(val.is_gift == 1){
                        var button='<button data-comm-set-id="'+val.commodity_set_id+'" data-grouped="'+val.is_grouped+'" data-comm-id="'+val.commodity_id+'"'+
                            ' class=" btn btn-white active btn-sm " disabled >已参与['+val.activity_name+']买赠活动</button>';
                    }else if(val.is_seckill==1){
                        var button='<button data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                            '" data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white active btn-sm " disabled>已参与其他秒杀</button>';
                    } else {
                        var button='<button data-comm-id="'+val.commodity_id+'" home="'+val.home+'" data-comm-set-id="'+val.commodity_set_id+
                            '" data-dlr-code="'+val.dlr_code+'"  class=" btn btn-white btn-sm  btn-fight">选择添加</button>';
                    }
                }

                html += '<tr id="comm_tab_tr_'+val.commodity_id+'">' +
                    '<td class="text-left"><image class="cover-image" src="'+val.cover_image+'">'+
                    '<a class="init-commodity-preview" data-dlr-code="'+val.dlr_code+'" data-commodity-id="'+val.commodity_id+'">'+val.commodity_name+'</a></td>'+
                    '<td class="text-center">'+ val.up_down_channel_name +'</td>' +
                    '<td class="text-center">'+ val.price +'</td>' +
                    '<td class="text-center">'+ val.count_stock +'</td>' +
                    '<td class="text-center">' + button +
                    '</td> </tr>';
            });
            $("#add-comm-tbody").html(html);

        },'json');
    }
    //添加商品
    $("#add-comm-tbody").on('click',".btn-fight",function (e) {
        e.preventDefault();
        var obj=$(this);
        var home = $(this).attr('home');
        var fhome = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
        if(fhome != home){
            if(fhome == 1){
//                layer.msg('只能添加“快递到家“的商品，该商品为“到店安装”');
//                return false;
            }
            if(fhome == 0){
//                layer.msg('只能添加“到店安装“的商品，该商品为“快递到家”');
//                return false;
            }
        }

        $("#modal_home").val("");
        $("#modal_home").val(home);
        $.getJSON(getSkuUrl,{commodity_id:$(this).data('comm-id'),commodity_set_id:$(this).data('comm-set-id')},function (res) {
            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            $("#comm-name-header").html(res.data.commodity_row.commodity_name);
            var html='';
            $.each(sku_list,function(i,val){

                var sku_check='<input type="checkbox" checked class="sku-ckeck" value="6" name="">';
                var sku_price= '<div class="input-group " style="width: 100px;">'+
                    '<input  type="text" class="form-control sku-price input-sm" value="" data-parsley-required="true"  data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'+
                    '<span class="input-group-addon">元</span> </div>';

                html += '<tr set_sku_id="'+val.id+'">' +
                    '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                    '<td id="td_price">'+ val.price +'</td>' +
                    '<td >'+sku_price+'</td>' +
                    '<td >' + val.stock +
                    '</td> </tr>';
            });

            $("#sku-modal").data("comm-id",obj.data('comm-id'));
            $("#sku-modal").data("comm-set-id",obj.data('comm-set-id'));
            $("#sku-modal").data('type','add');
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');
            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val("").data("sku-dlr","");

        });

    })


    //查看商品
    $("body").on("click",".btn-fight-view",function (e) {

        e.preventDefault();

        var set_sku_list=$(this).data('sku-list');

        console.log(set_sku_list);
        var sku_price_list=set_sku_list.sku_list;

        var obj=$(this);
        $.getJSON(getSkuUrl,{commodity_id:set_sku_list.commodity_id,commodity_set_id:set_sku_list.commodity_set_id},function (res) {

            var sku_list=res.data.sku_list;
            $(".sku-image img").attr("src",res.data.commodity_row.cover_image);
            $(".sku-comm").html(res.data.commodity_row.commodity_name);
            var html='';
            $.each(sku_list,function(i,val){
                var price='';
                var checked=''
                if(sku_price_list[val.id]){
                    price  =sku_price_list[val.id];
                    checked='checked';
                }
                var sku_check='<input type="checkbox" ' +checked+'  class="sku-ckeck" value="6" name="">';
                var sku_price= '<div class="input-group " style="width: 100px;">'+
                    '<input  type="text" class="form-control sku-price input-sm" value="'+price+'" data-parsley-required="true"  data-parsley-min="0.01" data-parsley-pattern-message="格式不正确,请输入不小于0.01的数值" data-parsley-pattern="/^(-?\d+)(\.\d{1,2})?$/">'+
                    '<span class="input-group-addon">元</span> </div>';
                html += '<tr set_sku_id="'+val.id+'">' +
                    '<td class="text-left">'+sku_check+val.sku_val+'</td>' +
                    '<td id="td_price">'+ val.price +'</td>' +
                    '<td >'+sku_price+'</td>' +
                    '<td >' + val.stock +
                    '</td> </tr>';
            });

            $("#sku-modal").data("comm-id",set_sku_list.commodity_id);
            $("#sku-modal").data("comm-set-id",set_sku_list.commodity_set_id);
            $("#comm-name-header").html(set_sku_list.commodity_name);
            var index=$(".btn-fight-view").index(obj);
            $("#sku-modal").data('type',index);
            $(".sku-tb").html(html);
            $("#sku-modal").modal('show');

            //dlr初始化
            $("#sku-modal").find(".sku-dlr").val(set_sku_list.dlr_name).data("sku-dlr",set_sku_list.dlr_code);

        });

    })


    //批量设置价格
    $('#all_price').editable({

        success: function(response, newValue) {
            update_sku_price(newValue);
            $(".editable-cancel").click();
            return false;

        },
        validate: function (value) { //字段验证
            if (!$.trim(value)) {
                return '不能为空';
            }
            if (!(/^[0-9/.]*$/).test(value)) {
                return '格式有误';
            }
        }
    });

    //更新折扣价格
    function update_sku_price(obj_body){
        var comm_discount = obj_body;
        // console.info(comm_discount);
        if(comm_discount==null || comm_discount<=0) return false;
        var obj_tr = $("#change_sku_tb").find("tr");
        for(var i=0;i<obj_tr.length;i++){
            var this_val = obj_tr.eq(i);
            var sku_price = ((comm_discount/10) * this_val.find("#td_price").text());
            console.info(sku_price.toFixed(2));
            this_val.find(".sku-price").val(sku_price.toFixed(2));      //保留两位小数
        }
    }

    //获取专营店
    $(".sku-dlr").on('click',function () {
        var commodity_id=$(this).parents('#sku-modal').data('comm-id');
        var commodity_set_id=$(this).parents('#sku-modal').data('comm-set-id');
        var dlr_sku_arr =$(this).data('sku-dlr').split(",");
        var haved_dlr_code=getdlr(commodity_set_id);

        $.getJSON(getDlr_url,{commodity_set_id:commodity_set_id,fight_group_id:fight_group_id},function (resData) {
            if(resData.error==0){
                var data = $.parseJSON(resData.data);
                console.log(data);
                Custom.selectDlr(data,dlr_sku_arr,function (dlr_code,dlr_name) {
                    $("#dlr_content").empty();
                    $(".sku-dlr").val(dlr_name);
                    $(".sku-dlr").data("sku-dlr",dlr_code.join(','));
                    $("#select-dlr-modal").modal("hide");
                })

                // $("#dlr-modal").modal("show");
            }else {
                layer.msg(resData.msg);
            }
        })

    })

    //全选专营店
    $("#check_all").on("change",function () {
        var check = $(this).attr("checked");
        if(check){
            $.each($(".dlr_checkbox.single"),function (i,ele) {
                $(ele).attr("checked",true);
            })
        }else {
            $.each($(".dlr_checkbox.single"),function (i,ele) {
                $(ele).attr("checked",false);
            })

        }
    })
    //sku 点击确认
    $("#sku-confirm").on('click',function (e) {
        var sku_modal=$(this).parents("#sku-modal");
        var home =  sku_modal.find("#modal_home").val();
        var commodity_list =new Object();
        commodity_list.commodity_id    =sku_modal.data('comm-id');
        commodity_list.commodity_set_id=sku_modal.data('comm-set-id');
        commodity_list.dlr_code        =sku_modal.find(".sku-dlr").data("sku-dlr");
        commodity_list.dlr_name        =sku_modal.find(".sku-dlr").val();
        commodity_list.commodity_name  =sku_modal.find("#comm-name-header").html();
        var image=sku_modal.find(".cover-image").attr("src");
        var  commodity_name=sku_modal.find(".sku-comm").html();
        var sku_list={};
        var check_price=true;
        $(".sku-tb tr").each(function () {

            if($(this).find(".sku-ckeck").attr("checked")){
                var set_sku_id=$(this).attr("set_sku_id");
                var reg=/^0\.([1-9]|\d[0-9])$|^[1-9]\d{0,8}\.\d{0,2}$|^[1-9]\d{0,8}$/
                if(!reg.test($(this).find(".sku-price").val())){
                    layer.msg('价格格式不对');
                    check_price=false;
                    return false;
                }else{
                    sku_list[set_sku_id]=$(this).find(".sku-price").val();
                }
            }
        })
        if (!check_price){
            return;
        }
        if(JSON.stringify(sku_list) == "{}"){
            layer.msg('请选择规格');
            return;
        }
        if ((!$(".sku-dlr").val() || !commodity_list.dlr_code) && set_type==1){
            layer.msg('请选择经销商');
            return ;
        }

        commodity_list.sku_list=sku_list;
        //新增数据添加到后面
        var json_st=JSON.stringify(commodity_list);
        var type   =sku_modal.data('type');
        //添加

        if (type=='add'){
            var html='<tr class="info haved-add" data><td style="width: 350px;"><input type="hidden" name="home"  class="home" value="'+home+'"/><img class="cover-image" src="'+image+'">'+
                '<a class="init-commodity-preview" data-dlr-code="'+dlr_code+'" data-commodity-id="'+commodity_list.commodity_id+'">'+commodity_list.commodity_name+'</a></td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list">删除</button></td></tr>';
            //alert(html);
            $("#haved-commodtiy").append(html);

        }else {  //查看
            //
            var html='<td style="width: 350px;"><img class="cover-image" src="'+image+'">'+
                '<a class="init-commodity-preview" data-dlr-code="'+dlr_code+'" data-commodity-id="'+commodity_list.commodity_id+'">'+commodity_name+'</td><td class="text-right">'+
                "<button data-sku-list='"+json_st+"'"+'  class="  btn btn-primary m-r-5 m-b-5 btn-sm btn-fight-view">查看</button>'+
                '<button '+'"  class="btn btn-danger btn-sm m-r-5 m-b-5 del-sku-list ">删除</button></td>';
            $(".btn-fight-view").eq(type).parents('tr').html(html);
        }

        //专营店 禁用添加按钮
        var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+commodity_list.commodity_set_id+"']");
        btn_obj.removeClass("btn-white").addClass("btn-default active btn-sm").attr("disabled",true).html('已添加');

        $("#sku-modal").modal('hide');
        console.log(commodity_list);
    })

    /**********删除sku************/
    $("body").on("click",".del-sku-list",function (e) {

        if($("#act_status").length > 0){
            if($("#act_status").val() == 2){
                alert('活动已开始,禁止删除')
                return false;
            }
        }

        e.preventDefault();
        var sku_list=$(this).parents("tr").find(".btn-fight-view").data("sku-list");
        var type=$(".del-sku-list").index($(this));
        $("#del-sku-modal").find("#del-data-id").val(type).data('comm-set-id',sku_list.commodity_set_id);
        $("#del-sku-modal").modal('show');
    })

    $("#del-confirm").on("click",function () {

        $(".del-sku-list").eq($("#del-sku-modal").find("#del-data-id").val()).parents('tr').remove();
        $("#del-sku-modal").modal('hide');
        //专营店端修改添加按钮
        //if(admin_type==2){
        var btn_obj=$('#add-comm-tbody').find("[data-comm-set-id='"+$("#del-sku-modal").find("#del-data-id").data('comm-set-id')+"']");
        btn_obj.removeClass("btn-default active").addClass("btn-white").attr("disabled",false).html('选择添加');
        //  }

    })

    /**********删除sku************/


    //遍历已添加数据,获取专营店
    function getdlr(commodity_set_id) {
        console.log(commodity_set_id);
        var dl_code_list=[]
        $('#haved-commodtiy tr').each(function () {
            var sku_list=$(this).find('.btn-fight-view').data('sku-list');
            console.log(sku_list);
            if(commodity_set_id==sku_list.commodity_set_id){
                dl_code_list= dl_code_list.concat(sku_list.dlr_code.split(','));
            }
        })
        console.log(dl_code_list);
        return dl_code_list;
    }

    $(".datetimepicker3").datetimepicker({
        format:"YYYY-MM-DD HH:mm:00",
        locale: moment.locale('zh-cn'),
    });

    /* 专营店端获取添加id*/
    function gethavedCommodityId() {
        var comm_set_id_arr=[];
        $("#haved-commodtiy tr").each(function () {
            var sku_list=$(this).find(".btn-fight-view").data("sku-list");
            comm_set_id_arr.push(sku_list.commodity_set_id);
        })

        return comm_set_id_arr;
    }

    //获取并设置商品分类
    function setCommType(param){
        var url = ajaxCommTypeUrl;
        if(param!=null) url=param;
        var data_id = $('#addCommTypeModal').find("[name='text_click_id']").attr('data-id');
        $.get(url,function(res){
            var html='';
            var list = res.data.data;
            $.each(list,function(i,val){
                var checkbox_obj = '';
                if(val.id==data_id) {
                    checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id +'" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="" checked>选择 </label>';
                }else{
                    checkbox_obj = '<label> <input type="checkbox" class="commodity_checkbox" data-id="' + val.id + '" data-name="'+val.comm_type_name+'" data-parent-name="'+val.comm_parent_name+'" value="" name="">选择 </label>';
                }
                html += '<tr id="comm_tab_tr_'+val.id+'">' +
                    '<td class="text-center">'+ val.comm_type_name +'</td>' +
                    '<td class="text-center">' + checkbox_obj +
                    '</td> </tr>';
            });
            $("#add-comm-type-tbody").html(html);

        },'json');
    }

    //选择商品-取消
    $("#add-comm-no").on('click',function(){
//        var text_id = $(this).closest('.form').find("[name='text_click_id']").val();
//        $('body').find("#"+text_id).attr('data-id','');
//        $('body').find("#"+text_id).val('');
//        $(this).closest('.form').find("input.commodity_checkbox").each(function(){
//            $(this).removeAttr('checked');
//        });
        $("#addCommModal").click();
    });
    /*------------------- end 添加商品、分类 -----------------------*/

    function getSkuList() {
        var sku_list=[];
        $("#haved-commodtiy tr").each(function () {
            //alert($(this).find(".btn-fight-view").data("sku-list"));

            sku_list.push($(this).find(".btn-fight-view").data("sku-list"));
        })

        console.log(sku_list);

        return sku_list;
    }

    $("#put-form").on('click',function(){
        var $form=$("#fight-form");
        // dealer_select 有hidden, 就清空 dlr_hide 的value
        if($("#dealer_select").hasClass("hidden")){
            $("#dlr_hide").attr('value','');
        }else{
            var dlr_value = $("#dlr_hide").val();
            //遍历 checkbox-inline, 把没有勾选的数据从dlr_hide 中去除
            $(".checkbox-inline input:not(:checked)").each(function(i,v){
                var reg = new RegExp(this.value,"g");//g,表示全部替换。
                dlr_value = dlr_value.replace(reg,"");
            })
            $("#dlr_hide").attr('value',dlr_value);
        }
        var up_down_channel_name = getUpDownChannel();
        var validate=$form.psly().validate();  //表单验证
        if(!validate) return false;
        var form_data=$form.serialize();
        var home = $("#haved-commodtiy").find("tr:first-child").find(".home").val();
        console.log(form_data);
        var data=$.param({sku_list:getSkuList(),'up_down_channel_name':up_down_channel_name,'home':home}) + '&' + form_data;
        Custom.ajaxPost(save_url,data,null,index_url);
    });

    //图片上传
    $("#put-pic").on('click',function () {
        $("#pic-file").click();

    })
    //封面图上传
    $(".form-group").on('change','#pic-file',function(){
        var check_file = Custom.validationFile("pic-file",50,'文件不能超过50k');
        if (!check_file) return;
        Custom.ajaxFileUpload("pic-file",{file_path:"fight_group"},function(res){
            if (res.error==0){
                $("#put-pic").hide();
                var img_url=res.data.upload_url+res.data.image;
                $(".upload-pic img").attr("src",img_url);
                $(".upload-pic").show();
                $("#pic-file").val("");
                $("input[name='pic']").val(img_url);
            }else {
                layer.msg(res.msg);
            }
            console.log(res);
        })
    });

    $(".upload-pic").on("click","del",function () {
        $("#put-pic").show();
        $(".upload-pic").hide();
        $("input[name='pic']").val("");
    })

})