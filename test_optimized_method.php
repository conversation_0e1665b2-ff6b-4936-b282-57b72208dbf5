<?php
/**
 * 测试优化后的卡券组合方法
 */

// 模拟测试数据
$test_cards = [
    [
        'id' => '1',
        'value' => 10,
        'can_use' => '',
        'can_no_use' => 'all',
        'can_no_with' => '92,94',
        'act_id' => '91',
        'act_card_id' => '1001',
        'card_code' => 'CARD001',
        'is_gift_card' => 0
    ],
    [
        'id' => '2',
        'value' => 20,
        'can_use' => '3,4',
        'can_no_use' => '',
        'can_no_with' => '92,94',
        'act_id' => '91',
        'act_card_id' => '1002',
        'card_code' => 'CARD002',
        'is_gift_card' => 0
    ],
    [
        'id' => '3',
        'value' => 30,
        'can_use' => '2,4',
        'can_no_use' => '',
        'can_no_with' => '92,94',
        'act_id' => '91',
        'act_card_id' => '1003',
        'card_code' => 'CARD003',
        'is_gift_card' => 1
    ]
];

echo "测试数据准备完成\n";
echo "卡券数量: " . count($test_cards) . "\n";
echo "新的优化方法已添加到 Common.php 中\n";

// 使用说明
echo "\n使用方法:\n";
echo "1. 调用 act_card_hc_optimized(\$cards) - 优化版本，限制最大15张卡券\n";
echo "2. 调用 act_card_hc_batch(\$cards, \$batch_size) - 分批处理版本，适合大数据集\n";

echo "\n优化特点:\n";
echo "- 输入限制: 最大处理15张卡券，防止指数爆炸\n";
echo "- 缓存机制: 5分钟缓存，避免重复计算\n";
echo "- 动态规划: 使用位掩码替代递归，提高效率\n";
echo "- 早期剪枝: 快速过滤无效组合\n";
echo "- 分批处理: 支持超大数据集处理\n";